# n8n YouTube Automation Template

Conversation ID: 68249209-c4ec-8008-907f-8ba75b0813fb

## Message 1

please create a template/workflow that i can import into n8n for automatic youtube control 

## Message 2

**Setting up the process**

I’m constructing an n8n workflow to automate YouTube actions. This involves a cron node for scheduling, a Google Drive node for checking new files, and a YouTube node for uploads. Potentially, a set node for metadata could be added.

**Piecing together the template**

I’m working on a single node JSON for an n8n workflow, focusing on a YouTube node for video upload with fields like title, description, tags, categoryId, and privacyStatus.

**Streamlining the upload process**

I’m noting a minimal multi-node example: "Start" node, "HTTP Request" for sample video download, and "YouTube" node for upload. The JSON will include metadata placeholders and credential setup instructions.

## Message 3

Below is a minimal **n8n** workflow (in JSON format) that you can import directly into your n8n instance. It demonstrates a simple pattern of:

1. **Triggering on a schedule** (via `Cron` node).  
2. **Listing newly added files** in a specific folder on Google Drive.  
3. **Downloading** each new file.  
4. **Uploading** it to YouTube automatically.

> **Important**: 
> - You’ll need to [configure valid Google Drive and YouTube credentials in n8n](https://docs.n8n.io/credentials/google/) beforehand.  
> - Update node parameters—such as the folder ID on Google Drive and any metadata for YouTube (e.g., title, description)—according to your needs.  
> - This example uses a basic approach to illustrate the workflow. Depending on your exact requirements, you may refine or expand upon it.

---

## Sample Workflow JSON

You can **copy/paste** this JSON into **n8n** by going to **Workflows → Import from File** (or **Import from Clipboard**) in your n8n Editor UI.

```json
{
  "name": "Automatic YouTube Upload",
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "item": [
            {
              "hour": 4,
              "minute": 0
            }
          ]
        }
      },
      "name": "Cron",
      "type": "n8n-nodes-base.cron",
      "typeVersion": 1,
      "position": [
        240,
        300
      ]
    },
    {
      "parameters": {
        "operation": "list",
        "useQueryString": false,
        "queryFilters": {
          "trashed": false,
          "parents": "YOUR_FOLDER_ID_HERE"
        },
        "options": {}
      },
      "name": "List Files in Folder",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 1,
      "position": [
        480,
        300
      ],
      "credentials": {
        "googleApi": "Google Drive Account"
      }
    },
    {
      "parameters": {
        "operation": "download",
        "fileId": "={{$json[\"id\"]}}",
        "options": {}
      },
      "name": "Download File",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 1,
      "position": [
        720,
        300
      ],
      "credentials": {
        "googleApi": "Google Drive Account"
      }
    },
    {
      "parameters": {
        "operation": "upload",
        "binaryProperty": "data",
        "additionalFields": {
          "title": "={{$json[\"name\"]}}",
          "description": "Uploaded via n8n automation.",
          "privacyStatus": "public"
        }
      },
      "name": "Upload to YouTube",
      "type": "n8n-nodes-base.youtube",
      "typeVersion": 1,
      "position": [
        960,
        300
      ],
      "credentials": {
        "youtubeOAuth2Api": "YouTube OAuth2"
      }
    }
  ],
  "connections": {
    "Cron": {
      "main": [
        [
          {
            "node": "List Files in Folder",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "List Files in Folder": {
      "main": [
        [
          {
            "node": "Download File",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Download File": {
      "main": [
        [
          {
            "node": "Upload to YouTube",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
```

### How This Workflow Operates

1. **Cron Trigger**  
   - Fires once a day at 04:00 (you can adjust this in the `Cron` node parameters).
2. **List Files in Folder**  
   - Queries a specific Google Drive folder (`YOUR_FOLDER_ID_HERE`) for all non-trashed files.
   - Each file result will pass to the next node.  
3. **Download File**  
   - Downloads each listed file in binary form, storing the content under `data` in the node’s output JSON.
4. **Upload to YouTube**  
   - Uses the binary data from `Download File` to upload each file as a video to YouTube.  
   - Automatically populates title from the Drive filename and sets basic metadata (e.g. `privacyStatus` = `public`).  

### Customizations

- **Google Drive Folder**  
  - Replace `YOUR_FOLDER_ID_HERE` with the actual folder ID. (In Google Drive, open the folder → look at the URL → copy the `folderID` portion.)
- **YouTube Video Metadata**  
  - Change title (`{{$json["name"]}}`), description, tags, and privacy settings as needed.
- **Cron Schedule**  
  - Adjust the `hour` and `minute` in the `Cron` node for your desired trigger schedule.

---

### Next Steps

- **Credentials**: Ensure you have both “Google Drive” and “YouTube OAuth2” credentials set up under **Settings → Credentials** in n8n.  
- **Production Hardening**: If you have many files or large videos, consider error handling (e.g. `Error Trigger` node) or rate-limiting (e.g. using a [Batch or SplitInBatches node](https://docs.n8n.io/nodes/n8n-nodes-base.splitinbatches)) to manage API rate limits.  
- **Notification**: Extend the workflow with messaging nodes (Slack, Discord, Email) to notify you upon successful upload.

---

**That’s it!** Once you import and configure this template, your workflow should periodically check the specified Google Drive folder, download any new files it finds, and automatically upload them to your YouTube channel.
