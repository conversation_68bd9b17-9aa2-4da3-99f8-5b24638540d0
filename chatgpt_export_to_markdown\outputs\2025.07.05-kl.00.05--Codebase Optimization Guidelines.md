# Codebase Optimization Guidelines

Conversation ID: 6868502f-11c0-8008-9828-409721adb2cb

## Message 1

Please consolidate and transform these codebase rules into ordered and organized highly efficient llm-optimized **generalized** instructions (for use with codebase interaction through ai-assisted coding assistants such as CursorAI, VSCode+Augment, etc):



```

"Always reuse existing tests before creating new ones.",

"Document only essential decisions in a concise format.",

"Optimize file/module size for comprehension and ease of navigation.",

"Justify and integrate new files/modules within existing organization.",

"Post-edit, audit for and remove all residual clutter or inconsistency.",

"Eliminate anomalies, redundancies, and duplications after every change.",

"Consolidate related functionality; minimize cross-component dependencies.",

"Justify any new file creation; align additions with project organization.",

"Write self-explanatory code; only add brief comments for complex sections.",

"Check for and use current tests before creating new ones for added features.",

"Prohibit loss of actionable detail, overgeneralization, and diluted guidance."

"Require concise, self-explanatory code: minimal comments only where necessary.",

"Prioritize simplicity, elegance, and high-leverage changes with maximal impact.",

"Optimize directory and module organization for clarity and developer ergonomics.",

"After each change, rigorously clean up and check for complete codebase alignment.",

"Enforce total adherence to established code patterns, conventions, and standards.",

"Verify and integrate with pre-existing tests; avoid redundancy when adding tests.",

"Verify and reuse existing tests before adding new ones for any new functionality.",

"Mandate thorough evaluation of codebase for patterns/anti-patterns before changes.",

"Balance file/module granularity for maximum comprehension and developer ergonomics.",

"Review recent modifications to ensure no residual clutter or inconsistency remains.",

"Document only condensed, integral decisions with rationale as structural annotations.",

"Validate no loss of prior functionality or clarity; confirm full alignment pre-change.",

"Every modification must be deliberate, justified, and enhance long-term maintainability."

"Enforce single responsibility for components; composition over inheritance when possible.",

"Implement only simple, high-impact changes that maximize clarity and minimize disruption.",

"Review recent changes for thorough cleanup and conformity with structure and conventions.",

"Refactor or add code only if it strictly matches and adapts to current codebase structure.",

"Favor composition over inheritance; enforce single responsibility and minimize dependencies.",

"Prefer composition, not inheritance; keep each unit single-purpose and dependencies minimal.",

"Preserve existing functionality and clarity; ensure full alignment before initiating changes.",

"Prioritize rigorous code cleanliness, respecting and adapting to existing codebase structure.",

"Before creating new tests, confirm no duplicates exist; integrate with existing test frameworks.",

"Rule enforcing decoupling of unrelated parts. Minimize dependencies between unrelated components",

"Creation of new files or components should be justified and conform with established organization.",

"Uphold integrity, maintainability, and clarity; maximize functionality while minimizing disruption.",

"Enforce code clarity via expressive structure and naming; comments only for clarifying complexities.",

"Favor simplicity, elegance, and concise intent; pursue high-impact improvements with low disruption.",

"Create new files only with documented project-specific justification and strict structural alignment.",

"Never compromise current functionality or clarity; verify absolute alignment before any modification.",

"Ensure code clarity via precise naming and concise logic; add comments only for unavoidable complexity.",

"Continuously reorganize files and modules for maximum navigability and intuitive clarity for developers.",

"Reject any generalization or abstraction that eliminates actionable steps or specific developer guidance."

"After edits, audit all recent code changes for strict adherence to codebase conventions and full cleanup.",

"Document only key architectural decisions in brief; prohibit inconsistencies, redundancies, and anomalies."

"Implement only elegant, simple, high-leverage improvements; reject changes causing unnecessary complexity.",

"Rule for minimal yet sufficient documentation. Document only integral decisions in a highly condensed form",

"Scan recent commits and structure before edits; identify existing tests, patterns, and naming conventions.",

"Segment explicit compliance rules (low-level) from aspirational principles (high-level) for enforceability.",

"Merge and co-locate related functions into unified modules; eliminate unnecessary inter-module dependencies.",

"Guideline for organizing code into logical groupings. Consolidate related functionality into cohesive modules",

"Tag and separate explicit, enforceable compliance rules from broader aspirational best-practices for clarity.",

"Design enforcement: uphold single responsibility principle. Ensure all components have a single responsibility",

"Continuously refine structure: merge related modules, eliminate anti-patterns, optimize directory organization.",

"Only create new files/components when necessary, with clear rationale, and match established project structure.",

"Enforce clear, self-explanatory code via precise naming and structure; use comments exclusively for complex logic.",

"Mandate balancing simplicity with capability. Maintain inherent simplicity while providing powerful functionality.",

"Always pursue inherent clarity, structure, simplicity, and elegant, high-impact solutions that minimize disruption.",

"Enforce consolidation of related code into cohesive, easily navigable modules with minimized redundant dependencies."

"Refactor all code to single-responsibility units; prefer composition strategies over inheritance wherever feasible.",

"Uphold and adapt to existing codebase structure after any changes; cohesively clean up and integrate modifications.",

"Pre-change: Rigorously analyze codebase for structural patterns and anti-patterns; act only upon confirmed insights.",

"Annotate code structure with only the most essential, rationale-focused comments—no verbose or redundant explanations.",

"Mandate comprehensive conformity checks and categorical rejection of non-compliant contributions via automated systems."

"Require all modifications to strictly align with established standards—no anomalies, inconsistencies, or redundancies.",

"After changes, immediately refactor and adapt code to match existing structure; eliminate any integration inconsistencies.",

"Optimize all improvements for minimal intrusion and maximal impact, prioritizing elegance, maintainability, and coherence.",

"Instruction to weigh module/file size versus system clarity. Balance file granularity with overall system comprehensibility",

"Guideline for future-proofing and developer friendliness. Prioritize readability and understandability for future developers",

"Design recommendation: prioritize composition for flexibility and clarity. Favor composition over inheritance when applicable",

"Do not introduce or tolerate anomalies, inconsistencies, redundancies, or deviations from standards in any code modification.",

"Directive for structural codebase assessment. Evaluate the existing codebase structure and identify patterns and anti-patterns",

"Only document essential decisions succinctly; all changes must prevent inconsistencies, redundancies, and structural anomalies."

"Fully justify and harmonize any new file, function, or dependency; require automated preservation of all existing functionality.",

"Project-wide objective emphasizing three guiding traits. Aim for simplicity, clarity, and maintainability in all project aspects",

"Code must be self-explanatory through structure and naming, using minimal and targeted comments only as necessary for complexity.",

"Objective to improve ease of use and comprehension in project structure. Optimize for developer ergonomics and intuitive navigation",

"Mandate absolute alignment with established organizational standards, preserving clarity, intent, and system integrity at all times.",

"Strictly maintain conformity with codebase patterns, conventions, and standards for seamless integration and ongoing maintainability.",

"No change permitted if it compromises codebase integrity, maintainability, or clarity; favor minimally disruptive, functional enhancement.",

"Rigorously analyze the current codebase structure—including recent changes and underlying design rationale—before making any modification.",

"Prescriptive statement: adopt standards to achieve simplicity and maintainability. Coding standards that promote simplicity and maintainability",

"Discern unwavering enforcement of clarity, simplicity, and maintainability through explicit, automatable standards and rigorous structural discipline.",

"Ensure any new functionality or files are fully justified, seamlessly integrated, and explicitly documented in line with existing project organization.",

"Explicitly require that all changes are accompanied by concise, in-context annotations detailing only non-obvious rationale or potential integration concerns.",

"Enforce codebase clarity, inherent simplicity, and maintainability: single responsibility per component, strict organization, zero abstraction/redundancy/ambiguity.",

"Directive for detailed, actionable process documentation. Define explicit adaptation, testing, documentation, and review procedures in codebase policies; avoid vague meta-rules.",

"Provide comprehensive, actionable, and unambiguous rules that enforce maximal maintainability, clarity, alignment, and contextual excellence for autonomous coding agent conduct.",

"Notice potential for clearer sequencing (requirements -> enforcement -> workflows -> code standards -> contributions -> automation) and enhancement of developer-centric language.",

"Mandate that each modification references and links to prior relevant architectural or design decisions when applicable, reinforcing context awareness without adding procedural bloat."

"Call to understand both implementation and reasoning behind architectural choices. Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.",

"Requirement for rationalizing file creation and maintaining organizational consistency. Justify the creation of any new files and ensure all additions align with the existing project organization.",

"Requirement to preserve current behavior and clarity as precondition to edits. Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.",

"Establish a baseline of codebase immutability regarding quality and standards. Absolute prohibition of introducing inconsistencies, anomalies, redundancies, or deviations from codebase standards in any modification.",

"Policy to ensure principles are verifiable through automated tooling. Translate principles into one-to-one, machine-readable mappings, enabling tools (linters, CI, code reviewers) to verify compliance automatically.",

"Prevent arbitrary file proliferation and enforce organizational discipline. Obligation to justify creation of new files, coupled with strict alignment of all additions to the organizational structure of the project.",

"High-level principle enforcement: adhere to specific qualities guiding code and design patterns. Follow patterns derived from the principles of inherent clarity, structure, simplicity, elegance, precision, and intent.",

"Rule for testing: ensure awareness of existing tests before adding new ones. If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.",

"Policy to avoid overgeneralization and maintain strict requirements for reliability. Do not replace actionable directives with abstractions—retain strict, granular requirements to ensure unambiguous, consistent outcomes.",

"Specific guideline to structure directories for clarity and assistive toolability. Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.",

"Emphasis on adherence to standards for smooth integration and long-term code health. Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.",

"Direct testing integration with existing codebase and prevent redundant test cases. Specific rule mandating review of existing tests before creating new ones, ensuring no duplication and investment in comprehensive verification.",

"Mandate for rigorous compliance and avoidance of codebase issues post-changes. Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.",

"Mandate comprehensive analysis as a precursor to all code changes. Demand for post-edit holistic review, including cleanup, adherence to structure, and respect for conventions; maintain project integrity and manageability at all times.",

"Directive to assess and uphold cleanliness, structure, and consistency following changes. Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.",

"Guideline emphasizing automation-readiness and unambiguous mandate articulation. Implement automation-focused rule structures—preserve hierarchy, explicit mandates, and concise justifications, ensuring robust integration and effective enforcement.",

"Mandate for responsibility in editing and maintaining code cleanliness, while respecting project structure. Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.",

"Concrete rules and advice for arranging and organizing codebases. Explicit call to simplify project aspects, prefer composition over inheritance, ensure readability, embrace single-responsibility, and condense documentation solely to critical decisions.",

"Amplified policy: encourage direct, machine-enforceable rule embedding to enable automation. Embed precise, enforceable rules directly in config files (YAML, JSON, or schema) for automated enforcement: prioritize clarity, modularity, and maintainability.",

"Define action-oriented, enforceable coding standards for any codebase modifications. List of non-negotiable operational requirements for modifying a codebase, emphasizing conformity to existing structure and detailed attention to cleanup, organization, and cohesion.",

"Enforce codebase hygiene and organizational rigor after any modification. Prerequisite to investigate and understand code structure and design rationale before modifying anything, ensuring clarity, alignment with existing intent, and retention of system functionality.",

"Ensure clarity is primarily achieved via code structure and naming, not excessive comments. Directive to write code that is self-explanatory, well-structured, and minimalistic in commenting, avoiding long explanations in favor of clarifying complex logic only where absolutely necessary.",

"Translate aspirational best practices into general, actionable development patterns. Procedural guidelines for code organization: analyze structures, identify patterns and anti-patterns, consolidate related functions, minimize cross-dependencies, and optimize for developer navigation and file clarity.",

"Establish aspirational ideals influencing all code and organizational decisions. Enumeration of core guiding principles: inherent clarity, structure, simplicity, elegance, precision, intent; pursue minimal, powerful improvements and seek prevailing solutions that maximize impact with minimal disruption.",

"Inspirational principle motivating impactful, minimally disruptive, elegant change. Embrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.",

"Aspirational design principle: seek unified, contextually integrated solutions with outstanding execution. Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.",

## Message 2

**The Synthesist’s Consolidated Code-Change Playbook  
(LLM-optimized for tools like CursorAI, VS Code + Augment, etc.)**

---

### 0  Orientation
> **Goal:** Every modification preserves *clarity, integrity,* and *maintainability* while maximizing impact and minimizing disruption.

---

## 1  Guiding Principles — Aspirational North Star
1. **Clarity over cleverness** – Code should explain itself through structure and naming.  
2. **Simplicity scales** – Prefer the smallest, most elegant change that solves the problem.  
3. **Composition beats inheritance** – Single-responsibility units composed together outlive class hierarchies.  
4. **Structure drives understanding** – Directory, module, and test layouts exist to help humans (and AIs) navigate.  
5. **Relentless cleanliness** – Eliminate anomalies, redundancies, and dead code at every touchpoint.  
6. **Context continuity** – Respect existing patterns, tests, and architectural rationales; integrate, don’t overwrite.  
7. **Automation first** – Rules must be provable by linters, CI, and code-review bots wherever feasible.

---

## 2  Enforceable Rules — Machine-Verifiable “Red Lines”
| # | Rule |
|---|------|
| **R1** | *Preserve behavior & tests* – No change may degrade existing test suites or functionality. |
| **R2** | *Reuse before create* – Leverage existing tests, modules, and patterns; add only when a verified gap exists. |
| **R3** | *Justify new files* – Add a file/module only with an inline comment citing the rationale **and** location fit. |
| **R4** | *Single responsibility* – One logical concern per function, class, file, and directory. |
| **R5** | *Min-dep consolidation* – Group related code; avoid cross-module coupling unless essential. |
| **R6** | *Self-documenting code* – Comments for complex intent only; no redundant, stale, or obvious remarks. |
| **R7** | *Size & navigation limits* – Keep files and classes small enough to scan in one screen (~250 LOC guideline). |
| **R8** | *Zero clutter post-edit* – After every PR, the touched area must be free of TODOs, dead code, and stylistic drift. |
| **R9** | *Standards supremacy* – Coding conventions, formatters, and static-analysis gates are mandatory pass criteria. |
| **R10** | *No dilution* – Never replace explicit, actionable guidance with vague abstractions or meta-rules. |

---

## 3  Change Workflow — Checklists for Every Edit

### 3.1  Pre-Change
1. **Scan** recent commits & directory to learn prevailing patterns and anti-patterns.  
2. **Locate** relevant tests; confirm coverage gaps.  
3. **Plan** the minimal-impact path (single-responsibility units, composition, tight scope).

### 3.2  During Change
1. **Code** to be self-explanatory; comment only for non-obvious logic.  
2. **Refactor** nearby code if a 2-minute tidy improves clarity—leave no “small mess” behind.  
3. **Write/extend tests** *only* for uncovered behavior; avoid duplication.

### 3.3  Post-Change (Gatekeeper)
1. **Run** full test suite + static analysis; expect zero regressions.  
2. **Audit** for residual clutter, naming drift, or doc gaps; fix immediately.  
3. **Document** *only* the key decision/rationale in changelog or architectural notes (≤ 3 concise sentences).  
4. **Seek review**; ensure CI bots can automatically validate all enforceable rules (Section 2).

---

## 4  Documentation & Decision Logs

| Scope | Requirement |
|-------|-------------|
| **Code** | Express intent via names & structure. |
| **Inline comments** | Only for complexity that cannot be expressed via code alone. |
| **Architectural notes** | Bullet-level rationale for structural additions/changes; link to prior decisions. |
| **No verbose prose** | Prefer 1-line summaries over paragraphs; the code remains the canonical truth. |

---

## 5  Testing Guidelines

1. **Reuse first** – search for existing test cases targeting the same behavior.  
2. **Minimal, meaningful** – each test validates one concept; avoid overlapping coverage.  
3. **Integration fit** – keep new tests in the same suite/layout conventions; no custom harness unless essential.

---

## 6  Automation Hooks (for CI / Linters / Bots)

- **Static lint**: enforce R1–R10.  
- **Commit hooks**: reject new files lacking justification comment.  
- **Size checker**: flag files > 250 LOC or functions > 50 LOC.  
- **Dependency graph**: alert on new cross-module cycles.  
- **Documentation linter**: ensure decision logs present for structural changes.

---

### 7  Quick-Reference Mnemonic

> **“C L E A N  C-T-R-I-P”**  
> *Clarity, Lean, Existing, Audit, Navigate – Code, Tests, Refactor, Integrate, Preserve*

---

**End of Playbook** – Embed or reference these sections in your AI coding assistant configuration so the model consistently enforces clarity, simplicity, and maintainability on every interaction.

## Message 3

That's not adhering to the input parameters, I'll provide clarification. The final product should be a clean markdown-block with all of the generalized instruction ordered chronologically by "category", example:



```markdown

# Requirements



**BEFORE ANY ACTION: Conduct a structured review of all recent codebase changes as well as their associated design rationales; synthesize key impacts and verify that any contemplated changes conform fully to existing project organization, standards, and conventions.**



- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.

- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.

- Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.

- Justify the creation of any new files and ensure all additions align with the existing project organization.

- Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.

- Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.

- Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.

- Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.



## Core Principles

- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.

- Maintain inherent simplicity while providing powerful functionality.

- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.

- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.



## General Principles

- Aim for simplicity, clarity, and maintainability in all project aspects

- Favor composition over inheritance when applicable

- Prioritize readability and understandability for future developers

- Ensure all components have a single responsibility

- Coding standards that promote simplicity and maintainability

- Document only integral decisions in a highly condensed form



## Code Organization

- Evaluate the existing codebase structure and identify patterns and anti-patterns

- Consolidate related functionality into cohesive modules

- Minimize dependencies between unrelated components

- Optimize for developer ergonomics and intuitive navigation

- Balance file granularity with overall system comprehensibility

- Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.

- Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.```"```'

```

## Message 4

```markdown
# Requirements
**BEFORE ANY ACTION: Conduct a structured review of recent codebase changes and their design rationales; verify that any contemplated modification fully conforms to existing organization, standards, and conventions.**

- Rigorously analyze current structure, patterns, and anti-patterns before touching code.
- Preserve existing functionality and clarity; ensure full alignment prior to change.
- Always clean up after yourself, adapting to the existing structure of the codebase.
- Justify the creation of any new file or module; prevent unnecessary proliferation.
- Confirm no loss of actionable detail, over-generalization, or diluted guidance.
- Favour minimal, high-impact modifications that maximize maintainability and clarity.

## Core Principles
- **Inherent clarity, structure, simplicity, elegance, precision, intent.**
- Simplicity conquers complexity; pursue high-leverage improvements with minimal disruption.
- Composition over inheritance; enforce single responsibility and low coupling.
- Relentless cleanliness—eliminate anomalies, redundancies, and dead code.
- Respect context: integrate with established patterns and rationales, never overwrite blindly.

## General Principles
- Write self-explanatory code through precise naming and structure.
- Provide comments only for non-obvious complexity; keep documentation concise.
- Balance file/module granularity for comprehension and developer ergonomics.
- Consolidate related functionality; minimize cross-component dependencies.
- Prioritize readability and future-developer friendliness.
- Uphold integrity, maintainability, and clarity at all times.

## Code Organization
- Evaluate and align with existing directory and module layout.
- Merge related modules; enforce single-responsibility units.
- Keep files and classes small enough to scan easily (~250 LOC guideline).
- Prefer composition; avoid dependency cycles and unnecessary inheritance.
- Tag enforceable rules separately from aspirational guidelines to aid tooling.
- Optimize structure for clarity, brevity, and autonomous toolability.

## Coding Standards & Implementation
- Adhere strictly to established patterns, conventions, and formatting tools.
- Eliminate anomalies, redundancies, and duplications immediately after change.
- Optimize logic for simplicity and elegance; avoid unnecessary abstraction.
- Maintain expressive structure and naming—clarity first.
- Ensure every change is deliberate, justified, and enhances long-term maintainability.

## Testing
- Verify and reuse existing tests before adding new ones.
- Add tests only for uncovered behavior; avoid duplication.
- Integrate new tests with the project’s framework and conventions.
- Maintain full coverage; all tests must pass before merge.

## Documentation
- Document only essential decisions and rationale in concise form (≤ 3 sentences).
- Reference prior architectural decisions where relevant.
- Embed machine-readable rule mappings (configs, schemas) for automation.
- Avoid verbose prose; the code remains the canonical source of truth.

## Post-Change Cleanup & Review
- Audit touched areas for residual clutter, naming drift, and inconsistencies.
- Remove TODOs, dead code, and stylistic deviation.
- Confirm complete alignment with structure, standards, and patterns.
- Optimize for navigability, clarity, and ergonomic structure.

## Automation & Enforcement
- Configure linters, formatters, CI, and code-review bots to enforce all rules automatically.
- Reject new files lacking justification or exceeding size limits.
- Use dependency-graph checks to prevent unwanted coupling.
- Maintain machine-verifiable standards; no change merges unless all gates pass.
```

## Message 5

Is there anything from these we could use to improve the rules even further?



```

{

  "goal_map": [

    {

      "explicit_asks": [

        "Analyze and understand the entire codebase, including its architecture, patterns, anti-patterns, and guiding principles.",

        "Summarize the purpose and scope of the codebase.",

        "Deep dive into all documentation, especially in docs/, memory bank, and relevant markdown files.",

        "Evaluate file/directory structure, focusing on a 'filestructure first' perspective (dirtree comprehension).",

        "Familiarize with named files (e.g., @rl-website-initial-notes.md, config.json, package.json, VSCode_Portable_Documentation.md, README.md, DEPLOYMENT.md, IMPORT_STANDARDS.md, etc.).",

        "Install dependencies and successfully run/start the codebase/website.",

        "Verify documentation and README accuracy and coherence with current project state.",

        "Identify and document all codebase functionality, focusing on maintainability, redundancy, and component relationships.",

        "Identify and address codebase sprawl, duplication, and lack of clear component relationships.",

        "Consolidate and standardize documentation (e.g., merging READMEs, deduplicating docs).",

        "Perform sensitivity/impact analyses before making any codebase changes.",

        "Consolidate and de-duplicate code and tests, ensuring existing function is preserved.",

        "Ensure autonomous validation of all actions (guardrails, feedback mechanisms, website/UI checks).",

        "Ensure all codebase modifications are globally consistent; avoid piecemeal/local changes.",

        "Map out a detailed, step-by-step, safe and systematic plan for enacting changes and improvements.",

        "Articulate the author’s/architect’s abstract structural intent (meta-, abstract, and 'vehicle/buttons' metaphors).",

        "Illustrate inter-component/module relationships (possibly via diagrams like mermaid or knowledge graphs).",

        "Identify opportunities for impactful, low-risk, high-value consolidation/refactoring.",

        "Recognize and manage SE-related requirements or opportunities (e.g., analytics, SEO, etc.).",

        "Maintain a perpetual focus on clarity, brevity, and cohesion throughout the project.",

        "Conduct a structured review of all recent codebase changes and associated design rationales before making any change.",

        "Verify that any contemplated modification fully conforms to the current organization, standards, and conventions.",

        "Analyze structure and identify patterns, anti-patterns, and directory/module layout.",

        "Locate and examine relevant tests for coverage; avoid duplicate testing.",

        "Justify creation of new files, modules, or dependencies; prevent unnecessary proliferation.",

        "Ensure changes preserve all existing functionality, clarity, and public contracts.",

        "Link any changes to prior architectural/design rationales, or create a new concise rationale when none exists.",

        "Ensure new tests only cover uncovered/edge-case behavior and integrate them with existing test frameworks.",

        "Clean up residual clutter, inconsistencies, anomalies, duplications, dead code, TODOs, and stylistic deviations after any change.",

        "Run and pass all formatting, linting, and full test suites prior to merge.",

        "Ensure all changes enhance or, at minimum, preserve integrity, maintainability, clarity."

      ],

      "hidden_assumptions": [

        "A thorough, end-to-end mental model of the entire codebase and its documentation is required before making any changes.",

        "Project and codebase are complex, modular, and potentially have significant technical and organizational debt.",

        "Safety, reliability, and the preservation of functionality are paramount during all operations.",

        "All introduced documentation and code changes must avoid unnecessary bloat or fragmentation.",

        "Changes must be trackable, reversible, and their impact must be visible (visual validation/operational guardrails).",

        "Team or downstream agents will rely on outputs/analyses/guidelines; instructions must be explicit, reproducible, and systematized.",

        "Any consolidation must balance file granularity, maintainability, and developer ergonomics.",

        "Codebase may be in flux or have inconsistent practices: mapping, reconciliation, and standardizing are necessary before action.",

        "The codebase is not self-explanatory—contexts from memory banks, usage docs, and even UI/UX must supplement code understanding.",

        "Abstract and meta-level viewpoints are required for truly optimal restructuring, not just local code analysis.",

        "Automated tools (dependency-cruiser, d3, manifests, etc.) are part of the workflow and must be understood and leveraged.",

        "‘Leaving the codebase in an optimal state’ includes both technical and documentation excellence.",

        "Comprehensive, retrievable commit history and rationales exist and are accessible.",

        "The codebase has a defined and documented organization and standard conventions.",

        "Naming, granularity, and directory conventions are already formalized.",

        "Test coverage information (what is covered, what is not) is available and up-to-date.",

        "There is an existing mechanism for referencing or recording architectural/design rationales (e.g., ADRs).",

        "Tooling is in place for linters, formatters, coverage, and CI enforcement.",

        "There are criteria for 'high-impact/minimally disruptive' changes and mechanisms to assess such impact.",

        "Change authors can evaluate and prove the absence of content, functionality, or clarity regressions.",

        "There are actionable size/coupling guidelines (e.g., file/class LOC, dependency rules).",

        "Automation rules and enforcement are outlined and machine-verifiable.",

        "Rollbacks/refactorings are possible and processes are known if redundancy/duplication is detected.",

      ],

      "sub_goals": [

        "Inventory and map all current files, directories, and modules. Produce a high-fidelity dirtree.",

        "Read and synthesize all relevant markdown and documentation files (memory bank, lineage, RulesForAI, etc.).",

        "Identify redundant, duplicate, or outdated files, components, and patterns in both code and documentation.",

        "Survey and comprehend existing tests, scripts, and build/deployment instructions.",

        "Summarize cohesion/relationships between website, utilities, plugins (Sublime/VScode, MCPs, etc.), and the core app logic.",

        "Verify all links (internal and external) within markdown and doc files are correct.",

        "Validate the codebase by successfully building and running the project (including dependencies, website, UI checks).",

        "Map out key functional workflows (e.g., folder scanning, manifests, plugin operation, knowledge-graph generation).",

        "Identify and enumerate all meta-patterns, abstract design principles, and overarching coding philosophies.",

        "Develop a safe, repeatable method for de-duplication/consolidation; test with non-breaking changes first.",

        "Plan and implement guardrails for future autonomous changes (validation, self-checks, feedback loops).",

        "Analyze project dependencies (package.json, config.json, etc.) for bloat, duplication, and consolidation options.",

        "Ensure all essential documentation (README, DEPLOYMENT, etc.) is consolidated, accurate, and up to date.",

        "Provide architectural diagrams or visualizations mapping high-level relationships, module interactions, and code/data flow.",

        "Extract and restate the project’s guiding principles, explicit and implicit, for future contributors/LLM agents.",

        "Propose or design general processes for maintenance, validation, and future refactoring with minimal risk.",

        "Assess developer and user ergonomics: navigation, cohesion, and intuitiveness.",

        "Perform a final, comprehensive audit before any commit, ensuring all consolidation/cleanup goals are achieved.",

        "Review and document all changes since the last successful state, surfacing their rationales.",

        "Enumerate and describe current organization: directory layout, module boundaries, grouping principles.",

        "List and rationalize prevailing patterns and anti-patterns within the codebase.",

        "Cross-reference all proposed changes against the existing structure, patterns, and rationale.",

        "Scan for and map all related tests, including test type, domain, and coverage gaps.",

        "Determine if any contemplated change duplicates or degrades existing structure, clarity, or test coverage.",

        "Prepare or update a brief, context-rich rationale for each intended change; link to ADRs if present.",

        "Explicitly justify every new file, dependency, or module, contextualizing it within existing organization.",

        "Confirm changes uphold naming, design, and directory conventions.",

        "Integrate any new test into the correct location in the test structure; check for and prevent duplicates.",

        "Run comprehensive format/lint/test/coverage checks; ensure all pass without warnings or failures.",

        "Audit for residual irregularities: TODOs, unused imports, anomalies, redundancies.",

        "If duplication or overlap is found, refactor to merge or eliminate before progressing.",

        "Block or revise the change if any non-conformity with standards, structure, or rationale is identified.",

        "Configure or augment automation (lint, format, CI) to enforce all non-negotiable rules going forward.",

      ],

      "blockers": [

        "Lack of complete and accurate mapping of codebase and documentation structure.",

        "Insufficient understanding of project workflows, dependencies, and implicit architectural decisions.",

        "Lack of established or enforced standards for file organization, naming, and module boundaries.",

        "Risk of breaking existing functionality due to incomplete end-to-end comprehension before acting.",

        "Potential for introducing bloat, redundancy, or fragmentation via poorly scoped changes or documentation.",

        "Inconsistent or incomplete documentation, making onboarding or validation difficult.",

        "Inadequate validation/guardrails for assessing the impact of changes (both functional and structural).",

        "Difficulty in visually or programmatically verifying the results of codebase modifications.",

        "Resistance to change or ambiguity on what constitutes an ‘optimal’ codebase state.",

        "Lack of automated or systematic means to perform duplications/consolidations safely.",

        "Absence of a finalized, shared philosophy document to guide abstract design decisions.",

        "Unclear or shifting project requirements, especially regarding SEO, analytics, or intended reusability.",

        "Absence, inconsistency, or inaccessibility of recent commit history and rationales.",

        "Unclear or outdated project organization, structural conventions, or naming policies.",

        "Incomplete, missing, or outdated test coverage information.",

        "Lack of formalized or accessible architectural decision records.",

        "Tooling gaps for lint, format, test, or CI enforcement.",

        "Ambiguity in what constitutes high-impact, low-disruption, or justified changes.",

        "Lack of process for justifying and merging new files, modules, or dependencies.",

        "No enforced mapping between changes and their rationales or ADRs.",

        "Automation is not fully enforced—making it possible for anomalies or violations to slip through.",

      ]

    }

  ]

}

```

## Message 6

Below are the **delta-additions** worth folding into the existing rule-set, grouped by the same chronological “category” flow you already use.  
(Everything here was missing or only implied before.)  
Merge or splice these bullets where they best fit; wording is already style-matched to your playbook.

```markdown
## Foundation & Discovery  (⇢ insert *before* “Requirements”)

- Inventory the entire file/directory tree and publish a high-fidelity dirtree snapshot.
- Build a mental model of architecture, patterns, and anti-patterns; document findings succinctly.
- Verify that the project **builds and runs end-to-end** (install deps, start site/app, exercise UI).
- Summarize project purpose, scope, and guiding principles in ≤ 10 lines for quick onboarding.
- Cross-check all documentação (README, DEPLOYMENT, docs/, memory-bank notes) for accuracy ⇢ fix drift.
- Consolidate duplicated or scattered docs into a single, canonical location; remove obsolete files.
- Produce (or update) an architecture diagram (Mermaid/PlantUML) showing module interactions & data flow.

## Requirements  (additions)

- Perform a **sensitivity / impact analysis** for every contemplated change; log expected blast radius.
- Draft a step-by-step action plan **before** touching code; include rollback strategy.
- Guarantee changes are *globally* consistent—no piecemeal local tweaks that break holistic cohesion.
- Link each change, PR, or commit to a concise Architectural Decision Record (ADR) when rationale is new.

## Code Organization  (additions)

- Maintain an always-up-to-date dependency graph; break new cycles immediately.
- Keep a live map of “component → owning directory” to prevent stealth sprawl.

## Testing  (additions)

- Include **UI/UX or API smoke-tests** for any outward-facing behavior you touch.
- Ensure guardrails/feedback mechanisms (snapshots, visual diffs, contract tests) validate autonomous actions.

## Documentation  (additions)

- Embed key diagrams adjacent to source (e.g., `docs/architecture.mmd`); auto-generate PNGs in CI.
- Record every consolidation or mass-rename in a CHANGELOG section titled *“Structural Refactors.”*

## Automation & Enforcement  (additions)

- CI must block merges if:
  1. Dirtree snapshot changed without updated diagram,
  2. Docs build fails,
  3. Dependency graph gains new cycles.
- Schedule nightly jobs that re-run lint/format/coverage + diagram generation to catch drift early.
```

**Why these help**

*Foundation & Discovery* bullets force a **complete mental model** before edits, addressing the largest blocker (“incomplete mapping of codebase and docs”).  
The new *Sensitivity / impact analysis* and *step-by-step plan* bullets convert hidden assumptions into actionable, verifiable checkpoints.  
Diagram, dependency-graph, and UI-guardrail requirements harden your **automation first** principle and surface regressions that pure unit tests miss.

Simply weave these bullets into the existing markdown-block and the rule-set will cover every explicit ask, hidden assumption, and blocker in the JSON brief.

## Message 7

please show the full updated markdown:

    # Requirements



    **BEFORE ANY ACTION: Conduct a structured review of all recent codebase changes as well as their associated design rationales; synthesize key impacts and verify that any contemplated changes conform fully to existing project organization, standards, and conventions.**



    * Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.

    * Perform sensitivity and impact analyses before making any codebase changes to ensure minimal risk and maximum value.

    * Ensure global consistency across all modifications, explicitly avoiding isolated or piecemeal changes.

    * Explicitly link all modifications to existing architectural/design rationales; if none exist, document a concise rationale.

    * If you decide to create a test for verifying newly added functionality, always ensure you first check the codebase for existing tests.

    * Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.

    * Justify the creation of any new files and ensure all additions align with the existing project organization.

    * Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.

    * Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.

    * Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.

    * Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.



    ## Core Principles



    * Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.

    * Maintain inherent simplicity while providing powerful functionality.

    * Embrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.

    * Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.



    ## General Principles



    * Aim for simplicity, clarity, and maintainability in all project aspects.

    * Favor composition over inheritance when applicable.

    * Prioritize readability and understandability for future developers.

    * Ensure all components have a single responsibility.

    * Changes must always be trackable and reversible; provide clear mechanisms to visualize and validate impacts (e.g., UI checks, operational guardrails).

    * Coding standards that promote simplicity and maintainability.

    * Adopt standards that are machine-verifiable and automatable to ensure continuous, systematic enforcement.

    * Document only integral decisions in a highly condensed form.



    ## Code Organization



    * Evaluate the existing codebase structure and identify patterns and anti-patterns.

    * Identify and document all inter-component relationships, utilizing visualizations or diagrams to clarify system architecture.

    * Consolidate related functionality into cohesive modules.

    * Minimize dependencies between unrelated components.

    * Optimize for developer ergonomics and intuitive navigation.

    * Balance file granularity with overall system comprehensibility.

    * Establish and enforce clear criteria for high-impact/minimally disruptive changes, ensuring these criteria are explicitly documented and adhered to.

    * Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.

    * Incorporate continuous review and auditing processes, ensuring all changes pass comprehensive formatting, linting, and test suites prior to merging.

    * Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.


## Message 8

```markdown
# Foundation & Discovery

* **Inventory the entire file/directory tree** and publish a high-fidelity dirtree snapshot.
* Build a mental model of architecture, patterns, and anti-patterns; **document findings succinctly**.
* **Verify end-to-end build & run** (install dependencies, start site/app, exercise UI).
* Summarize project purpose, scope, and guiding principles in ≤ 10 lines for quick onboarding.
* Cross-check all documentation (README, DEPLOYMENT, docs/, memory-bank notes) for accuracy; **fix drift**.
* **Consolidate duplicated or scattered docs** into a single, canonical location; remove obsolete files.
* Produce or update an **architecture diagram** (Mermaid/PlantUML) showing module interactions & data flow.

---

# Requirements

**BEFORE ANY ACTION: Conduct a structured review of all recent codebase changes as well as their associated design rationales; synthesize key impacts and verify that any contemplated changes conform fully to existing project organization, standards, and conventions.**

* Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.  
* **Perform sensitivity and impact analyses** before making any codebase change to ensure minimal risk and maximal value.  
* Ensure **global consistency** across all modifications, explicitly avoiding isolated or piecemeal changes.  
* **Explicitly link** every modification to prior architectural/design rationales; if none exist, add a concise rationale (ADR).  
* If you decide to create a test for verifying newly added functionality, always check the codebase for existing tests first.  
* Ensure the code is **self-explanatory and well-structured**, avoiding long comments or docstrings; use concise comments only for complex logic.  
* **Justify** the creation of any new files and ensure all additions align with the existing project organization.  
* Review recent code changes to ensure comprehensive cleanup after edits; rigorously respect established structure and conventions.  
* Maintain meticulous conformity for seamless, harmonious integration, upholding integrity and long-term maintainability.  
* Pay attention not only to how the system is constructed but also to the **rationale behind its design decisions**.  
* Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.  

---

## Core Principles

* Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.  
* Maintain inherent simplicity while providing powerful functionality.  
* Embrace the truth that **simplicity conquers chaos**: chase high-impact innovations that demand minimal disruption but yield profound, elegant improvements.  
* Recognize that the **chosen solution** (context-integrated, well-executed) always trumps any mere method.

---

## General Principles

* Aim for **simplicity, clarity, and maintainability** in all project aspects.  
* **Favor composition** over inheritance whenever applicable.  
* Prioritize readability and understandability for future developers.  
* Ensure each component has a **single responsibility**.  
* Changes must be **trackable and reversible**; provide clear mechanisms to visualize and validate impacts (UI checks, operational guardrails).  
* Adopt coding standards that promote simplicity, maintainability, and **machine-verifiable enforcement**.  
* Document only integral decisions in a highly condensed form.  

---

## Code Organization

* Evaluate the existing structure; **identify patterns and anti-patterns**.  
* Document inter-component relationships; use **visualizations** or diagrams to clarify architecture.  
* **Consolidate related functionality** into cohesive modules.  
* **Minimize dependencies** between unrelated components.  
* Optimize for developer **ergonomics and intuitive navigation**.  
* Balance file granularity with overall system comprehensibility.  
* Establish and enforce clear criteria for **high-impact / minimally disruptive** changes.  
* Ensure the directory structure is optimized for clarity, brevity, and autonomous toolability.  
* Maintain an **always-up-to-date dependency graph**; break new cycles immediately.  
* Keep a **live “component → owning-directory” map** to prevent stealth sprawl.  
* Incorporate continuous review and auditing; all changes must pass comprehensive format, lint, and test suites before merge.  
* Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.  

---

## Coding Standards & Implementation

* Adhere strictly to established patterns, conventions, and formatting tools.  
* Eliminate anomalies, redundancies, and duplications immediately after change.  
* Optimize logic for **simplicity and elegance**; avoid unnecessary abstraction.  
* Maintain expressive structure and naming—**clarity first**.  
* Ensure every change is deliberate, justified, and enhances long-term maintainability.  

---

## Testing

* **Reuse existing tests** before adding new ones.  
* Add tests only for **uncovered or edge-case behavior**; avoid duplication.  
* Integrate new tests with the project’s test framework and conventions.  
* Include **UI/UX or API smoke-tests** for any outward-facing behavior you touch.  
* Ensure guardrails/feedback mechanisms (snapshots, visual diffs, contract tests) validate autonomous actions.  
* Maintain full coverage; all tests must pass before merge.  

---

## Documentation

* Embed key diagrams adjacent to source (e.g., `docs/architecture.mmd`); **auto-generate** PNGs in CI.  
* Record every consolidation or mass-rename in a CHANGELOG section titled **“Structural Refactors.”**  
* Document only essential decisions and rationales in ≤ 3 sentences; link to ADRs when relevant.  
* Avoid verbose prose; **code remains the canonical source of truth**.  

---

## Post-Change Cleanup & Review

* Audit touched areas for residual clutter, naming drift, and inconsistencies.  
* Remove TODOs, unused imports, dead code, and stylistic deviation.  
* Confirm complete alignment with structure, standards, patterns, and dependency graph.  
* Optimize for navigability, clarity, and ergonomic structure.  

---

## Automation & Enforcement

* CI must block merges if:  
  1. The dirtree snapshot changes without an updated architecture diagram,  
  2. Documentation build fails,  
  3. The dependency graph gains new cycles.  
* Schedule **nightly jobs** that re-run lint, format, coverage, and diagram generation to catch drift early.  
* Configure linters, formatters, CI, and code-review bots to enforce all rules automatically.  
* Reject new files lacking justification comments or exceeding size limits.  
* Maintain machine-verifiable standards; no change merges unless **all gates pass**.  
```

## Message 9

please show the full updated markdown with **generalized** instructions (so it works on any codebase)

## Message 10

```markdown
# Foundation & Discovery
* **Inventory the entire file/directory tree** and publish a high-fidelity snapshot.
* Build a mental model of architecture, patterns, and anti-patterns; **document findings succinctly**.
* **Verify end-to-end build & run** (install dependencies, start the application, exercise UI/API flows).
* Summarize project purpose, scope, and guiding principles in ≤ 10 lines for quick onboarding.
* Cross-check all documentation (README, guides, ADRs, etc.) for accuracy; **fix any drift**.
* **Consolidate duplicated or scattered docs** into a single canonical location; remove obsolete files.
* Produce or update an **architecture diagram** (e.g., Mermaid, PlantUML) illustrating module interactions and data flow.

---

# Requirements
**BEFORE ANY ACTION:** Conduct a structured review of recent changes and their design rationales; synthesize key impacts and verify that any contemplated change fully conforms to current organization, standards, and conventions.

* Clean up after yourself, adapting to the existing structure.
* **Perform sensitivity/impact analyses** before coding to minimize risk and maximize value.
* Guarantee **global consistency**; avoid isolated or piecemeal tweaks.
* **Link** every change to an existing or new concise Architectural Decision Record (ADR).
* **Reuse existing tests** before adding new ones; create tests only for uncovered behavior.
* Write **self-explanatory, well-structured code**; use concise comments only for non-obvious logic.
* **Justify** every new file/module; ensure alignment with project organization.
* Review and tidy surrounding code after edits; respect established conventions.
* Preserve existing functionality and clarity; maintain alignment before merging.

---

## Core Principles
* **Clarity, structure, simplicity, elegance, precision, intent.**
* Simplicity conquers complexity—pursue high-impact changes with minimal disruption.
* **Composition over inheritance**; enforce single responsibility and low coupling.
* The **context-integrated, well-executed solution** always trumps clever implementations.

---

## General Principles
* Strive for **simplicity, clarity, and maintainability** in every artifact.
* Favor composition whenever possible.
* Prioritize readability for future developers.
* Ensure each unit has a **single responsibility**.
* All changes must be **trackable and reversible**; provide guardrails (UI checks, contract tests, etc.).
* Enforce standards via **machine-verifiable tooling**.
* Document only essential decisions **in condensed form**.

---

## Code Organization
* Evaluate existing structure; **surface patterns and anti-patterns**.
* Visualize inter-component relationships with up-to-date diagrams.
* **Consolidate related functionality** into cohesive modules.
* **Minimize dependencies** between unrelated components.
* Optimize for developer **ergonomics and intuitive navigation**.
* Balance file granularity with overall comprehensibility.
* Maintain an **up-to-date dependency graph**; break new cycles immediately.
* Keep a **live “component → owning-directory” map** to prevent sprawl.
* All changes must pass format, lint, and full test suites before merge.
* Reject modifications that introduce anomalies, inconsistencies, or redundancy.

---

## Coding Standards & Implementation
* Adhere strictly to established patterns, conventions, and formatting tools.
* Remove anomalies, redundancies, and dead code immediately after change.
* Favor **simple, elegant logic**; avoid unnecessary abstraction.
* Use expressive naming—**clarity first**.
* Ensure every change is deliberate, justified, and improves long-term maintainability.

---

## Testing
* **Reuse before create**—extend existing tests when possible.
* Add tests only for **new, uncovered, or edge-case behavior**.
* Integrate tests with the project’s test framework and directory layout.
* Include **UI/UX or API smoke-tests** for outward-facing behavior.
* Employ guardrails (snapshots, visual diffs, contract tests) to validate autonomous actions.
* All tests, lint, and coverage checks must pass prior to merge.

---

## Documentation
* **Embed key diagrams** next to source (e.g., `docs/architecture.mmd`); auto-render images in CI.
* Record every large rename or consolidation in a CHANGELOG section titled **“Structural Refactors.”**
* Keep decision logs concise (≤ 3 sentences); link to ADRs when appropriate.
* Favor the code as the **canonical source of truth**; avoid verbose prose.

---

## Post-Change Cleanup & Review
* Audit touched areas for residual clutter, naming drift, and stylistic deviations.
* Remove TODOs, unused imports, and dead code.
* Verify full alignment with structure, standards, and dependency graph.
* Optimize for navigability, clarity, and ergonomic structure.

---

## Automation & Enforcement
* Continuous Integration must block merges if:  
  1. The file-tree snapshot changes without an updated diagram,  
  2. Documentation build fails,  
  3. The dependency graph gains new cycles.
* Schedule **nightly jobs** to rerun lint, format, coverage, and diagram generation.
* Configure linters, formatters, and review bots to enforce all rules automatically.
* Reject new files lacking justification or exceeding size limits.
* No change merges unless **all automated gates pass**.
```
