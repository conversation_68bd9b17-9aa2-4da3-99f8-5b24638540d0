# Optimized Cursor Rules

Conversation ID: 67cddb38-9124-8008-9e92-a00db490b274

## Message 1

how could we tailor a highly optimized .cursorrules for this particular project to ensure inherent understanding and persistent guidelines rooted in the descriptions in @rl-website-initial-notes.md  ?
# Ringerike Landskap



---



## Company Overview



Ringerike Landskap AS is a relatively young but growing landscaping and machine-contracting company based in Hole, Buskerud. With extensive experience in the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik), they deeply understand the terrain’s unique demands, crafting outdoor areas that skillfully blend functionality, resilience, and authentic personal style. From Røyse to Hønefoss, their detailed knowledge of local conditions ensures robust foundations, effective drainage, and frost-proof solutions, whether they're building elegant stone patios (belegningsstein), durable retaining walls, vibrant gardens, or practical driveways.



What truly sets Ringerike Landskap apart are the two young, wise, and dedicated owners, who take genuine pride in their work and personally invest in each customer relationship. Known for their exceptional interpersonal skills and unwavering commitment, they consistently exceed expectations, leaving clients pleasantly surprised by the care, creativity, and precision of their craftsmanship. The owners’ diverse expertise—including professional welding and specialized use of corten steel—allows them to deliver innovative, personalized solutions that beautifully reflect client preferences and harmonize with the Norwegian climate.



---



## Team



The company consists of two owners who are young and wise, and they take actual **pride** in their work. They make it a point to themselves to **invest** in every customer they meet. Both have multiple disciplines, including one being a professional welder, making corten-steel something they intertwine in their work.



They are creative and flexible while covering a wide and flexible skillset. Whether it's crafting welcoming front yards or designing sophisticated commercial landscapes, the team ensures each solution reflects the client's preferences while honoring the Norwegian climate. They are known for shaping outdoor spaces that stand out in the local environment.



---



## Customer Approach



Clients consistently appreciate the genuine personal connection and meticulous attention provided by Ringerike Landskap. Each project begins with transparent, thoughtful dialogue where ideas, preferences, and creative possibilities are openly explored. The owners' hands-on involvement and sincere investment in every customer ensure that projects not only meet but exceed expectations, creating lasting satisfaction and often delightful surprises.



Their versatile portfolio, ranging from intimate backyard enhancements to expansive commercial landscapes, demonstrates a proven flexibility and deep commitment to quality at any scale. Customers within a 20–50 km radius can feel confident, from initial consultation through final implementation, knowing their outdoor spaces will uniquely reflect their personal vision and practical requirements.



---



## Services



The company focuses on eight core services:



1. **Kantstein** (curbstones)

2. **Ferdigplen** (ready lawn)

3. **Støttemur** (retaining walls)

4. **Hekk/beplantning** (hedges and planting)

5. **Cortenstål** (steel installations)

6. **Belegningsstein** (paving stones)

7. **Platting** (decking)

8. **Trapp/repo** (stairs and landings)



Every project showcases meticulous attention to detail—from selecting high-quality materials to matching each space with the best design strategies. By emphasizing local conditions and seasonal changes, Ringerike Landskap consistently delivers outdoor solutions that stand the test of time.



---



## Digital Presence



Ringerike Landskap AS is a forward-thinking company, actively leveraging their digital presence within a market where few landscaping companies in Norway have fully embraced technology. Recognizing this opportunity, they are currently finalizing an engaging and modern website aimed at expanding their customer reach and clearly demonstrating their unique expertise.



Ringerike Landskap's new website will serve as more than just a digital portfolio—it will be an authentic representation of how two dedicated owners personally invest in delivering exceptional landscaping solutions throughout the Ringerike region. By emphasizing their customer-centered approach, creative versatility, and consistent ability to exceed expectations, the site will effectively communicate why local residents and businesses continue to entrust their outdoor spaces to this distinctive landscaping company. The website's strategic focus on the owners' personal involvement and commitment to customer satisfaction positions Ringerike Landskap as the natural choice for anyone seeking truly personalized landscaping expertise in Hole, Hønefoss, Jevnaker, Sundvollen, and Vik.



The website is in development as a dynamic digital showcase, spotlighting Ringerike Landskap’s specialized landscaping services for both private homes and commercial properties throughout the Ringerike region. Central to its appeal are the company’s two young owners' personalized approach, exceptional craftsmanship, and genuine commitment to customer satisfaction.



---



## Website Purpose



The new company website is designed to significantly expand their reach within the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik, and surrounding areas) by *leveraging* advanced and unique search engine optimization (SEO) techniques. More than just an online presence, the site will function as a dynamic platform, strategically built to connect with homeowners, businesses, and property developers actively searching for local landscaping services.



The website's ultimate purpose is to solidify Ringerike Landskap's position as *the* leading landscaping experts in the Ringerike region. It achieves this not only by presenting their portfolio but also by actively engaging their target audience through a customer-centric online experience and a powerful, locally-focused SEO strategy.



---



## SEO



The site’s content strategy focuses on delivering fresh, search-engine-friendly information and visually compelling examples—ranging from robust stone patios to custom corten-steel features—that resonate with homeowners, commercial property managers, and anyone seeking unique outdoor transformations. This SEO-centered approach underscores the owners’ commitment to exceptional craftsmanship, effective project management, and transparent communication, distinguishing Ringerike Landskap as the go-to resource for innovative landscape solutions in Buskerud.



Target Groups:

    - Primary: Homeowners, businesses, property developers, and local residents in Ringerike and surrounding areas seeking professional landscaping services.

    - Primary: Prospective customers looking for tailored landscaping solutions or inspiration for outdoor spaces.

    - Secondary: Users researching local landscaping solutions online or comparing contractors.

    - Secondary: Existing customers revisiting the site for additional services or to share testimonials.

    - Secondary: Existing customers reviewing projects or contacting the company.



Core Strategies:

    - Hyperlocal SEO: The website will be meticulously optimized for location-specific keywords, including variations and long-tail phrases (e.g., "anleggsgartner Røyse" "pris på støttemur Jevnaker," "cortenstål i hage Ringerike"). This ensures high visibility in search results when potential customers within their service area are seeking relevant services.

    - Technical SEO Foundation: The website's architecture is built for speed, mobile-friendliness, and optimal crawlability by search engines. Optimized images, clean code, structured data markup (Schema.org), and a clear sitemap ensure the site's content is easily

    - Optimized UI/UX: User-friendly navigation and detailed service breakdowns—covering everything from welcoming front yards to sophisticated commercial landscapes—to inspiring project galleries categorized by season and location, the site is strategically structured to enhance engagement.

    - Showcasing professionalism: Transparent communication, a genuine commitment to customer satisfaction, and thoughtful seasonal insights, the platform guides visitors effortlessly from inspiration to consultation, positioning Ringerike Landskap as the trusted local authority in outdoor design and landscape craftsmanship.

    - Authenticity: The website will authentically portray the two dedicated owners, their personal investment in each project, and their commitment to exceeding client expectations. This "people-first" approach, reinforced through testimonials, case studies, and behind-the-scenes content, builds trust and resonates with local customers, differentiating Ringerike Landskap from larger competitors.

    - Clear calls-to-action ("Book Gratis Befaring," "Få et Pristilbud"), user-friendly contact forms, and a streamlined user experience encourage inquiries and consultations.



User Interface:

    - Visual Style: Clean design with high-quality imagery showcasing landscaping work across prioritized services; green accents reflect nature and sustainability.

    - Navigation: Clear menu structure.

    - Call-to-Actions: Prominent buttons like “Book Gratis Befaring” should stand out visually to encourage conversions.

    - Project Showcase: Grid-style layout with filters for easy exploration of completed works categorized by job type or location.

    - Mobile-Friendly Features: Responsive layouts ensuring usability across all devices.



Responsive Practices:

    - Mobile-First Approach: Start with mobile layout and enhance for larger screens

    - Fluid Typography: Scale text based on screen size

    - Flexible Images: Ensure images adapt to their containers

    - Touch-Friendly UI: Larger touch targets on mobile

    - Performance Optimization: Lazy loading and optimized assets

    - Testing: Test on multiple devices and screen sizes

    - Accessibility: Ensure accessibility across all screen sizes



User Experience:

    - Browsing Services: Visitors land on the homepage or “Hva vi gjør” page to learn about the firm’s core landscaping solutions.

    - Exploring Projects: They can explore real examples under “Prosjekter,” filtering results by interest—like building a new terrace or installing a støttemur.

    - Seasonal Tips: The site subtly adapts to reflect current or upcoming seasons, guiding users to relevant services (e.g., planting ideas in spring).

    - Users filter projects by category or location to find relevant examples.

    - Testimonials provide social proof and build trust throughout the decision-making process.

    - Requesting a Quote: When ready, they fill out the contact form or tap a “Book gratis befaring” button to schedule an on-site evaluation.

    - Personal Follow-Up: The Ringerike Landskap team personally contacts clients to discuss specific needs, ensuring straightforward, no-frills communication.



Optimizations:

    - Meta Tags: Dynamic meta tags for each page

    - Structured Data: Schema.org markup for rich results

    - Semantic HTML: Proper heading structure

    - Canonical URLs: Proper URL structure



Accessibility:

    - Semantic HTML: Proper use of HTML elements

    - ARIA Attributes: For enhanced accessibility

    - Screen Reader Support: Proper labels and descriptions

    - Keyboard Navigation: All interactive elements are keyboard accessible



---



## Website Requirements



The website should be fully responsive, providing an optimal viewing experience across a wide range of devices, from mobile phones to desktop monitors. This is achieved through a combination of Tailwind CSS utility classes, custom responsive components, and media queries.



Component Architecture:

    - Well-organized component structure following a feature-based organization

    - Reusable UI components in the `ui` directory

    - Page-specific components in feature directories

    - Layout components for consistent page structure



State Management:

    - Uses React hooks for component-level state management

    - Custom hooks for reusable logic (e.g., `useMediaQuery`, `useScrollPosition`)

    - Local storage integration for persisting user preferences



Data Structure:

    - Static data files for services and projects

    - Well-defined TypeScript interfaces for type safety

    - Structured content with rich metadata



Architecture:

    - Clear separation of concerns between UI, data, and business logic

    - Component composition patterns for flexible UI building

    - Abstraction layers that separate presentation from data



Design Philosophy:

    - Focus on accessibility with proper semantic HTML and ARIA attributes

    - Performance optimization with code splitting and asset optimization

    - SEO-friendly structure with metadata and schema.org markup



Code Organization Principles:

    - High cohesion: Related functionality is grouped together

    - Low coupling: Components are independent and reusable

    - Consistent naming conventions and file structure



Seasonal Adaptation

    - Content changes based on current season

    - Affects projects, services, and UI elements

    - Automatic detection and mapping



Component Hierarchy

    - UI Components → Feature Components → Page Components

    - Composition over inheritance

    - Reusable building blocks



Data Flow

    - Static data in `/data`

    - Props down, events up

    - Context for global state

    - Custom hooks for logic



Responsive Design

    - Mobile-first approach

    - Tailwind breakpoints

    - Fluid typography

    - Adaptive layouts



Type Safety

    - TypeScript throughout

    - Strict type checking

    - Interface-driven development



Navigation Structure:

    - Home (/)

        - Hero section

        - Seasonal projects carousel

        - Service areas list

        - Seasonal services section

        - Testimonials section



    - Services (/hva-vi-gjor) # todo:change to `/hva`

        - Hero section

        - Service filtering (Category, Function, Season)

        - Service listings with details

        - Benefits section (Lokalkunnskap, Tilpassede løsninger, Klimatilpasset)

        - CTA section



    - Projects (/prosjekter)

        - Hero section

        - Project filtering (Category, Location, Tag, Season)

        - Project grid with details

        - Seasonal recommendations



    - About Us (/hvem-er-vi) # todo:change to `/hvem`

        - Hero section

        - Company information

        - Team members

        - Core values and benefits



    - Contact (/kontakt)

        - Hero section

        - Contact form

        - Location information

        - Service areas



Dynamic Routes:

    - Service Detail (/tjenester/:id)

        - Service description

        - Features list

        - Related projects

        - Image gallery

        - Contact CTA



    - Project Detail (/prosjekter/:id)

        - Project description

        - Project specifications

        - Materials and features

        - Related service

        - Testimonial (if available)



Additional Pages:

    - Testimonials (/kundehistorier)

        - Testimonial filtering

        - Testimonial grid

        - Testimonial categories

        - CTA section



Service Areas:

    - Røyse (Main Base)

    - Hønefoss

    - Hole kommune

    - Jevnaker

    - Sundvollen

    - Vik



Seasonal Content Adaptation:

    - Spring (Vår)

        - Focus on: Hekk og Beplantning, Ferdigplen

        - Relevant tags: beplantning, plen, hage



    - Summer (Sommer)

        - Focus on: Platting, Cortenstål, Belegningsstein

        - Relevant tags: terrasse, uteplass, innkjørsel



    - Fall (Høst)

        - Focus on: Støttemurer, Kantstein, Trapper og Repoer

        - Relevant tags: terrengforming, støttemur, trapp



    - Winter (Vinter)

        - Focus on: Planning and Design

        - Relevant tags: planlegging, design, prosjektering



Service Categories:

    - Belegningsstein

    - Cortenstål

    - Støttemurer

    - Platting

    - Ferdigplen

    - Kantstein

    - Trapper og Repoer

    - Hekk og Beplantning




## Message 2

**Crafting customized rules**

I’m forming a .cursorrules file for optimized project coding guidelines, potentially for a RedwoodJS or Cursor editor environment. This involves detailed crafting and adherence to specific formatting rules.

**Crafting a detailed file**

I’m compiling a .cursorrules file with sections for directory structure, feature organization, coding style, TypeScript, SEO, accessibility, and seasonal adaptability to ensure comprehensive project guidelines.

**Ensuring adherence**

I’m working on a .cursorrules file to include persistent guidelines for architecture, data structure, design, SEO, teamwork, and coding. It could be formatted in JSON, YAML, or as a structured text.

## Message 3

Below is an example `.cursorrules` file you could adapt for this Ringerike Landskap website project. The goal is to enforce consistency, clarity, and best practices outlined in your `@rl-website-initial-notes.md`—including architecture, naming conventions, SEO awareness, accessibility, and the “people-first” emphasis. Tailor the sections as needed, but this template should help you maintain persistent guidelines across your codebase.

---

```jsonc
{
  "$schema": "https://raw.githubusercontent.com/getcursor/cursor/main/docs/cursorrules.schema.json",

  "rules": {
    // 1. PROJECT & BRANDING CONSISTENCY
    "brand/landing-pages-structure": {
      "description": "Enforce consistent landing page structure that reflects the Ringerike Landskap brand and the website’s seasonal adaptation approach.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/pages/**/index.tsx",
          "checks": [
            {
              "type": "presence",
              "value": "Hero",
              "message": "All main landing pages must include a Hero section aligned with the brand’s local, personal, trust-building focus."
            },
            {
              "type": "presence",
              "value": "SeasonalSection",
              "message": "Landing pages should contain a seasonal highlight or an adaptive content block reflecting the current season (Spring, Summer, Fall, Winter)."
            },
            {
              "type": "presence",
              "value": "Testimonials or a CTA for 'Book Gratis Befaring'",
              "message": "Each page should feature social proof (testimonials) or direct CTA for user conversion."
            }
          ]
        }
      ]
    },

    // 2. NAMING & ROUTING
    "routing/naming-conventions": {
      "description": "Ensure route paths conform to the localized names recommended in the notes (e.g., 'hvem' instead of 'about', etc.).",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "src/pages/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "(/about|/services|/projects)",
              "inverse": true,
              "message": "Use the Norwegian route paths: 'hvem' for About Us, 'hva' for Services, 'prosjekter' for Projects. Avoid English route naming."
            }
          ]
        }
      ]
    },

    // 3. ARCHITECTURE & FILE STRUCTURE
    "architecture/component-hierarchy": {
      "description": "Encourage the three-tiered component approach: UI → Feature → Page, with clear separation of concerns.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "directoryStructure",
              "value": {
                "ui": "Shared UI components",
                "features": "Feature-specific logic",
                "pages": "Page-level components"
              },
              "message": "Components must follow the recommended hierarchy: UI components in /ui, feature logic in /features, and page-level in /pages."
            }
          ]
        }
      ]
    },

    // 4. TYPING & STATE MANAGEMENT
    "typescript/strict-typing": {
      "description": "Ensure strict TypeScript usage, including well-defined interfaces for data and props.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "any",
              "message": "Avoid `any` type to maintain strict type safety for data structures (e.g., Services, Projects, Seasonal changes)."
            }
          ]
        }
      ]
    },
    "state/custom-hooks": {
      "description": "All shared business logic must be implemented via custom hooks for clarity and reusability (e.g., useMediaQuery, useScrollPosition).",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/hooks/**/*.ts",
          "checks": [
            {
              "type": "presence",
              "value": "export function use",
              "message": "Hooks should be named `useSomething` and exported from /hooks directory."
            }
          ]
        }
      ]
    },

    // 5. SEO & STRUCTURED DATA
    "seo/metadata": {
      "description": "All pages should define appropriate meta tags, structured data, and location-specific keywords.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/pages/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "presence",
              "value": "Helmet",
              "message": "Use React Helmet (or Next.js Head) to define meta tags (title, description) and location-based keywords for hyperlocal SEO."
            },
            {
              "type": "presence",
              "value": "Schema.org",
              "message": "Where relevant, include schema.org structured data for local business and services (støttemur, kantstein, etc.)."
            }
          ]
        }
      ]
    },

    // 6. ACCESSIBILITY & SEMANTIC HTML
    "a11y/semantic-html": {
      "description": "Promote inclusive, semantic markup for better user experience and SEO.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "<div[^>]+role=\"button\"",
              "message": "Use native <button> elements instead of divs with role='button'."
            },
            {
              "type": "regex",
              "value": "<img[^>]+alt=\"\"",
              "message": "All <img> elements should include alt text describing the image content. Avoid empty alt attributes unless truly decorative."
            }
          ]
        }
      ]
    },

    // 7. VISUAL & RESPONSIVE PRINCIPLES
    "style/responsive-tailwind": {
      "description": "Ensure mobile-first approach with Tailwind breakpoints, fluid typography, and accessible color contrast.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "className=\".*(xl:|md:|sm:|lg:)\"",
              "message": "Use Tailwind’s responsive classes (e.g., sm:, md:, lg:, xl:) to ensure consistent adaptation across all devices."
            }
          ]
        }
      ]
    },

    // 8. SEASONAL CONTENT ADAPTATION
    "features/seasonal-adaptation": {
      "description": "Whenever referencing a season-specific component or content, ensure relevant tags or components match the guidelines from the notes.",
      "level": "info",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "Season\\.(Spring|Sommer|Høst|Vinter)",
              "message": "Use the recognized four-season pattern from the notes: Spring(Vår), Summer(Sommer), Fall(Høst), Winter(Vinter)."
            }
          ]
        }
      ]
    },

    // 9. “PEOPLE-FIRST” & COMPANY STORYTELLING
    "content/voice-tone": {
      "description": "Remind developers to incorporate the personal investment angle from the owners and the brand’s focus on trust & authenticity.",
      "level": "info",
      "applyPatterns": [
        {
          "pattern": "src/content/**/*.{ts,tsx,md,mdx,json}",
          "checks": [
            {
              "type": "regex",
              "value": "(Owner|Team|Personal|Trust|Local knowledge|People-first)",
              "inverse": false,
              "message": "Include brand values (local knowledge, trust, personal involvement) in relevant copy sections or data content."
            }
          ]
        }
      ]
    },

    // 10. TESTIMONIAL & CTA PLACEMENT
    "ui/testimonial-cta-structure": {
      "description": "Ensure that each service or project page includes a testimonial snippet and a clear CTA for user conversion.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/pages/(hva|prosjekter)/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "presence",
              "value": "TestimonialSection",
              "message": "Include a testimonial or social proof snippet on service or project pages to build trust."
            },
            {
              "type": "presence",
              "value": "BookGratisBefaring",
              "message": "Always present a 'Book Gratis Befaring' CTA for frictionless user conversion."
            }
          ]
        }
      ]
    }
  }
}
```

---

### How to Use & Customize

1. **Adjust Rule Severity**  
   - Each rule has a `level` field that can be `info`, `warning`, or `error`. Increase or decrease severity based on how strictly you want to enforce each principle.

2. **Refine Patterns**  
   - The `applyPatterns` array matches file paths (e.g., `src/pages/**`). Update these to reflect your actual file structure.

3. **Extend Checks**  
   - You can add or remove checks under each rule. For example, you might add a check requiring certain components (e.g., `SeasonalSection`) only on specific pages or remove checks you find extraneous.

4. **Connect with Existing Tooling**  
   - If you already use ESLint, Prettier, or other linters, `.cursorrules` should complement—not duplicate—those rules. Keep advanced coding style checks in ESLint, and let `.cursorrules` reinforce project-specific conventions (naming, architecture, brand values).

5. **Iterate Over Time**  
   - As you finalize your site or add new features (e.g., advanced forms, new seasonal flows), update `.cursorrules` accordingly. It’s a living document that evolves with the project.

By embedding these project-specific guidelines into `.cursorrules`, you’ll foster a consistent development flow that always reflects the unique focus on local expertise, personal investment, and the technical/architectural best practices outlined in your `@rl-website-initial-notes.md`.

## Message 4

please account for the full context:

# Dir `docs`



*Files marked `[-]` are shown in structure but not included in content.



### File Structure



```

├── 01-project-state

│   ├── 01-current-codebase-reality.md

│   ├── 02-technical-debt-inventory.md

│   └── 03-company-information.md

├── 02-codebase-map

│   ├── 01-directory-structure.md

│   ├── 02-architecture-overview.md

│   └── 03-component-hierarchy.md

├── 03-implementation-patterns

│   ├── 01-current-patterns.md

│   ├── 02-component-patterns.md

│   └── 03-target-patterns.md

├── 04-visual-systems

│   ├── 01-screenshot-infrastructure.md

│   ├── 02-design-guidelines.md

│   └── 03-styling-approach.md

├── 05-development-workflow

│   ├── 01-environment-setup.md

│   ├── 02-optimization-guide.md

│   └── 03-accessibility-guide.md

├── AI-README.md

├── README.md

└── rl-website-initial-notes.md

```





#### `01-project-state\01-current-codebase-reality.md`



```markdown

# Current Codebase Reality



This document provides an honest assessment of the current state of the Ringerike Landskap website codebase. Understanding the real state of the code is essential for making effective improvements.



## Project Overview



The Ringerike Landskap website is a React/TypeScript application built with modern web technologies:



- **Frontend**: React 18.3.1

- **Build Tool**: Vite 5.4.2

- **Styling**: Tailwind CSS 3.4.1

- **Routing**: React Router 6.22.3

- **TypeScript**: Version 5.5.3



## Current Structure



The codebase follows a modular structure organized by feature and responsibility:



```

src/

├── components/       # Reusable UI components

│   ├── layout/       # Layout components (Header, Footer)

│   └── ui/           # Basic UI components (Button, Card, etc.)

├── features/         # Feature-specific components

│   ├── home/         # Home page specific components

│   ├── services/     # Services page specific components

│   └── projects/     # Projects page specific components

├── hooks/            # Custom React hooks

├── pages/            # Page components

│   ├── home/

│   ├── about/

│   ├── services/

│   ├── projects/

│   └── contact/

├── data/             # Mock data and content definitions

│   ├── services.ts

│   ├── projects.ts

│   └── testimonials.ts

├── types/            # TypeScript type definitions

├── utils/            # Utility functions

└── App.tsx           # Main application component

```



## Actual State Assessment



### Strengths



1. **Component Organization**: Generally well-organized by feature and responsibility

2. **TypeScript Implementation**: Basic type definitions are in place

3. **Routing Structure**: Clean routing implementation with React Router

4. **Styling System**: Consistent use of Tailwind CSS



### Areas Needing Improvement



1. **Inconsistent Component Patterns**: Components follow mixed patterns:

   - Some use props drilling extensively

   - Others use React context irregularly

   - Mix of functional and class-based approaches



2. **TypeScript Usage**: 

   - Type definitions exist but are incomplete

   - Some components lack proper prop typing

   - `any` type is used in several places



3. **Data Management**:

   - Data is primarily stored as static TypeScript objects

   - No centralized state management approach

   - Inconsistent data fetching patterns



4. **Performance Considerations**:

   - Limited use of memoization

   - No code splitting implemented

   - Image optimization is inconsistent



## Critical Systems



### Screenshot System



The project has a sophisticated screenshot management system that is essential to the development workflow:



- Captures website at various viewport sizes

- Maintains historical screenshots in timestamped directories

- Integrates with development workflow through `npm run dev:ai`

- Enables AI-assisted development through visual feedback



This system is critical and must be handled carefully in any refactoring efforts.



### Content Management



Content is currently managed through TypeScript files in `src/data/`:



- Services, projects, and testimonials are defined as typed arrays

- Images are referenced with paths to the `public` directory

- Relationship mappings exist between service categories and projects



The content structure should be preserved even as implementation patterns are improved.



## Technical Debt Inventory



1. **Component Refactoring Needs**:

   - Standardize component patterns

   - Improve prop typing

   - Implement proper error boundaries



2. **State Management Improvements**:

   - Implement a consistent state management approach

   - Reduce prop drilling

   - Consider context API usage or a state management library



3. **Performance Optimizations**:

   - Implement code splitting for routes

   - Add lazy loading for components

   - Optimize image loading strategy



4. **Testing Gap**:

   - No testing infrastructure currently in place

   - Critical components need unit tests

   - Visual regression testing would benefit from the screenshot system



## Next Steps



The primary focus should be on:



1. Standardizing component patterns

2. Implementing a consistent state management approach

3. Enhancing TypeScript usage for better type safety

4. Leveraging the screenshot system for visual regression testing



This assessment serves as the foundation for targeted improvements while maintaining the existing functionality and visual design. ```





#### `01-project-state\02-technical-debt-inventory.md`



```markdown

# Technical Debt Inventory



This document provides a comprehensive inventory of technical debt in the Ringerike Landskap website codebase. It identifies specific issues that need to be addressed as the project moves forward.



## Component Structure Issues



1. **Inconsistent Component Patterns**

   - Some components use functional patterns while others are class-based

   - Props drilling is used extensively in some areas, context in others

   - Component organization lacks consistency across features



2. **Component Props Typing**

   - Some components lack proper TypeScript interface definitions

   - Inconsistent use of default props and prop validation

   - Overuse of any type in component definitions



## State Management Concerns



1. **Mixed State Management Approaches**

   - Local state (useState) is used inconsistently

   - React Context is implemented in different ways across the codebase

   - No clear pattern for state sharing between components



2. **Prop Drilling**

   - Excessive prop drilling in nested component hierarchies

   - Missing context providers for deeply nested state requirements

   - Unclear state ownership in complex component trees



## TypeScript Implementation



1. **Type Safety Gaps**

   - Incomplete interface definitions for core data structures

   - Use of `any` type in several components and utilities

   - Missing generics in reusable components and hooks



2. **Type Organization**

   - Type definitions are scattered across the codebase

   - Inconsistent naming conventions for interfaces and types

   - Duplicate type definitions in multiple files



## Performance Optimization Needs



1. **Rendering Optimization**

   - Missing memoization for expensive calculations

   - No code splitting implemented for route-based components

   - Unnecessary re-renders in complex component trees



2. **Asset Optimization**

   - Inconsistent image optimization strategy

   - Large assets affecting initial page load

   - No lazy loading for below-the-fold images



## Testing Coverage



1. **Missing Tests**

   - No unit tests for core components

   - No integration tests for key user flows

   - Missing visual regression tests



2. **Test Infrastructure**

   - No testing framework set up

   - Missing test utilities and helpers

   - No CI/CD integration for testing



## Code Organization



1. **Directory Structure**

   - Some components are placed in inconsistent locations

   - Unclear boundaries between features/ and components/ directories

   - Utility functions not properly organized by purpose



2. **File Naming**

   - Inconsistent file naming conventions

   - Some files contain multiple components

   - Lack of index files for cleaner imports



## Accessibility Issues



1. **A11y Implementation**

   - Missing ARIA attributes on interactive elements

   - Insufficient keyboard navigation support

   - Color contrast issues in some UI components



2. **Semantic HTML**

   - Non-semantic div soup in some components

   - Improper heading hierarchy

   - Missing alt text on some images



## Action Plan



Based on this inventory, the following prioritized actions are recommended:



1. **High Priority**

   - Standardize component patterns across the codebase

   - Improve TypeScript type definitions and usage

   - Implement consistent state management approach



2. **Medium Priority**

   - Add code splitting for route-based components

   - Create test infrastructure and add tests for key components

   - Address major accessibility issues



3. **Lower Priority**

   - Refine directory structure and naming conventions

   - Optimize assets for performance

   - Clean up unused code and dependencies



This inventory should be reviewed and updated regularly as the project evolves. ```





#### `01-project-state\03-company-information.md`



```markdown

# Ringerike Landskap Company Information



## Company Overview



Ringerike Landskap AS is a relatively young but growing landscaping and machine-contracting company based in Hole, Buskerud. With extensive experience in the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik), they deeply understand the terrain's unique demands, crafting outdoor areas that skillfully blend functionality, resilience, and authentic personal style. From Røyse to Hønefoss, their detailed knowledge of local conditions ensures robust foundations, effective drainage, and frost-proof solutions, whether they're building elegant stone patios (belegningsstein), durable retaining walls, vibrant gardens, or practical driveways.



What truly sets Ringerike Landskap apart are the two young, wise, and dedicated owners, who take genuine pride in their work and personally invest in each customer relationship. Known for their exceptional interpersonal skills and unwavering commitment, they consistently exceed expectations, leaving clients pleasantly surprised by the care, creativity, and precision of their craftsmanship. The owners' diverse expertise—including professional welding and specialized use of corten steel—allows them to deliver innovative, personalized solutions that beautifully reflect client preferences and harmonize with the Norwegian climate.



## Team



The company consists of two owners who are young and wise, and they take actual **pride** in their work. They make it a point to themselves to **invest** in every customer they meet. Both have multiple disciplines, including one being a professional welder, making corten-steel something they intertwine in their work.



They are creative and flexible while covering a wide and flexible skillset. Whether it's crafting welcoming front yards or designing sophisticated commercial landscapes, the team ensures each solution reflects the client's preferences while honoring the Norwegian climate. They are known for shaping outdoor spaces that stand out in the local environment.



## Services



The company focuses on eight core services:



1. **Kantstein** (curbstones)

2. **Ferdigplen** (ready lawn)

3. **Støttemur** (retaining walls)

4. **Hekk/beplantning** (hedges and planting)

5. **Cortenstål** (steel installations)

6. **Belegningsstein** (paving stones)

7. **Platting** (decking)

8. **Trapp/repo** (stairs and landings)



Every project showcases meticulous attention to detail—from selecting high-quality materials to matching each space with the best design strategies. By emphasizing local conditions and seasonal changes, Ringerike Landskap consistently delivers outdoor solutions that stand the test of time.



## Customer Approach



Clients consistently appreciate the genuine personal connection and meticulous attention provided by Ringerike Landskap. Each project begins with transparent, thoughtful dialogue where ideas, preferences, and creative possibilities are openly explored. The owners' hands-on involvement and sincere investment in every customer ensure that projects not only meet but exceed expectations, creating lasting satisfaction and often delightful surprises.



Their versatile portfolio, ranging from intimate backyard enhancements to expansive commercial landscapes, demonstrates a proven flexibility and deep commitment to quality at any scale. Customers within a 20–50 km radius can feel confident, from initial consultation through final implementation, knowing their outdoor spaces will uniquely reflect their personal vision and practical requirements.



## Digital Presence



Ringerike Landskap AS is a forward-thinking company, actively leveraging their digital presence within a market where few landscaping companies in Norway have fully embraced technology. Recognizing this opportunity, they are currently finalizing an engaging and modern website aimed at expanding their customer reach and clearly demonstrating their unique expertise.



Ringerike Landskap's new website will serve as more than just a digital portfolio—it will be an authentic representation of how two dedicated owners personally invest in delivering exceptional landscaping solutions throughout the Ringerike region. By emphasizing their customer-centered approach, creative versatility, and consistent ability to exceed expectations, the site will effectively communicate why local residents and businesses continue to entrust their outdoor spaces to this distinctive landscaping company. The website's strategic focus on the owners' personal involvement and commitment to customer satisfaction positions Ringerike Landskap as the natural choice for anyone seeking truly personalized landscaping expertise in Hole, Hønefoss, Jevnaker, Sundvollen, and Vik.



## Website Purpose



The new company website is designed to significantly expand their reach within the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik, and surrounding areas) by *leveraging* advanced and unique search engine optimization (SEO) techniques. More than just an online presence, the site will function as a dynamic platform, strategically built to connect with homeowners, businesses, and property developers actively searching for local landscaping services.



The website's ultimate purpose is to solidify Ringerike Landskap's position as *the* leading landscaping experts in the Ringerike region. It achieves this not only by presenting their portfolio but also by actively engaging their target audience through a customer-centric online experience and a powerful, locally-focused SEO strategy.

```





#### `02-codebase-map\01-directory-structure.md`



```markdown

# Codebase Directory Structure



This document provides a comprehensive map of the Ringerike Landskap website codebase organization, focusing on the actual structure rather than an idealized version.



## Root Directory Structure



```

project/

├── .ai-analysis/       # AI-specific screenshots and analysis data

├── content/            # Structured content organization

│   ├── projects/

│   ├── services/

│   ├── team/

│   └── testimonials/

├── docs/               # Project documentation

├── node_modules/       # Dependencies

├── public/             # Static assets

│   └── images/         # Image assets organized by category

├── screenshots/        # Screenshot captures for visual reference

│   ├── latest/         # Always contains most recent screenshots

│   └── [timestamped]/  # Historical screenshot directories

├── scripts/            # Automation scripts

│   ├── screenshot-manager.js

│   ├── dev-with-snapshots.js

│   └── auto-snapshot.js

├── src/                # Source code

└── various config files (package.json, tsconfig.json, etc.)

```



## Source Directory Structure



```

src/

├── app/                # Core application setup

├── components/         # Reusable UI components

│   ├── layout/         # Layout components

│   │   ├── Header.tsx

│   │   └── Footer.tsx

│   └── ui/             # UI components

│       ├── Button.tsx

│       ├── Card.tsx

│       └── etc.

├── contexts/           # React context providers

├── data/               # Mock data and content

│   ├── services.ts

│   ├── projects.ts

│   ├── testimonials.ts

│   ├── team.ts

│   └── navigation.ts

├── features/           # Feature-specific components

│   ├── home/

│   ├── services/

│   └── projects/

├── hooks/              # Custom React hooks

├── lib/                # Third-party integrations

├── pages/              # Route components

│   ├── home/

│   │   └── index.tsx

│   ├── about/

│   │   └── index.tsx

│   ├── services/

│   │   ├── index.tsx

│   │   └── detail.tsx

│   ├── projects/

│   │   ├── index.tsx

│   │   └── detail.tsx

│   └── contact/

│       └── index.tsx

├── styles/             # Global styles

├── types/              # TypeScript type definitions

│   ├── services.ts

│   ├── projects.ts

│   └── common.ts

├── utils/              # Utility functions

├── App.tsx             # Main application component

├── main.tsx            # Application entry point

└── index.css           # Global CSS

```



## Key Relations Between Directories



### Content Flow



```

content/ → src/data/ → components/ → pages/

```



The content directory provides structured content which is imported into data files, then consumed by components and finally rendered in pages.



### Visual Documentation System



```

src/ → scripts/ → screenshots/ and .ai-analysis/

```



The source code is captured through screenshot scripts, which store visual output in both screenshots and .ai-analysis directories.



## Critical Directories



These directories contain essential functionality that must be handled carefully:



1. **scripts/**: Contains screenshot and development automation

2. **src/data/**: Contains all content definitions

3. **src/pages/**: Contains the routing structure

4. **.ai-analysis/**: Contains AI-specific captures and metadata



## Dependency Management



The project uses npm with these key dependencies:



- React ecosystem: react, react-dom, react-router-dom

- UI utilities: clsx, lucide-react, tailwind-merge

- Development tools: vite, typescript, eslint

- Build tools: postcss, autoprefixer, tailwindcss



## Configuration Files



- **vite.config.ts**: Configures the Vite build tool

- **tsconfig.json**: Main TypeScript configuration

- **tsconfig.app.json**: App-specific TypeScript configuration

- **tsconfig.node.json**: Node-specific TypeScript configuration

- **tailwind.config.js**: Tailwind CSS configuration

- **postcss.config.js**: PostCSS configuration

- **eslint.config.js**: ESLint configuration



## Notes on Actual Usage



The structure described above represents the actual organization, with some observations:



1. Not all directories are fully populated (some are placeholders)

2. Some components don't follow the ideal organization pattern

3. The boundaries between features/ and components/ are sometimes blurred

4. Context usage is inconsistent across the application



This map serves as a guide for navigating the codebase while acknowledging its current organizational state. ```





#### `02-codebase-map\02-architecture-overview.md`



```markdown

# Ringerike Landskap Website Architecture



This document outlines the technical architecture of the Ringerike Landskap website, providing a comprehensive overview of the codebase structure, component organization, and implementation details.



## Technology Stack



The website is built using the following technologies:



-   **React**: Frontend library for building user interfaces (v18.3)

-   **TypeScript**: Typed superset of JavaScript for improved developer experience

-   **Vite**: Build tool and development server

-   **Tailwind CSS**: Utility-first CSS framework for styling

-   **React Router**: Library for routing and navigation (v6.22)

-   **React Helmet**: Library for managing document head

-   **Lucide React**: Icon library



## Project Structure



The project follows a modular structure organized by feature and responsibility:



```

src/

├── components/       # Reusable UI components

│   ├── layout/       # Layout components (Header, Footer)

│   └── ui/           # Basic UI components (Button, Card, etc.)

├── features/         # Feature-specific components

│   ├── home/         # Home page specific components

│   ├── services/     # Services page specific components

│   └── projects/     # Projects page specific components

├── hooks/            # Custom React hooks

├── pages/            # Page components

│   ├── home/

│   ├── about/

│   ├── services/

│   ├── projects/

│   └── contact/

├── types/            # TypeScript type definitions

├── utils/            # Utility functions

└── App.tsx           # Main application component

```



## Component Architecture



The website follows a component-based architecture with the following principles:



1. **Atomic Design**: Components are organized from small, atomic components to larger, composite components

2. **Composition Over Inheritance**: Components are composed together rather than extended

3. **Single Responsibility**: Each component has a single, well-defined responsibility

4. **Reusability**: Components are designed to be reusable across the application



### Core Components



#### Layout Components



-   **Header**: Main navigation component with responsive design

-   **Footer**: Site footer with contact information and links

-   **Container**: Wrapper component for consistent horizontal padding

-   **Layout**: Main layout component that includes Header and Footer



#### UI Components



-   **Button**: Reusable button component with variants

-   **Card**: Card component for displaying content with consistent styling

-   **Hero**: Hero section component for page headers

-   **Input**: Form input component with validation support



## Routing



The website uses React Router for navigation with the following route structure:



```jsx

<Routes>

    <Route path="/" element={<HomePage />} />

    <Route path="/hvem-er-vi" element={<AboutPage />} />

    <Route path="/hva-vi-gjor" element={<ServicesPage />} />

    <Route path="/prosjekter" element={<ProjectsPage />} />

    <Route path="/kontakt" element={<ContactPage />} />

</Routes>

```



## State Management



The website uses React's built-in state management with hooks:



-   **useState**: For component-level state

-   **useContext**: For sharing state between components

-   **useReducer**: For more complex state logic



## Data Fetching



Data fetching is handled using custom hooks that encapsulate the fetching logic:



```tsx

function useProjects() {

    const [projects, setProjects] = useState<Project[]>([]);

    const [loading, setLoading] = useState(true);

    const [error, setError] = useState<Error | null>(null);



    useEffect(() => {

        async function fetchProjects() {

            try {

                setLoading(true);

                // Fetch projects from API or mock data

                const data = await fetchProjectsData();

                setProjects(data);

            } catch (err) {

                setError(err as Error);

            } finally {

                setLoading(false);

            }

        }



        fetchProjects();

    }, []);



    return { projects, loading, error };

}

```



## Styling



The website uses Tailwind CSS for styling with a custom configuration:



```js

// tailwind.config.js

module.exports = {

    content: ["./src/**/*.{js,jsx,ts,tsx}", "./index.html"],

    theme: {

        extend: {

            colors: {

                primary: "#1e9545",

                "primary-dark": "#0d6b30",

                "primary-light": "#e6f4ea",

            },

            fontFamily: {

                sans: ["Inter", "sans-serif"],

            },

        },

    },

    plugins: [],

};

```



## Responsive Design



The website is designed to be responsive with the following breakpoints:



-   **Mobile**: < 768px

-   **Tablet**: 768px - 1023px

-   **Desktop**: ≥ 1024px



Responsive design is implemented using Tailwind's responsive utilities:



```jsx

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

    {/* Grid items */}

</div>

```



## Performance Optimization



The website implements several performance optimizations:



1. **Code Splitting**: Using React.lazy and Suspense for route-based code splitting

2. **Image Optimization**: Using responsive images and WebP format

3. **Lazy Loading**: Implementing lazy loading for below-the-fold content

4. **Memoization**: Using React.memo and useMemo for expensive computations



## Accessibility



The website follows accessibility best practices:



1. **Semantic HTML**: Using appropriate HTML elements

2. **ARIA Attributes**: Adding ARIA attributes for interactive elements

3. **Keyboard Navigation**: Ensuring keyboard accessibility

4. **Color Contrast**: Maintaining sufficient color contrast

5. **Focus Management**: Implementing proper focus states



## Deployment



The website is built using Vite and deployed as a static site:



```bash

# Build for production

npm run build



# Preview the production build

npm run preview

```



## Future Considerations



1. **State Management**: Consider implementing Redux or Context API for more complex state management

2. **API Integration**: Implement a proper API client for data fetching

3. **Testing**: Add unit and integration tests

4. **Internationalization**: Add support for multiple languages

5. **Analytics**: Implement analytics tracking



## Route Structure



The website implements a carefully planned route structure that reflects the organizational needs and user journeys:



### Main Navigation Routes



- **Home** (/) 

  - Hero section

  - Seasonal projects carousel

  - Service areas list

  - Seasonal services section

  - Testimonials section



- **Services** (/hva-vi-gjor)  

  - Hero section

  - Service filtering (Category, Function, Season)

  - Service listings with details

  - Benefits section (Lokalkunnskap, Tilpassede løsninger, Klimatilpasset)

  - CTA section



- **Projects** (/prosjekter)

  - Hero section

  - Project filtering (Category, Location, Tag, Season)

  - Project grid with details

  - Seasonal recommendations



- **About Us** (/hvem-er-vi)

  - Hero section

  - Company information

  - Team members

  - Core values and benefits



- **Contact** (/kontakt)

  - Hero section

  - Contact form

  - Location information

  - Service areas



### Dynamic Routes



- **Service Detail** (/tjenester/:id)

  - Service description

  - Features list

  - Related projects

  - Image gallery

  - Contact CTA



- **Project Detail** (/prosjekter/:id)

  - Project description

  - Project specifications

  - Materials and features

  - Related service

  - Testimonial (if available)



### Additional Pages



- **Testimonials** (/kundehistorier)

  - Testimonial filtering

  - Testimonial grid

  - Testimonial categories

  - CTA section



## SEO Strategy



The website employs a comprehensive SEO strategy to enhance visibility and reach in the Ringerike region:



### Target Groups



- **Primary**: Homeowners, businesses, property developers, and local residents in Ringerike and surrounding areas seeking professional landscaping services

- **Primary**: Prospective customers looking for tailored landscaping solutions or inspiration for outdoor spaces

- **Secondary**: Users researching local landscaping solutions online or comparing contractors

- **Secondary**: Existing customers revisiting the site for additional services or to share testimonials

- **Secondary**: Existing customers reviewing projects or contacting the company



### Core SEO Strategies



- **Hyperlocal SEO**: The website is meticulously optimized for location-specific keywords, including variations and long-tail phrases (e.g., "anleggsgartner Røyse" "pris på støttemur Jevnaker," "cortenstål i hage Ringerike"). This ensures high visibility in search results when potential customers within their service area are seeking relevant services.



- **Technical SEO Foundation**: The website's architecture is built for speed, mobile-friendliness, and optimal crawlability by search engines. Optimized images, clean code, structured data markup (Schema.org), and a clear sitemap ensure the site's content is easily discoverable.



- **Optimized UI/UX**: User-friendly navigation and detailed service breakdowns—covering everything from welcoming front yards to sophisticated commercial landscapes—to inspiring project galleries categorized by season and location, the site is strategically structured to enhance engagement.



- **Showcasing Professionalism**: Transparent communication, a genuine commitment to customer satisfaction, and thoughtful seasonal insights, the platform guides visitors effortlessly from inspiration to consultation, positioning Ringerike Landskap as the trusted local authority in outdoor design and landscape craftsmanship.



- **Authenticity**: The website authentically portrays the two dedicated owners, their personal investment in each project, and their commitment to exceeding client expectations. This "people-first" approach, reinforced through testimonials, case studies, and behind-the-scenes content, builds trust and resonates with local customers, differentiating Ringerike Landskap from larger competitors.



### SEO Technical Implementation



- **Meta Tags**: Dynamic meta tags for each page using React Helmet

- **Structured Data**: Schema.org markup for rich results

- **Semantic HTML**: Proper heading structure

- **Canonical URLs**: Proper URL structure

- **Performance**: Optimized loading times and Core Web Vitals metrics

- **Mobile Optimization**: Fully responsive design with mobile-friendly features

```





#### `02-codebase-map\03-component-hierarchy.md`



```markdown

# Ringerike Landskap Website Component Tree



```

App

├── Router

│   ├── Header

│   │   ├── Logo

│   │   ├── Navigation (Desktop)

│   │   └── MobileMenuToggle

│   │

│   ├── main

│   │   └── Routes

│   │       ├── HomePage (path="/")

│   │       │   ├── Hero

│   │       │   │   ├── Title

│   │       │   │   ├── Subtitle

│   │       │   │   ├── LocationBadge

│   │       │   │   └── CTAButton

│   │       │   │

│   │       │   ├── Container

│   │       │   │   ├── Grid (Projects & Service Areas)

│   │       │   │   │   ├── SeasonalProjectsCarousel

│   │       │   │   │   │   ├── ProjectCard(s)

│   │       │   │   │   │   ├── NavigationArrows

│   │       │   │   │   │   └── PaginationIndicators

│   │       │   │   │   │

│   │       │   │   │   └── ServiceAreaList

│   │       │   │   │       └── ServiceAreaItem(s)

│   │       │   │   │

│   │       │   │   └── Grid (Services & CTA)

│   │       │   │       ├── FilteredServicesSection

│   │       │   │       │   └── ServiceCard(s)

│   │       │   │       │

│   │       │   │       └── SeasonalCTA

│   │       │   │

│   │       │   └── TestimonialsSection

│   │       │       └── TestimonialCard(s)

│   │       │

│   │       ├── AboutPage (path="/hvem-er-vi")

│   │       ├── ServicesPage (path="/hva-vi-gjor")

│   │       ├── ServiceDetailPage (path="/tjenester/:id")

│   │       ├── ProjectsPage (path="/prosjekter")

│   │       ├── ProjectDetailPage (path="/prosjekter/:id")

│   │       ├── ContactPage (path="/kontakt")

│   │       └── TestimonialsPage (path="/kundehistorier")

│   │

│   └── Footer

│       ├── CompanyInfo

│       │   └── SocialLinks

│       ├── ContactInfo

│       │   ├── Address

│       │   ├── Phone

│       │   └── Email

│       ├── OpeningHours

│       ├── QuickLinks

│       └── PrivacyLinks

│

└── ReactHelmet (SEO metadata)

```



## Data Flow



```

┌─────────────────┐

│                 │

│  Mock API Data  │◄───────┐

│                 │        │

└────────┬────────┘        │

         │                 │

         ▼                 │

┌─────────────────┐        │

│                 │        │

│  Custom Hooks   │        │

│  (useData)      │        │

│                 │        │

└────────┬────────┘        │

         │                 │

         ▼                 │

┌─────────────────┐        │

│                 │        │

│  Page Component │        │

│                 │        │

└────────┬────────┘        │

         │                 │

         ▼                 │

┌─────────────────┐        │

│                 │        │

│  UI Components  │────────┘

│                 │

└─────────────────┘

```



## Rendered UI Structure (Homepage)



```

┌─────────────────────────────────────────────────────────────┐

│ Header                                                      │

├─────────────────────────────────────────────────────────────┤

│                                                             │

│ ┌─────────────────────────────────────────────────────────┐ │

│ │ Hero Section                                            │ │

│ │ - Title, Subtitle, CTA Button                           │ │

│ └─────────────────────────────────────────────────────────┘ │

│                                                             │

│ ┌───────────────────────────┐ ┌───────────────────────────┐ │

│ │                           │ │                           │ │

│ │ Seasonal Projects         │ │ Service Areas             │ │

│ │ Carousel                  │ │                           │ │

│ │                           │ │                           │ │

│ └───────────────────────────┘ └───────────────────────────┘ │

│                                                             │

│ ┌───────────────────────────┐ ┌───────────────────────────┐ │

│ │                           │ │                           │ │

│ │ Filtered Services         │ │ Seasonal CTA              │ │

│ │                           │ │                           │ │

│ │                           │ │                           │ │

│ └───────────────────────────┘ └───────────────────────────┘ │

│                                                             │

│ ┌─────────────────────────────────────────────────────────┐ │

│ │ Testimonials Section                                    │ │

│ │                                                         │ │

│ └─────────────────────────────────────────────────────────┘ │

│                                                             │

├─────────────────────────────────────────────────────────────┤

│ Footer                                                      │

└─────────────────────────────────────────────────────────────┘

```



## Responsive Layouts



### Desktop (>1024px)



```

┌─────────────────────────────────────────────────────────────┐

│ Header (Full Navigation)                                    │

├─────────────────────────────────────────────────────────────┤

│ Hero                                                        │

├───────────────────────────┬───────────────────────────────┬─┤

│ Projects (2/3)            │ Service Areas (1/3)           │ │

├───────────────────────────┼───────────────────────────────┼─┤

│ Services (2/3)            │ CTA (1/3)                     │ │

├─────────────────────────────────────────────────────────────┤

│ Testimonials                                               │

├─────────────────────────────────────────────────────────────┤

│ Footer (3 columns)                                         │

└─────────────────────────────────────────────────────────────┘

```



### Tablet (768px-1024px)



```

┌─────────────────────────────────────────────────────────────┐

│ Header (Full Navigation)                                    │

├─────────────────────────────────────────────────────────────┤

│ Hero                                                        │

├───────────────────────────┬───────────────────────────────┬─┤

│ Projects (2/3)            │ Service Areas (1/3)           │ │

├───────────────────────────┼───────────────────────────────┼─┤

│ Services (2/3)            │ CTA (1/3)                     │ │

├─────────────────────────────────────────────────────────────┤

│ Testimonials                                               │

├─────────────────────────────────────────────────────────────┤

│ Footer (2-3 columns)                                       │

└─────────────────────────────────────────────────────────────┘

```



### Mobile (<768px)



```

┌─────────────────────────────────────────────────────────────┐

│ Header (Hamburger Menu)                                     │

├─────────────────────────────────────────────────────────────┤

│ Hero                                                        │

├─────────────────────────────────────────────────────────────┤

│ Projects                                                    │

├─────────────────────────────────────────────────────────────┤

│ Service Areas                                               │

├─────────────────────────────────────────────────────────────┤

│ Services                                                    │

├─────────────────────────────────────────────────────────────┤

│ CTA                                                         │

├─────────────────────────────────────────────────────────────┤

│ Testimonials                                                │

├─────────────────────────────────────────────────────────────┤

│ Footer (Single column)                                      │

└─────────────────────────────────────────────────────────────┘

```

```





#### `03-implementation-patterns\01-current-patterns.md`



```markdown

# Current Implementation Patterns



This document outlines the current implementation patterns found in the Ringerike Landskap website codebase. Understanding these patterns is essential for maintaining consistency during development and planning improvements.



## Component Patterns



The codebase currently uses several different component implementation patterns:



### Functional Components with Props



Most components use the functional component pattern with explicit props:



```tsx

interface ButtonProps {

  children: React.ReactNode;

  variant?: 'primary' | 'secondary' | 'outline';

  onClick?: () => void;

}



function Button({ children, variant = 'primary', onClick }: ButtonProps) {

  return (

    <button 

      className={`btn ${variant === 'primary' ? 'btn-primary' : variant === 'secondary' ? 'btn-secondary' : 'btn-outline'}`}

      onClick={onClick}

    >

      {children}

    </button>

  );

}



export default Button;

```



### Context Usage



Some components use React Context for state management:



```tsx

// Context definition

export const ServiceContext = createContext<{

  services: ServiceType[];

  loading: boolean;

} | undefined>(undefined);



// Provider component

export function ServiceProvider({ children }: { children: React.ReactNode }) {

  const [services] = useState<ServiceType[]>(servicesData);

  const [loading] = useState(false);

  

  return (

    <ServiceContext.Provider value={{ services, loading }}>

      {children}

    </ServiceContext.Provider>

  );

}



// Usage in components

function ServiceList() {

  const context = useContext(ServiceContext);

  

  if (!context) {

    throw new Error('ServiceList must be used within ServiceProvider');

  }

  

  const { services, loading } = context;

  

  if (loading) return <div>Loading...</div>;

  

  return (

    <div>

      {services.map(service => (

        <ServiceCard key={service.id} service={service} />

      ))}

    </div>

  );

}

```



### Prop Drilling



Deeply nested components often use prop drilling:



```tsx

function ProjectsPage() {

  const [filter, setFilter] = useState('all');

  

  return (

    <div>

      <ProjectFilter filter={filter} setFilter={setFilter} />

      <ProjectList filter={filter} />

    </div>

  );

}



function ProjectList({ filter }: { filter: string }) {

  return (

    <div>

      {projects

        .filter(project => filter === 'all' || project.category === filter)

        .map(project => (

          <ProjectItem key={project.id} project={project} />

        ))}

    </div>

  );

}

```



## Styling Patterns



### Tailwind Classes



The primary styling approach uses Tailwind CSS utility classes:



```tsx

function Card({ title, content }) {

  return (

    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">

      <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>

      <p className="text-gray-600">{content}</p>

    </div>

  );

}

```



### Conditional Classes



Conditional styling is implemented using template literals or conditional expressions:



```tsx

function Alert({ type, message }) {

  return (

    <div className={`p-4 rounded ${

      type === 'error' ? 'bg-red-100 text-red-800' :

      type === 'warning' ? 'bg-yellow-100 text-yellow-800' :

      'bg-green-100 text-green-800'

    }`}>

      {message}

    </div>

  );

}

```



## Data Management Patterns



### Static Data Files



Data is primarily managed through static TypeScript files:



```tsx

// src/data/services.ts

import { ServiceType } from '@/types';



export const services: ServiceType[] = [

  {

    id: "belegningsstein",

    title: "Belegningsstein",

    description: "Profesjonell legging av belegningsstein for innkjÃ¸rsler og gangveier.",

    image: "/images/categorized/belegg/IMG_3037.webp",

    features: [

      "Solid grunnarbeid",

      "Presis legging",

      "Flere designmuligheter"

    ]

  },

  // More services...

];

```



### Helper Functions



Helper functions for data filtering and transformation:



```tsx

// Helper function to get a service by ID

export function getServiceById(id: string): ServiceType | undefined {

  return services.find(service => service.id === id);

}



// Helper function to filter services by season

export function getSeasonalServices(): ServiceType[] {

  const currentMonth = new Date().getMonth();

  const isSummer = currentMonth >= 4 && currentMonth <= 8;

  

  return services.filter(service => 

    isSummer ? !service.winterOnly : !service.summerOnly

  );

}

```



## Routing Pattern



The routing structure uses React Router with nested routes:



```tsx

function App() {

  return (

    <Router>

      <div className="flex flex-col min-h-screen">

        <Header />

        <main className="flex-grow">

          <Routes>

            <Route path="/" element={<HomePage />} />

            <Route path="/hvem-er-vi" element={<AboutPage />} />

            <Route path="/hva-vi-gjor" element={<ServicesPage />} />

            <Route path="/tjenester/:id" element={<ServiceDetailPage />} />

            <Route path="/prosjekter" element={<ProjectsPage />} />

            <Route path="/prosjekter/:id" element={<ProjectDetailPage />} />

            <Route path="/kontakt" element={<ContactPage />} />

          </Routes>

        </main>

        <Footer />

      </div>

    </Router>

  );

}

```



## State Management Patterns



The codebase uses several approaches to state management:



1. **Component Local State**: Using `useState` for component-specific state

2. **React Context**: For sharing state between related components

3. **URL Parameters**: For page-specific state reflected in the URL

4. **Form State**: Managed within form components



No central state management library is currently in use.



## TypeScript Usage



TypeScript is used throughout the codebase with varying levels of type safety:



### Interface Definitions



```tsx

// src/types/services.ts

export interface ServiceType {

  id: string;

  title: string;

  description: string;

  image: string;

  features: string[];

  summerOnly?: boolean;

  winterOnly?: boolean;

}

```



### Type Assertions



```tsx

function getProjectImage(project: any) {

  // Using type assertion

  return (project as ProjectType).image || '/images/placeholder.webp';

}

```



### Generic Types



```tsx

function useFetch<T>(url: string) {

  const [data, setData] = useState<T | null>(null);

  const [loading, setLoading] = useState(true);

  const [error, setError] = useState<Error | null>(null);

  

  // Implementation...

  

  return { data, loading, error };

}

```



## Areas for Pattern Improvement



Based on the current implementation patterns, several areas could benefit from standardization:



1. **Component Structure**: Standardize on functional components with explicit prop interfaces

2. **State Management**: Adopt a more consistent approach (either Context API or a state management library)

3. **TypeScript Usage**: Improve type safety by reducing `any` usage and adding proper generics

4. **Styling Approach**: Standardize conditional styling with utility functions

5. **Data Fetching**: Implement consistent data fetching patterns



These improvements would enhance code quality while maintaining the existing functionality. ```





#### `03-implementation-patterns\02-component-patterns.md`



```markdown

# Component-Specific Review



This document provides a detailed analysis of key components in the Ringerike Landskap website, examining their implementation, strengths, and areas for improvement.



## Core Layout Components



### Header (`src/components/layout/Header.tsx`)



The Header component provides the main navigation for the website, with both desktop and mobile versions.



**Implementation Details:**



-   Uses React hooks (`useState`, `useEffect`) for state management

-   Implements responsive design with different layouts for mobile and desktop

-   Includes a sticky positioning with background opacity changes on scroll

-   Uses the Lucide React library for icons



**Strengths:**



-   Clean separation of mobile and desktop navigation

-   Effective use of React hooks for state management

-   Good responsive behavior with appropriate breakpoints

-   Active state clearly indicates current page



**Improvement Opportunities:**



-   The mobile menu transition could be smoother with CSS transitions

-   Consider using `useCallback` for event handlers

-   The logo sizing could be more responsive on smaller screens

-   Add ARIA attributes for better accessibility



### Footer (`src/components/layout/Footer.tsx`)



The Footer component provides contact information, opening hours, and navigation links.



**Implementation Details:**



-   Organized into multiple sections (company info, contact, opening hours)

-   Uses Lucide React icons for visual elements

-   Implements responsive grid layout

-   Dynamically calculates the current year for copyright



**Strengths:**



-   Comprehensive information architecture

-   Good use of icons to enhance readability

-   Responsive layout adapts well to different screen sizes

-   Clear organization of content sections



**Improvement Opportunities:**



-   Consider extracting sub-components for better maintainability

-   Add more structured microdata for SEO

-   Enhance social media integration

-   Implement a newsletter signup form



### Hero (`src/components/ui/Hero.tsx`)



The Hero component creates a full-width banner with background image, text, and call-to-action.



**Implementation Details:**



-   Configurable through props (height, overlay, text alignment)

-   Uses multiple background layers for visual effect

-   Implements responsive typography

-   Includes a location badge and CTA button



**Strengths:**



-   Highly customizable through props

-   Effective use of overlay gradients for text readability

-   Responsive design with appropriate text sizing

-   Good visual hierarchy with clear focal points



**Improvement Opportunities:**



-   The multiple overlay layers could be simplified for better performance

-   Consider using CSS variables for more consistent styling

-   Add animation options for more dynamic presentations

-   Implement image preloading for faster rendering



## UI Components



### ServiceCard (`src/components/ServiceCard.tsx`)



The ServiceCard component displays a service with image, title, description, and action link.



**Implementation Details:**



-   Uses background image with overlay gradient

-   Implements hover effects with scale transformation

-   Includes title, description, and action button

-   Uses React Router's Link component for navigation



**Strengths:**



-   Attractive design with good use of imagery

-   Effective hover interaction enhances user experience

-   Good text contrast against the gradient overlay

-   Clean, focused presentation of service information



**Improvement Opportunities:**



-   Add image loading optimization

-   Implement consistent aspect ratios

-   Consider adding truncation for long descriptions

-   Enhance the action button's visual prominence



### ContactForm (`src/components/ContactForm.tsx`)



The ContactForm component provides a form for users to contact the company.



**Implementation Details:**



-   Includes various input fields (name, email, phone, etc.)

-   Uses grid layout for responsive field arrangement

-   Implements focus states for form fields

-   Includes a submit button with hover effect



**Strengths:**



-   Clean layout with appropriate spacing

-   Responsive design with field reordering on smaller screens

-   Good focus states for improved usability

-   Logical grouping of related fields



**Improvement Opportunities:**



-   Add form validation with error messages

-   Implement form submission handling

-   Add loading state for the submit button

-   Include a privacy policy consent checkbox



## Feature Components



### SeasonalProjectsCarousel (`src/features/home/<USER>



This component displays a carousel of seasonal projects on the home page.



**Implementation Details:**



-   Fetches project data using a custom hook

-   Implements carousel navigation with next/previous buttons

-   Displays project cards with images and information

-   Includes loading state for data fetching



**Strengths:**



-   Dynamic content based on seasonal relevance

-   Good loading state implementation

-   Effective navigation controls

-   Responsive design adapts to different screen sizes



**Improvement Opportunities:**



-   Add keyboard navigation support

-   Implement touch swipe gestures for mobile

-   Add pagination indicators

-   Optimize image loading with lazy loading



### FilteredServicesSection (`src/features/home/<USER>



This component displays services filtered by seasonal relevance.



**Implementation Details:**



-   Fetches service data using a custom hook

-   Filters services based on seasonal relevance

-   Displays service cards in a responsive grid

-   Includes loading state for data fetching



**Strengths:**



-   Dynamic content filtering based on current season

-   Clean grid layout with appropriate spacing

-   Good loading state implementation

-   Responsive design with grid adjustments



**Improvement Opportunities:**



-   Add category filtering options

-   Implement animation for smoother transitions

-   Consider adding a "View All" option

-   Optimize image loading performance



## Page Components



### HomePage (`src/pages/home/<USER>



The HomePage component serves as the landing page for the website.



**Implementation Details:**



-   Combines multiple components (Hero, SeasonalProjectsCarousel, etc.)

-   Uses custom hooks for data fetching

-   Implements responsive grid layouts

-   Includes loading states for async content



**Strengths:**



-   Good component composition

-   Effective use of custom hooks for data fetching

-   Clean, organized structure

-   Appropriate loading states for async content



**Improvement Opportunities:**



-   Consider implementing code splitting for performance

-   Add more structured microdata for SEO

-   Implement scroll restoration for navigation

-   Add animation for page transitions



## Utility Components



### Container (`src/components/ui/Container.tsx`)



The Container component provides consistent horizontal padding and maximum width.



**Implementation Details:**



-   Accepts className prop for customization

-   Implements responsive padding

-   Sets maximum width with centered alignment

-   Uses React's children prop for content



**Strengths:**



-   Simple, reusable implementation

-   Consistent spacing across the site

-   Responsive padding adjustments

-   Easy to customize through className prop



**Improvement Opportunities:**



-   Add more size variants (narrow, wide, etc.)

-   Consider implementing vertical padding options

-   Add max-width customization through props

-   Implement background color options



## Recommendations for Component Improvements



1. **Implement Lazy Loading**



    - Add lazy loading to image components

    - Use React.lazy for code splitting

    - Implement intersection observer for below-fold content



2. **Enhance Accessibility**



    - Add ARIA attributes to interactive components

    - Implement keyboard navigation support

    - Add skip links for keyboard users

    - Ensure sufficient color contrast



3. **Optimize Performance**



    - Memoize expensive computations with useMemo

    - Use useCallback for event handlers

    - Implement virtualization for long lists

    - Optimize image loading and rendering



4. **Improve Component Architecture**



    - Extract reusable sub-components

    - Implement compound components where appropriate

    - Use context for deeply nested component trees

    - Add better TypeScript typing for props



5. **Enhance User Experience**

    - Add meaningful animations and transitions

    - Implement better loading states

    - Add error handling and recovery

    - Enhance form validation and feedback

```





#### `03-implementation-patterns\03-target-patterns.md`



```markdown

# Target Implementation Patterns



This document outlines the recommended implementation patterns for the Ringerike Landskap website codebase. These patterns should be followed for all new code and when refactoring existing code.



## Component Patterns



### Functional Components with TypeScript



All components should use functional components with explicit TypeScript interfaces for props:



```tsx

interface ButtonProps {

  /** The content to be rendered inside the button */

  children: React.ReactNode;

  /** Button variant style */

  variant?: 'primary' | 'secondary' | 'outline';

  /** Function called when button is clicked */

  onClick?: () => void;

  /** Whether the button is disabled */

  isDisabled?: boolean;

  /** Additional CSS classes */

  className?: string;

}



/**

 * Button component for user interactions

 */

function Button({

  children,

  variant = 'primary',

  onClick,

  isDisabled = false,

  className = '',

}: ButtonProps) {

  const baseClasses = 'px-4 py-2 rounded transition-colors';

  

  const variantClasses = {

    primary: 'bg-primary text-white hover:bg-primary-dark',

    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300',

    outline: 'border border-primary text-primary hover:bg-primary-light'

  };

  

  return (

    <button 

      className={`${baseClasses} ${variantClasses[variant]} ${className}`}

      onClick={onClick}

      disabled={isDisabled}

    >

      {children}

    </button>

  );

}



export default Button;

```



### Component Organization



Components should follow this organizational pattern:



1. Import statements

2. Type definitions

3. Constants and helper functions

4. Component definition

5. Export statement



Example:



```tsx

import { useState, useEffect } from 'react';

import { formatDate } from '@/utils/date';

import { ProjectType } from '@/types';



interface ProjectCardProps {

  project: ProjectType;

  isHighlighted?: boolean;

}



const MAX_DESCRIPTION_LENGTH = 150;



function truncateText(text: string, maxLength: number): string {

  if (text.length <= maxLength) return text;

  return text.substring(0, maxLength) + '...';

}



function ProjectCard({ project, isHighlighted = false }: ProjectCardProps) {

  const formattedDate = formatDate(project.completedDate);

  const truncatedDescription = truncateText(project.description, MAX_DESCRIPTION_LENGTH);

  

  return (

    <div className={`card ${isHighlighted ? 'bg-primary-light' : 'bg-white'}`}>

      <img src={project.image} alt={project.title} className="card-image" />

      <div className="card-content">

        <h3 className="card-title">{project.title}</h3>

        <p className="card-description">{truncatedDescription}</p>

        <span className="card-date">{formattedDate}</span>

      </div>

    </div>

  );

}



export default ProjectCard;

```



## State Management



### React Context for Shared State



Use React Context with custom hooks for shared state:



```tsx

// context.tsx

import { createContext, useContext, useState, ReactNode } from 'react';

import { ServiceType } from '@/types';

import { services as servicesData } from '@/data/services';



interface ServicesContextType {

  services: ServiceType[];

  filteredServices: ServiceType[];

  filterServices: (category: string) => void;

  isLoading: boolean;

}



const ServicesContext = createContext<ServicesContextType | undefined>(undefined);



export function ServicesProvider({ children }: { children: ReactNode }) {

  const [services] = useState<ServiceType[]>(servicesData);

  const [filteredServices, setFilteredServices] = useState<ServiceType[]>(servicesData);

  const [isLoading, setIsLoading] = useState(false);



  function filterServices(category: string) {

    setIsLoading(true);

    

    try {

      if (category === 'all') {

        setFilteredServices(services);

      } else {

        setFilteredServices(services.filter(service => service.category === category));

      }

    } finally {

      setIsLoading(false);

    }

  }



  return (

    <ServicesContext.Provider value={{ 

      services, 

      filteredServices, 

      filterServices, 

      isLoading 

    }}>

      {children}

    </ServicesContext.Provider>

  );

}



// Custom hook for using the context

export function useServices() {

  const context = useContext(ServicesContext);

  

  if (context === undefined) {

    throw new Error('useServices must be used within a ServicesProvider');

  }

  

  return context;

}

```



### Local Component State



For component-specific state, use the useState hook with proper typing:



```tsx

function SearchForm() {

  const [query, setQuery] = useState<string>('');

  const [results, setResults] = useState<SearchResult[]>([]);

  const [isSearching, setIsSearching] = useState<boolean>(false);

  const [error, setError] = useState<Error | null>(null);

  

  // Rest of the component

}

```



## Styling Patterns



### Tailwind with Composition



Use Tailwind CSS with composition utilities:



```tsx

import { clsx } from 'clsx';



interface CardProps {

  title: string;

  content: string;

  variant?: 'default' | 'highlight' | 'muted';

  className?: string;

}



function Card({ title, content, variant = 'default', className }: CardProps) {

  const baseClasses = 'rounded-lg p-6 shadow-sm';

  

  const variantClasses = {

    default: 'bg-white',

    highlight: 'bg-primary-light border border-primary',

    muted: 'bg-gray-50 text-gray-700'

  };

  

  return (

    <div className={clsx(

      baseClasses,

      variantClasses[variant],

      className

    )}>

      <h3 className="text-xl font-bold mb-2">{title}</h3>

      <p>{content}</p>

    </div>

  );

}

```



### Responsive Design



Always implement responsive design using Tailwind's responsive modifiers:



```tsx

function ProjectGrid({ projects }) {

  return (

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

      {projects.map(project => (

        <ProjectCard key={project.id} project={project} />

      ))}

    </div>

  );

}

```



## Data Management



### TypeScript Interfaces



Define comprehensive interfaces for all data structures:



```tsx

// types/project.ts

export interface ProjectType {

  id: string;

  title: string;

  description: string;

  image: string;

  category: string;

  location: string;

  completedDate: string;

  client?: string;

  features: string[];

  images?: string[];

  isFeatured?: boolean;

  seasonalRelevance?: ('summer' | 'winter' | 'spring' | 'autumn')[];

}

```



### Data Utilities



Create typed utility functions for data operations:



```tsx

// utils/projects.ts

import { ProjectType } from '@/types';

import { projects } from '@/data/projects';



/**

 * Returns a project by its ID

 */

export function getProjectById(id: string): ProjectType | undefined {

  return projects.find(project => project.id === id);

}



/**

 * Returns projects filtered by category

 */

export function getProjectsByCategory(category: string): ProjectType[] {

  if (category === 'all') return projects;

  return projects.filter(project => project.category === category);

}



/**

 * Returns featured projects

 */

export function getFeaturedProjects(limit?: number): ProjectType[] {

  const featured = projects.filter(project => project.isFeatured);

  return limit ? featured.slice(0, limit) : featured;

}



/**

 * Returns projects relevant for the current season

 */

export function getSeasonalProjects(): ProjectType[] {

  const currentMonth = new Date().getMonth();

  let currentSeason: 'winter' | 'spring' | 'summer' | 'autumn';

  

  if (currentMonth >= 2 && currentMonth <= 4) currentSeason = 'spring';

  else if (currentMonth >= 5 && currentMonth <= 7) currentSeason = 'summer';

  else if (currentMonth >= 8 && currentMonth <= 10) currentSeason = 'autumn';

  else currentSeason = 'winter';

  

  return projects.filter(project => 

    !project.seasonalRelevance || 

    project.seasonalRelevance.includes(currentSeason)

  );

}

```



## Error Handling



### Error Boundaries



Implement error boundaries for component error handling:



```tsx

import { Component, ErrorInfo, ReactNode } from 'react';



interface ErrorBoundaryProps {

  fallback?: ReactNode;

  children: ReactNode;

  onError?: (error: Error, errorInfo: ErrorInfo) => void;

}



interface ErrorBoundaryState {

  hasError: boolean;

  error: Error | null;

}



class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {

  constructor(props: ErrorBoundaryProps) {

    super(props);

    this.state = { hasError: false, error: null };

  }



  static getDerivedStateFromError(error: Error) {

    return { hasError: true, error };

  }



  componentDidCatch(error: Error, errorInfo: ErrorInfo) {

    if (this.props.onError) {

      this.props.onError(error, errorInfo);

    }

  }



  render() {

    if (this.state.hasError) {

      return this.props.fallback || (

        <div className="error-boundary p-4 bg-red-50 text-red-800 rounded border border-red-300">

          <h2>Something went wrong.</h2>

          <p>{this.state.error?.message || 'Unknown error'}</p>

        </div>

      );

    }



    return this.props.children;

  }

}



export default ErrorBoundary;

```



### Try-Catch for Async Operations



Always use try-catch for async operations:



```tsx

async function fetchData() {

  try {

    setLoading(true);

    setError(null);

    

    const response = await fetch('/api/data');

    

    if (!response.ok) {

      throw new Error(`HTTP error ${response.status}`);

    }

    

    const data = await response.json();

    setData(data);

  } catch (err) {

    setError(err instanceof Error ? err : new Error('Unknown error'));

  } finally {

    setLoading(false);

  }

}

```



## Performance Optimizations



### React.memo for Pure Components



Use React.memo for components that render often but with the same props:



```tsx

import { memo } from 'react';



interface PriceTagProps {

  amount: number;

  currency?: string;

}



function PriceTag({ amount, currency = 'NOK' }: PriceTagProps) {

  const formattedPrice = new Intl.NumberFormat('nb-NO', {

    style: 'currency',

    currency,

  }).format(amount);

  

  return <span className="price">{formattedPrice}</span>;

}



export default memo(PriceTag);

```



### useMemo for Expensive Calculations



Use useMemo for expensive calculations:



```tsx

import { useMemo } from 'react';



function ProjectStats({ projects }) {

  const statistics = useMemo(() => {

    return {

      total: projects.length,

      featured: projects.filter(p => p.isFeatured).length,

      byCategory: projects.reduce((acc, project) => {

        acc[project.category] = (acc[project.category] || 0) + 1;

        return acc;

      }, {})

    };

  }, [projects]);

  

  return (

    <div className="stats">

      <p>Total Projects: {statistics.total}</p>

      <p>Featured: {statistics.featured}</p>

      {/* Rest of the component */}

    </div>

  );

}

```



### Lazy Loading for Routes



Use React.lazy and Suspense for route-based code splitting:



```tsx

import { Suspense, lazy } from 'react';

import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

import Loading from './components/ui/Loading';



// Lazy-loaded components

const HomePage = lazy(() => import('./pages/home'));

const AboutPage = lazy(() => import('./pages/about'));

const ServicesPage = lazy(() => import('./pages/services'));

const ProjectsPage = lazy(() => import('./pages/projects'));

const ContactPage = lazy(() => import('./pages/contact'));



function App() {

  return (

    <Router>

      <Suspense fallback={<Loading />}>

        <Routes>

          <Route path="/" element={<HomePage />} />

          <Route path="/hvem-er-vi" element={<AboutPage />} />

          <Route path="/hva-vi-gjor" element={<ServicesPage />} />

          <Route path="/prosjekter" element={<ProjectsPage />} />

          <Route path="/kontakt" element={<ContactPage />} />

        </Routes>

      </Suspense>

    </Router>

  );

}

```



## Testing



### Component Tests



Write unit tests for components:



```tsx

// Button.test.tsx

import { render, screen, fireEvent } from '@testing-library/react';

import Button from './Button';



describe('Button component', () => {

  test('renders correctly with default props', () => {

    render(<Button>Click me</Button>);

    

    const button = screen.getByRole('button', { name: /click me/i });

    expect(button).toBeInTheDocument();

    expect(button).toHaveClass('bg-primary');

    expect(button).not.toBeDisabled();

  });

  

  test('renders with different variants', () => {

    const { rerender } = render(<Button variant="primary">Primary</Button>);

    expect(screen.getByRole('button')).toHaveClass('bg-primary');

    

    rerender(<Button variant="secondary">Secondary</Button>);

    expect(screen.getByRole('button')).toHaveClass('bg-gray-200');

    

    rerender(<Button variant="outline">Outline</Button>);

    expect(screen.getByRole('button')).toHaveClass('border-primary');

  });

  

  test('calls onClick when clicked', () => {

    const handleClick = jest.fn();

    render(<Button onClick={handleClick}>Click me</Button>);

    

    fireEvent.click(screen.getByRole('button'));

    expect(handleClick).toHaveBeenCalledTimes(1);

  });

  

  test('is disabled when isDisabled is true', () => {

    render(<Button isDisabled>Disabled</Button>);

    expect(screen.getByRole('button')).toBeDisabled();

  });

});

```



By following these patterns, we can improve code quality, maintainability, and performance while ensuring a consistent development experience. ```





#### `04-visual-systems\01-screenshot-infrastructure.md`



```markdown

# Screenshot Infrastructure



This document provides a detailed explanation of the screenshot capture system, which is a critical component of the Ringerike Landskap website development workflow.



## System Overview



The screenshot infrastructure captures and maintains visual records of the website, serving several important purposes:



1. **Visual Documentation**: Provides snapshots of the website's appearance at different stages

2. **AI-Assisted Development**: Enables AI tools to "see" the website output

3. **Visual Regression Testing**: Allows comparison of changes over time

4. **Responsive Design Verification**: Captures multiple viewport sizes



## Key Components



### Directory Structure



```

project/

├── .ai-analysis/               # AI-specific screenshots

│   ├── latest/                 # Most recent AI analysis

│   ├── snapshot-[timestamp]/   # Historical AI snapshots

│   └── metadata.json           # Snapshot tracking information

├── screenshots/                # General screenshot storage

│   ├── latest/                 # Most recent screenshots

│   ├── [YYYY-MM-DD_HH-MM-SS]/  # Timestamped screenshot sets

│   └── screenshot-report.html  # Visual report of screenshots

└── scripts/                    # Screenshot automation

    ├── screenshot-manager.js   # Main screenshot management

    ├── dev-with-snapshots.js   # Development with auto-capture

    ├── auto-snapshot.js        # Automated snapshot tool

    └── various helper scripts

```



### Core Scripts



1. **screenshot-manager.js**: Central script that handles:

   - Capturing screenshots at specified viewport sizes

   - Organizing screenshots into timestamped directories

   - Generating HTML reports

   - Cleaning up old screenshot sets



2. **dev-with-snapshots.js**: Enhanced development server that:

   - Starts the Vite development server

   - Watches for file changes

   - Automatically captures screenshots when changes occur

   - Updates the `latest` directories



3. **auto-snapshot.js**: AI-focused tool that:

   - Captures specific screenshots for AI analysis

   - Maintains the `.ai-analysis` directory

   - Tracks metadata about captures



### Capture Configuration



Screenshots are captured at these standard viewport sizes:



- **Mobile**: 375×667px

- **Tablet**: 768×1024px

- **Desktop**: 1440×900px



And for these primary pages:



- Home (/)

- About (/hvem-er-vi)

- Services (/hva-vi-gjor)

- Projects (/prosjekter)

- Contact (/kontakt)



## Usage in Development



### Standard Development Workflow



```bash

npm run dev:ai

```



This command:

1. Starts the Vite development server

2. Sets up file watchers for auto-capturing

3. Maintains the `latest` directories with current screenshots



### Manual Screenshot Capture



```bash

npm run screenshots:capture

```



This command manually captures a full set of screenshots across all configured viewports and pages.



### Screenshot Management



```bash

# View screenshot help

npm run screenshots



# Clean old screenshots (older than 7 days by default)

npm run screenshots:clean



# Refresh screenshots (capture, tidy, and clean)

npm run screenshots:refresh

```



## Integration with AI Tools



The system is designed to help AI tools (like Cursor) understand the visual output:



1. AI-specific snapshots are stored in `.ai-analysis/latest/`

2. Metadata about snapshots is maintained in `.ai-analysis/metadata.json`

3. Scripts detect changes and automatically update visual information



When working with AI assistants, they can reference:

```

/project/.ai-analysis/latest/

```



This provides consistent access to current visual state regardless of when snapshots were taken.



## Technical Implementation



The system uses:



- **Puppeteer**: For headless browser capture

- **Node.js file system APIs**: For directory management

- **Custom HTML templates**: For report generation

- **File watchers**: For detecting changes during development



## Critical Considerations



When working with the codebase:



1. **Do not modify** the core screenshot scripts without careful testing

2. **Do not delete** the `.ai-analysis` or `screenshots` directories

3. **Use the provided npm scripts** rather than calling scripts directly

4. Be aware that screenshot capture adds some overhead to development



## Extending the System



To add new pages or viewports to the capture system:



1. Edit `capture-website.js` to update the configuration

2. Add new entries to the `pages` or `viewports` arrays

3. Run `npm run screenshots:capture` to test



## Troubleshooting



If screenshots aren't being captured:



1. Ensure the development server is running

2. Check that the page is accessible at the expected URL

3. Verify there are no JavaScript errors preventing render

4. Try running `npm run screenshots:capture` manually



The screenshot infrastructure is a critical part of the development workflow and should be maintained carefully as the project evolves. ```





#### `04-visual-systems\02-design-guidelines.md`



```markdown

# Ringerike Landskap Website Design Guidelines



## Introduction



This document defines the UI and UX guidelines for the Ringerike Landskap website, ensuring a cohesive, brand-aligned, and user-friendly digital experience. It serves as an implementation blueprint for developers and designers.



## Objectives



-   Provide a structured, intuitive interface that clearly presents services and projects

-   Ensure seamless navigation with a user journey that mirrors how customers discover and engage with Ringerike Landskap

-   Maintain brand identity through consistent typography, colors, and imagery

-   Optimize for performance, accessibility, and SEO



## User Experience (UX) & Information Architecture



The UX design is clean, direct, and conversion-driven, guiding visitors toward understanding services, exploring past work, and making contact. The structure is designed to be logical and easy to navigate.



### Navigation & Site Structure



The website is structured into five core sections, accessible via the main navigation bar:



-   **Hjem** (Home) – The landing page providing a broad overview and brand introduction

-   **Hva vi gjør** (What We Do / Services) – A detailed presentation of landscaping and related services

-   **Prosjekter** (Projects) – A portfolio/gallery of past projects showcasing work quality

-   **Hvem er vi** (Who We Are / About Us) – Information about the company, team, and values

-   **Kontakt** (Contact) – Contact information and a form for inquiries or quotes



### User Journey



- **Browsing Services**: Visitors land on the homepage or "Hva vi gjør" page to learn about the firm's core landscaping solutions.

- **Exploring Projects**: They can explore real examples under "Prosjekter," filtering results by interest—like building a new terrace or installing a støttemur.

- **Seasonal Tips**: The site subtly adapts to reflect current or upcoming seasons, guiding users to relevant services (e.g., planting ideas in spring).

- **Filtering**: Users filter projects by category or location to find relevant examples.

- **Social Proof**: Testimonials provide proof and build trust throughout the decision-making process.

- **Requesting a Quote**: When ready, they fill out the contact form or tap a "Book gratis befaring" button to schedule an on-site evaluation.

- **Personal Follow-Up**: The Ringerike Landskap team personally contacts clients to discuss specific needs, ensuring straightforward, no-frills communication.



## Visual Design System



### Color Palette



The color palette is centered around greens to reflect the landscaping focus, with complementary neutrals:



-   **Primary Green**: #1e9545 (Used for primary buttons, links, and accents)

-   **Dark Green**: #0d6b30 (Used for hover states and secondary elements)

-   **Light Green**: #e6f4ea (Used for backgrounds and subtle highlights)

-   **Neutral Dark**: #333333 (Used for primary text)

-   **Neutral Mid**: #767676 (Used for secondary text)

-   **Neutral Light**: #f5f5f5 (Used for backgrounds and dividers)

-   **White**: #ffffff (Used for backgrounds and text on dark colors)



### Typography



The website uses the Inter font family throughout:



-   **Headings**: Inter Bold (700)

    -   H1: 2.5rem (40px)

    -   H2: 2rem (32px)

    -   H3: 1.5rem (24px)

    -   H4: 1.25rem (20px)

-   **Body Text**: Inter Regular (400), 1rem (16px)

-   **Small Text/Captions**: Inter Regular (400), 0.875rem (14px)

-   **Buttons/CTAs**: Inter Medium (500), 1rem (16px)



### Imagery & Icons



-   **Photography**: High-quality images of completed landscaping projects, focusing on natural elements

-   **Icons**: Simple, line-based icons for UI elements (using Lucide React library)

-   **Illustrations**: Minimal use, primarily for decorative purposes



## User Interface Elements



### Visual Style

- Clean design with high-quality imagery showcasing landscaping work across prioritized services

- Green accents reflect nature and sustainability

- Balance of white space and content for readability



### Call-to-Actions

- Prominent buttons like "Book Gratis Befaring" should stand out visually to encourage conversions

- Consistent styling for all interactive elements

- Clear visual hierarchy to guide user attention



### Project Showcase

- Grid-style layout with filters for easy exploration of completed works 

- Projects categorized by job type or location

- High-quality images with consistent aspect ratios



## Seasonal Content Adaptation



The website content adapts based on the current season to highlight seasonally-relevant services and projects:



### Spring (Vår)

- **Focus on**: Hekk og Beplantning, Ferdigplen

- **Relevant tags**: beplantning, plen, hage

- **Visual cues**: Fresh greenery, new plantings, garden preparations



### Summer (Sommer)

- **Focus on**: Platting, Cortenstål, Belegningsstein

- **Relevant tags**: terrasse, uteplass, innkjørsel

- **Visual cues**: Outdoor living spaces, entertaining areas, completed projects



### Fall (Høst)

- **Focus on**: Støttemurer, Kantstein, Trapper og Repoer

- **Relevant tags**: terrengforming, støttemur, trapp

- **Visual cues**: Structural elements, terrain shaping, preparation for winter



### Winter (Vinter)

- **Focus on**: Planning and Design

- **Relevant tags**: planlegging, design, prosjektering

- **Visual cues**: Design sketches, planning elements, winter-ready landscapes



## Responsive Design Practices



- **Mobile-First Approach**: Start with mobile layout and enhance for larger screens

- **Fluid Typography**: Scale text based on screen size

- **Flexible Images**: Ensure images adapt to their containers

- **Touch-Friendly UI**: Larger touch targets on mobile

- **Performance Optimization**: Lazy loading and optimized assets

- **Testing**: Test on multiple devices and screen sizes

- **Accessibility**: Ensure accessibility across all screen sizes



## Project Categories & Locations



### Service Categories

- Belegningsstein

- Cortenstål

- Støttemurer

- Platting

- Ferdigplen

- Kantstein

- Trapper og Repoer

- Hekk og Beplantning



### Service Areas

- Røyse (Main Base)

- Hønefoss

- Hole kommune

- Jevnaker

- Sundvollen

- Vik



## Component Guidelines



### Buttons



-   **Primary Button**: Green background (#1e9545), white text, slight rounded corners (4px)

-   **Secondary Button**: White background, green border, green text

-   **Tertiary Button/Text Link**: No background, green text with subtle underline on hover



### Cards



-   **Service Cards**: Image background with gradient overlay, white text, subtle hover effect

-   **Project Cards**: Image with consistent aspect ratio, title and location information below



### Forms



-   **Input Fields**: Clear labels, light background, green focus state

-   **Validation**: Inline validation with clear error messages

-   **Submit Buttons**: Primary button style with loading state



## Responsive Design



The website follows a mobile-first approach with three main breakpoints:



-   **Mobile**: < 768px

-   **Tablet**: 768px - 1023px

-   **Desktop**: ≥ 1024px



Key responsive considerations:



-   Navigation collapses to hamburger menu on mobile

-   Grid layouts adjust from multi-column to single-column

-   Font sizes scale proportionally

-   Touch targets are appropriately sized for mobile



## Implementation Notes



-   Use Tailwind CSS for styling with custom theme configuration

-   Implement responsive images with appropriate sizing and formats

-   Ensure accessibility compliance with WCAG 2.1 standards

-   Optimize performance with lazy loading and code splitting

```





#### `04-visual-systems\03-styling-approach.md`



```markdown

# Ringerike Landskap Website Styling and Theming



## Color Palette



The website uses a custom green-focused color palette that reflects the landscaping business's connection to nature:



```

colors: {

  green: {

    50: '#f0f9f4',   // Very light green (backgrounds, hover states)

    100: '#dbf0e3',  // Light green (backgrounds, borders)

    200: '#b8e0ca',  // Light green (secondary elements)

    300: '#89c9a8',  // Medium green (accents)

    400: '#57ab83',  // Medium green (secondary buttons)

    500: '#1e9545',  // Primary green (buttons, links)

    600: '#157c3a',  // Darker green (hover states)

    700: '#116530',  // Dark green (active states)

    800: '#0e4f27',  // Very dark green (text on light backgrounds)

    900: '#0c4121',  // Darkest green (text on light backgrounds)

  },

}

```



Other colors used:



-   White (`#ffffff`) - Backgrounds, text on dark surfaces

-   Gray shades - Text, borders, subtle backgrounds

-   Black with opacity - Overlays, shadows



## Typography



The website uses a clean, modern typography system:



```

fontFamily: {

  sans: ['Inter', 'system-ui', 'sans-serif'],

}

```



Font sizes follow a hierarchical scale:



-   Headings:

    -   H1: `text-4xl sm:text-5xl lg:text-6xl` (2.25rem to 3.75rem)

    -   H2: `text-xl sm:text-2xl` (1.25rem to 1.5rem)

    -   H3: `text-lg font-semibold` (1.125rem)

-   Body: `text-base` (1rem)

-   Small text: `text-sm` (0.875rem)

-   Micro text: `text-xs` (0.75rem)



Font weights:



-   Regular: 400

-   Medium: 500

-   Semibold: 600

-   Bold: 700



## Spacing System



The website uses Tailwind's default spacing scale with consistent spacing throughout:



-   Container padding: `px-4 sm:px-6 lg:px-8`

-   Vertical section spacing: `py-8 sm:py-12`

-   Grid gaps: `gap-6 sm:gap-8`

-   Component padding: `px-4 py-3` (buttons), `p-6` (cards)



## Animations



Custom animations defined in the Tailwind config:



```

animation: {

  'fade-in': 'fadeIn 0.5s ease-out forwards',

  'slide-up': 'slideUp 0.5s ease-out forwards',

  'gradient': 'gradient 8s ease-in-out infinite',

},

keyframes: {

  fadeIn: {

    '0%': { opacity: '0' },

    '100%': { opacity: '1' },

  },

  slideUp: {

    '0%': {

      opacity: '0',

      transform: 'translateY(20px)'

    },

    '100%': {

      opacity: '1',

      transform: 'translateY(0)'

    },

  },

  gradient: {

    '0%, 100%': { backgroundPosition: '0% 50%' },

    '50%': { backgroundPosition: '100% 50%' },

  },

}

```



These animations are used for:



-   Page transitions

-   Component mounting

-   Hover effects

-   Background gradients



## UI Components



### Buttons



Primary button:



```

className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"

```



Secondary button:



```

className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"

```



### Cards



Service/Project card:



```

className="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1"

```



### Hero Section



The Hero component uses multiple overlays for text readability:



1. Base background image with fixed attachment

2. Gradient overlay for text contrast

3. Radial gradient for corner darkening



### Navigation



Desktop navigation:



```

className="hidden md:flex md:items-center md:space-x-8"

```



Mobile navigation:



```

className="md:hidden"

```



Active link:



```

className="text-green-600"

```



Inactive link:



```

className="text-gray-700 hover:text-green-600 transition-colors"

```



## Responsive Design



The website uses a mobile-first approach with breakpoints:



-   Default: Mobile styles

-   `sm`: 640px and up

-   `md`: 768px and up

-   `lg`: 1024px and up

-   `xl`: 1280px and up



Key responsive patterns:



-   Single column on mobile, multi-column on larger screens

-   Hidden navigation on mobile, visible on desktop

-   Adjusted font sizes across breakpoints

-   Flexible spacing that increases on larger screens



## Shadows and Depth



The website uses a consistent shadow system for depth:



-   Cards: `shadow-md`

-   Hover states: `shadow-lg`

-   Buttons: `shadow-sm`

-   Header (when scrolled): `shadow-md`



## Special Effects



1. **Backdrop Blur**:



    ```

    className="bg-white/90 backdrop-blur-sm"

    ```



    Used in the header for a frosted glass effect



2. **Gradient Overlays**:



    ```

    background: linear-gradient(50deg, transparent 15%, rgba(0,0,0,0.75) 32%, rgba(0,0,0,0.6) 72%, transparent 85%)

    ```



    Used in the hero section for text readability



3. **Hover Transitions**:



    ```

    className="transition-colors hover:text-green-600"

    ```



    Smooth color changes on hover



4. **Scroll Animations**:

   Elements fade in and slide up as they enter the viewport

```





#### `05-development-workflow\01-environment-setup.md`



```markdown

# Development Environment Setup



This document provides instructions for setting up the development environment for the Ringerike Landskap website and outlines the recommended workflow.



## Prerequisites



Before starting development, ensure you have the following installed:



- **Node.js**: v18.x or later

- **npm**: v8.x or later

- **Git**: For version control

- A modern code editor (VS Code recommended)



## Initial Setup



1. **Clone the repository**:



   ```bash

   git clone [repository-url]

   cd rl-website_web/project

   ```



2. **Install dependencies**:



   ```bash

   npm install

   ```



3. **Verify the installation**:



   ```bash

   npm run dev

   ```



   This should start the development server and make the website available at http://localhost:5173/.



## Development Workflows



### Standard Development



For regular development work without screenshot capture:



```bash

npm run dev

```



This starts the Vite development server with hot module replacement.



### Development with Screenshots



For development with automatic screenshot capturing:



```bash

npm run dev:ai

```



This starts the development server and sets up automatic screenshot capture. Screenshots will be captured whenever significant changes are detected and stored in both the `screenshots/latest/` and `.ai-analysis/latest/` directories.



### Manual Screenshot Management



```bash

# Capture screenshots manually

npm run screenshots:capture



# Clean up old screenshots

npm run screenshots:clean



# Refresh screenshots (capture, tidy, and clean)

npm run screenshots:refresh

```



## Working with the Codebase



### Folder Structure



When working with the codebase, follow these guidelines:



1. **Components**: Place reusable components in `src/components/`

   - UI components in `src/components/ui/`

   - Layout components in `src/components/layout/`



2. **Pages**: Place page components in `src/pages/`

   - Each page in its own directory

   - Use `index.tsx` for the main page component



3. **Feature-specific components**: Place in `src/features/[feature-name]/`



4. **Data**: Place data files in `src/data/`



5. **Types**: Place TypeScript interfaces in `src/types/`



### Coding Standards



Follow these standards when writing code:



1. **TypeScript**: Use TypeScript for all new code

   - Define interfaces for props and data structures

   - Avoid using `any` type

   - Use proper type guards



2. **Components**: Use functional components with hooks

   - Define explicit prop interfaces

   - Use destructuring for props

   - Export components as named exports



3. **Styling**: Use Tailwind CSS for styling

   - Group related classes together

   - Use clsx or tailwind-merge for conditional classes

   - Follow the existing color scheme and design system



4. **State Management**: Prefer React Context for shared state

   - Use local state for component-specific state

   - Avoid prop drilling where possible



### Git Workflow



Follow this workflow for version control:



1. Create a branch for your feature or fix

2. Make your changes, following the coding standards

3. Run tests and ensure all features work

4. Create a pull request with a clear description

5. Address review comments

6. Merge only when approved



## Build and Deployment



### Building for Production



To build the website for production:



```bash

npm run build

```



This creates optimized production files in the `dist/` directory.



### Preview Production Build



To preview the production build locally:



```bash

npm run preview

```



This serves the production build for local testing.



## Troubleshooting



### Development Server Issues



If the development server fails to start:



1. Check if another process is using port 5173

2. Try deleting `node_modules` and reinstalling dependencies

3. Check for errors in the console output



### Screenshot Capture Issues



If screenshots aren't being captured:



1. Ensure the development server is running

2. Check that Puppeteer can access the server

3. Try running screenshots manually with `npm run screenshots:capture`

4. Check the console for error messages



## AI-Assisted Development



When working with AI tools (like Cursor):



1. Make sure screenshots are up to date using `npm run screenshots:capture`

2. Reference the current visual state using the `.ai-analysis/latest/` directory

3. Provide clear context about the component or feature you're working on

4. Reference the appropriate documentation for context



This setup ensures a smooth development experience while maintaining high code quality and consistent visual output. ```





#### `05-development-workflow\02-optimization-guide.md`



```markdown

# Ringerike Landskap Website Optimization Guide



## Overview



This guide outlines the performance optimization strategies and implementation patterns for the Ringerike Landskap website to ensure fast loading times, responsive behavior, and an optimal user experience across all devices.



## Performance Goals



-   **Load Time**: Initial page load under 2 seconds on 4G connections

-   **First Contentful Paint (FCP)**: Under 1.5 seconds

-   **Time to Interactive (TTI)**: Under 3.5 seconds

-   **Cumulative Layout Shift (CLS)**: <0.1

-   **Largest Contentful Paint (LCP)**: <2.5 seconds

-   **First Input Delay (FID)**: <100ms

-   **Lighthouse Score**: 90+ in all categories (Performance, Accessibility, Best Practices, SEO)



## Asset Optimization



### Images



-   **WebP Format**: Convert all images to WebP format with fallbacks for older browsers

-   **Lazy Loading**: Implement lazy loading for images below the fold

-   **Responsive Images**: Use the `srcset` attribute to serve different image sizes based on viewport

-   **Size Attributes**: Include width and height attributes to prevent layout shifts

-   **Optimization Tool**: Use ImageOptim or similar tools for further compression

-   **CDN Integration**: Serve images through a CDN for faster delivery



### Fonts



-   **Font Display**: Use `font-display: swap` to prevent invisible text during font loading

-   **Font Subsetting**: Only include character sets that are needed

-   **WOFF2 Format**: Use WOFF2 as the primary format with fallbacks

-   **Preload Critical Fonts**: Preload fonts used in above-the-fold content

-   **System Fonts**: Use system fonts for non-branding text elements



## JavaScript Optimization



### Code Splitting



-   **Route-Based Splitting**: Split code by routes to load only what's needed

-   **Component-Level Splitting**: Lazy load heavy components that aren't immediately visible

-   **Vendor Chunking**: Separate vendor code that changes less frequently

-   **Dynamic Imports**: Use dynamic imports for conditional functionality



```javascript

// Example of lazy loading a component

const HeavyComponent = React.lazy(() => import('./HeavyComponent'));



// Usage with Suspense

<Suspense fallback={<div>Loading...</div>}>

  <HeavyComponent />

</Suspense>

```



### Bundle Size Management



-   **Tree Shaking**: Ensure unused code is eliminated

-   **Dependencies Audit**: Regularly review and minimize dependencies

-   **Bundle Analyzer**: Use Webpack Bundle Analyzer or Vite's bundle analysis tools to identify large packages

-   **Code Minification**: Ensure all production code is minified and compressed



## Component Optimization Patterns



### Responsive Optimization



- **Mobile-First Approach**: Start with mobile layout and enhance for larger screens

- **Fluid Typography**: Scale text based on screen size

- **Flexible Images**: Ensure images adapt to their containers

- **Touch-Friendly UI**: Larger touch targets on mobile

- **Performance Optimization**: Lazy loading and optimized assets

- **Testing**: Test on multiple devices and screen sizes

- **Accessibility**: Ensure accessibility across all screen sizes



### Code Organization Principles



- **High cohesion**: Related functionality is grouped together

- **Low coupling**: Components are independent and reusable

- **Consistent naming conventions and file structure**: Maintain consistency throughout the codebase



### Implementation Techniques



-   **Memoization**: Use React.memo for expensive components

-   **Virtual Lists**: For long scrollable lists, use virtual scrolling techniques

-   **State Management**: Keep state as local as possible, avoid unnecessary global state

-   **Debounce & Throttle**: Apply to frequent events like scroll and resize



```typescript

// Example of memoizing a component

const MemoizedComponent = React.memo(({ prop1, prop2 }) => {

  // Component implementation

  return <div>{/* rendered content */}</div>;

});



// Example of debouncing a search input

function SearchInput() {

  const [value, setValue] = useState('');

  const debouncedValue = useDebounce(value, 300);



  // Use debouncedValue for search operations

}

```



## Caching Strategy



-   **HTTP Caching**: Set appropriate cache-control headers

-   **Service Worker**: Use for offline functionality and caching static assets

-   **Local Storage**: Cache UI preferences and non-sensitive data

-   **Memory Caching**: Use for frequently accessed data during the session



## Rendering Strategy



-   **Static Generation**: Pre-render pages that don't need frequent updates

-   **Incremental Static Regeneration**: Update static pages in the background

-   **Client-Side Rendering**: Use for highly dynamic, personalized content



## Monitoring and Analytics



-   **Real User Monitoring (RUM)**: Track actual user experiences

-   **Core Web Vitals**: Monitor LCP, FID, and CLS

-   **Error Tracking**: Implement comprehensive error tracking

-   **Performance Budgets**: Set and maintain performance budgets



## Accessibility and Performance



-   **Keyboard Navigation**: Ensure all interactions are keyboard accessible

-   **Focus Management**: Properly manage focus for modals and dynamic content

-   **Reduced Motion**: Respect user preferences for reduced motion

-   **Color Contrast**: Maintain appropriate contrast ratios

-   **Semantic HTML**: Use correct HTML elements for better performance and accessibility



## Data Optimization



### Static Data



-   **Preload Critical Data**: Include critical data in the initial HTML

-   **Local Caching**: Cache static data that doesn't change frequently

-   **Data Bundling**: Bundle related data to reduce request overhead

-   **Data Compression**: Compress data responses



### API Optimizations



-   **Request Batching**: Combine multiple API requests where possible

-   **Data Pagination**: Paginate large data sets

-   **Query Optimization**: Only request the data that's needed

-   **Response Caching**: Cache API responses appropriately



## SEO and Performance



-   **Meta Tags**: Optimize title, description, and other meta tags using React Helmet

-   **Structured Data**: Implement Schema.org markup

-   **Semantic HTML**: Use proper HTML structure

-   **URL Structure**: Maintain clean, descriptive URLs



## Mobile Performance



-   **Touch Optimization**: Ensure touch targets are at least 44Ã—44 pixels

-   **Viewport Settings**: Configure properly for responsive design

-   **Critical CSS**: Inline critical CSS for faster rendering on mobile

-   **Reduced Network Requests**: Minimize HTTP requests for mobile



## Testing and Validation



-   **Performance Testing**: Regular performance audits with Lighthouse

-   **Cross-Browser Testing**: Test in all major browsers

-   **Device Testing**: Test across various devices and screen sizes

-   **Network Throttling**: Test under various network conditions

-   **A/B Testing**: Test performance optimizations against user metrics



## Deployment and CI/CD



-   **Automated Testing**: Include performance tests in CI pipeline

-   **Performance Budgets**: Set budgets and fail builds that exceed them

-   **Cache Invalidation**: Proper strategy for updating cached assets

-   **Progressive Rollouts**: Gradual deployment to monitor performance impact



## Vite-Specific Optimizations



Ringerike Landskap's website leverages Vite's built-in optimization features for enhanced performance:



### Build Optimization



-   **Chunking Strategy**: Configure optimal chunk sizes

-   **Module Preloading**: Preload critical modules

-   **Asset Optimization**: Optimize CSS, JS, and other assets

-   **Tree Shaking**: Remove unused code

-   **Minification**: Minify all production assets



### Development Optimization



-   **Hot Module Replacement**: Fast updates during development

-   **Fast Refresh**: Maintain component state during updates

-   **ESBuild**: Leverage fast bundling during development

```





#### `05-development-workflow\03-accessibility-guide.md`



```markdown

# Accessibility Recommendations



This document outlines specific recommendations to improve the accessibility of the Ringerike Landskap website, ensuring it's usable by people with various disabilities and meets WCAG 2.1 standards.



## Current Accessibility Assessment



Based on the analysis of the codebase and rendered website, several accessibility aspects have been identified:



-   **Semantic HTML**: Generally good use of semantic elements, but some improvements needed

-   **ARIA Attributes**: Limited implementation of ARIA roles and attributes

-   **Keyboard Navigation**: Basic functionality exists but needs enhancement

-   **Color Contrast**: Generally good, but some areas need improvement

-   **Focus Management**: Basic focus states exist but could be enhanced

-   **Screen Reader Support**: Limited explicit support for screen readers



## Semantic HTML Improvements



### Document Structure



Ensure proper heading hierarchy throughout the site:



```tsx

// Before - Incorrect heading hierarchy

<h1>Page Title</h1>

<div>

  <h3>Section Title</h3> // Skipping h2

  <p>Content</p>

</div>



// After - Correct heading hierarchy

<h1>Page Title</h1>

<div>

  <h2>Section Title</h2>

  <p>Content</p>

</div>

```



### Landmark Regions



Add proper landmark regions to improve navigation:



```tsx

// Before

<div className="header">...</div>

<div className="main-content">...</div>

<div className="footer">...</div>



// After

<header>...</header>

<main>

  <section aria-labelledby="section-title">

    <h2 id="section-title">Section Title</h2>

    ...

  </section>

</main>

<footer>...</footer>

```



## ARIA Implementation



### Interactive Elements



Add appropriate ARIA attributes to interactive elements:



```tsx

// Before - Mobile menu button

<button

  type="button"

  className="md:hidden rounded-md p-2 text-gray-700"

  onClick={() => setIsMenuOpen(!isMenuOpen)}

>

  <span className="sr-only">

    {isMenuOpen ? "Lukk meny" : "Åpne meny"}

  </span>

  {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}

</button>



// After - Enhanced mobile menu button

<button

  type="button"

  className="md:hidden rounded-md p-2 text-gray-700"

  onClick={() => setIsMenuOpen(!isMenuOpen)}

  aria-expanded={isMenuOpen}

  aria-controls="mobile-menu"

  aria-label={isMenuOpen ? "Lukk meny" : "Åpne meny"}

>

  <span className="sr-only">

    {isMenuOpen ? "Lukk meny" : "Åpne meny"}

  </span>

  {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}

</button>

```



### Form Elements



Enhance form accessibility with proper labels and ARIA attributes:



```tsx

// Before - Form field without proper label

<input

  type="email"

  placeholder="E-post"

  className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"

/>



// After - Form field with proper label and ARIA

<div className="form-field">

  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">

    E-post

  </label>

  <input

    id="email"

    type="email"

    placeholder="<EMAIL>"

    aria-required="true"

    className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"

  />

</div>

```



### Dynamic Content



Add ARIA live regions for dynamic content:



```tsx

// Before - Dynamic content without ARIA

<div className="notification">

  {message && <p>{message}</p>}

</div>



// After - Dynamic content with ARIA live region

<div

  className="notification"

  aria-live="polite"

  aria-atomic="true"

>

  {message && <p>{message}</p>}

</div>

```



## Keyboard Navigation



### Skip Link



Add a skip link to bypass navigation:



```tsx

// Add at the top of the page layout

<a

  href="#main-content"

  className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-white focus:text-green-600 focus:shadow-md"

>

  Hopp til hovedinnhold

</a>



// Then add the corresponding ID to the main content

<main id="main-content">

  ...

</main>

```



### Focus Management



Improve focus styles for better visibility:



```css

/* Add to global CSS */

:focus {

    outline: 2px solid #1e9545;

    outline-offset: 2px;

}



/* For elements where the default outline doesn't work well */

.custom-focus:focus {

    box-shadow: 0 0 0 2px white, 0 0 0 4px #1e9545;

    outline: none;

}

```



### Keyboard Accessible Components



Enhance the carousel component for keyboard accessibility:



```tsx

// Add keyboard support to carousel navigation

<button

  onClick={prevSlide}

  onKeyDown={(e) => e.key === 'Enter' && prevSlide()}

  tabIndex={0}

  aria-label="Forrige prosjekt"

  className="carousel-control"

>

  <ChevronLeft className="w-6 h-6" />

</button>



<button

  onClick={nextSlide}

  onKeyDown={(e) => e.key === 'Enter' && nextSlide()}

  tabIndex={0}

  aria-label="Neste prosjekt"

  className="carousel-control"

>

  <ChevronRight className="w-6 h-6" />

</button>

```



## Color and Contrast



### Text Contrast



Ensure sufficient contrast for all text:



```tsx

// Before - Potentially low contrast

<p className="text-gray-400">Some text</p>



// After - Improved contrast

<p className="text-gray-700">Some text</p>

```



### Non-Text Contrast



Ensure interactive elements have sufficient contrast:



```tsx

// Before - Border might have insufficient contrast

<button className="border border-gray-300 text-gray-700">Button</button>



// After - Improved border contrast

<button className="border-2 border-gray-500 text-gray-700">Button</button>

```



### Color Independence



Don't rely solely on color to convey information:



```tsx

// Before - Using only color to indicate status

<div className={`status ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>



// After - Using color and text to indicate status

<div

  className={`status ${isActive ? 'bg-green-500' : 'bg-red-500'}`}

  aria-label={isActive ? 'Aktiv' : 'Inaktiv'}

>

  {isActive ? '✓' : '✗'}

</div>

```



## Images and Media



### Alt Text



Ensure all images have appropriate alt text:



```tsx

// Before - Missing or generic alt text

<img src="/images/projects/project1.jpg" alt="Project" />



// After - Descriptive alt text

<img

  src="/images/projects/project1.jpg"

  alt="Completed garden landscaping project in Røyse with stone pathways and native plantings"

/>



// For decorative images

<img

  src="/images/decorative/pattern.jpg"

  alt=""

  role="presentation"

/>

```



### SVG Accessibility



Enhance SVG accessibility:



```tsx

// Before - SVG without accessibility attributes

<svg width="24" height="24" viewBox="0 0 24 24">

  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />

</svg>



// After - Accessible SVG

<svg

  width="24"

  height="24"

  viewBox="0 0 24 24"

  aria-hidden="true"

  focusable="false"

>

  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />

</svg>

```



## Form Accessibility



### Form Validation



Implement accessible form validation:



```tsx

// Enhanced form field with validation

const FormField = ({ id, label, type, required, error, ...props }) => {

    return (

        <div className="form-field">

            <label

                htmlFor={id}

                className="block text-sm font-medium text-gray-700 mb-1"

            >

                {label} {required && <span aria-hidden="true">*</span>}

            </label>

            <input

                id={id}

                type={type}

                aria-required={required}

                aria-invalid={!!error}

                aria-describedby={error ? `${id}-error` : undefined}

                className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent ${

                    error ? "border-red-500" : "border-gray-300"

                }`}

                {...props}

            />

            {error && (

                <div

                    id={`${id}-error`}

                    className="mt-1 text-sm text-red-600"

                    role="alert"

                >

                    {error}

                </div>

            )}

        </div>

    );

};

```



### Required Fields



Clearly indicate required fields:



```tsx

// Add to the form component

<p className="text-sm text-gray-500 mb-4">

    Felt markert med <span aria-hidden="true">*</span>

    <span className="sr-only">stjerne</span> er obligatoriske

</p>

```



## Screen Reader Support



### Descriptive Link Text



Improve link text for screen readers:



```tsx

// Before - Vague link text

<Link to="/prosjekter">Les mer</Link>



// After - Descriptive link text

<Link to="/prosjekter">

  Se alle prosjekter

  <span className="sr-only"> om landskapsdesign</span>

</Link>

```



### Table Accessibility



Enhance table accessibility:



```tsx

// Accessible table structure

<table>

    <caption>Prisliste for tjenester</caption>

    <thead>

        <tr>

            <th scope="col">Tjeneste</th>

            <th scope="col">Pris (NOK)</th>

            <th scope="col">Estimert tid</th>

        </tr>

    </thead>

    <tbody>

        <tr>

            <th scope="row">Hagedesign</th>

            <td>5000</td>

            <td>2 uker</td>

        </tr>

        {/* More rows */}

    </tbody>

</table>

```



## Implementation Priority



1. **High Priority (Essential for Accessibility)**



    - Add proper semantic HTML structure

    - Implement form labels and ARIA attributes

    - Ensure sufficient color contrast

    - Add alt text to all images



2. **Medium Priority (Important Improvements)**



    - Add skip links

    - Enhance keyboard navigation

    - Implement ARIA live regions

    - Improve focus management



3. **Lower Priority (Additional Enhancements)**

    - Add screen reader specific text

    - Enhance SVG accessibility

    - Implement advanced ARIA patterns for complex components



## Testing Recommendations



1. **Automated Testing**



    - Implement axe-core for automated accessibility testing

    - Add accessibility checks to CI/CD pipeline



2. **Manual Testing**



    - Test with keyboard navigation only

    - Test with screen readers (NVDA, JAWS, VoiceOver)

    - Test with high contrast mode

    - Test with text zoom (up to 200%)



3. **User Testing**

    - Conduct testing with users who have disabilities

    - Gather feedback on usability and accessibility



## Conclusion



Implementing these accessibility recommendations will significantly improve the usability of the Ringerike Landskap website for all users, including those with disabilities. The most critical improvements involve proper semantic HTML, form accessibility, and keyboard navigation, which should be prioritized in the implementation plan.



Regular accessibility testing should be conducted to ensure that the website maintains compliance with accessibility standards as new features and content are added.

```





#### `AI-README.md`



```markdown

# AI ASSISTANT GUIDE TO RINGERIKE LANDSKAP WEBSITE



## CRITICAL CONTEXT (PLEASE READ FIRST)



This project is an in-development website for Ringerike Landskap AS that has evolved into a complex codebase. The documentation in this directory serves two purposes:



1. To accurately map the CURRENT state of the codebase (not an idealized version)

2. To establish a clear direction for future development



## PROJECT OVERVIEW



Ringerike Landskap AS is a landscaping and machine-contracting company based in Hole, Norway, offering eight core services including stonework, planting, steel installations, and retaining walls. This website is designed to showcase their services, projects, and expertise while adapting seasonally to highlight relevant offerings.



The website is built with:

- React 18

- TypeScript

- Vite

- Tailwind CSS

- React Router

- React Helmet for SEO management



## KEY SYSTEMS



The project consists of four integrated systems:



1. **Website Application**: React/TypeScript site built with Vite, React Router, and Tailwind CSS

2. **Visual Documentation System**: Integrated screenshot capture system for tracking visual changes

3. **AI Development Tools**: Specialized infrastructure for AI-assisted development

4. **Content Management**: Typed content with seasonal adaptation



## DOCUMENTATION STRUCTURE



The documentation is organized into five main sections:



- **01-project-state/**: Current state documentation

  - [01-current-codebase-reality.md](./01-project-state/01-current-codebase-reality.md) - Start here

  - [02-technical-debt-inventory.md](./01-project-state/02-technical-debt-inventory.md) - Issues to address

  - [03-company-information.md](./01-project-state/03-company-information.md) - Business context



- **02-codebase-map/**: Structure and relationships

  - [01-directory-structure.md](./02-codebase-map/01-directory-structure.md) - File organization

  - [02-architecture-overview.md](./02-codebase-map/02-architecture-overview.md) - System architecture

  - [03-component-hierarchy.md](./02-codebase-map/03-component-hierarchy.md) - Component relationships



- **03-implementation-patterns/**: Code patterns

  - [01-current-patterns.md](./03-implementation-patterns/01-current-patterns.md) - Existing patterns

  - [02-component-patterns.md](./03-implementation-patterns/02-component-patterns.md) - Component details

  - [03-target-patterns.md](./03-implementation-patterns/03-target-patterns.md) - Target patterns



- **04-visual-systems/**: Visual documentation

  - [01-screenshot-infrastructure.md](./04-visual-systems/01-screenshot-infrastructure.md) - Critical system

  - [02-design-guidelines.md](./04-visual-systems/02-design-guidelines.md) - Design principles

  - [03-styling-approach.md](./04-visual-systems/03-styling-approach.md) - CSS approach



- **05-development-workflow/**: Development process

  - [01-environment-setup.md](./05-development-workflow/01-environment-setup.md) - Development environment

  - [02-optimization-guide.md](./05-development-workflow/02-optimization-guide.md) - Performance

  - [03-accessibility-guide.md](./05-development-workflow/03-accessibility-guide.md) - Accessibility



## KNOWN LIMITATIONS AND CHALLENGES



1. **Component Structure**: Inconsistent component structures exist

2. **State Management**: The implementation uses various state management approaches

3. **SEO Optimization**: The site needs further optimization for search engines

4. **Seasonal Content**: Website content should adapt to current seasons

5. **Performance**: Several areas need performance improvements



## RECOMMENDED READING ORDER



When engaging with this codebase for the first time, AI assistants should follow this reading path:



1. First, read `rl-website-initial-notes.md` to understand the complete project background, company details, and website requirements.

2. Next, read the files in the `01-project-state/` folder to understand the current state of the project

3. Continue with the `02-codebase-map/` to understand the structure

4. Then review `03-implementation-patterns/` to learn current and target patterns

5. For visual understanding, check `04-visual-systems/`

6. Finally, understand the development process from `05-development-workflow/`



## CAUTION AREAS



When modifying this codebase, be especially careful with:



1. **Screenshot generation system** - This is essential for development workflow

   - See [04-visual-systems/01-screenshot-infrastructure.md](./04-visual-systems/01-screenshot-infrastructure.md)

   - Never modify core screenshot scripts without careful testing

   - Don't delete the `.ai-analysis` or `screenshots` directories



2. **Content data structures** - These must maintain their current shape

   - Content is defined in `src/data/*.ts` files

   - Maintain existing type structures and relationships



3. **Routing structure** - Navigation patterns should remain consistent

   - Routes are defined in App.tsx

   - Norwegian route names should be preserved



## GETTING STARTED



For quick setup:

```bash

npm install    # Install dependencies

npm run dev    # Start development server

npm run dev:ai # Start development with auto-screenshots

```



## SCREENSHOT REFERENCE



To view the current visual state of the website, reference the latest screenshots at:

```

/.ai-analysis/latest/

```



These images are crucial for understanding the current visual state when making changes.



## CORE DOCUMENTATION FILES



- **README.md**: Main README file for human developers

- **AI-README.md**: This file, specifically for AI assistants

- **rl-website-initial-notes.md**: Initial project documentation with company information, website purpose, SEO strategy, and detailed requirements for the website.



## PROJECT OVERVIEW



Ringerike Landskap AS is a landscaping company in Norway specializing in outdoor spaces throughout the Ringerike region. The website serves as a digital showcase for their services while implementing hyperlocal SEO strategies to reach potential customers in Hole, HÃ¸nefoss, Jevnaker, Sundvollen, and Vik.



The website is built with:

- Vue 3 (Composition API)

- TypeScript

- Vite

- Tailwind CSS with DaisyUI

- Vue Router

- Pinia for state management



## KEY SYSTEMS



The website consists of several integrated systems:



1. **Website Application**: A Vue 3 frontend with TypeScript, organized by features

2. **Visual Documentation System**: Screenshots and visual references for development

3. **AI Development Tools**: Documentation and tools to assist AI in development

4. **Content Management**: Static data files for website content



## DOCUMENTATION STRUCTURE



This documentation is organized into five main sections:



1. **01-project-state/**: Current state documentation

   - `01-current-codebase-reality.md`: Analysis of current codebase state

   - `02-technical-debt-inventory.md`: Known issues and technical debt

   - `03-company-information.md`: Information about Ringerike Landskap



2. **02-codebase-map/**: Mapping the structure and relationships in the codebase

   - `01-directory-structure.md`: Explanation of folders and files

   - `02-architecture-overview.md`: High-level architecture explanation

   - `03-component-hierarchy.md`: Component relationships and hierarchy



3. **03-implementation-patterns/**: Current and target coding patterns

   - `01-current-patterns.md`: Current implementation patterns

   - `02-component-patterns.md`: Component implementation details

   - `03-target-patterns.md`: Recommended patterns to follow



4. **04-visual-systems/**: Documentation of visual systems

   - `01-screenshot-infrastructure.md`: How screenshots are organized

   - `02-design-guidelines.md`: Design system guidelines

   - `03-styling-approach.md`: How styling is implemented



5. **05-development-workflow/**: Development processes and requirements

   - `01-environment-setup.md`: Setting up the development environment

   - `02-optimization-guide.md`: Performance optimization strategies

   - `03-accessibility-guide.md`: Accessibility best practices



## KNOWN LIMITATIONS AND CHALLENGES



1. **Component Structure**: Inconsistent component structures exist

2. **State Management**: The implementation uses various state management approaches

3. **SEO Optimization**: The site needs further optimization for search engines

4. **Seasonal Content**: Website content should adapt to current seasons

5. **Performance**: Several areas need performance improvements



## RECOMMENDED READING ORDER



When engaging with this codebase for the first time, AI assistants should follow this reading path:



1. First, read `rl-website-initial-notes.md` to understand the complete project background, company details, and website requirements.

2. Next, read the files in the `01-project-state/` folder to understand the current state of the project

3. Continue with the `02-codebase-map/` to understand the structure

4. Then review `03-implementation-patterns/` to learn current and target patterns

5. For visual understanding, check `04-visual-systems/`

6. Finally, understand the development process from `05-development-workflow/`



## CAUTION AREAS



Be careful when working with:



1. **SEO Elements**: Any changes to meta tags, structured data, or URL structure

2. **Performance-Critical Code**: Components that load on every page

3. **Image Processing**: Ensure proper optimization and lazy loading

4. **Seasonal Adaptations**: Changes should respect the season-based content strategy



## GETTING STARTED



To begin development:



1. Clone the repository

2. Install dependencies with `npm install`

3. Run the development server with `npm run dev`

4. Access the local development server at `http://localhost:5173/`



## SCREENSHOT REFERENCES



Screenshots of the website can be found in the `/screenshots` directory. Use these for visual reference during development.



## CORE DOCUMENTATION FILES



- **README.md**: Main README file for human developers

- **AI-README.md**: This file, specifically for AI assistants

- **rl-website-initial-notes.md**: Initial project documentation with company information, website purpose, SEO strategy, and detailed requirements for the website. ```





#### `README.md`



```markdown

# Ringerike Landskap Website Documentation



This directory contains comprehensive documentation for the Ringerike Landskap website. The documentation is organized into logical sections to provide both high-level overviews and detailed technical recommendations.



### Key Files



- **AI-README.md**: Entry point for AI assistants with comprehensive context about the project, its architecture, and development patterns.

- **rl-website-initial-notes.md**: Initial project documentation that provides detailed information about the company, target audience, website purpose, SEO strategy, and technical requirements. This document serves as the foundational brief for the entire project.



### Project Documentation Folders



1. **01-project-state/**: Documentation about the current state of the project

   - `01-current-codebase-reality.md`: Assessment of the current codebase

   - `02-technical-debt-inventory.md`: Inventory of technical debt and issues

   - `03-company-information.md`: Information about Ringerike Landskap



2. **02-codebase-map/**: Documentation mapping the structure and relationships in the codebase

   - `01-directory-structure.md`: Explanation of the directory structure

   - `02-architecture-overview.md`: Overview of the architecture

   - `03-component-hierarchy.md`: Hierarchy of components



3. **03-implementation-patterns/**: Documentation of current and target coding patterns

   - `01-current-patterns.md`: Current implementation patterns

   - `02-component-patterns.md`: Component implementation details

   - `03-target-patterns.md`: Target implementation patterns



4. **04-visual-systems/**: Documentation of visual systems

   - `01-screenshot-infrastructure.md`: Infrastructure for capturing screenshots

   - `02-design-guidelines.md`: Design guidelines

   - `03-styling-approach.md`: Approach to styling



5. **05-development-workflow/**: Documentation of development processes and requirements

   - `01-environment-setup.md`: Setting up the development environment

   - `02-optimization-guide.md`: Performance optimization strategies

   - `03-accessibility-guide.md`: Accessibility best practices



## Key Systems



The website is built around several integrated systems:



1. **Website Application**: A React-based frontend application utilizing TypeScript, Vite, Tailwind CSS, and React Router to create a fast, responsive, and user-friendly website.



2. **Visual Documentation System**: A system for capturing and organizing screenshots of the website to provide visual reference for development and track visual changes over time.



3. **AI Development Tools**: A set of documentation and tools that help AI assistants understand and work with the codebase more effectively.



4. **Content Management**: A system for managing website content through structured data files.



## Using This Documentation



### For Developers



1. Start by reading the `rl-website-initial-notes.md` file to understand the project's background, company information, and detailed requirements.

2. Next, review the files in the `01-project-state/` folder to understand the current state of the project.

3. Use the `02-codebase-map/` folder to understand the structure and architecture of the codebase.

4. Refer to the `03-implementation-patterns/` folder for guidance on how to implement features.

5. Check the `04-visual-systems/` folder for design guidelines and styling approaches.

6. Follow the guidance in the `05-development-workflow/` folder for development processes.



### For AI Assistants



AI assistants should start with the `AI-README.md` file, which provides comprehensive context about the project, followed by `rl-website-initial-notes.md` for the foundational project brief. The documentation structure provides a clear path for understanding the project and its requirements.



## Screenshots



The website screenshots are available in three locations:



1. **Historical Screenshots**: Located in timestamped directories under `/screenshots/`

2. **Latest Screenshots**: Always available at `/screenshots/latest/` for the most current view

3. **AI Analysis Screenshots**: Available at `/.ai-analysis/latest/`



To capture new screenshots, use one of the following commands:



```bash

# Capture screenshots manually

npm run screenshots:capture



# Start development server with automatic screenshot capturing

npm run dev:ai

```



## Contributing to Documentation



When contributing to this documentation, please follow these guidelines:



1. Use clear, concise language

2. Include code examples where appropriate

3. Keep the documentation up-to-date with changes to the codebase

4. Follow the existing structure and naming conventions```





#### `rl-website-initial-notes.md`



```markdown

# Ringerike Landskap



---



## Company Overview



Ringerike Landskap AS is a relatively young but growing landscaping and machine-contracting company based in Hole, Buskerud. With extensive experience in the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik), they deeply understand the terrain’s unique demands, crafting outdoor areas that skillfully blend functionality, resilience, and authentic personal style. From Røyse to Hønefoss, their detailed knowledge of local conditions ensures robust foundations, effective drainage, and frost-proof solutions, whether they're building elegant stone patios (belegningsstein), durable retaining walls, vibrant gardens, or practical driveways.



What truly sets Ringerike Landskap apart are the two young, wise, and dedicated owners, who take genuine pride in their work and personally invest in each customer relationship. Known for their exceptional interpersonal skills and unwavering commitment, they consistently exceed expectations, leaving clients pleasantly surprised by the care, creativity, and precision of their craftsmanship. The owners’ diverse expertise—including professional welding and specialized use of corten steel—allows them to deliver innovative, personalized solutions that beautifully reflect client preferences and harmonize with the Norwegian climate.



---



## Team



The company consists of two owners who are young and wise, and they take actual **pride** in their work. They make it a point to themselves to **invest** in every customer they meet. Both have multiple disciplines, including one being a professional welder, making corten-steel something they intertwine in their work.



They are creative and flexible while covering a wide and flexible skillset. Whether it's crafting welcoming front yards or designing sophisticated commercial landscapes, the team ensures each solution reflects the client's preferences while honoring the Norwegian climate. They are known for shaping outdoor spaces that stand out in the local environment.



---



## Customer Approach



Clients consistently appreciate the genuine personal connection and meticulous attention provided by Ringerike Landskap. Each project begins with transparent, thoughtful dialogue where ideas, preferences, and creative possibilities are openly explored. The owners' hands-on involvement and sincere investment in every customer ensure that projects not only meet but exceed expectations, creating lasting satisfaction and often delightful surprises.



Their versatile portfolio, ranging from intimate backyard enhancements to expansive commercial landscapes, demonstrates a proven flexibility and deep commitment to quality at any scale. Customers within a 20–50 km radius can feel confident, from initial consultation through final implementation, knowing their outdoor spaces will uniquely reflect their personal vision and practical requirements.



---



## Services



The company focuses on eight core services:



1. **Kantstein** (curbstones)

2. **Ferdigplen** (ready lawn)

3. **Støttemur** (retaining walls)

4. **Hekk/beplantning** (hedges and planting)

5. **Cortenstål** (steel installations)

6. **Belegningsstein** (paving stones)

7. **Platting** (decking)

8. **Trapp/repo** (stairs and landings)



Every project showcases meticulous attention to detail—from selecting high-quality materials to matching each space with the best design strategies. By emphasizing local conditions and seasonal changes, Ringerike Landskap consistently delivers outdoor solutions that stand the test of time.



---



## Digital Presence



Ringerike Landskap AS is a forward-thinking company, actively leveraging their digital presence within a market where few landscaping companies in Norway have fully embraced technology. Recognizing this opportunity, they are currently finalizing an engaging and modern website aimed at expanding their customer reach and clearly demonstrating their unique expertise.



Ringerike Landskap's new website will serve as more than just a digital portfolio—it will be an authentic representation of how two dedicated owners personally invest in delivering exceptional landscaping solutions throughout the Ringerike region. By emphasizing their customer-centered approach, creative versatility, and consistent ability to exceed expectations, the site will effectively communicate why local residents and businesses continue to entrust their outdoor spaces to this distinctive landscaping company. The website's strategic focus on the owners' personal involvement and commitment to customer satisfaction positions Ringerike Landskap as the natural choice for anyone seeking truly personalized landscaping expertise in Hole, Hønefoss, Jevnaker, Sundvollen, and Vik.



The website is in development as a dynamic digital showcase, spotlighting Ringerike Landskap’s specialized landscaping services for both private homes and commercial properties throughout the Ringerike region. Central to its appeal are the company’s two young owners' personalized approach, exceptional craftsmanship, and genuine commitment to customer satisfaction.



---



## Website Purpose



The new company website is designed to significantly expand their reach within the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik, and surrounding areas) by *leveraging* advanced and unique search engine optimization (SEO) techniques. More than just an online presence, the site will function as a dynamic platform, strategically built to connect with homeowners, businesses, and property developers actively searching for local landscaping services.



The website's ultimate purpose is to solidify Ringerike Landskap's position as *the* leading landscaping experts in the Ringerike region. It achieves this not only by presenting their portfolio but also by actively engaging their target audience through a customer-centric online experience and a powerful, locally-focused SEO strategy.



---



## SEO



The site’s content strategy focuses on delivering fresh, search-engine-friendly information and visually compelling examples—ranging from robust stone patios to custom corten-steel features—that resonate with homeowners, commercial property managers, and anyone seeking unique outdoor transformations. This SEO-centered approach underscores the owners’ commitment to exceptional craftsmanship, effective project management, and transparent communication, distinguishing Ringerike Landskap as the go-to resource for innovative landscape solutions in Buskerud.



Target Groups:

    - Primary: Homeowners, businesses, property developers, and local residents in Ringerike and surrounding areas seeking professional landscaping services.

    - Primary: Prospective customers looking for tailored landscaping solutions or inspiration for outdoor spaces.

    - Secondary: Users researching local landscaping solutions online or comparing contractors.

    - Secondary: Existing customers revisiting the site for additional services or to share testimonials.

    - Secondary: Existing customers reviewing projects or contacting the company.



Core Strategies:

    - Hyperlocal SEO: The website will be meticulously optimized for location-specific keywords, including variations and long-tail phrases (e.g., "anleggsgartner Røyse" "pris på støttemur Jevnaker," "cortenstål i hage Ringerike"). This ensures high visibility in search results when potential customers within their service area are seeking relevant services.

    - Technical SEO Foundation: The website's architecture is built for speed, mobile-friendliness, and optimal crawlability by search engines. Optimized images, clean code, structured data markup (Schema.org), and a clear sitemap ensure the site's content is easily

    - Optimized UI/UX: User-friendly navigation and detailed service breakdowns—covering everything from welcoming front yards to sophisticated commercial landscapes—to inspiring project galleries categorized by season and location, the site is strategically structured to enhance engagement.

    - Showcasing professionalism: Transparent communication, a genuine commitment to customer satisfaction, and thoughtful seasonal insights, the platform guides visitors effortlessly from inspiration to consultation, positioning Ringerike Landskap as the trusted local authority in outdoor design and landscape craftsmanship.

    - Authenticity: The website will authentically portray the two dedicated owners, their personal investment in each project, and their commitment to exceeding client expectations. This "people-first" approach, reinforced through testimonials, case studies, and behind-the-scenes content, builds trust and resonates with local customers, differentiating Ringerike Landskap from larger competitors.

    - Clear calls-to-action ("Book Gratis Befaring," "Få et Pristilbud"), user-friendly contact forms, and a streamlined user experience encourage inquiries and consultations.



User Interface:

    - Visual Style: Clean design with high-quality imagery showcasing landscaping work across prioritized services; green accents reflect nature and sustainability.

    - Navigation: Clear menu structure.

    - Call-to-Actions: Prominent buttons like “Book Gratis Befaring” should stand out visually to encourage conversions.

    - Project Showcase: Grid-style layout with filters for easy exploration of completed works categorized by job type or location.

    - Mobile-Friendly Features: Responsive layouts ensuring usability across all devices.



Responsive Practices:

    - Mobile-First Approach: Start with mobile layout and enhance for larger screens

    - Fluid Typography: Scale text based on screen size

    - Flexible Images: Ensure images adapt to their containers

    - Touch-Friendly UI: Larger touch targets on mobile

    - Performance Optimization: Lazy loading and optimized assets

    - Testing: Test on multiple devices and screen sizes

    - Accessibility: Ensure accessibility across all screen sizes



User Experience:

    - Browsing Services: Visitors land on the homepage or “Hva vi gjør” page to learn about the firm’s core landscaping solutions.

    - Exploring Projects: They can explore real examples under “Prosjekter,” filtering results by interest—like building a new terrace or installing a støttemur.

    - Seasonal Tips: The site subtly adapts to reflect current or upcoming seasons, guiding users to relevant services (e.g., planting ideas in spring).

    - Users filter projects by category or location to find relevant examples.

    - Testimonials provide social proof and build trust throughout the decision-making process.

    - Requesting a Quote: When ready, they fill out the contact form or tap a “Book gratis befaring” button to schedule an on-site evaluation.

    - Personal Follow-Up: The Ringerike Landskap team personally contacts clients to discuss specific needs, ensuring straightforward, no-frills communication.



Optimizations:

    - Meta Tags: Dynamic meta tags for each page

    - Structured Data: Schema.org markup for rich results

    - Semantic HTML: Proper heading structure

    - Canonical URLs: Proper URL structure



Accessibility:

    - Semantic HTML: Proper use of HTML elements

    - ARIA Attributes: For enhanced accessibility

    - Screen Reader Support: Proper labels and descriptions

    - Keyboard Navigation: All interactive elements are keyboard accessible



---



## Website Requirements



The website should be fully responsive, providing an optimal viewing experience across a wide range of devices, from mobile phones to desktop monitors. This is achieved through a combination of Tailwind CSS utility classes, custom responsive components, and media queries.



Component Architecture:

    - Well-organized component structure following a feature-based organization

    - Reusable UI components in the `ui` directory

    - Page-specific components in feature directories

    - Layout components for consistent page structure



State Management:

    - Uses React hooks for component-level state management

    - Custom hooks for reusable logic (e.g., `useMediaQuery`, `useScrollPosition`)

    - Local storage integration for persisting user preferences



Data Structure:

    - Static data files for services and projects

    - Well-defined TypeScript interfaces for type safety

    - Structured content with rich metadata



Architecture:

    - Clear separation of concerns between UI, data, and business logic

    - Component composition patterns for flexible UI building

    - Abstraction layers that separate presentation from data



Design Philosophy:

    - Focus on accessibility with proper semantic HTML and ARIA attributes

    - Performance optimization with code splitting and asset optimization

    - SEO-friendly structure with metadata and schema.org markup



Code Organization Principles:

    - High cohesion: Related functionality is grouped together

    - Low coupling: Components are independent and reusable

    - Consistent naming conventions and file structure



Seasonal Adaptation

    - Content changes based on current season

    - Affects projects, services, and UI elements

    - Automatic detection and mapping



Component Hierarchy

    - UI Components → Feature Components → Page Components

    - Composition over inheritance

    - Reusable building blocks



Data Flow

    - Static data in `/data`

    - Props down, events up

    - Context for global state

    - Custom hooks for logic



Responsive Design

    - Mobile-first approach

    - Tailwind breakpoints

    - Fluid typography

    - Adaptive layouts



Type Safety

    - TypeScript throughout

    - Strict type checking

    - Interface-driven development



Navigation Structure:

    - Home (/)

        - Hero section

        - Seasonal projects carousel

        - Service areas list

        - Seasonal services section

        - Testimonials section



    - Services (/hva-vi-gjor) # todo:change to `/hva`

        - Hero section

        - Service filtering (Category, Function, Season)

        - Service listings with details

        - Benefits section (Lokalkunnskap, Tilpassede løsninger, Klimatilpasset)

        - CTA section



    - Projects (/prosjekter)

        - Hero section

        - Project filtering (Category, Location, Tag, Season)

        - Project grid with details

        - Seasonal recommendations



    - About Us (/hvem-er-vi) # todo:change to `/hvem`

        - Hero section

        - Company information

        - Team members

        - Core values and benefits



    - Contact (/kontakt)

        - Hero section

        - Contact form

        - Location information

        - Service areas



Dynamic Routes:

    - Service Detail (/tjenester/:id)

        - Service description

        - Features list

        - Related projects

        - Image gallery

        - Contact CTA



    - Project Detail (/prosjekter/:id)

        - Project description

        - Project specifications

        - Materials and features

        - Related service

        - Testimonial (if available)



Additional Pages:

    - Testimonials (/kundehistorier)

        - Testimonial filtering

        - Testimonial grid

        - Testimonial categories

        - CTA section



Service Areas:

    - Røyse (Main Base)

    - Hønefoss

    - Hole kommune

    - Jevnaker

    - Sundvollen

    - Vik



Seasonal Content Adaptation:

    - Spring (Vår)

        - Focus on: Hekk og Beplantning, Ferdigplen

        - Relevant tags: beplantning, plen, hage



    - Summer (Sommer)

        - Focus on: Platting, Cortenstål, Belegningsstein

        - Relevant tags: terrasse, uteplass, innkjørsel



    - Fall (Høst)

        - Focus on: Støttemurer, Kantstein, Trapper og Repoer

        - Relevant tags: terrengforming, støttemur, trapp



    - Winter (Vinter)

        - Focus on: Planning and Design

        - Relevant tags: planlegging, design, prosjektering



Service Categories:

    - Belegningsstein

    - Cortenstål

    - Støttemurer

    - Platting

    - Ferdigplen

    - Kantstein

    - Trapper og Repoer

    - Hekk og Beplantning

```


## Message 5

**Synthesizing concepts**

Piecing together the folder's contents to streamline references, covering architecture, structure, SEO, database access, and advanced steps like screenshot integration.

**Balancing compliance**

Integrating color guidelines, accessibility, and performance insights from various documents, while unifying everything under one .cursorrules. Ensuring a comprehensive and harmonious approach.

**Piecing together patterns**

I’m gathering insights from "requests" and "component patterns" to create a comprehensive JSON document for the .cursorrules file, incorporating rules on route naming and the AI screenshot system.

**Crafting the rules**

I’m focusing on explicit references to documentation disclaimers, strict adherence to route naming conventions, and preserving directory integrity. The goal is a comprehensive and precise .cursorrules file.

## Message 6

Below is an **expanded** example of a **`.cursorrules`** file designed specifically for the Ringerike Landskap website project. This version integrates **all** key points and requirements from your documentation—including architecture, naming conventions, SEO, accessibility, screenshot infrastructure, seasonal adaptation, and everything outlined in `rl-website-initial-notes.md` and the rest of the `/docs` directory. 

It aims to **keep developers (and AI assistants) aligned** with the desired code organization, brand voice, SEO strategies, performance guidelines, and the unique features—like **hyperlocal SEO**, **seasonal content adaptation**, and the **crucial screenshot system**—as they write or refactor code.

Feel free to **tailor** any rule to suit your codebase or adjust `level` severity (`info`, `warning`, `error`) based on how strict you need each enforcement to be.

---

```jsonc
{
  "$schema": "https://raw.githubusercontent.com/getcursor/cursor/main/docs/cursorrules.schema.json",

  "rules": {
    /* ---------------------------------------------------------
       1. PROJECT & BRANDING CONSISTENCY
       Ensures that code changes uphold the brand’s guidelines 
       and the high-level architecture from your docs.
    ---------------------------------------------------------- */
    "brand/structure-and-docs-reference": {
      "description": "Remind developers to respect the architecture and docs guidelines. The code must stay consistent with the strategic pillars in `rl-website-initial-notes.md` and the entire `/docs` directory.",
      "level": "info",
      "applyPatterns": [
        {
          "pattern": "**/*",
          "checks": [
            {
              "type": "presenceComment",
              "value": "Ringerike Landskap references",
              "message": "Code changes or doc changes must preserve references to Ringerike Landskap brand or incorporate them where relevant."
            },
            {
              "type": "presenceComment",
              "value": "docs structure",
              "message": "Remember the structure in `/docs`: everything from architecture to dev workflow is crucial. Keep doc references in sync."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       2. ROUTING & NAMING
       Conforms to Norwegian route names and consistent paths 
       (per `rl-website-initial-notes.md`).
    ---------------------------------------------------------- */
    "routing/norwegian-paths": {
      "description": "Use correct Norwegian route naming from the docs and avoid English routes. Must preserve the custom path changes: /hvem, /hva, /prosjekter, /kontakt, etc.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "src/pages/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "(about|services|projects|contact)",
              "inverse": true,
              "message": "Routes must be Norwegian: /hvem-er-vi → /hvem, /hva-vi-gjor → /hva, /prosjekter, /kontakt, etc."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       3. ARCHITECTURE & DIRECTORY STRUCTURE
       Reflect the "feature-based" and "UI → Feature → Page" 
       organization from your codebase maps in `/docs/02-codebase-map/`.
    ---------------------------------------------------------- */
    "architecture/feature-based": {
      "description": "Encourage the documented feature-based structure (e.g. `ui/`, `features/`, `pages/`, etc.) from `/docs/02-codebase-map/`.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "directoryStructure",
              "value": {
                "ui": "Re-usable UI components",
                "features": "Feature-specific logic and sub-components",
                "pages": "Page-level components"
              },
              "message": "Components must follow the recommended pattern from the architecture docs."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       4. SERVICES & SEASONAL ADAPTATION 
       Preserve references to the 8 core services and 
       correct usage of seasonal adaptation across the code.
    ---------------------------------------------------------- */
    "features/services-and-seasonality": {
      "description": "Ensure the 8 core services and seasonal adaptation logic from `rl-website-initial-notes.md` remain intact. The site must highlight these properly.",
      "level": "info",
      "applyPatterns": [
        {
          "pattern": "**/*.{ts,tsx,md,mdx,json}",
          "checks": [
            {
              "type": "regex",
              "value": "(Kantstein|Ferdigplen|Støttemur|Hekk/beplantning|Cortenstål|Belegningsstein|Platting|Trapp/repo)",
              "message": "Must reference the 8 core services properly."
            },
            {
              "type": "regex",
              "value": "(Vår|Sommer|Høst|Vinter)",
              "message": "Ensure correct seasonal references: Vår, Sommer, Høst, Vinter (Norwegian)."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       5. STATE MANAGEMENT & TYPING
       From `/docs/03-implementation-patterns/`, require 
       consistent TypeScript usage, minimal `any`, and 
       encourage custom hooks/Context for shared state.
    ---------------------------------------------------------- */
    "typescript/no-any-and-consistent-types": {
      "description": "Prevent `any` usage and enforce strong typing. Must maintain or improve typed data structures.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "any",
              "message": "Avoid using `any`; use a precise interface or generic param instead."
            }
          ]
        }
      ]
    },
    "react/use-context-or-hooks": {
      "description": "Encourage a consistent state mgmt approach from `03-target-patterns.md` (React Context + custom hooks).",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "presenceComment",
              "value": "useContext or custom hook usage",
              "message": "Where relevant, prefer using a custom hook or context if data is shared across multiple nested components."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       6. SEO & META STRUCTURE
       Per `rl-website-initial-notes.md` and 
       `/docs/02-codebase-map/02-architecture-overview.md`.
       Must maintain hyperlocal SEO, structured data, 
       correct meta tags, etc.
    ---------------------------------------------------------- */
    "seo/meta-and-schema": {
      "description": "All pages should define meta tags, location-based keywords, structured data (Schema.org), etc. per the SEO section in the docs.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/pages/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "presence",
              "value": "Helmet",
              "message": "Use React Helmet (or Next.js Head) for meta tags referencing local SEO keywords (Røyse, Hønefoss, Jevnaker...)."
            },
            {
              "type": "presenceComment",
              "value": "Schema.org",
              "message": "Include relevant Schema.org data for local business or specific services (e.g., `LocalBusiness`, `Product`, etc.)."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       7. ACCESSIBILITY & SEMANTIC HTML
       From `/docs/05-development-workflow/03-accessibility-guide.md`.
       Must keep ARIA usage, skip links, alt text, etc.
    ---------------------------------------------------------- */
    "a11y/enforce-semantic-and-aria": {
      "description": "All interactive elements require correct ARIA. Maintain a logical heading hierarchy. Provide alt text for images.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx,html}",
          "checks": [
            {
              "type": "regex",
              "value": "<img[^>]+alt=\"\"",
              "message": "Provide meaningful alt text for images (or mark as role=presentation if truly decorative)."
            },
            {
              "type": "regex",
              "value": "<div[^>]+onClick=",
              "message": "Avoid divs with onClick. Use <button> or role='button' + ARIA if absolutely required."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       8. SCREENSHOT INFRASTRUCTURE PROTECTION
       The screenshot system is critical (see `/docs/04-visual-systems/01-screenshot-infrastructure.md`).
       Do NOT break or delete `.ai-analysis/` or `screenshots/` directories.
    ---------------------------------------------------------- */
    "infrastructure/screenshot-integrity": {
      "description": "Preserve the screenshot system. Changing or removing `.ai-analysis/` or `screenshots/` directories is prohibited.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "**/*",
          "checks": [
            {
              "type": "fileDeletion",
              "value": ".ai-analysis",
              "message": "Modifying or deleting `.ai-analysis/` is disallowed. This folder is critical for AI-based dev workflow."
            },
            {
              "type": "fileDeletion",
              "value": "screenshots",
              "message": "Modifying or deleting `screenshots/` is disallowed. This folder is integral to the visual doc system."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       9. PERFORMANCE & OPTIMIZATION
       Must reflect best practices from `/docs/05-development-workflow/02-optimization-guide.md`.
       Keep lazy loading, code splitting, etc.
    ---------------------------------------------------------- */
    "performance/lazy-loading-and-code-splitting": {
      "description": "Check for usage of lazy loading, code splitting, and other performance best practices from the docs.",
      "level": "info",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "presenceComment",
              "value": "React.lazy",
              "message": "Consider lazy loading routes or large components to improve performance (code splitting)."
            },
            {
              "type": "presenceComment",
              "value": "suspense fallback",
              "message": "When using React.lazy, ensure <Suspense fallback={...}> is implemented for the user experience."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       10. CONTENT & COPYWRITING 
       Must preserve the "people-first," personal investment tone, 
       and mention the 2 owners' dedication, as outlined in the docs.
    ---------------------------------------------------------- */
    "content/voice-and-tone": {
      "description": "Maintain the brand voice emphasizing personal investment, authenticity, local knowledge. The code or content must reflect that tone.",
      "level": "info",
      "applyPatterns": [
        {
          "pattern": "src/content/**/*.{md,mdx,ts,tsx,json}",
          "checks": [
            {
              "type": "regex",
              "value": "(egeninvestering|personlig|fornøyde kunder|lokalkunnskap|stolthet|personlig investering|to eiere|unge og kloke)",
              "inverse": false,
              "message": "Include references to personal involvement, local expertise, or the owners’ authenticity in relevant content."
            }
          ]
        }
      ]
    },

    /* ---------------------------------------------------------
       11. TESTIMONIALS & CTA PLACEMENT
       Each relevant service or project page should have 
       a CTA like "Book Gratis Befaring" or "Kontakt oss."
    ---------------------------------------------------------- */
    "ui/testimonials-and-cta": {
      "description": "Ensure that each services or projects page includes at least one testimonial or CTA for an on-site evaluation. This fosters user conversion.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/pages/(hva|prosjekter)/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "presence",
              "value": "BookGratisBefaring",
              "message": "Must have a CTA that references booking a free on-site consultation on service or project pages."
            },
            {
              "type": "presence",
              "value": "TestimonialSection",
              "message": "Include a testimonial or social-proof snippet per the brand’s focus on authenticity."
            }
          ]
        }
      ]
    }
  }
}
```

---

### How to Use & Customize

1. **Adjust Rule Severity**  
   Each rule has a `level` field that can be `info`, `warning`, or `error`. Use `error` only for absolute musts (like the screenshot infrastructure). Use `warning` where you want strong nudges. `info` is more of a gentle reminder.

2. **Refine File Patterns**  
   The `"pattern"` fields use glob-like expressions. Update them to match your real file structure. You might, for instance, want certain rules to target only `src/pages/**` or `docs/**`.

3. **Check Types**  
   There are different check `type`s used above: `regex`, `presence`, `presenceComment`, `fileDeletion`, etc. Adjust them to the enforcement style you prefer. (See the Cursor `.cursorrules` documentation for details on each.)

4. **Combine with ESLint & Prettier**  
   `.cursorrules` is meant to **complement** rather than duplicate your linting or formatting rules. Keep style and syntax checks in ESLint/Prettier. Use `.cursorrules` for **project-specific** or architectural rules that help preserve your unique structure and brand guidelines.

5. **Iterate & Expand**  
   As the codebase evolves, keep `.cursorrules` up to date—especially if you add new features (e.g., advanced forms, separate e-commerce flows, etc.) or discover new patterns that should be enforced.

This approach gives you a **one-stop** `.cursorrules` that **reinforces** everything from your `@rl-website-initial-notes.md` file and the broader `/docs` directory, making sure everyone (human or AI) follows the same **inherent understanding** and **persistent guidelines** throughout development.

## Message 7

what are the absolute best practices for `.cursorrules` file in general? e.g. what is the most optimal way to write it, what syntax, how it should be structured, etc?


## Message 8

Below is a **high-level guide** of **best practices** and **recommended patterns** for writing a **`.cursorrules`** file. Since `.cursorrules` is designed to help developers (and AI assistants) follow **project-specific** coding, organizational, and architectural conventions, these guidelines will ensure that your `.cursorrules` file is **clear**, **maintainable**, and **effective** across your entire team or organization.

---

## 1. **Align With the Official Schema**

- **Reference the Schema**  
  At the top of your `.cursorrules` file, include the official `$schema` link so that tools like Cursor can validate your JSON. Typically:
  ```jsonc
  {
    "$schema": "https://raw.githubusercontent.com/getcursor/cursor/main/docs/cursorrules.schema.json",
    ...
  }
  ```
  This helps ensure auto-completion, linting, and validation work properly in supported editors.

- **Validate Your JSON**  
  Make sure the file is valid JSON. Missing commas or mismatched brackets can break the rules entirely, so use a JSON validator or an IDE that supports JSON schema validation.

---

## 2. **Use a **Clear, Top-Level Structure****

Most `.cursorrules` files follow this top-level structure:

```jsonc
{
  "$schema": "...",
  "rules": {
    "ruleOneID": {
      "description": "...",
      "level": "...",
      "applyPatterns": [
        {
          "pattern": "...",
          "checks": [...]
        },
        ...
      ]
    },
    "ruleTwoID": { ... }
  }
}
```

Key points:

1. **`rules` Object**  
   - Each rule is a key-value pair under `"rules"`.  
   - The key (`"ruleOneID"`) can be any string that **uniquely** identifies the rule—often namespaced to a domain (like `"architecture/"`, `"typescript/"`, `"a11y/"`, etc.).

2. **`description`**  
   - Provide a concise explanation of *why* this rule exists and *what* it enforces.

3. **`level`**  
   - One of: `"error"`, `"warning"`, or `"info"`.  
   - Use `"error"` only for absolute musts (like not deleting critical files).  
   - Use `"warning"` when you want a strong nudge without outright blocking merges/commits.  
   - Use `"info"` for gentle reminders.

4. **`applyPatterns`**  
   - An array that defines where to apply the rule and which checks to run.  
   - Each item has a `pattern` (which is a minimatch-style file glob) and a `checks` array of zero or more checks.

5. **`checks`**  
   - Each item in the `checks` array is an object describing exactly what to check (for instance, a `regex` match, a `fileDeletion` check, a `presence` check, etc.).

---

## 3. **Group & Name Rules Logically**

- **Name-Spacing**  
  A common approach is to name your rules with a prefix that describes the **domain** of the rule. For example:
  - `"typescript/no-any"`  
  - `"architecture/directory-structure"`  
  - `"seo/meta-tags"`  
  - `"ui/button-naming"`  
  - `"docs/presence"`  

- **Thematic Groupings**  
  Group rules by broad categories. For instance:
  1. **TypeScript & Coding Style**  
  2. **Architecture & Directory Structure**  
  3. **Accessibility (A11y)**  
  4. **Security**  
  5. **Performance**  
  6. **Documentation**  

  This makes the file more readable and easier to maintain.

---

## 4. **Keep Rules Atomic & Descriptive**

Each rule should:

1. **Have One Core Purpose**  
   - Keep each rule focused on **one** type of requirement.  
   - This keeps the rule easy to understand, test, and maintain.

2. **Be Well-Described**  
   - The `"description"` field should clarify what is being enforced and *why*.  
   - Example:
     ```jsonc
     "typescript/no-any": {
       "description": "Prevent usage of `any` to ensure strict typing throughout the codebase.",
       ...
     }
     ```

3. **Provide Clear Error Messages**  
   - Under `checks`, each check has a `"message"` field for describing what went wrong or how to fix it.  
   - Keep it succinct but helpful, e.g. `"Use a stricter type than 'any'—try an interface or a generic."`

---

## 5. **Use the Right Check Types**

Cursor supports several check types. Here are some you’ll likely use often:

1. **`regex`**  
   - Checks if a pattern appears in a file’s contents (or the inverse if you set `"inverse": true`).  
   - Useful for scanning code for disallowed APIs, terms, or patterns.

2. **`presence`**  
   - Checks if a certain string or token *must* appear somewhere in the file.  
   - Good for ensuring certain code or library references are included.

3. **`presenceComment`**  
   - Similar to `presence`, but specifically checks for comments that might note a piece of context.  
   - Useful for ensuring important doc references or disclaimers.

4. **`fileDeletion`**  
   - Prevents (or warns about) deletion of certain critical files or directories.

5. **`directoryStructure`**  
   - Enforces a specific folder structure. Good for ensuring projects follow a standard layout.

6. **`extension`**  
   - Restricts allowed file extensions within a certain directory.

Each check type has a set of parameters you can tune, like `value`, `message`, `inverse`, etc.

---

## 6. **Leverage `applyPatterns` Wisely**

- **Match Only Relevant Files**  
  - Use the `pattern` property to target exactly where the rule matters.  
  - For example, a **TypeScript** rule might only target `src/**/*.{ts,tsx}`, while a **docs** rule might target `**/*.md`.

- **Multiple Patterns**  
  - You can have multiple objects in the `applyPatterns` array if you need to run the same checks on different sets of files.  
  - E.g., one for `src/**/*` and another for `tests/**/*`.

- **Be Specific**  
  - The narrower the pattern, the fewer false positives you’ll get.  
  - For example, if you only want to ensure `.md` files mention a particular phrase, use `"**/*.md"` not `"**/*"`.

---

## 7. **Balance Strictness & Flexibility**

- Not all project policies are absolute. Some might need to be "info" or "warning" to let developers override them.  
- If a rule is so broad or strict that it constantly blocks merges, consider using `"warning"` or refining the logic so it only targets truly problematic code.  
- Provide easy instructions on how to override or disable a rule if the situation calls for it (while still preserving team alignment).

---

## 8. **Comment & Document Your Rules**

Just like you’d document code, **document your `.cursorrules`**:

1. **Rule-Level Comments**  
   - Some teams add an inline comment above each rule to give additional rationale or a link to internal docs.

2. **Overall Comments at the Top**  
   - Summarize the big picture, like who authored the rules, how to add or remove rules, or how to test them locally.

3. **Maintenance Notes**  
   - If certain rules rely on external systems (e.g., a container security scanning tool), specify that so future maintainers understand dependencies.

---

## 9. **Keep `.cursorrules` in Version Control**

- Commit your `.cursorrules` file to the same repository.  
- This ensures everyone on the team (and all CI pipelines) uses the exact same rule set.  
- If you want to define rules across multiple projects, consider an internal package or a shared config that can be imported—similar to how you might share an ESLint config across different repos.

---

## 10. **Iterate as Your Project Evolves**

- `.cursorrules` is **not** set in stone. It should grow and adapt as your codebase and processes change.  
- You can create a simple process: for example, require a quick team review if someone wants to add or remove a rule, just like you’d do for an ESLint rule.
- Regularly **review** rules to ensure they remain relevant. If a rule is producing frequent false positives, refine it.

---

## 11. **Complement, Don’t Duplicate ESLint/Prettier**

- `.cursorrules` typically enforces **project-specific** or **architectural** constraints that ESLint can’t handle out-of-the-box (e.g., “Never delete `/.ai-analysis` folder” or “Always reference ‘people-first approach’ in marketing copy”).  
- Keep typical code style, syntax, and formatting checks in ESLint/Prettier.  
- Use `.cursorrules` for **higher-level** guidelines or advanced checks you can’t easily do with other tools.

---

## 12. **Examples & Template**

A minimal, best-practice `.cursorrules` might look like this:

```jsonc
{
  "$schema": "https://raw.githubusercontent.com/getcursor/cursor/main/docs/cursorrules.schema.json",

  // Top-level explanation:
  // This file enforces project-specific guidelines for ABC Company’s web app.

  "rules": {
    "typescript/no-any-usage": {
      "description": "Prohibits `any` type usage to maintain strict typing standards.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "any",
              "message": "Avoid using `any`. Use a stricter or generic type instead."
            }
          ]
        }
      ]
    },

    "security/prevent-secrets-in-repo": {
      "description": "Warn if a likely secret token or password is committed.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "**/*.env",
          "checks": [
            {
              "type": "regex",
              "value": "(password|secret|api_key|token)",
              "message": "Potential secret detected. Remove or mask secrets from the codebase."
            }
          ]
        }
      ]
    },

    "docs/readme-presence": {
      "description": "Require a README.md in each feature directory to detail usage.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/features/**/*",
          "checks": [
            {
              "type": "presence",
              "value": "README.md",
              "message": "Each feature directory must have a README.md to explain its components."
            }
          ]
        }
      ]
    }
  }
}
```

---

## **In Summary**

1. **Reference the official schema** to benefit from validation and tooling.  
2. **Organize your rules** by domain, name them consistently, and keep them **atomic**.  
3. **Choose suitable check types** (`regex`, `presence`, `fileDeletion`, etc.) and apply them to the right file patterns.  
4. **Use helpful error or warning messages** that guide developers on how to fix or comply.  
5. **Complement** (rather than overlap with) ESLint/Prettier.  
6. **Version-control** your `.cursorrules`, and **iteratively refine** it as your project matures.

Following these **best practices** will keep your `.cursorrules` file structured, maintainable, and robust—ensuring it plays a pivotal role in aligning code quality, architecture, and project conventions across your entire team.

## Message 9

with that in mind, what would be the optimal `.cursorrules` for this particular project? can you please show the best possible version of the optimal .cursorrules?

here's my old attempt:

{

  "$schema": "https://raw.githubusercontent.com/getcursor/cursor/main/docs/cursorrules.schema.json",

  "projectInfo": {

    "name": "Ringerike Landskap Website",

    "description": "A modern, responsive website for a Norwegian landscaping company based in Hole, Buskerud.",

    "documentation": "./docs/development-guidelines.md"

  },



  "rules": {

    /* ---------------------------------------------------------

       1. NORWEGIAN LANGUAGE & CONTENT

       Ensures Norwegian language content and regional focus.

    ---------------------------------------------------------- */

    "content/norwegian-language": {

      "description": "Maintain Norwegian language and local regional focus in content.",

      "level": "info",

      "applyPatterns": [

        {

          "pattern": "src/data/**/*.{ts,tsx,json}",

          "checks": [

            {

              "type": "regex",

              "value": "(Røyse|Hønefoss|Hole|Ringerike|Jevnaker|Sundvollen|Vik)",

              "message": "Ensure content references the Ringerike region service areas."

            },

            {

              "type": "regex",

              "value": "(Kantstein|Ferdigplen|Støttemur|Hekk\\/beplantning|Cortenstål|Belegningsstein|Platting|Trapp\\/repo)",

              "message": "Maintain references to core services in Norwegian."

            }

          ]

        }

      ]

    },



    /* ---------------------------------------------------------

       2. COMPONENT STRUCTURE

       Enforces feature-based organization.

    ---------------------------------------------------------- */

    "architecture/feature-based": {

      "description": "Follow feature-based directory structure and component hierarchy.",

      "level": "warning",

      "applyPatterns": [

        {

          "pattern": "src/**/*.{ts,tsx}",

          "checks": [

            {

              "type": "directoryStructure",

              "value": {

                "components": "Reusable UI components",

                "features": "Feature-specific components",

                "pages": "Page components",

                "hooks": "Custom React hooks",

                "utils": "Utility functions",

                "types": "TypeScript type definitions",

                "data": "Static data files" 

              },

              "message": "Maintain the feature-based directory structure as defined in the guidelines."

            }

          ]

        }

      ]

    },



    /* ---------------------------------------------------------

       3. SEASONAL ADAPTATION

       Ensures proper seasonal content logic.

    ---------------------------------------------------------- */

    "features/seasonal-adaptation": {

      "description": "Implement seasonal content adaptation based on current date.",

      "level": "warning",

      "applyPatterns": [

        {

          "pattern": "src/**/*.{ts,tsx}",

          "checks": [

            {

              "type": "regex",

              "value": "(getCurrentSeason|seasonalContent|isSeason\\(|isSpring|isSummer|isFall|isWinter)",

              "message": "Use the seasonal detection and content adaptation logic for displaying seasonal content."

            }

          ]

        }

      ]

    },



    /* ---------------------------------------------------------

       4. TYPESCRIPT STANDARDS

       Enforces TypeScript best practices.

    ---------------------------------------------------------- */

    "typescript/standards": {

      "description": "Follow TypeScript standards with strict type checking.",

      "level": "error",

      "applyPatterns": [

        {

          "pattern": "src/**/*.{ts,tsx}",

          "checks": [

            {

              "type": "regex",

              "value": ":\\s*any",

              "message": "Avoid using 'any' type; prefer unknown with type guards."

            },

            {

              "type": "regex",

              "value": "(\\/\\*\\*[\\s\\S]*?\\*\\/|\\*\\s@[a-zA-Z]+)",

              "message": "Document public functions and components with JSDoc."

            }

          ]

        }

      ]

    },



    /* ---------------------------------------------------------

       5. NAVIGATION STRUCTURE

       Enforces correct Norwegian route names.

    ---------------------------------------------------------- */

    "routing/norwegian-routes": {

      "description": "Use correct Norwegian route names for navigation.",

      "level": "error",

      "applyPatterns": [

        {

          "pattern": "src/**/*.{ts,tsx}",

          "checks": [

            {

              "type": "regex",

              "value": "route=\"\\/(about|services|projects|contact)",

              "inverse": true,

              "message": "Use Norwegian route names: /hvem, /hva, /prosjekter, /kontakt"

            }

          ]

        }

      ]

    },



    /* ---------------------------------------------------------

       6. RESPONSIVE DESIGN

       Ensures mobile-first approach.

    ---------------------------------------------------------- */

    "ui/responsive-design": {

      "description": "Implement mobile-first, responsive design with Tailwind.",

      "level": "warning",

      "applyPatterns": [

        {

          "pattern": "src/components/**/*.{ts,tsx}",

          "checks": [

            {

              "type": "regex",

              "value": "className=\"[^\"]*sm:[^\"]*md:[^\"]*lg:",

              "message": "Follow mobile-first approach with progressive enhancement using Tailwind breakpoints."

            },

            {

              "type": "regex",

              "value": "width=\"[^\"]+\"\\s+height=\"[^\"]+\"",

              "message": "Set explicit width and height on images to prevent layout shifts."

            }

          ]

        }

      ]

    },



    /* ---------------------------------------------------------

       7. SEO OPTIMIZATION

       Ensures proper SEO implementation.

    ---------------------------------------------------------- */

    "seo/hyperlocal-focus": {

      "description": "Implement hyperlocal SEO targeting the Ringerike region.",

      "level": "warning",

      "applyPatterns": [

        {

          "pattern": "src/pages/**/*.{ts,tsx}",

          "checks": [

            {

              "type": "presence",

              "value": "Head",

              "message": "Implement meta tags using the Head component."

            },

            {

              "type": "regex",

              "value": "itemScope\\s+itemType=\"http://schema.org/",

              "message": "Implement structured data markup using Schema.org."

            }

          ]

        }

      ]

    },



    /* ---------------------------------------------------------

       8. CONTENT STRATEGY

       Emphasizes company's unique selling points.

    ---------------------------------------------------------- */

    "content/unique-selling-points": {

      "description": "Highlight company's unique selling points in content.",

      "level": "info",

      "applyPatterns": [

        {

          "pattern": "src/data/**/*.{ts,tsx,json}",

          "checks": [

            {

              "type": "regex",

              "value": "(personlig|dedikert|engasjert|lokalkunnskap|skreddersydd|kvalitet)",

              "message": "Emphasize the personal touch and dedicated approach of the two owners in content."

            }

          ]

        }

      ]

    },



    /* ---------------------------------------------------------

       9. CORE SERVICES

       Ensures correct representation of core services.

    ---------------------------------------------------------- */

    "services/core-offerings": {

      "description": "Maintain correct representation of the eight core services.",

      "level": "warning",

      "applyPatterns": [

        {

          "pattern": "src/data/services.ts",

          "checks": [

            {

              "type": "presence",

              "value": "Kantstein",

              "message": "Include Kantstein (curbstones) as a core service."

            },

            {

              "type": "presence",

              "value": "Ferdigplen",

              "message": "Include Ferdigplen (ready lawn) as a core service."

            },

            {

              "type": "presence",

              "value": "Støttemur",

              "message": "Include Støttemur (retaining walls) as a core service."

            },

            {

              "type": "presence",

              "value": "Hekk",

              "message": "Include Hekk/beplantning (hedges and planting) as a core service."

            },

            {

              "type": "presence",

              "value": "Cortenstål",

              "message": "Include Cortenstål (steel installations) as a core service."

            },

            {

              "type": "presence",

              "value": "Belegningsstein",

              "message": "Include Belegningsstein (paving stones) as a core service."

            },

            {

              "type": "presence",

              "value": "Platting",

              "message": "Include Platting (decking) as a core service."

            },

            {

              "type": "presence",

              "value": "Trapp",

              "message": "Include Trapp/repo (stairs and landings) as a core service."

            }

          ]

        }

      ]

    },



    /* ---------------------------------------------------------

       10. SCREENSHOT INFRASTRUCTURE

       Protects critical infrastructure.

    ---------------------------------------------------------- */

    "infrastructure/screenshot-system": {

      "description": "Protect the screenshot infrastructure essential for development.",

      "level": "error",

      "applyPatterns": [

        {

          "pattern": "**/*",

          "checks": [

            {

              "type": "fileDeletion",

              "value": ".ai-analysis",

              "message": "The .ai-analysis directory contains critical screenshot data and should not be deleted."

            },

            {

              "type": "fileDeletion",

              "value": "screenshots",

              "message": "The screenshots directory contains important visual documentation and should not be deleted."

            }

          ]

        }

      ]

    }

  }

}

## Message 10

**Refining the rules**

I am working on refining the `.cursorrules` file by incorporating best practices such as consistent naming conventions, adding top-level comments, and including presence checks. This ensures a robust and efficient setup.

**Renaming and organizing**

I’m mapping out rule renaming for consistency and clarity, such as "language/norwegian-content" and "architecture/feature-based." Future tasks include thematic grouping and integrating a succinct project usage doc.

## Message 11

Below is a **refined, “best-practice”** `.cursorrules` file specifically tailored to the **Ringerike Landskap** website. It follows the **optimal structure**, **naming**, and **rule-organization** guidelines we discussed. You can adapt any parts or severities (`"error"`, `"warning"`, `"info"`) to fit your exact needs.

```jsonc
{
  // Schema reference ensures editor/tooling validation & autocompletion
  "$schema": "https://raw.githubusercontent.com/getcursor/cursor/main/docs/cursorrules.schema.json",

  // Optional: Provide top-level metadata about the project
  "projectInfo": {
    "name": "Ringerike Landskap Website",
    "description": "A modern, responsive website for a Norwegian landscaping company in Hole, Buskerud."
  },

  "rules": {
    /* 
    ======================================================================
      1. LANGUAGE & REGIONAL FOCUS
      Enforce Norwegian language usage & references to local service areas.
    ====================================================================== 
    */
    "content/norwegian-language-regional-focus": {
      "description": "Ensure consistent references to the Ringerike region and services in Norwegian.",
      "level": "info",
      "applyPatterns": [
        {
          "pattern": "src/data/**/*.{ts,tsx,json,md,mdx}",
          "checks": [
            {
              "type": "regex",
              "value": "(Røyse|Hønefoss|Hole|Ringerike|Jevnaker|Sundvollen|Vik)",
              "message": "Verify content references the local Ringerike region areas where relevant."
            },
            {
              "type": "regex",
              "value": "(Kantstein|Ferdigplen|Støttemur|Hekk\\/beplantning|Cortenstål|Belegningsstein|Platting|Trapp\\/repo)",
              "message": "Ensure Norwegian naming of the 8 core services is used where appropriate."
            }
          ]
        }
      ]
    },

    /* 
    ======================================================================
      2. ARCHITECTURE & DIRECTORY STRUCTURE
      Adhere to the feature-based organization from docs (pages, features, etc.).
    ====================================================================== 
    */
    "architecture/feature-based-structure": {
      "description": "Follow the recommended feature-based directory layout and naming in `/docs/02-codebase-map/`.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "directoryStructure",
              "value": {
                "components": "Reusable UI components",
                "features": "Feature-specific logic",
                "pages": "Page components",
                "hooks": "Custom React hooks",
                "utils": "Utility functions",
                "types": "TypeScript type definitions",
                "data": "Static data or content definitions"
              },
              "message": "Maintain the feature-based directory structure as defined in project docs."
            }
          ]
        }
      ]
    },

    /* 
    ======================================================================
      3. SEASONAL ADAPTATION
      Mandate presence of seasonal content logic from `rl-website-initial-notes.md`.
    ====================================================================== 
    */
    "features/seasonal-adaptation": {
      "description": "Enforce usage of seasonal detection (Vår, Sommer, Høst, Vinter) in relevant code or UI logic.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "(getCurrentSeason|seasonal(Adaptation|Content|Services)|is(Spring|Summer|Fall|Winter)|Vår|Sommer|Høst|Vinter)",
              "message": "Reference or implement the seasonal content logic described in the docs."
            }
          ]
        }
      ]
    },

    /* 
    ======================================================================
      4. TYPESCRIPT STRICTNESS
      Uphold strict type checking; avoid `any` and encourage JSDoc or TSDoc for clarity.
    ====================================================================== 
    */
    "typescript/strict-typing": {
      "description": "Avoid `any` type usage and ensure documentation for exported functions/components.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": ":\\s*any",
              "message": "Avoid using 'any' type—use unknown, generics, or properly typed interfaces."
            },
            {
              "type": "regex",
              "value": "(\\/\\*\\*[\\s\\S]*?\\*\\/|@param|@returns)",
              "message": "Encourage JSDoc/TSDoc comments for public functions, components, and data structures."
            }
          ]
        }
      ]
    },

    /* 
    ======================================================================
      5. ROUTING & NORWEGIAN PATHS
      Enforce correct Norwegian route naming (e.g. /hva, /hvem, /prosjekter, /kontakt).
    ====================================================================== 
    */
    "routing/norwegian-routes": {
      "description": "Use Norwegian route names per project docs; disallow English equivalents.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "src/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "regex",
              "value": "(about|services|projects|contact)",
              "inverse": true,
              "message": "Routes must be /hvem, /hva, /prosjekter, /kontakt, etc.—avoid English route paths."
            }
          ]
        }
      ]
    },

    /* 
    ======================================================================
      6. RESPONSIVE DESIGN
      Encourage mobile-first approach with Tailwind breakpoints, explicit image dims, etc.
    ====================================================================== 
    */
    "ui/responsive-design": {
      "description": "Use Tailwind for a mobile-first, responsive layout. Provide explicit image dimensions to prevent CLS.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/components/**/*.{ts,tsx,js,jsx}",
          "checks": [
            {
              "type": "regex",
              "value": "className=\"[^\"]*(sm:|md:|lg:|xl:)",
              "message": "Include Tailwind’s responsive classes to ensure a mobile-first approach (e.g., sm:, md:, lg:)."
            },
            {
              "type": "regex",
              "value": "img.+(width|height)=",
              "message": "Ensure <img> tags have explicit width/height to prevent layout shift, or use next/image-like strategies."
            }
          ]
        }
      ]
    },

    /* 
    ======================================================================
      7. SEO & HYPERLOCAL
      Ensure hyperlocal SEO references, meta tags, and structured data from the docs.
    ====================================================================== 
    */
    "seo/hyperlocal-implementation": {
      "description": "Mandate local SEO (Ringerike) references, use meta tags, and structured data (Schema.org).",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/pages/**/*.{ts,tsx}",
          "checks": [
            {
              "type": "presence",
              "value": "Helmet", 
              "message": "Use React Helmet (or Next.js Head) for meta & local SEO keywords in page components."
            },
            {
              "type": "regex",
              "value": "itemScope\\s+itemType=\"http://schema.org/",
              "message": "Include Schema.org markup (e.g., LocalBusiness, Product) for better local SEO."
            }
          ]
        }
      ]
    },

    /* 
    ======================================================================
      8. USP & CONTENT EMPHASIS
      Ensure references to owners’ personal investment, local knowledge, etc.
    ====================================================================== 
    */
    "content/owners-dedication-unique-selling-points": {
      "description": "Reinforce references to personal involvement, local knowledge, and high-quality craftsmanship.",
      "level": "info",
      "applyPatterns": [
        {
          "pattern": "src/data/**/*.{ts,tsx,json,md,mdx}",
          "checks": [
            {
              "type": "regex",
              "value": "(personlig|dedikert|stolthet|kvalitet|lokalkunnskap|eiernes engasjement)",
              "message": "Mention the owners’ personal investment, dedication, local knowledge, or high-quality approach."
            }
          ]
        }
      ]
    },

    /* 
    ======================================================================
      9. CORE SERVICES INTEGRITY
      Verify references to the 8 core services remain intact in the `services.ts` data file.
    ====================================================================== 
    */
    "services/core-offerings": {
      "description": "All eight core services (Kantstein, Ferdigplen, Støttemur, Hekk/beplantning, etc.) must be present.",
      "level": "warning",
      "applyPatterns": [
        {
          "pattern": "src/data/services.ts",
          "checks": [
            {
              "type": "presence",
              "value": "Kantstein",
              "message": "Kantstein must exist in the services data."
            },
            {
              "type": "presence",
              "value": "Ferdigplen",
              "message": "Ferdigplen must exist in the services data."
            },
            {
              "type": "presence",
              "value": "Støttemur",
              "message": "Støttemur must exist in the services data."
            },
            {
              "type": "presence",
              "value": "Hekk",
              "message": "Hekk/beplantning must exist in the services data."
            },
            {
              "type": "presence",
              "value": "Cortenstål",
              "message": "Cortenstål must exist in the services data."
            },
            {
              "type": "presence",
              "value": "Belegningsstein",
              "message": "Belegningsstein must exist in the services data."
            },
            {
              "type": "presence",
              "value": "Platting",
              "message": "Platting must exist in the services data."
            },
            {
              "type": "presence",
              "value": "Trapp",
              "message": "Trapp/repo must exist in the services data."
            }
          ]
        }
      ]
    },

    /* 
    ======================================================================
      10. SCREENSHOT INFRASTRUCTURE PROTECTION
      Prevent accidental deletion of `.ai-analysis` or `screenshots` dirs.
    ====================================================================== 
    */
    "infrastructure/screenshot-protection": {
      "description": "Safeguard the screenshot & AI analysis directories crucial for the dev workflow.",
      "level": "error",
      "applyPatterns": [
        {
          "pattern": "**/*",
          "checks": [
            {
              "type": "fileDeletion",
              "value": ".ai-analysis",
              "message": "The `.ai-analysis` folder is critical for AI-based dev; do not remove it."
            },
            {
              "type": "fileDeletion",
              "value": "screenshots",
              "message": "The `screenshots` folder stores visual references; do not remove it."
            }
          ]
        }
      ]
    }
  }
}
```

---

### Explanation of Key Points

1. **Schema Reference**  
   - Ensures that editors using Cursor can validate and auto-complete your `.cursorrules`.

2. **Grouping & Namespacing**  
   - Rules are prefixed with a category like `architecture/`, `services/`, `routing/`, `seo/`, etc. This keeps them organized and easily searchable.

3. **Descriptive Rule IDs & Descriptions**  
   - Each rule focuses on a single theme (e.g., Norwegian routing, screenshot infrastructure, or seasonal adaptation). The `"description"` clarifies *why* the rule exists.

4. **Level of Enforcement**  
   - `error` for absolutely critical (e.g., not deleting essential directories).  
   - `warning` for strong nudges (like ensuring your directory structure or seasonal logic).  
   - `info` for softer reminders (ensuring certain copy or brand references).

5. **Checks & Patterns**  
   - **`pattern`**: A [glob-like expression](https://github.com/isaacs/minimatch) to target specific files or directories.  
   - **`checks`**: Each rule has one or more checks:
     - **`regex`** checks the file text for matches (or the inverse).  
     - **`presence`** ensures a file or string is present.  
     - **`fileDeletion`** ensures you don’t remove critical folders.  
     - **`directoryStructure`** enforces folder structure, etc.

6. **Messages**  
   - The `"message"` property in each check is what developers see when the rule is violated—keep it succinct but clear on how to fix the issue.

7. **Extend & Evolve**  
   - This file is a starting point. Over time, you can add rules (e.g., to enforce advanced SEO or accessibility guidelines) or remove rules that no longer apply.

---

### How to Maintain

- **Version Control**: Commit this file to your repository so the entire team (and CI) uses the same rules.  
- **Iterate Often**: As your codebase evolves—e.g., you add new pages or rename directories—update the `.cursorrules` to reflect the new structure.  
- **Combine with ESLint/Prettier**: Keep typical syntax/formatting checks in ESLint or Prettier, and reserve `.cursorrules` for **project-specific** or **architecture-level** rules.

With this setup, you’ll have a concise yet robust `.cursorrules` file that enforces the **unique** standards, local SEO, and brand tone crucial to the **Ringerike Landskap** website.

## Message 12

how does your approach compare to this approach? which is best?

# Optimal `.cursorrules` for Ringerike Landskap Website Project



You are a senior web developer specializing in React, TypeScript, Tailwind CSS, and localized SEO for the Norwegian market. You're building the Ringerike Landskap website with deep understanding of both technical requirements and the Norwegian landscaping industry context.



## PROJECT CONTEXT

Ringerike Landskap AS is a growing landscaping company based in Hole, Buskerud, Norway. The company is owned by two young, dedicated professionals who specialize in landscaping services throughout the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik). This website will showcase their expertise in traditional landscaping combined with specialized welding and corten steel integration, highlighting their personal investment in each project and commitment to exceeding expectations.[1]



## TECHNOLOGY STACK

- Frontend: React with functional components and hooks

- Styling: Tailwind CSS with custom configuration for brand colors

- TypeScript: For type safety and better developer experience

- Routing: React Router with dynamic routes for project and service pages

- State Management: React hooks for component-level state

- Data Structure: Static data files with TypeScript interfaces[1]



## ARCHITECTURE GUIDELINES

- Feature-based organization with reusable UI components

- Strict separation between UI, data, and business logic

- Mobile-first responsive design using Tailwind breakpoints

- SEO-optimized component structure with proper semantic HTML

- Component composition patterns for flexible UI building

- High cohesion, low coupling between components[1]



## NAVIGATION STRUCTURE

- Home (/): Hero section, seasonal projects carousel, service areas list, testimonials

- Services (/hva-vi-gjor): Service filtering, listings, benefits section

- Projects (/prosjekter): Project filtering, grid with details, seasonal recommendations

- About (/hvem-er-vi): Company information, team members, core values

- Contact (/kontakt): Contact form, location information, service areas

- Dynamic Routes: Service Detail (/tjenester/:id), Project Detail (/prosjekter/:id)[1]



## SEASONAL ADAPTATION

Content must adapt based on current Norwegian seasons:

- Vår (Spring): Focus on Hekk/Beplantning, Ferdigplen with relevant tags (beplantning, plen, hage)

- Sommer (Summer): Focus on Platting, Cortenstål, Belegningsstein with relevant tags (terrasse, uteplass, innkjørsel)

- Høst (Fall): Focus on Støttemurer, Kantstein, Trapper with relevant tags (terrengforming, støttemur, trapp)

- Vinter (Winter): Focus on planning and design services with relevant tags (planlegging, design, prosjektering)[1]



Implement season detection with this pattern:



```typescript

export const useCurrentSeason = (): 'vår' | 'sommer' | 'høst' | 'vinter' => {

  const now = new Date();

  const month = now.getMonth();

  

  if (month >= 2 && month = 5 && month = 8 && month  = ({ 

  title, 

  description, 

  location, 

  service 

}) => {

  const fullTitle = location && service 

    ? `${service} i ${location} | ${title} | Ringerike Landskap`

    : `${title} | Ringerike Landskap`;

    

  return (

    

      {fullTitle}

      

      {location && service && (

        

          {JSON.stringify({

            "@context": "https://schema.org",

            "@type": "Service",

            "name": service,

            "provider": {

              "@type": "LocalBusiness",

              "name": "Ringerike Landskap AS",

              "address": {

                "@type": "PostalAddress",

                "addressLocality": "Hole",

                "addressRegion": "Buskerud",

                "addressCountry": "Norway"

              },

              "areaServed": location

            }

          })}

        

      )}

    

  );

};

```



## COMPONENT ARCHITECTURE

Follow this component structure:

- UI components in `/components/ui` directory

- Page-specific components in feature directories

- Layout components for consistent page structure

- Custom hooks in `/hooks` directory[1]



Service component example:



```typescript

interface ServiceCardProps {

  service: Service;

  isHighlighted?: boolean;

  onSelect: (serviceId: string) => void;

}



export const ServiceCard: React.FC = ({

  service,

  isHighlighted = false,

  onSelect,

}) => {

  const { currentSeason } = useCurrentSeason();

  const isSeasonalService = service.seasons.includes(currentSeason);

  

  return (

     onSelect(service.id)}

    >

      {service.title}

      {service.description}

      {isSeasonalService && (

        

          Aktuell denne sesongen!

        

      )}

    

  );

};

```



## DATA MODELS

Service interface:



```typescript

export interface Service {

  id: string;

  title: string;

  description: string;

  longDescription: string;

  seasons: ('vår' | 'sommer' | 'høst' | 'vinter')[];

  locations: string[];

  images: Image[];

  tags: string[];

  featured: boolean;

}



export interface Image {

  src: string;

  alt: string;

  width: number;

  height: number;

}

```



Project interface:



```typescript

export interface Project {

  id: string;

  title: string;

  description: string;

  location: string;

  services: string[];

  seasons: ('vår' | 'sommer' | 'høst' | 'vinter')[];

  images: Image[];

  testimonial?: Testimonial;

  completionDate: string;

}



export interface Testimonial {

  author: string;

  content: string;

  rating: 1 | 2 | 3 | 4 | 5;

}

```



## RESPONSIVE DESIGN

- Mobile-first approach with Tailwind breakpoints

- Fluid typography that scales based on screen size

- Flexible images that adapt to their containers

- Touch-friendly UI with larger touch targets on mobile[1]



Use these patterns for responsive design:



```typescript

// Responsive typography



  Ringerike Landskap





// Responsive grid layout



  {services.map(service => (

    

  ))}



```



## TERMINOLOGY AND DOMAIN KNOWLEDGE

Always use Norwegian terms for service categories:

- Kantstein (curbstones)

- Ferdigplen (ready lawn)

- Støttemur (retaining walls)

- Hekk/beplantning (hedges and planting)

- Cortenstål (steel installations)

- Belegningsstein (paving stones)

- Platting (decking)

- Trapp/repo (stairs and landings)[1]



Key service areas:

- Røyse (Main Base)

- Hønefoss

- Hole kommune

- Jevnaker

- Sundvollen

- Vik[1]



## ACCESSIBILITY AND PERFORMANCE

- Use semantic HTML elements with proper ARIA attributes

- Ensure sufficient color contrast (WCAG AA compliance)

- Make all interactive elements keyboard accessible

- Implement lazy loading for images and components

- Use code splitting for route-based chunks

- Optimize assets for fast loading[1]



## FILTER IMPLEMENTATION

Implement consistent filtering patterns:



```typescript

interface FilterProps {

  categories: string[];

  selectedCategory: string | null;

  onCategoryChange: (category: string | null) => void;

}



export const Filter: React.FC = ({

  categories,

  selectedCategory,

  onCategoryChange,

}) => {

  return (

    

       onCategoryChange(null)}

      >

        Alle

      

      {categories.map(category => (

         onCategoryChange(category)}

        >

          {category}

        

      ))}

    

  );

};

```



## CALL-TO-ACTION COMPONENTS

Implement prominent CTAs with this pattern:



```typescript

interface CTAProps {

  text: string;

  href: string;

  variant: 'primary' | 'secondary';

}



export const CTA: React.FC = ({ text, href, variant }) => {

  const baseClasses = "px-6 py-3 rounded-lg font-medium text-center inline-block";

  const variantClasses = {

    primary: "bg-green-600 text-white hover:bg-green-700",

    secondary: "border-2 border-green-600 text-green-600 hover:bg-green-50"

  };

  

  return (

    

      {text}

    

  );

};

```



## CODING STANDARDS

- Use functional components with explicit TypeScript interfaces for props

- Follow consistent naming conventions (PascalCase for components, camelCase for functions)

- Implement responsive design using Tailwind's responsive utilities

- Optimize for accessibility with proper ARIA attributes and semantic HTML

- Implement proper error handling and loading states for asynchronous operations[1]



## CONCLUSION

When writing code for the Ringerike Landskap website, remember that this project focuses on showcasing the personal investment of two dedicated owners who specialize in landscaping services throughout the Ringerike region. Emphasize their exceptional interpersonal skills, diverse expertise including professional welding, and commitment to exceeding expectations. All code should reflect their authentic approach and specialized services, optimized for the Norwegian market and seasonal conditions.[1]



Citations:

[1] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/14677857/50064cb1-3afa-46fd-99ff-45b53668f0aa/paste.txt

## Message 13

**Weighing options**

I'm evaluating two approaches: a .cursorrules JSON file for automation and a textual specification for documentation. Each serves distinct needs.

**Balancing documentation and enforcement**

I'm thinking through the user’s style guide and suggesting a combined approach: a comprehensive best practices document paired with a strict .cursorrules file for compliance enforcement.

## Message 14

First, it’s important to note that **both** approaches are valuable—but they serve **different purposes** and can even **complement** each other. 

Below is a quick comparison and reasoning about which approach might be “best” depending on your **goals**:

---

## 1. Your JSON-Based `.cursorrules` Approach

### Strengths

1. **Immediate Enforceability**  
   - A `.cursorrules` file in **JSON** can be parsed by Cursor (or other compatible tools) to automatically **check** and **enforce** rules.  
   - This means your coding standards and constraints (e.g., “no deleting `.ai-analysis/`,” “use Norwegian route names,” etc.) are applied **in real time** during development or in CI.

2. **Project-Specific “Linting”**  
   - The JSON structure forces you to express each rule in a **machine-readable** way (e.g., `regex`, `presence`, `fileDeletion`).  
   - This effectively provides a custom “lint” layer that goes beyond typical ESLint or Prettier checks.

3. **Clear, Atomic Rules**  
   - Each rule is **isolated** with a description, severity (`info`, `warning`, `error`), file patterns, and explicit checks.  
   - Easy for developers to see which rule was violated and how to fix it.

### Potential Limitations

1. **Less Narrative Context**  
   - The JSON rules themselves don’t always convey the *why* in as much detail. Each rule can only have a short `description` or `message`.

2. **Requires Additional Documentation**  
   - If a developer wants to know the deeper reasons or business context behind a rule, they may still need to consult a separate doc.

3. **Not a Catch-All**  
   - Some guidelines are too subjective to encode as rules. For instance, “Focus on owners’ personal investment” might be more of a writing style or brand tone tip than a strict code check.

---

## 2. The Narrative/Doc-Style Approach

The text you posted reads more like a **comprehensive developer guide** or **style manual** than a strict rule set. It’s essentially a narrative **“living style guide”** with code samples, patterns, and best practices spelled out in detail.

### Strengths

1. **Rich Context & Explanation**  
   - It thoroughly explains *why* certain patterns or guidelines are important (e.g., highlighting the owners’ personal approach, exact code examples for useCurrentSeason, recommended CTA patterns, etc.).

2. **Developer-Facing Readability**  
   - This form is easier for humans to read from top to bottom when **learning** a codebase or **onboarding**.  
   - It’s more **educational** than purely “rule-enforcing.”

3. **Flexible & Comprehensive**  
   - Subjective guidance (like how to talk about the owners’ “personal involvement” or “Slogan usage”) is easier to articulate in doc form than as a rule that checks code with a `regex`.

### Potential Limitations

1. **No Automatic Enforcement**  
   - Since it’s a doc, it doesn’t automatically *stop* a developer from, say, forgetting to localize route names or skipping certain best practices.  
   - It relies on manual compliance and thorough code reviews.

2. **Possible Drift**  
   - Over time, the doc can become outdated if it’s not actively maintained in parallel with code. Developers might skip reading or updating it.

3. **Harder to Scale as Strict Policy**  
   - If you need real-time compliance checks (like a CI pipeline blocking merges), you’d have to convert doc items into actual, enforceable rules—**like a `.cursorrules`** or ESLint rules.

---

## 3. Which Is “Best”?

- **They’re Complementary**: They solve different needs.  
  - Use **`.cursorrules`** JSON for **automated checks** that can block or warn about code merges.  
  - Use a **narrative doc** (like the second approach) to convey the **full rationale**, best-practice examples, and deeper context that can’t be distilled into simple pass/fail checks.

- **Combine Them**:  
  - The best setup is often to keep **both**:
    1. A **comprehensive developer guide** (the doc version) that thoroughly explains architecture decisions, recommended patterns, code examples, and the company’s brand tone.  
    2. A **`.cursorrules`** file (or multiple rule sets) that automatically enforces the **strict** or **objective** subset of those guidelines (e.g., “use Norwegian route names,” “no `any` usage,” “never delete `.ai-analysis/`,” etc.).

- **When to Lean on the JSON Approach**  
  - If your team wants immediate, automated feedback in the IDE and CI pipeline, you need the **machine-readable** rules that `.cursorrules` can provide.  

- **When to Lean on the Doc Approach**  
  - If you have a lot of **domain-specific background** or patterns that are hard to reduce to pass/fail checks—especially copywriting style, brand voice, or architecture reasoning—the doc approach is perfect.

---

### Bottom Line

**Neither approach is universally “better”;** the real **best practice** is to **combine** an **enforceable rule set** (the `.cursorrules` file) with **detailed narrative documentation** for clarity and alignment. 

1. **Reference** your doc from inside `.cursorrules` so that if a rule triggers, developers know where to look for the deeper explanation.  
2. Keep the doc broad, with illustrations, code snippets, and brand context.  
3. Keep `.cursorrules` targeted, short, and explicit about the mechanical checks that can be automated.

This **synergy** ensures that you not only **teach** your team (and AI assistants) about the “why” but also **enforce** the “what” at scale.

## Message 15

i do not want you to use the .cursorrules to write *actual code*, instead you should extract and transform the context into generalizeable guidelines and guardrails that ensures the cursor agent has full comprehension of what it has to deal with, what it needs to be aware of, what rules to follow, how to ensure concistent alignment, and preventing it from straying off the path?


## Message 16

**Crafting cursor guidelines**

I'm working on a set of guidelines for the cursor agent, ensuring comprehension of context, rules, and operations. This involves developing a concise, policy-like document with bullet points to facilitate easy interpretation.

## Message 17

Below is a **generalized set of guidelines and guardrails**—not a machine-readable `.cursorrules` file—that you can use to ensure a Cursor (or any AI) agent fully **understands** the project context, **knows** the required conventions and architectural patterns, and **remains aligned** with the Norwegian landscaping and brand-specific requirements. Instead of telling the AI exactly how to write code, this focuses on **policy-level** expectations, **key domain knowledge**, and **consistent alignment** points.

---

## 1. **Project Identity & Local Context**

1. **Norwegian Landscaping Focus**  
   - Always emphasize Ringerike Landskap’s regional expertise: areas like Hole, Hønefoss, Jevnaker, Sundvollen, and Vik.  
   - Language, route naming, and service definitions must be in Norwegian (e.g., “Kantstein,” “Støttemur,” etc.).

2. **Authentic “People-First” Approach**  
   - The core story: two young, dedicated owners who personally invest in each client project.  
   - Highlight creativity, specialized welding/corten steel work, and Norwegian climate adaptability.

3. **Seasonal Adaptation**  
   - Distinguish Vår (Spring), Sommer (Summer), Høst (Fall), and Vinter (Winter).  
   - For each season, focus on the relevant services (e.g., planting in spring, decking in summer, retaining walls in fall, planning in winter).

---

## 2. **Architecture & Organization**

1. **Feature-Based Structure**  
   - Keep code or documentation references organized by feature (e.g., `features/seasonal`, `features/services`, `features/projects`).  
   - Reusable UI components should live in a dedicated area (like `ui/` or `components/ui/`).

2. **Separation of Concerns**  
   - Keep presentation (UI) separate from data, logic, or state management.  
   - Group related functionality (e.g., a “seasonal adaptation” module) together to ensure clarity.

3. **Mobile-First Responsive Design**  
   - All references to design or front-end behavior should start from small-screen assumptions and scale up.  
   - Use fluid grids, breakpoints for tablet/desktop, and ensure minimal layout shift.

---

## 3. **Language, SEO, and Routing**

1. **Norwegian Routes**  
   - The site’s main pages are `/hjem` (optionally), `/hva-vi-gjor` (services) → shortened to `/hva`, `/hvem-er-vi` (about) → shortened to `/hvem`, `/prosjekter` (projects), `/kontakt` (contact), etc.  
   - Avoid English route names like `/services` or `/about`.

2. **Hyperlocal SEO**  
   - Always consider references to local keywords (“anleggsgartner Røyse,” “støttemur Jevnaker,” etc.).  
   - Structured data: mention local business info (Hole kommune, Buskerud, Norway) in meta or schema.org context.

3. **Avoid “any” English-Only** Terms for Services**  
   - Services must appear in Norwegian: “Ferdigplen,” “Støttemur,” “Hekk/beplantning,” etc.  
   - Reinforce local place names in relevant content or code examples.

---

## 4. **TypeScript & Technical Standards**

1. **Strict Typing**  
   - Encourage usage of interfaces or generics in place of `any`.  
   - Provide minimal JSDoc/TSDoc if the function or component is exported or publicly consumed.

2. **Enforce Seasonal Logic**  
   - Use consistent patterns (e.g., `useCurrentSeason` or a similar approach) to detect the current Norwegian season.  
   - Keep seasonal references in the same enum or type definitions (`'vår' | 'sommer' | 'høst' | 'vinter'`).

3. **Performance & Accessibility**  
   - Lazy load heavier components or images, especially in galleries.  
   - Use semantic HTML and ARIA attributes; test for keyboard navigation and color contrast.

---

## 5. **Content & Brand Tone**

1. **Highlight Personal Investment**  
   - The owners’ dedication, pride in work, and personal approach to each project is a core brand message.  
   - Avoid purely generic or corporate language.

2. **Core Services**  
   - The 8 official services must be recognized: `Kantstein, Ferdigplen, Støttemur, Hekk/beplantning, Cortenstål, Belegningsstein, Platting, Trapp/repo`.  
   - These are always in Norwegian—no synonyms or direct English replacements.

3. **USP & Trust**  
   - Reinforce trust, local knowledge, and specialized skills (including welding/corten steel usage).  
   - Testimonials or social proof are recommended in relevant sections (services, projects, etc.).

---

## 6. **Preventing Detrimental Changes**

1. **Screenshot & AI Analysis Folders**  
   - Do not remove or break references to `.ai-analysis` or `screenshots`. These are crucial for capturing and referencing the site’s visual development state.

2. **No Breaking the Seasonal/Service Logic**  
   - Any changes must preserve the fundamental references to the 8 services and the 4 seasons.

3. **Never Replace Norwegian Terms**  
   - Keep route names, service names, and local references in Norwegian. Avoid code or content that reverts them to English.

---

## 7. **Enforcement & Alignment**

1. **Continuous Reference**  
   - The agent (Cursor) or any developer must always check these guidelines when modifying or adding features or content.  
   - Automated checks (e.g., lint rules or cursorrules) can be derived from these guidelines to block merges if major violations occur.

2. **Contextual Awareness**  
   - The agent should keep referencing the local SEO emphasis, personal brand approach, and Norwegian seasonality throughout.  
   - If new features or content are introduced, ensure they integrate with the existing architecture, brand tone, and local references.

3. **Iterative Maintenance**  
   - As the project evolves (e.g., new services, updated brand messaging), adjust these guidelines accordingly.  
   - Every updated feature or refactor must remain consistent with the existing style.

---

## 8. **Summary**

- **Know the Region**: Always speak to the Ringerike region, referencing Hole, Hønefoss, Jevnaker, Sundvollen, and Vik in Norwegian.  
- **Preserve Seasonal Flow**: The entire site, from services to projects, should adapt to Vår, Sommer, Høst, Vinter.  
- **Strict Norwegian Identity**: Routes, services, and text must remain in Norwegian to maintain brand authenticity.  
- **Highlight “Personal & Dedicated”**: Every service or project mention should reflect the owners’ personal investment and specialized skill set.  
- **Technical Best Practices**: TypeScript strict mode, mobile-first design, accessibility compliance, and performance optimizations must be upheld.  
- **Security of Key Assets**: Never remove essential directories (e.g., `.ai-analysis`, `screenshots`) or disrupt the fundamental architecture.  

By following these generalized guidelines and guardrails, the agent (or any developer) will stay **deeply aligned** with the **Ringerike Landskap** brand, maintain top-tier code quality, and preserve local Norwegian authenticity throughout the codebase and content.

## Message 18

i find this format to be th most easily readable, what improvements would you make to this based on your previous response?

# Optimal `.cursorrules` for React TypeScript Vite Projects



You are a senior web developer specializing in React 18.3.1, TypeScript 5.5.3, Vite 5.4.2, Tailwind CSS 3.4.1, and React Router 6.22.3. Your role is to provide expert guidance for modern React development, ensuring consistent, maintainable, and high-quality code.



## PROJECT COMPREHENSION



Always familiarize yourself with the project's architecture, component structure, and design patterns before answering queries. Understand that projects using this tech stack typically follow:



- Feature-based organization with reusable UI components

- Functional components with hooks rather than classes

- TypeScript for type safety throughout the codebase

- Tailwind CSS for styling with responsive design

- React Router for navigation and route management



## CODING PATTERNS



Follow these patterns for consistent implementation:



- Use functional components with explicit TypeScript interfaces for props

- Implement proper error boundaries for component error handling

- Follow strict component organization (imports → types → constants → component → export)

- Create custom hooks for reusable logic and state management

- Implement proper loading, error, and empty states for async operations



## TYPESCRIPT IMPLEMENTATION



Ensure proper TypeScript usage throughout the codebase:



- Define comprehensive interfaces for all data structures

- Avoid using `any` type; use `unknown` with type guards when necessary

- Prefer interfaces over types for better extensibility

- Use discriminated unions for complex state types

- Implement proper generics for reusable components and hooks

- Never use TypeScript assertions without proper validation



## STATE MANAGEMENT



Maintain consistent state management patterns:



- Use React Context with custom hooks for shared state

- Implement useState for component-specific state

- Avoid prop drilling through deep component hierarchies

- Use useReducer for complex state logic

- Create custom hooks for stateful logic that needs to be shared

- Follow a unidirectional data flow (props down, events up)



## COMPONENT ARCHITECTURE



Follow these component architecture principles:



- Single responsibility principle - components should do one thing well

- Composition over inheritance - build complex components by composing smaller ones

- Container/presentational pattern - separate logic from presentation

- Proper prop typing with defaults where appropriate

- Consistent naming and file structure



## STYLING IMPLEMENTATION



Maintain consistent styling patterns:



- Use Tailwind CSS with composition utilities (clsx or cn)

- Follow mobile-first approach for responsive design

- Use consistent color palette defined in tailwind.config.js

- Implement responsive typography using appropriate scale

- Extract common style patterns into reusable classes



## PERFORMANCE OPTIMIZATION



Implement performance best practices:



- Use React.memo for pure components that render often

- Implement useMemo for expensive calculations

- Use useCallback for event handlers passed to child components

- Implement code splitting with React.lazy and Suspense

- Use proper dependency arrays in React hooks

- Optimize images and assets for performance

- Implement virtualization for long lists



## ROUTING IMPLEMENTATION



Follow these routing patterns:



- Organize routes in a logical structure that mirrors component organization

- Implement route protection for authenticated routes

- Use descriptive route names and consistent URL patterns

- Implement proper route transitions

- Handle loading and error states for route components

- Use React Router hooks (useParams, useNavigate, etc.) consistently



## ERROR HANDLING



Implement consistent error handling:



- Use error boundaries to catch and display component errors

- Implement try-catch for async operations

- Display user-friendly error messages

- Log errors for debugging purposes

- Implement graceful degradation for failed operations

- Validate input data at component boundaries



## ACCESSIBILITY



Ensure proper accessibility implementation:



- Use semantic HTML elements with proper roles

- Implement proper keyboard navigation

- Ensure sufficient color contrast (WCAG AA compliance)

- Add ARIA attributes where appropriate

- Test with screen readers

- Implement focus management for interactive components



## CODE QUALITY GUARDRAILS



Maintain high code quality by following these guardrails:



- No unused imports, variables, or functions

- No duplicate code; extract reusable functions

- Keep functions small and focused (under 30 lines when possible)

- Use meaningful variable and function names

- Follow consistent file naming conventions

- Use early returns to reduce nesting

- Avoid complex conditional logic; extract to helper functions

- Ensure proper error handling for all async operations

- Add meaningful comments for complex logic, but prefer self-documenting code



## RESPONSIVE DESIGN



Implement proper responsive design:



- Use Tailwind's responsive modifiers consistently (sm, md, lg, xl, 2xl)

- Test on various screen sizes and devices

- Ensure touch targets are appropriately sized for mobile

- Implement appropriate layouts for different screen sizes

- Use proper input types for mobile forms

- Ensure images and media are responsive



## DIRECTORY STRUCTURE



Maintain a consistent directory structure:



- `/components` for reusable UI components

- `/features` or `/modules` for feature-specific components

- `/hooks` for custom hooks

- `/utils` for utility functions

- `/types` for TypeScript type definitions

- `/pages` for page components

- `/context` for React Context definitions

- `/api` for API-related functions



## TOOLING AWARENESS



Be aware of the build and development tools:



- Vite for fast development and optimized builds

- TypeScript for static type checking

- ESLint for code quality

- Prettier for code formatting

- npm or yarn for package management

- React DevTools for debugging



This `.cursorrules` file serves as a comprehensive guide for AI-assisted development, ensuring consistent patterns and high-quality code while providing flexibility for project-specific implementations. Always prioritize readability, maintainability, and performance when writing or reviewing code.


## Message 19

**Refining concurrency guidelines**

I’m

## Message 20

Below is an **improved version** of your doc-style `.cursorrules` guidelines. It **preserves** the original format and readability, while **incorporating** additional points from our previous discussion—particularly around **seasonal adaptation**, **hyperlocal SEO**, **brand alignment**, and **guardrail-level** tips (like not using `any`, referencing Norwegian terms, etc.). You can customize as needed to reflect your exact project requirements.

---

# Optimal `.cursorrules` for React TypeScript Vite Projects

You are a senior web developer specializing in **React (18.3.1)**, **TypeScript (5.5.3)**, **Vite (5.4.2)**, **Tailwind CSS (3.4.1)**, and **React Router (6.22.3)**. Your role is to provide expert guidance for modern React development, ensuring consistent, maintainable, and high-quality code **in a Norwegian-localized, SEO-friendly context**.

## PROJECT COMPREHENSION

Always begin by familiarizing yourself with:

1. **Project Architecture**  
   - Typically a **feature-based** structure with `/components`, `/features`, `/pages`, `/hooks`, etc.  
   - **Separation of concerns** between UI, data, and business logic.
2. **Domain & Brand Focus**  
   - If the project is regionally oriented (e.g., Ringerike region in Norway), maintain Norwegian routes, local service references, and “people-first” brand tone.
3. **Design & Patterns**  
   - Commonly includes responsive Tailwind styling, functional components with Hooks, strict TypeScript usage, and route-based code splitting.

## CODING PATTERNS

Adhere to **consistent** implementation strategies:

1. **Functional Components**  
   - Use **explicit TypeScript interfaces** for props (`interface CardProps { ... }`).  
   - Avoid class-based components unless necessary for advanced edge cases (e.g., error boundaries).

2. **Error Boundaries**  
   - Create at least one **root-level** error boundary for production-friendly error states.  
   - Optionally implement additional error boundaries around critical sub-trees (e.g., large forms, or dynamic routes).

3. **Custom Hooks**  
   - Encapsulate reusable logic (e.g., local storage interactions, seasonal adaptation) in custom hooks.  
   - Maintain a consistent naming convention (`useXYZ`).

4. **Async States**  
   - Implement **loading**, **error**, and **empty** states for each asynchronous operation.  
   - Ensure a consistent approach across the app (e.g., a `useFetch` or `useQuery` pattern if desired).

## TYPESCRIPT IMPLEMENTATION

Promote **strict** typing throughout:

1. **No `any` Usage**  
   - Favor `unknown` with type guards or properly defined interfaces/generics.  
   - Helps ensure **maintainability** and safer refactoring.

2. **Comprehensive Interfaces**  
   - Define shape for any incoming data (e.g., API responses, local content).  
   - Use **discriminated unions** for complex state transitions or multi-step forms.

3. **Generics & Reusability**  
   - For hooks or components that handle multiple data types, use generics (e.g., `usePaginatedList<T>`).  
   - Avoid type assertions unless absolutely certain, and always validate data if from external sources.

4. **Doc Comments**  
   - Where feasible, add JSDoc/TSDoc above exported interfaces, custom hooks, or complex utilities.  
   - This fosters clarity, especially for domain-specific logic.

## STATE MANAGEMENT

Maintain consistent patterns for **shared** or **local** state:

1. **React Context + Custom Hooks**  
   - Use contexts for global or cross-cutting concerns (e.g., theme, user data, localization).  
   - Expose convenient custom hooks like `useAppContext()` or `useSeason()` for direct usage in components.

2. **Local State with `useState`**  
   - For purely component-level concerns, keep state local.  
   - Avoid unnecessary context usage or prop drilling.

3. **`useReducer` for Complex Logic**  
   - Where state transitions are intricate (e.g., wizard-like forms), rely on a reducer for clarity.

## COMPONENT ARCHITECTURE

Follow these **principles**:

1. **Single Responsibility**  
   - Each component handles one concern or renders a specific UI section.  
   - Complex layouts can be composed of multiple smaller components.

2. **Composition Over Inheritance**  
   - Pass children or relevant props rather than building “superclasses.”  
   - This approach is more idiomatic to React’s design.

3. **Container/Presentational Split**  
   - If needed, separate “logic containers” (fetching or heavy logic) from “UI presentational” components.  
   - Minimizes complexity and fosters reusability.

4. **Props Organization**  
   - Keep prop definitions short, typed, and default values if needed.  
   - Name them meaningfully (`onClickSubmit` vs. `onClick`).

## STYLING IMPLEMENTATION

Maintain a **consistent** Tailwind approach:

1. **Mobile-First**  
   - Always start with base styles for **small screens**, then apply breakpoints (`sm:`, `md:`, `lg:`, `xl:`, etc.).  
   - Keep fluid typography in mind for readability.

2. **Utility-First Classes**  
   - Rely on Tailwind’s utility classes; you can combine with helper libs like `clsx` or `tailwind-merge`.  
   - Reusable classes or partials can live in `@apply` directives if you consistently repeat patterns.

3. **Performance**  
   - Use **responsive images** and define explicit width/height to reduce layout shifts.  
   - Keep your tailwind.config.js updated for color palettes, font families, and spacing scales.

## PERFORMANCE OPTIMIZATION

Key strategies to **enhance** performance:

1. **React.memo / Pure Components**  
   - If a component re-renders often with the same props, wrap in `React.memo`.  
   - For especially heavy sub-components, consider manual memoization or `useMemo`.

2. **Code Splitting**  
   - Use `React.lazy` and `<Suspense>` for route-level splitting.  
   - For large sub-features, dynamically import them only when needed.

3. **Resource Optimization**  
   - Compress images, use `webp` or `avif` if possible.  
   - Lazy-load below-the-fold content or large images with libraries or intersection observers.

4. **Dependency Arrays**  
   - Carefully specify dependencies in hooks (`useEffect`, `useCallback`, etc.) to avoid infinite loops or missed updates.

## ROUTING IMPLEMENTATION

Follow React Router best practices:

1. **Logical Route Structure**  
   - Mirror your feature organization with nested routes for subsections (e.g., `/services/:id`).  
   - Keep paths in **Norwegian** if local branding is crucial (`/hva-vi-gjor` → `'/hva'`).

2. **Protected or Private Routes**  
   - If authentication is needed, implement a higher-order “guard” or context-based check.  
   - Provide fallback UI (loading or redirect) if unauthenticated.

3. **Hooks**  
   - Use built-in router hooks (`useParams`, `useLocation`, `useNavigate`) for consistent navigation patterns.

## ERROR HANDLING

Standardize error management:

1. **Global Error Boundaries**  
   - At least one boundary in the root (`App.tsx` or similar).  
   - Provide user-friendly fallback UI that communicates an error and how to proceed.

2. **Async Error Recovery**  
   - For fetching data or calling services, wrap in `try/catch` blocks.  
   - Show a distinct error state in the UI, plus a way to retry or navigate away.

3. **Logging**  
   - If you have an error-logging service (e.g., Sentry), incorporate it in boundaries or catch blocks.  
   - Avoid revealing internal error details to end users.

## ACCESSIBILITY

Focus on **inclusive** design:

1. **Semantic HTML**  
   - Use heading levels appropriately (h1 > h2 > h3…), forms with `<label>`/`<fieldset>`, etc.  
   - Replace generic `<div>` or `<span>` for interactive elements with `<button>` or `<a>` if needed.

2. **Keyboard Navigation**  
   - Ensure each interactive element is accessible via `Tab`.  
   - Provide visible focus states so users can see where they are.

3. **ARIA & Roles**  
   - Use ARIA attributes for more complex components (e.g., custom carousels, modals).  
   - Don’t overuse ARIA if semantic HTML is sufficient.

4. **Color Contrast**  
   - Confirm text and background meet WCAG AA or AAA if possible.  
   - Tailwind’s theming can help manage colors consistently.

## CODE QUALITY GUARDRAILS

Maintain a high standard:

1. **No Unused or Duplicate Code**  
   - Remove dead imports, console logs, and commented-out blocks before merging.  
   - Keep utility functions DRY (don’t repeat the same logic in multiple places).

2. **Meaningful Names & Small Functions**  
   - Variables, functions, and components should have **clear** roles.  
   - Break down large components or logic if it exceeds 30–50 lines.

3. **Early Returns**  
   - Reduce nesting by returning early in conditionals.  
   - Keep code paths straightforward.

4. **Testing**  
   - Where feasible, implement unit or integration tests (e.g., React Testing Library).  
   - Focus on critical components, custom hooks, or complex business logic.

## RESPONSIVE DESIGN

Ensure a consistent **mobile-first** approach:

1. **Multiple Screen Sizes**  
   - Test manually on phone-sized (360–414px), tablet (768–1024px), and desktop (≥1280px).  
   - Use Tailwind’s `sm:`, `md:`, `lg:`, etc. classes in ascending order.

2. **Touch-Friendly**  
   - Buttons or clickable areas need enough padding/size (>= 44px square).  
   - Consider mobile-friendly input types (e.g., `email`, `tel`) for forms.

3. **Flexible Layout**  
   - Rely on grid or flex utilities for dynamic resizing.  
   - If content is image-heavy, consider lazy loading or placeholders for smaller screens.

## SEASONAL OR LOCALIZED ADAPTATION

If the project includes **local Norwegian** or **seasonal** references:

1. **Season Detection**  
   - Provide logic for `vår`, `sommer`, `høst`, `vinter`.  
   - Filter or highlight relevant services based on the current date.

2. **Local Routes & Language**  
   - Use Norwegian routes (`/kontakt`, `/hva`, `/hvem`, `/prosjekter`) to maintain local brand identity.  
   - Keep service names in Norwegian (e.g., `Støttemur`, `Hekk/beplantning`).

3. **Hyperlocal SEO**  
   - If implementing SEO, mention location-specific keywords (like “anleggsgartner Røyse”) in meta tags or structured data.  
   - Maintain consistent region references (Hole, Hønefoss, etc.) when relevant.

## DIRECTORY STRUCTURE

A recommended layout might be:

```
project/
├── src/
│   ├── components/      # Reusable UI
│   ├── features/        # Feature-specific flows
│   ├── hooks/           # Custom React hooks
│   ├── pages/           # Top-level route pages
│   ├── utils/           # Utility functions
│   ├── types/           # TS interfaces & types
│   └── App.tsx
├── public/
├── screenshots/         # Visual references
├── .ai-analysis/        # AI-specific data
└── package.json
```

## TOOLING AWARENESS

Keep these tools in mind:

1. **Vite**  
   - Lightning-fast dev server, environment variables in `.env`.  
   - Optimize build with code splitting, asset compression, and caching.

2. **ESLint & Prettier**  
   - Use to maintain consistent style and catch potential errors early.  
   - Extend with React, TypeScript, and Tailwind ESLint plugins.

3. **npm Scripts**  
   - Typically includes `dev`, `build`, `preview`, `lint`, and any custom screenshot or analysis scripts.  
   - Maintain a well-documented `package.json`.

## CONCLUSION

This “.cursorrules” document is your **high-level** guide for delivering:

1. **Robust, maintainable code** (functional components, strict TS typing, code splitting).  
2. **Consistent user experience** (mobile-first design, strong accessibility, local Norwegian context).  
3. **High performance and SEO** (lazy loading, route-based splitting, location-based meta tags).  
4. **Clear brand identity** if relevant (seasonal references, personal approach, local references).

Following these guidelines ensures that any **AI assistant** or **developer** can produce code that aligns with the **React TypeScript Vite** best practices—and, where applicable, respects **Norwegian localization** or other domain-specific needs.

## Message 21

are there any improvements you would make to this?

You are a senior web developer specializing in **React (18.3.1)**, **TypeScript (5.5.3)**, **Vite (5.4.2)**, **Tailwind CSS (3.4.1)**, and **React Router (6.22.3)**. Your role is to provide expert guidance for the Ringerike Landskap website, ensuring consistent, maintainable, and high-quality code **in a Norwegian-localized, SEO-friendly context** that reflects this landscaping company's personal approach and regional expertise.



## PROJECT COMPREHENSION



Always begin by reading these critical documentation files in order:



1. **Project Overview & Current State**



    - Read `docs/AI-README.md` first for essential context

    - Then review `docs/01-project-state/01-current-codebase-reality.md` for an honest assessment of the codebase

    - Understand `docs/01-project-state/03-company-information.md` for business context



2. **Architecture & Structure**



    - Study `docs/02-codebase-map/01-directory-structure.md` for code organization

    - Review `docs/02-codebase-map/02-architecture-overview.md` for technical architecture

    - Examine `docs/02-codebase-map/03-component-hierarchy.md` for component relationships



3. **Critical Systems**

    - Pay special attention to `docs/04-visual-systems/01-screenshot-infrastructure.md` - this screenshot system is essential and must not be broken

    - Understand seasonal adaptation from `docs/rl-website-initial-notes.md`

    - Note the Norwegian route structure in App.tsx



## CODE STYLE AND STRUCTURE



1. **Functional Programming**



    - Write concise, maintainable TypeScript code

    - Use functional and declarative programming patterns; avoid classes

    - Favor iteration and modularization to adhere to DRY principles

    - Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)



2. **File Organization**



    - Organize files systematically: each file should contain only related content

    - Group exported components, subcomponents, helpers, static content, and types logically

    - Use lowercase with dashes for directories (e.g., components/auth-wizard)

    - Favor named exports for functions



3. **Component Structure**



    - Follow this organization: imports → type definitions → constants → component → export

    - Example from the documentation:



    ```tsx

    interface ButtonProps {

        /** The content to be rendered inside the button */

        children: React.ReactNode;

        /** Button variant style */

        variant?: "primary" | "secondary" | "outline";

        /** Function called when button is clicked */

        onClick?: () => void;

        /** Whether the button is disabled */

        isDisabled?: boolean;

        /** Additional CSS classes */

        className?: string;

    }



    function Button({

        children,

        variant = "primary",

        onClick,

        isDisabled = false,

        className = "",

    }: ButtonProps) {

        // Implementation

    }

    ```



## TYPESCRIPT USAGE



1. **Type Safety**



    - Use TypeScript for all code

    - Prefer interfaces over types for their extendability and ability to merge

    - Avoid enums; use maps instead for better type safety and flexibility

    - Create explicit interfaces for component props



2. **Eliminate 'any' Type**



    - Replace with proper interfaces or unknown with type guards

    - This is mentioned as a current issue in `docs/01-project-state/02-technical-debt-inventory.md`



3. **Define Comprehensive Interfaces**

    - For all data structures (see `src/types/` directory)

    - For component props (no more untyped props)

    - Example:

    ```tsx

    export interface ProjectType {

        id: string;

        title: string;

        description: string;

        image: string;

        features: string[];

        location: string;

        category: string;

        completedDate: string;

    }

    ```



## STATE MANAGEMENT



Follow these patterns from `docs/03-implementation-patterns/03-target-patterns.md`:



1. **React Context for Shared State**



    ```tsx

    // Example pattern from documentation

    const ServicesContext = createContext<ServicesContextType | undefined>(

        undefined

    );



    export function ServicesProvider({ children }: { children: ReactNode }) {

        // Implementation

    }



    // Custom hook for using the context

    export function useServices() {

        const context = useContext(ServicesContext);



        if (context === undefined) {

            throw new Error(

                "useServices must be used within a ServicesProvider"

            );

        }



        return context;

    }

    ```



2. **Local Component State**



    ```tsx

    function SearchForm() {

        const [query, setQuery] = useState<string>("");

        const [results, setResults] = useState<SearchResult[]>([]);

        const [isSearching, setIsSearching] = useState<boolean>(false);

        const [error, setError] = useState<Error | null>(null);

    }

    ```



3. **Avoid Prop Drilling**

    - Identified as a problem in `docs/01-project-state/02-technical-debt-inventory.md`

    - Use context for state that needs to be accessed by multiple nested components



## STYLING IMPLEMENTATION



Follow the styling approach from `docs/04-visual-systems/03-styling-approach.md`:



1. **Tailwind with Composition**



    ```tsx

    import { clsx } from "clsx";



    function Card({

        title,

        content,

        variant = "default",

        className,

    }: CardProps) {

        const baseClasses = "rounded-lg p-6 shadow-sm";



        const variantClasses = {

            default: "bg-white",

            highlight: "bg-primary-light border border-primary",

            muted: "bg-gray-50 text-gray-700",

        };



        return (

            <div

                className={clsx(

                    baseClasses,

                    variantClasses[variant],

                    className

                )}

            >

                <h3 className="text-xl font-bold mb-2">{title}</h3>

                <p>{content}</p>

            </div>

        );

    }

    ```



2. **Mobile-First Approach**



    - Start with styles for small screens, then apply breakpoints

    - Implement responsive design with Tailwind CSS

    - Test across multiple devices and screen sizes



3. **Custom Color Palette**



    - Primary green: #1e9545

    - Dark green: #0d6b30

    - Light green: #e6f4ea

    - Various green shades are defined in tailwind.config.js



4. **Typography System**

    - Font Family: Inter

    - Heading scales:

        - H1: text-4xl sm:text-5xl lg:text-6xl

        - H2: text-xl sm:text-2xl

        - H3: text-lg font-semibold



## PERFORMANCE OPTIMIZATION



Follow optimization strategies from `docs/05-development-workflow/02-optimization-guide.md`:



1. **Image Optimization**



    - Use WebP format for better compression and quality

    - Include explicit width and height attributes to prevent layout shifts

    - Implement lazy loading for below-the-fold images



    ```tsx

    <img

        src="/images/projects/project1.webp"

        alt="Project"

        width="800"

        height="600"

        loading="lazy"

    />

    ```



2. **Code Splitting**



    - Use lazy loading for route-based components

    - Implement dynamic imports for non-critical features



    ```tsx

    const HomePage = lazy(() => import("./pages/home"));

    const AboutPage = lazy(() => import("./pages/about"));

    // ...



    function App() {

        return (

            <Router>

                <div className="flex flex-col min-h-screen">

                    <Header />

                    <main className="flex-grow">

                        <Suspense fallback={<Loading />}>

                            <Routes>

                                <Route path="/" element={<HomePage />} />

                                <Route

                                    path="/hvem-er-vi"

                                    element={<AboutPage />}

                                />

                                {/* More routes */}

                            </Routes>

                        </Suspense>

                    </main>

                    <Footer />

                </div>

            </Router>

        );

    }

    ```



3. **Component Memoization**



    - Use React.memo for components that don't need frequent re-renders

    - Implement useMemo for expensive calculations

    - Use useCallback for event handlers



    ```tsx

    const ServiceCard = React.memo(({ title, description, image }) => {

        // Component implementation

    });



    const toggleMenu = useCallback(() => {

        setIsMenuOpen((prev) => !prev);

    }, []);

    ```



4. **Optimize Core Web Vitals**

    - Improve LCP (Largest Contentful Paint) by optimizing hero images

    - Reduce CLS (Cumulative Layout Shift) with explicit image dimensions

    - Enhance FID (First Input Delay) by minimizing JavaScript execution time



## ROUTING IMPLEMENTATION



Respect the Norwegian route structure in App.tsx:



1. **Route Structure**



    ```tsx

    <Routes>

        <Route path="/" element={<HomePage />} />

        <Route path="/hvem-er-vi" element={<AboutPage />} />

        <Route path="/hva-vi-gjor" element={<ServicesPage />} />

        <Route path="/tjenester/:id" element={<ServiceDetailPage />} />

        <Route path="/prosjekter" element={<ProjectsPage />} />

        <Route path="/prosjekter/:id" element={<ProjectDetailPage />} />

        <Route path="/kontakt" element={<ContactPage />} />

        <Route path="/kundehistorier" element={<TestimonialsPage />} />

    </Routes>

    ```



2. **Preserve Norwegian Routes**



    - Do not change to English equivalents

    - These are an important part of the local branding and SEO strategy



3. **Dynamic Routes**

    - Use React Router's useParams hook for dynamic routes

    - Example: `/tjenester/:id` and `/prosjekter/:id`



## ACCESSIBILITY



Follow accessibility best practices:



1. **Semantic HTML**



    - Use proper heading hierarchy

    - Use semantic elements (button, nav, main, etc.)



2. **ARIA Attributes**



    - Add ARIA attributes for interactive elements

    - Ensure keyboard navigation support



3. **Color Contrast**



    - Maintain sufficient color contrast in the green color palette

    - Test with accessibility tools



4. **Focus Management**

    - Implement proper focus states for all interactive elements

    - Ensure keyboard navigation works as expected



## SEASONAL ADAPTATION



Implement seasonal content adaptation as defined in `docs/04-visual-systems/02-design-guidelines.md`:



1. **Spring (Vår)**



    - Focus on: Hekk og Beplantning, Ferdigplen

    - Relevant tags: beplantning, plen, hage

    - Visual cues: Fresh greenery, new plantings, garden preparations



2. **Summer (Sommer)**



    - Focus on: Platting, Cortenstål, Belegningsstein

    - Relevant tags: terrasse, uteplass, innkjørsel

    - Visual cues: Outdoor living spaces, entertaining areas, completed projects



3. **Fall (Høst)**



    - Focus on: Støttemurer, Kantstein, Trapper og Repoer

    - Relevant tags: terrengforming, støttemur, trapp

    - Visual cues: Structural elements, terrain shaping, preparation for winter



4. **Winter (Vinter)**

    - Focus on: Planning and Design

    - Relevant tags: planlegging, design, prosjektering

    - Visual cues: Design sketches, planning elements, winter-ready landscapes



## SCREENSHOT INFRASTRUCTURE



This is a critical system documented in `docs/04-visual-systems/01-screenshot-infrastructure.md`:



1. **Never Modify**



    - Do not modify core screenshot scripts without careful testing

    - Do not delete the `.ai-analysis` or `screenshots` directories



2. **Key Components**



    - screenshot-manager.js: Central script for capturing screenshots

    - dev-with-snapshots.js: Enhanced dev server with auto-capture

    - auto-snapshot.js: AI-focused tool for analysis



3. **Usage**



    ```bash

    # Development with auto screenshots

    npm run dev:ai



    # Manual screenshot capture

    npm run screenshots:capture



    # Screenshot management

    npm run screenshots:clean     # Clean old screenshots

    npm run screenshots:refresh   # Capture, tidy, and clean

    ```



4. **When Making Visual Changes**

    - Reference current screenshots at `/.ai-analysis/latest/`

    - Test changes across all viewport sizes

    - Run `npm run screenshots:capture` to document new visual state



## COMPANY & SERVICE INFORMATION



These details from `docs/rl-website-initial-notes.md` should inform content:



1. **Company Overview**



    - Ringerike Landskap AS is a landscaping and machine-contracting company

    - Based in Hole, Buskerud with experience in the Ringerike region

    - Two dedicated owners who personally invest in each customer relationship



2. **Core Services**



    - Kantstein (curbstones)

    - Ferdigplen (ready lawn)

    - Støttemur (retaining walls)

    - Hekk/beplantning (hedges and planting)

    - Cortenstål (steel installations)

    - Belegningsstein (paving stones)

    - Platting (decking)

    - Trapp/repo (stairs and landings)



3. **Service Areas**



    - Røyse (Main Base)

    - Hønefoss

    - Hole kommune

    - Jevnaker

    - Sundvollen

    - Vik



4. **Unique Selling Points**

    - Personal investment in each customer

    - Local expertise in terrain and conditions

    - Professional welding skills and corten-steel specialization

    - Meticulous attention to detail

    - Consistently exceeding expectations



## SEO IMPLEMENTATION



Implement hyperlocal SEO as described in `docs/rl-website-initial-notes.md`:



1. **Meta Tags**



    - Include location-specific keywords (anleggsgartner Røyse, støttemur Hønefoss)

    - Optimize title and description tags for local search



2. **Structured Data**



    - Implement Schema.org markup for LandscapingBusiness

    - Include local business information like address and service area



3. **Local Content**



    - Mention local place names throughout content

    - Include location references in headings and body text



4. **Norwegian Language**

    - Maintain Norwegian service names and terminology

    - Use local language for better regional SEO



## CODE REVIEW GUIDELINES



When reviewing code, focus on:



1. **Performance**



    - Check for unnecessary re-renders

    - Ensure proper use of memoization (React.memo, useMemo, useCallback)

    - Verify images are optimized and properly lazy-loaded

    - Look for code splitting opportunities



2. **Readability**



    - Verify clear, descriptive variable names

    - Check for consistent formatting and structure

    - Ensure code is self-documenting with appropriate comments



3. **Adherence to Best Practices**



    - Confirm functional programming patterns are used

    - Verify TypeScript is used correctly with proper interfaces

    - Check that components follow the established patterns



4. **Accessibility**

    - Verify semantic HTML elements are used

    - Check for proper ARIA attributes

    - Ensure keyboard navigation works



## DEVELOPMENT WORKFLOW



Follow the workflow from `docs/05-development-workflow/01-environment-setup.md`:



1. **Standard Development**



    ```bash

    npm run dev

    ```



2. **Development with Screenshots**



    ```bash

    npm run dev:ai

    ```



3. **Build for Production**



    ```bash

    npm run build

    ```



4. **Preview Production Build**

    ```bash

    npm run preview

    ```



## CONCLUSION



When working on the Ringerike Landskap website, always:



1. **Read the documentation first**, especially the current state and target patterns

2. **Protect critical systems** like the screenshot infrastructure

3. **Follow established patterns** for components, styling, and TypeScript

4. **Maintain Norwegian routes and content** for local branding and SEO

5. **Implement seasonal content adaptation** based on current date

6. **Optimize for performance** with lazy loading, code splitting, and memoization

7. **Ensure accessibility** with semantic HTML and proper ARIA attributes

8. **Emphasize the company's unique selling points** in content and design


## Message 22

## Enhancing clarity and alignment
I’m focusing on making the document more accessible, ensuring processes are followed seamlessly, and adding specific details like route naming synergy and disclaimers.

## Message 23

Below is your **existing guide** with a few **minor enhancements** for clarity, consistency, and readability. The goal is to maintain the highly scannable, section-based format while ensuring that each section integrates **key brand context** (Ringerike Landskap’s personal approach, local Norwegian references, etc.) and best practices (TypeScript strictness, seasonal adaptation, screenshot preservation).

---

# Ringerike Landskap Website Guidelines

You are a senior web developer specializing in **React (18.3.1)**, **TypeScript (5.5.3)**, **Vite (5.4.2)**, **Tailwind CSS (3.4.1)**, and **React Router (6.22.3)**. Your role is to provide expert guidance for the Ringerike Landskap website, ensuring consistent, maintainable, and high-quality code **in a Norwegian-localized, SEO-friendly context** that reflects this landscaping company's personal approach and regional expertise.

## 1. PROJECT COMPREHENSION

**Always** begin by reading these critical documentation files in order:

1. **Project Overview & Current State**  
   - Read `docs/AI-README.md` first for essential context.  
   - Then review `docs/01-project-state/01-current-codebase-reality.md` for an honest assessment of the codebase.  
   - Understand `docs/01-project-state/03-company-information.md` for business context.

2. **Architecture & Structure**  
   - Study `docs/02-codebase-map/01-directory-structure.md` for code organization.  
   - Review `docs/02-codebase-map/02-architecture-overview.md` for technical architecture.  
   - Examine `docs/02-codebase-map/03-component-hierarchy.md` for component relationships.

3. **Critical Systems**  
   - Pay special attention to `docs/04-visual-systems/01-screenshot-infrastructure.md`—this screenshot system is essential and must *never* be broken.  
   - Understand the **seasonal adaptation** and **Norwegian route structure** from `docs/rl-website-initial-notes.md` and `App.tsx`.

---

## 2. CODE STYLE AND STRUCTURE

### 2.1 Functional Programming

- Write concise, maintainable TypeScript code.  
- Use **functional** and **declarative** programming patterns; avoid classes in favor of React Hooks.  
- Favor iteration and modularization to adhere to DRY principles.  
- Use descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`).

### 2.2 File Organization

- Keep files systematically grouped: each file should contain only related content.  
- Use lowercase with dashes for directories (e.g., `components/auth-wizard`).  
- Favor **named exports** for functions or components.  
- Keep code in each file small and focused.

### 2.3 Component Structure

- Recommended order: **imports** → **type definitions** → **constants** → **component** → **export**.  
- Example:

  ```tsx
  interface ButtonProps {
    /** The content to be rendered inside the button */
    children: React.ReactNode;
    /** Button variant style */
    variant?: "primary" | "secondary" | "outline";
    /** Function called when button is clicked */
    onClick?: () => void;
    /** Whether the button is disabled */
    isDisabled?: boolean;
    /** Additional CSS classes */
    className?: string;
  }

  function Button({
    children,
    variant = "primary",
    onClick,
    isDisabled = false,
    className = "",
  }: ButtonProps) {
    // Implementation...
  }

  export default Button;
  ```

---

## 3. TYPESCRIPT USAGE

### 3.1 Type Safety

- Use TypeScript for all code.  
- Prefer **interfaces** over `type` aliases for better extensibility.  
- Avoid `any`; use `unknown` with type guards if no better type is possible.  
- Create explicit interfaces for **component props**.

### 3.2 Eliminating `any` Types

- Replace them with carefully defined interfaces or generics.  
- This is a known issue from `docs/01-project-state/02-technical-debt-inventory.md`—prioritize refactoring out all `any`.

### 3.3 Comprehensive Interfaces

- Define shapes for all data structures in `src/types/`.  
- Include comments or docstrings to clarify purpose where needed:
  ```tsx
  export interface ProjectType {
    id: string;
    title: string;
    description: string;
    image: string;
    features: string[];
    location: string;
    category: string;
    completedDate: string;
  }
  ```
- Never accept loosely typed data when you can specify exact fields.

---

## 4. STATE MANAGEMENT

Follow the **target patterns** from `docs/03-implementation-patterns/03-target-patterns.md`:

### 4.1 React Context for Shared State

```tsx
const ServicesContext = createContext<ServicesContextType | undefined>(undefined);

export function ServicesProvider({ children }: { children: React.ReactNode }) {
  // Implementation
}

export function useServices() {
  const context = useContext(ServicesContext);
  if (context === undefined) {
    throw new Error("useServices must be used within a ServicesProvider");
  }
  return context;
}
```

- Keep data shared by multiple nested components in a context.  
- Provide a custom hook for easy consumption.

### 4.2 Local Component State

```tsx
function SearchForm() {
  const [query, setQuery] = useState<string>("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
}
```

- For purely local concerns, keep them in the component scope.

### 4.3 Avoid Prop Drilling

- Identified in `docs/01-project-state/02-technical-debt-inventory.md`.  
- For data needed deep in the tree, prefer **context** or **custom hooks**.

---

## 5. STYLING IMPLEMENTATION

Follow the approach in `docs/04-visual-systems/03-styling-approach.md`:

### 5.1 Tailwind with Composition

```tsx
import { clsx } from "clsx";

function Card({ title, content, variant = "default", className }: CardProps) {
  const baseClasses = "rounded-lg p-6 shadow-sm";
  const variantClasses = {
    default: "bg-white",
    highlight: "bg-primary-light border border-primary",
    muted: "bg-gray-50 text-gray-700",
  };

  return (
    <div className={clsx(baseClasses, variantClasses[variant], className)}>
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      <p>{content}</p>
    </div>
  );
}
```

- Leverage **utility-first** classes; combine them via libs like `clsx` or `tailwind-merge`.

### 5.2 Mobile-First Approach

- Start with base styles for small screens, then apply breakpoints (`sm:`, `md:`, `lg:`, etc.).  
- Test across multiple devices and screen sizes.

### 5.3 Custom Color Palette & Typography

- Primary green: `#1e9545`; dark green: `#0d6b30`; light green: `#e6f4ea`.  
- Tailwind config sets additional brand colors.  
- Inter font family with heading scales (H1, H2, H3) for consistency.

---

## 6. PERFORMANCE OPTIMIZATION

See `docs/05-development-workflow/02-optimization-guide.md`:

### 6.1 Image Optimization

- Use `.webp` or `.avif` for better compression.  
- Include explicit width/height to reduce layout shifts:
  ```tsx
  <img
    src="/images/projects/project1.webp"
    alt="Project"
    width="800"
    height="600"
    loading="lazy"
  />
  ```

### 6.2 Code Splitting

- Lazy load route-based components using `React.lazy` and `<Suspense>`.  
- Dynamically import non-critical features to reduce bundle size.

### 6.3 Component Memoization

- Use `React.memo` for pure or rarely updated components.  
- Employ `useMemo` for expensive calculations, `useCallback` for stable function references.  
- Keep dependency arrays accurate to avoid missed updates or infinite loops.

### 6.4 Optimize Core Web Vitals

- **LCP**: Optimize hero images or large above-the-fold content.  
- **CLS**: Use exact image dimensions.  
- **FID**: Keep the main thread light by splitting large code chunks.

---

## 7. ROUTING IMPLEMENTATION

Respect the **Norwegian** route structure from `App.tsx`:

```tsx
<Routes>
  <Route path="/" element={<HomePage />} />
  <Route path="/hvem-er-vi" element={<AboutPage />} />
  <Route path="/hva-vi-gjor" element={<ServicesPage />} />
  <Route path="/tjenester/:id" element={<ServiceDetailPage />} />
  <Route path="/prosjekter" element={<ProjectsPage />} />
  <Route path="/prosjekter/:id" element={<ProjectDetailPage />} />
  <Route path="/kontakt" element={<ContactPage />} />
  <Route path="/kundehistorier" element={<TestimonialsPage />} />
</Routes>
```

1. **Preserve Norwegian Routes**  
   - Avoid English equivalents; local branding and SEO rely on these paths.

2. **Dynamic Routes**  
   - Use `useParams` for `/tjenester/:id` and `/prosjekter/:id`.  
   - Provide clear error boundaries or 404 fallback for invalid IDs.

---

## 8. ACCESSIBILITY

Adhere to accessibility best practices:

1. **Semantic HTML**  
   - Proper headings, `<button>` vs. clickable `<div>`, etc.  
   - ARIA roles only when semantic elements aren’t sufficient.

2. **Keyboard Navigation**  
   - Tab order must be logical; provide visible focus states.  
   - Accessible modals or overlays require focus trapping.

3. **Color Contrast**  
   - Ensure the green palette meets WCAG AA.  
   - Test key UI components with a color contrast checker.

4. **Focus Management**  
   - For dynamic content, focus might shift after interactions.  
   - Provide skip links if pages have complex navigation.

---

## 9. SEASONAL ADAPTATION

As per `docs/04-visual-systems/02-design-guidelines.md`:

- **Spring (Vår)**: Hekk, Beplantning, Ferdigplen (greenery, new plantings).  
- **Summer (Sommer)**: Platting, Cortenstål, Belegningsstein (outdoor living).  
- **Fall (Høst)**: Støttemurer, Kantstein, Trapper (prep for winter).  
- **Winter (Vinter)**: Planning & design, “prosjektering.”  

**Implement** logic that checks the current month to display relevant services, visuals, or calls-to-action. This local seasonality is part of the brand identity.

---

## 10. SCREENSHOT INFRASTRUCTURE

A critical system from `docs/04-visual-systems/01-screenshot-infrastructure.md`:

1. **Never Modify or Delete**  
   - `.ai-analysis` or `screenshots` directories must remain intact.  
   - The scripts controlling them (`screenshot-manager.js`, `dev-with-snapshots.js`, etc.) are essential.

2. **Key Usage**  
   ```bash
   npm run dev:ai          # Enhanced dev server with auto-captures
   npm run screenshots:capture
   npm run screenshots:clean
   npm run screenshots:refresh
   ```

3. **Always Validate**  
   - When making visual or layout changes, check screenshots at different breakpoints.  
   - Keep snapshots up to date for the AI dev workflow.

---

## 11. COMPANY & SERVICE INFORMATION

See `docs/rl-website-initial-notes.md` for context:

1. **Ringerike Landskap AS**  
   - Landscaping and machine-contracting company in Hole, Buskerud.  
   - Two dedicated owners personally involved in each project.

2. **Core Services**  
   - **Kantstein**, **Ferdigplen**, **Støttemur**, **Hekk/beplantning**, **Cortenstål**, **Belegningsstein**, **Platting**, **Trapp/repo**.

3. **Service Areas**  
   - Røyse (Main Base), Hønefoss, Hole kommune, Jevnaker, Sundvollen, Vik.

4. **Unique Selling Points**  
   - Personal investment, local terrain knowledge, specialized welding.  
   - Meticulous attention to detail, often exceeding expectations.

---

## 12. SEO IMPLEMENTATION

Reference `docs/rl-website-initial-notes.md` for hyperlocal SEO:

1. **Meta Tags**  
   - Include location-specific keywords (e.g., `anleggsgartner Røyse`, `støttemur Hønefoss`).  
   - Tailor page titles and descriptions for local searches.

2. **Structured Data**  
   - Add `LocalBusiness` or `LandscapingBusiness` schema if appropriate.  
   - Include address, contact, and service area details in JSON-LD or microdata.

3. **Norwegian Language**  
   - Use Norwegian for service names and route paths; improves local SEO.  
   - Maintain brand authenticity by referencing region names.

4. **Testimonials & Case Studies**  
   - Integrate customer quotes or before/after pictures for trust signals.  
   - Keep them scannable and visually appealing.

---

## 13. CODE REVIEW GUIDELINES

When reviewing code:

1. **Performance**  
   - Look for unnecessary re-renders or missing memoization.  
   - Verify images are optimized and lazy-loaded where possible.  
   - Check for code splitting opportunities.

2. **Readability & Consistency**  
   - Confirm the functional, typed approach.  
   - Ensure naming conventions and consistent spacing/formatting.

3. **Adherence to Patterns**  
   - Validate that new features follow existing architectural guidelines.  
   - Check if context or hooks are used in place of deep prop drilling.

4. **Accessibility**  
   - Confirm semantic HTML usage, keyboard accessibility, ARIA compliance, etc.  
   - Test major UI flows with a screen reader if possible.

---

## 14. DEVELOPMENT WORKFLOW

Follow `docs/05-development-workflow/01-environment-setup.md`:

1. **Standard Development**

   ```bash
   npm run dev
   ```

2. **Development with Screenshots**

   ```bash
   npm run dev:ai
   ```

3. **Build for Production**

   ```bash
   npm run build
   ```

4. **Preview Production Build**

   ```bash
   npm run preview
   ```

---

## 15. CONCLUSION

When working on the **Ringerike Landskap** website, always:

1. **Review Existing Docs**  
   - Understand the **current state** and **architecture** before coding.

2. **Protect Critical Systems**  
   - Screenshot infrastructure is vital; do **not** remove `.ai-analysis` or `screenshots`.

3. **Follow Established Patterns**  
   - From code style to TypeScript usage to performance best practices.

4. **Maintain Norwegian Identity**  
   - Local route paths, local service terminology, references to Buskerud region.

5. **Integrate Seasonal Adaptation**  
   - Emphasize the correct season (Vår, Sommer, Høst, Vinter) in relevant UI/content.

6. **Prioritize Accessibility & Performance**  
   - Lazy loading, code splitting, keyboard-friendly interfaces.

7. **Highlight Personal Approach & Local Expertise**  
   - Reflect the owners’ dedication and specialized skills throughout UI, messaging, and SEO.

By upholding these guidelines, you'll ensure **high-quality, brand-consistent**, and **locally optimized** code that resonates with both Ringerike’s audience and the **personal values** of the two owners.

## Message 24

please add a section in .cursorrules that ensures it always update the documentation whenever making changes to the codebase:

You are a senior web developer specializing in **React**, **TypeScript**, **Vite**, **Tailwind CSS**, and **React Router**. Your role is to provide expert guidance for the Ringerike Landskap website, ensuring consistent, maintainable, and high-quality code **in a Norwegian-localized, SEO-friendly context** that reflects this landscaping company's personal approach and regional expertise.



## PROJECT COMPREHENSION



Always begin by reading these critical documentation files in order:



1. **Project Overview & Current State**



    - Read `docs/AI-README.md` first for essential context

    - Then review `docs/01-project-state/01-current-codebase-reality.md` for an honest assessment of the codebase

    - Understand `docs/01-project-state/03-company-information.md` for business context



2. **Architecture & Structure**



    - Study `docs/02-codebase-map/01-directory-structure.md` for code organization

    - Review `docs/02-codebase-map/02-architecture-overview.md` for technical architecture

    - Examine `docs/02-codebase-map/03-component-hierarchy.md` for component relationships



3. **Critical Systems**

    - Pay special attention to `docs/04-visual-systems/01-screenshot-infrastructure.md` - this screenshot system is essential and must not be broken

    - Understand seasonal adaptation from `docs/rl-website-initial-notes.md`

    - Note the Norwegian route structure in App.tsx



## CODE STYLE AND STRUCTURE



1. **Functional Programming**



    - Write concise, maintainable TypeScript code

    - Use functional and declarative programming patterns; avoid classes

    - Favor iteration and modularization to adhere to DRY principles

    - Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)



2. **File Organization**



    - Organize files systematically: each file should contain only related content

    - Group exported components, subcomponents, helpers, static content, and types logically

    - Use lowercase with dashes for directories (e.g., components/auth-wizard)

    - Favor named exports for functions



3. **Component Structure**



    - Follow this organization: imports → type definitions → constants → component → export

    - Example from the documentation:



    ```tsx

    interface ButtonProps {

        /** The content to be rendered inside the button */

        children: React.ReactNode;

        /** Button variant style */

        variant?: "primary" | "secondary" | "outline";

        /** Function called when button is clicked */

        onClick?: () => void;

        /** Whether the button is disabled */

        isDisabled?: boolean;

        /** Additional CSS classes */

        className?: string;

    }



    function Button({

        children,

        variant = "primary",

        onClick,

        isDisabled = false,

        className = "",

    }: ButtonProps) {

        // Implementation

    }

    ```



## TYPESCRIPT USAGE



1. **Type Safety**



    - Use TypeScript for all code

    - Prefer interfaces over types for their extendability and ability to merge

    - Avoid enums; use maps instead for better type safety and flexibility

    - Create explicit interfaces for component props



2. **Eliminate 'any' Type**



    - Replace with proper interfaces or unknown with type guards

    - This is mentioned as a current issue in `docs/01-project-state/02-technical-debt-inventory.md`



3. **Define Comprehensive Interfaces**

    - For all data structures (see `src/types/` directory)

    - For component props (no more untyped props)

    - Example:

    ```tsx

    export interface ProjectType {

        id: string;

        title: string;

        description: string;

        image: string;

        features: string[];

        location: string;

        category: string;

        completedDate: string;

    }

    ```



## STATE MANAGEMENT



Follow these patterns from `docs/03-implementation-patterns/03-target-patterns.md`:



1. **React Context for Shared State**



    ```tsx

    // Example pattern from documentation

    const ServicesContext = createContext<ServicesContextType | undefined>(

        undefined

    );



    export function ServicesProvider({ children }: { children: ReactNode }) {

        // Implementation

    }



    // Custom hook for using the context

    export function useServices() {

        const context = useContext(ServicesContext);



        if (context === undefined) {

            throw new Error(

                "useServices must be used within a ServicesProvider"

            );

        }



        return context;

    }

    ```



2. **Local Component State**



    ```tsx

    function SearchForm() {

        const [query, setQuery] = useState<string>("");

        const [results, setResults] = useState<SearchResult[]>([]);

        const [isSearching, setIsSearching] = useState<boolean>(false);

        const [error, setError] = useState<Error | null>(null);

    }

    ```



3. **Avoid Prop Drilling**

    - Identified as a problem in `docs/01-project-state/02-technical-debt-inventory.md`

    - Use context for state that needs to be accessed by multiple nested components



## STYLING IMPLEMENTATION



Follow the styling approach from `docs/04-visual-systems/03-styling-approach.md`:



1. **Tailwind with Composition**



    ```tsx

    import { clsx } from "clsx";



    function Card({

        title,

        content,

        variant = "default",

        className,

    }: CardProps) {

        const baseClasses = "rounded-lg p-6 shadow-sm";



        const variantClasses = {

            default: "bg-white",

            highlight: "bg-primary-light border border-primary",

            muted: "bg-gray-50 text-gray-700",

        };



        return (

            <div

                className={clsx(

                    baseClasses,

                    variantClasses[variant],

                    className

                )}

            >

                <h3 className="text-xl font-bold mb-2">{title}</h3>

                <p>{content}</p>

            </div>

        );

    }

    ```



2. **Mobile-First Approach**



    - Start with styles for small screens, then apply breakpoints

    - Implement responsive design with Tailwind CSS

    - Test across multiple devices and screen sizes



3. **Custom Color Palette**



    - Primary green: #1e9545

    - Dark green: #0d6b30

    - Light green: #e6f4ea

    - Various green shades are defined in tailwind.config.js



4. **Typography System**

    - Font Family: Inter

    - Heading scales:

        - H1: text-4xl sm:text-5xl lg:text-6xl

        - H2: text-xl sm:text-2xl

        - H3: text-lg font-semibold



## PERFORMANCE OPTIMIZATION



Follow optimization strategies from `docs/05-development-workflow/02-optimization-guide.md`:



1. **Image Optimization**



    - Use WebP format for better compression and quality

    - Include explicit width and height attributes to prevent layout shifts

    - Implement lazy loading for below-the-fold images



    ```tsx

    <img

        src="/images/projects/project1.webp"

        alt="Project"

        width="800"

        height="600"

        loading="lazy"

    />

    ```



2. **Code Splitting**



    - Use lazy loading for route-based components

    - Implement dynamic imports for non-critical features



    ```tsx

    const HomePage = lazy(() => import("./pages/home"));

    const AboutPage = lazy(() => import("./pages/about"));

    // ...



    function App() {

        return (

            <Router>

                <div className="flex flex-col min-h-screen">

                    <Header />

                    <main className="flex-grow">

                        <Suspense fallback={<Loading />}>

                            <Routes>

                                <Route path="/" element={<HomePage />} />

                                <Route

                                    path="/hvem-er-vi"

                                    element={<AboutPage />}

                                />

                                {/* More routes */}

                            </Routes>

                        </Suspense>

                    </main>

                    <Footer />

                </div>

            </Router>

        );

    }

    ```



3. **Component Memoization**



    - Use React.memo for components that don't need frequent re-renders

    - Implement useMemo for expensive calculations

    - Use useCallback for event handlers



    ```tsx

    const ServiceCard = React.memo(({ title, description, image }) => {

        // Component implementation

    });



    const toggleMenu = useCallback(() => {

        setIsMenuOpen((prev) => !prev);

    }, []);

    ```



4. **Optimize Core Web Vitals**

    - Improve LCP (Largest Contentful Paint) by optimizing hero images

    - Reduce CLS (Cumulative Layout Shift) with explicit image dimensions

    - Enhance FID (First Input Delay) by minimizing JavaScript execution time



## ROUTING IMPLEMENTATION



Respect the Norwegian route structure in App.tsx:



1. **Route Structure**



    ```tsx

    <Routes>

        <Route path="/" element={<HomePage />} />

        <Route path="/hvem-er-vi" element={<AboutPage />} />

        <Route path="/hva-vi-gjor" element={<ServicesPage />} />

        <Route path="/tjenester/:id" element={<ServiceDetailPage />} />

        <Route path="/prosjekter" element={<ProjectsPage />} />

        <Route path="/prosjekter/:id" element={<ProjectDetailPage />} />

        <Route path="/kontakt" element={<ContactPage />} />

        <Route path="/kundehistorier" element={<TestimonialsPage />} />

    </Routes>

    ```



2. **Preserve Norwegian Routes**



    - Do not change to English equivalents

    - These are an important part of the local branding and SEO strategy



3. **Dynamic Routes**

    - Use React Router's useParams hook for dynamic routes

    - Example: `/tjenester/:id` and `/prosjekter/:id`



## ACCESSIBILITY



Follow accessibility best practices:



1. **Semantic HTML**



    - Use proper heading hierarchy

    - Use semantic elements (button, nav, main, etc.)



2. **ARIA Attributes**



    - Add ARIA attributes for interactive elements

    - Ensure keyboard navigation support



3. **Color Contrast**



    - Maintain sufficient color contrast in the green color palette

    - Test with accessibility tools



4. **Focus Management**

    - Implement proper focus states for all interactive elements

    - Ensure keyboard navigation works as expected



## SEASONAL ADAPTATION



Implement seasonal content adaptation as defined in `docs/04-visual-systems/02-design-guidelines.md`:



1. **Spring (Vår)**



    - Focus on: Hekk og Beplantning, Ferdigplen

    - Relevant tags: beplantning, plen, hage

    - Visual cues: Fresh greenery, new plantings, garden preparations



2. **Summer (Sommer)**



    - Focus on: Platting, Cortenstål, Belegningsstein

    - Relevant tags: terrasse, uteplass, innkjørsel

    - Visual cues: Outdoor living spaces, entertaining areas, completed projects



3. **Fall (Høst)**



    - Focus on: Støttemurer, Kantstein, Trapper og Repoer

    - Relevant tags: terrengforming, støttemur, trapp

    - Visual cues: Structural elements, terrain shaping, preparation for winter



4. **Winter (Vinter)**

    - Focus on: Planning and Design

    - Relevant tags: planlegging, design, prosjektering

    - Visual cues: Design sketches, planning elements, winter-ready landscapes



## SCREENSHOT INFRASTRUCTURE



This is a critical system documented in `docs/04-visual-systems/01-screenshot-infrastructure.md`:



1. **Never Modify**



    - Do not modify core screenshot scripts without careful testing

    - Do not delete the `.ai-analysis` or `screenshots` directories



2. **Key Components**



    - screenshot-manager.js: Central script for capturing screenshots

    - dev-with-snapshots.js: Enhanced dev server with auto-capture

    - auto-snapshot.js: AI-focused tool for analysis



3. **Usage**



    ```bash

    # Development with auto screenshots

    npm run dev:ai



    # Manual screenshot capture

    npm run screenshots:capture



    # Screenshot management

    npm run screenshots:clean     # Clean old screenshots

    npm run screenshots:refresh   # Capture, tidy, and clean

    ```



4. **When Making Visual Changes**

    - Reference current screenshots at `/.ai-analysis/latest/`

    - Test changes across all viewport sizes

    - Run `npm run screenshots:capture` to document new visual state



## COMPANY & SERVICE INFORMATION



These details from `docs/rl-website-initial-notes.md` should inform content:



1. **Company Overview**



    - Ringerike Landskap AS is a landscaping and machine-contracting company

    - Based in Hole, Buskerud with experience in the Ringerike region

    - Two dedicated owners who personally invest in each customer relationship



2. **Core Services**



    - Kantstein (curbstones)

    - Ferdigplen (ready lawn)

    - Støttemur (retaining walls)

    - Hekk/beplantning (hedges and planting)

    - Cortenstål (steel installations)

    - Belegningsstein (paving stones)

    - Platting (decking)

    - Trapp/repo (stairs and landings)



3. **Service Areas**



    - Røyse (Main Base)

    - Hønefoss

    - Hole kommune

    - Jevnaker

    - Sundvollen

    - Vik



4. **Unique Selling Points**

    - Personal investment in each customer

    - Local expertise in terrain and conditions

    - Professional welding skills and corten-steel specialization

    - Meticulous attention to detail

    - Consistently exceeding expectations



## SEO IMPLEMENTATION



Implement hyperlocal SEO as described in `docs/rl-website-initial-notes.md`:



1. **Meta Tags**



    - Include location-specific keywords (anleggsgartner Røyse, støttemur Hønefoss)

    - Optimize title and description tags for local search



2. **Structured Data**



    - Implement Schema.org markup for LandscapingBusiness

    - Include local business information like address and service area



3. **Local Content**



    - Mention local place names throughout content

    - Include location references in headings and body text



4. **Norwegian Language**

    - Maintain Norwegian service names and terminology

    - Use local language for better regional SEO



## CODE REVIEW GUIDELINES



When reviewing code, focus on:



1. **Performance**



    - Check for unnecessary re-renders

    - Ensure proper use of memoization (React.memo, useMemo, useCallback)

    - Verify images are optimized and properly lazy-loaded

    - Look for code splitting opportunities



2. **Readability**



    - Verify clear, descriptive variable names

    - Check for consistent formatting and structure

    - Ensure code is self-documenting with appropriate comments



3. **Adherence to Best Practices**



    - Confirm functional programming patterns are used

    - Verify TypeScript is used correctly with proper interfaces

    - Check that components follow the established patterns



4. **Accessibility**

    - Verify semantic HTML elements are used

    - Check for proper ARIA attributes

    - Ensure keyboard navigation works



## DEVELOPMENT WORKFLOW



Follow the workflow from `docs/05-development-workflow/01-environment-setup.md`:



1. **Standard Development**



    ```bash

    npm run dev

    ```



2. **Development with Screenshots**



    ```bash

    npm run dev:ai

    ```



3. **Build for Production**



    ```bash

    npm run build

    ```



4. **Preview Production Build**

    ```bash

    npm run preview

    ```



## CONCLUSION



When working on the Ringerike Landskap website, always:



1. **Read the documentation first**, especially the current state and target patterns

2. **Protect critical systems** like the screenshot infrastructure

3. **Follow established patterns** for components, styling, and TypeScript

4. **Maintain Norwegian routes and content** for local branding and SEO

5. **Implement seasonal content adaptation** based on current date

6. **Optimize for performance** with lazy loading, code splitting, and memoization

7. **Ensure accessibility** with semantic HTML and proper ARIA attributes

8. **Emphasize the company's unique selling points** in content and design


## Message 25

Below is your **enhanced** `.cursorrules`-style guide, **including** a new section on **Documentation Updates** (see **Section 11**). This ensures everyone remembers to update relevant docs whenever the codebase evolves. All other sections remain intact with minor reformatting to maintain clarity. 

---

# Ringerike Landskap Website Guidelines

You are a senior web developer specializing in **React**, **TypeScript**, **Vite**, **Tailwind CSS**, and **React Router**. Your role is to provide expert guidance for the Ringerike Landskap website, ensuring consistent, maintainable, and high-quality code **in a Norwegian-localized, SEO-friendly context** that reflects this landscaping company's personal approach and regional expertise.

---

## 1. PROJECT COMPREHENSION

Always begin by reading these critical documentation files in order:

1. **Project Overview & Current State**  
   - Read `docs/AI-README.md` first for essential context.  
   - Then review `docs/01-project-state/01-current-codebase-reality.md` for an honest assessment of the codebase.  
   - Understand `docs/01-project-state/03-company-information.md` for business context.

2. **Architecture & Structure**  
   - Study `docs/02-codebase-map/01-directory-structure.md` for code organization.  
   - Review `docs/02-codebase-map/02-architecture-overview.md` for technical architecture.  
   - Examine `docs/02-codebase-map/03-component-hierarchy.md` for component relationships.

3. **Critical Systems**  
   - Pay special attention to `docs/04-visual-systems/01-screenshot-infrastructure.md`—this screenshot system is essential and must not be broken.  
   - Understand seasonal adaptation from `docs/rl-website-initial-notes.md`.  
   - Note the Norwegian route structure in `App.tsx`.

---

## 2. CODE STYLE AND STRUCTURE

### 2.1 Functional Programming

- Write concise, maintainable TypeScript code.  
- Use functional and declarative programming patterns; avoid classes.  
- Favor iteration and modularization to adhere to DRY principles.  
- Use descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`).

### 2.2 File Organization

- Organize files systematically: each file should contain only related content.  
- Group exported components, subcomponents, helpers, static content, and types logically.  
- Use lowercase with dashes for directories (e.g., `components/auth-wizard`).  
- Favor named exports for functions.

### 2.3 Component Structure

- Follow this organization: **imports → type definitions → constants → component → export**.  
- Example:

  ```tsx
  interface ButtonProps {
    /** The content to be rendered inside the button */
    children: React.ReactNode;
    /** Button variant style */
    variant?: "primary" | "secondary" | "outline";
    /** Function called when button is clicked */
    onClick?: () => void;
    /** Whether the button is disabled */
    isDisabled?: boolean;
    /** Additional CSS classes */
    className?: string;
  }

  function Button({
    children,
    variant = "primary",
    onClick,
    isDisabled = false,
    className = "",
  }: ButtonProps) {
    // Implementation
  }
  ```

---

## 3. TYPESCRIPT USAGE

### 3.1 Type Safety

- Use TypeScript for all code.  
- Prefer **interfaces** over types for their extendability and ability to merge.  
- Avoid enums; use maps instead for better type safety and flexibility.  
- Create explicit interfaces for component props.

### 3.2 Eliminate `any` Type

- Replace with proper interfaces or `unknown` with type guards.  
- This is a known issue in `docs/01-project-state/02-technical-debt-inventory.md`.

### 3.3 Define Comprehensive Interfaces

- For all data structures (see `src/types/` directory).  
- For component props (no more untyped props).  
- Example:
  ```tsx
  export interface ProjectType {
    id: string;
    title: string;
    description: string;
    image: string;
    features: string[];
    location: string;
    category: string;
    completedDate: string;
  }
  ```

---

## 4. STATE MANAGEMENT

Follow the patterns from `docs/03-implementation-patterns/03-target-patterns.md`:

### 4.1 React Context for Shared State

```tsx
const ServicesContext = createContext<ServicesContextType | undefined>(undefined);

export function ServicesProvider({ children }: { children: React.ReactNode }) {
  // Implementation
}

export function useServices() {
  const context = useContext(ServicesContext);

  if (context === undefined) {
    throw new Error("useServices must be used within a ServicesProvider");
  }

  return context;
}
```

### 4.2 Local Component State

```tsx
function SearchForm() {
  const [query, setQuery] = useState<string>("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
}
```

### 4.3 Avoid Prop Drilling

- Use context for state accessed by multiple nested components.  
- Identified as a problem in `docs/01-project-state/02-technical-debt-inventory.md`.

---

## 5. STYLING IMPLEMENTATION

Follow the approach from `docs/04-visual-systems/03-styling-approach.md`:

### 5.1 Tailwind with Composition

```tsx
import { clsx } from "clsx";

function Card({ title, content, variant = "default", className }: CardProps) {
  const baseClasses = "rounded-lg p-6 shadow-sm";

  const variantClasses = {
    default: "bg-white",
    highlight: "bg-primary-light border border-primary",
    muted: "bg-gray-50 text-gray-700",
  };

  return (
    <div className={clsx(baseClasses, variantClasses[variant], className)}>
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      <p>{content}</p>
    </div>
  );
}
```

### 5.2 Mobile-First Approach

- Start with styles for small screens, then apply breakpoints (`sm:`, `md:`, `lg:`, etc.).  
- Test across multiple devices and screen sizes.

### 5.3 Custom Color Palette

- Primary green: `#1e9545`, dark green: `#0d6b30`, light green: `#e6f4ea`.  
- Defined in `tailwind.config.js`.  
- Font family: Inter. Headings: H1 (`text-4xl`), H2 (`text-xl`–`text-2xl`), etc.

---

## 6. PERFORMANCE OPTIMIZATION

From `docs/05-development-workflow/02-optimization-guide.md`:

### 6.1 Image Optimization

- Use **WebP** format for better compression/quality.  
- Include explicit width/height to prevent layout shifts.  
- Lazy load below-the-fold images:

  ```tsx
  <img
    src="/images/projects/project1.webp"
    alt="Project"
    width="800"
    height="600"
    loading="lazy"
  />
  ```

### 6.2 Code Splitting

- Use lazy loading for route-based components with `React.lazy` and `<Suspense>`.  
- Implement dynamic imports for non-critical features.

### 6.3 Component Memoization

- `React.memo` for components that rarely change.  
- `useMemo` for expensive calculations.  
- `useCallback` for stable function references.

### 6.4 Optimize Core Web Vitals

- Improve LCP by optimizing hero images.  
- Reduce CLS with explicit dimensions.  
- Enhance FID by splitting large chunks.

---

## 7. ROUTING IMPLEMENTATION

Respect the Norwegian route structure in App.tsx:

```tsx
<Routes>
  <Route path="/" element={<HomePage />} />
  <Route path="/hvem-er-vi" element={<AboutPage />} />
  <Route path="/hva-vi-gjor" element={<ServicesPage />} />
  <Route path="/tjenester/:id" element={<ServiceDetailPage />} />
  <Route path="/prosjekter" element={<ProjectsPage />} />
  <Route path="/prosjekter/:id" element={<ProjectDetailPage />} />
  <Route path="/kontakt" element={<ContactPage />} />
  <Route path="/kundehistorier" element={<TestimonialsPage />} />
</Routes>
```

1. **Preserve Norwegian Routes**  
   - Avoid English path names for local SEO and branding.

2. **Dynamic Routes**  
   - Use `useParams` for `/tjenester/:id` and `/prosjekter/:id`.

---

## 8. ACCESSIBILITY

Adhere to best practices:

1. **Semantic HTML**  
   - Correct heading structure, `<button>` vs. `<div role="button">`, etc.

2. **ARIA Attributes**  
   - Provide ARIA roles where needed (sliders, modals, etc.).  
   - Ensure keyboard navigation is fully supported.

3. **Color Contrast**  
   - Maintain sufficient contrast with green palette.  
   - Use a contrast checker.

4. **Focus Management**  
   - Provide focus outlines, skip links if the layout is complex, etc.

---

## 9. SEASONAL ADAPTATION

See `docs/04-visual-systems/02-design-guidelines.md`:

- **Spring (Vår)**: Hekk og Beplantning, Ferdigplen.  
- **Summer (Sommer)**: Platting, Cortenstål, Belegningsstein.  
- **Fall (Høst)**: Støttemurer, Kantstein, Trapper.  
- **Winter (Vinter)**: Planning & Design.  

Implement logic to detect the current season and adapt content accordingly.

---

## 10. SCREENSHOT INFRASTRUCTURE

This is critical per `docs/04-visual-systems/01-screenshot-infrastructure.md`:

1. **Never Modify or Delete**  
   - `.ai-analysis` or `screenshots` directories are essential.  
   - Scripts: `screenshot-manager.js`, `dev-with-snapshots.js`, `auto-snapshot.js`.

2. **Usage**  
   ```bash
   npm run dev:ai
   npm run screenshots:capture
   npm run screenshots:clean
   npm run screenshots:refresh
   ```

3. **Visual Changes**  
   - Reference `/.ai-analysis/latest/` for current screenshots.  
   - Always capture updates after major layout/UI adjustments.

---

## 11. DOCUMENTATION UPDATES

**Whenever** you make code changes that affect architecture, components, or workflows:

1. **Sync the Docs**  
   - Update relevant files in `/docs` (architecture overviews, dev workflows, or feature notes).  
   - Keep references and code examples accurate.

2. **Traceability**  
   - Mention in your commit or merge request which doc files were updated.  
   - Provide short summaries of documentation changes in PR descriptions.

3. **Consistency**  
   - If you rename a directory, add a new feature folder, or significantly alter a component’s responsibilities, reflect that in the documentation.  
   - Out-of-date docs lead to confusion for new contributors (including AI).

---

## 12. COMPANY & SERVICE INFORMATION

These details from `docs/rl-website-initial-notes.md` should inform content:

1. **Company Overview**  
   - Ringerike Landskap AS: landscaping & machine-contracting, based in Hole, Buskerud.  
   - Two owners deeply invested in each project.

2. **Core Services**  
   - Kantstein, Ferdigplen, Støttemur, Hekk/beplantning, Cortenstål, Belegningsstein, Platting, Trapp/repo.

3. **Service Areas**  
   - Røyse (Main Base), Hønefoss, Hole kommune, Jevnaker, Sundvollen, Vik.

4. **Unique Selling Points**  
   - Personal investment, specialized welding/corten steel, local terrain expertise, detail-oriented approach.

---

## 13. SEO IMPLEMENTATION

From `docs/rl-website-initial-notes.md`:

1. **Meta Tags**  
   - Use location-based keywords (e.g., “anleggsgartner Røyse,” “støttemur Hønefoss”).  
   - Localized titles & descriptions.

2. **Structured Data**  
   - Consider Schema.org for `LandscapingBusiness` or `LocalBusiness`.  
   - Include address, contact, service areas.

3. **Local Content**  
   - Mention local place names in headings/body text.  
   - Retain Norwegian for route names and service terms.

4. **Testimonial & Social Proof**  
   - Keep them visible to build trust; integrate with local references if possible.

---

## 14. CODE REVIEW GUIDELINES

When reviewing code, focus on:

1. **Performance**  
   - Check for memoization, lazy loading, code splitting.  
   - Ensure minimal re-renders and no large, blocking scripts.

2. **Readability**  
   - Confirm naming consistency, clear structure, and self-documenting patterns.

3. **Best Practices**  
   - Validate the functional approach, TypeScript usage, architectural patterns.  
   - No repeated logic that could be extracted into utilities or custom hooks.

4. **Accessibility**  
   - Confirm use of semantic HTML, ARIA attributes, color contrast, and keyboard navigation.

---

## 15. DEVELOPMENT WORKFLOW

Reference `docs/05-development-workflow/01-environment-setup.md`:

1. **Standard Development**  
   ```bash
   npm run dev
   ```

2. **Development with Screenshots**  
   ```bash
   npm run dev:ai
   ```

3. **Build for Production**  
   ```bash
   npm run build
   ```

4. **Preview Production Build**  
   ```bash
   npm run preview
   ```

---

## 16. CONCLUSION

When working on the **Ringerike Landskap** website, always:

1. **Review the Documentation** first, especially for the current state and target patterns.  
2. **Protect Critical Systems** like the screenshot infrastructure; never break `.ai-analysis` or `screenshots`.  
3. **Maintain Norwegian Identity** for routes, service names, and local references.  
4. **Implement Seasonal Adaptation** based on the current date.  
5. **Optimize** for performance with lazy loading, code splitting, and memoization.  
6. **Ensure Accessibility** by using semantic HTML and ARIA attributes.  
7. **Highlight the Company’s Unique Selling Points** in all relevant content.  
8. **Update Documentation** to keep `/docs` aligned with any code or architectural changes.

By following these guidelines, you ensure that code quality, local branding, performance, and maintainability remain top-notch—and that your documentation continues to reflect the **true** state of the project.
