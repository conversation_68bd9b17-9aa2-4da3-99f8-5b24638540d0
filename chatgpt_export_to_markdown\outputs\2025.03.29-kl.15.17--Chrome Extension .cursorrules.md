# Chrome Extension .cursorrules

Conversation ID: 67e80101-e858-8008-a860-6e0d70899589

## Message 1



**Meta Context:**

- Personal workflow extension for chrome to automatically deal with tabs and bookmarks.

- Intentionally streamlined to create ideal initial conditions for interacting with autonomous coding platforms (e.g., `cursor.ai`, `bolt.new`, `v0.dev`).

- Prioritizes minimalism, performance, and clarity to avoid unnecessary complexity and cognitive load.



**Category:**

- Chrome Extension



**Extension Name:**

- `app_chrome_nucnuc`



**Extension Description:**

- Elegantly simple Chrome extension that optimizes tab and bookmark organizing automatically.



**Extension Goals:**

- Provide quick-access bookmark handling tailored to frequent interactions with LLM coding tools.

- Maintain inherent simplicity in both functionality and user experience, ensuring effortless usability and intuitive interactions.



**Design Principles:**

- Minimalistic UI for reduced visual noise.

- UX that enables full and easy control through only using the mouse.

- Clear, direct functionality without hidden complexity.

- Optimized workflows specifically tuned for coding-focused LLM applications.



---





Context:

```

[Cursor AI](https://cursor.sh/) is an AI-powered code editor. `.cursorrules` files define custom rules for Cursor AI to follow when generating code, allowing you to tailor its behavior to your specific needs and preferences.



## Why .cursorrules?

`.cursorrules` is a powerful feature in Cursor AI that allows developers to define project-specific instructions for the AI. Here's why you might want to use it:

1. **Customized AI Behavior**: `.cursorrules` files help tailor the AI's responses to your project's specific needs, ensuring more relevant and accurate code suggestions.

2. **Consistency**: By defining coding standards and best practices in your `.cursorrules` file, you can ensure that the AI generates code that aligns with your project's style guidelines.

3. **Context Awareness**: You can provide the AI with important context about your project, such as commonly used methods, architectural decisions, or specific libraries, leading to more informed code generation.

4. **Improved Productivity**: With well-defined rules, the AI can generate code that requires less manual editing, speeding up your development process.

5. **Team Alignment**: For team projects, a shared `.cursorrules` file ensures that all team members receive consistent AI assistance, promoting cohesion in coding practices.

6. **Project-Specific Knowledge**: You can include information about your project's structure, dependencies, or unique requirements, helping the AI to provide more accurate and relevant suggestions.



By creating a `.cursorrules` file in your project's root directory, you can leverage these benefits and enhance your coding experience with Cursor AI.

```



In preparing for this project I have gathered 9 different variations of the .cursorrules, please help the chance of producing a `.cursorrules` that will be able to concistently be able produce full working chrome extensions, and use it to produce one **final** `.cursorrules`:





    # Dir `alternatives`



    ### File Structure



    ```

    ├── _01_.cursorrules.yaml

    ├── _02_.cursorrules.json

    ├── _03_.cursorrules.yaml

    ├── _04_.cursorrules.yaml

    ├── _05_.cursorrules.yaml

    ├── _06_.cursorrules.yaml

    ├── _07_.cursorrules.md

    ├── _08_.cursorrules.yaml

    └── _09_.cursorrules.yaml

    ```



    ---



    #### `_01_.cursorrules.yaml`



    ```yaml

        # .cursorrules for app_chrome_nucnuc

        # Cursor AI Rule File for Optimized Tab & Bookmark Management Chrome Extension

        # Manifest V3, minimalist, performance-oriented, LLM-friendly.



        project:

          name: "app_chrome_nucnuc"

          description: |

            Elegantly simple Chrome extension optimizing tab and bookmark management,

            specifically engineered to provide ideal initial conditions and frictionless

            interactions with autonomous LLM-coding agents (e.g., v0.dev, cursor.ai, bolt.new).



        metaContext:

          purpose: |

            Ensure optimal compatibility with autonomous LLM coding platforms by reducing cognitive load,

            minimizing workflow friction, and eliminating unnecessary complexity.



        codingStandards:

          language: "JavaScript"

          syntax: "ES2022+"

          preferred:

            - ES Modules (import/export)

            - const/let (no var)

            - async/await pattern for Chrome API calls

            - Concise JSDoc-style documentation on non-trivial functions

          style:

            indentation: 2 spaces

            quotes: single

            semicolons: false

            maxLineLength: 100

          namingConventions:

            variables: camelCase

            functions: camelCase (clear and descriptive verbs)

            constants: UPPER_SNAKE_CASE

            classes: PascalCase

            cssClasses: kebab-case



        chromeExtensionConfig:

          manifestVersion: 3

          requiredPermissions:

            - tabs

            - tabGroups

            - bookmarks

            - storage

            - alarms

          structure:

            manifest: root directory

            background:

              type: "service_worker"

              scripts:

                - background.js

            popup:

              html: popup.html

              scripts:

                - popup.js

              css:

                - popup.css

            contentScripts: minimal, only if absolutely required



        designPrinciples:

          ui: Minimalist and distraction-free, specifically tailored to developer workflows

          ux: Explicit, predictable, intuitive; frictionless navigation

          performance: Optimized for responsiveness, minimal resource usage

          security: Strict adherence to Chrome MV3 security policies, minimal permissions



        projectStructure:

          root:

            - manifest.json

            - assets (icons, static files)

            - src

              - background

                - background.js (service worker entry)

                - modules

                  - tabManager.js (intelligent grouping, suspension, cleanup)

                  - bookmarkManager.js (rapid-access bookmark handling)

                  - messageHandler.js (inter-component communication)

              - popup

                - popup.html

                - popup.js

                - popup.css

              - shared

                - utils.js (reusable utility functions)

                - constants.js (project-wide constants and configuration)

              - options (optional, for later feature expansion)



        keyFunctionalities:

          tabManagement:

            automation:

              - intelligent domain/context-based tab grouping

              - suspension (discarding) of inactive tabs

              - duplicate tab detection & cleanup

            interaction:

              - clear, minimal UI for quick tab control (popup)

              - one-click actions (e.g., save/restore tab sessions)

          bookmarkHandling:

            automation:

              - rapid bookmark/tag creation (potentially context-aware)

              - bookmark deduplication

            interaction:

              - intuitive search with fuzzy matching capability

              - quick-access bookmarks UI designed for coding workflows

              - optional visual thumbnails or quick previews



        integrationWithLLMAgents:

          principles:

            - Clear modular structure facilitating effortless code generation and adaptation by LLM tools

            - Simple, self-describing functions and clear data-flow for easy parsing by AI

          guidance:

            - Use concise and explicit naming and comments for clarity

            - Ensure clear separation of concerns across components (background, popup, shared utils)



        dependencies:

          external: Minimal; leverage native Chrome APIs extensively

          criteriaForInclusion:

            - Provides clear and significant benefit (e.g., reduces code complexity dramatically)

            - Does not significantly impact performance or bundle size



        developmentWorkflow:

          versionControl:

            system: Git

            repository: https://github.com/yourusername/app_chrome_nucnuc

            branchingStrategy: "Feature-branch workflow"

          incrementalDevelopment:

            - Develop in small, clearly defined increments

            - Ensure each increment is functional and stable before proceeding



        testingStrategy:

          unitTests: Required for utility and background modules

          integrationTests: Recommended for key workflows (tabs/bookmarks lifecycle)

          performanceBenchmarking:

            - Ensure operations complete within <100ms user-perceived response times

            - Regular memory profiling for resource efficiency validation



        cursorAIGenerationPreferences:

          verbosity: "concise"

          modularity: "high"

          abstractionLevel: "low to moderate"

          explicitness: "high"

          aiInteraction:

            - prefer generating complete, self-contained module implementations

            - avoid generating overly abstracted or indirect solutions



        # Exponential Improvement Through Structured Clarity:

        # Each component and rule explicitly interacts with the others, collectively creating

        # a robust, intuitive, and highly maintainable Chrome extension codebase tailored for

        # optimal development with autonomous LLM coding agents.

    ```



    ---



    #### `_02_.cursorrules.json`



    ```json

        {

            // ----------------------------------------------------------------------------

            // PROJECT IDENTITY & GOALS

            // ----------------------------------------------------------------------------

            "project": {

                "name": "app_chrome_nucnuc",

                "description": "A minimalistic, high-performance Chrome extension (Manifest V3) for automated tab and bookmark management, optimized for synergy with LLM-coding agents (v0.dev, cursor.ai, bolt.new). It emphasizes elegance, minimal overhead, and frictionless user experience."

            },



            // ----------------------------------------------------------------------------

            // DESIGN PRINCIPLES

            // ----------------------------------------------------------------------------

            "designPrinciples": {

                "simplicity": "Favor direct, uncluttered solutions for both UI and code architecture. Provide only essential functionality and keep user interactions intuitive.",

                "performance": "Optimize for speed and minimal resource usage (memory, CPU). Ensure quick response times (<100ms) for core features (tab grouping, bookmarking).",

                "clarity": "Write code that is self-explanatory. Add concise comments only where logic is non-obvious. Maintain consistent, discoverable folder structure.",

                "llmIntegration": "Structure code so LLMs can easily parse, extend, and refine features. Provide well-defined modules or hooks for expansions or third-party AI integration.",

                "scalability": "Enable future enhancements (e.g., domain-based grouping, ephemeral session tracking) without large-scale refactoring."

            },



            // ----------------------------------------------------------------------------

            // FILE STRUCTURE & ARCHITECTURE

            // ----------------------------------------------------------------------------

            "architecture": {

                // Example recommended layout:

                "fileStructureExample": [

                    "app_chrome_nucnuc/",

                    "├── manifest.json",

                    "├── src/",

                    "│   ├── background/       // Service Worker logic (e.g., index.js, modules/)",

                    "│   ├── popup/            // UI for the popup (popup.html, popup.js, popup.css, components/)",

                    "│   ├── content/          // Content scripts if needed",

                    "│   ├── options/          // Options page if needed (options.html, .js, .css)",

                    "│   ├── shared/           // Reusable modules, utils, constants",

                    "│   └── ...               // Additional directories for specialized logic (ui/, etc.)",

                    "├── public/               // Static assets like icons or images",

                    "├── dist/                 // Output folder (if using bundler)",

                    "├── package.json          // Dependencies & scripts",

                    "└── .cursorrules          // This file!"

                ],

                "rules": [

                    "Keep background logic strictly in /background. Use ES modules to break large tasks into smaller modules (e.g., tabManager.js, bookmarkManager.js).",

                    "Popup-specific UI code resides in /popup. No direct background logic there. Use messaging if needed.",

                    "Place cross-cutting utilities in /shared/ (e.g., storage helpers, type definitions)."

                ]

            },



            // ----------------------------------------------------------------------------

            // CODE STYLE & CONVENTIONS

            // ----------------------------------------------------------------------------

            "codeStyle": {

                "language": "JavaScript (ES6+). TypeScript is allowed if configured.",

                "namingConventions": {

                    "variablesFunctions": "camelCase",

                    "classes": "PascalCase",

                    "constants": "UPPER_CASE",

                    "cssClasses": "kebab-case"

                },

                "formatting": {

                    "indent": 2,

                    "quotes": "single",

                    "semi": false,

                    "trailingComma": "none",

                    "lineLength": 100

                },

                "comments": {

                    "policy": "Only comment on complex logic or intent (avoid trivial code documentation). Use JSDoc when clarifying function usage or parameters.",

                    "example": "/* Explains purpose, not the obvious. */"

                },

                "asyncHandling": "Prefer async/await for Chrome API calls. Check chrome.runtime.lastError on callbacks to handle exceptions gracefully."

            },



            // ----------------------------------------------------------------------------

            // CHROME EXTENSION (MANIFEST V3) PARAMETERS

            // ----------------------------------------------------------------------------

            "chromeExtension": {

                "manifestVersion": 3,

                "permissions": [

                    "storage",

                    "tabs",

                    "tabGroups",

                    "alarms"

                    // add "bookmarks" if implementing bookmark features

                    // add "scripting" if dynamic script injection is necessary

                ],

                "background": {

                    "serviceWorker": "src/background/index.js",

                    "type": "module"

                },

                "action": {

                    "defaultPopup": "src/popup/popup.html",

                    "defaultIcon": {

                        "16": "public/icons/icon16.png",

                        "48": "public/icons/icon48.png",

                        "128": "public/icons/icon128.png"

                    }

                },

                "optionsPage": {

                    "html": "src/options/options.html"

                },

                "csp": "script-src 'self'; object-src 'self';"

            },



            // ----------------------------------------------------------------------------

            // FUNCTIONAL MODULE GUIDANCE

            // ----------------------------------------------------------------------------

            "functionalAreas": {

                "tabManagement": [

                    "Group or ungroup tabs by domain or user-defined rules.",

                    "Optionally suspend inactive tabs for memory savings (chrome.tabs.discard).",

                    "Implement periodic cleanup (chrome.alarms) or ephemeral session saves."

                ],

                "bookmarkHandling": [

                    "Quickly create, update, or remove bookmarks. Possibly implement tagging or fuzzy search.",

                    "Ensure minimal UI clutter: optional or advanced features behind toggles or secondary screens."

                ],

                "popupUI": [

                    "Keep the popup's interface minimal (no more than 3 primary elements).",

                    "Use straightforward CSS or a lightweight framework if beneficial (e.g., shadcn/ui with Tailwind).",

                    "Offer essential controls (e.g., 'Group Tabs', 'Bookmark All', 'Cleanup Duplicates')—avoid bloat."

                ],

                "messaging": [

                    "Use chrome.runtime.sendMessage or chrome.runtime.onMessage for background–popup communication.",

                    "Maintain JSON-structured messages. Provide a clear 'type' field and 'payload'."

                ]

            },



            // ----------------------------------------------------------------------------

            // LLM INTEGRATION & TEAM COLLABORATION

            // ----------------------------------------------------------------------------

            "llmIntegration": {

                "modularCode": "Write discrete modules so LLMs can add or refine features with minimal ripple effects.",

                "clearHooks": "Surface extension points (like tabManager.js) with exported functions for expansion.",

                "documentation": "Keep high-level README or in-file docstrings explaining each major module’s responsibilities."

            },



            // ----------------------------------------------------------------------------

            // PERFORMANCE & TESTING

            // ----------------------------------------------------------------------------

            "performance": {

                "avoidExcessiveDependencies": true,

                "useAsyncPatterns": true,

                "profileLargeOperations": "If a feature loops over many tabs or bookmarks, ensure efficient code or chunking to maintain responsiveness."

            },

            "testing": {

                "unitTests": "Encourage using a lightweight test runner (Jest or similar) for critical modules.",

                "integration": "Manually test with real tabs/bookmarks to confirm memory usage & user experience. Optionally use Puppeteer for automation."

            },



            // ----------------------------------------------------------------------------

            // DEPLOYMENT & VERSION CONTROL

            // ----------------------------------------------------------------------------

            "versionControl": {

                "system": "Git",

                "branchingStrategy": "Feature branches -> Pull requests -> Merge to main",

                "ciWorkflow": "Optional. If introduced, run lint & minimal tests upon each push."

            },



            // ----------------------------------------------------------------------------

            // CURSOR PROMPT GUIDANCE

            // ----------------------------------------------------------------------------

            "promptGuidance": {

                "responseDetail": "concise",

                "avoidExcessiveImports": true,

                "focusOnCoreFeatures": true,

                "allowIncrementalImprovements": true

            }

        }

    ```



    ---



    #### `_03_.cursorrules.yaml`



    ```yaml

        # .cursorrules for app_chrome_nucnuc Chrome Extension



        # Project Overview

        description: |

          app_chrome_nucnuc is a streamlined Chrome extension designed for efficient tab and bookmark management. It prioritizes minimalism, performance, and clarity to create ideal conditions for interaction with autonomous LLM-coding agents like `v0.dev`, `cursor.ai`, and `bolt.new`.



        # Architecture & Structure

        architecture:

          pattern: "feature-first modular organization"

          fileStructure: |

            src/

              background/             # Service worker logic

                modules/              # Feature-specific modules

                index.ts              # Main service worker entry

              content/                # Content scripts

                components/           # UI components

              popup/                  # Extension popup

                components/           # UI components

              shared/                 # Shared code

                types/                # TypeScript interfaces

                utils/                # Utility functions

              manifest.json           # Extension manifest



        # Coding Standards

        standards:

          general:

            - "Prioritize readability and maintainability over clever code"

            - "Use meaningful variable and function names that describe purpose"

            - "Keep functions small and focused on a single responsibility"

            - "Avoid unnecessary dependencies to maintain a lightweight extension"

            - "Include JSDoc comments for public functions and interfaces"



          javascript:

            - "Use modern ES6+ features where supported by Chrome"

            - "Prefer const over let, avoid var entirely"

            - "Use arrow functions for callbacks and anonymous functions"

            - "Use optional chaining and nullish coalescing for safer code"



          typescript:

            - "Define clear interfaces for all data structures"

            - "Use strict typing, avoid any where possible"

            - "Leverage union types for state management"



        # Performance Guidelines

        performance:

          - "Minimize DOM operations in content scripts"

          - "Use event delegation for multiple similar elements"

          - "Implement lazy loading for non-critical functionality"

          - "Cache expensive computation results"

          - "Optimize storage operations by batching updates"

          - "Implement progressive enhancement for core features"

          - "Target a maximum of 100ms response time for all user interactions"



        # Feature Implementation Preferences

        features:

          tabManagement:

            - "Implement intelligent grouping based on domain patterns and browsing sessions"

            - "Add one-click save/restore for tab collections with customizable naming"

            - "Include background tab suspension for memory optimization"

            - "Provide automatic cleanup of duplicate or inactive tabs"



          bookmarkOrganization:

            - "Create a flat tagging system to replace traditional folder hierarchies"

            - "Implement natural language search with fuzzy matching for quick retrieval"

            - "Add visual preview thumbnails for rapid identification"



          userExperience:

            - "Limit UI to maximum 3 components per view to maintain simplicity"

            - "Implement progressive disclosure patternâ€”expose advanced features only when needed"

            - "Create focus mode to temporarily hide distracting tabs/sites"



        # Chrome API Usage

        chromeAPIs:

          - "Prefer chrome.storage.sync for cross-device functionality"

          - "Use chrome.tabs API for tab manipulation"

          - "Implement chrome.bookmarks API for bookmark management"

          - "Leverage chrome.commands API for keyboard shortcuts"



        # LLM Agent Interaction Guidelines

        llmIntegration:

          generalGuidelines:

            - "Structure code with clear comments and organization for better AI comprehension"

            - "Use descriptive function and variable names to improve AI context understanding"

            - "Maintain modular architecture to allow incremental development with AI assistance"



          agentSpecificGuidelines:

            v0.dev:

              uiFramework: |

                Use React components with shadcn/ui, Tailwind CSS, and Lucide React icons.

              codeBlockType: |

                Use `tsx project= file= type=react` format.

              constraints: |

                Adhere to single-file component structure. Avoid dynamic imports or fetch requests.



            bolt.new:

              promptStrategy: |

                Structure prompts incrementally. Each step should fix issues from the previous step before adding new features.



        # Testing Guidelines

        testing:

          generalTesting:

            - "Write unit tests for core utility functions using Jest or similar frameworks."

            - "Implement integration tests for critical user workflows."



          performanceTesting:

            - "Test memory usage under heavy tab loads."

            - "Ensure smooth operation across various devices."



        # Design Principles

        designPrinciples:

          uiDesign:

            - Minimalistic interface with progressive disclosure of advanced features.



          uxDesign:

            - Ensure predictable behavior across all features.



          accessibility:

            - Follow ARIA standards. Add alt text to all images.

    ```



    ---



    #### `_04_.cursorrules.yaml`



    ```yaml

        # .cursorrules for app_chrome_nucnuc Chrome Extension



        # Project Overview

        description: |

          app_chrome_nucnuc is an elegantly simple Chrome extension that optimizes tab and bookmark management.

          It prioritizes minimalism, performance, and clarity to create ideal conditions for working with LLM-coding agents.

          The extension follows a feature-first, modular architecture to maintain inherent simplicity while providing powerful functionality.



        # Architecture & Structure

        architecture:

          pattern: "feature-first modular organization"

          manifest_version: "Manifest V3"

          fileStructure: |

            src/

              background/             # Service worker logic

                modules/              # Feature-specific modules

                index.ts              # Main service worker entry

              content/                # Content scripts (if needed)

                components/           # UI components

              popup/                  # Extension popup

                components/           # UI components

              shared/                 # Shared code

                types/                # TypeScript interfaces

                utils/                # Utility functions

              manifest.json           # Extension manifest



        # Coding Standards

        standards:

          general:

            - "Prioritize readability and maintainability over clever code"

            - "Use meaningful variable and function names that describe purpose"

            - "Keep functions small and focused on a single responsibility"

            - "Avoid unnecessary dependencies to maintain a lightweight extension"

            - "Include JSDoc comments for public functions and interfaces"



          javascript:

            - "Use modern ES6+ features where supported by Chrome"

            - "Prefer const over let, avoid var entirely"

            - "Use arrow functions for callbacks and anonymous functions"

            - "Use optional chaining and nullish coalescing for safer code"



          typescript:

            - "Define clear interfaces for all data structures"

            - "Use strict typing, avoid any where possible"

            - "Leverage union types for state management"



          ui:

            - "Keep components small and focused on a single responsibility"

            - "Use functional style where appropriate"

            - "Implement progressive disclosure pattern for advanced features"

            - "Limit UI to maximum 3 components per view to maintain simplicity"



        # Performance Guidelines

        performance:

          - "Minimize DOM operations in content scripts"

          - "Use event delegation for multiple similar elements"

          - "Implement lazy loading for non-critical functionality"

          - "Cache expensive computation results"

          - "Optimize storage operations by batching updates"

          - "Target a maximum of 100ms response time for all user interactions"

          - "Implement memory optimization strategies for handling large numbers of tabs"



        # Feature Implementation Preferences

        features:

          tabManagement:

            - "Implement intelligent grouping based on domain patterns and browsing sessions"

            - "Add one-click save/restore for tab collections with customizable naming"

            - "Include background tab suspension for memory optimization"

            - "Provide automatic cleanup of duplicate or inactive tabs"



          bookmarkOrganization:

            - "Create a flat tagging system to replace traditional folder hierarchies"

            - "Implement natural language search with fuzzy matching for quick retrieval"

            - "Add visual preview thumbnails for rapid identification"

            - "Support keyboard shortcuts for mouseless operation"



          userExperience:

            - "Design for accessibility from the start"

            - "Create focus mode to temporarily hide distracting tabs/sites"

            - "Implement progressive disclosure pattern - expose advanced features only when needed"

            - "Optimize for keyboard-first operation"



        # Chrome API Usage

        chromeAPIs:

          - "Prefer chrome.storage.sync for cross-device functionality"

          - "Use chrome.tabs API for tab manipulation"

          - "Implement chrome.bookmarks API for bookmark management"

          - "Leverage chrome.commands API for keyboard shortcuts"

          - "Use chrome.runtime.sendMessage for communication between extension components"

          - "Implement chrome.runtime.onInstalled for initial setup and migrations"



        # Integration with LLM-Coding Agents

        llmIntegration:

          - "Structure code with clear comments and organization for better AI comprehension"

          - "Use descriptive function and variable names to improve AI context understanding"

          - "Maintain modular architecture to allow incremental development with AI assistance"

          - "Include type definitions that help AI generate appropriate code suggestions"

          - "Design components with clear interfaces and minimal dependencies"



        # Testing Guidelines

        testing:

          - "Write unit tests for core utility functions"

          - "Implement integration tests for critical user workflows"

          - "Test for memory leaks in long-running processes"

          - "Verify performance against defined benchmarks"

          - "Include browser compatibility testing"



        # Documentation Requirements

        documentation:

          - "Document all public APIs and functions"

          - "Include rationale for architectural decisions"

          - "Provide usage examples for complex features"

          - "Document performance considerations and optimization strategies"

          - "Maintain a changelog for version updates"



        # Code Organization Principles

        codeOrganization:

          - "Group related functionality into modules"

          - "Separate UI components from business logic"

          - "Use composition over inheritance"

          - "Implement state management appropriate to component complexity"

          - "Extract reusable utilities into shared modules"

          - "Follow the single responsibility principle"



        # Extension Optimization

        optimization:

          memoryUsage:

            - "Implement tab suspension for inactive tabs"

            - "Clean up event listeners when components unmount"

            - "Minimize DOM size and complexity"

            - "Use efficient data structures for storing tab/bookmark information"



          processingEfficiency:

            - "Debounce frequent events like scroll or resize"

            - "Use requestAnimationFrame for visual updates"

            - "Implement pagination or virtualization for large data sets"

            - "Batch DOM operations to minimize layout thrashing"

    ```



    ---



    #### `_05_.cursorrules.yaml`



    ```yaml

        # Project Information

        PROJECT: "app_chrome_nucnuc"

        DESCRIPTION: "A lightweight Chrome extension for optimized tab and bookmark management with minimalist design"

        TYPE: "Chrome Extension (Manifest V3)"



        # Core Technologies

        LANGUAGE: "JavaScript/TypeScript"

        FRAMEWORK: "Chrome Extension API"

        FRONTEND: "HTML, CSS, JavaScript"



        # Architecture

        STRUCTURE:

          - "manifest.json: Properly configured for Manifest V3"

          - "background.js: Service worker for background processes"

          - "popup/: Contains UI components (HTML, CSS, JS)"

          - "utils/: Utility functions and helpers"

          - "services/: API and browser interaction logic"



        # Coding Standards

        STYLE:

          - "Follow Chrome Extension best practices for Manifest V3"

          - "Prefer const over let, avoid var"

          - "Use arrow functions for callbacks"

          - "Maintain 80-100 character line limit"

          - "Use descriptive variable and function names"

          - "Add JSDoc comments for all functions"

          - "Use async/await for asynchronous operations"



        # Performance Guidelines

        OPTIMIZATION:

          - "Minimize DOM operations in popup for responsiveness"

          - "Use Event Pages pattern for background scripts"

          - "Leverage Chrome APIs directly rather than abstractions"

          - "Implement efficient tab data processing algorithms"

          - "Cache results when appropriate to reduce API calls"



        # Feature Implementation

        CORE_FEATURES:

          - "Tab Management: Grouping, suspension, and archiving of inactive tabs"

          - "Bookmark Handling: Quick access and context-aware bookmarking"

          - "Rule-based automation for tab organization"

          - "Low-friction UI with minimal cognitive load"



        # UI/UX Principles

        DESIGN:

          - "Minimalist interface with clear visual hierarchy"

          - "Consistent and predictable interaction patterns"

          - "High contrast, readable text and accessible controls"

          - "Keyboard shortcuts for power users"

          - "Non-intrusive notifications and status indicators"



        # Extension Requirements

        PERMISSIONS:

          - "tabs: For accessing and manipulating browser tabs"

          - "tabGroups: For organizing tabs into groups"

          - "storage: For saving user preferences and rules"

          - "alarms: For scheduling background tasks"

          - "bookmarks: For bookmark management features"



        # Development Workflow

        WORKFLOW:

          - "Test in Chrome's developer mode before packaging"

          - "Verify manifest.json validity before each commit"

          - "Follow semantic versioning for releases"

          - "Prioritize browser compatibility and degradation"

    ```



    ---



    #### `_06_.cursorrules.yaml`



    ```yaml

        # app_chrome_nucnuc Chrome Extension Rules



        ## Project Identity

        - This is a Chrome Extension (Manifest V3) focused on tab and bookmark management

        - Extension should be lightweight, performant, and follow minimalist design principles

        - Code should be optimized for collaboration with LLM-coding agents (v0.dev, cursor.ai, bolt.new)



        ## Code Structure & Organization

        - Maintain clear separation between background service worker, content scripts, and popup UI

        - Place shared utilities in a dedicated `/utils` directory

        - Group related functionality into logical modules

        - Follow a consistent naming convention for all files and functions

        - Use descriptive variable and function names that reflect their purpose



        ## Code Style

        - Use ES6+ features where appropriate

        - Prefer const over let, avoid var

        - Use 2 spaces for indentation

        - Place opening braces on the same line

        - Use semicolons at the end of statements

        - Maximum line length should be 100 characters

        - Use camelCase for variables and functions, PascalCase for classes and components

        - Favor arrow functions for callbacks and anonymous functions



        ## Chrome Extension Best Practices

        - Strictly adhere to Manifest V3 requirements

        - Request only permissions that are absolutely necessary

        - Use event-driven patterns for background service worker

        - Implement proper error handling for all API calls

        - Prefer chrome.storage.local for simple data needs, chrome.storage.sync for user preferences

        - Avoid inline scripts (prohibited in Manifest V3)

        - Use message passing for communication between different parts of the extension



        ## Performance Optimization

        - Minimize DOM manipulations in UI components

        - Lazy-load resources when possible

        - Use debounce/throttle for event handlers that might fire frequently

        - Implement efficient algorithms for tab management features

        - Keep background service worker logic minimal and event-driven

        - Cache results where appropriate to avoid redundant operations



        ## UI/UX Guidelines

        - Design a clean, unobtrusive interface that feels native to Chrome

        - Use consistent spacing, typography, and color schemes

        - Ensure all interactive elements have appropriate hover/focus states

        - Make UI responsive to different popup sizes

        - Implement keyboard shortcuts for common actions

        - Provide clear visual feedback for user actions



        ## Documentation

        - Include JSDoc comments for all functions and complex code blocks

        - Document the purpose of each file at the top

        - Explain non-obvious algorithms or design decisions

        - Include examples for complex API usage

        - Document message formats for inter-script communication



        ## Testing

        - Write unit tests for core utility functions

        - Test tab management logic with various scenarios

        - Ensure the extension works in different Chrome contexts (regular tabs, incognito, etc.)

        - Validate extension behavior across different operating systems



        ## Feature Implementation Details

        - Tab Management:

          - Prioritize efficient algorithms for contextual tab grouping

          - Use chrome.tabs and chrome.tabGroups APIs appropriately

          - Implement tab suspension with proper user notification

          - Design rule-based cleanup to be configurable and non-intrusive

        - Bookmark Handling:

          - Optimize for quick access to frequently used resources

          - Design with awareness of LLM coding platform workflows

          - Use chrome.bookmarks API effectively

          - Consider context-aware suggestions based on current browsing



        ## Security Considerations

        - Follow Content Security Policy best practices

        - Sanitize any user input

        - Be cautious with localStorage/sessionStorage usage

        - Do not store sensitive data

        - Use HTTPS for any external communications



        ## Dependencies

        - Minimize external dependencies

        - Justify any third-party libraries added to the project

        - Favor vanilla JS solutions when reasonable

        - If using external code, verify compatibility with Manifest V3



        ## LLM Agent Interaction Notes

        - Structure code to be easily understood by LLM agents

        - Provide clear component boundaries for incremental development

        - Include detailed comments that explain intent, not just implementation

        - When generating code with agents, prefer complete, functional components over partial implementations

    ```



    ---



    #### `_07_.cursorrules.md`



    ```markdown

        # Project: app_chrome_nucnuc

        # Type: Chrome Extension (Manifest V3)



        # Vision & High-Level Goals

        * Develop an elegantly simple, lightweight, and performant Chrome extension for optimizing personal tab and bookmark management workflows.

        * Core focus: Significantly reduce user cognitive load and workflow friction through minimalist design and intuitive functionality.

        * Target workflow: Efficient interaction with online documentation, research materials, and LLM coding platforms (v0.dev, Cursor, bolt.new).

        * Prioritize seamless integration, user-friendly UX, and establishing optimal initial conditions for development with LLM agents.



        # Core Functionality to Implement

        * **Automated Tab Management:**

            * Implement intelligent tab handling (e.g., contextual/rule-based grouping, automatic suspension/archiving of inactive tabs).

            * Potentially include rule-based cleanup suggestions/actions.

            * Goal: Maintain an uncluttered workspace.

        * **Streamlined Bookmark Handling:**

            * Provide rapid-access and potentially context-aware bookmarking features.

            * Optimize for workflows involving frequent interaction with documentation/research/LLM platforms.



        # Technical Requirements & Constraints

        * **Platform:** Strictly adhere to Chrome Extension Manifest V3 standards.

        * **Technologies:** Use standard HTML, CSS, and JavaScript/TypeScript. Prefer TypeScript for new JavaScript logic unless the file is extremely simple.

        * **Performance:** Prioritize speed and low resource consumption (CPU, memory). Use efficient algorithms and leverage native Chrome APIs (`chrome.*`) extensively.

        * **Permissions:** Ensure `manifest.json` correctly requests necessary permissions as features are developed (placeholders initially: `tabs`, `tabGroups`, `storage`, `alarms`).



        # Code Quality & Style

        * Generate code that is:

            * Clean, clear, and easy to read/understand.

            * Modular (well-organized into logical components/functions).

            * Maintainable (easy to debug, modify, extend).

            * Well-commented, especially for non-obvious logic.

        * Structure code for sustainable flexibility, allowing incremental enhancements with minimal refactoring.



        # UI/UX Principles

        * Design and implement a minimalist, clean, unobtrusive, and distraction-free user interface focused strictly on core tasks.

        * Functionality must be direct, explicit, predictable, and reliable. Avoid hidden complexity or ambiguous interactions.



        # Development Process & LLM Agent Context

        * This project definition aims for optimal interaction with LLM coding agents (Cursor, v0.dev, bolt.new). Generate code accordingly.

        * **Agent Adaptability:** Be mindful of potential target agent requirements:

            * **If generating UI components specifically for v0.dev:** Use React, shadcn/ui, Tailwind CSS, Lucide Icons. Adhere strictly to its required MDX code block formatting (e.g., tsx project="..." file="..." type="react", single-file components, specific import styles). **Only use these if explicitly generating UI via v0.dev context.**

            * **For general development or other agents (like bolt.new):** Standard HTML/CSS/JS/TS is preferred unless React/Tailwind are explicitly requested for a specific part (like the popup). Structure prompts/tasks incrementally.

        * **Initial Development Step:** The first priority is generating the foundational structure:

            * `manifest.json` (Manifest V3 configured, with placeholder permissions).

            * A placeholder background service worker (`background.js` or `background.ts`).

            * A simple placeholder popup (`popup.html`, `popup.css`, `popup.js` or potentially `.tsx` files if React is chosen for the popup).



        # Things to Avoid

        * DO NOT introduce unnecessary complexity in features or code.

        * DO NOT use Manifest V2 APIs or configurations.

        * Avoid adding external libraries unless essential for core functionality or specified for agent compatibility (like UI libs for v0.dev). Prefer native Web APIs and Chrome APIs.

        * Avoid computationally expensive operations in the background script; keep it efficient.

    ```



    ---



    #### `_08_.cursorrules.yaml`



    ```yaml

        # .cursorrules for app_chrome_nucnuc

        # Cursor AI Rule File for Optimized Tab & Bookmark Management Chrome Extension

        # Manifest V3, minimalist, performance-oriented, LLM-friendly.



        project:

          name: "app_chrome_nucnuc"

          description: |

            Elegantly simple Chrome extension optimizing tab and bookmark management,

            specifically engineered to provide ideal initial conditions and frictionless

            interactions with autonomous LLM-coding agents (e.g., v0.dev, cursor.ai, bolt.new).



        metaContext:

          purpose: |

            Ensure optimal compatibility with autonomous LLM coding platforms by reducing cognitive load,

            minimizing workflow friction, and eliminating unnecessary complexity.



        codingStandards:

          language: "JavaScript"

          syntax: "ES2022+"

          preferred:

            - ES Modules (import/export)

            - const/let (no var)

            - async/await pattern for Chrome API calls

            - Concise JSDoc-style documentation on non-trivial functions

          style:

            indentation: 2 spaces

            quotes: single

            semicolons: false

            maxLineLength: 100

          namingConventions:

            variables: camelCase

            functions: camelCase (clear and descriptive verbs)

            constants: UPPER_SNAKE_CASE

            classes: PascalCase

            cssClasses: kebab-case



        chromeExtensionConfig:

          manifestVersion: 3

          requiredPermissions:

            - tabs

            - tabGroups

            - bookmarks

            - storage

            - alarms

          structure:

            manifest: root directory

            background:

              type: "service_worker"

              scripts:

                - background.js

            popup:

              html: popup.html

              scripts:

                - popup.js

              css:

                - popup.css

            contentScripts: minimal, only if absolutely required



        designPrinciples:

          ui: Minimalist and distraction-free, specifically tailored to developer workflows

          ux: Explicit, predictable, intuitive; frictionless navigation

          performance: Optimized for responsiveness, minimal resource usage

          security: Strict adherence to Chrome MV3 security policies, minimal permissions



        projectStructure:

          root:

            - manifest.json

            - assets (icons, static files)

            - src

              - background

                - background.js (service worker entry)

                - modules

                  - tabManager.js (intelligent grouping, suspension, cleanup)

                  - bookmarkManager.js (rapid-access bookmark handling)

                  - messageHandler.js (inter-component communication)

              - popup

                - popup.html

                - popup.js

                - popup.css

              - shared

                - utils.js (reusable utility functions)

                - constants.js (project-wide constants and configuration)

              - options (optional, for later feature expansion)



        keyFunctionalities:

          tabManagement:

            automation:

              - intelligent domain/context-based tab grouping

              - suspension (discarding) of inactive tabs

              - duplicate tab detection & cleanup

            interaction:

              - clear, minimal UI for quick tab control (popup)

              - one-click actions (e.g., save/restore tab sessions)

          bookmarkHandling:

            automation:

              - rapid bookmark/tag creation (potentially context-aware)

              - bookmark deduplication

            interaction:

              - intuitive search with fuzzy matching capability

              - quick-access bookmarks UI designed for coding workflows

              - optional visual thumbnails or quick previews



        integrationWithLLMAgents:

          principles:

            - Clear modular structure facilitating effortless code generation and adaptation by LLM tools

            - Simple, self-describing functions and clear data-flow for easy parsing by AI

          guidance:

            - Use concise and explicit naming and comments for clarity

            - Ensure clear separation of concerns across components (background, popup, shared utils)



        dependencies:

          external: Minimal; leverage native Chrome APIs extensively

          criteriaForInclusion:

            - Provides clear and significant benefit (e.g., reduces code complexity dramatically)

            - Does not significantly impact performance or bundle size



        developmentWorkflow:

          versionControl:

            system: Git

            repository: https://github.com/yourusername/app_chrome_nucnuc

            branchingStrategy: "Feature-branch workflow"

          incrementalDevelopment:

            - Develop in small, clearly defined increments

            - Ensure each increment is functional and stable before proceeding



        testingStrategy:

          unitTests: Required for utility and background modules

          integrationTests: Recommended for key workflows (tabs/bookmarks lifecycle)

          performanceBenchmarking:

            - Ensure operations complete within <100ms user-perceived response times

            - Regular memory profiling for resource efficiency validation



        cursorAIGenerationPreferences:

          verbosity: "concise"

          modularity: "high"

          abstractionLevel: "low to moderate"

          explicitness: "high"

          aiInteraction:

            - prefer generating complete, self-contained module implementations

            - avoid generating overly abstracted or indirect solutions



        # Exponential Improvement Through Structured Clarity:

        # Each component and rule explicitly interacts with the others, collectively creating

        # a robust, intuitive, and highly maintainable Chrome extension codebase tailored for

        # optimal development with autonomous LLM coding agents.

    ```



    ---



    #### `_09_.cursorrules.yaml`



    ```yaml

        # app_chrome_nucnuc.cursorrules.yaml

        # Consolidated configuration for reliable Chrome extension development

        # Manifest V3 | AI-Agent Optimized | Performance-Centric



        project:

          name: "app_chrome_nucnuc"

          type: "chrome-extension"

          manifest_version: 3

          description: |

            Minimalist tab/bookmark manager with AI-agent optimized architecture,

            enforcing Manifest V3 compliance and performance-first principles[1][3][4]



        structure:

          root:

            - manifest.json

            - src/

              - background/

                - service_worker.ts

                - modules/

                  - tab_manager.ts

                  - bookmark_handler.ts

              - popup/

                - components/

                  - TabGroup.tsx

                  - BookmarkSearch.tsx

                - styles/

                  - main.css

          globs:

            - "**/*.ts"

            - "**/*.tsx"

            - "**/*.css"[1][3][4]



        manifest:

          requirements:

            service_worker: "src/background/service_worker.ts"

            permissions:

              required:

                - tabs

                - tabGroups

                - bookmarks

                - storage

                - alarms

              prohibited:

                - webRequest

                -

          security:

            csp: "script-src 'self'; object-src 'none'"

            validation:

              - pre-commit: "chrome-ext validate --manifest"

              - post-build: "lighthouse-extension audit"[2][4]



        coding:

          standards:

            language: "TypeScript (strict)"

            style:

              indent: 2

              quotes: "single"

              semicolons: false

            patterns:

              - "ES Modules (import/export)"

              - "Async/await for Chrome APIs"

              - "Optional chaining & nullish coalescing"

          types:

            interfaces:

              - "TabGroup: { id: string, tabs: chrome.tabs.Tab[] }"

              - "BookmarkConfig: { title: string, url: string, tags: string[] }"[3][4]



        performance:

          constraints:

            memory:

                      {{children}}



                  )

                }}

              requirements:

                - "Single-file components"

                - "Explicit prop typing"



            bolt.new:

              phases:

                1: "Scaffold core manifest"

                2: "Implement tab management"

                3: "Add bookmark integration"

              error_handling: "Fail-fast with debug snapshots"[2][4]



        validation:

          unit:

            - "Jest coverage >85%"

            - "Chrome API mocks"

          integration:

            - "500-tab stress test"

            - "Cross-origin isolation checks"

          performance:

            - "Memory leak detection"

            - "Background thread analysis"[1][3]



        workflow:

          hooks:

            pre-commit:

              - "eslint --fix"

              - "manifest-v3-validate"

            post-merge:

              - "type-check"

              - "perf-benchmark"

          deployment:

            - "semver versioning"

            - "chrome-web-store upload"[2][4]



        documentation:

          jsdoc:

            - "Public API functions"

            - "Complex algorithms"

          architecture:

            - "module-responsibility.md"

            - "data-flow-diagrams.md"

          changelog: "keepachangelog.com format"[3][4]



        # Quality Enforcement Guards

        guarantees:

          - "100% Manifest V3 compliance"

          - "Zero unsafe-eval CSP violations"

          - "Sub-100MB memory footprint"

          - "TypeScript strict null checks"

          - "Accessibility (WCAG AA compliance)"[1][2][3][4]

    ```




## Message 2

**Analyzing guidelines**

Piecing together the 9 `.cursorrules` files to craft a unified, practical set of guidelines for generating a working Chrome extension. This involves noting key points, uniqueness, and potential conflicts.

**Creating a comprehensive YAML**

I’m crafting a detailed `.cursorrules` YAML file, blending project structure, coding standards, testing strategies, performance insights, and extension specifics to craft a unified set of guidelines.

**Compiling the final file**

I’m consolidating the unified `.cursorrules` into a final YAML file, ensuring it addresses all aspects comprehensively while preserving key references and unique instructions.

**Unifying guidelines**

I’m integrating key directives like Manifest V3, coding style preferences, and functional programming, aiming for a concise, unified `.cursorrules.yaml` file that maintains clarity and logical cohesion.

**Weighing options**

I’m piecing together the final `.cursorrules.yaml` file, ensuring it clearly includes details on producing functional Chrome extensions, LLM integration, and adhering to modern coding practices like using JSDoc and avoiding `var`.

**Resolving conflicts**

Choosing "No semicolons" for consistency, aligning with our other settings, and keeping our guidelines streamlined and clear.

## Message 3

Below is one consolidated **`.cursorrules`** file that merges and harmonizes the best ideas from the nine variations you provided. It emphasizes **Manifest V3**, minimalism, performance, clarity, and ease of collaboration with LLM-based coding tools. It should help Cursor AI (and similar platforms) reliably generate a working Chrome extension for tab and bookmark management.

```yaml
# =============================================================================
# FINAL .cursorrules FOR app_chrome_nucnuc
# =============================================================================

project:
  name: "app_chrome_nucnuc"
  description: |
    A minimalist, performance-oriented Chrome extension (Manifest V3) for automated
    tab and bookmark management. Designed to create frictionless user workflows
    and seamlessly integrate with LLM coding agents (e.g., Cursor, v0.dev, bolt.new).

  goals:
    - Provide fast, reliable tab management features (grouping, cleanup, suspension).
    - Offer simple yet effective bookmark organization (quick creation, tagging, fuzzy search).
    - Maintain a clean, distraction-free UI/UX that reduces cognitive load.
    - Ensure optimal structure for straightforward incremental enhancements by LLMs.

# -----------------------------------------------------------------------------
# DESIGN & UX PRINCIPLES
# -----------------------------------------------------------------------------
designPrinciples:
  simplicity:
    - Keep UI and overall code architecture uncluttered.
    - Limit UI components to essential elements (no more than 2–3 main interactive controls in the popup).
  performance:
    - Minimize resource consumption (CPU & memory).
    - Target sub-100ms response times for user actions.
    - Efficiently handle large numbers of open tabs.
  clarity:
    - Write code that is self-documenting whenever possible.
    - Provide concise comments (JSDoc) only for non-trivial logic or architectural decisions.
  accessibility:
    - Adhere to ARIA guidelines for interactive elements.
    - Provide sensible keyboard shortcuts and focus management.

# -----------------------------------------------------------------------------
# EXTENSION CONFIGURATION (MANIFEST V3)
# -----------------------------------------------------------------------------
chromeExtension:
  manifestVersion: 3
  permissions:
    - tabs
    - tabGroups
    - bookmarks
    - storage
    - alarms
  background:
    service_worker: "src/background/background.js"  # or background.ts if using TypeScript
    type: "module"
  action:
    defaultPopup: "src/popup/popup.html"
    defaultIcon:
      "16": "assets/icons/icon16.png"
      "48": "assets/icons/icon48.png"
      "128": "assets/icons/icon128.png"
  optionsPage:
    html: "src/options/options.html"
  contentScripts:
    # Add only if absolutely necessary
    - matches: ["<all_urls>"]
      js: ["src/content/content.js"]
      run_at: "document_idle"
      css: ["src/content/content.css"]
  csp: "script-src 'self'; object-src 'self';"

# -----------------------------------------------------------------------------
# FOLDER & FILE STRUCTURE
# -----------------------------------------------------------------------------
architecture:
  recommendedLayout:
    - manifest.json
    - assets/          # icons, static files
    - src/
      - background/
        - background.js       # Service worker (Manifest V3)
        - modules/
          - tabManager.js     # Tab grouping, cleanup, suspension
          - bookmarkManager.js
          - messageHandler.js # Inter-script communication
      - popup/
        - popup.html
        - popup.js
        - popup.css
      - content/              # Content scripts (if needed)
        - content.js
      - shared/
        - utils.js            # Reusable utility functions
        - constants.js        # Project-wide constants and configurations
      - options/              # Optional options page
        - options.html
        - options.js
        - options.css
    - package.json (if bundling or using external dependencies)
    - .cursorrules (this file)

  rules:
    - Keep background logic in `/background` with modular breakdown (tabManager, bookmarkManager, etc.).
    - For UI, keep the popup self-contained in `/popup`; communicate with background via `chrome.runtime.sendMessage`.
    - Reuse code in `/shared` for cross-cutting utilities or constants.

# -----------------------------------------------------------------------------
# CODING STANDARDS & STYLE GUIDE
# -----------------------------------------------------------------------------
codingStandards:
  language: "JavaScript or TypeScript (strict if TS)"
  esFeatures:
    - "ES Modules (import/export)"
    - "Async/await for Chrome API calls"
    - "Optional chaining, nullish coalescing"
  namingConventions:
    variablesFunctions: "camelCase"
    classes: "PascalCase"
    constants: "UPPER_SNAKE_CASE"
    cssClasses: "kebab-case"
  style:
    indentation: 2
    quotes: "single"
    semicolons: false
    maxLineLength: 100
  comments:
    policy: "JSDoc for public or complex functions; minimal in-line commentary for clarity"
    example: |
      /**
       * Cleanup inactive tabs based on user-defined rules.
       * @param {Array<chrome.tabs.Tab>} tabs
       */
      async function cleanupInactiveTabs(tabs) { ... }

# -----------------------------------------------------------------------------
# KEY FUNCTIONALITY
# -----------------------------------------------------------------------------
features:
  tabManagement:
    automation:
      - Contextual grouping by domain or custom user rules
      - Automatic discarding (suspension) for inactive tabs
      - Duplicate tab detection & optional auto-cleanup
    interaction:
      - One-click grouping, saving/restoring tab sessions
      - Simple popup controls for quick tab actions
  bookmarkHandling:
    automation:
      - Rapid bookmark creation with optional tagging
      - Duplicate bookmark detection
    interaction:
      - Fuzzy search for fast retrieval
      - Basic visual previews or quick link icons
  userExperience:
    - Minimal UI with clear visuals and tooltips
    - Optional advanced features (exposed only when needed)
    - Keyboard shortcuts for power users

# -----------------------------------------------------------------------------
# LLM INTEGRATION & STRUCTURE
# -----------------------------------------------------------------------------
llmIntegration:
  agentReadiness:
    - Keep modules short, focused, and clearly commented
    - Avoid overly abstract solutions that hamper code generation
  recommendedPractices:
    - Provide function boundaries that are easy for AI to extend or replace
    - Maintain descriptive variable/function names (improve AI context)
    - For UI generation with frameworks (e.g., React + Tailwind for v0.dev), keep each component in a single file

# -----------------------------------------------------------------------------
# DEPENDENCIES & EXTENSIBILITY
# -----------------------------------------------------------------------------
dependencies:
  usagePolicy:
    - Prefer native Chrome APIs and vanilla JS for core operations
    - Only add libraries if they significantly reduce complexity or dev time
    - Ensure any library is compatible with Manifest V3 (e.g., no inline scripts)
  external:
    - Avoid large UI frameworks unless specifically needed for a given LLM agent scenario

# -----------------------------------------------------------------------------
# DEVELOPMENT & TESTING WORKFLOW
# -----------------------------------------------------------------------------
developmentWorkflow:
  versionControl:
    system: "Git"
    branching: "Feature-branch -> Pull request -> Merge to main"
  incrementalDevelopment:
    - Build one feature or module at a time
    - Validate each change set in Chrome dev mode
    - Keep changes small and reviewable
  testingStrategy:
    unitTests:
      - Required for core logic (tab management, bookmark routines)
      - Use a lightweight test runner (e.g., Jest)
    integrationTests:
      - Verify tab grouping, bookmark management, and UI flows
    performanceBenchmarking:
      - Stress-test with >100 tabs
      - Check memory usage and CPU load under typical usage
    accessibilityChecks:
      - Validate keyboard navigation and screen reader compatibility

# -----------------------------------------------------------------------------
# SECURITY & PRIVACY
# -----------------------------------------------------------------------------
security:
  csp: "script-src 'self'; object-src 'none';"
  guidelines:
    - No unsafe eval or dynamic code generation
    - Sanitize user inputs to avoid code injection
    - Store only necessary data in chrome.storage (never store sensitive info)
  permissionsMinimization:
    - Only request tabs, tabGroups, bookmarks, storage, alarms if needed
    - Add other permissions strictly on an as-needed basis
  privacy:
    - No external data collection or tracking by default

# -----------------------------------------------------------------------------
# DOCUMENTATION & MAINTENANCE
# -----------------------------------------------------------------------------
documentation:
  structure:
    - "README.md" for project overview, setup, and usage
    - "CHANGELOG.md" for version updates (semantic versioning recommended)
    - Inline JSDoc or TSDoc for public methods/classes
  updates:
    - Keep a clear record of new features and bug fixes
    - Provide usage examples or short demo gifs in the README

# -----------------------------------------------------------------------------
# END OF FILE
# =============================================================================

```

**How to use this file**  
1. Place it at your project’s root (e.g., `.cursorrules` or `.cursorrules.yaml`).  
2. When you prompt Cursor AI (or another LLM agent) to generate code, reference these guidelines.  
3. The LLM should now have a clearer roadmap for how to structure, style, and implement your Chrome extension features consistently.  

This **unified** `.cursorrules` blends all key points from your nine different rule sets—Manifest V3 compliance, minimal permissions, efficient tab/bookmark handling, consistent code style, and easy-to-follow architecture for incremental LLM-driven enhancements.
