# Robocopy Exclude Cache

Conversation ID: 68445e89-0c64-8008-8e9a-d64f751ee4ad

## Message 1

How can robocopy be triggered with an exclude pattern that excludes anything matching **/*cache*/** (including exclusions of subfolders/files)?

## Message 2

To exclude all folders and files that **contain `cache` anywhere in their path**, including nested ones, you can use `robocopy` with the `/XD` (exclude directories) and `/XF` (exclude files) options **along with wildcard patterns**. However, `robocopy` does **not support recursive wildcards** like `**` (as in glob syntax). Instead, you must use partial string matches or wildcard folder names.

Here’s a **robust workaround**:

---

### ✅ Working Syntax

```bash
robocopy "source" "destination" /MIR /XD cache *cache* /XF *cache*
```

---

### 🔍 Explanation

- `/XD cache *cache*`  
  Excludes all folders named `cache` or containing `cache` in their names anywhere in the path.
- `/XF *cache*`  
  Excludes any files with `cache` in their name (including subfolders).
- `*cache*` works because `robocopy` matches substrings with wildcards.

---

### ❗Caveats

- `robocopy` **does not natively support** full-path pattern matching like `**/*cache*/**`.
- It applies patterns to **file and folder names only**, not full relative paths.
- If your folder structure is very deep and you need to skip based on partial paths (like `some/temp/cache/data.txt`), this still works **as long as "cache" is part of the folder or file name.**

---

### 🧪 Example

Given this tree:

```
src/
├── keep/
│   └── file.txt
├── cache/
│   └── remove.txt
└── nested/
    └── .cache/
        └── temp.log
```

Running:

```bash
robocopy src dst /MIR /XD *cache* /XF *cache*
```

Would only copy `keep/file.txt`, skipping the others.

---

Let me know if you want a recursive PowerShell-based solution that allows true path-based exclusions.

## Message 3

I don't want it to match files/folders named e.g. "py_cleanupCacheFiles" (it should be safe)

## Message 4

Thanks for the clarification — you want to exclude **only folders or files that have `/cache/` or `\cache\` somewhere in their path**, **not just names that *contain* the word "cache"** like `py_cleanupCacheFiles`.

Since `robocopy` doesn’t support full relative path matching or regex, but instead matches **name patterns**, here's how to simulate your intent safely:

---

### ✅ **Recommended Solution**

```cmd
robocopy "source" "destination" /MIR /XD cache
```

---

### 🔍 Explanation

- `/XD cache` excludes folders **named exactly `cache`**, at any level of the tree.
- This **does not** exclude folders like `py_cleanupCacheFiles` or `cache_backup`.
- It **does** exclude:
  ```
  /src/cache/
  /src/some/nested/cache/
  ```

- If you also want to exclude files **inside those `cache` folders**, `/XD` is enough — robocopy will skip entire directories.

---

### ✅ If You Also Want to Exclude Files Named Exactly `cache`, but Not `cache123` or `mycache`:

Add:

```cmd
/XF cache
```

---

### ❗Avoid Wildcards Like `*cache*`

Do **not** use `/XD *cache*` or `/XF *cache*` — these match **any name containing `cache`**, including safe ones like `py_cleanupCacheFiles`, which you explicitly want to keep.

---

### 🧪 Test Example

Assume this tree:

```
src/
├── keep/
│   └── py_cleanupCacheFiles.py  ← should be kept
├── cache/
│   └── tmp.txt                  ← should be excluded
└── nested/
    └── cache/
        └── temp.log            ← should be excluded
```

Command:

```cmd
robocopy src dst /MIR /XD cache
```

Only `keep/py_cleanupCacheFiles.py` will be copied.

---

Let me know if you need a more complex exclusion logic (e.g., based on full relative paths). A PowerShell wrapper or prefilter might be more appropriate in that case.
