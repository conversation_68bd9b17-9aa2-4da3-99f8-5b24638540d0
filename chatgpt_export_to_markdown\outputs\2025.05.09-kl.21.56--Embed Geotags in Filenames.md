# Embed Geotags in Filenames

Conversation ID: 681e5dec-ef70-8008-8dce-8cb883d93d9d

## Message 1

after having retrieved geotags from my images, is there any way to embed it into the filename? example:



Processing HEIC: \py__ExtractImageGeotags\IMG_0035.HEIC

Latitude: 59.92319166666667

Longitude: 10.649405555555555

Google Maps: https://www.google.com/maps?q=59.92319166666667,10.649405555555555

Location: 35C, Tyribakken, Øvre Ullern, Ullern, Oslo, 0282, Norge



Processing HEIC: \py__ExtractImageGeotags\IMG_0085.HEIC

Latitude: 60.16304722222222

Longitude: 10.252727777777778

Google Maps: https://www.google.com/maps?q=60.16304722222222,10.252727777777778

Location: 25A, Bloms gate, To<PERSON><PERSON>rud, <PERSON><PERSON><PERSON><PERSON>ss, <PERSON>erike, Buskerud, 3510, <PERSON>ge



Processing HEIC: \py__ExtractImageGeotags\IMG_0121.HEIC

Latitude: 60.**************

Longitude: 10.***************

Google Maps: https://www.google.com/maps?q=60.**************,10.***************

Location: 2, Granliveien, Hønefoss, Ringerike, Buskerud, 3513, Norge

## Message 2

**Evaluating options**

The user is considering both Python and exiftool to embed geotags into filenames, taking into account filename limitations and special characters.
