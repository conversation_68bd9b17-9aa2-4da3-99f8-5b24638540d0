# RenameWithEditor.py

Conversation ID: 67cb2eb6-262c-8008-948d-4e2729bc53de

## Message 1

**Pulling together**

I’m pulling together a code snippet to modify the write function in HashFileManager, adapting it to file lines like `""" 2025.03.07 | """ - 'filename1.txt' # | "hash"`. Parsing logic will need updating to match this new format.

**Tweaking the parse logic**

Updating `_parse_hash_entry` to handle lines with an optional date prefix like `""" 2025.03.07 | """ - 'filename1.txt' # | "hash"` is necessary to maintain accurate parsing.

**Focusing on writing**

The main task is to write lines in the format `""" {yyyy.mm.dd} """ - 'filename1.txt' # | "hash"`, which requires updating the write method. Parsing logic might remain unchanged.

**Mapping out the write function**

I’m considering adding a header and modifying each line to include a date prefix, possibly storing along with filename and hash in `collect_file_hashes()`.

**Piecing together date prefixes**

Mapping out the `FileProcessor` to date code entries and updating the `HashFileManager` to format lines with date prefixes. This step effectively streamlines interval integration.

**Evaluating simplification**

The user might benefit from skipping complex prefix parsing in `_parse_hash_entry()` for a streamlined and consistent code experience.

**Sifting through regex options** 

Recognizing the need for a new regex to accurately parse file names and hashes, while preserving the date prefix format. Progressing towards a streamlined solution.

**Updating the code**

I'll compile a minimal code snippet, storing dates in `collect_file_hashes` to return 3-tuples, and adjust `HashFileManager` for consistent data structure references.

**Tweaking file management**

I’m renaming `hash_entries` to include date tuples, updating `_parse_hash_entry` for precise date handling, and ensuring `write` manages these enhanced entries efficiently.

**Refining regex and file handling**

I’m updating `_parse_hash_entry` with a regex pattern for consistent date handling. Considering dictionaries or named tuples for enhanced file operations.

**Simplifying the approach**

I’m noting that storing file information as `(file_hash, filename, date_str)` and applying the same logic to `org_manager` and `new_manager` operations simplifies the process.

**Implementing incremental changes**

I’m detailing the steps: updating `FileProcessor` to return 3-tuples, modifying `HashFileManager` to handle these tuples and headers, and refining `_parse_hash_entry` with a new regex.

**Adjusting the input**

I’m piecing together how to adjust `_determine_rename_pairs` to handle a list of `(hash, path)` by ignoring the date from the read results.

**Constructing the final solution**

I’m detailing how to finalize the code by adding time imports, updating the header to include three columns, and prefixing each file line with its date of modification for clarity and structure.

**Enhancing file manager**

I’m mapping out the final changes: adding time imports, refining `FileProcessor` to format dates, and updating `HashFileManager` to align entries with the new header format for consistent file handling.

**Enhancing file management**

I’m adding time imports, refining `FileProcessor` to format dates, and updating `HashFileManager` to align entries with the new header format, ensuring consistent file handling.

**Mapping and refining**

OK, let's map hashes to paths, adjust file entries, and manage date formats for accurate identification and logging. This approach ensures reliable file rename operations.

## Message 2



The provided script named `RenameWithEditor.py` is a util for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). Here's the code:



```python

""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """



import argparse

import hashlib

import os

import pathlib

import shutil

import subprocess

import sys

import re

import unicodedata

from pathlib import Path

from typing import Dict, List, Optional, Set, Tuple



from dataclasses import dataclass

from enum import Enum

# Rich imports for console interactions

from rich.console import Console

from rich.prompt import Prompt, Confirm

from rich.panel import Panel

from rich.table import Table

from rich.box import ROUNDED



# Loguru for logging

from loguru import logger





# ----------------------------

#        Configuration

# ----------------------------



class Config:

    """

    Holds default settings for the Batch Rename Utility.

    """

    DEFAULT_CLEANUP_LOGS = True

    USE_DEFAULT_SETTINGS = True

    # You may introduce additional defaults as needed

    DEFAULT_INCLUDE_SUBDIRECTORIES = False



# ----------------------------

#       Logger Setup

# ----------------------------



class LoggerSetup:

    """Sets up Loguru logger to write YAML logs to a file."""



    @staticmethod

    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):

        """

        Configure Loguru to log messages in YAML format to `log_file`.

        """

        def yaml_sink(message):

            record = message.record



            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')

            level_str = f"!{record['level'].name}"

            name_str = record['name']

            func_name_str = f"*{record['function']}"

            line_no = record["line"]

            msg = record["message"]



            # For multi-line messages, use a block scalar in YAML

            if "\n" in msg:

                lines = msg.split("\n")

                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)

            else:

                # Quote message if it has special characters like ':'

                if ":" in msg:

                    message_str = f"'{msg}'"

                else:

                    message_str = msg



            yaml_lines = [

                f"- time: {time_str}",

                f"  level: {level_str}",

                f"  name: {name_str}",

                f"  funcName: {func_name_str}",

                f"  lineno: {line_no}",

                f"  message: {message_str}",

                ""

            ]



            with open(log_file, "a", encoding="utf-8") as f:

                f.write("\n".join(yaml_lines) + "\n")



        # Remove default handlers and add our YAML sink

        logger.remove()

        logger.add(yaml_sink, level=level, enqueue=True)



    @staticmethod

    def initialize_logging(verbosity: str = "INFO"):

        """

        Initialize YAML logging with a level mapped from the string `verbosity`.

        Maps:

          quiet -> ERROR

          normal -> INFO

          verbose -> DEBUG

          debug -> DEBUG

        """

        level_map = {

            "quiet": "ERROR",

            "normal": "INFO",

            "verbose": "DEBUG",

            "debug": "DEBUG"

        }

        selected_level = level_map.get(verbosity.lower(), "INFO")

        LoggerSetup.setup_yaml_logging(level=selected_level)





# ----------------------------

#     Argument Handler

# ----------------------------



class ArgumentHandler:

    # -------------------- Argument Parsing and Prompting --------------------

    def __init__(self):

        self.parser = self.parse_arguments()



    @staticmethod

    def parse_arguments():

        logger.debug("Setting up argument parser.")

        parser = argparse.ArgumentParser(

            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."

        )

        # Adapted from your first script’s approach but tailored to this utility:

        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")

        parser.add_argument('--include_subdirs', action='store_true',

                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,

                            help="Include subdirectories in file processing")

        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")

        parser.add_argument('-v', '--verbosity',

                            choices=["quiet", "normal", "verbose", "debug"],

                            default="normal",

                            help="Set output verbosity level")



        # New Arguments for Log Cleanup (same approach as first utility)

        cleanup_logs_group = parser.add_mutually_exclusive_group()

        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',

                                        help="Clean up log files after successful execution")

        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',

                                        help="Do not clean up log files after successful execution")

        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)



        logger.debug("Argument parser setup complete.")

        return parser



    def get_arguments(self):

        return self.parser.parse_args()



    def prompt_for_missing_arguments(self, args):

        logger.debug("Prompting for missing arguments.")

        console = Console()



        def print_section(title):

            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)



        if args.prompt:

            print_section("Default Settings")

            use_defaults = Confirm.ask("Use default settings?", default=Config.DEFAULT_CLEANUP_LOGS)

            logger.debug(f"Use defaults: {use_defaults}")



            if not use_defaults:

                # Ask for directory

                print_section("Directory")

                current_dir = args.directory or ""

                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()



                print_section("Include Subdirectories?")

                args.include_subdirs = Confirm.ask("Include subdirectories?", default=args.include_subdirs)



                print_section("Logging Verbosity")

                choices = ["quiet", "normal", "verbose", "debug"]

                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")

                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)

                args.verbosity = chosen_verbosity



                # Prompt for cleanup logs

                print_section("Log Cleanup")

                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=args.cleanup_logs)

            else:

                # Assign defaults if none provided

                args.directory = args.directory or ""

                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES

                # Let the existing defaults stand for verbosity and cleanup_logs



        # Validation

        if not args.directory:

            console.print("[red]Error:[/] The following argument is required: directory")

            sys.exit(1)



        dir_path = Path(args.directory)

        if not dir_path.exists() or not dir_path.is_dir():

            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")

            sys.exit(1)



        logger.debug("Argument prompting complete.")

        return args





# ------------------

#    File Hasher

# ------------------



class FileHasher:

    """Computes SHA256 hashes for files."""

    CHUNK_SIZE = 4096



    @staticmethod

    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:

        sha256 = hashlib.sha256()

        try:

            with file_path.open('rb') as f:

                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):

                    sha256.update(chunk)

            return sha256.hexdigest()

        except IOError as error:

            logger.error(f"Error reading `{file_path}`: {error}")

            return None





# ------------------

#  File Processor

# ------------------



class FileProcessor:

    """Processes files to collect their hashes."""



    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool):

        self.root_dir = root_dir

        self.include_subdirs = include_subdirs



    def collect_file_hashes(self) -> List[Tuple[str, str]]:

        hash_entries = []

        for root, _, files in os.walk(self.root_dir):

            for filename in files:

                file_path = pathlib.Path(root) / filename

                if not self._is_accessible_file(file_path):

                    continue



                relative_path = file_path.relative_to(self.root_dir).as_posix()

                file_hash = FileHasher.compute_sha256(file_path)

                if file_hash:

                    hash_entries.append((file_hash, relative_path))

                    logger.debug(f"Processed: {relative_path}")



            if not self.include_subdirs:

                break

        return hash_entries



    def _is_accessible_file(self, path: pathlib.Path) -> bool:

        if not path.is_file() or not os.access(path, os.R_OK):

            logger.warning(f"`{path}` is not accessible or not a file")

            return False

        return True





# ------------------

#  Hash File Manager

# ------------------



class HashFileManager:

    """Manages reading and writing hash files."""



    def __init__(self, file_path: pathlib.Path):

        self.file_path = file_path



    def write(self, hash_entries: List[Tuple[str, str]]) -> None:

        try:

            max_length = max((len(filename) for _, filename in hash_entries), default=0) + 2

            with self.file_path.open("w", encoding='utf-8') as f:

                f.write("# Hash to Filename Mapping\n")

                for file_hash, filename in sorted(hash_entries, key=lambda x: x[1].lower()):

                    padded_filename = f"'{filename}'".ljust(max_length)

                    f.write(f"- {padded_filename} # | \"{file_hash}\"\n")

            logger.info(f"Hash file written: {self.file_path.name}")

        except IOError as error:

            logger.error(f"Failed to write hash file: {error}")



    def read(self) -> List[Tuple[str, str]]:

        hash_entries = []

        try:

            with self.file_path.open("r", encoding='utf-8') as f:

                for line in f:

                    entry = self._parse_hash_entry(line)

                    if entry:

                        hash_entries.append(entry)

        except IOError as error:

            logger.error(f"Failed to read hash file: {error}")

        return hash_entries



    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str]]:

        line = line.strip()

        if not (line.startswith("- '") and ' # | "' in line and line.endswith('"')):

            return None



        try:

            filename_part, hash_part = line.split(" # | \"")

            filename = filename_part.strip("- '").rstrip()

            file_hash = hash_part[:-1]

            return (file_hash, filename)

        except (IndexError, ValueError):

            logger.warning(f"Invalid hash file entry: {line}")

            return None





# ------------------

#   File Renamer

# ------------------



class FileRenamer:

    """Handles the renaming of files based on hash comparisons."""



    def __init__(self, root_dir: pathlib.Path):

        self.root_dir = root_dir



    def execute(

        self,

        source_hashes: List[Tuple[str, str]],

        target_hashes: List[Tuple[str, str]],

        dry_run: bool = True

    ) -> bool:

        source_map = self._map_hash_to_paths(source_hashes)

        target_map = self._map_hash_to_paths(target_hashes)



        rename_pairs = self._determine_rename_pairs(source_map, target_map)

        conflicts = False



        if dry_run:

            self._preview_renames(rename_pairs)



        for src_rel, tgt_rel in rename_pairs:

            src_path = self.root_dir / src_rel

            tgt_path = self.root_dir / tgt_rel



            if src_rel == tgt_rel:

                logger.debug(f"Unchanged: {src_rel}")

                continue



            if not self._validate_paths(src_path, tgt_path):

                conflicts = True

                continue



            if dry_run:

                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')

            else:

                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)



        self._log_completion(dry_run, conflicts)

        return not conflicts



    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:

        hash_map: Dict[str, List[str]] = {}

        for file_hash, path in hash_entries:

            hash_map.setdefault(file_hash, []).append(path)

        return hash_map



    def _determine_rename_pairs(

        self,

        source_map: Dict[str, List[str]],

        target_map: Dict[str, List[str]],

    ) -> List[Tuple[str, str]]:

        pairs: List[Tuple[str, str]] = []

        processed_targets: Set[str] = set()



        for file_hash, src_paths in source_map.items():

            tgt_paths = target_map.get(file_hash, [])

            for src in src_paths:

                if any(src == pair[0] for pair in pairs):

                    # Already paired

                    continue



                available_tgts = [t for t in tgt_paths if t not in processed_targets]

                if not available_tgts:

                    logger.warning(f"No matching hash for: {src}")

                    continue



                best_match = self._select_best_match(src, available_tgts)

                if best_match:

                    pairs.append((src, best_match))

                    processed_targets.add(best_match)



        return pairs



    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:

        source_clean = self._clean_name(source)

        best_similarity = -1.0

        best_target = None



        for tgt in targets:

            tgt_clean = self._clean_name(tgt)

            similarity = self._name_similarity(source_clean, tgt_clean)

            if similarity > best_similarity:

                best_similarity = similarity

                best_target = tgt



        if best_target:

            logger.debug(

                f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}"

            )

        else:

            logger.warning(f"No suitable match found for '{source}'")



        return best_target



    @staticmethod

    def _name_similarity(name1: str, name2: str) -> float:

        matches = sum(a == b for a, b in zip(name1, name2))

        max_len = max(len(name1), len(name2))

        return matches / max_len if max_len else 0



    @staticmethod

    def _clean_name(name: str) -> str:

        # Normalize to ASCII, remove special chars

        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')

        name = name.lower()

        name = pathlib.Path(name).stem

        name = re.sub(r'[^a-z0-9]', '', name)

        return name



    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:

        if not src.exists():

            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")

            return False

        if tgt.exists() and tgt != src:

            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")

            return False

        return True



    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:

        try:

            tgt.parent.mkdir(parents=True, exist_ok=True)

            shutil.move(str(src), str(tgt))

            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')

        except OSError as error:

            logger.error(f"Failed to rename {src_rel}: {error}")



    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:

        if not rename_pairs:

            logger.warning("No files require renaming")

            return



        table = Table(

            title="Pending Rename Operations",

            show_header=True,

            header_style="bold blue",

            box=ROUNDED

        )

        table.add_column("Operation", style="cyan", width=4)

        table.add_column("Source", style="white")

        table.add_column("Target", style="green")



        changes = 0

        for src, tgt in rename_pairs:

            if src != tgt:

                table.add_row("→", src, tgt)

                changes += 1



        if changes:

            console = Console()

            console.print("\n")

            console.print(Panel(table, border_style="blue"))

            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")

        else:

            logger.warning("No files require renaming")



    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:

        operation = "Dry run" if dry_run else "File renaming"

        if has_conflicts:

            logger.warning(f"{operation}: Conflicts detected, some files skipped")

        else:

            logger.info(f"{operation} completed successfully")





# ------------------

#    File Editor

# ------------------



class FileEditor:

    """Opens files using the default system editor."""



    @staticmethod

    def open(file_path: pathlib.Path) -> None:

        try:

            if sys.platform.startswith('darwin'):

                subprocess.call(['open', str(file_path)])

            elif os.name == 'nt':

                os.startfile(str(file_path))

            elif os.name == 'posix':

                subprocess.call(['xdg-open', str(file_path)])

            else:

                logger.warning(f"Unsupported platform: {sys.platform}")

        except Exception as error:

            logger.error(f"Failed to open editor: {error}")





# ----------------------------

#     Main App Class

# ----------------------------



class BatchRenameApp:

    """

    Encapsulates the CLI workflow for batch renaming:

      1) Parse & prompt for Arguments

      2) Initialize Logging

      3) Collect & write .original_hashes.py & .new_hashes.py

      4) Edit & confirm renaming

      5) Cleanup

    """



    def __init__(self):

        self.arg_handler = ArgumentHandler()

        self.args: Optional[argparse.Namespace] = None



    def run(self) -> None:

        # 1) Parse & prompt

        self.args = self.arg_handler.get_arguments()

        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)



        # 2) Initialize Logging with Loguru

        LoggerSetup.initialize_logging(self.args.verbosity)



        # 3) Handle the main process

        success = False

        try:

            self.handle_process_command()

            success = True

        except Exception as e:

            logger.error(f"Execution failed: {e}")



        # 4) Cleanup logs if needed and success

        if success and self.args.cleanup_logs:

            logger.remove()  # remove Loguru handler to release file

            log_file = Path("app.log.yml")

            if log_file.exists():

                console = Console()

                try:

                    log_file.unlink()

                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")

                except Exception as e:

                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")



    def handle_process_command(self) -> None:

        """Handles the main processing and renaming of files."""

        console = Console()

        root_dir = pathlib.Path(self.args.directory).resolve()



        org_file = root_dir / ".original_hashes.py"

        new_file = root_dir / ".new_hashes.py"



        try:

            processor = FileProcessor(root_dir, self.args.include_subdirs)

            initial_hashes = processor.collect_file_hashes()



            for file_path in (org_file, new_file):

                manager = HashFileManager(file_path)

                manager.write(initial_hashes)



            logger.info("Opening new hash file for editing...")

            FileEditor.open(new_file)



            if not Confirm.ask("\nProceed with renaming? [y/n]: ", default="y"):

                logger.warning("Operation cancelled by user")

                return



            org_manager = HashFileManager(org_file)

            new_manager = HashFileManager(new_file)



            renamer = FileRenamer(root_dir)

            # Dry run first

            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):

                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):

                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)



        finally:

            # Cleanup: Attempt to delete the hash files

            for file_path in (org_file, new_file):

                try:

                    if file_path.exists():

                        file_path.unlink()

                        logger.info(f"Cleaned up: {file_path.name}")

                except OSError as error:

                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")





# ------------------

#      Entry

# ------------------



def main() -> None:

    """Main entry point of the utility."""

    app = BatchRenameApp()

    app.run()





if __name__ == "__main__":

    main()

```



---



The way it currently works is that it generates a textfile looking like this:



```

# Hash to Filename Mapping

- 'filename1.txt' # | "a8280169b91551799750bce7540f5eb1a12e700cf5846e4593de32ebed8ad208"

- 'filename2.txt' # | "5742b246b010a2f1633abff74856ae95410adfac8f574b4247db9d280f5db93d"

- 'filename3.txt' # | "30f415e1fbdc8ec8c5739d9ca5eb982b13e2612273538f87ecef70df9d60444d"

- 'filename4.txt' # | "44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a"

- 'filename5.txt' # | "5beeefd47276a5481fecbd9cd12d9bf16e07ccafcc494519aa7f501e7f051b0f"

- 'filename6.txt' # | "b61bc81cd94101b9d7b16aab5fa2277b9f51f6d82912755d576694244c122d4f"

- 'filename7.txt' # | "d8054cc6afa1f7caf4163756e98164b8e2c9524bc70e7984f6477b2a77342031"

```



---





Your goal is to modify it such that it changes the header to represent three columns, then for each file it will prefix the line with each file's datemodified (formatted like this `""" {yyyy.mm.dd} """`). Example:



```

#  YYYY.MM.DD  | FILENAME                | FILEHASH

""" 2025.03.07 | """ - 'filename1.txt' # | "a8280169b91551799750bce7540f5eb1a12e700cf5846e4593de32ebed8ad208"

""" 2025.03.07 | """ - 'filename2.txt' # | "5742b246b010a2f1633abff74856ae95410adfac8f574b4247db9d280f5db93d"

""" 2025.03.07 | """ - 'filename3.txt' # | "30f415e1fbdc8ec8c5739d9ca5eb982b13e2612273538f87ecef70df9d60444d"

""" 2025.03.07 | """ - 'filename4.txt' # | "44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a"

""" 2025.03.07 | """ - 'filename5.txt' # | "5beeefd47276a5481fecbd9cd12d9bf16e07ccafcc494519aa7f501e7f051b0f"

""" 2025.03.07 | """ - 'filename6.txt' # | "b61bc81cd94101b9d7b16aab5fa2277b9f51f6d82912755d576694244c122d4f"

""" 2025.03.07 | """ - 'filename7.txt' # | "d8054cc6afa1f7caf4163756e98164b8e2c9524bc70e7984f6477b2a77342031"

```


## Message 3

Below is one possible way to integrate file “date modified” into the hash file alongside the filename and its hash. The core changes are:

1. **Store each file’s date-modified** when collecting file hashes.  
2. **Write** the output lines with three columns and the date prefix (in your requested format).  
3. **Adjust** the parsing logic so you can still read the updated lines.  
4. **Pass** only the hash+filename pairs to the renamer (since the renamer doesn’t actually need the date).

Below is a complete example of how you can update the script. The biggest changes are in `FileProcessor.collect_file_hashes`, `HashFileManager.write`, and `HashFileManager._parse_hash_entry`, plus small adjustments where reading those entries is used.

---

## 1) Imports and Data Changes

- We’ll import `time` to format Unix timestamps.
- Instead of returning a list of `(file_hash, filename)`, we’ll return `(file_hash, filename, date_str)` from `collect_file_hashes`.
- The `HashFileManager` will write and parse these three-element tuples.

---

## 2) Updated Script

```python
""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """

import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata
import time  # NEW for date/time formatting
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from dataclasses import dataclass
from enum import Enum
# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# Loguru for logging
from loguru import logger


# ----------------------------
#        Configuration
# ----------------------------

class Config:
    """
    Holds default settings for the Batch Rename Utility.
    """
    DEFAULT_CLEANUP_LOGS = True
    USE_DEFAULT_SETTINGS = True
    DEFAULT_INCLUDE_SUBDIRECTORIES = False

# ----------------------------
#       Logger Setup
# ----------------------------

class LoggerSetup:
    """Sets up Loguru logger to write YAML logs to a file."""

    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        """
        Configure Loguru to log messages in YAML format to `log_file`.
        """
        def yaml_sink(message):
            record = message.record

            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            func_name_str = f"*{record['function']}"
            line_no = record["line"]
            msg = record["message"]

            # For multi-line messages, use a block scalar in YAML
            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote message if it has special characters like ':' 
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {func_name_str}",
                f"  lineno: {line_no}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        # Remove default handlers and add our YAML sink
        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging(verbosity: str = "INFO"):
        """
        Initialize YAML logging with a level mapped from the string `verbosity`.
        """
        level_map = {
            "quiet": "ERROR",
            "normal": "INFO",
            "verbose": "DEBUG",
            "debug": "DEBUG"
        }
        selected_level = level_map.get(verbosity.lower(), "INFO")
        LoggerSetup.setup_yaml_logging(level=selected_level)


# ----------------------------
#     Argument Handler
# ----------------------------

class ArgumentHandler:
    # -------------------- Argument Parsing and Prompting --------------------
    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(
            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."
        )
        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")
        parser.add_argument('--include_subdirs', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,
                            help="Include subdirectories in file processing")
        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")
        parser.add_argument('-v', '--verbosity',
                            choices=["quiet", "normal", "verbose", "debug"],
                            default="normal",
                            help="Set output verbosity level")

        # New Arguments for Log Cleanup
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',
                                        help="Clean up log files after successful execution")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',
                                        help="Do not clean up log files after successful execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        logger.debug("Prompting for missing arguments.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.DEFAULT_CLEANUP_LOGS)
            logger.debug(f"Use defaults: {use_defaults}")

            if not use_defaults:
                print_section("Directory")
                current_dir = args.directory or ""
                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()

                print_section("Include Subdirectories?")
                args.include_subdirs = Confirm.ask("Include subdirectories?", default=args.include_subdirs)

                print_section("Logging Verbosity")
                choices = ["quiet", "normal", "verbose", "debug"]
                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")
                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)
                args.verbosity = chosen_verbosity

                print_section("Log Cleanup")
                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=args.cleanup_logs)
            else:
                args.directory = args.directory or ""
                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES

        # Validation
        if not args.directory:
            console.print("[red]Error:[/] The following argument is required: directory")
            sys.exit(1)

        dir_path = Path(args.directory)
        if not dir_path.exists() or not dir_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
            sys.exit(1)

        logger.debug("Argument prompting complete.")
        return args


# ------------------
#    File Hasher
# ------------------

class FileHasher:
    """Computes SHA256 hashes for files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None


# ------------------
#  File Processor
# ------------------

class FileProcessor:
    """Processes files to collect their hashes plus date-modified."""

    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs

    def collect_file_hashes(self) -> List[Tuple[str, str, str]]:
        """
        Collect a list of (file_hash, relative_filename, date_modified).
        date_modified will be formatted as YYYY.MM.DD.
        """
        hash_entries = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path)
                if file_hash:
                    # Compute date modified
                    mtime = file_path.stat().st_mtime
                    date_str = time.strftime("%Y.%m.%d", time.localtime(mtime))
                    hash_entries.append((file_hash, relative_path, date_str))
                    logger.debug(f"Processed: {relative_path}")

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            logger.warning(f"`{path}` is not accessible or not a file")
            return False
        return True


# ------------------
#  Hash File Manager
# ------------------

class HashFileManager:
    """Manages reading and writing hash files with date, filename, and hash."""

    def __init__(self, file_path: pathlib.Path):
        self.file_path = file_path

    def write(self, hash_entries: List[Tuple[str, str, str]]) -> None:
        """
        Writes lines in the format:
          #  YYYY.MM.DD  | FILENAME                | FILEHASH
          """ 2025.03.07 | """ - 'filename.txt' # | "abc123..."
        """
        try:
            # Write a three-column style header
            with self.file_path.open("w", encoding='utf-8') as f:
                f.write("#  YYYY.MM.DD  | FILENAME                | FILEHASH\n")

                # Sort by filename (lowercased) for consistency
                sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())

                # Determine how much padding we need for the filename column
                max_length = 2 + max((len(filename) for _, filename, _ in sorted_entries), default=0)

                # Generate lines
                for file_hash, filename, date_str in sorted_entries:
                    # Pad the filename portion similarly to before
                    padded_filename = f"'{filename}'".ljust(max_length)
                    line = f'""" {date_str} | """ - {padded_filename} # | "{file_hash}"\n'
                    f.write(line)

            logger.info(f"Hash file written: {self.file_path.name}")
        except IOError as error:
            logger.error(f"Failed to write hash file: {error}")

    def read(self) -> List[Tuple[str, str, str]]:
        """
        Reads lines matching either the new format with date or the old format
        (so it can still handle older files if they exist).
        Returns a list of (hash, filename, date_str).
        """
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            logger.error(f"Failed to read hash file: {error}")
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str]]:
        """
        Attempts to parse a line in the new format:
           """ 2025.03.07 | """ - 'filename.txt' # | "abc123..."
        Or the old format:
           - 'filename.txt' # | "abc123..."
        We ignore comment/blank lines.
        """
        line = line.strip()
        if not line or line.startswith("#"):
            return None

        # New format:
        # """ YYYY.MM.DD | """ - 'some_file.txt' # | "hash"
        new_format = r'^"""\s*(\d{4}\.\d{2}\.\d{2})\s*\|\s*"""\s*-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'
        match_new = re.match(new_format, line)
        if match_new:
            date_str = match_new.group(1)
            filename = match_new.group(2)
            file_hash = match_new.group(3)
            return (file_hash, filename, date_str)

        # Old format fallback:
        # - 'some_file.txt' # | "hash"
        old_format = r'^-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'
        match_old = re.match(old_format, line)
        if match_old:
            filename = match_old.group(1)
            file_hash = match_old.group(2)
            return (file_hash, filename, "")  # No date in old format

        logger.warning(f"Invalid hash file entry: {line}")
        return None


# ------------------
#   File Renamer
# ------------------

class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(
        self,
        source_hashes: List[Tuple[str, str, str]],
        target_hashes: List[Tuple[str, str, str]],
        dry_run: bool = True
    ) -> bool:
        """
        Expects (hash, filename, date_str), but only uses the hash + filename for matching.
        """
        # Convert to (hash -> list_of_paths)
        source_map = self._map_hash_to_paths([(h, f) for (h, f, _) in source_hashes])
        target_map = self._map_hash_to_paths([(h, f, _) for (h, f, _) in target_hashes])

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                logger.debug(f"Unchanged: {src_rel}")
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    logger.warning(f"No matching hash for: {src}")
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1.0
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            logger.debug(
                f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}"
            )
        else:
            logger.warning(f"No suitable match found for '{source}'")

        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")
            return False
        if tgt.exists() and tgt != src:
            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')
        except OSError as error:
            logger.error(f"Failed to rename {src_rel}: {error}")

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            logger.warning("No files require renaming")
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            console = Console()
            console.print("\n")
            console.print(Panel(table, border_style="blue"))
            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            logger.warning("No files require renaming")

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            logger.warning(f"{operation}: Conflicts detected, some files skipped")
        else:
            logger.info(f"{operation} completed successfully")


# ------------------
#    File Editor
# ------------------

class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.warning(f"Unsupported platform: {sys.platform}")
        except Exception as error:
            logger.error(f"Failed to open editor: {error}")


# ----------------------------
#     Main App Class
# ----------------------------

class BatchRenameApp:
    """
    Encapsulates the CLI workflow for batch renaming:
      1) Parse & prompt for Arguments
      2) Initialize Logging
      3) Collect & write .original_hashes.py & .new_hashes.py
      4) Edit & confirm renaming
      5) Cleanup
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args: Optional[argparse.Namespace] = None

    def run(self) -> None:
        # 1) Parse & prompt
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Initialize Logging
        LoggerSetup.initialize_logging(self.args.verbosity)

        # 3) Handle the main process
        success = False
        try:
            self.handle_process_command()
            success = True
        except Exception as e:
            logger.error(f"Execution failed: {e}")

        # 4) Cleanup logs if needed and success
        if success and self.args.cleanup_logs:
            logger.remove()  # remove Loguru handler to release file
            log_file = Path("app.log.yml")
            if log_file.exists():
                console = Console()
                try:
                    log_file.unlink()
                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")

    def handle_process_command(self) -> None:
        console = Console()
        root_dir = pathlib.Path(self.args.directory).resolve()

        org_file = root_dir / ".original_hashes.py"
        new_file = root_dir / ".new_hashes.py"

        try:
            processor = FileProcessor(root_dir, self.args.include_subdirs)
            initial_hashes = processor.collect_file_hashes()

            # Write initial hashes (with date) to both “original” and “new” files
            for file_path in (org_file, new_file):
                manager = HashFileManager(file_path)
                manager.write(initial_hashes)

            logger.info("Opening new hash file for editing...")
            FileEditor.open(new_file)

            if not Confirm.ask("\nProceed with renaming? [y/n]: ", default="y"):
                logger.warning("Operation cancelled by user")
                return

            org_manager = HashFileManager(org_file)
            new_manager = HashFileManager(new_file)

            renamer = FileRenamer(root_dir)
            # Dry run first
            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):
                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

        finally:
            # Cleanup: Attempt to delete the hash files
            for file_path in (org_file, new_file):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Cleaned up: {file_path.name}")
                except OSError as error:
                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")


# ------------------
#      Entry
# ------------------

def main() -> None:
    """Main entry point of the utility."""
    app = BatchRenameApp()
    app.run()


if __name__ == "__main__":
    main()
```

---

### How This Differs From the Original

1. **Collecting `date_str`:**  
   - In `FileProcessor.collect_file_hashes`, each file now produces `(hash, relative_path, date_str)`.

2. **Writing Three Columns:**  
   - In `HashFileManager.write`, the header changes to  
     ```
     #  YYYY.MM.DD  | FILENAME                | FILEHASH
     ```
   - Each line now starts with:
     ```
     """ {date_str} | """ - 'filename.txt' # | "filehash"
     ```

3. **Reading Three Columns (New Format):**  
   - The `_parse_hash_entry` method uses a new regex to grab `date_str`, `filename`, and `file_hash` from the triple-quoted prefix.
   - It also supports **old lines** by falling back to the older “`- 'filename' # | "hash"`” pattern if no date is found.

4. **Renamer Adjustments:**  
   - The `FileRenamer.execute` now expects `(hash, filename, date_str)`, but discards the date since it’s only relevant for display. It strips out the date when building the rename maps.

With these changes, your generated file lines will resemble:

```
#  YYYY.MM.DD  | FILENAME                | FILEHASH
""" 2025.03.07 | """ - 'filename1.txt' # | "a8280169b91551799750bce7540f5eb1a12e700cf5846e4593de32ebed8ad208"
""" 2025.03.07 | """ - 'filename2.txt' # | "5742b246b010a2f1633abff74856ae95410adfac8f574b4247db9d280f5db93d"
...
```

Where the actual date is your file’s `mtime` (last modified time), formatted as `YYYY.MM.DD`. If you simply want today’s date for all, replace that logic with a single `time.strftime(...)` call rather than reading `file_path.stat()`.

---

**That’s it!** Now your output has a three-column style header, and each line is prefixed by the file’s date (in the requested string format).

## Message 4

**Swapping quotes**

OK, let me see. The user wants single and double quotes swapped for a specific line format, adjusting `_parse_hash_entry` to handle the new format.

**Refining regex for new format**

I’m working on a regex pattern to handle a new format with triple single quotes, date, double-quoted filename, and single-quoted hash. Options for fallback formats are considered.

## Message 5

Below is the **minimal** set of changes needed so the script emits the inverted quotes in the output lines, for example:

```
#  YYYY.MM.DD  | FILENAME                                   | FILEHASH
''' 2025.03.07 | ''' - "003_prompt001.md"                 # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'
''' 2025.03.07 | ''' - "003_prompt002.md"                 # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'
''' 2025.03.07 | ''' - "003_prompt003.md"                 # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'
```

---

## 1) Update the Write Logic

In **`HashFileManager.write`**, change the line construction from:

```python
line = f'""" {date_str} | """ - \'{filename}\' # | "{file_hash}"\n'
```

**to**:

```python
line = f"''' {date_str} | ''' - \"{filename}\" # | '{file_hash}'\n"
```

**Example snippet:**

```python
def write(self, hash_entries: List[Tuple[str, str, str]]) -> None:
    try:
        with self.file_path.open("w", encoding='utf-8') as f:
            # Updated header (still the same text)
            f.write("#  YYYY.MM.DD  | FILENAME                                   | FILEHASH\n")

            sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())
            # Determine padding for the filename column
            max_length = 2 + max((len(filename) for _, filename, _ in sorted_entries), default=0)

            for file_hash, filename, date_str in sorted_entries:
                # Build the line with triple single quotes for the date, double quotes for the filename,
                # and single quotes for the hash:
                padded_filename = f"\"{filename}\"".ljust(max_length)
                line = f"''' {date_str} | ''' - {padded_filename} # | '{file_hash}'\n"
                f.write(line)

        logger.info(f"Hash file written: {self.file_path.name}")
    except IOError as error:
        logger.error(f"Failed to write hash file: {error}")
```

---

## 2) Update the Read/Parse Logic

In **`HashFileManager._parse_hash_entry`**, change the **“new format”** regex to match triple single-quotes for the date, double-quotes for the filename, and single-quotes for the hash. For example:

```python
new_format = (
    r"^'''\s*(\d{4}\.\d{2}\.\d{2})\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
)
```

So it looks like:

```python
def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str]]:
    line = line.strip()
    if not line or line.startswith("#"):
        return None

    # New format:
    # ''' 2025.03.07 | ''' - "filename" # | 'hash'
    new_format = r"^'''\s*(\d{4}\.\d{2}\.\d{2})\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
    match_new = re.match(new_format, line)
    if match_new:
        date_str = match_new.group(1)
        filename = match_new.group(2)
        file_hash = match_new.group(3)
        return (file_hash, filename, date_str)

    # Old format fallback (unchanged)
    old_format = r'^-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'
    match_old = re.match(old_format, line)
    if match_old:
        filename = match_old.group(1)
        file_hash = match_old.group(2)
        return (file_hash, filename, "")

    logger.warning(f"Invalid hash file entry: {line}")
    return None
```

This ensures the lines are **written** in the new style with triple single-quotes and read back with the correct pattern.

---

### Summary of Key Adjustments

1. **Write-side**:
   ```diff
   - line = f'""" {date_str} | """ - \'{filename}\' # | "{file_hash}"\n'
   + line = f"''' {date_str} | ''' - \"{filename}\" # | '{file_hash}'\n"
   ```

2. **Read-side**:
   ```diff
   - new_format = r'^"""\s*(\d{4}\.\d{2}\.\d{2})...'
   + new_format = r"^'''\s*(\d{4}\.\d{2}\.\d{2})\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
   ```
   
With these two changes, your script will produce the requested triple-single-quoted date, double-quoted filename, and single-quoted hash.

## Message 6



It currently outputs like this:



```

#  YYYY.MM.DD  | FILENAME                                   | FILEHASH

""" 2025.03.07 | """ - '003_prompt001.md'                 # | "991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe"

""" 2025.03.07 | """ - '003_prompt002.md'                 # | "a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41"

""" 2025.03.07 | """ - '003_prompt003.md'                 # | "0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537"

```



---



Please modify it like this instead (swapping places of single/double-quotes):



```

#  YYYY.MM.DD  | FILENAME                                   | FILEHASH

''' 2025.03.07 | ''' - "003_prompt001.md"                 # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'

''' 2025.03.07 | ''' - "003_prompt002.md"                 # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'

''' 2025.03.07 | ''' - "003_prompt003.md"                 # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'

```


## Message 7

Updating the script to align with new formatting and

## Message 8

Provide the complete, updated code as a direct replacement for the existing functionality. Ensure seamless integration without disrupting current operations and with full retention of all existing features. The replacement must be fully functional.

## Message 9

Below is the **complete, updated** script. Simply replace your existing `RenameWithEditor.py` with the following contents. All existing features are retained, while the hash file lines now use triple single-quotes for dates, double quotes for filenames, and single quotes for hashes.

```python
""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """

import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata
import time  # for date/time formatting
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from dataclasses import dataclass
from enum import Enum
# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# Loguru for logging
from loguru import logger


# ----------------------------
#        Configuration
# ----------------------------

class Config:
    """
    Holds default settings for the Batch Rename Utility.
    """
    DEFAULT_CLEANUP_LOGS = True
    USE_DEFAULT_SETTINGS = True
    DEFAULT_INCLUDE_SUBDIRECTORIES = False


# ----------------------------
#       Logger Setup
# ----------------------------

class LoggerSetup:
    """Sets up Loguru logger to write YAML logs to a file."""

    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        """
        Configure Loguru to log messages in YAML format to `log_file`.
        """
        def yaml_sink(message):
            record = message.record

            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            func_name_str = f"*{record['function']}"
            line_no = record["line"]
            msg = record["message"]

            # For multi-line messages, use a block scalar in YAML
            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote message if it has special characters like ':'
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {func_name_str}",
                f"  lineno: {line_no}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        # Remove default handlers and add our YAML sink
        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging(verbosity: str = "INFO"):
        """
        Initialize YAML logging with a level mapped from the string `verbosity`.
        """
        level_map = {
            "quiet": "ERROR",
            "normal": "INFO",
            "verbose": "DEBUG",
            "debug": "DEBUG"
        }
        selected_level = level_map.get(verbosity.lower(), "INFO")
        LoggerSetup.setup_yaml_logging(level=selected_level)


# ----------------------------
#     Argument Handler
# ----------------------------

class ArgumentHandler:
    """Handles CLI argument parsing and optional prompting."""

    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(
            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."
        )
        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")
        parser.add_argument('--include_subdirs', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,
                            help="Include subdirectories in file processing")
        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")
        parser.add_argument('-v', '--verbosity',
                            choices=["quiet", "normal", "verbose", "debug"],
                            default="normal",
                            help="Set output verbosity level")

        # Log Cleanup arguments
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',
                                        help="Clean up log files after successful execution")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',
                                        help="Do not clean up log files after successful execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        logger.debug("Prompting for missing arguments.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.DEFAULT_CLEANUP_LOGS)
            logger.debug(f"Use defaults: {use_defaults}")

            if not use_defaults:
                print_section("Directory")
                current_dir = args.directory or ""
                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()

                print_section("Include Subdirectories?")
                args.include_subdirs = Confirm.ask("Include subdirectories?", default=args.include_subdirs)

                print_section("Logging Verbosity")
                choices = ["quiet", "normal", "verbose", "debug"]
                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")
                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)
                args.verbosity = chosen_verbosity

                print_section("Log Cleanup")
                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=args.cleanup_logs)
            else:
                args.directory = args.directory or ""
                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES

        # Validation
        if not args.directory:
            console.print("[red]Error:[/] The following argument is required: directory")
            sys.exit(1)

        dir_path = Path(args.directory)
        if not dir_path.exists() or not dir_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
            sys.exit(1)

        logger.debug("Argument prompting complete.")
        return args


# ------------------
#    File Hasher
# ------------------

class FileHasher:
    """Computes SHA256 hashes for files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None


# ------------------
#  File Processor
# ------------------

class FileProcessor:
    """Processes files to collect their hashes plus date-modified."""

    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs

    def collect_file_hashes(self) -> List[Tuple[str, str, str]]:
        """
        Collect a list of (file_hash, relative_filename, date_modified).
        date_modified will be formatted as YYYY.MM.DD.
        """
        hash_entries = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path)
                if file_hash:
                    # Get date modified
                    mtime = file_path.stat().st_mtime
                    date_str = time.strftime("%Y.%m.%d", time.localtime(mtime))
                    hash_entries.append((file_hash, relative_path, date_str))
                    logger.debug(f"Processed: {relative_path}")

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            logger.warning(f"`{path}` is not accessible or not a file")
            return False
        return True


# ------------------
#  Hash File Manager
# ------------------

class HashFileManager:
    """Manages reading and writing hash files with date, filename, and hash."""

    def __init__(self, file_path: pathlib.Path):
        self.file_path = file_path

    def write(self, hash_entries: List[Tuple[str, str, str]]) -> None:
        """
        Writes lines in the format:
          #  YYYY.MM.DD  | FILENAME                                   | FILEHASH
          ''' 2025.03.07 | ''' - "filename.md"                 # | 'abc123...'
        """
        try:
            with self.file_path.open("w", encoding='utf-8') as f:
                f.write("#  YYYY.MM.DD  | FILENAME                                   | FILEHASH\n")

                # Sort entries by filename (case-insensitive) for consistency
                sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())

                max_length = 2 + max((len(filename) for _, filename, _ in sorted_entries), default=0)

                for file_hash, filename, date_str in sorted_entries:
                    # Insert double-quotes around filename and single-quotes around the hash.
                    padded_filename = f"\"{filename}\"".ljust(max_length)
                    line = f"''' {date_str} | ''' - {padded_filename} # | '{file_hash}'\n"
                    f.write(line)

            logger.info(f"Hash file written: {self.file_path.name}")
        except IOError as error:
            logger.error(f"Failed to write hash file: {error}")

    def read(self) -> List[Tuple[str, str, str]]:
        """
        Reads lines in the updated format or the old one, returning (hash, filename, date_str).
        """
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            logger.error(f"Failed to read hash file: {error}")
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str]]:
        """
        Attempts to parse lines of the form:
           ''' 2025.03.07 | ''' - "filename"   # | 'hash'
        or the old format:
           - 'filename' # | "hash"
        """
        line = line.strip()
        if not line or line.startswith("#"):
            return None

        # New format with triple single-quotes around date, double-quotes around filename, single-quotes around hash:
        # ''' YYYY.MM.DD | ''' - "some_file.txt" # | 'abc123...'
        new_format = r"^'''\s*(\d{4}\.\d{2}\.\d{2})\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        match_new = re.match(new_format, line)
        if match_new:
            date_str = match_new.group(1)
            filename = match_new.group(2)
            file_hash = match_new.group(3)
            return (file_hash, filename, date_str)

        # Old format fallback: - 'filename' # | "hash"
        old_format = r'^-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'
        match_old = re.match(old_format, line)
        if match_old:
            filename = match_old.group(1)
            file_hash = match_old.group(2)
            return (file_hash, filename, "")  # date_str not present in old format

        logger.warning(f"Invalid hash file entry: {line}")
        return None


# ------------------
#   File Renamer
# ------------------

class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(
        self,
        source_hashes: List[Tuple[str, str, str]],
        target_hashes: List[Tuple[str, str, str]],
        dry_run: bool = True
    ) -> bool:
        """
        We only need the (hash, filename) pair for matching and renaming.
        """
        src_map_entries = [(h, f) for (h, f, _) in source_hashes]
        tgt_map_entries = [(h, f) for (h, f, _) in target_hashes]

        source_map = self._map_hash_to_paths(src_map_entries)
        target_map = self._map_hash_to_paths(tgt_map_entries)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                logger.debug(f"Unchanged: {src_rel}")
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    # Already handled
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    logger.warning(f"No matching hash for: {src}")
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1.0
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            logger.debug(
                f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}"
            )
        else:
            logger.warning(f"No suitable match found for '{source}'")
        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        # Normalize to ASCII, remove special chars
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")
            return False
        if tgt.exists() and tgt != src:
            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')
        except OSError as error:
            logger.error(f"Failed to rename {src_rel}: {error}")

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            logger.warning("No files require renaming")
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            console = Console()
            console.print("\n")
            console.print(Panel(table, border_style="blue"))
            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            logger.warning("No files require renaming")

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            logger.warning(f"{operation}: Conflicts detected, some files skipped")
        else:
            logger.info(f"{operation} completed successfully")


# ------------------
#    File Editor
# ------------------

class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.warning(f"Unsupported platform: {sys.platform}")
        except Exception as error:
            logger.error(f"Failed to open editor: {error}")


# ----------------------------
#     Main App Class
# ----------------------------

class BatchRenameApp:
    """
    Encapsulates the CLI workflow for batch renaming:
      1) Parse & prompt for Arguments
      2) Initialize Logging
      3) Collect & write .original_hashes.py & .new_hashes.py
      4) Edit & confirm renaming
      5) Cleanup
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args: Optional[argparse.Namespace] = None

    def run(self) -> None:
        # 1) Parse & prompt
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Initialize Logging
        LoggerSetup.initialize_logging(self.args.verbosity)

        # 3) Handle the main process
        success = False
        try:
            self.handle_process_command()
            success = True
        except Exception as e:
            logger.error(f"Execution failed: {e}")

        # 4) Cleanup logs if needed and success
        if success and self.args.cleanup_logs:
            logger.remove()  # remove Loguru handler to release file
            log_file = Path("app.log.yml")
            if log_file.exists():
                console = Console()
                try:
                    log_file.unlink()
                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")

    def handle_process_command(self) -> None:
        console = Console()
        root_dir = pathlib.Path(self.args.directory).resolve()

        org_file = root_dir / ".original_hashes.py"
        new_file = root_dir / ".new_hashes.py"

        try:
            processor = FileProcessor(root_dir, self.args.include_subdirs)
            initial_hashes = processor.collect_file_hashes()

            # Write initial (with date) to both “original” and “new” for editing
            for file_path in (org_file, new_file):
                manager = HashFileManager(file_path)
                manager.write(initial_hashes)

            logger.info("Opening new hash file for editing...")
            FileEditor.open(new_file)

            if not Confirm.ask("\nProceed with renaming? [y/n]: ", default="y"):
                logger.warning("Operation cancelled by user")
                return

            org_manager = HashFileManager(org_file)
            new_manager = HashFileManager(new_file)

            renamer = FileRenamer(root_dir)
            # Dry run first
            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):
                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

        finally:
            # Cleanup: Attempt to delete the hash files
            for file_path in (org_file, new_file):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Cleaned up: {file_path.name}")
                except OSError as error:
                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")


# ------------------
#      Entry
# ------------------

def main() -> None:
    """Main entry point of the utility."""
    app = BatchRenameApp()
    app.run()


if __name__ == "__main__":
    main()
```

Just drop this into `RenameWithEditor.py` (or the equivalent file) to replace your existing code. It will now generate hash files in the format:

```
#  YYYY.MM.DD  | FILENAME                                   | FILEHASH
''' 2025.03.07 | ''' - "003_prompt001.md"                 # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'
''' 2025.03.07 | ''' - "003_prompt002.md"                 # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'
''' 2025.03.07 | ''' - "003_prompt003.md"                 # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'
```

where the date is derived from the file’s last-modified time.

## Message 10

**Evaluating options**

Considering an optional argument like `--show_time` for incorporating time in the date, with flexibility to parse different formats. This adjustment could streamline the process, enhancing efficiency and clarity.

**Integrating optional time parsing**

I’m working on a regex to handle optional time in the date format. This includes a new `--with-time` argument and updates for the date and heading formats in file processing.

## Message 11



The way it currently works is that it generates a textfile looking like this (three columns):



```

#  YYYY.MM.DD  | FILENAME                                | FILEHASH

''' 2025.03.07 | ''' - "filename1.txt"                 # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'

''' 2025.03.07 | ''' - "filename2.txt"                 # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'

''' 2025.03.07 | ''' - "filename3.txt"                 # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'

```



---





Your goal is to modify it such that it **optionally** includes the time also (instead of `YYYY.MM.DD` it would be `YYYY.MM.DD HH:MM`):



```

#  YYYY.MM.DD  HH:MM | FILENAME                          | FILEHASH

''' 2025.03.07 21:13 | ''' - "filename1.txt"           # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'

''' 2025.03.07 21:17 | ''' - "filename2.txt"           # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'

''' 2025.03.07 21:21 | ''' - "filename3.txt"           # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'

```




## Message 12

Below is a **complete, updated** script that **optionally** includes `HH:MM` in the date column rather than just `YYYY.MM.DD`. The new functionality is controlled by a command-line argument `--include-time`. When set, the generated lines contain both date and time.

<details>
<summary>How it Works</summary>

1. **New CLI Argument**:  
   - We add `--include-time` (boolean flag).  
   - If you run with `--include-time`, then the date portion becomes `YYYY.MM.DD HH:MM`.  
   - Otherwise, it remains `YYYY.MM.DD` (the old behavior).

2. **Passing the Flag**:  
   - The argument is extracted in `ArgumentHandler.parse_arguments`.  
   - It’s stored in `args.include_time`.

3. **Date/Time Formatting**:  
   - In `FileProcessor.collect_file_hashes`, we check `include_time`.  
   - If `True`, we do: `time.strftime("%Y.%m.%d %H:%M", time.localtime(mtime))`.  
   - If `False`, we do: `time.strftime("%Y.%m.%d", time.localtime(mtime))`.

4. **Header**:  
   - In `HashFileManager.write`, we output:
     - `#  YYYY.MM.DD  HH:MM | FILENAME           | FILEHASH` if `include_time` is set.
     - Otherwise: `#  YYYY.MM.DD  | FILENAME           | FILEHASH`.  
   - This ensures the header line matches the actual format of the date/time in each row.

Everything else (file hashing, renaming, logging) is unchanged. 
</details>

---

```python
""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """

import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from dataclasses import dataclass
from enum import Enum
# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# Loguru for logging
from loguru import logger


# ----------------------------
#        Configuration
# ----------------------------

class Config:
    """
    Holds default settings for the Batch Rename Utility.
    """
    DEFAULT_CLEANUP_LOGS = True
    USE_DEFAULT_SETTINGS = True
    DEFAULT_INCLUDE_SUBDIRECTORIES = False


# ----------------------------
#       Logger Setup
# ----------------------------

class LoggerSetup:
    """Sets up Loguru logger to write YAML logs to a file."""

    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        """
        Configure Loguru to log messages in YAML format to `log_file`.
        """
        def yaml_sink(message):
            record = message.record

            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            func_name_str = f"*{record['function']}"
            line_no = record["line"]
            msg = record["message"]

            # For multi-line messages, use a block scalar in YAML
            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote if it has special characters like ':'
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {func_name_str}",
                f"  lineno: {line_no}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        # Remove default handlers and add our YAML sink
        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging(verbosity: str = "INFO"):
        """
        Initialize YAML logging with a level mapped from the string `verbosity`.
        """
        level_map = {
            "quiet": "ERROR",
            "normal": "INFO",
            "verbose": "DEBUG",
            "debug": "DEBUG"
        }
        selected_level = level_map.get(verbosity.lower(), "INFO")
        LoggerSetup.setup_yaml_logging(level=selected_level)


# ----------------------------
#     Argument Handler
# ----------------------------

class ArgumentHandler:
    """Handles CLI argument parsing and optional prompting."""

    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(
            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."
        )
        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")
        parser.add_argument('--include_subdirs', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,
                            help="Include subdirectories in file processing")
        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")
        parser.add_argument('-v', '--verbosity',
                            choices=["quiet", "normal", "verbose", "debug"],
                            default="normal",
                            help="Set output verbosity level")

        # NEW ARG to include time:
        parser.add_argument('--include-time', action='store_true',
                            help="Include 'HH:MM' in the date column (e.g. YYYY.MM.DD HH:MM).")

        # Log Cleanup arguments
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',
                                        help="Clean up log files after successful execution")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',
                                        help="Do not clean up log files after successful execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        logger.debug("Prompting for missing arguments.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.DEFAULT_CLEANUP_LOGS)
            logger.debug(f"Use defaults: {use_defaults}")

            if not use_defaults:
                print_section("Directory")
                current_dir = args.directory or ""
                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()

                print_section("Include Subdirectories?")
                args.include_subdirs = Confirm.ask("Include subdirectories?", default=args.include_subdirs)

                print_section("Logging Verbosity")
                choices = ["quiet", "normal", "verbose", "debug"]
                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")
                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)
                args.verbosity = chosen_verbosity

                print_section("Include Time?")
                args.include_time = Confirm.ask("Include HH:MM in the date column?", default=False)

                print_section("Log Cleanup")
                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=args.cleanup_logs)
            else:
                args.directory = args.directory or ""
                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES

        # Validation
        if not args.directory:
            console.print("[red]Error:[/] The following argument is required: directory")
            sys.exit(1)

        dir_path = Path(args.directory)
        if not dir_path.exists() or not dir_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
            sys.exit(1)

        logger.debug("Argument prompting complete.")
        return args


# ------------------
#    File Hasher
# ------------------

class FileHasher:
    """Computes SHA256 hashes for files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None


# ------------------
#  File Processor
# ------------------

class FileProcessor:
    """Processes files to collect their hashes plus date/time (optionally)."""

    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool, include_time: bool = False):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs
        self.include_time = include_time

    def collect_file_hashes(self) -> List[Tuple[str, str, str]]:
        """
        Collect a list of (file_hash, relative_filename, date_str).
        If self.include_time is True, date_str = 'YYYY.MM.DD HH:MM'
        Otherwise, date_str = 'YYYY.MM.DD'.
        """
        hash_entries = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path)
                if file_hash:
                    # Generate the date/time string
                    mtime = file_path.stat().st_mtime
                    if self.include_time:
                        date_str = time.strftime("%Y.%m.%d %H:%M", time.localtime(mtime))
                    else:
                        date_str = time.strftime("%Y.%m.%d", time.localtime(mtime))

                    hash_entries.append((file_hash, relative_path, date_str))
                    logger.debug(f"Processed: {relative_path}")

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            logger.warning(f"`{path}` is not accessible or not a file")
            return False
        return True


# ------------------
#  Hash File Manager
# ------------------

class HashFileManager:
    """
    Manages reading and writing hash files (with optional time).
    We accept an additional param 'include_time' to adjust the header.
    """

    def __init__(self, file_path: pathlib.Path, include_time: bool = False):
        self.file_path = file_path
        self.include_time = include_time

    def write(self, hash_entries: List[Tuple[str, str, str]]) -> None:
        """
        Writes lines in one of two formats for the header:
          #  YYYY.MM.DD  | FILENAME                            | FILEHASH
          or
          #  YYYY.MM.DD  HH:MM | FILENAME                       | FILEHASH

        Then for each file:
          ''' 2025.03.07 | ''' - "filename.txt"           # | 'abc123...'
        or if time is included:
          ''' 2025.03.07 21:13 | ''' - "filename.txt"     # | 'abc123...'
        """
        try:
            with self.file_path.open("w", encoding='utf-8') as f:
                # Adjust the header line depending on whether time is included
                if self.include_time:
                    f.write("#  YYYY.MM.DD  HH:MM | FILENAME                            | FILEHASH\n")
                else:
                    f.write("#  YYYY.MM.DD  | FILENAME                                  | FILEHASH\n")

                # Sort by filename (case-insensitive)
                sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())

                # Determine padding for the filename column
                max_length = 2 + max((len(filename) for _, filename, _ in sorted_entries), default=0)

                for file_hash, filename, date_str in sorted_entries:
                    # Insert double-quotes around filename, single-quotes around the hash
                    padded_filename = f"\"{filename}\"".ljust(max_length)
                    line = f"''' {date_str} | ''' - {padded_filename} # | '{file_hash}'\n"
                    f.write(line)

            logger.info(f"Hash file written: {self.file_path.name}")
        except IOError as error:
            logger.error(f"Failed to write hash file: {error}")

    def read(self) -> List[Tuple[str, str, str]]:
        """
        Reads lines in the updated format or the old one, returning (hash, filename, date_str).
        """
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            logger.error(f"Failed to read hash file: {error}")
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str]]:
        """
        Attempts to parse lines of the form:
           ''' 2025.03.07 | ''' - "filename"   # | 'hash'
        or
           ''' 2025.03.07 21:17 | ''' - "filename"   # | 'hash'
        or the old format:
           - 'filename' # | "hash"
        """
        line = line.strip()
        if not line or line.startswith("#"):
            return None

        # Variation that handles optional HH:MM after the date
        #  e.g. ''' 2025.03.07 21:17 | ''' - "filename"   # | 'abcdef1234...'
        new_format = r"^'''\s*(\d{4}\.\d{2}\.\d{2}(?:\s+\d{2}:\d{2})?)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        match_new = re.match(new_format, line)
        if match_new:
            date_str = match_new.group(1)   # can be "YYYY.MM.DD" or "YYYY.MM.DD HH:MM"
            filename = match_new.group(2)
            file_hash = match_new.group(3)
            return (file_hash, filename, date_str)

        # Old format fallback: - 'filename' # | "hash"
        old_format = r'^-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'
        match_old = re.match(old_format, line)
        if match_old:
            filename = match_old.group(1)
            file_hash = match_old.group(2)
            return (file_hash, filename, "")  # no date info

        logger.warning(f"Invalid hash file entry: {line}")
        return None


# ------------------
#   File Renamer
# ------------------

class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(
        self,
        source_hashes: List[Tuple[str, str, str]],
        target_hashes: List[Tuple[str, str, str]],
        dry_run: bool = True
    ) -> bool:
        """
        We only need the (hash, filename) pair for matching and renaming.
        """
        src_map_entries = [(h, f) for (h, f, _) in source_hashes]
        tgt_map_entries = [(h, f) for (h, f, _) in target_hashes]

        source_map = self._map_hash_to_paths(src_map_entries)
        target_map = self._map_hash_to_paths(tgt_map_entries)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                logger.debug(f"Unchanged: {src_rel}")
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    # Already handled
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    logger.warning(f"No matching hash for: {src}")
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1.0
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            logger.debug(
                f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}"
            )
        else:
            logger.warning(f"No suitable match found for '{source}'")
        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        # Normalize to ASCII, remove special chars
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")
            return False
        if tgt.exists() and tgt != src:
            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')
        except OSError as error:
            logger.error(f"Failed to rename {src_rel}: {error}")

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            logger.warning("No files require renaming")
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            console = Console()
            console.print("\n")
            console.print(Panel(table, border_style="blue"))
            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            logger.warning("No files require renaming")

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            logger.warning(f"{operation}: Conflicts detected, some files skipped")
        else:
            logger.info(f"{operation} completed successfully")


# ------------------
#    File Editor
# ------------------

class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.warning(f"Unsupported platform: {sys.platform}")
        except Exception as error:
            logger.error(f"Failed to open editor: {error}")


# ----------------------------
#     Main App Class
# ----------------------------

class BatchRenameApp:
    """
    Encapsulates the CLI workflow for batch renaming:
      1) Parse & prompt for Arguments
      2) Initialize Logging
      3) Collect & write .original_hashes.py & .new_hashes.py
      4) Edit & confirm renaming
      5) Cleanup
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args: Optional[argparse.Namespace] = None

    def run(self) -> None:
        # 1) Parse & prompt
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Initialize Logging
        LoggerSetup.initialize_logging(self.args.verbosity)

        # 3) Handle the main process
        success = False
        try:
            self.handle_process_command()
            success = True
        except Exception as e:
            logger.error(f"Execution failed: {e}")

        # 4) Cleanup logs if needed and success
        if success and self.args.cleanup_logs:
            logger.remove()  # remove Loguru handler to release file
            log_file = Path("app.log.yml")
            if log_file.exists():
                console = Console()
                try:
                    log_file.unlink()
                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")

    def handle_process_command(self) -> None:
        console = Console()
        root_dir = pathlib.Path(self.args.directory).resolve()

        org_file = root_dir / ".original_hashes.py"
        new_file = root_dir / ".new_hashes.py"

        try:
            # Pass include_time to the FileProcessor
            processor = FileProcessor(root_dir, self.args.include_subdirs, self.args.include_time)
            initial_hashes = processor.collect_file_hashes()

            # Pass include_time to HashFileManager so it writes the correct header
            for file_path in (org_file, new_file):
                manager = HashFileManager(file_path, include_time=self.args.include_time)
                manager.write(initial_hashes)

            logger.info("Opening new hash file for editing...")
            FileEditor.open(new_file)

            if not Confirm.ask("\nProceed with renaming? [y/n]: ", default="y"):
                logger.warning("Operation cancelled by user")
                return

            org_manager = HashFileManager(org_file)  # time usage not strictly needed for reading
            new_manager = HashFileManager(new_file)

            renamer = FileRenamer(root_dir)
            # Dry run first
            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):
                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

        finally:
            # Cleanup: Attempt to delete the hash files
            for file_path in (org_file, new_file):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Cleaned up: {file_path.name}")
                except OSError as error:
                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")


# ------------------
#      Entry
# ------------------

def main() -> None:
    """Main entry point of the utility."""
    app = BatchRenameApp()
    app.run()


if __name__ == "__main__":
    main()
```

---

## Usage

- **Normal mode** (only date, no time):  
  ```bash
  python RenameWithEditor.py --directory path/to/files
  ```
  The output lines will look like:
  ```
  #  YYYY.MM.DD  | FILENAME                | FILEHASH
  ''' 2025.03.07 | ''' - "filename1.txt" # | '1234abcd...'
  ...
  ```

- **Including time** (date + time):  
  ```bash
  python RenameWithEditor.py --directory path/to/files --include-time
  ```
  The output lines will look like:
  ```
  #  YYYY.MM.DD  HH:MM | FILENAME                          | FILEHASH
  ''' 2025.03.07 21:17 | ''' - "filename1.txt"           # | '1234abcd...'
  ...
  ```

Everything else remains the same—same interactive prompts, same rename logic, etc.

## Message 13



Here's the current code for  `RenameWithEditor.py`:



```python

""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """



import argparse

import hashlib

import os

import pathlib

import shutil

import subprocess

import sys

import re

import unicodedata

import time

from pathlib import Path

from typing import Dict, List, Optional, Set, Tuple



from dataclasses import dataclass

from enum import Enum



# Rich imports for console interactions

from rich.console import Console

from rich.prompt import Prompt, Confirm

from rich.panel import Panel

from rich.table import Table

from rich.box import ROUNDED



# Loguru for logging

from loguru import logger





# =======================================================

# Configuration

# =======================================================

class Config:

    """

    Holds default settings for the Batch Rename Utility.

    """

    DEFAULT_CLEANUP_LOGS = True

    USE_DEFAULT_SETTINGS = True

    DEFAULT_INCLUDE_SUBDIRECTORIES = False

    DEFAULT_INCLUDE_TIMESTAMP = True





# =======================================================

# Logger Setup

# =======================================================

class LoggerSetup:

    """Sets up Loguru logger to write YAML logs to a file."""



    @staticmethod

    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):

        """

        Configure Loguru to log messages in YAML format to `log_file`.

        """

        def yaml_sink(message):

            record = message.record



            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')

            level_str = f"!{record['level'].name}"

            name_str = record['name']

            func_name_str = f"*{record['function']}"

            line_no = record["line"]

            msg = record["message"]



            # For multi-line messages, use a block scalar in YAML

            if "\n" in msg:

                lines = msg.split("\n")

                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)

            else:

                # Quote if it has special characters like ':'

                if ":" in msg:

                    message_str = f"'{msg}'"

                else:

                    message_str = msg



            yaml_lines = [

                f"- time: {time_str}",

                f"  level: {level_str}",

                f"  name: {name_str}",

                f"  funcName: {func_name_str}",

                f"  lineno: {line_no}",

                f"  message: {message_str}",

                ""

            ]



            with open(log_file, "a", encoding="utf-8") as f:

                f.write("\n".join(yaml_lines) + "\n")



        # Remove default handlers and add our YAML sink

        logger.remove()

        logger.add(yaml_sink, level=level, enqueue=True)



    @staticmethod

    def initialize_logging(verbosity: str = "INFO"):

        """

        Initialize YAML logging with a level mapped from the string `verbosity`.

        Maps:

          quiet -> ERROR

          normal -> INFO

          verbose -> DEBUG

          debug -> DEBUG

        """

        level_map = {

            "quiet": "ERROR",

            "normal": "INFO",

            "verbose": "DEBUG",

            "debug": "DEBUG"

        }

        selected_level = level_map.get(verbosity.lower(), "INFO")

        LoggerSetup.setup_yaml_logging(level=selected_level)





# =======================================================

# Argument Handler

# =======================================================

class ArgumentHandler:

    """Handles CLI argument parsing and optional prompting."""



    def __init__(self):

        self.parser = self.parse_arguments()



    @staticmethod

    def parse_arguments():

        logger.debug("Setting up argument parser.")

        parser = argparse.ArgumentParser(

            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."

        )



        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")



        parser.add_argument('--include_subdirs', action='store_true',

                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,

                            help="Include subdirectories in file processing")



        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")



        parser.add_argument('-v', '--verbosity',

                            choices=["quiet", "normal", "verbose", "debug"],

                            default="normal",

                            help="Set output verbosity level")



        parser.add_argument('--include-time', action='store_true',

                            default=Config.DEFAULT_INCLUDE_TIMESTAMP,

                            help="Include 'HH:MM' in the date column (e.g. YYYY.MM.DD HH:MM).")



        # Log Cleanup arguments

        cleanup_logs_group = parser.add_mutually_exclusive_group()

        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',

                                        help="Clean up log files after successful execution")

        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',

                                        help="Do not clean up log files after successful execution")

        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)



        logger.debug("Argument parser setup complete.")

        return parser



    def get_arguments(self):

        return self.parser.parse_args()



    def prompt_for_missing_arguments(self, args):

        logger.debug("Prompting for missing arguments.")

        console = Console()



        def print_section(title):

            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)



        if args.prompt:

            print_section("Default Settings")

            use_defaults = Confirm.ask("Use default settings?", default=Config.USE_DEFAULT_SETTINGS)

            logger.debug(f"Use defaults: {use_defaults}")



            if not use_defaults:

                # Prompt for input directory

                print_section("Directory")

                current_dir = args.directory or ""

                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()



                # Prompt for including subdirectories

                print_section("Include Subdirectories?")

                args.include_subdirs = Confirm.ask("Include subdirectories?", default=True)



                print_section("Logging Verbosity")

                choices = ["quiet", "normal", "verbose", "debug"]

                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")

                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)

                args.verbosity = chosen_verbosity



                # Prompt for including timestamps

                print_section("Include Time?")

                args.include_time = Confirm.ask("Include HH:MM in the date column?", default=True)



                # Prompt for cleanup logs

                print_section("Log Cleanup")

                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=args.cleanup_logs)

            else:

                # Assign defaults if none provided

                args.directory = args.directory or ""

                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES

                # Let the existing defaults stand for verbosity and cleanup_logs



        # Validation

        if not args.directory:

            console.print("[red]Error:[/] The following argument is required: directory")

            sys.exit(1)



        dir_path = Path(args.directory)

        if not dir_path.exists() or not dir_path.is_dir():

            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")

            sys.exit(1)



        logger.debug("Argument prompting complete.")

        return args





# =======================================================

# File Hasher

# =======================================================

class FileHasher:

    """Computes SHA256 hashes for files."""

    CHUNK_SIZE = 4096



    @staticmethod

    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:

        sha256 = hashlib.sha256()

        try:

            with file_path.open('rb') as f:

                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):

                    sha256.update(chunk)

            return sha256.hexdigest()

        except IOError as error:

            logger.error(f"Error reading `{file_path}`: {error}")

            return None





# =======================================================

#  File Processor

# =======================================================

class FileProcessor:

    """Processes files to collect their hashes plus date/time (optionally)."""



    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool, include_time: bool = False):

        self.root_dir = root_dir

        self.include_subdirs = include_subdirs

        self.include_time = include_time



    def collect_file_hashes(self) -> List[Tuple[str, str, str]]:

        """

        Collect a list of (file_hash, relative_filename, date_str).

        If self.include_time is True, date_str = 'YYYY.MM.DD HH:MM'

        Otherwise, date_str = 'YYYY.MM.DD'.

        """

        hash_entries = []

        for root, _, files in os.walk(self.root_dir):

            for filename in files:

                file_path = pathlib.Path(root) / filename

                if not self._is_accessible_file(file_path):

                    continue



                relative_path = file_path.relative_to(self.root_dir).as_posix()

                file_hash = FileHasher.compute_sha256(file_path)

                if file_hash:

                    # Generate the date/time string

                    mtime = file_path.stat().st_mtime

                    if self.include_time:

                        date_str = time.strftime("%Y.%m.%d %H:%M", time.localtime(mtime))

                    else:

                        date_str = time.strftime("%Y.%m.%d", time.localtime(mtime))



                    hash_entries.append((file_hash, relative_path, date_str))

                    logger.debug(f"Processed: {relative_path}")



            if not self.include_subdirs:

                break

        return hash_entries



    def _is_accessible_file(self, path: pathlib.Path) -> bool:

        if not path.is_file() or not os.access(path, os.R_OK):

            logger.warning(f"`{path}` is not accessible or not a file")

            return False

        return True





# =======================================================

# Hash File Manager

# =======================================================

class HashFileManager:

    """

    Manages reading and writing hash files (with optional time).

    We accept an additional param 'include_time' to adjust the header.

    """



    def __init__(self, file_path: pathlib.Path, include_time: bool = False):

        self.file_path = file_path

        self.include_time = include_time



    def write(self, hash_entries: List[Tuple[str, str, str]]) -> None:

        """

        Writes lines in one of two formats for the header:

            #  YYYY.MM.DD  | FILENAME                                | FILEHASH

            ''' 2025.03.07 | ''' - "filename1.txt"                 # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'

            ''' 2025.03.07 | ''' - "filename2.txt"                 # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'

            ''' 2025.03.07 | ''' - "filename3.txt"                 # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'



            or:



            #  YYYY.MM.DD  HH:MM | FILENAME                          | FILEHASH

            ''' 2025.03.07 21:13 | ''' - "filename1.txt"           # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'

            ''' 2025.03.07 21:17 | ''' - "filename2.txt"           # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'

            ''' 2025.03.07 21:21 | ''' - "filename3.txt"           # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'

        """

        try:

            with self.file_path.open("w", encoding='utf-8') as f:

                # Adjust the header line depending on whether time is included

                if self.include_time:

                    f.write("#  YYYY.MM.DD  HH:MM | FILENAME                            | FILEHASH\n")

                else:

                    f.write("#  YYYY.MM.DD  | FILENAME                                  | FILEHASH\n")



                # Sort by filename (case-insensitive)

                sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())



                # Determine padding for the filename column

                max_length = 2 + max((len(filename) for _, filename, _ in sorted_entries), default=0)



                for file_hash, filename, date_str in sorted_entries:

                    # Insert double-quotes around filename, single-quotes around the hash

                    padded_filename = f"\"{filename}\"".ljust(max_length)

                    line = f"''' {date_str} | ''' - {padded_filename} # | '{file_hash}'\n"

                    f.write(line)



            logger.info(f"Hash file written: {self.file_path.name}")

        except IOError as error:

            logger.error(f"Failed to write hash file: {error}")



    def read(self) -> List[Tuple[str, str, str]]:

        """

        Reads lines in the updated format or the old one, returning (hash, filename, date_str).

        """

        hash_entries = []

        try:

            with self.file_path.open("r", encoding='utf-8') as f:

                for line in f:

                    entry = self._parse_hash_entry(line)

                    if entry:

                        hash_entries.append(entry)

        except IOError as error:

            logger.error(f"Failed to read hash file: {error}")

        return hash_entries



    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str]]:

        """

        Attempts to parse lines of the form:

           ''' 2025.03.07 | ''' - "filename"   # | 'hash'

        or

           ''' 2025.03.07 21:17 | ''' - "filename"   # | 'hash'

        or the old format:

           - 'filename' # | "hash"

        """

        line = line.strip()

        if not line or line.startswith("#"):

            return None



        # Variation that handles optional HH:MM after the date

        #  e.g. ''' 2025.03.07 21:17 | ''' - "filename"   # | 'abcdef1234...'

        new_format = r"^'''\s*(\d{4}\.\d{2}\.\d{2}(?:\s+\d{2}:\d{2})?)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"

        match_new = re.match(new_format, line)

        if match_new:

            date_str = match_new.group(1)   # can be "YYYY.MM.DD" or "YYYY.MM.DD HH:MM"

            filename = match_new.group(2)

            file_hash = match_new.group(3)

            return (file_hash, filename, date_str)



        # Old format fallback: - 'filename' # | "hash"

        old_format = r'^-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'

        match_old = re.match(old_format, line)

        if match_old:

            filename = match_old.group(1)

            file_hash = match_old.group(2)

            return (file_hash, filename, "")  # no date info



        logger.warning(f"Invalid hash file entry: {line}")

        return None





# =======================================================

# File Renamer

# =======================================================

class FileRenamer:

    """Handles the renaming of files based on hash comparisons."""



    def __init__(self, root_dir: pathlib.Path):

        self.root_dir = root_dir



    def execute(

        self,

        source_hashes: List[Tuple[str, str, str]],

        target_hashes: List[Tuple[str, str, str]],

        dry_run: bool = True

    ) -> bool:

        """

        We only need the (hash, filename) pair for matching and renaming.

        """

        src_map_entries = [(h, f) for (h, f, _) in source_hashes]

        tgt_map_entries = [(h, f) for (h, f, _) in target_hashes]



        source_map = self._map_hash_to_paths(src_map_entries)

        target_map = self._map_hash_to_paths(tgt_map_entries)



        rename_pairs = self._determine_rename_pairs(source_map, target_map)

        conflicts = False



        if dry_run:

            self._preview_renames(rename_pairs)



        for src_rel, tgt_rel in rename_pairs:

            src_path = self.root_dir / src_rel

            tgt_path = self.root_dir / tgt_rel



            if src_rel == tgt_rel:

                logger.debug(f"Unchanged: {src_rel}")

                continue



            if not self._validate_paths(src_path, tgt_path):

                conflicts = True

                continue



            if dry_run:

                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')

            else:

                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)



        self._log_completion(dry_run, conflicts)

        return not conflicts



    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:

        hash_map: Dict[str, List[str]] = {}

        for file_hash, path in hash_entries:

            hash_map.setdefault(file_hash, []).append(path)

        return hash_map



    def _determine_rename_pairs(

        self,

        source_map: Dict[str, List[str]],

        target_map: Dict[str, List[str]],

    ) -> List[Tuple[str, str]]:

        pairs: List[Tuple[str, str]] = []

        processed_targets: Set[str] = set()



        for file_hash, src_paths in source_map.items():

            tgt_paths = target_map.get(file_hash, [])

            for src in src_paths:

                if any(src == pair[0] for pair in pairs):

                    # Already handled

                    continue



                available_tgts = [t for t in tgt_paths if t not in processed_targets]

                if not available_tgts:

                    logger.warning(f"No matching hash for: {src}")

                    continue



                best_match = self._select_best_match(src, available_tgts)

                if best_match:

                    pairs.append((src, best_match))

                    processed_targets.add(best_match)



        return pairs



    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:

        source_clean = self._clean_name(source)

        best_similarity = -1.0

        best_target = None



        for tgt in targets:

            tgt_clean = self._clean_name(tgt)

            similarity = self._name_similarity(source_clean, tgt_clean)

            if similarity > best_similarity:

                best_similarity = similarity

                best_target = tgt



        if best_target:

            logger.debug(

                f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}"

            )

        else:

            logger.warning(f"No suitable match found for '{source}'")

        return best_target



    @staticmethod

    def _name_similarity(name1: str, name2: str) -> float:

        matches = sum(a == b for a, b in zip(name1, name2))

        max_len = max(len(name1), len(name2))

        return matches / max_len if max_len else 0



    @staticmethod

    def _clean_name(name: str) -> str:

        # Normalize to ASCII, remove special chars

        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')

        name = name.lower()

        name = pathlib.Path(name).stem

        name = re.sub(r'[^a-z0-9]', '', name)

        return name



    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:

        if not src.exists():

            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")

            return False

        if tgt.exists() and tgt != src:

            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")

            return False

        return True



    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:

        try:

            tgt.parent.mkdir(parents=True, exist_ok=True)

            shutil.move(str(src), str(tgt))

            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')

        except OSError as error:

            logger.error(f"Failed to rename {src_rel}: {error}")



    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:

        if not rename_pairs:

            logger.warning("No files require renaming")

            return



        table = Table(

            title="Pending Rename Operations",

            show_header=True,

            header_style="bold blue",

            box=ROUNDED

        )

        table.add_column("Operation", style="cyan", width=4)

        table.add_column("Source", style="white")

        table.add_column("Target", style="green")



        changes = 0

        for src, tgt in rename_pairs:

            if src != tgt:

                table.add_row("→", src, tgt)

                changes += 1



        if changes:

            console = Console()

            console.print("\n")

            console.print(Panel(table, border_style="blue"))

            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")

        else:

            logger.warning("No files require renaming")



    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:

        operation = "Dry run" if dry_run else "File renaming"

        if has_conflicts:

            logger.warning(f"{operation}: Conflicts detected, some files skipped")

        else:

            logger.info(f"{operation} completed successfully")





# =======================================================

# File Editor

# =======================================================

class FileEditor:

    """Opens files using the default system editor."""



    @staticmethod

    def open(file_path: pathlib.Path) -> None:

        try:

            if sys.platform.startswith('darwin'):

                subprocess.call(['open', str(file_path)])

            elif os.name == 'nt':

                os.startfile(str(file_path))

            elif os.name == 'posix':

                subprocess.call(['xdg-open', str(file_path)])

            else:

                logger.warning(f"Unsupported platform: {sys.platform}")

        except Exception as error:

            logger.error(f"Failed to open editor: {error}")





# =======================================================

# Main App Class

# =======================================================

class BatchRenameApp:

    """

    Encapsulates the CLI workflow for batch renaming:

      1) Parse & prompt for Arguments

      2) Initialize Logging

      3) Collect & write .original_hashes.py & .new_hashes.py

      4) Edit & confirm renaming

      5) Cleanup

    """



    def __init__(self):

        self.arg_handler = ArgumentHandler()

        self.args: Optional[argparse.Namespace] = None



    def run(self) -> None:

        # 1) Parse & prompt

        self.args = self.arg_handler.get_arguments()

        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)



        # 2) Initialize Logging

        LoggerSetup.initialize_logging(self.args.verbosity)



        # 3) Handle the main process

        success = False

        try:

            self.handle_process_command()

            success = True

        except Exception as e:

            logger.error(f"Execution failed: {e}")



        # 4) Cleanup logs if needed and success

        if success and self.args.cleanup_logs:

            logger.remove()  # remove Loguru handler to release file

            log_file = Path("app.log.yml")

            if log_file.exists():

                console = Console()

                try:

                    log_file.unlink()

                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")

                except Exception as e:

                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")



    def handle_process_command(self) -> None:

        console = Console()

        root_dir = pathlib.Path(self.args.directory).resolve()



        org_file = root_dir / ".original_hashes.py"

        new_file = root_dir / ".new_hashes.py"



        try:

            # Pass include_time to the FileProcessor

            processor = FileProcessor(root_dir, self.args.include_subdirs, self.args.include_time)

            initial_hashes = processor.collect_file_hashes()



            # Pass include_time to HashFileManager so it writes the correct header

            for file_path in (org_file, new_file):

                manager = HashFileManager(file_path, include_time=self.args.include_time)

                manager.write(initial_hashes)



            logger.info("Opening new hash file for editing...")

            FileEditor.open(new_file)



            if not Confirm.ask("\nProceed with renaming? [y/n]: ", default="y"):

                logger.warning("Operation cancelled by user")

                return



            org_manager = HashFileManager(org_file)

            new_manager = HashFileManager(new_file)



            renamer = FileRenamer(root_dir)

            # Dry run first

            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):

                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):

                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)



        finally:

            # Cleanup: Attempt to delete the hash files

            for file_path in (org_file, new_file):

                try:

                    if file_path.exists():

                        file_path.unlink()

                        logger.info(f"Cleaned up: {file_path.name}")

                except OSError as error:

                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")





# =======================================================

# Execution entrypoint

# =======================================================

def main() -> None:

    """Main entry point of the utility."""

    app = BatchRenameApp()

    app.run()





if __name__ == "__main__":

    main()

```



---





Similar to the optional date, can you add optional column for file's folderdepth? Example:



```

#  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                          | FILEHASH

''' 2025.03.07 21:13 | 5     | ''' - "filename1.txt"           # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'

''' 2025.03.07 21:17 | 5     | ''' - "filename2.txt"           # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'

''' 2025.03.07 21:21 | 5     | ''' - "filename3.txt"           # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'

```




## Message 14

**Adapting and enhancing**

I’m planning to add an optional column for directory depth using a new CLI argument `--include-depth`. This will adjust the code to store and manage folder depths efficiently in the `HashFileManager`.

**Assembling the data structure**

I’m pulling together a tuple that includes file hash, relative filename, date string, and depth, parsing it from left to right, then storing this new format in the `HashFileManager`.

**Deciding on depth calculation**

I’m figuring out how to define `folder_depth` with options: counting path parts minus one or adding one for each slash. This will clear up how deep the file is in its folder hierarchy.

**Refining depth calculation**

I’m working on a standard formula to determine depth by counting the number of path components in the relative path. Also, I propose adding an optional `--include-depth` argument for clarity.

**Introducing flexibility**

I'm mapping out a new 'super format' to manage the flexible inclusion of depth and date-time columns. This will streamline handling these variables consistently in our code.

**Single new format**

I’m proposing a unified format where a single line accommodates depth and date-time, ensuring consistent parsing and attendance to different scenarios.

**Creating new format**

I’m working through a single format for both reading and writing, incorporating depth and date-time variables. This will simplify parsing and maintain consistency.

**Mapping out regex patterns**

I’m defining four distinct regex patterns to handle different combinations of depth and date-time fields. This approach will ensure accurate parsing for each unique scenario.

**Piecing together regex patterns**

I’m outlining four regex patterns to capture depth and date-time fields, ensuring all scenarios are addressed for accurate data parsing. Here are the patterns:

**Mapping out enhancements**

I’m piecing together steps to integrate folder depth into the code. This involves updating arguments, adjusting file processing, and refining parsing to ensure accurate data storage.

## Message 15

Below is a **complete, updated** script that adds an **optional folder-depth column** (labeled **DEPTH**)—similar to how we optionally include time. Now you can specify a new CLI argument `--include-depth`. If set, each line in the hash file will show the folder-depth of that file in a separate column.

Here’s how it works at a glance:

1. **New Flag**: `--include-depth`  
   If you run with `--include-depth`, the hash file gets a **DEPTH** column.  
   Otherwise, the output lines remain unchanged (no depth column).

2. **Computing Depth**:  
   - We compute depth by counting the number of path components in the file’s relative path.  
   - For example, if a file is `folder/subfolder/filename.txt`, its relative path `Path("folder/subfolder/filename.txt").parts` yields `("folder", "subfolder", "filename.txt")`, which has a length of 3.  

3. **Writing**:  
   - If both `--include-time` and `--include-depth` are used, the header and lines look like:
     ```
     #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                 | FILEHASH
     ''' 2025.03.07 21:17 | 3     | ''' - "subfolder/filename.txt" # | 'abcdef1234...'
     ```
   - If you omit one or both, the script reverts to the previous column layout.

4. **Reading**:  
   - The script attempts to parse the new “depth” lines if present, but it will also handle older lines (with no depth/time) seamlessly.  
   - Folder depth is not used by the renamer itself; it’s just informational.

Everything else—prompts, hashing, and renaming logic—remains intact.

---

```python
""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """

import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from dataclasses import dataclass
from enum import Enum

# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# Loguru for logging
from loguru import logger


# =======================================================
# Configuration
# =======================================================
class Config:
    """
    Holds default settings for the Batch Rename Utility.
    """
    DEFAULT_CLEANUP_LOGS = True
    USE_DEFAULT_SETTINGS = True
    DEFAULT_INCLUDE_SUBDIRECTORIES = False
    DEFAULT_INCLUDE_TIMESTAMP = True
    # New default for depth:
    DEFAULT_INCLUDE_DEPTH = False


# =======================================================
# Logger Setup
# =======================================================
class LoggerSetup:
    """Sets up Loguru logger to write YAML logs to a file."""

    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        """
        Configure Loguru to log messages in YAML format to `log_file`.
        """
        def yaml_sink(message):
            record = message.record

            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            func_name_str = f"*{record['function']}"
            line_no = record["line"]
            msg = record["message"]

            # For multi-line messages, use a block scalar in YAML
            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote if it has special characters like ':'
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {func_name_str}",
                f"  lineno: {line_no}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        # Remove default handlers and add our YAML sink
        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging(verbosity: str = "INFO"):
        """
        Initialize YAML logging with a level mapped from the string `verbosity`.
        """
        level_map = {
            "quiet": "ERROR",
            "normal": "INFO",
            "verbose": "DEBUG",
            "debug": "DEBUG"
        }
        selected_level = level_map.get(verbosity.lower(), "INFO")
        LoggerSetup.setup_yaml_logging(level=selected_level)


# =======================================================
# Argument Handler
# =======================================================
class ArgumentHandler:
    """Handles CLI argument parsing and optional prompting."""

    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(
            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."
        )

        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")

        parser.add_argument('--include_subdirs', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,
                            help="Include subdirectories in file processing")

        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")

        parser.add_argument('-v', '--verbosity',
                            choices=["quiet", "normal", "verbose", "debug"],
                            default="normal",
                            help="Set output verbosity level")

        parser.add_argument('--include-time', action='store_true',
                            default=Config.DEFAULT_INCLUDE_TIMESTAMP,
                            help="Include 'HH:MM' in the date column (e.g. YYYY.MM.DD HH:MM).")

        # NEW: optional folder depth
        parser.add_argument('--include-depth', action='store_true',
                            default=Config.DEFAULT_INCLUDE_DEPTH,
                            help="Include a folder depth column (integer).")

        # Log Cleanup arguments
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',
                                        help="Clean up log files after successful execution")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',
                                        help="Do not clean up log files after successful execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        logger.debug("Prompting for missing arguments.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.USE_DEFAULT_SETTINGS)
            logger.debug(f"Use defaults: {use_defaults}")

            if not use_defaults:
                # Prompt for input directory
                print_section("Directory")
                current_dir = args.directory or ""
                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()

                # Prompt for including subdirectories
                print_section("Include Subdirectories?")
                args.include_subdirs = Confirm.ask("Include subdirectories?", default=True)

                # Prompt for logging verbosity
                print_section("Logging Verbosity")
                choices = ["quiet", "normal", "verbose", "debug"]
                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")
                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)
                args.verbosity = chosen_verbosity

                # Prompt for including timestamps
                print_section("Include Time?")
                args.include_time = Confirm.ask("Include HH:MM in the date column?", default=True)

                # Prompt for including depth
                print_section("Include Folder Depth?")
                args.include_depth = Confirm.ask("Include folder depth column?", default=False)

                # Prompt for cleanup logs
                print_section("Log Cleanup")
                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=args.cleanup_logs)
            else:
                # Assign defaults if none provided
                args.directory = args.directory or ""
                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES
                # Let the existing defaults stand for verbosity, cleanup_logs, etc.

        # Validation
        if not args.directory:
            console.print("[red]Error:[/] The following argument is required: directory")
            sys.exit(1)

        dir_path = Path(args.directory)
        if not dir_path.exists() or not dir_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
            sys.exit(1)

        logger.debug("Argument prompting complete.")
        return args


# =======================================================
# File Hasher
# =======================================================
class FileHasher:
    """Computes SHA256 hashes for files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None


# =======================================================
# File Processor
# =======================================================
class FileProcessor:
    """Processes files to collect their hashes + optional date/time + optional depth."""

    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool, include_time: bool = False, include_depth: bool = False):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs
        self.include_time = include_time
        self.include_depth = include_depth

    def collect_file_hashes(self) -> List[Tuple[str, str, str, str]]:
        """
        Collect a list of (file_hash, relative_filename, date_str, depth_str).
          - date_str:
              if include_time = True, "YYYY.MM.DD HH:MM"
              else "YYYY.MM.DD"
          - depth_str:
              if include_depth = True, the integer depth as string (e.g. "3")
              else "" (empty).
        """
        hash_entries: List[Tuple[str, str, str, str]] = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path)
                if file_hash:
                    # 1) Generate the date/time string
                    mtime = file_path.stat().st_mtime
                    if self.include_time:
                        date_str = time.strftime("%Y.%m.%d %H:%M", time.localtime(mtime))
                    else:
                        date_str = time.strftime("%Y.%m.%d", time.localtime(mtime))

                    # 2) Compute depth if requested
                    if self.include_depth:
                        depth_val = len(Path(relative_path).parts)
                        depth_str = str(depth_val)
                    else:
                        depth_str = ""

                    hash_entries.append((file_hash, relative_path, date_str, depth_str))
                    logger.debug(f"Processed: {relative_path}")

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            logger.warning(f"`{path}` is not accessible or not a file")
            return False
        return True


# =======================================================
# Hash File Manager
# =======================================================
class HashFileManager:
    """
    Manages reading and writing hash files (with optional time + optional depth).
    If 'include_time' -> we add date/time to lines.
    If 'include_depth' -> we add a DEPTH column.
    """

    def __init__(self, file_path: pathlib.Path, include_time: bool = False, include_depth: bool = False):
        self.file_path = file_path
        self.include_time = include_time
        self.include_depth = include_depth

    def write(self, hash_entries: List[Tuple[str, str, str, str]]) -> None:
        """
        Writes lines in one of these formats for the header:

          #  YYYY.MM.DD  | FILENAME                           | FILEHASH
          #  YYYY.MM.DD  HH:MM | FILENAME                     | FILEHASH
          #  YYYY.MM.DD  | DEPTH | FILENAME                   | FILEHASH
          #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME             | FILEHASH

        Each data line is like:

          ''' 2025.03.07 | ''' - "filename.txt" # | 'abc123...'
          ''' 2025.03.07 21:13 | 3 | ''' - "filename.txt" # | 'abc123...'
        """
        try:
            with self.file_path.open("w", encoding='utf-8') as f:
                # Build the header line
                header = self._build_header_line()
                f.write(header + "\n")

                # Sort by filename (case-insensitive)
                sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())

                # Determine padding for the filename column
                max_length = 2 + max((len(filename) for _, filename, _, _ in sorted_entries), default=0)

                for file_hash, filename, date_str, depth_str in sorted_entries:
                    # Construct the date/depth portion in the line
                    line = self._build_data_line(file_hash, filename, date_str, depth_str, max_length)
                    f.write(line)

            logger.info(f"Hash file written: {self.file_path.name}")
        except IOError as error:
            logger.error(f"Failed to write hash file: {error}")

    def _build_header_line(self) -> str:
        """Return the appropriate header line based on include_time / include_depth."""
        if self.include_time and self.include_depth:
            # #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                    | FILEHASH
            return "#  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                          | FILEHASH"
        elif self.include_time and not self.include_depth:
            # #  YYYY.MM.DD  HH:MM | FILENAME                              | FILEHASH
            return "#  YYYY.MM.DD  HH:MM | FILENAME                          | FILEHASH"
        elif not self.include_time and self.include_depth:
            # #  YYYY.MM.DD  | DEPTH | FILENAME                             | FILEHASH
            return "#  YYYY.MM.DD  | DEPTH | FILENAME                          | FILEHASH"
        else:
            # #  YYYY.MM.DD  | FILENAME                                    | FILEHASH
            return "#  YYYY.MM.DD  | FILENAME                                | FILEHASH"

    def _build_data_line(self, file_hash: str, filename: str, date_str: str, depth_str: str, max_length: int) -> str:
        """Return a single data line with the right columns for date/depth/filename/hash."""
        # Always triple single-quotes for date portion
        # Possibly also "depth" plus the triple quotes for the next chunk
        # Then the double-quoted filename, single-quoted hash, etc.

        padded_filename = f"\"{filename}\"".ljust(max_length)

        # Examples:
        #   ''' 2025.03.07 | ''' - "filename.txt"          # | 'abc123...'
        #   ''' 2025.03.07 21:13 | 5 | ''' - "filename.txt" # | 'abc123...'

        if self.include_depth and depth_str:
            return f"''' {date_str} | {depth_str} | ''' - {padded_filename} # | '{file_hash}'\n"
        else:
            return f"''' {date_str} | ''' - {padded_filename} # | '{file_hash}'\n"

    def read(self) -> List[Tuple[str, str, str, str]]:
        """
        Reads lines in all possible formats, returning (hash, filename, date_str, depth_str).
        If a line lacks date/time or depth, we store those as empty strings.
        """
        hash_entries: List[Tuple[str, str, str, str]] = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            logger.error(f"Failed to read hash file: {error}")
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str, str]]:
        """
        We have up to four "states":
          1) date only
          2) date/time
          3) date + depth
          4) date/time + depth

        Then we fallback to the old format:
          - 'filename' # | "abc123..."

        We'll attempt multiple patterns in descending order of specificity.
        If none match, we log a warning and return None.
        """
        line = line.strip()
        if not line or line.startswith("#"):
            return None

        # Pattern 1: date/time + depth
        # Example: ''' 2025.03.07 21:13 | 5 | ''' - "filename.txt" # | 'abc123...'
        # Groups:
        #   (1) -> "2025.03.07 21:13"
        #   (2) -> "5"
        #   (3) -> "filename.txt"
        #   (4) -> "abc123..."
        pattern_dt_depth = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"  # date + time
            r"\s*\|\s*(\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(pattern_dt_depth, line)
        if match:
            date_str = match.group(1)
            depth_str = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            return (file_hash, filename, date_str, depth_str)

        # Pattern 2: date/time, no depth
        # ''' 2025.03.07 21:13 | ''' - "filename.txt" # | 'abc123...'
        pattern_dt = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"  # date + time
            r"\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(pattern_dt, line)
        if match:
            date_str = match.group(1)
            filename = match.group(2)
            file_hash = match.group(3)
            return (file_hash, filename, date_str, "")

        # Pattern 3: date + depth
        # ''' 2025.03.07 | 5 | ''' - "filename.txt" # | 'abc123...'
        pattern_date_depth = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"  # just date
            r"\s*\|\s*(\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(pattern_date_depth, line)
        if match:
            date_str = match.group(1)
            depth_str = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            return (file_hash, filename, date_str, depth_str)

        # Pattern 4: date only
        # ''' 2025.03.07 | ''' - "filename.txt" # | 'abc123...'
        pattern_date = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"  # just date
            r"\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(pattern_date, line)
        if match:
            date_str = match.group(1)
            filename = match.group(2)
            file_hash = match.group(3)
            return (file_hash, filename, date_str, "")

        # Old format fallback: - 'filename' # | "abc123..."
        # We'll interpret date_str/depth_str as empty
        old_format = r'^-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'
        match_old = re.match(old_format, line)
        if match_old:
            filename = match_old.group(1)
            file_hash = match_old.group(2)
            return (file_hash, filename, "", "")

        logger.warning(f"Invalid hash file entry: {line}")
        return None


# =======================================================
# File Renamer
# =======================================================
class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(
        self,
        source_hashes: List[Tuple[str, str, str, str]],
        target_hashes: List[Tuple[str, str, str, str]],
        dry_run: bool = True
    ) -> bool:
        """
        We only need the (hash, filename) pair for matching and renaming.
        The date_str / depth_str are unused for the rename logic.
        """
        src_map_entries = [(h, f) for (h, f, _, _) in source_hashes]
        tgt_map_entries = [(h, f) for (h, f, _, _) in target_hashes]

        source_map = self._map_hash_to_paths(src_map_entries)
        target_map = self._map_hash_to_paths(tgt_map_entries)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                logger.debug(f"Unchanged: {src_rel}")
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    # Already handled
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    logger.warning(f"No matching hash for: {src}")
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1.0
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            logger.debug(f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}")
        else:
            logger.warning(f"No suitable match found for '{source}'")
        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        # Normalize to ASCII, remove special chars
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")
            return False
        if tgt.exists() and tgt != src:
            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')
        except OSError as error:
            logger.error(f"Failed to rename {src_rel}: {error}")

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            logger.warning("No files require renaming")
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            console = Console()
            console.print("\n")
            console.print(Panel(table, border_style="blue"))
            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            logger.warning("No files require renaming")

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            logger.warning(f"{operation}: Conflicts detected, some files skipped")
        else:
            logger.info(f"{operation} completed successfully")


# =======================================================
# File Editor
# =======================================================
class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.warning(f"Unsupported platform: {sys.platform}")
        except Exception as error:
            logger.error(f"Failed to open editor: {error}")


# =======================================================
# Main App Class
# =======================================================
class BatchRenameApp:
    """
    Encapsulates the CLI workflow for batch renaming:
      1) Parse & prompt for Arguments
      2) Initialize Logging
      3) Collect & write .original_hashes.py & .new_hashes.py
      4) Edit & confirm renaming
      5) Cleanup
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args: Optional[argparse.Namespace] = None

    def run(self) -> None:
        # 1) Parse & prompt
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Initialize Logging
        LoggerSetup.initialize_logging(self.args.verbosity)

        # 3) Handle the main process
        success = False
        try:
            self.handle_process_command()
            success = True
        except Exception as e:
            logger.error(f"Execution failed: {e}")

        # 4) Cleanup logs if needed and success
        if success and self.args.cleanup_logs:
            logger.remove()  # remove Loguru handler to release file
            log_file = Path("app.log.yml")
            if log_file.exists():
                console = Console()
                try:
                    log_file.unlink()
                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")

    def handle_process_command(self) -> None:
        console = Console()
        root_dir = pathlib.Path(self.args.directory).resolve()

        org_file = root_dir / ".original_hashes.py"
        new_file = root_dir / ".new_hashes.py"

        try:
            # Pass include_time & include_depth to the FileProcessor
            processor = FileProcessor(
                root_dir,
                self.args.include_subdirs,
                include_time=self.args.include_time,
                include_depth=self.args.include_depth
            )
            initial_hashes = processor.collect_file_hashes()

            # Write to both “original” and “new” for editing
            for file_path in (org_file, new_file):
                manager = HashFileManager(
                    file_path,
                    include_time=self.args.include_time,
                    include_depth=self.args.include_depth
                )
                manager.write(initial_hashes)

            logger.info("Opening new hash file for editing...")
            FileEditor.open(new_file)

            if not Confirm.ask("\nProceed with renaming? [y/n]: ", default="y"):
                logger.warning("Operation cancelled by user")
                return

            # For reading, we don't strictly need 'include_time/include_depth' since all formats parse
            org_manager = HashFileManager(org_file)
            new_manager = HashFileManager(new_file)

            renamer = FileRenamer(root_dir)
            # Dry run first
            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):
                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

        finally:
            # Cleanup: Attempt to delete the hash files
            for file_path in (org_file, new_file):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Cleaned up: {file_path.name}")
                except OSError as error:
                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")


# =======================================================
# Execution entrypoint
# =======================================================
def main() -> None:
    """Main entry point of the utility."""
    app = BatchRenameApp()
    app.run()


if __name__ == "__main__":
    main()
```

---

## Using the New Depth Column

1. **Collect date + time + depth**:
   ```bash
   python RenameWithEditor.py \
       --directory /path/to/files \
       --include-time \
       --include-depth
   ```
   Produces lines like:
   ```
   #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                              | FILEHASH
   ''' 2025.03.07 21:13 | 3 | ''' - "subfolder/filename1.txt"         # | 'abc123...'
   ...
   ```
   Where `3` is the number of components (`"subfolder", "filename1.txt"`) in the relative path.

2. **Any Combination** of `--include-time` and `--include-depth`:  
   - With or without time,
   - With or without depth,  
   the script will generate a consistent hash file that can be read later.  

3. **Renaming**:  
   - Depth doesn’t affect the rename logic—it’s just displayed in the file for reference.  
   - The script still matches files by `(hash, filename)` only.

This gives you a **four-column** display if both are included, or reverts back to **three columns** (and possibly fewer) if not. All older lines are still parseable via the fallback patterns.

## Message 16

please integrate these additional parameters for dirs and patterns to exclude:

class Config:

    """

    Holds default settings for the Batch Rename Utility.

    """

    DEFAULT_CLEANUP_LOGS = True

    USE_DEFAULT_SETTINGS = True

    DEFAULT_INCLUDE_SUBDIRECTORIES = False

    DEFAULT_INCLUDE_TIMESTAMP = True

    DEFAULT_INCLUDE_DEPTH = True



    EXCLUDED_DIRS = [

        ".backups",

        ".git",

        ".github",

        ".venv",

        ".versions",

        "__pycache__",

        "__tmp__",

        "node_modules",

        "venv",

    ]



    EXCLUDED_PATTERNS = [

        "*.pyc", 

        "*.sublime-workspace", 

    ]

## Message 17

**Integrating exclude parameters**

I’m pulling together additional parameters for directories and patterns to exclude in the `FileProcessor.collect_file_hashes` method. This involves skipping directories listed in `EXCLUDED_DIRS` and files matching `EXCLUDED_PATTERNS`.

**Integrating exclusion parameters**

I’m figuring out how to incorporate `Config.EXCLUDED_DIRS` and `Config.EXCLUDED_PATTERNS` into the scanning process, using `os.walk` to skip directories and `fnmatch` for patterns.

## Message 18

Below is the **complete, updated** script that **excludes** certain directories and file patterns using the new config fields `EXCLUDED_DIRS` and `EXCLUDED_PATTERNS`. As before, it supports:

1. **Optional timestamp** (`--include-time`)
2. **Optional depth** (`--include-depth`)
3. **Excluded directories** (does not even recurse into them).
4. **Excluded file patterns** (skips files that match those patterns).

Everything else remains the same: prompts, hashing, logging, renaming. You can replace your current `RenameWithEditor.py` with the following code.

---

```python
""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """

import argparse
import fnmatch
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from dataclasses import dataclass
from enum import Enum

# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# Loguru for logging
from loguru import logger


# =======================================================
# Configuration
# =======================================================
class Config:
    """
    Holds default settings for the Batch Rename Utility.
    """
    DEFAULT_CLEANUP_LOGS = True
    USE_DEFAULT_SETTINGS = True
    DEFAULT_INCLUDE_SUBDIRECTORIES = False
    DEFAULT_INCLUDE_TIMESTAMP = True
    DEFAULT_INCLUDE_DEPTH = True

    EXCLUDED_DIRS = [
        ".backups",
        ".git",
        ".github",
        ".venv",
        ".versions",
        "__pycache__",
        "__tmp__",
        "node_modules",
        "venv",
    ]

    EXCLUDED_PATTERNS = [
        "*.pyc",
        "*.sublime-workspace",
    ]


# =======================================================
# Logger Setup
# =======================================================
class LoggerSetup:
    """Sets up Loguru logger to write YAML logs to a file."""

    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        """
        Configure Loguru to log messages in YAML format to `log_file`.
        """
        def yaml_sink(message):
            record = message.record

            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            func_name_str = f"*{record['function']}"
            line_no = record["line"]
            msg = record["message"]

            # For multi-line messages, use a block scalar in YAML
            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote if it has special characters like ':'
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {func_name_str}",
                f"  lineno: {line_no}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        # Remove default handlers and add our YAML sink
        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging(verbosity: str = "INFO"):
        """
        Initialize YAML logging with a level mapped from the string `verbosity`.
        """
        level_map = {
            "quiet": "ERROR",
            "normal": "INFO",
            "verbose": "DEBUG",
            "debug": "DEBUG"
        }
        selected_level = level_map.get(verbosity.lower(), "INFO")
        LoggerSetup.setup_yaml_logging(level=selected_level)


# =======================================================
# Argument Handler
# =======================================================
class ArgumentHandler:
    """Handles CLI argument parsing and optional prompting."""

    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(
            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."
        )

        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")

        parser.add_argument('--include_subdirs', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,
                            help="Include subdirectories in file processing")

        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")

        parser.add_argument('-v', '--verbosity',
                            choices=["quiet", "normal", "verbose", "debug"],
                            default="normal",
                            help="Set output verbosity level")

        parser.add_argument('--include-time', action='store_true',
                            default=Config.DEFAULT_INCLUDE_TIMESTAMP,
                            help="Include 'HH:MM' in the date column (e.g. YYYY.MM.DD HH:MM).")

        parser.add_argument('--include-depth', action='store_true',
                            default=Config.DEFAULT_INCLUDE_DEPTH,
                            help="Include a folder depth column (integer).")

        # Log Cleanup arguments
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',
                                        help="Clean up log files after successful execution")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',
                                        help="Do not clean up log files after successful execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        logger.debug("Prompting for missing arguments.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.USE_DEFAULT_SETTINGS)
            logger.debug(f"Use defaults: {use_defaults}")

            if not use_defaults:
                # Prompt for input directory
                print_section("Directory")
                current_dir = args.directory or ""
                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()

                # Prompt for including subdirectories
                print_section("Include Subdirectories?")
                args.include_subdirs = Confirm.ask("Include subdirectories?", default=True)

                # Prompt for logging verbosity
                print_section("Logging Verbosity")
                choices = ["quiet", "normal", "verbose", "debug"]
                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")
                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)
                args.verbosity = chosen_verbosity

                # Prompt for including timestamps
                print_section("Include Time?")
                args.include_time = Confirm.ask("Include HH:MM in the date column?", default=True)

                # Prompt for including depth
                print_section("Include Folder Depth?")
                args.include_depth = Confirm.ask("Include folder depth column?", default=True)

                # Prompt for cleanup logs
                print_section("Log Cleanup")
                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=args.cleanup_logs)
            else:
                # Assign defaults if none provided
                args.directory = args.directory or ""
                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES
                # Let the existing defaults stand for verbosity, cleanup_logs, etc.

        # Validation
        if not args.directory:
            console.print("[red]Error:[/] The following argument is required: directory")
            sys.exit(1)

        dir_path = Path(args.directory)
        if not dir_path.exists() or not dir_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
            sys.exit(1)

        logger.debug("Argument prompting complete.")
        return args


# =======================================================
# File Hasher
# =======================================================
class FileHasher:
    """Computes SHA256 hashes for files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None


# =======================================================
# File Processor
# =======================================================
class FileProcessor:
    """
    Processes files to collect their hashes, plus optional date/time + optional depth.
    Excludes any directories in Config.EXCLUDED_DIRS and any files matching Config.EXCLUDED_PATTERNS.
    """

    def __init__(
        self,
        root_dir: pathlib.Path,
        include_subdirs: bool,
        include_time: bool = False,
        include_depth: bool = False
    ):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs
        self.include_time = include_time
        self.include_depth = include_depth

        # Prepare sets/lists for exclusions
        self.excluded_dirs = set(Config.EXCLUDED_DIRS)
        self.excluded_patterns = Config.EXCLUDED_PATTERNS

    def collect_file_hashes(self) -> List[Tuple[str, str, str, str]]:
        """
        Collect a list of (file_hash, relative_filename, date_str, depth_str).

        - date_str: "YYYY.MM.DD" or "YYYY.MM.DD HH:MM"
        - depth_str: the integer folder depth as string, or "" if not used

        We skip directories in Config.EXCLUDED_DIRS and files matching any pattern in Config.EXCLUDED_PATTERNS.
        """
        hash_entries: List[Tuple[str, str, str, str]] = []
        # Use os.walk to gather files
        for root, dirs, files in os.walk(self.root_dir):
            # 1) remove excluded dirs from the walk
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]

            for filename in files:
                # 2) skip if matches any excluded pattern
                if any(fnmatch.fnmatch(filename, pat) for pat in self.excluded_patterns):
                    continue

                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path)
                if file_hash:
                    # Build date/time
                    mtime = file_path.stat().st_mtime
                    if self.include_time:
                        date_str = time.strftime("%Y.%m.%d %H:%M", time.localtime(mtime))
                    else:
                        date_str = time.strftime("%Y.%m.%d", time.localtime(mtime))

                    # Build depth
                    if self.include_depth:
                        depth_val = len(Path(relative_path).parts)
                        depth_str = str(depth_val)
                    else:
                        depth_str = ""

                    hash_entries.append((file_hash, relative_path, date_str, depth_str))
                    logger.debug(f"Processed: {relative_path}")

            if not self.include_subdirs:
                # If user doesn't want subdirectories, stop after top-level
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            logger.warning(f"`{path}` is not accessible or not a file")
            return False
        return True


# =======================================================
# Hash File Manager
# =======================================================
class HashFileManager:
    """
    Manages reading and writing hash files (with optional time + optional depth).
    If 'include_time' -> we add date/time to lines.
    If 'include_depth' -> we add a DEPTH column.
    """

    def __init__(self, file_path: pathlib.Path, include_time: bool = False, include_depth: bool = False):
        self.file_path = file_path
        self.include_time = include_time
        self.include_depth = include_depth

    def write(self, hash_entries: List[Tuple[str, str, str, str]]) -> None:
        """
        Writes lines in any of these formats for the header:
          #  YYYY.MM.DD  | FILENAME                       | FILEHASH
          #  YYYY.MM.DD  HH:MM | FILENAME                 | FILEHASH
          #  YYYY.MM.DD  | DEPTH | FILENAME               | FILEHASH
          #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME         | FILEHASH

        Each data line:
          ''' 2025.03.07 | ''' - "filename.txt" # | 'abc123...'
          ''' 2025.03.07 21:13 | 3 | ''' - "filename.txt" # | 'abc123...'
        """
        try:
            with self.file_path.open("w", encoding='utf-8') as f:
                # Build the header line
                header = self._build_header_line()
                f.write(header + "\n")

                # Sort by filename (case-insensitive)
                sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())

                # Determine padding for the filename column
                max_length = 2 + max((len(filename) for _, filename, _, _ in sorted_entries), default=0)

                for file_hash, filename, date_str, depth_str in sorted_entries:
                    # Construct the date/depth portion in the line
                    line = self._build_data_line(file_hash, filename, date_str, depth_str, max_length)
                    f.write(line)

            logger.info(f"Hash file written: {self.file_path.name}")
        except IOError as error:
            logger.error(f"Failed to write hash file: {error}")

    def _build_header_line(self) -> str:
        """Return the appropriate header line based on include_time / include_depth."""
        if self.include_time and self.include_depth:
            # #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                   | FILEHASH
            return "#  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                        | FILEHASH"
        elif self.include_time and not self.include_depth:
            # #  YYYY.MM.DD  HH:MM | FILENAME                             | FILEHASH
            return "#  YYYY.MM.DD  HH:MM | FILENAME                        | FILEHASH"
        elif not self.include_time and self.include_depth:
            # #  YYYY.MM.DD  | DEPTH | FILENAME                           | FILEHASH
            return "#  YYYY.MM.DD  | DEPTH | FILENAME                        | FILEHASH"
        else:
            # #  YYYY.MM.DD  | FILENAME                                  | FILEHASH
            return "#  YYYY.MM.DD  | FILENAME                              | FILEHASH"

    def _build_data_line(self, file_hash: str, filename: str, date_str: str, depth_str: str, max_length: int) -> str:
        """Return a single data line with the right columns for date/depth/filename/hash."""
        # Always triple single-quotes for date portion
        # Possibly also "depth" plus the triple quotes for the next chunk
        # Then the double-quoted filename, single-quoted hash, etc.

        padded_filename = f"\"{filename}\"".ljust(max_length)

        if self.include_depth and depth_str:
            return f"''' {date_str} | {depth_str} | ''' - {padded_filename} # | '{file_hash}'\n"
        else:
            return f"''' {date_str} | ''' - {padded_filename} # | '{file_hash}'\n"

    def read(self) -> List[Tuple[str, str, str, str]]:
        """
        Reads lines in all possible formats, returning (hash, filename, date_str, depth_str).
        If a line lacks date/time or depth, we store those as empty strings.
        """
        hash_entries: List[Tuple[str, str, str, str]] = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            logger.error(f"Failed to read hash file: {error}")
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str, str]]:
        """
        We have up to four states:
          1) date/time + depth
          2) date/time only
          3) date + depth
          4) date only
        Then we fallback to the old format (- 'filename' # | "hash").

        Returns (hash, filename, date_str, depth_str).
        """
        line = line.strip()
        if not line or line.startswith("#"):
            return None

        # 1) date/time + depth
        dt_depth_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"
            r"\s*\|\s*(\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(dt_depth_pat, line)
        if match:
            date_str = match.group(1)
            depth_str = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            return (file_hash, filename, date_str, depth_str)

        # 2) date/time only
        dt_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"
            r"\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(dt_pat, line)
        if match:
            date_str = match.group(1)
            filename = match.group(2)
            file_hash = match.group(3)
            return (file_hash, filename, date_str, "")

        # 3) date + depth
        date_depth_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"
            r"\s*\|\s*(\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(date_depth_pat, line)
        if match:
            date_str = match.group(1)
            depth_str = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            return (file_hash, filename, date_str, depth_str)

        # 4) date only
        date_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"
            r"\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(date_pat, line)
        if match:
            date_str = match.group(1)
            filename = match.group(2)
            file_hash = match.group(3)
            return (file_hash, filename, date_str, "")

        # Old format fallback
        old_pat = r'^-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'
        match_old = re.match(old_pat, line)
        if match_old:
            filename = match_old.group(1)
            file_hash = match_old.group(2)
            return (file_hash, filename, "", "")

        logger.warning(f"Invalid hash file entry: {line}")
        return None


# =======================================================
# File Renamer
# =======================================================
class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(
        self,
        source_hashes: List[Tuple[str, str, str, str]],
        target_hashes: List[Tuple[str, str, str, str]],
        dry_run: bool = True
    ) -> bool:
        """
        We only need the (hash, filename) pair for matching and renaming.
        The date_str / depth_str are unused for the rename logic.
        """
        src_map_entries = [(h, f) for (h, f, _, _) in source_hashes]
        tgt_map_entries = [(h, f) for (h, f, _, _) in target_hashes]

        source_map = self._map_hash_to_paths(src_map_entries)
        target_map = self._map_hash_to_paths(tgt_map_entries)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                logger.debug(f"Unchanged: {src_rel}")
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    # Already handled
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    logger.warning(f"No matching hash for: {src}")
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1.0
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            logger.debug(f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}")
        else:
            logger.warning(f"No suitable match found for '{source}'")
        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        # Normalize to ASCII, remove special chars
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")
            return False
        if tgt.exists() and tgt != src:
            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')
        except OSError as error:
            logger.error(f"Failed to rename {src_rel}: {error}")

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            logger.warning("No files require renaming")
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            console = Console()
            console.print("\n")
            console.print(Panel(table, border_style="blue"))
            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            logger.warning("No files require renaming")

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            logger.warning(f"{operation}: Conflicts detected, some files skipped")
        else:
            logger.info(f"{operation} completed successfully")


# =======================================================
# File Editor
# =======================================================
class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.warning(f"Unsupported platform: {sys.platform}")
        except Exception as error:
            logger.error(f"Failed to open editor: {error}")


# =======================================================
# Main App Class
# =======================================================
class BatchRenameApp:
    """
    Encapsulates the CLI workflow for batch renaming:
      1) Parse & prompt for Arguments
      2) Initialize Logging
      3) Collect & write .original_hashes.py & .new_hashes.py
      4) Edit & confirm renaming
      5) Cleanup
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args: Optional[argparse.Namespace] = None

    def run(self) -> None:
        # 1) Parse & prompt
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Initialize Logging
        LoggerSetup.initialize_logging(self.args.verbosity)

        # 3) Handle the main process
        success = False
        try:
            self.handle_process_command()
            success = True
        except Exception as e:
            logger.error(f"Execution failed: {e}")

        # 4) Cleanup logs if needed and success
        if success and self.args.cleanup_logs:
            logger.remove()  # remove Loguru handler to release file
            log_file = Path("app.log.yml")
            if log_file.exists():
                console = Console()
                try:
                    log_file.unlink()
                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")

    def handle_process_command(self) -> None:
        console = Console()
        root_dir = pathlib.Path(self.args.directory).resolve()

        org_file = root_dir / ".original_hashes.py"
        new_file = root_dir / ".new_hashes.py"

        try:
            processor = FileProcessor(
                root_dir,
                self.args.include_subdirs,
                include_time=self.args.include_time,
                include_depth=self.args.include_depth
            )
            initial_hashes = processor.collect_file_hashes()

            # Write to both “original” and “new” for editing
            for file_path in (org_file, new_file):
                manager = HashFileManager(
                    file_path,
                    include_time=self.args.include_time,
                    include_depth=self.args.include_depth
                )
                manager.write(initial_hashes)

            logger.info("Opening new hash file for editing...")
            FileEditor.open(new_file)

            if not Confirm.ask("\nProceed with renaming? [y/n]: ", default="y"):
                logger.warning("Operation cancelled by user")
                return

            org_manager = HashFileManager(org_file)
            new_manager = HashFileManager(new_file)

            renamer = FileRenamer(root_dir)
            # Dry run first
            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):
                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

        finally:
            # Cleanup: Attempt to delete the hash files
            for file_path in (org_file, new_file):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Cleaned up: {file_path.name}")
                except OSError as error:
                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")


# =======================================================
# Execution entrypoint
# =======================================================
def main() -> None:
    """Main entry point of the utility."""
    app = BatchRenameApp()
    app.run()


if __name__ == "__main__":
    main()
```

---

### How It Skips Excluded Directories and Files

1. **Excluded Directories**:  
   - We override the `dirs` list in `os.walk` to remove any directories that appear in `Config.EXCLUDED_DIRS`.  
   - This prevents Python from recursing into them altogether.

2. **Excluded File Patterns**:
   - We compare each filename with `fnmatch.fnmatch(filename, pattern)` for every pattern in `Config.EXCLUDED_PATTERNS`.  
   - If it matches, we skip hashing that file.

Everything else—timestamp, depth, hashing, file renaming—works just as before.
