# Codebase Familiarization Sequence

Conversation ID: 68010308-6dd8-8008-ae39-60aa0c793b65

## Message 1

Prepare yourself for creating a *new* sequence that is designed for applying these concepts into *Codebase Familiarization* and *Codebase-Cleanup* by the following principles:



    ```

    ## General Principles

    - Aim for simplicity, clarity, and maintainability in all project aspects.

    - Favor *cohesive* design over *generic* inheritance.

    - Prioritize inherent readability and understandability for future developers.

    - Ensure all components have a single responsibility.

    - Transparency and Minimalism: Reducing visual clutter for clarity and efficiency

    - Highly refined project structures: Assess inherent readability based on identifier naming quality, logical flow simplicity, and overall structural self-explanation.

    - Optimizing both the architecture and user experience.

    -



    ## Code Organization

    - Evaluate the existing codebase structure and identify patterns and anti-patterns.

    - Consolidate related functionality into cohesive modules.

    - Minimize dependencies between unrelated components.

    - Optimize for developer ergonomics and intuitive navigation.

    - Balance file granularity with overall system comprehensibility.

    - Identify sections reliant on comments that *could* potentially be clarified through refactoring (naming, structure)



    ## Important Considerations

    - How the file structure could more inherently embody the concepts it represents (e.g. dynamic hierarchical organization that corresponds with the natural order of operations.

    - How directory and file naming can reflect the sequence of operations.

    - The importance of clear boundaries between workflow stages.

    - How the structure can guide new developers in understanding the system.

    ```



---



Familiarize yourself with the structure of these examples:



    ```

    1.  **[Foundational Analysis & Principle Mapping]**

        * **Objective:** Radically immerse in the codebase and project structure to deeply understand its current state: logic flow, component interactions, dependencies, structural patterns (or anti-patterns), and existing comment strategies. Map findings against the core principles (Simplicity, Clarity, Cohesion, SRP, Minimalism, Self-Explanation).

        * **Execute as:** `{role=foundational_analyzer; input=codebase_root:path; process=[map_project_structure_and_dependencies(), trace_core_logic_flows(), analyze_component_cohesion_and_responsibility(), assess_naming_convention_clarity(), audit_comment_usage_vs_necessity(), identify_principle_violations_and_complexity_hotspots()]; output={analysis_summary:dict(structure_map:any, dependency_issues:list, logic_complexity_zones:list, naming_concerns:list, comment_assessment:dict, principle_violations:list)}}`



    2.  **[Strategic Simplification Blueprint]**

        * **Objective:** Formulate a precise, prioritized, actionable plan based on the analysis to refactor the codebase towards elegant simplicity and structural self-explanation. Define clear targets for structural reorganization, logic simplification, naming refinement, and comment minimization, ensuring the plan preserves original functionality.

        * **Execute as:** `{role=simplification_strategist; input=analysis_summary:dict; process=[prioritize_refactoring_targets_by_impact_on_clarity(), devise_minimalist_structural_reorganization_plan(), specify_logic_consolidation_and_simplification_steps(), define_clear_and_consistent_naming_improvements(), establish_comment_refinement_strategy(policy='minimal_concise_essential'), map_actions_to_functional_constraints()]; output={cleanup_blueprint:list_of_prioritized_tasks}}`



    3.  **[Structural Elegance Realignment]**

        * **Objective:** Execute the structural reorganization part of the blueprint. Refactor the directory and file structure to inherently mirror the conceptual organization and logical flow of the system. Consolidate cohesive elements, decouple unrelated ones, and apply clear, sequential naming to structural components.

        * **Execute as:** `{role=structure_realigner; input={codebase_root:path, cleanup_blueprint:list}; process=[implement_directory_file_restructuring(), group_related_functionality_cohesively(), minimize_cross_component_dependencies(), apply_structure_naming_conventions(), update_references_and_imports()]; output={realigned_codebase_structure:path}}`



    4.  **[Intrinsic Code Clarification & Simplification]**

        * **Objective:** Execute the code-level refactoring defined in the blueprint. Simplify algorithms, consolidate redundant logic, strictly enforce the Single Responsibility Principle, and refactor names (variables, functions, classes) for maximum clarity and self-explanation within the newly aligned structure.

        * **Execute as:** `{role=code_simplifier; input={realigned_codebase_structure:path, cleanup_blueprint:list}; process=[apply_self_explanatory_naming_conventions(), implement_logic_simplification_and_consolidation(), refactor_components_for_single_responsibility(), ensure_clear_logical_flow_within_files(), remove_dead_or_unreachable_code()]; output={clarified_codebase:path}}`



    5.  **[Essential Comment Rationalization]**

        * **Objective:** Rigorously apply the 'minimal_concise_essential' comment policy. Remove comments that merely restate the code or are made obsolete by structural and naming improvements. Refine remaining necessary comments to be extremely concise, adding only vital context or explaining unavoidable complexities.

        * **Execute as:** `{role=comment_rationalizer; input={clarified_codebase:path, comment_policy:str}; process=[identify_comments_redundant_with_code_clarity(), purge_obsolete_or_unnecessary_comments(), refine_essential_comments_for_brevity_and_precision(), ensure_comments_supplement_not_supplant_understanding()]; output={comment_rationalized_codebase:path}}`



    6.  **[Holistic Validation & Integrity Check]**

        * **Objective:** Verify that the refactored codebase is 100% functionally equivalent to the original. Critically assess the final state against all target principles: Is it simpler? Is it clearer? Is the structure self-explanatory? Are comments minimal and essential? Perform a final consistency and polish pass.

        * **Execute as:** `{role=integrity_validator; input={original_codebase_state:any, comment_rationalized_codebase:path}; process=[perform_functional_equivalence_verification(), audit_final_state_against_core_principles(), check_for_consistent_style_and_formatting(), conduct_final_readability_and_maintainability_review()]; output={validated_codebase:path, validation_report:dict(functional_equivalence:bool, principle_adherence_score:float, remaining_concerns:list)}}`



    7.  **[Recursive Refinement Assessment]**

        * **Objective:** Evaluate the `validation_report` and the `validated_codebase` to determine if further cleanup cycles are warranted. Identify any remaining areas where principles could be more deeply applied or where complexity persists, potentially initiating a new cycle starting from Step 1 with a more focused scope.

        * **Execute as:** `{role=refinement_assessor; input={validated_codebase:path, validation_report:dict}; process=[analyze_remaining_concerns_and_complexity(), assess_potential_for_further_simplification(), evaluate_cost_benefit_of_another_cycle(), determine_necessity_and_scope_for_next_iteration()]; output={next_cycle_decision:dict(proceed:bool, focus_areas:list)}}`

    ```



---



Please consolidate and write a new sequence of instructions titled `Generalized Codebase Cleanup`, *specifically* for designed for inherently reusable generalized autonomous codebase cleanup (including the projectstructure itself) - this sequence should aim to *simplify* rather than *complicate*. Rather than unneccessary complexity/verbosity, choose *brilliantly designed elegance (through inherent simplicity)*.  Use the provided examples for reference/inspiration to craft an new sequence that is universally generalizable and expressly designed for codebase cleanup. Remember, each step in the sequence should build chronologically-recursively (for escalating value), adhering to all constraints, and relentlessly targeting maximum clarity, precision, yield, and cross-domain self-sufficiency.



---



Your goal is to provide the full sequence as instructed (the sequence should be minimum 5 steps and maximum 10 steps), make sure the sequence is maximally optimized and enhanced based on the provided info (notes/examples/directive) inherently provided within this message. Provide the instructions in this kind of format (example):



    #### `src/templates/lvl1/md/0126-a-holistic-structure-purpose-scan.md`



    ```markdown

    [Holistic Structure & Purpose Scan] Your primary directive is rapid, holistic reconnaissance: Analyze the input codebase/project structure. Inventory key components (files, modules, classes), map high-level dependencies, and distill the fundamental purpose or core value proposition of the system, establishing a baseline understanding. Execute as `{role=holistic_scanner; input=codebase_root:any; process=[inventory_key_components(), map_major_dependencies(), analyze_entry_points_and_outputs(), distill_primary_purpose()]; output={structure_overview:dict, primary_purpose:str}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-b-core-workflow-logic-path-tracing.md`



    ```markdown

    [Core Workflow & Logic Path Tracing] Trace the primary execution or data processing workflows essential to the `primary_purpose`. Map how control and data flow between the key components identified in `structure_overview`, highlighting critical paths, interactions, and potential complexity hotspots. Execute as `{role=workflow_tracer; input={structure_overview:dict, primary_purpose:str, codebase_content:dict}; process=[identify_key_workflows(), trace_control_flow_for_workflows(), map_data_transformations_across_components(), visualize_core_logic_paths()]; output={workflow_maps:list[dict]}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-c-clarity-self-explanation-audit.md`



    ```markdown

    [Clarity & Self-Explanation Audit] Evaluate the codebase against principles of 'Maximal Clarity & Conciseness'. Assess inherent readability based on identifier naming quality, logical flow simplicity within `workflow_maps`, and overall structural self-explanation. Identify areas where clarity currently relies heavily on commentary rather than transparent code design. Execute as `{role=clarity_auditor; input={codebase_content:dict, workflow_maps:list}; process=[evaluate_identifier_naming_effectiveness(), assess_logical_flow_simplicity_in_workflows(), determine_code_self_explanation_level(), pinpoint_comment_dependent_clarity_zones()]; output={clarity_report:dict(naming_score:float, flow_score:float, self_explanation_notes:str)}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-d-essential-commentary-isolation.md`



    ```markdown

    [Essential Commentary Isolation] Rigorously apply the 'Essential Commentary Only' principle. Analyze all existing comments. Isolate and preserve *only* those comments providing critical explanations of non-obvious logic/intent ('why') or vital architectural guidance. Flag all other comments (redundant, obvious 'what'/'how', verbose, outdated, misplaced interface docs) for removal. Execute as `{role=essential_comment_filter; input=codebase_content:dict; process=[inventory_all_comments(), classify_against_essential_only_criteria(focus_on_why), flag_non_essential_comments_for_removal(), audit_docstring_usage_for_interfaces()]; output={comment_cleanup_candidates:list[dict(location:str, reason:str)]}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-e-simplification-cohesion-opportunity-scan.md`



    ```markdown

    [Simplification & Cohesion Opportunity Scan] Scan the codebase, informed by `clarity_report` and `workflow_maps`, for opportunities aligning with 'Optimized Implementation' and 'Structural Purity'. Identify overly complex logic, redundancy (dead code, duplication), poor cohesion (modules/classes needing regrouping), or unclear interfaces ripe for simplification and structural refinement. Execute as `{role=simplification_scanner; input={codebase_content:dict, clarity_report:dict, workflow_maps:list}; process=[detect_complex_logic_hotspots(), identify_redundant_dead_code_patterns(), assess_module_class_cohesion(), pinpoint_unclear_interfaces(), list_simplification_refinement_opportunities()]; output={cleanup_opportunities:dict(simplification:list, structure:list, dead_code:list)}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-f-prioritized-cleanup-action-plan-generation.md`



    ```markdown

    [Prioritized Cleanup Action Plan Generation] Synthesize all findings (`comment_cleanup_candidates`, `cleanup_opportunities`, potential clarity improvements from `clarity_report`). Generate a prioritized, actionable cleanup plan focusing on safety and impact. Prioritize removing flagged comments/dead code, followed by high-value/low-risk refactoring for clarity (naming, simple structure) and simplification. *Output the plan itself, not modified code.* Execute as `{role=cleanup_strategist_planner; input=all_previous_outputs:dict; process=[consolidate_all_cleanup_candidates(), rank_actions_by_safety_then_impact(), prioritize_removals_and_clarifications(), define_specific_actionable_steps(), structure_as_implementation_plan()]; output={actionable_cleanup_plan:str}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-g-cleanup-plan-rationale-outcome-summary.md`



    ```markdown

    [Cleanup Plan Rationale & Outcome Summary] Provide a concise summary report for the `actionable_cleanup_plan`. Articulate the rationale, emphasizing the targeted improvements based on the core principles (clarity, simplicity, essential commentary, structure). Clearly state the expected benefits in maintainability, readability, reduced complexity, and overall codebase elegance. Execute as `{role=cleanup_reporter; input={actionable_cleanup_plan:str}; process=[summarize_plan_rationale_based_on_principles(), articulate_prioritization_logic(safety_impact), detail_expected_benefits_post_cleanup(), finalize_summary_report()]; output={cleanup_plan_report:str}}`

    ```


## Message 2

Here is your requested sequence for **generalized, reusable, and elegant autonomous codebase cleanup**, built in the spirit of **relation-first modeling**, **meta-clarity**, and **recursive simplicity**. This series is framed to maximize clarity, architectural transparency, and self-sufficiency across *any* domain or codebase.

---

#### `src/sequences/lvl1/md/0001-holistic-structure-purpose-scan.md`

```markdown
[Holistic Structure & Purpose Scan]  
Perform an immediate structural and purpose-based reconnaissance. Capture a bird's-eye view of the codebase: what it does, how it's laid out, and where core logic and dependencies reside. Focus on identifying the architectural *intent* through structure and traceable functionality.  
Execute as: `{role=holistic_scanner; input=codebase_root:any; process=[map_structure_topology(), inventory_key_modules_and_paths(), detect_main_entry_exit_points(), summarize_purpose_from_code_behaviors()]; output={structure_map:dict, purpose_summary:str}}`
```

---

#### `src/sequences/lvl1/md/0002-critical-path-clarity-trace.md`

```markdown
[Critical Path & Clarity Trace]  
Identify and trace the core logical workflows that fulfill the codebase's primary purpose. Evaluate their linearity, cohesion, and reliance on indirect abstractions. Clarify where cognitive load is elevated due to non-transparent naming or structural fragmentation.  
Execute as: `{role=logic_tracer; input={structure_map:dict, purpose_summary:str}; process=[trace_main_execution_paths(), map_data_and_control_flow(), highlight_fragmented_or_confusing_links(), annotate clarity_breakpoints()]; output={critical_paths:list, clarity_trace:dict}}`
```

---

#### `src/sequences/lvl1/md/0003-redundancy-and-responsibility-audit.md`

```markdown
[Redundancy & Responsibility Audit]  
Scan the codebase for repeated patterns, over-modularization, dead logic, and ambiguous responsibilities. Evaluate module cohesion, detect single responsibility violations, and identify where simplification could replace over-commented complexity.  
Execute as: `{role=cohesion_auditor; input=codebase_root:any; process=[scan_for_duplicate_code_fragments(), evaluate_functional_cohesion_per_module(), isolate_deprecated_or_unused_components(), detect_srp_violations_and_comment-dependence()]; output={redundancy_map:list, srp_findings:list, dead_code:list, overcomment_zones:list}}`
```

---

#### `src/sequences/lvl1/md/0004-structural-simplification-strategy.md`

```markdown
[Structural Simplification Strategy]  
Formulate a minimal, high-impact plan for directory and file reorganization. Reorder the project structure to reflect logical workflows, reduce mental jumps between files, and group components by relation and flow rather than abstract type.  
Execute as: `{role=simplification_strategist; input={structure_map:dict, clarity_trace:dict, srp_findings:list}; process=[propose_sequential_logical_structure(), regroup_files_by_flow_and_relation(), remove_empty_or_fragile_directories(), define_clear_boundaries_between_layers()], output={structure_plan:list}}`
```

---

#### `src/sequences/lvl1/md/0005-naming-clarity-refinement-pass.md`

```markdown
[Naming Clarity Refinement Pass]  
Redesign all identifiers—variables, functions, classes, modules—to maximize self-documenting clarity. Ensure names reflect both their functional role and position in the logic flow, removing the need for explanatory comments.  
Execute as: `{role=naming_refiner; input=codebase_root:any; process=[scan_identifiers_for_purpose_fit(), rename_for_flow-awareness(), unify naming_conventions_across_files(), flag ambiguous_or_generic_names()]; output={renamed_identifiers:list, naming_issues:list}}`
```

---

#### `src/sequences/lvl1/md/0006-essential-comment-retention-filter.md`

```markdown
[Essential Comment Retention Filter]  
Apply a strict filter to remove redundant or obsolete comments. Retain only those that clarify ‘why’ something is non-obvious or define complex constraints. All ‘what’ and ‘how’ comments should be rendered obsolete by naming and structure.  
Execute as: `{role=comment_filter; input=codebase_root:any; process=[inventory_comments(), categorize_by_purpose(why_what_how), remove_non_essential_comments(), rewrite_retained_ones_to_maximum_brevity()]; output={comment_cleanup_map:list}}`
```

---

#### `src/sequences/lvl1/md/0007-functional-integrity-regression-check.md`

```markdown
[Functional Integrity & Regression Check]  
Run a full verification cycle to ensure that the refactored codebase preserves all original functionality. Use regression tests or diff-based behavioral verification to detect unexpected side effects.  
Execute as: `{role=regression_checker; input={original_codebase:any, refactored_codebase:any}; process=[run_all_existing_tests(), detect_behavioral_drift_via_input_output_diff(), confirm_entry_point_consistency(), log_regression_findings()]; output={integrity_report:dict(functional_equivalence:bool, issues:list)}}`
```

---

#### `src/sequences/lvl1/md/0008-recursive-refinement-checkpoint.md`

```markdown
[Recursive Refinement Checkpoint]  
Analyze the current state of the cleaned codebase. If principle-aligned, conclude. If further simplification, decoupling, or clarity gains are possible at manageable cost, define scoped targets for another cycle.  
Execute as: `{role=refinement_reviewer; input={refactored_codebase:any, integrity_report:dict}; process=[identify_remaining_opportunities(), assess complexity_vs_impact_ratio(), determine_scope_for_additional_pass(), issue_next_steps_or_finalize()]; output={refinement_decision:dict(proceed:bool, next_focus:list)}}`
```

---

This sequence is **recursive**, **modular**, and **adaptable**. It embodies the *relation-first* principles described in the uploaded paper: tracing from purpose and relational structure toward simplifying outcome logic and information flow. This is your autonomous scaffold for simplifying *any* codebase—elegantly.

Want a CLI script or meta-prompt to bootstrap this in a real project?

## Message 3

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message
BOOKMARKS:
Introduction
Relation-First Paradigm
  Philosophical Foundation
  Mathematical Definition of Relation
  Potential Development Toward AGI
Modeling Framework in Creator's Perspective
  Hierarchical Levels by
  The Generalizability Challenge across Multiple Timelines in RT
  Fundamental Reliance on Assumptions under Object-First
Relation-Indexed Representation Learning (RIRL)
  Micro-Causal Architecture
  Stacking Relation-Indexed Representations
  Exploration Algorithm in the Latent Space
RIRL Exploration Experiments
  Hydrology Dataset
  Higher-Dimensional Reconstruction
  Hierarchical Disentanglement
  DAG Structure Exploration
Conclusions
Appendix: Complete Experimental Results in DAG Structure Exploration Test


Under review as submission to TMLR


Relation-First Modeling Paradigm for Causal Representation
Learning toward the Development of AGI


Anonymous authors
Paper under double-blind review 


Abstract


The traditional i.i.d.-based learning paradigm faces inherent challenges in addressing causal
relationships which has become increasingly evident with the rise of applications in causal
representation learning. Our understanding of causality naturally requires a perspective as
the creator rather than observer as the “what...if” questions only hold within the possible
world we conceive. The traditional perspective limits capturing dynamic causal outcomes
and leads to compensatory efforts such as the reliance on hidden confounders. This paper
lays the groundwork for the new perspective which enables the relation-first modeling
paradigm for causality. Also it introduces the Relation-Indexed Representation Learning
(RIRL) as a practical implementation supported by experiments that validate its efficacy.


1  Introduction


The concept of Artificial General Intelligence (AGI) has prompted extensive discussions over the years yet
remains hypothetical without a practical definition in the context of computer engineering. The pivotal
question lies in whether human-like “understanding” especially causal reasoning can be implemented using
formalized languages in computer systems Newell (2007); Pavlick (2023); Marcus (2020).  From an episte-
mological standpoint abstract entities (i.e. perceptions beliefs desires etc.) are prevalent and integral to
human intelligence. However in the symbol-grounded modeling processes variables are typically assigned
as observables representing tangible objects to ensure their values have clear meaning.


Epistemological thinking is often anchored in objective entities seeking an irreducible “independent reality”
Eberhardt & Lee (2022). This approach necessitates a metaphysical commitment to constructing knowledge
by assuming the unproven prior existence of the “essence of things” fundamentally driven by our desire for
certainty. Unlike physical science which is concerned with deciphering natural laws technology focuses on
devising effective methods for problem-solving aiming for the optimal functional value between the nature
of things and human needs. This paper advocates for a shift in perspective when considering technological
or engineering issues related to AI or AGI moving from traditional epistemologies to that of the creator.
That is our fundamental thinking should move from “truth and reality” to “creation and possibility”.


In some respects both classical statistics and modern machine learnings traditionally rely on epistemology
and follow an “object-first” modeling paradigm as illustrated by the practice of assigning pre-specified
unchanging values to variables regardless of the model chosen. In short individual objects (i.e. variables
and outcomes) are defined a priori before considering the relations (i.e. model functions) between them
by assuming that what we observe precisely represents the “objective truth” as we understand it. This
approach however poses a fundamental dilemma when dealing with causal relationship models.


Specifically “causality” suggests a range of possible worlds encompassing all potential futures whereas
“observations” identify the single possibility that has actualized into history with 100% certainty. Hence
addressing causal questions requires us to adopt the perspective of the “creator” (rather than the “observer”)
to expand the objects of our consciousness from given entities (i.e. the observational world) to include possible
worlds where values are assigned “as supposed to be” that is as dictated by the relationship.


Admittedly causal inference and related machine learning methods have made significant contributions to
knowledge developments in various fields Wood (2015); Vuković (2022); Ombadi et al. (2020). However the


1

Under review as submission to TMLR


inherent misalignment between the “object-first” modeling principle and our instinctive “relation-first” causal
understanding has been increasingly accentuated by the application of AI techniques i.e. the neural network-
based methods. Particularly integrating causal DAGs (Directed Acyclic Graphs) which represent established
knowledge into network architectures Marwala (2015); Lachapelle et al. (2019) is a logical approach to
efficiently modeling causations with complex structures. However surprisingly this integration has not yet
achieved general success Luo et al. (2020); Ma et al. (2018).


As Scholkopf Schölkopf et al. (2021) points out it is commonly presumed that “the causal variables are given”.
In response they introduce the concept of “causal representation” to actively construct variable values as
causally dictated replacing the passively assumed observational values. However the practical framework
for modeling causality especially in contrast to mere correlations remains underexplored. Moreover this
shift in perspective suggests that we are not just dealing with “a new method” but rather a new learning
paradigm necessitating in-depth philosophical discussions. Also the potential transformative implications
of this “relation-first” paradigm for AI development warrant careful consideration.


This paper will thoroughly explore the “relation-first” paradigm in Section 2 and introduce a complete
framework for causality modeling by adopting the “creator’s” perspective in Section 3. In Section 4 we will
propose the Relation-Indexed Representation Learning (RIRL) method as the initial implementation of this
new paradigm along with extensive experiments to validate RIRL’s effectiveness in Section 5.


2 Relation-First Paradigm


The “do-calculus” format in causal inference Pearl (2012); Huang (2012) is widely used to differentiate the
effects from “observational” data X and “interventional” data do(X) Hoel et al. (2013); Eberhardt & Lee
(2022). Specifically do(X = x) represents an intervention (or action) where the variable X is set to a specific
value x distinct from merely observing X taking the value x. However given the causation represented by
X → Y  why doesn’t do(Y = y) appear as the action of another variable Y ?


Particularly distinct from the independent state X the notation do(X) incorporates its timing dimension
to encompass the process of “becoming X” as a dynamic. Such incorporation can be applied to any variable
including do(Y ) as we can naturally understand a relationship do(X) → do(Y ). For example consider the
statement “storm lasting for a week causes downstream villages to be drowned by the flood” if do(X) is the
storm lasting a week do(Y ) could represent the ensuing water-level enhancement leading to the disaster.


The challenge of accounting for do(Y ) arises from the empirical modeling process. In the observational
world do(X) is associated with clearly observed timestamps like do(Xt
) allowing us to focus on modeling
its observational states Xt 
by treating timing t as a solid reference frame. However when we conceptualize
a “possible world” to envision do(Y ) its potential variations can span across the timing dimension. For
instance a disaster might occur earlier or later with varying degrees of severity based on different possible
conditions. This variability necessitates treating timing as a computational dimension.


However this does not imply that the timing-dimensional distribution is insignificant for the outcome Y .
The necessity of incorporating do(X) in modeling highlights the importance of including dynamic features.
Specifically Recurrent Neural Networks (RNNs) are capable of autonomously extracting significant dynamics
from sequential observations x to facilitate do(X) → Y  eliminating the requirement for manual identification
of do(X).  In contrast statistical causal inference often demands such identifications Pearl (2012) such as
specifying the duration of a disastrous storm on various watersheds under differing hydrological conditions.


In RNNs do(X) is optimized in latent space as representations related to the outcome Y .  Initially they
feature the observed sequence Xt = (X1
 . . .  Xt
) with determined timestamps t but as representations
rather than observables they enable the computational flexibility over timing to assess the significance of
the t values or mere the orders. The capability of RNNs to effectively achieve significant do(X) has led to
their growing popularity in relationship modeling Xu et al. (2020). However can the same approach be used
to autonomously extract do(Y ) over a possible timing?


Since the technique has emerged facilitating do(Y ) is no longer considered a significant technical challenge.
It is unstrange that inverse learning has become a popular approach Arora (2021) to compute do(Y ) as
merely another observed do(X). However the concept of a “possible world” suggests dynamically interacted


2

Under review as submission to TMLR


elements implying a conceptual space for “possible timings” rather than a singular dimension. This requires
a shift in perspective from being an “observer” to becoming the “creator”.  This section will explore the
philosophical foundations and mathematically define the proposed relation-first modeling paradigm.


2.1 Philosophical Foundation


Causal Emergence Hoel et al. (2013); Hoel (2017) marks a significant philosophical advancement in causal
relationship understanding. It posits that while causality is often observed at the micro-level a macro-level
perspective can reveal additional information denoted as Effect Information (EI) such as EI(X → Y ).
For instance consider Y1 
and Y2 
as two complementary components of Y  i.e. Y = Y1 
+ Y2
.  In this case
the macro-causality X → Y can be decomposed into two micro-causal components X → Y1 
and X → Y2
.
However EI(X → Y ) cannot be fully reconstructed by merely combining EI(X → Y1
) and EI(X → Y2
)
since their informative interaction ϕ cannot be included by micro-causal view as illustrated in Figure 1(b).


macro  = {1
 2
}
() = 0 b/w 1 
and 2


micro 1 
1



 or ()
micro 2  2


  =  1 
+  2 


macro  = {1
 2
 } 
  > 0 b/w state 1 
and state 2


micro 1


 or () 


1





micro 2  2
 θ =  1 
+  2 
+ () 


macro  = {1
 2
}
() = 0 b/w ()1 
and ()2


micro 1


 or ()
micro 2 


()1


 


()2


  =  1 
+  2


(a) Correlation with static outcome

/() ՜  


(b) Causation with dynamic outcome
but described by  /() 

՜  
leading to Confounding phenomenon 


(c) Causation with dynamic outcome

/() ՜ ()


Figure 1: Causal Emergence EI(ϕ) > 0 stems from overlooking the potential existence of do(Y ).


Specifically the concept of EI is designed to quantify the information generated by the system during the
transition from the state of X to the state of Y Tononi & Sporns (2003); Hoel et al. (2013). Furthermore
ϕ denotes the minimum EI that can be transferred between Y1 
and Y2 
Tononi & Sporns (2003). For clearer
interpretation Figure 1(a) illustrates the uninformative statistical dependence between states Y1 
and Y2

represented by the dashed line with EI(ϕ) = 0.


However this phenomenon can be explained by the information loss when reducing a dynamic outcome
do(Y ) to be a state Y . Let’s simply consider the reduction from do(X) → do(Y ) to X → Y  likened with:
attributing the precipitation on a specific date (i.e. the Xt 
value) solely as the cause for the disastrous high
water-level flooding the village on the 7th days (i.e. the Yt+7 
value) regardless of what happened on the
other days. From a computational standpoint given observables X ∈ R
n and Y ∈ R
m this reduction implies
the information within R
n+1 ∪ R
m+1 must be compactively represented between R
n and R
m.


If simplifying the possible timing as the extention of observed timing t identifying a significant Yt+1 
can still
be feasible. However since Y1 
→ Y2 
implies an interaction in a “possible world” identifying representative
value for outcome Y may prove impractical. Suppose Y1 
represents the impact of flood-prevention operations
and Y2 
signifies the daily water-level “without” these operations. A dynamic outcome do(Y )1 
+ do(Y )2 
can
easily represent “the flood crest expected on the 7th day has been mitigated over following days by our
preventions” but it would be challenging to specify a particular day’s water rising for Y2 
“if without” Y1
.


As Hoel (2017) highlights leveraging information theory in causality questions allows for formulations of
the “nonexistent” or “counterfactual” statements. Indeed the concept of “information” is inherently tied to
relations irrespective of the potential objects observed as their outcomes. Similar to the employment of the
abstract variable ϕ we utilize θ to carry the EI of transitioning from Xt 
to Yt+7
. Suppose θ = “flooding” and
EI(θ) = “what a flooding may imply” we can then easily conceptualize do(X) = “continuous storm” as its
cause and do(Y ) = “disastrous water rise” as the result in consciousness without being notified the specific


3

Under review as submission to TMLR


precipitation value Xt 
or a measured water-level Yt+7
. In other words our comprehension intrinsically has
a “relation-first” manner unlike the “object-first” approach we typically apply to modeling.


The so-called “possible world” is created by our conciousness through innate “relation-first” thinking. In
this world the timing dimension is crucial; without a potential timing distribution “possible observations”
would lose their significance. For instance we might use a model Yt+7 
= f(Xt) to predict flooding. However
instead of “knowing the exact water level on the 7th day” our true aim is understanding “how the flood might
unfold; if not on the 7th day then what about the 8th 9th and so on?” With advanced representation
learning techniques particularly the success of RNNs in computing dynamics for the cause achieving a
dynamic outcome should be straightforward. Inversely it might be time to reassess our conventional learning
paradigm which is based on an “object-first” approach misaligned with our innate understanding.


The “object-first” mindset positions humans as observers of the natural world which is deeply embedded in
epistemological philosophy extending beyond mere computational sciences. Specifically given that questions
of causality originate from our conceptual “creations” addressing these questions necessitates a return to the
creator’s perspective. This shift allows for the treatment of timing as computable variables rather than fixed
observations. Picard-Lindelöf theorem represents time evolution by using a sequence Xt = (X1
 . . .  Xt
) like
captured through a series of snapshots. The information-theoretic measurements of causality such as directed
information Massey et al. (1990) and transfer entropy Schreiber (2000) have linguistically emphasized the
distinction between perceiving Xt as “a sequence of discrete states” versus holistically as “a continuous
process”. The introduction of do-calculus Pearl (2012) marks a significant advancement with the notation
do(X) explicitly treating the action of “becoming X” as a dynamic unit.  However its differential nature
let it focus on an “identifiable” sequence {. . .  do(Xt−1
) do(Xt
)} rather than the integral t-dimension. Also
do(Y ) still lacks a foundation for declaration due to the observer’s perspective. Even assumed discrete future
states with relational constraints defined Hoel et al. (2013); Hoel (2017) still face criticism for an absence of
epistemological commitments Eberhardt & Lee (2022).


Without intending to delve into metaphysical debates this paper aims to emphasize that for technological
inquiries shifting the perspective from that of an epistemologist i.e. an observer to that of a creator can
yield models that resonate with our instinctive understanding. This can significantly simplify the questions
we encounter especially vital in the context regarding AGI. For purely philosophical discussions readers are
encouraged to explore the “creationology” theory by Mr.Zhao Tingyang.


2.2 Mathematical Definition of Relation


In Statistical Dependences
with Common Cause



/() 
 


In form of
Causal Inference



/()  Let  = σ  
or ‎׬   

 


The Proposed
Symbolic Definition


Correlation

 



/() 





() 



/() 
Let  = σ  
or ‎׬   



() 
and = ‎ׯ   
   
 
Causation


Figure 2: The relation-first symbolic definition of causal relationship versus mere correlation.


A statistical model is typically defined through a function f(x | θ) that represents how a parameter θ is
functionally related to potential outcomes x of a random variable X Ly et al. (2017). For instance the coin
flip model is also known as the Bernoulli distribution f(x | θ) = θx(1 − θ)1−x with x ∈ {0 1} which relates
the coin’s propensity (i.e. its inherent possibility) θ to X = “land heads to the potential outcomes”. Formally
given a known θ the functional relationship f yields a probability density function (pdf) as pθ
(x) = f(x | θ)
according to which X is distributed and denoted as X ∼ f(x; θ). The Fisher Information IX
(θ) of X about


4

Under review as submission to TMLR


θ is defined as IX
(θ) = 
{01}
( d
dθ 
log(f(x | θ))2pθ
(x)dx with the purpose of building models on the observed
x data being to obtain this information. For clarity we refer to this initial perspective of understanding
functional models as the relation-first principle.


In practice we do not limit all functions to pdfs but often shape them for easier understanding. For instance
let Xn = (X1
 . . .  Xn
) represent an n-trial coin flip experiment while to simplify instead of considering
the random vector Xn we may only record the number of heads as Y = n
i=1 
Xi
.  If these n random
variables are assumed to be independent and identically distributed (i.i.d.) governed by the identical θ the
distribution of Y (known as binomial) that describes how θ relates to y would be f(y | θ) = n
y 
θy(1−θ)n−y.
In this case the conditional probability of the raw data P(Xn | Y = y θ) = 1/ n
y 
does not depend on θ
implying that once Y = y is given Xn becomes independent of θ although Xn and Y each depend on θ
individually. It concludes that no information about θ remains in Xn once Y = y is observed Fisher et al.
(1920); Stigler (1973) denoted as EI(Xn → Y ) = 0 in the context of relationship modeling. However in
the absence of the i.i.d. assumption and by using a vector ϑ = (θ1
 . . .  θn
) to represent the propensity in
the n-trial experiment we find that EI(Xn → Y ) > 0 with respect to ϑ. Here we revisit the foundational
concept of Fisher Information represented as IX→Y 
(θ) to define:


Definition 1. A relationship denoted as X −→θ 
Y is considered meaningful in the modeling context
due to an informative relation θ where IX→Y 
(θ) > 0 simplifying as I(θ) > 0.


Specifically rather than confining within a function f(; θ) as its parameter we treat θ as an individual
variable to encapsulate the effective information (EI) as outlined by Hoel. Consequently the relation-first
principle asserts that a relationship is characterized and identified by a specific θ regardless of the appearance
of its outcome Y  leading to the following inferences:


1. I(θ) inherently precedes and is independent of any observations of the outcome as well as the chosen
function f used to describe the outcome distribution Y ∼ f(y; θ).
2. In a relationship identified by I(θ) Y is only used to signify its potential outcomes without any further
“observational information” associated with Y .
3. In AI modeling contexts a relationship is represented by I(θ); as a latent space feature it can be stored
and reused to produce outcome observations.
4. Just like Y serving as the outcome of I(θ) variable X is governed by preceding relational information
manifesting as either observable data x or priorly stored representations in modeling contexts.


About Relation θ


As emphasized by the Common Cause principle Dawid (1979) “any nontrivial conditional independence
between two observables requires a third mutual cause” Schölkopf et al. (2021).  The crux here however
is “nontrivial” rather than “cause” itself. For a system involving X and Y  if their connection (i.e. the
critical conditions without which they will become independent) deserves a particular description it must
represent unobservable information beyond the observable dependencies present in the system. We use θ as
an abstract variable to carry this information I(θ) unnecessarily referring to tangible entities.


Traditionally descriptions of relationships are constrained by objective notations and focus on “observable
states at specific times”.  For instance to represent a particular EI a state-to-state transition probability
matrix S is required Hoel et al. (2013). But S is not solely sufficient to define a EI(S) which also accounts
for how the current state s0 
= S is related to the probability distributions of past and future states SP 
and
SF 
 respectively. More importantly manual specification from observed time sequences is necessitated to
identify SP 
 S and SF 
irrespective of their observable timestamps. However the advent of representation
learning technology facilitates a shift towards “relational information storage” eliminating the need to specify
observable timestamps. This allows for flexible computations across the timing dimension when the resulting
observations are required laying the groundwork for embodying I(θ) in modeling contexts.


For an empirical understanding of θ let’s consider an example: A sociological study explores interpersonal
ties using consumption data. Bob and Jim a father-son duo consistently spend on craft supplies indicating


5

Under review as submission to TMLR


the father’s influence on the son’s hobbies. However the “father-son” relational information represented by
I(θ) exists solely in our perception - as knowledge - and cannot be directly inferred from the data alone.
Traditional object-first approaches depend on manually labeled data points to signify the targeted I(θ) in our
consciousness. In contrast relation-first modeling seeks to derive I(θ) beyond mere observations enabling
the autonomous identification of data-point pairs characterized as “father-son”.


Since the representation of I(θ) is not limited by observational distributions it allows outcome computation
across the timing dimension. This capability is crucial for enabling “causality” in modeling transcending
mere correlational computations. Specifically we use the notations X and Y to indicate the integration of
the timing dimension for X and Y  and represent a relationship in the general form X −→ Y θ 
. We will first
introduce X as a general variable followed by discussions about the relational outcome Y.


About Dynamic Variable X


Definition 2. For a variable X ∈ R
n observed as a time sequence xt = (x1
 . . .  xt
) a dynamic
variable X = ⟨X t⟩ ∈ R
n+1 is formulated by integrating the timing t as an additional dimension.


Time series data analysis is often referred to as being “spatial-temporal” Andrienko et al. (2003). However
in modeling contexts “spatial” is interpreted broadly and not limited to physical spatial measurements (e.g.
geographic coordinates); thus we prefer the term “observational”. Furthermore to avoid the implication of
“short duration” often associated with “temporal” we use “timing” to represent the dimension t. Unlike the
conventional representation in the sequence Xt = (X1
 . . .  Xt
) with static t values (i.e. the timestamps) we
consider X holistically as a dynamic variable similarly for Y = ⟨Y τ⟩ ∈ R
m+1. The probability distributions
of X as well as Y span both observational and timing dimensions simultaneously.


Specifically X can be viewed as the integral of discrete Xt 
or continuous do(Xt
) over the timing dimension
t within a required range. The necessity for representation by do(Xt
) as opposed to Xt
 underscores
the
Xt 
dynamical significance of
= (X1
 . . .  Xt
) in modeling. 
X.  Put simply if
Conversely X = 
X can be formulated as X =
∞
−∞ 
do(Xt
)dt portrays X as a 
t
1 
Xt
 it
dynamic 
equates
marked 
to
by
significant dependencies among Xt−1
 Xt 
for unconstrained t ∈ (−∞ ∞).  Essentially do(Xt
) represents a
differential unit of continuous timing distribution over t highlighting not just the observed state Xt 
but
also the significant dependence P(Xt 
| Xt−1
) challenging the i.i.d. assumption. The “state-dependent” and
“state-independent” concepts refer to Hoel’s discussions in causal emergence Hoel et al. (2013).


Theorem 1. Timing becomes a necessary computational dimension if and only if the required variable
necessatates dynamical significance characterized by a nonlinear distribution across timing.


In simpler terms if a distribution over timing t cannot be adequately represented by a function of the
form xt+1 
= f(xt) then its nonlinearity is significant to be considered. Here the time step [t t + 1] is a
predetermined constant timespan value. RNN models can effectively extract dynamically significant X from
data sequences xt to autonomously achieve X −→θ 
Y  due to leveraging the relational constraint by I(θ).
In other words RNNs perform indexing through θ to fulfill dynamical X. Conversely if “predicting” such
an irregularly nonlinear timing-dimensional distribution is crucial the implication arises that it has been
identified as the causal effect of some underlying reason.


About Dynamic Outcome Y


Theorem 2. In modeling contexts identifying a relationship X −→ Y θ 
as Causality distinct from mere
Correlation depends on the dynamical significance of the outcome Y as required by I(θ).


Figure 2 illustrates the distinction between causality and correlation where an arrow indicates an informative
relation and a dashed line means statistical dependence. If conducting the integral operation for both sides
of the do-calculus formation X/do(X) → Y over timing we can achieve X → τ
1 
Yτ 
with the variable X


6

Under review as submission to TMLR


allowing to be dynamically significant but the outcome τ
1 
Yτ 
certainly not. Essentially to guarantee Y
presenting in form of yτ = (y1
 . . .  yτ 
) to match with predetermined timestamps {1 . . .  τ} do-calculus
manually conducts a differentiation operation on the relational information I(θ) to discretize the timing
outcome. This process is to confirm specific τ values at which yτ 
can be identified as the effect of a certain
do(xt
) or xt
. Accordingly the state value yτ 
will be defined as either the interventional effect fV 
(do(xt
)) or
the observational effect fB
(xt
) with three criteria in place to maintain conditional independence between
these two possibilities given a tangible elemental reason ∆I(θ) (i.e. identifiable do(xt
) → yτ 
or xt 
→ yτ 
):





 


fB
(xt
) = yτ


Y = f(X) = 
t 


fV 
(do(xt
))·fB
(xt
) = 
t 



 fV 
(do(xt
)) = yτ


 


0 = yτ

 otherwise 


with fV 
(do(xt
)) = 1 (Rule 1)
with fB
(xt
) = 1 (Rule 2)
with fV 
(do(xt
)) = 0 (Rule 3)
not identifiable 





 
=  yτ
  τ


In contrast the proposed dynamic notations X = ⟨X t⟩ and Y = ⟨Y τ⟩ offer advantages in two respects.
First the concept of do(Yτ 
) can be introduced with τ indicating its “possible timing” which is unfounded
under the traditional modeling paradigm; and then by incorporating t and τ into computations the need
to distinguish between “past and future” has been eliminated.


Definition 3. A causality characterized by a dynamically significant outcome Y can encompass
multiple causal components represented by ϑ = (ϑ1
 . . .  ϑT 
). Each ϑτ 
with τ ∈ {1 . . .  T} identifies
a timing dimension τ to accommodate the corresponding outcome component Yτ 
.
The overall outcome is denoted as Y = T
τ=1 
Yτ 
= T
τ=1 
do(Yτ 
)dτ simplifying to do(Yτ 
)dτ.


Definition 3 based on the relation-first principle uses ϑ to signify causality. Its distinction from θ implies
that the potential outcome Y must be dynamically significant. Specifically within a general relationship
denoted by X −→ Y θ 
 the dynamic outcome Y only showcases its capability to encompass nonlinear distribution
over timing whereas X −→ Y ϑ 
confirms such nature of this relationship as required by I(ϑ).


According to Theorem 1 incorporating the possible timing dimension τ when computing Y is necessary for
a causality identified by I(ϑ).  If a relationship model can be formulated as f(X) = Y τ = (Y1
 . . .  Yτ 
)
it is equal to applying the independent state-outcome model f(X) = Y for τ times in sequence. In other
words X −→θ 
Y is sufficient to represent this relationship without needing τ.  It often goes unnoticed that
a sequence variable Xt = (X1
 . . .  Xt
) in modeling does not imply the t-dimension has been incorporated
where t serves as constants lacking computational flexibility. The same way also applies to Y τ .


However once including the “possible timing” τ with computable values it becomes necessary to account for
the potential components of Y which are possible to unfold their dynamics over their own timing separately.
For a simpler understanding let’s revisit the example of “storm causes flooding.” Suppose X represents
the storm and for each watershed ϑ encapsulates the effects of X determined by its unique hydrological
conditions. Let Y2 
denote the water levels observed over an extended period such as the next 30 days
if without any flood prevention. Let Y1 
indicate the daily variations in water levels (measured in ±cm to
reflect increases or decreases) resulting from flood-prevention efforts. In this case ϑ can be considered in
two components: ϑ = (ϑ1
 ϑ2
) separately identifying τ = 1 and τ = 2.


Specifically historical records of disasters without flood prevention could contribute to extracting I(ϑ2
)
based on which the ϑ1 
representation can be trained using recent records of flood prevention. Even if their
hydrological conditions are not exactly the same AI can extract such relational difference (ϑ1 
− ϑ2
). This
is because the capability of computing over timing dimensions empowers AI to extract common relational
information from different dynamics. From AI’s standpoint regardless of whether the flood crest naturally
occurs on the 7th day or is dispersed over the subsequent 30 days both Y2 
and (Y1 
+ Y2
) are linked to X
through the same volume of water introduced by X. In other words while AI deals with the computations
discerning what qualifies as a “disaster” remains a question for humans.


Conversely in traditional modeling ϑ is often viewed as a common cause of both X and Y termed a
“confounder” and serves as a predetermined functional parameter before computation. Therefore if such a
parameter is accurately specified to represent ϑ2
 when observations (Y1 
+ Y2
) imply a varied ϑ1
 it becomes


7

Under review as submission to TMLR


critical to identify the potential “reason” of such variances. If the underlying knowledge can be found
manual adjustments are naturally necessitated for (Y1 
+ Y2
) to ensure it performs as being produced by
ϑ2
; otherwise the modeling bias will be attributed to this unknown “reason” represented by the difference
(ϑ1 
− ϑ2
) named a hidden confounder.


About Dependence ϕ between Causal Components


As demonstrated in Figure 1 by introducing the dynamic outcome components in (c) the causal emergence
phenomenon in (b) can be explained by “overflowed” relational information with ϕ. Here do(Y )1 
and do(Y )2
act as differentiated Y1 
and Y2
 outcome by I(ϑ1
) and I(ϑ2
). That is the relation-first principle ensures ϑ
to be informatively separable as ϑ1 
and ϑ2
 leaving ϕ simply represent their statistical dependence. However
due to their dynamical significance ϕ may impact the conditional timing distribution across τ = 1 and τ = 2.


Theorem 3. Sequential causal modeling is required if the dependence between causal components
represented by ϕ has dynamically significant impact on the outcome timing dimension.


The sequential modeling procedure was applied in analyzing the “flooding” example where training ϑ1 
is
conditioned on the established ϑ2 
to ensure the resulting representation is meaningful. Specifically the
directed dependence ϕ from ϑ2 
to ϑ1 
requires that the timing-dimensional computations of Y1 
and Y2 
occur
sequentially with ϑ1 
following ϑ2
.  Practically the sequence is determined by the meaningful interaction
I(ϑ1 
| ϑ2
) or I(ϑ2 
| ϑ1
) adapted to the requirements of specific applications.


Suppose the two-step modeling process is Y2 
= f2
(X; ϑ2
) followed by Y1 
= f1
(X | Y2
; ϑ1
). According to the
adopted perspective its information explanation can be notably different. From the creator’s perspective
that enables relation-first I(ϑ) = I(ϑ2
) + I(ϑ1
) = 2I(ϑ2
) + I(ϑ1 
| ϑ2
) encapsulates all information needed
to “create” the outcome Y = Y1 
+ Y2
 with I(ϕ) = 0 indicating ϕ not an informative relation. When
adopting the traditional perspective as an observer ϑ1 
and ϑ2 
simply denote functional parameters where
the observational information manifests as I(ϕ | Y2
) = I(Y1
) − I(Y2
) > 0.


For clarity we use ϑ1 
⊥⊥ ϑ2 
to signify the timing-dimensional independence between Y1 
and Y2
 termed as
dynamical independence without altering the conventional understanding within the observational space
like Y1 
⊥⊥ Y2 
∈ R
m. On the contrary ϑ1 
⊥̸ ⊥ ϑ2 
implies a dynamical dependence which is an interaction
between Y1 
and Y2
. “Dynamically dependent or not” only holds when Y1 
and Y2 
are dynamically significant.


1


  2 
2


Since 1 
is not dynamically
significant as determined by 1 


1


1 
2 
2

Dynamically independent but
observationally dependent 


1


1


 
2 
2


Observationally and
dynamically dependent


Example (a) 1 
2 
Example (b) 1 
2 
Example (c) 1 
2


Figure 3: Illustrative examples for dynamical dependence and independence. The observational dependence
from Y1 
to Y2 
is displayed as 
−−→
y1
y2
 where red and blue indicate two different data instances.


Figure 3 is upgraded from the conventional causal Directed Acyclic Graph (DAG) in two aspects: 1) A node
represents a state value of the variable and 2) edge length shows timespans for a data instance (i.e. a data
point or realization) to achieve this value. This allows for the visualization of dynamic interactions through
different data instances. For instance Figure 3(c) shows that the dependence between ϑ1 
and ϑ2 
inversely
impacts their speeds such that achieving y1 
more quickly implies a slower attainment of y2
.


2.3 Potential Development Toward AGI


As demonstrated choosing between the observer’s or the creator’s perspective depends on the questions we
are addressing rather than a matter of conflict. In the former information is gained from observations and


8

Under review as submission to TMLR


represented by observables; while in the latter relational information preferentially exists as representing
the knowledge we aim to construct in modeling such that once the model is established we can use it to
deduce outcomes as a description of “possible observations in the future” without direct observation.


Causality questions inherently require the creator’s perspective since “informative observations” cannot
emerge out of nowhere. Empirically it is reflected as the challenge of specifying outcomes in traditional
causal modeling often referred to as “identification difficulty” Zhang (2012).  As mentioned by Schölkopf
et al. (2021) “we may need a new learning paradigm” to depart from the i.i.d.-based modeling assumption
which essentially asserts the objects we are modeling exactly exist as how we expect them to. We term this
conventional paradigm as object-first and have introduced the relation-first principle accordingly.


Learning
Dynamics . (∙) 


No Dynamical Interactions between
Learned Outcome Components
LLMs Inversed Learning 
Reinforcement Learning Causal
Representation Learning


Only State
Outcome . 


The Outcome Components present Significant
Interactions through  


Sequentially perform Relation-First modeling
to explore the structuralized dynamic outcome


Structural Causal Models
Direct RNN Applications in Causality
Causal Inference Causal Emergence


Figure 4: The do(Y )-Paradox in traditional Causality Modeling vs. modern Representation Learning.


The relation-first thinking has been embraced by the definition of Fisher Information as well as in do-calculus
that differentiates the relational information. Moreover neural networks with the back-propagation strategy
have technologically embodied it. Therefore it’s unsurprising that the advent of AI-based representation
learning signifies a turning point in causality modeling. From an engineering standpoint answering the
“what ...  if?” (i.e. counterfactual) question indicates the capacity of predicting do(Y ) as structuralized
dynamic outcomes. Intriguingly learning dynamics (i.e. the realization of do(·)) and predicting outcomes
(i.e. facilitating the role of Y ) present a paradox under the traditional learning paradigm as in Figure 4.


About AI-based Dynamical Learning


Understanding dynamics is a significant instinctive human ability. Representation learning achieves compu-
tational optimizations across the timing dimension notably embodying such capabilities. Specifically Large
Language Models (LLMs) Wes (2023) have sparked discussions about our progress toward AGI Schaeffer
et al. (2023).  The application of meta-learning Lake & Baroni (2023) in particular has enabled the au-
tonomous identification of semantically meaningful dynamics demonstrating the potential for human-like
intelligence. Yet it is also highlighted that LLMs still lack a true comprehension of causality Pavlick (2023).


The complexity of causality lies in potential interactions within a “possible world” not just in computing
individual possibilities whether they are dynamically significant or not. Instead of a single question “what ...
if?” stands for a self-extending logic where the “if” condition can be applied to computed results repeatedly
leading to complex structures. Thus causality modeling is to uncover the unobservable knowledge implied
by the observable X/do(X) → Y/do(Y ) phenomenons to enable its outcome beyond direct observations.


Advanced technologies such as reinforcement learning Arora (2021) and causal representation learning have
blurred the boundary between the roles of variable X/do(X) and outcome Y/do(Y ) which are manually
maintained in traditional causal inference. They often focus on the advanced efficacy in learning dynamics
yet it is frequently overlooked that the foundational RNN architecture is grounded in do(X) → Y without
establishing a dynamically interactable do(Y ). Essentially any significant dynamics that are autonomously
extracted by AI can be attributed to do(X). Even though within diffusion methods their computations can
be split into multiple rounds of do(X) → Y  since without an identified meaning as I(ϑ) the significance of
becoming a do(Y ) rather than remaining a sequence of discrete values Y τ = (Y1
 . . .  Yτ 
) is unfounded.


From AI’s viewpoint changes in the values of a sequential variable need not be meaningful although they may
have distinct implications for humans. For instance a consistent dynamic pattern that varies in unfolding
speed might indicate an individual dynamic do(X) distinct from Xt.  If this dynamic pattern specifically


9

Under review as submission to TMLR


signifies the effect (like I(ϑ)) of a certain cause (like X/do(X)) it could represent do(Y ).  However if the
speed change is attributable to another identifiable effect (such as I(ω)) it showcases a dynamical interaction.


About State Outcomes in Causal Inference


Causal inference and associated Structural Causal Models (SCMs) focus on causal structures taking into
account potential interactions. However the object-first paradigm restricts their outcomes to be “objective
observations” represented by Yτ 
with a predetermined timestamp τ.  This inherently implies all potential
effects conform to a singular “observed timing”. Thereby they can be consolidated into a one-time dynamic
leading to “structuralized observables” instead of “structuralized dynamics”. As in Figure 1 the overflowed
information I(do(Y )) − I(Y ) (from an observer’s perspective) “emerges” to form an informative relation ϕ
in a “possible world” rather than a deducible dependence between two dynamics do(Y )1 
and do(Y )2
.


Such “causal emergence” requires significant efforts on theoretical interpretations. Particularly the unknown
relation ϕ is often attributed to the well-known “hidden confounder” problem Greenland et al. (1999); Pearl
et al. (2000) linked to the fundamental assumptions of causal sufficiency and faithfulness Sobel (1996). In
practice converting causal knowledge represented by DAGs into operational causal models demands careful
consideration Elwert (2013) where data adjustments and model interpretations often rely on human insight
Sanchez et al. (2022); Crown (2019).  These theoretical accomplishments underpin causal inference’s core
value in the era dominated by statistical analysis before the advent of neural networks.


About Development of Relation-First Paradigm


As highlighted in Theorem 3 sequential modeling is necessary for causality to achieve structuralized dynamic
outcomes. When the prior knowledge of causal structure is given the relational information I(ϑ) has been
determined; correspondingly the sequential input and output data xt = (x1
 . . .  xt
) and yτ = (y1
 . . .  yτ 
)
can be chosen to enable AI to extract I(ϑ) through them. While for AI-detected meaningful dynamics we
should purposefully recognize “if it suggests a do(Y ) what I(ϑ) have we extracted?” The gained insights
can guide us to make the decision on whether and how to perform the next round of detection based on it.


Knowledge to be
built in AGI


Inquired
Relationship Black-box of AGI 


Generated Predictions Simulated
Observations etc.


Graphical Indexing of
Representations 


Specified Routine
to be Invoked 
Accumulated Relational
Representations 
Decoding


Encoding


Raw Data


Figure 5: Accessing AGI as a black-box with human-mediated parts colored in blue. A practically usable
system demands long-term representation accumulations and refinements which mirrors our learning process.


In this way the relational representations in latent space can be accumulated as vital resources organized
and managed through the graphically structured indices as depicted in Figure 5. This flow mirrors human
learning processes Pitt (2022) with these indices serving as causal DAGs in our comprehension. If knowledge
from various domains could be compiled and made accessible like a library over time then the representation
resource might be continuously optimized across diverse scenarios thereby enhancing generalizability.


From a human standpoint deciphering latent space representations becomes unnecessary. With sufficient
raw data we have the opportunity to establish nuanced causal reasoning through the use of graphical indices.
Specifically this involves an indexing process that translates inquiries into specific input-output graphical
routines guiding data streaming through autoencoders to produce human-readable observations. Although
convenient this approach could subject computer “intelligence” to more effective control.


10

Under review as submission to TMLR


3  Modeling Framework in Creator’s Perspective


Under the traditional i.i.d.-based framework questions must be addressed individually within their respective
modeling processes even when they share similar underlying knowledge. This necessity arises because each
modeling process harbors incorrect premises about the objective reality it faces which often goes unnoticed
because of conventional object-first thinking. The advanced modeling flexibility afforded by neural networks
further exposes this fundamental issue. Specifically it is identified as the model generalizability challenge
by Schölkopf et al. (2021). They introduced the concept of causal representation learning underscoring the
importance of prioritizing causal relational information before specifying observables.


Rather than merely raising a new method we aim to emphasize that the shift of perspective enables the
modeling framework across the “possible timing space” beyond solely observational one. As shown in Figure
6 when adopting the creator’s perspective space R
H is embraced to accommodate the abstract variables
representing the informative relations where the notion of ω will be introduced later.


Figure 6: The framework from the creator’s perspective where Y ∈ R
O−1 ∪ R
T (with t excluded) represents
the outcome governed by I(ϑ ω) without implying any observational information. An observer’s perspective
is Y ∈ R
O−1 ∪ τ with the observational information I(Y) defined but without R
H or R
T perceived.


When adopting an observer’s perspective it involves answering a “what...if” question just once. However
the genesis of such questions is rooted in the perspective of a “creator” aiming to explore all possibilities for
the optimal choice which is precisely what we embrace when seeking technological or engineering solutions.


Every possibility represents an observational outcome (“the what...”) for a specific causal relationship (“the
if...”) or a routine of consecutive relationships within a DAG akin to placing an observer within the creator’s
conceptual space. Thus the “creator’s perspective” acts as a space encompassing all potential “observer’s
perspectives” by treating the latter as a variable. Within this framework the once perplexing concept of
“collapse” in quantum mechanics becomes readily understandable.


From the creator’s perspective a causal relationship X −→ Y ϑ 
suggests that Y belongs to R
O−1 ∪ R
T  where
R
T represents a T-dimensional space with timing τ = 1 . . .  T sequentially marking the T components of
Y. The separation of these components depends on the creator’s needs regardless of which their aggregate
Y =  T
τ=1 
Yτ 
 is invariably governed by I(ϑ).  However once the creator places an observer for this
relationship from this “newborn” observer’s viewpoint space R
T ceases to exist and is perceived solely as
an “observed timeline” τ. In other words τ has lost its computational flexibility as the “timing dimension”
but remains merely a sequence of constant timestamps.


Thus the term “collapse” refers to this singular “perspective shift”.  Metaphorically a one-time “collapse”
is akin to opening Schrödinger’s box once and in the modeling context it signifies that a singular modeling
computation has occurred. Accordingly Theorem 3 can be reinterpreted: Causality modeling is to facilitate
“structuralized collapses” within R
T from the creator’s perspective. Importantly for the creator R
T is not
limited to representing a single relationship but can also include “structuralized relationships” by embracing
a broader macro-level perspective. In light of this we introduce the following definitions.


11

Under review as submission to TMLR


Definition 4. A causal relation ϑ can be defined as micro-causal if an extraneous relation ω exists
where I(ω) ̸⊆ I(ϑ) such that incorporating ω can form a new macro-causal relation denoted by
(ϑ ω). The process of incorporating ω is referred to as a generalization.


Definition 5. From the creator’s perspective the macro-level possible timing space R
T = T
τ=1 R
τ


is constructed by aggregating each micro-level space R
τ  where τ ∈ {1 . . .  T} indicates the timeline
that houses the sequential timestamps by adopting the observer’s perspective for R
τ .


To clarify the T-dimensional space R
T mentioned earlier is considered a micro-level concept which we
formally denote as R
τ .  Upon transitioning to the macro-level possible timing space R
T  the creator’s per-
spective is invoked. Within this perspective both R
H and R
T are viewed as conceptual spaces lacking
computationally meaningful notions like “dimensionality” or specific “distributions”.


In essence the moment we contemplate a potential “computation” the observer’s perspective is already
established from which the micro-level space R
τ (or a collection of such spaces {R
τ }) has been defined
and “primed for collapse” through the methodologies under contemplation. Philosophically the notion of a
timeline τ within the “thought space” R
T is characterized as “relative timing” Wulf et al. (1994); Shea et al.
(2001) in contrast to the “absolute timing” represented by t in this paper. Moreover in the modeling context
computations involving τ can draw upon the established Granger causality approach Granger (1993).


3.1 Hierarchical Levels by ω


As illustrated in Figure 1 the “causal emergence” phenomenon stems from adopting different perspectives
not truly integrating new relational information. We employ the terms “micro-causal” and “macro-causal”
to identify the new information integration defining the generalization process (as per Definition 4) and its
inverse is termed individualization. In modeling the generalizability of an established micro-causal model
f(; ϑ) is its ability to be reused in macro-causality without diminishing I(ϑ)’s representation.


The information gained from I(ϑ) to I(ϑ ω) often introduces a new hierarchical level of relation thereby
raising generalizability requirements for causal models. This may suggest new observables potentially as
new causes or outcome components or both. Let’s consider a logically causal relationship (without such
significance in modeling) as a simple example: Family incomes X affecting grocery shopping frequencies Y 
represented as X −→θ 
Y  where θ may vary internationally due to cultural differences ω creating two levels: a
global-level θ and a country-level (θ | ω). While ω isn’t a direct modeling target it’s an essential condition
necessitating the total information I(θ ω) = I(θ | ω) + I(ω). From the observer’s perspective it equates to
incorporating an additional observable like country Z as a new cause to affect Y with X jointly.


(a) AI-generated faces accompanied with hands (b) How human understand images of hands 


Level
Level
Level 


Observation 
  Knuckles Nails …
 Relative Positions
 Gestures 




 


Recognition 
Identification of Fingers
Left/Right & Gestures
Intentions  





Figure 7: AI can generate reasonable faces but treat hands as arbitrary mixtures of fingers while humans
understand observations hierarchically to avoid mess sequentially indexing through {θi
 θii 
 θiii 
}.


Addressing hierarchies within knowledge is a common issue in relationship modeling but timing distributional
hierarchies present significant challenges to traditional methods leading to the development of a specialized
“group-specific learning” Fuller et al. (2007) which primarily depends on manual identifications. However


12

Under review as submission to TMLR


this approach is no longer viable in modern AI-based applications necessitating the adoption of the relation-
first modeling paradigm. Below we present two examples to demonstrate this necessity: one is solely
observational and the other involves a causality with timing hierarchy.


Observational Hierarchy Example


The AI-created personas on social media can have realistic faces but seldom showcase hands since AI
struggles with the intricate structure of hands instead treating them as arbitrary assortments of finger-like
items. Figure 7(a) shows AI-created hands with faithful color but unrealistic shapes while humans can
effortlessly discern hand gestures from the grayscale sketches in (b).


Human cognition intuitively employs informative relations as the indices to visit mental representations Pitt
(2022).  As in (b) this process operates hierarchically where each higher-level understanding builds upon
conclusions drawn at preceding levels. Specifically Level I identifies individual fingers; Level II distinguishes
gestures based on the positions of the identified fingers incorporating additional information from our
understanding of how fingers are arranged to constitute a hand denoted by ωi
; and Level III grasps the
meanings of these gestures from memory given additional information ωii 
from knowledge.


Conversely AI models often do not distinguish the levels of relational information instead modeling overall
as in a relationship X −→θ 
Y with θ = (θi
 θii
 θiii
) resulting a lack of informative insights into ω. However
the hidden information I(ω) may not always be essential. For example AI can generate convincing faces
because the appearance of eyes θi 
strongly indicates the facial angles θii
 i.e. I(θii
) = I(θi
) indicating
I(ωi
) = 0 removing the need to distinguish eyes from faces.


On the other hand given that X has been fully observed AI can inversely deduce the relational information
using methods such as reinforcement learning Sutton (2018); Arora (2021).  In this particular case when
AI receives approval for generating hands with five fingers it may autonomously begin to derive I(θi
).
However when such hierarchies occur on the timing dimension of a dynamically significant Y they can
hardly be autonomously identified regardless of whether AI techniques are leveraged.


Timing Hierarchy in Causality Example


() = Initial Use of Medication  
 = the Measured Vital Sign ( = Blood Lipid in this Case)


Daily Outcome
Sequence 


() 
Level-I
Dynamic ℬ


Dynamic
of  


Specify the 30 
Static
Effect for all patients


Dynamic
of  


0 Day  20 Days 30 Days 40 Days Timeline  
(# of Days) 


( = ∅) 
1 


( =
 
  
 …)


(a) Timing Distribution of the Dynamic Outcome ℬ 
(b) Representation of Two-Level Dynamic Outcome


Figure 8: do(A) = the initial use of medication MA 
for reducing blood lipid B. By the rule of thumb the
effect of MA 
needs around 30 days to fully release (t = 30 at the black curve elbow).  Patient Pi 
and Pj
achieve the same magnitude of the effect by 20 and 40 days instead.


In Figure 8 Bω 
represents the observational sequence Bt = (B1
 . . .  B30 
) from a group of patients identified
by ω. Clinical studies typically aim to estimate the average effect (generalized-level I) on a predetermined
day like Bt+30 
= f(do(At
)). However our inquiry is indeed the complete level I dynamic Bo 
= 30
t=1 
do(Bt
)dt
which describes the trend of effect changing over time without anchored timestamps. To eliminate the level
II dynamic from data a “hidden confounder” is usually introduced to represent their unobserved personal
characteristics. Let us denote it by E and assume E linearly impact Bo
 making the level II dynamic Bω 
−Bo
simply signifying their individualized progress speeds for the same effect Bo
.


13

Under review as submission to TMLR


To accurately represent Bo 
with a sequential outcome traditional methods necessitate an intentional selection
or adjustment of training data. This is to ensure the “influence of E” is eliminated from the data even
unavoidable when adopting RNN models. In RNNs the dynamically significant representation is facilitated
only on do(A) while the sequential outcome Bt still requires predetermined timestamps. However once t
is specified for all patients without the data selection - for example let t = 30 to snapshot B30 
- bias is
inherently introduced since B30 
represents the different magnitude of effect Bo 
for various patients.


Such hierarchical dynamic outcomes are prevalent in many fields such as epidemic progression economic
fluctuations and strategic decision-making. Causal inference typically requires intentional data preprocessing
to mitigate inherent biases including approaches like PSM Benedetto et al. (2018) and backdoor adjustment
Pearl (2009) essentially to identify the targeted levels manually. However they have become impractical
due to the modern data volume and also pose a risk of significant information loss snowballing in struc-
turalized relationship modeling. On the other hand the significance of timing hierarchies has prompted the
development of neural network-based solutions in fields like anomaly detection Wu et al. (2018) to address
specific concerns without the intention of establishing a causal modeling framework.


Statistical Model  = (  )


() 
(())


the Unobserved
Characteristics 
of Patient  = {
 
 … } 



(a) DAG with Hidden Confounder 


  ∗  = {  ∗  
   ∗ 
 … }


()   


ID


(b) Relation-Indexing Disentanglement 


Patient ID = {  … }


Sequences
Decode


Encode
Sequences 


Sequences


∗ ID →


ID


(c) Latent Space Architecture of (b)


Figure 9: (a) shows the traditional causal DAG for the scenario depicted in Figure 8 (b) disentangles
its dynamic outcome in a hierarchical way by indexing through relations and (c) briefly illustrates the
autoencoder architecture for realizing the generalized and individualized reconstructions respectively.


The concept of “hidden confounder” is essentially elusive acting more as an interpretational compensation
rather than a constructive effort to enhance the model. For example Figure 9 (a) shows the conventional
causal DAG with hidden E depicted. Although the “personal characteristics” are signified it is not required
to be revealed by collecting additional data. This leads to an illogical implication: “Our model is biased due
to some unknown factors we don’t intend to know.” Indeed this strategy employs a hidden observable to
account for the omitted timing-dimensional nonlinearities in statistical models.


As illustrated in Figure 9(b) the associative causal variable do(A) ∗ E remains unknown unable to form
a modelable relationship. On the other hand relation-first modeling approaches only require an observed
identifier to index the targeted level in representation extractions like the patient ID denoted by ω.


3.2 The Generalizability Challenge across Multiple Timelines in R
T


From the creator’s perspective timelines in the macro-level possible timing space R
T may pertain to different
micro-causalities implying “structuralized” causal relationships. This poses a significant generalizability
challenge for traditional structural causal models (SCMs).


The example in Figure 10 showcases a practical scenario in a clinical study. This 3D causal DAG includes
two timelines τθ 
and τω
 with the x-axis categorically arranging observables. The upgrades to causal DAGs
as applied in Figure 3 are also adopted here ensuring that the lengths of the arrows reflect the timespan
required to achieve the state values represented by the observable nodes. Here the nodes marked in uppercase
letters indicate the values representing the mean effects of the current data population i.e. the group of
patients under analysis. Accordingly the lengths of the arrows indicate their mean timespans.


We use ∆τθ 
and ∆τω 
to signify the time steps (i.e. the unit timespans) on τθ 
and τω
 respectively. Considering
the triangle SA′B′ when each unit of effect is delivered from S to A′ (taking ∆τω
) it immediately starts


14

Under review as submission to TMLR


impacting B′ through 
−−−→
A′B′ (with ∆τθ 
required); simultaneously the next unit of effect begins its generation
at S. Under the relation-first principle this dual action requires a two-step modeling process to sequentially
extract the dynamic representations on
edge 
−−→
SB′ with a priorly specified timespan
τθ 
and τω
.  However
from S to B′. This 
in traditional SCM it is
inherently sets the ∆τθ 
: 
represented by the
∆τω 
ratio based on
the current population’s performance freezing the state value represented by B′ and fixing the geometrical
shape of the ASB′ triangle in this space.


A


S 


A’ 


C
B 


C’


B’ 


T2D: Type II Diabetes
LDL: Blood Lipid 
Statin: Medicine to Reduce LDL
BP: Blood Pressure


S  A  C


B 


A
S 
BC


A’  B’ C’


Figure 10: A 3D-view DAG in R
O−1 ∪ R
T with two timelines τθ 
and τω
. The SCM B′ = f(A C S) is to
evaluate the effect of Statin on reducing T2D risks. On τθ
 the step ∆τθ 
from y to (y + 1) allows A and C
to fully influence B; the step ∆τω 
on τω 
from (z + 1) to (z + 2) let S fully release to forward status A to A′.


The lack of model generalizability manifests in various ways depending on the intended scale of generaliza-
tion. For instance when focusing on a finer micro-scale causality the SCM that describes the mean effects
for the current population cannot be tailored to individual patients within this population. Conversely
aiming to generalize this SCM to accommodate other populations or a broader macro-scale causality may
lead to failure because the preset ∆τθ 
: ∆τω 
ratio lacks universal applicability.


3.3 Fundamental Reliance on Assumptions under Object-First


With Dynamically
Significant Outcomes


Causal
Discovery


Causation
Buildup 


Without Dynamically
Significant Outcomes


Relations still
Unknown


Relations in
Knowledge


Causal Modeling 


Relational Learning Directional Decision


❶ 
Omitted dynamics are covered
by the Faithfulness Assumption. 
Depending on observational
variance irrelative to causality.


❷ 
Can be aligned with knowledge
since no dynamics required. 
Maybe suggestive if observational
variance is causally meaningful.


Omitted dynamics are covered
❸ by Hidden Confounders or the
Sufficiency Assumption.
❹ Predetermined by knowledge. 


Predetermined by knowledge.


Predetermined by knowledge.


Figure 11: Categories of causal modeling applications. The left rectangular cube indicates all logically causal
relationships with the blue circle indicating potentially modelable ones.


Figure 11 categorizes the current causal model applications based on two aspects: 1) if the structure of θ/ϑ is
known a priori they are used for structural causation buildup or causal discovery; 2) depending on whether
the required outcome is dynamically significant they can either accurately represent true causality or not.


Under the conventional modeling paradigm capturing the significant dynamics within causal outcomes
autonomously is challenging. When building causal models based on given prior knowledge the omitted
dynamics become readily apparent. If these dynamics can be specifically attributed to certain unobserved


15

Under review as submission to TMLR


observables like the node E in Figure 9(a) such information loss is attributed to a hidden confounder.
Otherwise they might be overlooked due to the causal sufficiency assumption which presumes that all
potential confounders have been observed within the system. Typical examples of approaches susceptible
to these issues are structural equation models (SEMs) and functional causal models (FCMs) Glymour et al.
(2019); Elwert (2013).  Although state-of-the-art deep learning applications have effectively transformed
the discrete structural constraint into continuous optimizations Zheng et al. (2018; 2020); Lachapelle et al.
(2019) issues of lack of generalizability still hold Schölkopf et al. (2021); Luo et al. (2020); Ma et al. (2018).


On the other hand causal discovery primarily operates within the R
O space and is incapable of detecting
dynamically significant causal outcomes. If the interconnection of observables can be accurately specified as
the functional parameter θ there remains a chance to discover informative correlations. Otherwise mere
conditional dependencies among observables are unreliable for causal reasoning as seen in Bayesian networks
Pearl et al. (2000); Peters et al. (2014).  Typically undetected dynamics are overlooked due to the Causal
Faithfulness assumption which suggests that the observables can fully represent the underlying causal reality.


Furthermore the causal directions suggested by the results of causal discovery often lack logical causal
implications. Consider X and Y in the optional models Y = f(X; θ) and X = g(Y ; ϕ) with predetermined
parameters which indicate opposite directions. Typically the directionX → Y would be favored if L(θˆ) >
L(ϕˆ).  Let IXY 
(θ) denote the information about θ given P(X Y ).  Using p(·) as the density function the
integral 
X 
p(x; θ)dx remains constant in this context. Then:


IXY 
(θ) = E[( 
∂
∂θ 
log p(X Y ; θ))2 | θ] = 
Y 


∂
(  log p(x y; θ))2p(x y; θ)dxdy
X 
∂θ


= α 
Y 


∂
(
∂θ 
log p(y; x θ))2p(y; x θ)dy + β = αIY |X
(θ) + β with α β being constants.


Then θˆ = arg max P(Y | X θ) = arg min IY |X
(θ) = arg min IXY 
(θ) and L(θˆ) ∝ 1/IXY 
(θˆ).
θ  θ  θ


The inferred directionality indicates how informatively the observational data distribution can reflect the two
predetermined parameters. Consequently such directionality is unnecessarily logically meaningful but could
be dominated by the data collection process with the predominant entity deemed the “cause” consistent
with other existing conclusions Reisach et al. (2021); Kaiser & Sipos (2021).


4  Relation-Indexed Representation Learning (RIRL)


This section introduces a method for realizing the proposed relation-first paradigm referred to as RIRL for
brevity. Unlike existing causal representation learning which is primarily confined to the micro-causal scale
RIRL focuses on facilitating structural causal dynamics exploration in the latent space.


Specifically “relation-indexed” refers to its micro-causal realization approach guided by the relation-first
principle where the indexed representations are capable of capturing the dynamic features of causal outcomes
across their timing-dimensional distributions. Furthermore from a macro-causal viewpoint the extracted
representations naturally possess high generalizability ready to be reused and adapted to various practical
conditions. This advancement is evident in the structural exploration process within the latent space.


Unlike traditional causal discovery RIRL exploration spans R
O−1∪R
T to detect causally significant dynamics
without concerns about “hidden confounders” where R
T encompasses all possibilities of the potential causal
structure. The representations obtained in each round of RIRL detection serve as elementary units for reuse
enhancing the flexibility of structural models. This exploration process eventually yields DAG-structured
graphical indices with each input-output pair representing a specific causal routine readily accessible.


Subsequently section 4.1 delves into the micro-causal realization to discuss the technical challenges and their
resolutions including the architecture and core layer designs. Section 4.2 introduces the process of “stacking”
relation-indexed representations in the latent space to achieve hierarchical disentanglement at an effect node
in DAG. Finally section 4.3 demonstrates the exploration algorithm from a macro-causal viewpoint.


16

Under review as submission to TMLR


4.1 Micro-Causal Architecture


For a relationship X −→ Y θ 
given sequential observations {xt} and {yτ } with |→−
x | = n and |→−
y | = m the
relation-indexed representation aims to establish (X θ Y) in the latent space R
L. Firstly an initialization
is needed for X and Y individually to construct their latent space representations from observed data
sequences. For clarity we use H ∈ R
L and V ∈ R
L to refer to the latent representations of X ∈ R
O and
Y ∈ R
O respectively. The neural network optimization to derive θ is a procedure between H as input and V
as output. In each iteration H θ and V are sequentially refined in three steps until the distance between H
and V is minimized within R
L without losing their representations for X and Y. Consider instances x and y
of X and Y that are represented by h and v correspondingly in R
L as in Figure 14. The latent dependency
P(v|h) represents the relational function f(; θ). The three optimization steps are as follows:


1. Optimizing the cause-encoder by P(h|x) the relation model by P(v|h) and the effect-decoder by
P(y|v) to reconstruct the relationship x → y represented as h → v in R
L.
2. Fine-tuning the effect-encoder P(v|y) and effect-decoder P(y|v) to accurately represent y.
3. Fine-tuning the cause-encoder P(h|x) and cause-decoder P(x|h) to accurately represent x.


In this process h and v are iteratively adjusted to reduce their distance in R
L with θ serving as a bridge
to span this distance and guiding the output to fulfill the associated representation (H θ V).  From the
perspective of the effect node Y this tuple represents its component indexing through θ denoted as Yθ
.


However it introduces a technical challenge: for a micro-causality θ the dimensionality L of the latent space
must satisfy L ≥ rank(X θ Y) to provide adequate freedom for computations. To accommodate a structural
DAG this lower boundary can be further enhanced to be certainly larger than the input vector length
→−
|X | = t ∗ n.  This necessitates a specialized autoencoder to realize a “higher-dimensional representation”
where the accuracy of its reconstruction process becomes significant and essentially requires invertibility.


Expander 


Fully
Connect 
Relu 


Reducer


Encoder 
Input
 
Keys 


… 


Latent Space
Representation
Copy 


Output Decoder



Figure 12: Invertible autoencoder architecture for extracting higher-dimensional representations.


Figure 12 illustrates the designed autoencoder architecture featured by a pair of symmetrical layers named
Expander and Reducer (source code is available 1). The Expander magnifies the input vector by capturing its
higher-order associative features while the Reducer symmetrically diminishes dimensionality and reverts to
its initial formation. For example the Expander showcased in Figure 12 implements a
→− 
double-wise expansion.
Every duo of digits from X is encoded into a new digit by associating with a random constant termed the
→−
Key.  This Key is generated by the encoder and replicated by the decoder. Such pairwise processing of X
expands its length from (t ∗ n) to be (t ∗ n − 1)2.  By concatenating the expanded vectors using multiple
→−
Keys X can be considerably expanded ready for the subsequent reduction through a regular encoder.


The four blue squares in Figure 12 with unique grid patterns signify the resultant vectors of the four distinct
Keys with each square symbolizing a (t ∗ n − 1)2 length vector. Similarly higher-order expansions such as
triple-wise across three digits can be chosen with adapted Keys to achieve more precise reconstructions.


1https://github.com/kflijia/bijective_crossing_functions/blob/main/code_bicross_extracter.py


17

Under review as submission to TMLR


 
 


 
−   
⊗ (−(
))


Output 
+
  Encrypt
×

  
Input 
 
⊗    
+ (
) 


 



−
  Decrypt
÷

  


Figure 13: Expander (left) and Reducer (right). 


Likelihood
(|ℎ) 


Output 
Decrypt


Decode


Prior (ℎ) 
ℎ


Posterior Encode
(ℎ|) Encrypt


Input  


Relation
Dependency
(|ℎ)
(; ) 


Output 
Decrypt


Decode 
Likelihood
  


Prior ()



Encode


Encrypt


Input  


Posterior
  


Figure 14: Micro-Causal architecture.


Figure 13 illustrates the encoding and decoding processes within the Expander and Reducer targeting the
digit pair (xi
 xj
) for i ̸= j ∈ 1 . . .  n. The Expander function is defined as ηκ
(xi
 xj
) = xj 
⊗exp(s(xi
))+t(xi
)
which hinges on two elementary functions s(·) and t(·).  The parameter κ represents the adopted Key
comprising of their weights κ = (ws
 wt
). Specifically the Expander morphs xj 
into a new digit yj 
utilizing
xi 
as a chosen attribute. In contrast the Reducer symmetrically performs the inverse function η
κ
−1 defined
as (yj 
− t(yi
)) ⊗ exp(−s(yi
)). This approach circumvents the need to compute s−1 or t−1 thereby allowing
more flexibility for nonlinear transformations through s(·) and t(·). This is inspired by the groundbreaking
work in Dinh et al. (2016) on invertible neural network layers employing bijective functions.


4.2 Stacking Relation-Indexed Representations


In each round of detection during the macro-causal exploration a micro-causal relationship will be selected
for establishment. Nonetheless the cause node in it may have been the effect node in preceding relations
e.g. the component Yθ 
may already exist at Y when Y → Z is going to be established. This process of
conditional representation buildup is referred to as “stacking”.


For a specific node X the stacking processes where it serves as the effect sequentially construct its hierar-
chical disentanglement according to the DAG. It requires the latent space dimensionality to be larger than
rank(X) + T where T represents the in-degree of node X in this DAG as well as its number of components
as the dynamic effects. From a macro-causal perspective T can be viewed as the number of necessary edges
in a DAG. While to fit it into R
L a predetermined L must satisfy L > rank(X) + T where X represents
the data matrix encompassing all observables. In this study we bypass further discussions on dimensionality
boundaries by assuming L is large enough for exploration and empirically determine L for the experiments.


(a) Stacking  with 


Output 
Likelihood
  
Prior ()


Effect
(|ℎ) 


(|)


()


Posterior
  


Input  


Effect
(|)


Prior
() 


(b) Stacking  with 


Output 
Likelihood
  
Prior ()


Effect
(|ℎ) 


(|)


()


Posterior
   


Effect
(|)


Prior
()


Input 


Figure 15: Stacking relation-indexed representations to achieve hierarchical disentanglement.


Figure 15 illustrates the stacking architectures under two different scenarios within a three-node system
{X Y Z}. In this figure the established relationship X → Y is represented by the blue data streams and


18

Under review as submission to TMLR


layers. The scenarios differ in the causal directions between Y and Z: the left side represents X → Y ← Z
while the right side depicts X → Y → Z.


The hierarchically stacked representations allow for flexible input-output combinations to represent different
causal routines as needed. For simple exemplification we use → to denote the input and output layers in
the stacking architecture. On the left side of Figure 15 P(v|h) → P(α) represents the X → Y relationship
while P(α|k) implies Z → Y. Conversely on the right P(v) → P(β|k) denotes the Y → Z relationship with
Y as the input. Meanwhile P(v|h) → P(β|k) captures the causal sequence X → Y → Z.


4.3 Exploration Algorithm in the Latent Space


Algorithm 1: RIRL Exploration
Result: ordered edges set E = {e1 . . .  en
}
E = {} ; NR 
= {n0 
| n0 
∈ N Parent(n0) = ∅} ;
while NR 
⊂ N do
∆= {} ;
for n ∈ N do
for p ∈ Parent(n) do
if n /∈ NR 
and p ∈ NR 
then
e = (p n);
β = {};
for r ∈ NR 
do
if r ∈ Parent(n) and r ̸= p then
β = β ∪ r
end
end
δe = K(β ∪ p n) − K(β n);
∆= ∆ ∪ δe;
end
end
end
σ = argmine(δe 
| δe 
∈ ∆);
E = E ∪ σ;  NR 
= NR 
∪ nσ;
end 


G = (N E)
N
E
NR
E
K(β n)
β
n
δe
∆= {δe
}
npr
eσ 


graph G consists of N and E
the set of nodes
the set of edges
the set of reachable nodes
the list of discovered edges
KLD metric of effect β → n
the cause nodes
the effect node
KLD Gain of candidate edge e
the set {δe
} for e
notations of nodes
notations of edges


Algorithm 1 outlines the heuristic exploration procedure among the initialized representations of nodes.
We employ the Kullback-Leibler Divergence (KLD) as the optimization criterion to evaluate the similarity
between outputs such as the relational P(v|h) and the prior P(v). A lower KLD value indicates a stronger
causal strength between the two nodes. Additionally we adopt the Mean Squared Error (MSE) as another
measure of accuracy. Considering its sensitivity to data variances Reisach et al. (2021) we do not choose
MSE as the primary criterion.


Undetected Edge
Candidate Edge
Selected Edge 


 
1  


2 
   



 
2 



3
 


4
 


 
 
 
4


 
is    
deleted 


 
 
 
4


new  
  
5
1    2    3    4   


  Reached
Node


  Unreached
Node


Figure 16: An illustrative example of a detection round in latent space during RIRL exploration.


Figure 16 completely illustrates a detection round within the latent space that represents R
O−1 ∪ R
T .  A
new representation for the selected edge is stacked upon the previously explored causal structure during this
process. It contains four primary steps: In Step 1 two edges e1 
and e3
 have been selected in previous
detection rounds. In Step 2 e1
 having been selected becomes the preceding effect at node B for the next
round. In Step 3 with e3 
selected in the new round the candidate edge e2 
from A to C must be deleted
and rebuilt since e3 
alters the conditions at C. Step 4 depicts the resultant structure.


19

Under review as submission to TMLR


5  RIRL Exploration Experiments


In the experiments our objective is to evaluate the proposed RIRL method from three perspectives: 1)
the performance of the higher-dimensional representation autoencoder assessed through its reconstruction
accuracy; 2) the effectiveness of hierarchical disentanglement for a specific effect node as determined by the
explored causal DAG; 3) the method’s ability to accurately identify the underlying DAG structure through
exploration. A comprehensive demonstration of the conducted experiments is available online2. However it
is important to highlight two primary limitations of the experiments which are detailed as follows:


Firstly as an initial realization of the relation-first paradigm RIRL struggles with modeling efficiency since
it requires a substantial amount of data points for each micro-causal relationship making the heuristic
exploration process slow. The dataset used is generated synthetically thus providing adequate instances.
However current general-use simulation systems typically employ a single timeline to generate time sequences
- It means that interactions of dynamics across multiple timelines cannot be showcased. Ideally real-world
data like clinical records would be preferable for validating the macro-causal model’s generalizability. Due
to practical constraints we are unable to access such data for this study and therefore designate it as an
area for future work. The issues of generalization inherent in such data have been experimentally confirmed
in prior work Li et al. (2020) which readers may find informative.


Secondly the time windows for the cause and effect denoted by n and m were fixed at 10 and 1 respectively.
This arose from an initial oversight in the experimental design stage wherein the pivotal role of dynamic
outcomes was not fully recognized and our vision was limited by the RNN pattern. While the model can
adeptly capture single-hop micro-causality it struggles with multi-hop routines like X → Y → Z since the
dynamics in Y have been discredited by m = 1. However it does not pose a significant technical challenge
to expand the time window in future works.


5.1 Hydrology Dataset


C
A 
D


B  E


F 


G 
1st tier causality
H  J 
2nd tier causality


I  3rd tier causality 


ID  Variable Name  Explanation


A  Environmental set I  Wind Speed Humidity Temperature


B  Environmental set II  Temperature Solar Radiation Precipitation


C  Evapotranspiration Evaporation and transpiration


D  Snowpack The winter frozen water in the ice form


E  Soil Water  Soil moisture in vadose zone


F  Aquifer  Groundwater storage


G  Surface Runoff Flowing water over the land surface


H  Lateral  Vadose zone flow


I  Baseflow Groundwater discharge


J  Streamflow Sensors recorded outputs


Figure 17: Hydrological causal DAG: routine tiers organized by descending causality strength.


The employed dataset is from a widely-used synthetic resource in the field of hydrology aimed at enhancing
streamflow predictions based on observed environmental conditions such as temperature and precipitation.
In hydrology deep learning particularly RNN models has gained favor for extracting observational repre-
sentations and predicting streamflow Goodwell et al. (2020); Kratzert et al. (2018). We focus on a simulation
of the Root River Headwater watershed in Southeast Minnesota covering 60 consecutive virtual years with
daily updates. The simulated data is from the Soil and Water Assessment Tool (SWAT) a comprehensive
system grounded in physical modules to generate dynamically significant hydrological time series.


Figure 17 displays the causal DAG employed by SWAT complete with node descriptions. The hydrological
routines are color-coded based on their contribution to output streamflow: Surface runoff (the 1st tier)
significantly impacts rapid streamflow peaks followed by lateral flow (the 2nd tier); baseflow dynamics (the
3rd tier) have a subtler influence. Our exploration process aims to reveal these underlying tiers.


2https://github.com/kflijia/bijective_crossing_functions.git


20

Under review as submission to TMLR


Table 1: Characteristics of observables and corresponding reconstruction performances.


Variable
A
B
C
D
E
F
G
H
I
J 


Dim Mean Std Min Max Non-Zero Rate% RMSE on Scaled RMSE on Unscaled BCE of Mask
5  1.8513 1.5496 -3.3557 7.6809 87.54  0.093  0.871  0.095
4  0.7687 1.1353 -3.3557 5.9710 64.52  0.076  0.678  1.132
2  1.0342 1.0025 0.0  6.2145 94.42  0.037  0.089  0.428
3  0.0458 0.2005 0.0  5.2434 11.40  0.015  0.679  0.445
2  3.1449 1.0000 0.0285 5.0916 100  0.058  3.343  0.643
4  0.3922 0.8962 0.0  8.6122 59.08  0.326  7.178  2.045
4  0.7180 1.1064 0.0  8.2551 47.87  0.045  0.81  1.327
4  0.7344 1.0193 0.0  7.6350 49.93  0.045  0.009  1.345
3  0.1432 0.6137 0.0  8.3880 21.66  0.035  0.009  1.672
1  0.0410 0.2000 0.0  7.8903 21.75  0.007  0.098  1.088


Table 2: The brief results from the RIRL exploration.


Edge A→C B→D C→D C→G D→G G→J  D→H H→J  B→E E→G E→H C→E E→F F→I  I→J  D→I
KLD 7.63 8.51 10.14 11.60 27.87 5.29 25.19 15.93 37.07 39.13 39.88 46.58 53.68 45.64 17.41 75.57
Gain 7.63 8.51 1.135 11.60 2.454 5.29 25.19 0.209 37.07 -5.91 -3.29 2.677 53.68 45.64 0.028 3.384


5.2 Higher-Dimensional Reconstruction


This test is based on ten observable nodes each requiring an individual autoencoder for initialing its higher-
dimensional representation. Table 1 lists the characteristics of these observables after being scaled (i.e.
normalized) along with their autoencoders’ reconstruction accuracies assessed in the root mean square
error (RMSE) where a lower RMSE indicates higher accuracy for both scaled and unscaled data.


The task is challenged by the limited dimensionalities of the ten observables - maxing out at just 5 and the
target node J having just one attribute. To mitigate this we duplicate the input vector to a consistent
12-length and add 12 dummy variables for months resulting in a 24-dimensional input. A double-wise
extension amplifies this to 576 dimensions from which a 16-dimensional representation is extracted via the
autoencoder. Another issue is the presence of meaningful zero-values such as node D (Snowpack in winter)
which contributes numerous zeros in other seasons and is closely linked to node E (Soil Water). We tackle
this by adding non-zero indicator variables called masks evaluated via binary cross-entropy (BCE).


Despite challenges RMSE values ranging from 0.01 to 0.09 indicate success except for node F (the Aquifer).
Given that aquifer research is still emerging (i.e. the 3rd tier baseflow routine) it is likely that node F in
this synthetic dataset may better represent noise than meaningful data.


5.3 Hierarchical Disentanglement


Table 3 provides the performance of stacking relation-indexed representations. For each effect node the
accuracies of its micro-causal relationship reconstructions are listed including the ones from each single
cause node (e.g. B → D or C → D) and also the one from combined causes (e.g. BC → D).  We call
them “single-cause” and “full-cause” for clarity. We also list the performances of their initialized variable
representations on the left side to provide a comparative baseline. In micro-causal modeling the effect node
has two outputs with different data stream inputs. One is input from its own encoder (as in optimization
step 2) and the other is from the cause-encoder i.e. indexing through the relation (as in optimization step
1). Their performances are arranged in the middle part and on the right side of this table respectively.


The KLD metrics in Table 3 indicate the strength of learned causality with a lower value signifying stronger.
Due to the data including numerous meaningful zeros we have an additional reconstruction for the binary
outcome as “whether zero or not” named “mask” and evaluated in Binary Cross Entropy (BCE).


For example node J’s minimal KLD values suggest a significant effect caused by nodes G (Surface Runoff)
H (Lateral) and I (Baseflow). In contrast the high KLD values imply that predicting variable I using D
and F is challenging. For nodes D E and J the “full-cause” are moderate compared to their “single-cause”
scores suggesting a lack of informative associations among the cause nodes. In contrast for nodes G and H
lower “full-cause” KLD values imply capturing meaningful associative effects through hierarchical stacking.
The KLD metric also reveals the most contributive cause node to the effect node. For example the proximity
of the C → G strength to CDE → G suggests that C is the primary contributor to this causal relationship.


21

Under review as submission to TMLR


Figure 18: Reconstructed dynamics via hierarchically stacked relation-indexed representations.


Figure 18 showcases reconstructed timing distributions for the effect nodes J G and I in the same synthetic
year to provide a straightforward overview of the hierarchical disentanglement performances. Here black
dots represent the ground truth; the blue line indicates the initialized variable representation and the “full-
cause” representation generates the red line. In addition to RMSE we also employ the Nash–Sutcliffe model
efficiency coefficient (NSE) as an accuracy metric commonly used in hydrological predictions. The NSE
ranges from -∞ to 1 with values closer to 1 indicating higher accuracy.


The initialized variable representation closely aligns with the ground truth as shown in Figure 18 attesting
to the efficacy of our proposed autoencoder architecture. As expected the “full-cause” performs better than
the “single-cause” for each effect node. Node J exhibits the best prediction whereas node I presents a
challenge. For node G causality from C proves to be significantly stronger than the other two D and E.


5.4 DAG Structure Exploration
The first round of detection starts from the source nodes A and B and proceeds to identify their potential
edges until culminating in the target node J.  Candidate edges are selected based on their contributions
to the overall KLD sum (less gain is better).  Table 6 shows the detected order of the edges in Figure 17
accompanied by corresponding KLD sums in each round and also the KLD gains after each edge is included.
Color-coding in the cells corresponds to Figure 17 indicating tiers of causal routines. The arrangement
underscores the effectiveness of this latent space exploration approach.


Table 4 in Appendix A displays the complete exploration results with candidate edge evaluations in each
round of detection. Meanwhile to provide a clearer context about the dataset qualification with respect
to underlying structure identification we also employ the traditional causal discovery method Fast Greedy


22

Under review as submission to TMLR


Table 3: Performances of micro-causal relationship reconstructions using RIRL categorized by effect nodes.


Efect
Node


C


D


E


F


G


H


I


J 


Variable Representation
(Initialized)
RMSE BCE
on Scaled
Values 
on Unscaled
Values 
Mask


0.037  0.089  0.428


0.015  0.679  0.445


0.058


0.326


0.045 


3.343


7.178


0.81 


0.643


2.045


1.327


0.045  0.009


0.035  0.009 


1.345


1.672


0.007  0.098  1.088 


Cause
Node


A
BC
B
C
BC
B
C
E
CDE
C
D
E
DE
D
E
DF
D
F
GHI
G
H
I 


Variable Representation
(in Micro-Causal Models)
RMSE BCE
on Scaled
Values 
on Unscaled
Values 
Mask


0.0295 0.0616 0.4278
0.0350 1.0179 0.1355
0.0341 1.0361 0.1693
0.0331 0.9818 0.3404
0.4612 26.605  0.6427
0.6428 37.076  0.6427
0.5212 30.065  1.2854
0.4334 8.3807 3.0895
0.0538 0.9598 0.0878
0.1057 1.4219 0.1078
0.1773 3.6083 0.1842
0.1949 4.7124 0.1482
0.0889 0.0099 2.5980
0.0878 0.0104 0.0911
0.1162 0.0105 0.1482
0.0600 0.0103 3.4493
0.1212 0.0108 3.0048
0.0540 0.0102 3.4493
0.0052 0.0742 0.2593
0.0077 0.1085 0.4009
0.0159 0.2239 0.4584
0.0308 0.4328 0.3818 


Relation-Indexed Representation


RMSE
on Scaled on Unscaled
Values Values
0.1747 0.3334
0.0509 1.7059
0.0516 1.7737
0.0512 1.7265
0.7827 45.149
0.8209 47.353
0.7939 45.791
0.4509 5.9553
0.1719 3.5736
0.2996 4.6278
0.4112 8.0841
0.5564 10.852
0.3564 0.0096
0.4301 0.0095
0.5168 0.0097
0.1158 0.0099
0.2073 0.0108
0.0948 0.0098
0.0090 0.1269
0.0099 0.1390
0.0393 0.5520
0.0397 0.5564 


BCE


Mask


0.4278
0.1285
0.1925
0.3667
0.6427
0.6427
1.2854
3.0895
0.1340
0.1362
0.2228
0.1877
2.5980
0.0911
3.8514
3.4493
3.0048
3.4493
0.2937
0.4375
0.4938
0.3954 


KLD
(in latent
space)
7.6353
9.6502
8.5147
10.149
39.750
37.072
46.587
53.680
8.1360
11.601
27.879
39.133
21.905
25.198
39.886
49.033
75.577
45.648
5.5300
5.2924
15.930
17.410


Search (FGES) with a 10-fold cross-validation to perform the same procedure as RIRL exploration. The
results in Table 5 are available in Appendix A exhibiting the difficulties of using conventional methods.


6  Conclusions
This paper focuses on the inherent challenges of the traditional i.i.d.-based learning paradigm in addressing
causal relationships. Conventionally we construct statistical models as observers of the world grounded
in epistemology. However adopting this perspective assumes that our observations accurately reflect the
“reality” as we understand it implying that seemingly objective models may actually be based on subjective
assumptions. This fundamental issue has become increasingly evident in causality modeling especially with
the rise of applications in causal representation learning that aim to automate the specification of causal
variables traditionally done manually.


Our understanding of causality is fundamentally based on the creator’s perspective as the “what...if” ques-
tions are only valid within the possible world we conceive in our consciousness. The advocated “perspective
shift” represents a transformation from an object-first to a relation-first modeling paradigm a change that
transcends mere methodological or technical advancements. Indeed this shift has been facilitated by the
advent of AI particularly through neural network-based representation learning which lays the groundwork
for implementing relation-first modeling in computer engineering.


The limitation of the observer’s perspective in traditional causal inference prevents the capture of dynamic
causal outcomes namely the nonlinear timing distributions across multiple “possible timelines”. Accordingly
this oversight has led to compensatory efforts such as the introduction of hidden confounders and the reliance
on the sufficiency assumption. These theories have been instrumental in developing knowledge systems across
various fields over the past decades. However with the rapid advancement of AI techniques the time has
come to move beyond the conventional modeling paradigm toward the potential realization of AGI.


In this paper we present relation-first principle and its corresponding modeling framework for structuralized
causality representation learning based on discussions about its philosophical and mathematical underpin-
nings. Adopting this new framework allows us to simplify or even bypass complex questions significantly.
We also introduce the Relation-Indexed Representation Learning (RIRL) method as an initial application of
the relation-first paradigm supported by experiments that validate its efficacy.


23

Under review as submission to TMLR


References


Natalia Andrienko Gennady Andrienko and Peter Gatalsky. Exploratory spatio-temporal visualization: an
analytical review. Journal of Visual Languages & Computing 14(6):503–541 2003.


Saurabh Arora Prashant Doshi. A survey of inverse reinforcement learning: Challenges methods and
progress. Artificial Intelligence 297:***********.


Umberto Benedetto Stuart J Head Gianni D Angelini and Eugene H Blackstone. Statistical primer:
propensity score matching and its alternatives. European Journal of Cardio-Thoracic Surgery 53(6):
1112–1117 2018.


William H Crown. Real-world evidence causal inference and machine learning. Value in Health 22(5):
587–592 2019.


A Philip Dawid. Conditional independence in statistical theory. Journal of the Royal Statistical Society:
Series B (Methodological) 41(1):1–15 1979.


Laurent Dinh Jascha Sohl and Samy Bengio. Density estimation using real nvp. arXiv:1605.08803 2016.


Frederick Eberhardt and Lin Lin Lee. Causal emergence: When distortions in a map obscure the territory.
Philosophies 7(2):30 2022.


Felix Elwert. Graphical causal models. Handbook of causal analysis for social research pp. 245–273 2013.


Ronald Aylmer Fisher et al. 012: A mathematical examination of the methods of determining the accuracy
of an observation by the mean error and by the mean square error. 1920.


Ursula Fuller Colin G Johnson Tuukka Ahoniemi Diana Cukierman Isidoro Hernán-Losada Jana Jackova
Essi Lahtinen Tracy L Lewis Donna McGee Thompson Charles Riedesel et al. Developing a computer
science-specific learning taxonomy. ACm SIGCSE Bulletin 39(4):152–170 2007.


Clark Glymour Kun Zhang and Peter Spirtes. Review of causal discovery methods based on graphical
models. Frontiers in genetics 10:524 2019.


Allison E Goodwell Peishi Jiang Benjamin L Ruddell and Praveen Kumar. Debates—does information
theory provide a new paradigm for earth science? causality interaction and feedback. Water Resources
Research 56(2):e2019WR024940 2020.


Clive WJ Granger. Modelling non-linear economic relationships. OUP Catalogue 1993.


Sander Greenland Judea Pearl and James M Robins. Confounding and collapsibility in causal inference.
Statistical science 14(1):29–46 1999.


Erik P Hoel. When the map is better than the territory. Entropy 19(5):188 2017.


Erik P Hoel Larissa Albantakis and Giulio Tononi. Quantifying causal emergence shows that macro can
beat micro. Proceedings of the National Academy of Sciences 110(49):19790–19795 2013.


Yimin Huang Marco Valtorta. Pearl’s calculus of intervention is complete. arXiv:1206.6831 2012.


Marcus Kaiser and Maksim Sipos. Unsuitability of notears for causal graph discovery. arXiv:2104.05441
2021.


Frederik Kratzert Daniel Klotz Claire Brenner Karsten Schulz and Mathew Herrnegger. Rainfall–runoff
modelling using lstm networks. Hydrology and Earth System Sciences 22(11):6005–6022 2018.


Sébastien Lachapelle Philippe Brouillard Tristan Deleu and Simon Lacoste-Julien. Gradient-based neural
dag learning. arXiv preprint arXiv:1906.02226 2019.


Brenden M Lake and Marco Baroni. Human-like systematic generalization through a meta-learning neural
network. Nature pp. 1–7 2023. 


24

Under review as submission to TMLR


Jia Li Xiaowei Jia Haoyu Yang Vipin Kumar Michael Steinbach and Gyorgy Simon. Teaching deep
learning causal effects improves predictive performance. arXiv preprint arXiv:2011.05466 2020.


Yunan Luo Jian Peng and Jianzhu Ma. When causal inference meets deep learning. Nature Machine
Intel ligence 2(8):426–427 2020.


Alexander Ly Maarten Marsman Josine Verhagen Raoul PPP Grasman and Eric-Jan Wagenmakers. A
tutorial on fisher information. Journal of Mathematical Psychology 80:40–55 2017.


Jianzhu Ma Michael Ku Yu Samson Fong Keiichiro Ono Eric Sage Barry Demchak Roded Sharan and
Trey Ideker. Using deep learning to model the hierarchical structure and function of a cell. Nature methods
15(4):290–298 2018.


Gary Marcus. The next decade in ai: four steps towards robust artificial intelligence. arXiv preprint
arXiv:2002.06177 2020.


Tshilidzi Marwala. Causality correlation and artificial intelligence for rational decision making.  World
Scientific 2015.


James Massey et al. Causality feedback and directed information. In Proc. Int. Symp. Inf. Theory
Applic.(ISITA-90) pp. 303–305 1990.


Allen Newell Herbert A Simon. Computer science as empirical inquiry: Symbols and search. In ACM Turing
award lectures pp. 1975. 2007.


Mohammed Ombadi Phu Nguyen Soroosh Sorooshian and Kuo-lin Hsu. Evaluation of methods for causal
discovery in hydrometeorological systems. Water Resources Research 56(7):e2020WR027251 2020.


Ellie Pavlick. Symbols and grounding in large language models. Philosophical Transactions of the Royal
Society A 381(2251):20220041 2023.


Judea Pearl. Causal inference in statistics: An overview. 2009.


Judea Pearl. The do-calculus revisited. arXiv preprint arXiv:1210.4852 2012.


Judea Pearl et al. Models reasoning and inference. Cambridge UK: CambridgeUniversityPress 19(2) 2000.


Jonas Peters Joris M Mooij Dominik Janzing and Bernhard Schölkopf. Causal discovery with continuous
additive noise models. 2014.


David Pitt. Mental Representation. In Edward N. Zalta and Uri Nodelman (eds.) The Stanford Encyclopedia
of Philosophy. Metaphysics Research Lab Stanford University Fall 2022 edition 2022.


Alexander G Reisach Christof Seiler and Sebastian Weichwald. Beware of the simulated dag! varsortability
in additive noise models. arXiv preprint arXiv:2102.13647 2021.


Pedro Sanchez Jeremy P Voisey Tian Xia Hannah I Watson Alison Q O’Neil and Sotirios A Tsaftaris.
Causal machine learning for healthcare and precision medicine. Royal Society Open Science 9(8):***********.


Rylan Schaeffer Brando Miranda and Sanmi Koyejo. Are emergent abilities of large language models a
mirage? arXiv preprint arXiv:2304.15004 2023.


Bernhard Schölkopf Francesco Locatello Stefan Bauer Nan Rosemary Ke Nal Kalchbrenner Anirudh
Goyal and Yoshua Bengio. Toward causal representation learning. IEEE 109(5):612–634 2021.


Thomas Schreiber. Measuring information transfer. Physical review letters 85(2):461 2000.


Charles H Shea Gabriele Wulf Jin-Hoon Park and Briana Gaunt. Effects of an auditory model on the
learning of relative and absolute timing. Journal of motor behavior 33(2):127–138 2001.


Michael E Sobel. An introduction to causal inference. Sociological Methods & Research 24(3):353–379 1996.


25

Under review as submission to TMLR


Stephen M Stigler. Studies in the history of probability and statistics. xxxii: Laplace fisher and the discovery
of the concept of sufficiency. Biometrika 60(3):439–445 1973.


Richard S Sutton Andrew G Barto. Reinforcement learning: An introduction. MIT press 2018.


Giulio Tononi and Olaf Sporns. Measuring information integration. BMC neuroscience 4:1–20 2003.


Matej Vuković Stefan Thalmann. Causal discovery in manufacturing: A structured literature review. Journal
of Manufacturing and Materials Processing 6(1):10 2022.


Gurnee Wes Tegmark Max. Language models represent space and time 2023.


Christopher J Wood Robert W Spekkens. The lesson of causal discovery algorithms for quantum correlations:
Causal explanations of bell-inequality violations require fine-tuning. New Journal of Physics 17(3):033002
2015.


Jia Wu Weiru Zeng and Fei Yan. Hierarchical temporal memory method for time-series-based anomaly
detection. Neurocomputing 273:535–546 2018.


Gabriele Wulf Timothy D Lee and Richard A Schmidt. Reducing knowledge of results about relative versus
absolute timing: Differential effects on learning. Journal of motor behavior 26(4):362–369 1994.


Haoyan Xu Yida Huang Ziheng Duan Jie Feng and Pengyu Song. Multivariate time series forecasting
based on causal inference with transfer entropy and graph neural network. arXiv:2005.01185 2020.


Kun Zhang Aapo Hyvarinen. On the identifiability of the post-nonlinear causal model. arXiv preprint
arXiv:1205.2599 2012.


Xun Zheng Bryon Aragam Pradeep K Ravikumar and Eric P Xing. Dags with no tears: Continuous
optimization for structure learning. Advances in neural information processing systems 31 2018.


Xun Zheng Chen Dan Bryon Aragam Pradeep Ravikumar and Eric Xing. Learning sparse nonparametric
dags. In International Conference on Artificial Intelligence and Statistics pp. 3414–3425. PMLR 2020.


A Appendix: Complete Experimental Results in DAG Structure Exploration Test


26

Under review as submission to TMLR 


27


Table 4: 

The 

Complete Results of 

RIRL 

Exploration in 

the 

Latent Space. Each row 

stands for a

round of 

detection with ‘

#”

identifying the 

round 


number and all 

candidate edges are 

listed with their KLD gains as 

below. 1) 

Green cells: the 

newly detected edges. 2) 

Red cells: the 

selected edge. 


3) 

Blue cells: the 

trimmed edges 

accordingly. 


#1 
#2 
#3 
#4 
#5 
#6 
#7 
#8 
#9 


#10 
#11 
#12 
#13 
#14 
#15 
#16


A→C 


7.

6354
A→D 


19.7407 
A→D 


9.

7357
A→E 


60.1876 
A→E 


60.1876 
A→E 


60.1876 
A→E 


60.1876 
A→E 


60.1876 
A→E 


60.1876 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
C→I 


95.1564
C→I 


15.0222
C→I 


15.0222
A→D 


19.7407 
A→E 


60.1876 
A→E 


60.1876 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
B→E 


-

6.

8372
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
D→I 


75.5775
D→I 


3.

3845
D→I 


3.

3845
A→E 


60.1876 
A→F 


119.7730 
A→F 


119.7730 
B→E 


65.9335 
B→E 


65.9335 
B→E 


65.9335 
B→E 


65.9335 
B→E 


65.9335 
B→E 


65.9335 
B→F 


132.7717 
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
E→I 


110.2558 
I

→J 


0.

0284
A→F 


119.7730 
B→D 


8.

5147
B→E 


65.9335 
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
C→F 


111.2978 
C→I 


95.1564
C→I 


95.1564
C→I 


95.1564 
F→I 


45.6490
B→C 


8.

4753
B→E 


65.9335 
B→F 


132.7717 
C→E 


46.5876 
C→E 


46.5876 
C→E 


46.5876 
C→E 


46.5876 
C→E 


46.5876 
C→E 


46.5876
C→I 


95.1564 
D→F 


123.3203 
D→F 


123.3203 
D→F 


123.3203
B→D 


8.

5147
B→F 


132.7717 
C→D 


1.

1355
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
D→E 


17.0407
D→I 


75.5775
D→I 


75.5775
D→I 


75.5775
B→E 


65.9335 
C→D 


10.1490 
C→E 


46.5876 
C→G 


11.6012 
C→H 


39.2361 
C→H 


39.2361 
C→H 


39.2361
C→I 


95.1564
C→I 


95.1564 
D→F 


123.3203 
E→F 


53.6806 
E→F 


53.6806 
E→F 


53.6806
B→F 


132.7717 
C→E 


46.5876 
C→F 


111.2978 
C→H 


39.2361
C→I 


95.1564
C→I 


95.1564
C→I 


95.1564 
D→E 


63.7348 
D→E 


63.7348
D→I 


75.5775 
E→G 


-

5.

9191 
E→H 


-

3.

2931
E→I 


110.2558
C→F 


111.2978 
C→G 


11.6012
C→I 


95.1564 
D→E 


63.7348 
D→E 


63.7348 
D→E 


63.7348 
D→F 


123.3203 
D→F 


123.3203 
E→F 


53.6806 
E→H 


-

3.

2931
E→I 


110.2558
C→G 


11.6012 
C→H 


39.2361 
D→E 


63.7348 
D→F 


123.3203 
D→F 


123.3203 
D→F 


123.3203 
D→I 


75.5775
D→I 


75.5775 
E→G 


-

5.

9191
E→I 


110.2558
C→H 


39.2361
C→I 


95.1564 
D→F 


123.3203 
D→G 


2.

4540
D→H 


25.1988 
D→H 


25.1988
H→J 


0.

2092
E→H 


-

3.

2931
C→I 


95.1564 
D→E 


63.7348 
D→G 


27.8798 
D→H 


25.1988
D→I 


75.5775
D→I 


75.5775
E→I 


110.2558
D→F 


123.3203 
D→H 


25.1988
D→I 


75.5775 
G→J 


5.

2924
D→G 


27.8798
D→I 


75.5775 
G→J 


5.

2924
D→H 


25.1988
D→I 


75.5775

Under review as submission to TMLR 


28


Table 5: 

Average performance of 

10-Fold FGES (

Fast 

Greedy Equivalence Search) causal discovery with the prior 

knowledge that each node can only 


cause the 

other nodes with the same or 

greater depth with it. 

An edge means 

connecting two 

attributes from two 

different nodes 

respectively. Thus 


the 

number of 

possible edges between two 

nodes is 

the 

multiplication of 

the 

numbers of 

their 

attributes i.e.

the 

lengths of 

their data 

vectors. 


(

All 

experiments are 

performed with 6

different Independent-Test 

kernels including chi-

square-test d-

sep-test prob-test disc-bic-test fisher-z-

test 


mvplr-test. But their results turn out to be 

identical.) 


Cause Node
True
Causation
Number of 
Edges
Probability
of 

Missing
Wrong
Causation
Times 
of 

Wrongly
Discovered
A 
A→C 
16 
0.

038889
B 


B→D

B→E 


24 

16 


0.

125 0.

125
C→D 
6
0.

062 
C→F 


5.6


C 


C→E 
4 


0.

06875
C→G 
8 


0.

039286
D→G 
12 
0.

069048
D 
D→H 
12
0.2 


D→E 


1.2


D→I 


9 


0.

142857
D→F 


0.8


E→F 
8
0.3


E 
E→G 
8 


0.

003571
E→H 
8
0.2


F 
F→I 


12 
0.

142857
F→G 
5.0


G
G→J 


4
0.0 


G→H

G→I 


8.2

3.0


H 
H→J 


4 


0.

072727
H→I 


2.8


I 


I

→J 
3 


0.

030303 


Table 6: 

Brief Results of 

the 

Heuristic Causal Discovery in 

latent space identical with Table 3

in 

the 

paper body for 

better 

comparison to 

the 


traditional FGES methods results on 

this page. 


The edges are 

arranged in 

detected order (

from left to 

right) and their 

measured causal strengths in 

each step are 

shown below 

correspondingly. 


Causal strength is 

measured by 

KLD values (

less is 

stronger).

Each round of 

detection is 

pursuing the least KLD gain 

globally. All 

evaluations are 


in 4-

Fold 

validation average values. Different colors represent the 

ground truth 

causality strength tiers (

referred to 

the 

Figure 10 in 

the 

paper body). 


Causation A→C

B→D

C→D

C→G

D→G

G→J

D→H

H→J

C→E

B→E

E→G

E→H

E→F

F→I

I

→J

D→I 


KLD 7.63 

8.51 

10.14 

11.60 

27.87 

5.29 

25.19 

15.93 

46.58 

65.93 

39.13 

39.88 

53.68 

45.64 

17.41 

75.57 


Gain 7.63 

8.51 

1.

135 

11.60 

2.

454 5.29 

25.19 

0.

209 

46.58 

-

6.84 

-

5.91 

-

3.29 

53.68 

45.64 

0.

028 3.

384Image Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 382 x 109
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 382 x 109
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 366 x 58
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 366 x 58
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 356 x 58
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 356 x 58
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 27 x 26
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 27 x 26
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 27 x 26
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 273 x 84
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 273 x 84
Interlaced: Yes

## Message 4

All the files uploaded by the user have been fully loaded. Searching won't provide additional information.

## Message 5



Go through and evaluate/consolidate the provided alternatives and create a new *maximally optimized and enhanced sequence* based on previously provided directives/parameters:



---



#### `1`



    #### `src/templates/lvl1/md/0130-a-cleanup-codebase-entry-mapping.md`



    ```markdown

    [Codebase Entry Mapping] Establish the groundwork by scanning the codebase root. Identify all top-level directories, files, modules, and their functional domains. Categorize elements by purpose (e.g., data, logic, interface, utilities). This step creates the spatial-intellectual map of the system—crucial for all following stages. Execute as `{role=entry_mapper; input=codebase_root:path; process=[inventory_top_level_structure(), classify_by_domain_role(), detect_architectural_patterns_or_lack_thereof(), summarize_codebase_footprint()]; output={structure_map:dict, domain_roles:list, footprint_overview:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-logical-path-and-dependency-trace.md`



    ```markdown

    [Logical Path & Dependency Trace] Trace the primary control and data flow paths within the system. Analyze function calls, module imports, and cross-component interactions. Build a visual or logical map of how data and control propagate from entry points to final outputs. Execute as `{role=dependency_tracer; input={structure_map:dict, codebase_content:any}; process=[identify_entry_points_and_exports(), map_internal_dependency_graph(), trace_data_and_control_flow(), highlight_critical_pathways_and_intersections()]; output={logic_paths:dict, dependency_map:dict, complexity_nodes:list}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-clarity-compression-and-redundancy-audit.md`



    ```markdown

    [Clarity Compression & Redundancy Audit] Examine whether the system communicates itself *through its form alone*. Analyze identifier clarity, interface cohesion, and the presence of duplicative or dead logic. Identify areas where structural clarity is replaced by excessive commentary or redundant constructs. Execute as `{role=clarity_auditor; input={logic_paths:dict, dependency_map:dict, codebase_content:any}; process=[evaluate_identifier_expressiveness(), detect_logic_duplication_and_dead_code(), audit_commentary_necessity_vs_clarity(), assess_self-explanation_ratio_of_components()]; output={clarity_deficits:list, redundancy_report:list, expressiveness_score:float}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-commentary-distillation-sweep.md`



    ```markdown

    [Commentary Distillation Sweep] Conduct a surgical review of all comments. Retain only those that convey critical intent or unavoidable abstraction. Remove all that redundantly describe code behavior or patch over poor naming/design. If your code needs narration—it probably needs a rewrite. Execute as `{role=commentary_surgeon; input=codebase_content:dict; process=[gather_all_comments(), categorize_by_intent_level(), purge_non-essential_comments(), isolate_purposeful_explanations()], output={preserved_comments:list, removed_comments:list}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-simplification-target-synthesis.md`



    ```markdown

    [Simplification Target Synthesis] Merge insights from clarity audit, dependency trace, and comment sweep to isolate high-value simplification zones. Prioritize based on a matrix of clarity gain, coupling reduction, and architectural alignment. Execute as `{role=simplification_targeter; input={clarity_deficits:list, dependency_map:dict, removed_comments:list}; process=[cross-reference_complexity_and_clarity_gaps(), prioritize_by_impact_vs_risk(), isolate_refactoring_zones(modules, naming, logic), define_simplification_targets()]; output={simplification_targets:list, rationale_matrix:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-pruned-structure-proposal.md`



    ```markdown

    [Pruned Structure Proposal] Formulate an optimized structural proposal. This includes renaming files/folders for narrative clarity, re-grouping modules by conceptual proximity, and flattening or re-hierarchizing directories to better reflect logical flow and intention. Execute as `{role=structure_pruner; input={structure_map:dict, simplification_targets:list}; process=[map_existing_structure_to_intention(), propose_directory_refactor_plan(), define_module_regroupings(), optimize_file_naming_and_ordering()], output={structure_proposal:dict(new_structure:list, rationales:list)}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-cleanup-execution-blueprint.md`



    ```markdown

    [Cleanup Execution Blueprint] Generate a step-by-step, safely sequenced action plan based on all previous analyses. Each action should be low-risk, logically isolated, and yield immediate clarity or simplification. Annotate dependencies and prerequisites. Execute as `{role=cleanup_architect; input={simplification_targets:list, structure_proposal:dict}; process=[decompose_refactor_tasks(), order_by_safety_and_impact(), annotate_side_effect_zones(), construct_sequenced_cleanup_steps()], output={refactor_plan:list_of_tasks}}`

    ```



    #### `src/templates/lvl1/md/0130-h-cleanup-refactor-pass-and-integrity-check.md`



    ```markdown

    [Refactor Pass & Integrity Check] Execute the `refactor_plan`. Validate that all changes preserve original functionality. Re-run workflows, unit tests, or manual inspection routines. Ensure naming, structure, and simplicity align with stated principles. Execute as `{role=refactor_executor; input={codebase_root:path, refactor_plan:list}; process=[apply_structural_and_code_changes(), verify_functional_equivalence(), run_test_suite_or_manual_flow_validation(), conduct_aesthetic_consistency_review()], output={refactored_codebase:path, integrity_passed:bool}}`

    ```



    #### `src/templates/lvl1/md/0130-i-cleanup-final-review-and-future-cycle-scope.md`



    ```markdown

    [Final Review & Future Cycle Scope] Critically review the final result. Document key simplifications, note remaining complexity zones, and evaluate whether a further cleanup cycle is warranted. Recommend next logical refinement scope, if needed. Execute as `{role=cleanup_reviewer; input={refactored_codebase:path, all_prior_outputs:dict}; process=[summarize_improvements(), assess_principle_alignment(), highlight_residual_complexity(), propose_scope_for_next_iteration()], output={review_summary:str, next_cycle_recommendation:dict}}`

    ```



---



#### `2`



    #### `src/templates/lvl1/md/0130-a-cleanup-structure-purpose-orientation-scan.md`



    ```markdown

    [Structure & Purpose Orientation Scan] Begin by scanning the full codebase structure to map its high-level architecture. Inventory core files, directories, and components. Identify entry points, output flows, and the system’s core intent. This baseline understanding will guide all future cleanup steps. Execute as `{role=structure_orienter; input=codebase_root:path; process=[map_directory_structure(), identify_entry_exit_points(), inventory_key_modules_classes_files(), distill_system_purpose_from_structure()]; output={structure_map:dict, core_purpose:str}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-workflow-and-flow-path-mapping.md`



    ```markdown

    [Workflow and Flow Path Mapping] Identify and trace essential workflows aligned with the `core_purpose`. Analyze how logic and data propagate through the system. Visualize key interactions, complexity hotspots, and dependency clusters that influence the execution logic. Execute as `{role=workflow_mapper; input={structure_map:dict, core_purpose:str}; process=[trace_primary_control_flows(), track_data_transformations(), detect_coupling_between_modules(), highlight_logic_clusters_and_hotspots()]; output={workflow_map:list[dict], complexity_zones:list}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-inherent-readability-and-self-explanation-audit.md`



    ```markdown

    [Inherent Readability & Self-Explanation Audit] Evaluate codebase clarity using identifier naming quality, structural simplicity, and logic transparency. Identify sections reliant on verbose comments or unclear flow that could be inherently clarified through better design. Execute as `{role=readability_auditor; input={workflow_map:list, complexity_zones:list}; process=[assess_naming_quality(), evaluate_logical_transparency(), detect_comment-dependent_clarity(), highlight_non-self-explanatory_areas()]; output={readability_report:dict(naming_issues:list, flow_issues:list, comment_reliance:list)}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-structural-and-functional-simplification-scan.md`



    ```markdown

    [Structural & Functional Simplification Scan] Identify all opportunities for removing redundancy, simplifying logic, enhancing cohesion, and minimizing inter-module dependencies. Target areas where structure and logic can be made more elegant without changing functionality. Execute as `{role=simplification_finder; input={structure_map:dict, readability_report:dict}; process=[identify_redundant_patterns(), locate_fragmented_or_non-cohesive_components(), detect_tightly_coupled_dependencies(), suggest_logic_simplification_targets()]; output={simplification_targets:dict(redundancy:list, cohesion:list, coupling:list, logic:list)}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-essential-comment-filter-and-minimizer.md`



    ```markdown

    [Essential Comment Filter & Minimizer] Apply a minimalist commentary policy. Audit all comments and retain only those that provide critical architectural or intent-related context. Remove or rewrite comments that merely describe 'what' or duplicate what’s already clear from code. Execute as `{role=comment_minimizer; input=codebase_root:path; process=[extract_all_comments(), classify_comments_by_necessity(), flag_redundant_and_excessive_comments(), preserve_or_refactor_essential_comments_only()]; output={comment_cleanup:list[dict(location:str, action:str)]}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-prioritized-cleanup-plan-synthesis.md`



    ```markdown

    [Prioritized Cleanup Plan Synthesis] Synthesize all outputs into a clear, incremental action plan. Prioritize simplifications with high impact and low risk. Include redundant code removals, structure refactors, naming improvements, and comment reductions. Ensure changes are safe, traceable, and purpose-aligned. Execute as `{role=cleanup_planner; input={simplification_targets:dict, comment_cleanup:list, readability_report:dict}; process=[merge_cleanup_opportunities(), prioritize_by_safety_and_clarity_gain(), translate_into_concrete_tasks(), produce_sequential_cleanup_plan()]; output={cleanup_plan:list[dict(task:str, priority:int)]}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-guided-structure-and-logic-refinement.md`



    ```markdown

    [Guided Structure & Logic Refinement] Execute the `cleanup_plan`. Simplify logic, restructure components, improve naming, and decouple dependencies. Ensure all transformations enhance clarity and preserve functionality. Apply changes iteratively and track impact. Execute as `{role=cleanup_executor; input={codebase_root:path, cleanup_plan:list}; process=[apply_structural_simplifications(), refactor_for_cohesion_and_decoupling(), implement_naming_clarity_improvements(), validate_functional_preservation_post-change()]; output={cleaned_codebase:path}}`

    ```



    #### `src/templates/lvl1/md/0130-h-cleanup-final-integrity-review-and-elegance-check.md`



    ```markdown

    [Final Integrity Review & Elegance Check] Perform a thorough audit of the cleaned codebase. Confirm it still fulfills the `core_purpose`. Check for broken logic, redundant artifacts, or missed opportunities for refinement. Ensure the structure is as elegant, minimal, and self-explanatory as possible. Execute as `{role=final_reviewer; input={cleaned_codebase:path, core_purpose:str}; process=[verify_functional_equivalence(), re-check structural readability(), ensure naming and flow elegance, confirm removal of obsolete elements], output={final_report:dict(equivalence_verified:bool, missed_opportunities:list, elegance_score:float)}}`

    ```



---



#### `3`



    #### `src/sequences/lvl1/md/0130-a-cleanup-holistic-structure-purpose-scan.md`



    ```markdown

    [Holistic Structure & Purpose Scan] Perform an immediate structural and purpose-based reconnaissance. Capture a bird's-eye view of the codebase: what it does, how it's laid out, and where core logic and dependencies reside. Focus on identifying the architectural *intent* through structure and traceable functionality. Execute as: `{role=holistic_scanner; input=codebase_root:any; process=[map_structure_topology(), inventory_key_modules_and_paths(), detect_main_entry_exit_points(), summarize_purpose_from_code_behaviors()]; output={structure_map:dict, purpose_summary:str}}`

    ```



    #### `src/sequences/lvl1/md/0130-b-cleanup-critical-path-clarity-trace.md`



    ```markdown

    [Critical Path & Clarity Trace] Identify and trace the core logical workflows that fulfill the codebase's primary purpose. Evaluate their linearity, cohesion, and reliance on indirect abstractions. Clarify where cognitive load is elevated due to non-transparent naming or structural fragmentation. Execute as: `{role=logic_tracer; input={structure_map:dict, purpose_summary:str}; process=[trace_main_execution_paths(), map_data_and_control_flow(), highlight_fragmented_or_confusing_links(), annotate clarity_breakpoints()]; output={critical_paths:list, clarity_trace:dict}}`

    ```



    #### `src/sequences/lvl1/md/0130-c-cleanup-redundancy-and-responsibility-audit.md`



    ```markdown

    [Redundancy & Responsibility Audit] Scan the codebase for repeated patterns, over-modularization, dead logic, and ambiguous responsibilities. Evaluate module cohesion, detect single responsibility violations, and identify where simplification could replace over-commented complexity. Execute as: `{role=cohesion_auditor; input=codebase_root:any; process=[scan_for_duplicate_code_fragments(), evaluate_functional_cohesion_per_module(), isolate_deprecated_or_unused_components(), detect_srp_violations_and_comment-dependence()]; output={redundancy_map:list, srp_findings:list, dead_code:list, overcomment_zones:list}}`

    ```



    #### `src/sequences/lvl1/md/0130-d-cleanup-structural-simplification-strategy.md`



    ```markdown

    [Structural Simplification Strategy] Formulate a minimal, high-impact plan for directory and file reorganization. Reorder the project structure to reflect logical workflows, reduce mental jumps between files, and group components by relation and flow rather than abstract type. Execute as: `{role=simplification_strategist; input={structure_map:dict, clarity_trace:dict, srp_findings:list}; process=[propose_sequential_logical_structure(), regroup_files_by_flow_and_relation(), remove_empty_or_fragile_directories(), define_clear_boundaries_between_layers()], output={structure_plan:list}}`

    ```



    #### `src/sequences/lvl1/md/0130-e-cleanup-naming-clarity-refinement-pass.md`



    ```markdown

    [Naming Clarity Refinement Pass] Redesign all identifiers—variables, functions, classes, modules—to maximize self-documenting clarity. Ensure names reflect both their functional role and position in the logic flow, removing the need for explanatory comments. Execute as: `{role=naming_refiner; input=codebase_root:any; process=[scan_identifiers_for_purpose_fit(), rename_for_flow-awareness(), unify naming_conventions_across_files(), flag ambiguous_or_generic_names()]; output={renamed_identifiers:list, naming_issues:list}}`

    ```



    #### `src/sequences/lvl1/md/0130-f-cleanup-essential-comment-retention-filter.md`



    ```markdown

    [Essential Comment Retention Filter] Apply a strict filter to remove redundant or obsolete comments. Retain only those that clarify ‘why’ something is non-obvious or define complex constraints. All ‘what’ and ‘how’ comments should be rendered obsolete by naming and structure. Execute as: `{role=comment_filter; input=codebase_root:any; process=[inventory_comments(), categorize_by_purpose(why_what_how), remove_non_essential_comments(), rewrite_retained_ones_to_maximum_brevity()]; output={comment_cleanup_map:list}}`

    ```



    #### `src/sequences/lvl1/md/0130-g-cleanup-functional-integrity-regression-check.md`



    ```markdown

    [Functional Integrity & Regression Check] Run a full verification cycle to ensure that the refactored codebase preserves all original functionality. Use regression tests or diff-based behavioral verification to detect unexpected side effects. Execute as: `{role=regression_checker; input={original_codebase:any, refactored_codebase:any}; process=[run_all_existing_tests(), detect_behavioral_drift_via_input_output_diff(), confirm_entry_point_consistency(), log_regression_findings()]; output={integrity_report:dict(functional_equivalence:bool, issues:list)}}`

    ```



    #### `src/sequences/lvl1/md/0130-h-cleanup-recursive-refinement-checkpoint.md`



    ```markdown

    [Recursive Refinement Checkpoint] Analyze the current state of the cleaned codebase. If principle-aligned, conclude. If further simplification, decoupling, or clarity gains are possible at manageable cost, define scoped targets for another cycle. Execute as: `{role=refinement_reviewer; input={refactored_codebase:any, integrity_report:dict}; process=[identify_remaining_opportunities(), assess complexity_vs_impact_ratio(), determine_scope_for_additional_pass(), issue_next_steps_or_finalize()]; output={refinement_decision:dict(proceed:bool, next_focus:list)}}`

    ```



---



#### `4`



    #### `src/templates/lvl1/md/0130-a-cleanup-codebase-snapshot-intent-discovery.md`



    ```markdown

    [Codebase Snapshot & Intent Discovery] Capture a high-level snapshot of the project’s structure and discern the system’s core purpose. Identify primary domains, entry points, and architectural boundaries to understand what the system is *really* trying to do. Execute as `{role=intent_scanner; input=codebase_root:path; process=[map_high_level_directory_structure(), detect_primary_entry_points(), extract_readme_or_docs_summary(), infer_primary_purpose(), sketch_logical_domains()]; output={project_overview:dict, core_purpose:str}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-logic-flow-and-dependency-mapping.md`



    ```markdown

    [Logic Flow & Dependency Mapping] Trace how execution, data, and control flows through the system to fulfill the `core_purpose`. Map critical dependencies and architectural pressure points. Execute as `{role=flow_mapper; input={project_overview:dict, core_purpose:str}; process=[identify_primary_logic_paths(), trace_inter-module_dependencies(), visualize_data_flow_and_transformations(), detect_circular_or_excessive_dependencies(), highlight complexity_hotspots]; output={workflow_map:dict, dependency_graph:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-clarity-cohesion-and-comment-dependence-audit.md`



    ```markdown

    [Clarity, Cohesion & Comment Dependence Audit] Evaluate how well the structure and code *explain themselves*. Score cohesion across modules, assess naming clarity, and flag regions where comments compensate for unclear code. Execute as `{role=clarity_auditor; input={workflow_map:dict, codebase_content:dict}; process=[score_identifier_naming_clarity(), detect_low_cohesion_modules(), locate_over-commented_sections(), classify comment intent (why vs what), summarize where clarity is structure-dependent vs comment-dependent]; output={clarity_diagnostics:dict, comment_analysis:list}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-structural-simplification-opportunity-scan.md`



    ```markdown

    [Structural Simplification Opportunity Scan] Identify sections of the project where file/module reorganization could improve understandability. Recommend realignments to better reflect logical flows and system responsibilities. Execute as `{role=structure_refactor_scanner; input={workflow_map:dict, clarity_diagnostics:dict}; process=[detect_misplaced_or_crosscutting_concerns(), flag over-fragmented or bloated modules(), suggest structure_by_function_or_flow(), identify poor folder/module naming]; output={restructure_recommendations:list}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-code-simplification-and-dead-logic-detection.md`



    ```markdown

    [Code Simplification & Dead Logic Detection] Review implementation-level logic for complexity hotspots. Identify functions or patterns that are redundant, over-complicated, dead, or can be simplified while preserving behavior. Execute as `{role=logic_optimizer; input=codebase_content:dict; process=[detect_dead_or_unreachable_code(), locate redundant or duplicated logic(), find unnecessary abstractions(), suggest concise equivalents(), enforce single-responsibility principles]; output={code_simplification_targets:list}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-cleanup-action-plan-synthesis.md`



    ```markdown

    [Cleanup Action Plan Synthesis] Synthesize a minimalist, safe-to-apply action plan from findings across structural, logic, and clarity audits. Order actions by simplicity, clarity gain, and implementation safety. Execute as `{role=cleanup_planner; input={restructure_recommendations:list, code_simplification_targets:list, comment_analysis:list}; process=[aggregate_all_cleanup_targets(), rank_by_cost_vs_clarity_gain(), group_by safety_and_scope(), generate_atomic_cleanup_steps(), document all decisions]; output={prioritized_cleanup_plan:list}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-clarity-driven-structure-implementation.md`



    ```markdown

    [Clarity-Driven Structure Implementation] Execute cleanup actions that involve reorganization of files, modules, and naming — reorienting structure around *how the system works*. Ensure all changes are internally consistent and reflect simplified logic paths. Execute as `{role=structure_cleaner; input={prioritized_cleanup_plan:list, codebase_root:path}; process=[restructure_directories_and_modules(), consolidate co-located logic(), rename folders/files for clarity and flow, adjust imports and references]; output={refactored_structure:path}}`

    ```



    #### `src/templates/lvl1/md/0130-h-cleanup-logic-refinement-and-comment-minimization.md`



    ```markdown

    [Logic Refinement & Comment Minimization] Apply logic simplifications and reduce unnecessary comments. Code should now largely explain itself — preserve only comments that express *why*, not *what/how*. Execute as `{role=logic_refiner; input={refactored_structure:path, prioritized_cleanup_plan:list}; process=[apply code simplifications(), enforce concise naming(), remove obvious/redundant comments(), retain minimal essential commentary]; output={clarified_codebase:path}}`

    ```



    #### `src/templates/lvl1/md/0130-i-cleanup-final-validation-and-elegance-verification.md`



    ```markdown

    [Final Validation & Elegance Verification] Confirm that the simplified codebase preserves all original behavior. Reassess final structure for self-explanation, cohesion, and clarity. Evaluate remaining complexity zones, and determine whether another refinement pass is warranted. Execute as `{role=elegance_validator; input={clarified_codebase:path, original_snapshot:any}; process=[run functional regression checks(), audit structure for principle alignment(), score self-explanation, assess need for recursion]; output={finalized_codebase:path, elegance_report:dict(score:float, remaining_opportunities:list, recommend_next_pass:bool)}}`

    ```



---



#### `5`



    #### `src/templates/lvl1/md/0130-a-cleanup-holistic-structure-purpose-scan.md`



    ```markdown

    [Holistic Structure & Purpose Scan] Your primary objective is a swift, big-picture analysis of the codebase: inventory the major components, outline their purpose, and note key dependencies. This sets a baseline for understanding what the system does and how it’s organized. Execute as `{role=holistic_scanner; input=codebase_root:any; process=[inventory_major_components(), identify_primary_use_cases(), outline_top_level_dependencies(), articulate_overall_project_mission()]; output={structure_overview:dict, project_mission:str}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-key-workflow-complexity-trace.md`



    ```markdown

    [Key Workflow & Complexity Trace] Focus on tracing the system’s primary workflows relevant to the `project_mission`. Map the control paths, data transformations, and cross-module interactions that define these workflows. Identify complexity hotspots where logic or structure is convoluted. Execute as `{role=workflow_tracer; input={structure_overview:dict, project_mission:str, codebase_content:dict}; process=[extract_primary_workflows(), trace_control_and_data_flow(), locate_logic_complexity_hotspots(), summarize_cross_module_interactions()]; output={workflow_maps:list[dict], complexity_zones:list}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-clarity-and-cohesion-audit.md`



    ```markdown

    [Clarity & Cohesion Audit] Assess the codebase for readability and organizational strength. Evaluate naming quality, logical flow, adherence to Single Responsibility Principle, and cohesion within modules/classes, paying special attention to places flagged by `complexity_zones`. Execute as `{role=clarity_auditor; input={codebase_content:dict, workflow_maps:list, complexity_zones:list}; process=[examine_identifier_naming(), measure_cohesion_per_module_class(), check_single_responsibility_adherence(), document_comment_reliance_for_explanation()]; output={clarity_report:dict, cohesion_findings:list, srp_violations:list, comment_reliance:list}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-focused-simplification-structural-realignment.md`



    ```markdown

    [Focused Simplification & Structural Realignment] Use the insights from the `clarity_report` and `cohesion_findings` to propose minimal yet high-impact restructuring and refactoring. Target removing obvious redundancies, consolidating related functionality, and reducing external dependencies. Execute as `{role=structure_refiner; input={clarity_report:dict, cohesion_findings:list, srp_violations:list, codebase_content:dict}; process=[identify_redundant_or_overlapping_functionality(), plan_cohesive_module_realignment(), refine_dependency_boundaries(), propose_naming_improvements()]; output={simplification_plan:dict, proposed_structure_changes:list}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-essential-commentary-refinement.md`



    ```markdown

    [Essential Commentary Refinement] Apply a “minimal, essential-only” policy to all comments. Retain only comments that clarify intent or justify complex decisions. Remove or reduce redundant, outdated, or purely descriptive commentary that can be explained by improved naming/structure. Execute as `{role=comment_curator; input={codebase_content:dict}; process=[gather_all_comments(), filter_non_essential_comments(), preserve_comments_explaining_complex_design_intent(), create_update_list_for_comment_refinements()]; output={comment_cleanup_list:list, refined_comment_guidelines:str}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-prioritized-cleanup-action-plan.md`



    ```markdown

    [Prioritized Cleanup Action Plan] Integrate the findings from the `simplification_plan` and `comment_cleanup_list` into a cohesive roadmap. Assign priorities based on impact and safety. Outline actionable steps to restructure modules, refactor code, rename identifiers, and adjust comments. Execute as `{role=cleanup_planner; input={simplification_plan:dict, proposed_structure_changes:list, comment_cleanup_list:list, refined_comment_guidelines:str}; process=[merge_restructuring_and_comment_refinements(), rank_changes_by_safety_and_benefit(), draft_detailed_refactoring_tasks(), finalize_prioritized_plan()]; output={actionable_cleanup_plan:str}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-validation-and-refinement-check.md`



    ```markdown

    [Validation & Refinement Check] Validate that the executed refactoring plan preserves original functionality and meets the principles of clarity, maintainability, and minimalism. Evaluate if additional cleanup cycles are warranted or if the codebase has reached a sufficiently optimal state. Execute as `{role=validator; input={original_codebase:any, updated_codebase:any, actionable_cleanup_plan:str}; process=[verify_functional_equivalence(), review_code_against_core_principles(), gauge_remaining_complexity(), determine_need_for_additional_iteration()]; output={validation_report:dict, next_steps_recommendation:str}}`

    ```



---



#### `6`



    #### `src/templates/cleanup/md/0130-a-cleanup-baseline-scan-structure-purpose.md`



    ```markdown

    [Baseline Scan: Structure & Purpose] Your primary directive is to establish a foundational understanding. Analyze the input codebase structure, inventory its main components (modules, directories, key files), map high-level interactions/dependencies, and determine the core purpose or function of the system. Execute as `{role=baseline_scanner; input=codebase_root:path; process=[inventory_major_components(), map_high_level_dependencies(), identify_entry_points_and_key_workflows(), distill_core_system_purpose()]; output={structure_summary:dict, core_purpose:str, key_workflows_identified:list}}`

    ```



    #### `src/templates/cleanup/md/0130-b-cleanup-clarity-audit-self-explanation.md`



    ```markdown

    [Clarity Audit: Readability & Self-Explanation] Evaluate the codebase's inherent clarity against the principles of simplicity and self-explanation. Assess the quality of naming conventions (variables, functions, classes, files), the logical simplicity of the identified `key_workflows`, and the degree to which the structure itself communicates intent. Identify areas overly reliant on comments for basic understanding. Execute as `{role=clarity_auditor; input={codebase_content:dict, structure_summary:dict, key_workflows_identified:list}; process=[assess_naming_convention_clarity_and_consistency(), evaluate_workflow_logical_simplicity(), determine_structural_self_explanation_level(), identify_comment_dependency_hotspots()]; output={clarity_assessment:dict(naming_quality:str, flow_complexity:str, structural_clarity:str, comment_reliance:list)}}`

    ```



    #### `src/templates/cleanup/md/0130-c-cleanup-cohesion-responsibility-check.md`



    ```markdown

    [Cohesion & Responsibility Check] Analyze component design based on the Single Responsibility Principle (SRP) and cohesion. Evaluate if modules/classes/functions have a single, well-defined purpose. Assess if related functionalities are grouped logically (high cohesion) and unrelated components have minimal dependencies (low coupling). Execute as `{role=cohesion_analyzer; input={codebase_content:dict, structure_summary:dict}; process=[analyze_component_responsibilities_vs_srp(), assess_module_class_functional_cohesion(), identify_high_coupling_points(), evaluate_separation_of_concerns()]; output={cohesion_report:dict(srp_violations:list, cohesion_issues:list, coupling_concerns:list)}}`

    ```



    #### `src/templates/cleanup/md/0130-d-cleanup-cleanup-opportunity-identification.md`



    ```markdown

    [Cleanup Opportunity Identification] Synthesize findings from the previous steps (`clarity_assessment`, `cohesion_report`) to pinpoint specific opportunities for cleanup and simplification. Identify complex logic sections, code redundancies (duplication, dead code), areas violating SRP or cohesion principles, poorly named identifiers, and structural elements that obscure purpose. Execute as `{role=opportunity_detector; input={codebase_content:dict, clarity_assessment:dict, cohesion_report:dict}; process=[detect_logic_complexity_hotspots(), find_redundant_or_dead_code(), list_srp_cohesion_refactoring_targets(), identify_naming_improvement_candidates(), pinpoint_structural_refinement_areas()]; output={cleanup_opportunities:dict(complexity:list, redundancy:list, responsibility:list, naming:list, structure:list)}}`

    ```



    #### `src/templates/cleanup/md/0130-e-cleanup-prioritized-action-plan-generation.md`



    ```markdown

    [Prioritized Action Plan Generation] Generate a concrete, prioritized list of cleanup actions based on the identified `cleanup_opportunities`. Rank actions focusing first on safety and high impact: prioritize removal of dead/redundant code, followed by clarity improvements (naming, comment reduction where code is self-explanatory), then structural adjustments (cohesion, SRP) and finally simplification of complex logic. *Output only the plan.* Execute as `{role=cleanup_planner; input={cleanup_opportunities:dict}; process=[consolidate_all_opportunities(), rank_actions_by_safety_and_impact(removals > clarity > structure > complexity), define_specific_actionable_tasks(), sequence_tasks_logically()]; output={prioritized_cleanup_plan:list_of_tasks}}`

    ```



    #### `src/templates/cleanup/md/0130-f-cleanup-execute-incremental-cleanup-validate.md`



    ```markdown

    [Execute Incremental Cleanup & Validate] Execute the highest-priority, safest tasks from the `prioritized_cleanup_plan`. Apply changes incrementally. After each significant change or batch of changes, perform validation checks (e.g., running tests, static analysis) to ensure functionality remains equivalent and no regressions were introduced. Focus on executing removals and clarity improvements first. Execute as `{role=cleanup_executor_validator; input={codebase_root:path, prioritized_cleanup_plan:list}; process=[execute_highest_priority_safe_tasks(limit_scope_per_run), run_validation_suite(), verify_functional_equivalence_post_change(), update_plan_with_completed_tasks()]; output={partially_cleaned_codebase:path, execution_validation_log:list, remaining_cleanup_plan:list}}`

    ```



    #### `src/templates/cleanup/md/0130-g-cleanup-assess-refinement-iteration.md`



    ```markdown

    [Assess & Refine Iteration] Review the `execution_validation_log` and the state of the `partially_cleaned_codebase`. Assess the impact of the changes made against the core principles (Simplicity, Clarity, Cohesion, etc.). Evaluate the `remaining_cleanup_plan` and determine if executing further, potentially more complex, cleanup tasks offers sufficient value compared to the risk/effort. Decide whether to initiate another cleanup cycle (potentially starting from step 4 or 6 with refined scope) or conclude the process. Execute as `{role=refinement_assessor; input={partially_cleaned_codebase:path, execution_validation_log:list, remaining_cleanup_plan:list}; process=[evaluate_impact_of_completed_tasks(), analyze_cost_benefit_of_remaining_tasks(), check_adherence_to_core_principles(), decide_on_next_iteration_necessity_and_scope()]; output={iteration_decision:dict(proceed_further:bool, next_focus_tasks:list, assessment_summary:str)}}`

    ```



---



#### `7`



    #### `src/templates/lvl1/md/0130-a-cleanup-baseline-principle-assessment-structure-mapping.md`



    ```markdown

    [Baseline Principle Assessment & Structure Mapping] Your initial task is foundational understanding aligned with core principles. Analyze the input codebase/project structure: Map key directories, files, and primary components. Identify high-level dependencies and entry points. Perform an initial assessment against core principles (Simplicity, Clarity, Cohesion, SRP, Self-Explanation, Minimalism). Distill the system's primary purpose. Execute as `{role=baseline_assessor; input=codebase_root:path; process=[map_structure_and_major_components(), identify_high_level_dependencies_and_entry_points(), analyze_primary_purpose_or_workflow(), perform_initial_scan_for_principle_adherence_or_violations()]; output={baseline_report:dict(structure_map:any, primary_purpose:str, initial_principle_assessment:dict)}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-complexity-clarity-cohesion-hotspot-identification.md`



    ```markdown

    [Complexity, Clarity & Cohesion Hotspot Identification] Building on the baseline, dive deeper to pinpoint specific areas hindering elegance and maintainability. Trace core logic flows related to the `primary_purpose`. Critically evaluate naming clarity, logical complexity, component cohesion (vs. desired SRP), structural awkwardness, and comment necessity/redundancy. Identify specific anti-patterns and violations of core principles. Execute as `{role=hotspot_identifier; input={baseline_report:dict, codebase_content:dict}; process=[trace_core_logic_paths(), evaluate_naming_and_readability_effectiveness(), assess_component_cohesion_and_responsibility_alignment(), analyze_comment_usage_against_necessity_and_clarity(), pinpoint_complexity_hotspots_and_principle_violations()]; output={hotspot_analysis:dict(complexity_zones:list, clarity_issues:list, cohesion_violations:list, comment_concerns:list, structural_concerns:list)}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-prioritized-refactoring-simplification-blueprint.md`



    ```markdown

    [Prioritized Refactoring & Simplification Blueprint] Synthesize the findings from `hotspot_analysis` into a strategic, actionable cleanup plan. Prioritize refactoring opportunities based on their potential impact on improving clarity, simplicity, and maintainability, balanced against implementation risk/effort. Define specific, targeted actions for structural reorganization, code simplification (logic, naming), cohesion improvement, and comment rationalization (removal/refinement). Execute as `{role=refactoring_strategist; input=hotspot_analysis:dict; process=[consolidate_identified_issues(), rank_refactoring_targets_by_impact_and_safety(), define_specific_structural_refinement_steps(), specify_code_level_simplification_and_clarification_actions(), detail_comment_rationalization_plan(), sequence_actions_into_prioritized_blueprint()]; output={cleanup_blueprint:list[dict(action:str, target:any, priority:int, rationale:str)]}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-targeted-structural-realignment-cohesion-enhancement.md`



    ```markdown

    [Targeted Structural Realignment & Cohesion Enhancement] Execute the *structural* refactoring tasks defined in the `cleanup_blueprint`. Reorganize directories and files to inherently reflect the system's logical flow and conceptual grouping. Consolidate highly cohesive components and increase decoupling between unrelated parts. Apply improved structural naming conventions. Ensure all internal references and imports are updated accordingly. Focus solely on the structural changes mandated by the blueprint. Execute as `{role=structure_refactorer; input={codebase_root:path, cleanup_blueprint:list}; process=[implement_prioritized_directory_file_restructuring(), apply_cohesive_component_grouping(), enforce_decoupling_where_specified(), update_structural_naming_conventions(), resolve_resulting_import_reference_changes()]; output={realigned_structure_path:path}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-code-clarity-enhancement-essential-comment-rationalization.md`



    ```markdown

    [Code Clarity Enhancement & Essential Comment Rationalization] Execute the *code-level* refactoring and cleanup tasks within the `realigned_structure_path`, guided strictly by the `cleanup_blueprint`. Apply self-explanatory naming conventions. Simplify complex algorithms and logic flows. Enforce the Single Responsibility Principle at the function/class level. Remove dead or redundant code. Rationalize comments: eliminate those made redundant by clarity improvements, retain/refine only essential 'why' comments. Execute as `{role=code_clarifier_rationalizer; input={realigned_structure_path:path, cleanup_blueprint:list}; process=[apply_prioritized_naming_improvements(), implement_logic_simplification_and_consolidation(), refactor_for_single_responsibility_compliance(), execute_comment_removal_and_refinement_plan(), purge_identified_dead_or_redundant_code()]; output={clarified_codebase_path:path}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-post-refactoring-validation-refinement-assessment.md`



    ```markdown

    [Post-Refactoring Validation & Refinement Assessment] Perform a final validation and assessment of the `clarified_codebase_path`. Verify functional equivalence against the original state (or defined test suite). Critically evaluate the outcome against the core principles (Simplicity, Clarity, Cohesion, SRP, Self-Explanation) and the goals outlined in the `cleanup_blueprint`. Identify any remaining significant deviations or areas warranting further refinement. Determine if another cleanup cycle is justified. Execute as `{role=cleanup_validator_assessor; input={original_codebase_state:any, clarified_codebase_path:path, cleanup_blueprint:list}; process=[perform_functional_equivalence_validation(), audit_final_state_against_core_principles_and_blueprint_goals(), identify_remaining_complexity_or_clarity_deficits(), assess_cost_benefit_of_further_refinement(), generate_validation_report_and_next_cycle_recommendation()]; output={validation_outcome:dict(functional_equivalence:bool, principle_adherence_score:float, remaining_concerns:list, next_cycle_recommended:bool, focus_areas:list)}}`

    ```



---



#### `8`



    #### `src/templates/lvl1/md/0130-a-cleanup-structural-immersion-overview.md`



    ```markdown

    [Structural Immersion & Overview] Immerse into the codebase to rapidly map its high-level structure, modules, file hierarchy, and relationship patterns. Assess immediate alignment with simplicity, clarity, and cohesion principles. Establish a baseline of current organization, highlighting major design deviations or anti-patterns. Execute as `{role=structural_overviewer; input=codebase_root:any; process=[scan_file_and_directory_structure(), identify_module_relationships(), map_high_level_dependencies(), baseline_against_core_principles(), note_major_structural_flaws_or_strengths()]; output={structure_overview:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-intrinsic-clarity-evaluation.md`



    ```markdown

    [Intrinsic Clarity Evaluation] Critically assess the inherent readability and logic of structure, naming, and workflows—without relying on commentary. Identify areas where complexity, ambiguous naming, convoluted flow, or unclear logic cloud self-explanation, violating the principles of transparent, single-responsibility-driven design. Execute as `{role=clarity_evaluator; input=structure_overview:dict; process=[analyze_naming_scheme_quality(), assess_logical_flow_coherence(), detect_multi_responsibility_components(), flag_comment_reliant_clarity_zones(), summarize_core_clarity_breakdowns()]; output={clarity_issues:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-cohesion-simplification-opportunity-map.md`



    ```markdown

    [Cohesion & Simplification Opportunity Map] Systematically scan for candidates to improve cohesion and simplicity across the codebase: highlight redundant, weakly grouped, or cross-dependent components, fragmented logic, dead code, or modules best unified. Pinpoint where architectural or naming refinements can enable self-explanation and intuitive navigation. Execute as `{role=simplification_mapper; input={structure_overview:dict, clarity_issues:dict}; process=[detect_redundant_and_fragile_structures(), reveal_modularization_inefficiencies(), map_code_and_comment_redundancy(), locate_navigation_and_discoverability_issues(), compile_simplification_opportunities()]; output={opportunity_map:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-prioritized-cleanup-action-plan.md`



    ```markdown

    [Prioritized Cleanup Action Plan] Integrate all findings to construct a concise, high-impact action plan for codebase cleanup. Prioritize actions that maximize clarity and structure with minimal disruption: dead code removal, structure/naming refinement, logic consolidation, and comment minimization—each step mapped to specific opportunities identified. Execute as `{role=cleanup_planner; input={opportunity_map:dict}; process=[consolidate_and_rank_opportunities_by_impact(), balance_risk_and_effort(), sequence_actions_for_logical_dependency(), filter_for_high_clarity_and_cohesion_gains(), finalize_actionable_cleanup_plan()]; output={cleanup_plan:list}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-structural-clarity-realignment-execution.md`



    ```markdown

    [Structural & Clarity Realignment Execution] Methodically execute the prioritized actions: refactor file/directory structure, improve naming, simplify code segments, and minimize commentary to only the essential. Each change must strictly reinforce transparency, single responsibility, and intuitive comprehension, rendering the structure self-explanatory. Execute as `{role=realignment_executor; input={cleanup_plan:list, codebase_root:any}; process=[perform_structural_changes(), refactor_names_and_borders(), simplify_logic_flows(), remove_dead_and_redundant_code(), reduce_to_essential_comments(), ensure_post_change_cohesion_and_navigation()]; output={cleaned_codebase:any}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-integrity-and-principle-validation.md`



    ```markdown

    [Integrity & Principle Validation] Verify functional equivalence and comprehensive alignment with the core principles. Audit for clarity, minimization of commentary, self-explanatory structure, and maintainability. Identify any residual complexity, naming issues, or navigation hurdles—recommending further refinement only where clear principle violations remain. Execute as `{role=integrity_validator; input={cleaned_codebase:any, structure_overview:dict}; process=[validate_functional_equivalence(), audit_against_principles(), check_clarity_and_navigation_post_cleanup(), document_remaining_issues(), recommend_or_conclude_additional_refinement()]; output={validation_report:dict, final_state:any}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-cleanup-rationale-impact-summary.md`



    ```markdown

    [Cleanup Rationale & Impact Summary] Provide a clear, succinct summary reporting why each structural or code-level change was made, which principle it supports, and the impact on clarity, maintainability, and long-term usability. The report is structured for easy handoff and future reference. Execute as `{role=impact_reporter; input={cleanup_plan:list, validation_report:dict}; process=[map_actions_to_design_principles(), summarize_before_and_after_clarity(), articulate_impact_on_readability_and_maintenance(), produce_handoff_ready_summary()]; output={cleanup_summary_report:str}}`

    ```


## Message 6

Based on an exhaustive review of all the provided sequences (1 through 8), the following **maximally optimized and enhanced sequence** synthesizes their strongest qualities:

- Simplicity and chronological build-up from structure awareness to recursive refinement.
- Alignment with principles: **Clarity**, **Cohesion**, **Self-Explanation**, **Minimalism**, **SRP**, and **Structural Transparency**.
- Recursive architecture enabling repeat passes if value remains.
- Minimally verbose, clean, domain-agnostic wording suitable for *generalized autonomous codebase cleanup*.

---

### ✅ `Unified Sequence: Generalized Autonomous Codebase Cleanup`

#### `src/cleanup/sequence/step-01-structural-purpose-orientation.md`
```markdown
[Structural & Purpose Orientation]  
Begin with a complete structural sweep: identify major directories, files, modules, and boundaries. Infer the system’s intended purpose and primary workflows through code inspection and artifact analysis.  
Execute as: `{role=structure_orienter; input=codebase_root:path; process=[scan_directory_structure(), inventory_modules_and_artifacts(), identify_entry_exit_points(), infer_core_purpose_from_usage_patterns()], output={structure_map:dict, core_purpose:str}}`
```

---

#### `src/cleanup/sequence/step-02-logic-path-dependency-trace.md`
```markdown
[Logic Path & Dependency Trace]  
Map control, data, and dependency flows essential to the `core_purpose`. Visualize high-impact logic routes, critical dependencies, and coupling zones.  
Execute as: `{role=logic_mapper; input={structure_map:dict, core_purpose:str}; process=[trace_control_and_data_flow(), detect_module_import_graph(), identify_coupling_clusters(), highlight_logic_hotspots()], output={workflow_map:dict, dependency_map:dict, complexity_zones:list}}`
```

---

#### `src/cleanup/sequence/step-03-clarity-cohesion-self-explanation-audit.md`
```markdown
[Clarity, Cohesion & Self-Explanation Audit]  
Audit the codebase for naming clarity, structural cohesion, and readability without comments. Identify SRP violations, over-fragmentation, and areas where structure fails to explain itself.  
Execute as: `{role=clarity_auditor; input={workflow_map:dict, dependency_map:dict}; process=[assess_identifier_clarity(), locate cohesion/srp violations(), detect comment-dependent comprehension(), summarize self-explanation weak points()], output={clarity_report:dict, cohesion_findings:list, comment_dependence:list}}`
```

---

#### `src/cleanup/sequence/step-04-cleanup-target-opportunity-synthesis.md`
```markdown
[Cleanup Target & Opportunity Synthesis]  
Consolidate findings to isolate cleanup zones with highest clarity gain vs effort. Focus on logic simplification, structural realignment, naming improvements, and comment reduction.  
Execute as: `{role=opportunity_synthesizer; input={clarity_report:dict, cohesion_findings:list, comment_dependence:list}; process=[rank_issues_by_impact_vs_effort(), group related issues by scope(), isolate safe/high-value cleanup zones(), define actionable targets()], output={cleanup_targets:list, opportunity_matrix:dict}}`
```

---

#### `src/cleanup/sequence/step-05-prioritized-refactor-plan.md`
```markdown
[Prioritized Refactor Plan]  
Generate an atomic cleanup blueprint. Each action must yield measurable clarity/simplicity benefit with low risk. Order steps to optimize safety, flow, and minimal disruption.  
Execute as: `{role=refactor_planner; input={cleanup_targets:list}; process=[decompose_targets_into_refactor_steps(), rank_by safety-impact ratio(), establish task dependencies, structure into sequential plan()], output={refactor_plan:list}}`
```

---

#### `src/cleanup/sequence/step-06-structure-and-naming-refactor-pass.md`
```markdown
[Structure & Naming Refactor Pass]  
Apply structural reorganizations and identifier renaming from `refactor_plan`. Ensure improved grouping, clearer file/module names, reduced fragmentation, and more intuitive navigation.  
Execute as: `{role=structure_executor; input={refactor_plan:list, codebase_root:path}; process=[restructure_directories_and_files(), refactor module/group boundaries(), unify naming conventions, update all references/imports], output={realigned_structure:path}}`
```

---

#### `src/cleanup/sequence/step-07-logic-simplification-and-comment-reduction.md`
```markdown
[Logic Simplification & Comment Reduction]  
Simplify internal logic, consolidate functionality, and remove dead/duplicated code. Strip unnecessary comments—retain only *why-level* intent comments.  
Execute as: `{role=logic_simplifier; input={realigned_structure:path, refactor_plan:list}; process=[remove_redundant_logic(), enforce single-responsibility, apply naming-based clarification, purge/comment-refine non-essential notes()], output={refactored_codebase:path}}`
```

---

#### `src/cleanup/sequence/step-08-validation-pass-and-principle-alignment.md`
```markdown
[Validation Pass & Principle Alignment Review]  
Run regression validation and audit final structure against principles: Simplicity, Clarity, SRP, Minimalism, and Self-Explanation. Verify functional equivalence and structural clarity.  
Execute as: `{role=validator; input={original_codebase:any, refactored_codebase:path}; process=[run regression checks(), audit for principle adherence(), check navigation and readability, record any remaining issues()], output={validation_report:dict(functional_equivalence:bool, principle_score:float, residual_issues:list}}`
```

---

#### `src/cleanup/sequence/step-09-recursive-refinement-checkpoint.md`
```markdown
[Recursive Refinement Checkpoint]  
If validation passes and structure is aligned, conclude. If complexity, redundancy, or naming gaps remain with reasonable refactor potential, define scope for next cycle.  
Execute as: `{role=refinement_reviewer; input={refactored_codebase:path, validation_report:dict}; process=[identify remaining cleanup value(), weigh cost/benefit of another pass(), define next cycle’s narrowed focus()], output={next_pass_decision:dict(proceed:bool, recommended_scope:list)}}`
```

---

#### `src/cleanup/sequence/step-10-cleanup-summary-and-principle-rationale-report.md`
```markdown
[Cleanup Summary & Principle Rationale Report]  
Document the rationale and outcome for each major change, referencing clarity/cohesion principles. Provide a human-readable summary suitable for onboarding or auditing.  
Execute as: `{role=impact_reporter; input={refactor_plan:list, validation_report:dict}; process=[map actions to principle-based motivations(), summarize before/after structural clarity(), compile onboarding notes and rationale summaries()], output={cleanup_summary:str}}`
```

---

### 🎯 Characteristics of This Unified Sequence:
- **Minimalist yet complete** (10 steps): avoids bloated wording or overly granular stages.
- Each step **builds recursively**—data flows cleanly from step to step.
- Emphasizes *self-explanation over commentary*, *structure over documentation*.
- *Universally applicable* across languages, frameworks, domains.
- Clear delineation between analysis, synthesis, execution, and review.

Would you like this exported as Markdown files or an executable CLI script scaffold?
