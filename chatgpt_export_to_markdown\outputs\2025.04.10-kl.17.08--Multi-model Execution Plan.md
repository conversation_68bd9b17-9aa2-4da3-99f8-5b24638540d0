# Multi-model Execution Plan

Conversation ID: 67f7df01-1d2c-8008-8ff2-26d44b01c638

## Message 1

Let's create a plan to execute a sequence of system instructions against multiple models using litellm for a single user prompt.

Goal:
Send one user prompt, paired sequentially with different system instructions, to multiple LLM models and collect all responses.

Inputs:

A single user_prompt (string).

A list of system_instructions (list of strings).

A list of target_models (list of litellm-compatible model strings, e.g., ["openai/gpt-4o-mini", "anthropic/claude-3-haiku-20240307", "gemini/gemini-1.5-flash"]).



---

Plan

1. Initialize Results Storage:
Create a data structure (e.g., a dictionary) to store the results.
A good structure might be:

results = {
    "user_prompt": user_prompt,
    "system_instruction_results": []
}

Each item in system_instruction_results will store results for one system instruction across all models.


2. Outer Loop (System Instructions):
Iterate through each system_instruction in the system_instructions list.

For each system_instruction, create a placeholder for its results. For example:

current_sys_instruction_results = {
    "system_instruction": system_instruction,
    "model_responses": {}
}



3. Inner Loop (Models):
Inside the system instruction loop, iterate through each model_name in the target_models list.

Construct the messages:
Typically, you’ll create an array of messages for the request:

messages = [
  {"role": "system", "content": system_instruction},
  {"role": "user", "content": user_prompt}
]

API Call with Error Handling:

Use a try...except block to call litellm.completion().

Pass the model=model_name, messages=messages, and any other desired parameters (e.g., temperature, max_tokens).

Inside try: If successful, extract the response content (e.g., response.choices[0].message.content) and store it:

current_sys_instruction_results["model_responses"][model_name] = {
    "status": "success",
    "response": response_content
}

Optionally include cost/token usage if needed (litellm.completion_cost).

Inside except: If an error occurs (e.g., litellm.exceptions.APIError, AuthenticationError, RateLimitError, or generic Exception), store the error information:

current_sys_instruction_results["model_responses"][model_name] = {
    "status": "error",
    "error_type": str(e.__class__.__name__),
    "error_message": str(e)
}




4. Store System Instruction Results:
After the inner loop (models) completes for a given system instruction, append current_sys_instruction_results to the results["system_instruction_results"] list.


5. Final Output:
Once the outer loop finishes, the results dictionary will contain all the responses (or errors) organized by system instruction and then by model. You can then print this dictionary, save it to a file (e.g., JSON), or process it further.



This structure ensures each system instruction is paired with the user prompt and sent independently to every specified model, collecting all outcomes systematically.


---

Example: Analyze Results and Create Report

After you’ve collected everything in results, you might:

Analyze Results: Loop through results["system_instruction_results"] and compare or aggregate each model’s responses.

Create Report: Format a summary (e.g., in JSON or tabular form) to share insights or store them for future reference.





## Message 2

**Mapping out the plan**

I’m thinking through a plan to execute a sequence of system instructions against multiple models using litellm. This involves sending a single prompt to multiple LLM models sequentially and gathering their responses.

## Message 3

Below is a concise, step-by-step plan—along with a sample Python code snippet—showing how to systematically send **one user prompt** with a series of **system instructions** to multiple **LLM models** using **litellm**, then collect all responses.

---

## 1. Overview

1. **Data Structure Setup**  
   Prepare a dictionary to store:
   - The single user prompt.
   - A list of results for each system instruction.
   - Inside each system instruction’s record, map model names to their respective responses or errors.

2. **Outer Loop (System Instructions)**  
   For each system instruction:
   - Prepare a structure to hold the responses from each model.

3. **Inner Loop (Models)**  
   For each model:
   - Construct the messages (system + user).
   - Call the `litellm.completion()` function in a `try/except` block.
   - Store success or error details.

4. **Aggregate and Return**  
   After iterating over all system instructions and models, you’ll have a nested data structure capturing every response.

5. **Optional: Report/Analysis**  
   You can then process or present the data in any format you need—JSON, tabular, or custom summary.

---

## 2. Example Code

```python
import litellm

def execute_multi_system_instructions(
    user_prompt: str,
    system_instructions: list,
    target_models: list,
    temperature: float = 0.7,
    max_tokens: int = 1000
):
    """
    Sends one user prompt, paired sequentially with different system instructions,
    to multiple LLM models using litellm, collecting all responses.
    """

    # 1. Initialize a results data structure
    results = {
        "user_prompt": user_prompt,
        "system_instruction_results": []
    }

    # 2. Outer Loop: Iterate over each system instruction
    for system_instruction in system_instructions:
        # Prepare a container for this system instruction’s results
        current_sys_instruction_results = {
            "system_instruction": system_instruction,
            "model_responses": {}
        }

        # 3. Inner Loop: Iterate over each model
        for model_name in target_models:
            # Build the messages array
            messages = [
                {"role": "system", "content": system_instruction},
                {"role": "user", "content": user_prompt}
            ]

            try:
                # Call the litellm API
                response = litellm.completion(
                    model=model_name,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens
                )

                # Extract the response content (adjust if response structure differs)
                response_content = response.choices[0].message.content

                # Store success result
                current_sys_instruction_results["model_responses"][model_name] = {
                    "status": "success",
                    "response": response_content
                }

                # Optionally retrieve token usage or cost details if available:
                # token_usage = response.usage
                # cost = litellm.completion_cost(response)
                # current_sys_instruction_results["model_responses"][model_name]["token_usage"] = token_usage
                # current_sys_instruction_results["model_responses"][model_name]["cost"] = cost

            except Exception as e:
                # Store error details
                current_sys_instruction_results["model_responses"][model_name] = {
                    "status": "error",
                    "error_type": str(e.__class__.__name__),
                    "error_message": str(e)
                }

        # 4. Store results for this system instruction
        results["system_instruction_results"].append(current_sys_instruction_results)

    # 5. Return the compiled results
    return results


if __name__ == "__main__":
    # Example usage:
    user_prompt = "How can we improve team communication in a remote environment?"
    system_instructions = [
        "You are a professional communication coach. Provide actionable advice.",
        "You are a project manager focusing on agile methodologies. Provide steps using sprints."
    ]
    target_models = [
        "openai/gpt-4o-mini",
        "anthropic/claude-3-haiku-20240307",
        "gemini/gemini-1.5-flash"
    ]

    # Execute multi-instruction calls
    all_results = execute_multi_system_instructions(
        user_prompt=user_prompt,
        system_instructions=system_instructions,
        target_models=target_models
    )

    # Print or further process results
    print(all_results)
```

---

## 3. Potential Post-Processing

After running your calls, you’ll have a nested dictionary in `all_results`. To analyze or create a report:

1. **Loop Through** the `system_instruction_results` list.
2. **Compare** or **extract** relevant details (e.g., statuses, response texts).
3. **Format** your findings into JSON, CSV, or a custom summary (e.g., generating a side-by-side comparison table across models).

Example pseudo-code snippet for a quick text-based summary:

```python
for instruction_result in all_results["system_instruction_results"]:
    print(f"System Instruction: {instruction_result['system_instruction']}")
    for model, model_result in instruction_result["model_responses"].items():
        print(f"  Model: {model}")
        if model_result["status"] == "success":
            print(f"    Response: {model_result['response']}")
        else:
            print(f"    Error: {model_result['error_message']} ({model_result['error_type']})")
    print("-" * 50)
```

This yields a straightforward console report of responses or errors.

---

### Key Takeaways

- **Flexibility**: You can add optional parameters (temperature, max_tokens, etc.) or metadata (token usage, cost).
- **Modularity**: The loops keep system instructions and models neatly separate.
- **Reliability**: Try/Except ensures errors are captured without stopping the entire process.
- **Clarity**: By storing data in a nested dictionary, you can easily generate structured reports or share partial results with your workflow.

By following this plan, you streamline the process of sending multiple **system instructions** with a single **user prompt** across different models, maintaining a clear record of successes and errors.

## Message 4

please write it better and through a proper and clean class structure, example:

class QueryManager:

    """

    Manages queries to LLMs using LiteLLM, supporting optional streaming

    and interaction logging.

    """



    @staticmethod

    def _format_interaction_log(request_details, response_details, timestamp):

        lines = []

        lines.append(f"## Interaction @ {timestamp.strftime('%Y-%m-%d %H:%M:%S')} ##")

        lines.append("REQUEST:")

        lines.append(f"  Model: {request_details.get('model')}")

        lines.append(f"  Temperature: {request_details.get('temperature')}")

        lines.append(f"  Max Tokens: {request_details.get('max_tokens')}")



        lines.append("  Messages:")

        for msg in request_details.get('messages', []):

            role = msg.get('role')

            content = msg.get('content')

            lines.append(f"    Role: {role}\n    Content: {content}")



        lines.append("\nRESPONSE:")

        if response_details.get("error"):

            lines.append("  Status: FAILED")

            lines.append(f"  Error: {response_details['error']}")

            if response_details.get("raw_content"):

                lines.append(f"  Raw Content: {response_details['raw_content'][:500]}...")

        else:

            lines.append("  Status: SUCCESS")

            lines.append(f"  Model Used: {response_details.get('model_used', 'N/A')}")

            lines.append(f"  Cost (USD): {response_details.get('cost_usd', 'N/A')}")

            lines.append(f"  Content: {response_details.get('content', 'N/A')}")

        lines.append("-" * 60)

        return "\n".join(lines) + "\n"



    @staticmethod

    def _log_interaction_to_file(log_entry, log_file):

        try:

            with open(log_file, "a", encoding="utf-8") as f:

                f.write(log_entry)

        except Exception as e:

            print(f"[Warning] Failed to write log to '{log_file}': {e}")



    @staticmethod

    def query(system_instruction, user_prompt, model,

              temperature=0.5, max_tokens=500,

              stream=False, log_interaction=False, log_file=None):

        """

        Sends a query, supporting streaming mode and optional logging.



        Args:

            system_instruction (str): The system prompt context.

            user_prompt (str): The user prompt.

            model (str): LiteLLM model identifier.

            temperature (float): sampling temperature.

            max_tokens (int): max tokens in response.

            stream (bool): if True, partial response chunks are streamed to stdout.

            log_interaction (bool): if True, we log request/response details.

            log_file (str|None): file to write logs. If None, logs go to console.



        Returns:

            - If stream=False, returns either parsed JSON or error dict.

            - If stream=True, returns the final concatenated string.

        """

        print(f"--- Querying {model} {'(Streaming)' if stream else '(Blocking)'} ---")

        request_timestamp = datetime.now()



        messages = [

            {"role": "system", "content": system_instruction},

            {"role": "user",   "content": user_prompt}

        ]

        # If blocking, request JSON by appending instructions & setting response_format

        response_format = None

        if not stream:

            messages[0]["content"] += "\n\nRESPONSE MUST BE A VALID JSON OBJECT."

            response_format = {"type": "json_object"}



        request_details = {

            "model": model,

            "messages": messages,

            "temperature": temperature,

            "max_tokens": max_tokens

        }

        response_details = {"error": "Unknown"}  # Default until we set success/error



        final_result = None



        try:

            response = completion(

                model=model,

                messages=messages,

                temperature=temperature,

                max_tokens=max_tokens,

                response_format=response_format,

                stream=stream

            )



            if stream:

                print("Streaming response:")

                full_content = ""

                chunks = []

                for chunk in response:

                    chunks.append(chunk)

                    text_chunk = chunk.choices[0].delta.content or ""

                    print(text_chunk, end="", flush=True)

                    full_content += text_chunk

                print()  # newline after stream



                # Attempt to fetch model/cost from last chunk or fallback

                last_chunk = chunks[-1] if chunks else None

                used_model = getattr(last_chunk, "model", model) if last_chunk else model

                cost = None  # cost from streaming is not automatically tracked



                final_result = full_content

                response_details = {

                    "content": full_content,

                    "model_used": used_model,

                    "cost_usd": "N/A (streaming)",

                    "error": None

                }



            else:  # Blocking call with JSON format

                text_output = response.choices[0].message.content

                full_content = text_output

                cost = getattr(response, "cost", None)

                used_model = response.model



                try:

                    parsed_json = json.loads(text_output)

                    cost_str = f"{cost:.6f}" if cost else "N/A"

                    metadata = {"model_used": used_model, "cost_usd": cost_str}

                    if isinstance(parsed_json, dict):

                        parsed_json["_metadata"] = metadata

                        final_result = parsed_json

                        response_details = {

                            "content": json.dumps(parsed_json, indent=2),

                            "model_used": used_model,

                            "cost_usd": cost_str,

                            "error": None

                        }

                    else:

                        final_result = {"response_data": parsed_json, "_metadata": metadata}

                        response_details = {

                            "content": str(parsed_json),

                            "model_used": used_model,

                            "cost_usd": cost_str,

                            "error": None

                        }



                except json.JSONDecodeError as e:

                    error_msg = f"Invalid JSON: {e}"

                    print(error_msg)

                    final_result = {"error": error_msg, "raw_content": text_output}

                    response_details = {"error": error_msg, "raw_content": text_output}



        except litellm_exceptions.APIError as api_e:

            error_msg = f"API Error ({api_e.status_code}): {api_e.message}"

            print(error_msg)

            final_result = {"error": error_msg}

            response_details = {"error": error_msg}



        except Exception as e:

            error_msg = f"Query failed: {type(e).__name__} - {e}"

            print(error_msg)

            final_result = {"error": error_msg}

            response_details = {"error": error_msg}



        # Logging (if enabled)

        if log_interaction:

            log_entry = QueryManager._format_interaction_log(request_details, response_details, request_timestamp)

            if log_file:

                QueryManager._log_interaction_to_file(log_entry, log_file)

            else:

                print("\n--- Interaction Log Entry ---")

                print(log_entry)

                print("--- End Log Entry ---")



        return final_result




## Message 5

**Integrating functionality**

I'm working on merging the user's class with multi-system instructions and model calls. Considering "MultiSystemInstructionManager" or "MultiPromptExecutor" to enhance query handling.

**Formulating the plan**

I’m drafting a class called "MultiSystemExecutor" with a method for iterating over system instructions and models, using "QueryManager" for results capture, and preparing the code structure.

## Message 6

Below is an example of how you might integrate the **multi-instruction + multi-model** approach into a clean, class-based design. It leverages the provided `QueryManager` for individual queries while orchestrating the sequence of instructions and models in a single structure.

---

```python
import json
from datetime import datetime
import litellm
from litellm import exceptions as litellm_exceptions

class QueryManager:
    """
    Manages queries to LLMs using LiteLLM, supporting optional streaming
    and interaction logging.
    """

    @staticmethod
    def _format_interaction_log(request_details, response_details, timestamp):
        lines = []
        lines.append(f"## Interaction @ {timestamp.strftime('%Y-%m-%d %H:%M:%S')} ##")
        lines.append("REQUEST:")
        lines.append(f"  Model: {request_details.get('model')}")
        lines.append(f"  Temperature: {request_details.get('temperature')}")
        lines.append(f"  Max Tokens: {request_details.get('max_tokens')}")

        lines.append("  Messages:")
        for msg in request_details.get('messages', []):
            role = msg.get('role')
            content = msg.get('content')
            lines.append(f"    Role: {role}\n    Content: {content}")

        lines.append("\nRESPONSE:")
        if response_details.get("error"):
            lines.append("  Status: FAILED")
            lines.append(f"  Error: {response_details['error']}")
            if response_details.get("raw_content"):
                lines.append(f"  Raw Content: {response_details['raw_content'][:500]}...")
        else:
            lines.append("  Status: SUCCESS")
            lines.append(f"  Model Used: {response_details.get('model_used', 'N/A')}")
            lines.append(f"  Cost (USD): {response_details.get('cost_usd', 'N/A')}")
            lines.append(f"  Content: {response_details.get('content', 'N/A')}")

        lines.append("-" * 60)
        return "\n".join(lines) + "\n"

    @staticmethod
    def _log_interaction_to_file(log_entry, log_file):
        try:
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(log_entry)
        except Exception as e:
            print(f"[Warning] Failed to write log to '{log_file}': {e}")

    @staticmethod
    def query(
        system_instruction,
        user_prompt,
        model,
        temperature=0.5,
        max_tokens=500,
        stream=False,
        log_interaction=False,
        log_file=None
    ):
        """
        Sends a query, supporting streaming mode and optional logging.
        """
        print(f"--- Querying {model} {'(Streaming)' if stream else '(Blocking)'} ---")
        request_timestamp = datetime.now()

        messages = [
            {"role": "system", "content": system_instruction},
            {"role": "user",   "content": user_prompt}
        ]

        # If blocking, request JSON by appending instructions & setting response_format
        response_format = None
        if not stream:
            messages[0]["content"] += "\n\nRESPONSE MUST BE A VALID JSON OBJECT."
            response_format = {"type": "json_object"}

        request_details = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        response_details = {"error": "Unknown"}  # Default until success/error assigned
        final_result = None

        try:
            response = litellm.completion(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                response_format=response_format,
                stream=stream
            )

            if stream:
                # Streaming mode
                print("Streaming response:")
                full_content = ""
                chunks = []
                for chunk in response:
                    chunks.append(chunk)
                    text_chunk = chunk.choices[0].delta.content or ""
                    print(text_chunk, end="", flush=True)
                    full_content += text_chunk
                print()  # newline after stream finishes

                # Attempt to fetch model/cost from last chunk or fallback
                last_chunk = chunks[-1] if chunks else None
                used_model = getattr(last_chunk, "model", model) if last_chunk else model

                final_result = full_content
                response_details = {
                    "content": full_content,
                    "model_used": used_model,
                    "cost_usd": "N/A (streaming)",
                    "error": None
                }

            else:
                # Blocking call, requesting JSON
                text_output = response.choices[0].message.content
                cost = getattr(response, "cost", None)
                used_model = getattr(response, "model", model)

                try:
                    parsed_json = json.loads(text_output)
                    cost_str = f"{cost:.6f}" if cost else "N/A"
                    metadata = {"model_used": used_model, "cost_usd": cost_str}

                    if isinstance(parsed_json, dict):
                        parsed_json["_metadata"] = metadata
                        final_result = parsed_json
                        response_details = {
                            "content": json.dumps(parsed_json, indent=2),
                            "model_used": used_model,
                            "cost_usd": cost_str,
                            "error": None
                        }
                    else:
                        final_result = {"response_data": parsed_json, "_metadata": metadata}
                        response_details = {
                            "content": str(parsed_json),
                            "model_used": used_model,
                            "cost_usd": cost_str,
                            "error": None
                        }
                except json.JSONDecodeError as e:
                    error_msg = f"Invalid JSON: {e}"
                    print(error_msg)
                    final_result = {"error": error_msg, "raw_content": text_output}
                    response_details = {"error": error_msg, "raw_content": text_output}

        except litellm_exceptions.APIError as api_e:
            error_msg = f"API Error ({api_e.status_code}): {api_e.message}"
            print(error_msg)
            final_result = {"error": error_msg}
            response_details = {"error": error_msg}

        except Exception as e:
            error_msg = f"Query failed: {type(e).__name__} - {e}"
            print(error_msg)
            final_result = {"error": error_msg}
            response_details = {"error": error_msg}

        # Optional Logging
        if log_interaction:
            log_entry = QueryManager._format_interaction_log(request_details, response_details, request_timestamp)
            if log_file:
                QueryManager._log_interaction_to_file(log_entry, log_file)
            else:
                print("\n--- Interaction Log Entry ---")
                print(log_entry)
                print("--- End Log Entry ---")

        return final_result


class MultiSystemInstructionExecutor:
    """
    Executes one user prompt against multiple system instructions
    and multiple models, collecting all responses in a structured format.
    """

    def __init__(
        self,
        user_prompt,
        system_instructions,
        target_models,
        temperature=0.5,
        max_tokens=500,
        stream=False,
        log_interaction=False,
        log_file=None
    ):
        self.user_prompt = user_prompt
        self.system_instructions = system_instructions
        self.target_models = target_models
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.stream = stream
        self.log_interaction = log_interaction
        self.log_file = log_file

    def execute(self):
        """
        Iterates over each system instruction, then queries each model,
        gathering responses in a nested data structure:

        results = {
            "user_prompt": <string>,
            "system_instruction_results": [
                {
                    "system_instruction": <string>,
                    "model_responses": {
                        <model_name>: {
                            "status": "success" or "error",
                            "response": <response or error info>
                        },
                        ...
                    }
                },
                ...
            ]
        }

        Returns:
            dict: Nested dictionary with full response details.
        """
        results = {
            "user_prompt": self.user_prompt,
            "system_instruction_results": []
        }

        for sys_instruction in self.system_instructions:
            current_sys_instr_result = {
                "system_instruction": sys_instruction,
                "model_responses": {}
            }

            for model in self.target_models:
                # Use QueryManager to make the actual call
                response_data = QueryManager.query(
                    system_instruction=sys_instruction,
                    user_prompt=self.user_prompt,
                    model=model,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    stream=self.stream,
                    log_interaction=self.log_interaction,
                    log_file=self.log_file
                )

                # Determine success or error from the response data
                if "error" in response_data:
                    current_sys_instr_result["model_responses"][model] = {
                        "status": "error",
                        "response": response_data
                    }
                else:
                    current_sys_instr_result["model_responses"][model] = {
                        "status": "success",
                        "response": response_data
                    }

            results["system_instruction_results"].append(current_sys_instr_result)

        return results


if __name__ == "__main__":
    # Example usage:
    user_prompt = "How can we improve team communication in a remote environment?"
    system_instructions = [
        "You are a professional communication coach. Provide actionable advice.",
        "You are a project manager focusing on agile methodologies. Provide steps using sprints."
    ]
    target_models = [
        "openai/gpt-4o-mini",
        "anthropic/claude-3-haiku-20240307",
        "gemini/gemini-1.5-flash"
    ]

    executor = MultiSystemInstructionExecutor(
        user_prompt=user_prompt,
        system_instructions=system_instructions,
        target_models=target_models,
        temperature=0.7,
        max_tokens=1000,
        stream=False,          # switch to True for real-time streaming
        log_interaction=True,  # enable to log request/response details
        log_file=None          # or 'interaction.log' to log to a file
    )

    results_data = executor.execute()
    print("\n--- Final Aggregated Results ---")
    print(json.dumps(results_data, indent=2))
    print("--- End Results ---")
```

---

### Explanation of Structure

1. **`QueryManager`**  
   - A static-utility class handling the low-level interaction with LiteLLM:
     - Builds and sends the request (`messages`, model, temperature, etc.).
     - Manages **streaming** vs. **blocking** usage.
     - Tries to parse the output as JSON when **blocking**.
     - Logs each interaction (request + response) if requested.

2. **`MultiSystemInstructionExecutor`**  
   - High-level orchestration class:
     - Knows about the **user prompt** and **multiple system instructions**.
     - Iterates through each `system_instruction`.
     - For each instruction, iterates over all `target_models`.
     - Delegates the actual request to `QueryManager.query(...)`.
     - Collects responses (or errors) in a final nested dictionary.

3. **Usage**  
   - In the `__main__` block, instantiate `MultiSystemInstructionExecutor` with:
     - One user prompt (`user_prompt`).
     - A list of system instructions (`system_instructions`).
     - A list of LLM models (`target_models`).
     - Additional parameters (temperature, max tokens, streaming mode, etc.).
   - Call `execute()` to run all queries and gather the results.

This class-based approach keeps logic **modular**, **readable**, and **flexible**—you can easily add or swap instructions, models, and other parameters without modifying the core request/response handling code.
