# Destination Path Modification

Conversation ID: 68664b15-29b0-8008-9190-1e16b53e19e4

## Message 1

please fix this script such that if the user only specifies a name in the destination prompt (and it's not a network path) that it will be copied one directory above the current folder?

```
:: =============================================================================

:: cmd: initialize

:: =============================================================================

@ECHO OFF

CLS

SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION

IF EXIST "%~1" (SET "__init_path__=%~1") ELSE (SET "__init_path__=%CD%")

IF "!__init_path__:~-1!"=="\" SET "__init_path__=!__init_path__:~0,-1!"



:: =============================================================================

:: robocopy: define arguments

:: =============================================================================

::

:: /COPY:DAT  - Copies Data, Attributes, Timestamps.

:: /DCOPY:T   - Copies directory Timestamps.

:: /E         - Copies subdirectories, including empty ones.

:: /V         - Verbose output (includes skipped files). Useful for logs.

:: /TS        - Includes source file Timestamps in output.

:: /FP        - Includes Full Path names of files in output.

:: /R:1       - Number of Retries on failed copies: 1.

:: /W:3       - Wait time between retries: 3 seconds.

SET "COPY_ARGS_SAFE=/COPY:DAT /DCOPY:T /E /V /TS /R:0 /W:3"



:: /XF        - Exclude Files matching these names/types.

:: /XD        - Exclude Directories matching these names.

:: Add or remove exclusions as needed.

SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /XF *.tmp *.pyc /XD venv node_modules __pycache__ .venv"



:: /LOG       - Create new log file (using unique name).

:: /NS        - No Size - don't log file sizes.

:: /TEE       - Outputs to console window as well as log file.

:: Uncomment the line below to enable logging

:: SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /NS /LOG:robocopy_log.txt /TEE"



:: =============================================================================

:: cmd: prompt for source path

:: =============================================================================

:PromptForSourcePath

    ECHO.

    ECHO Source path: !__init_path__!

    ECHO.

    SET "__source__="

    SET /P "__source__=Enter source path (or press ENTER for default): "

    IF "!__source__!"=="" (SET "__init_path__=!__init_path__!") ELSE (SET "__init_path__=!__source__!")



:: =============================================================================

:: cmd: prompt for destination path

:: =============================================================================

:PromptForDestinationPath

ECHO.

ECHO Source path: !__init_path!

ECHO.

SET "destination_path="

SET /P "destination_path=Enter the destination path and press ENTER: "



IF "!destination_path!"=="" (

    ECHO.

    ECHO No destination path was provided. Aborting operation.

    GOTO EndScript

)



:: =============================================================================

:: cmd: process destination path

:: =============================================================================

:ProcessDestinationPath

SET "final_destination=!destination_path!"



:: If the input is a simple name (no \ or :), create the folder alongside the source path

ECHO "!destination_path!" | FINDSTR /R /C:"[\\:]" > NUL

IF ERRORLEVEL 1 (

    ECHO Detected simple name. Destination will be created in the parent directory.

    SET "final_destination=..\!destination_path!"

)



:: =============================================================================

:: robocopy: execute

:: =============================================================================

:ExecuteRobocopy

SET timer_start=%time:~0,8%

ECHO.

ECHO ============================================================================

ECHO Starting Robocopy...

ECHO.

ECHO     Source: !__init_path!

ECHO Destination: !final_destination!

ECHO.

ECHO Command: ROBOCOPY "!__init_path__!" "!final_destination!" %COPY_ARGS_SAFE%

ECHO ============================================================================

ECHO.



ROBOCOPY "!__init_path__!" "!final_destination!" %COPY_ARGS_SAFE%



SET timer_end=%time:~0,8%

ECHO.

ECHO ============================================================================

ECHO Robocopy finished.

ECHO.

ECHO - Start time : %timer_start%

ECHO - End time   : %timer_end%

ECHO ============================================================================

ECHO.



:: =============================================================================

:: cmd: exit

:: =============================================================================

:EndScript

ECHO Press any key to close this window...

PAUSE > NUL

POPD

EXIT /B

```

## Message 2

Below is a drop-in replacement for the **ProcessDestinationPath** block that fulfils the rule:

*If the user types only a bare name (no “\”, no “:”, and not a UNC path that starts “\\”) that name is expanded to a folder **one level above the chosen source folder**.*

```batch
:: =============================================================================
:: cmd: process destination path
:: =============================================================================
:ProcessDestinationPath
REM Default: whatever the user typed in
SET "final_destination=!destination_path!"

REM ---------------------------------------------------------------------------
REM A “simple” name has **no back-slash, no colon, and does NOT start “\\”**.
REM If it is simple, build an absolute path that points to the parent of
REM the source folder and append the name.
REM ---------------------------------------------------------------------------
ECHO "!destination_path!" | FINDSTR /R /C:"^[^\\:]*$" >NUL
IF ERRORLEVEL 0 (
    REM Resolve the parent folder of the current source as an absolute path
    FOR %%P IN ("!__init_path__!\..") DO SET "parent_path=%%~fP"
    SET "final_destination=!parent_path!\!destination_path!"
    ECHO Detected simple name. Destination resolved to "!final_destination!"
)
```

### What changed & why  

| Problem | Fix | Notes |
|---------|-----|-------|
| **Relative “..\name” was worked out from *where the script is running*, not from the chosen source folder.** | We now build the path from the *parent of `__init_path__`* (the source) so behaviour is correct even when the script is run from elsewhere. | `FOR %%P IN ("!__init_path__!\..") DO SET "parent_path=%%~fP"` expands the parent directory to an absolute path. |
| **Bare-name test could give false positives/negatives.** | Replaced the old `findstr` test with a tighter expression: `^[^\\:]*$` means “string contains neither ‘\’ nor ‘:’”. UNC paths begin with “\\”, so they fail the test and are left untouched. | Ensures we *only* treat a value like `Backup_2025` (no path separators) as “simple”. |
| **Path printed for confirmation.** | Added `ECHO ...` line so the script shows the fully resolved destination before Robocopy starts. | Improves user feedback. |

Copy-paste the block above over the existing `:ProcessDestinationPath` section (lines remain self-contained—no other parts of the script need to change).
