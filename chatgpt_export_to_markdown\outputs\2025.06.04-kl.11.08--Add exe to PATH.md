# Add exe to PATH

Conversation ID: 68400cfe-a790-8008-85dd-9329eddf5f1c

## Message 1

To run your executable (e.g., `Everything64.exe`) by simply typing `everything` in the Run dialog (Win+R) on Windows 11, you can choose between two methods:

---

### **Option 1: Add the Executable's Directory to the PATH Environment Variable**

This method allows you to run any executable from its directory without specifying the full path.

1. **Open Environment Variables:**
   - Press `Win + S`, type **"environment variables"**, and select **"Edit the system environment variables"**.
   - In the System Properties window, click **"Environment Variables..."**.

2. **Edit the PATH Variable:**
   - Under **User variables** (for your user account) or **System variables** (for all users), find and select the **Path** variable, then click **Edit**.

3. **Add New Path:**
   - In the Edit Environment Variable window, click **New** and paste the path to the directory containing your executable:
     ```
     C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe
     ```
   - Click **OK** to close all dialog boxes.

4. **Verify the Change:**
   - Open a new Command Prompt window and type `everything`. If configured correctly, the application should launch.

> **Note:** Changes to environment variables may require restarting open applications or logging out and back in to take effect.

---

### **Option 2: Create a Shortcut in a Directory Included in the PATH**

This method is useful if you prefer not to modify environment variables.

1. **Create a Shortcut:**
   - Right-click on the desktop or in File Explorer and select **New > Shortcut**.
   - For the location, enter the full path to your executable:
     ```
     "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\Everything64.exe"
     ```
   - Click **Next**, then name the shortcut `everything`, and click **Finish**.

2. **Move the Shortcut to a PATH Directory:**
   - Cut the newly created shortcut.
   - Paste it into a directory that's already included in your system's PATH variable. A common choice is:
     ```
     C:\Windows
     ```
     or
     ```
     C:\Windows\System32
     ```
   - You can check which directories are in your PATH by opening Command Prompt and typing:
     ```
     echo %PATH%
     ```

3. **Run the Application:**
   - Press `Win + R`, type `everything`, and press Enter. The application should launch.

> **Caution:** Placing shortcuts or executables in system directories like `C:\Windows` or `C:\Windows\System32` can pose security risks. Ensure that the files are from trusted sources and that their names do not conflict with existing system files.

---

By following either of these methods, you can configure your system to launch `Everything64.exe` by simply typing `everything` in the Run dialog. 

## Message 2

using windows 11, how can i add the path of a executable such that i can just write the command in the win+r run? e.g. make it so that writing "everything" and pressing enter will open this exe: "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\Everything64.exe"
