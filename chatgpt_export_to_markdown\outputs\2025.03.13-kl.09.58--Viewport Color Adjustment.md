# Viewport Color Adjustment

Conversation ID: 67d29e4b-cf64-8008-a1e2-924164fe2283

## Message 1



i've attached an example that you can use as reference, but your goal is to only extract the most fundamental parts and add it to the initially provided maxscript:

    

    ```maxscript

    -- Function: Set Viewport Background Color

    -- =================================================================================

    function fnSetViewportColor singleViewIndex:false setGradient:1 setBrightness:0 distinctiveViews:1 =

    (

        -- setGradient:0 (Solid Colors)

        solidColor = #(

            #(24, 24, 24),    -- Very Dark

            #(28, 30, 36),    -- Dark

            #(107, 114, 121), -- Bright

            #(204, 216, 224)  -- Very Bright

        )

        solidColorSelected = solidColor[setBrightness+1]

    

        -- setGradient:1 (Gradient Colors)

        gradientColor = #(

            #(#(0, 0, 0), #(35, 35, 35)),         -- Very Dark

            #(#(31, 33, 37), #(63, 66, 86)),      -- Dark

            #(#(67, 73, 84), #(120, 127, 133)),   -- Bright

            #(#(183, 190, 199), #(240, 244, 246)) -- Very Bright

        )

        gradientColorSelected = gradientColor[setBrightness+1]

        -- For each available view

        initialActiveView = viewport.activeViewport

        for currentView=1 to viewport.numViewEx() do

        (

            -- Activate the current view

            viewport.activeViewportEx(currentView)

    

            -- Check if single view mode is on (and skip if it doesn't match)

            if (viewport.activeViewport!=initialActiveView and singleViewIndex!=0) do continue

    

            -- Get the current visual style for the current view

            currViewSetting = NitrousGraphicsManager.GetActiveViewportSetting()

            currVisualStyle = currViewSetting.VisualStyleMode

            -- For the following types of visual styles: [#Shaded | #Wireframe | #Realistic]

            for currStyle in #(#Shaded, #Wireframe, #Realistic) do

            (

                currViewSetting.VisualStyleMode = currStyle

                -- Normalize the colors

                currentSolidColor = for colorVal in solidColorSelected collect (colorVal / 255.0)

                currentGradiensTop = for colorVal in gradientColorSelected[1] collect (colorVal / 255.0)

                currentGradiensBtm = for colorVal in gradientColorSelected[2] collect (colorVal / 255.0)

    

                -- If distinct views are set to true, make ortographic views solid

                -- and the perspective gradient

                if (distinctiveViews == 1) or (distinctiveViews == true) then

                (

                    if (viewport.getType() == #view_persp_user) then

                    (

                        viewport.EnableSolidBackgroundColorMode false

                        colorMan.setColor #ViewportGradientBackgroundTop    [currentGradiensTop[1], currentGradiensTop[2], currentGradiensTop[3]]

                        colorMan.setColor #ViewportGradientBackgroundBottom [currentGradiensBtm[1], currentGradiensBtm[2], currentGradiensBtm[3]]

                        redrawViews()

                    )

                    else

                    (

                        -- solidColorValue = [(solidColorSelected[1] / 255.0), (solidColorSelected[2] / 255.0), (solidColorSelected[3] / 255.0)]

                        -- print("solidColorValue: " + solidColorValue as string)

                        viewport.EnableSolidBackgroundColorMode true

                        colorMan.setColor #ViewportBackground [currentSolidColor[1], currentSolidColor[2], currentSolidColor[3]]

                        redrawViews()

                    )

                )

    

                else

                (

                    if (setGradient == 0) or (setGradient == false) then

                    (

                        viewport.EnableSolidBackgroundColorMode true

                        colorMan.setColor #ViewportBackground [currentSolidColor[1], currentSolidColor[2], currentSolidColor[3]]

                        redrawViews()

                    )

                    -- Else if 'gradient' was passed in as true

                    else if (setGradient == 1) or (setGradient == true) do

                    (

                        viewport.EnableSolidBackgroundColorMode false

                        colorMan.setColor #ViewportGradientBackgroundTop    [currentGradiensTop[1], currentGradiensTop[2], currentGradiensTop[3]]

                        colorMan.setColor #ViewportGradientBackgroundBottom [currentGradiensBtm[1], currentGradiensBtm[2], currentGradiensBtm[3]]

                        redrawViews()

                    )

                )

    

                -- Set the current view style to it's original style

                currViewSetting.VisualStyleMode = currVisualStyle

    

                -- Summarize

                outputStrings1 = ("singleViewIndex: " + singleViewIndex as string + " | ")

                outputStrings2 = ("setGradient: " + setGradient as string + " | ")

                outputStrings3 = ("setBrightness: " + setBrightness as string + " | ")

                outputStrings4 = ("distinctiveViews: " + distinctiveViews as string + " | ")

                outputStringsLn = (outputStrings1 + outputStrings2 + outputStrings3 + outputStrings4)

                ReplacePrompt outputStringsLn

                -- Debug

                -- print(outputStringsLn)

    

                -- -- Personal preference

                -- if (sysInfo.username == "PaulsenJ") do ( setClipBoardText outputStringsLn )

            )

        )

        -- Re-activate original view

        viewport.activeViewportEx(initialActiveView)

    )

    ```





please add the neccessary lines to this maxscript to enable affecting *active view only*:



    ```maxscript

    -- Convert 0-255 RGB to normalized 0.0-1.0 UnitRGB

    function fnNormalizeRGB colorVal = (

        return (color (colorVal.r/255.0) (colorVal.g/255.0) (colorVal.b/255.0))

    )

    

    solidColorBlack = fnNormalizeRGB (color 0 0 0)

    solidColorWhite = fnNormalizeRGB (color 255 255 255)

    

    solidColorSelected = solidColorWhite

    

    viewport.EnableSolidBackgroundColorMode true

    colorMan.setColor #ViewportBackground solidColorSelected

    

    ```

    

## Message 2

**Modifying script for active view**

The user wants to modify the maxscript to affect only the active viewport. To do that, I need to check how to access the active viewport and apply the color changes to it specifically.

I can start by targeting the active viewport using `viewport.activeView`. I'll check how to set the color for just that view. Adding these lines before enabling the color change should address the user's request.

Now, I'll look up the proper syntax for modifying the active viewport’s background.

**Combining active view logic**

Okay, so the user wants to apply changes only to the active viewport. The code provided already has some logic to focus on the active view with `viewport.activeViewport` and `viewport.activeViewportEx`. 

I think the user's using the second code snippet that sets the color for the active view only. The challenge here will be incorporating that into the larger code, focusing on ensuring that the effect targets only the active view — maybe using something like the `singleViewIndex` check.

I’ll go ahead and combine the snippets accordingly to meet the user’s needs. 

## Message 3

Here's your simplified, clear, and functional MaxScript that preserves the essential logic and integrates your requirement to affect only the active viewport:

## Message 4

**Explanation & Modular Structure:**

- **Utility Function:**
  - `fnNormalizeRGB`: Converts standard RGB (0-255) to normalized RGB (0.0-1.0).

- **Main Function (`fnSetActiveViewportColor`):**
  - Parameters: 
    - `setGradient` (boolean): Switch between solid color and gradient mode.
    - `setBrightness` (0-3): Choose among four brightness levels (very dark, dark, bright, very bright).
  - Clearly defined solid and gradient color arrays for easy adjustments.
  - Applies color changes to the **active viewport only**, fulfilling your explicit requirement.

- **Usage Example:**
  - Provided a concise example at the bottom demonstrating how to invoke the function.

This structure maintains your preference for simplicity and clarity, ensuring only fundamental elements remain while clearly addressing the requirement of affecting only the active viewport.

## Message 5

Successfully created text document 'Active View Bgcolor' which will be referenced in all future messages with the unique identifier textdoc_id: '67d29ea35c508191bc11317a99bb04c2'
