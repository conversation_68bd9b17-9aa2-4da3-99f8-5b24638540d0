# PRD Creation Process

Conversation ID: 67c989a5-cc28-8008-8964-d432c72312e0

## Message 1

# Context

You are an expert product manager your role is to work with me the product owner to create a Product Requirements Document. This document will be in markdown format and used to help other large language models understand the Product. Be concise.



# Instructions

1. Ask the product owner to explain the project idea to you

2. If they leave any details out based on the Sample PRD output, ask clarifying questions

3. Output a markdown file based on the product owner’s context and use the Sample PRD headings as a guide to the output



# Sample PRD Headings

1. Elevator Pitch – Pitch this product in one paragraph

2. This is for

3. Functional Requirements – What does it do?

4. How it Works – How will the user interact?

5. User Interface – How will it look?

## Message 2

**Clarifying the project**

OK, let me see. I’m gathering project details from the product owner, ensuring all necessary points are covered. This helps streamline the documentation process, highlighting key areas to address.

**Piecing together the approach**

The steps involve engaging with the product owner, seeking detailed clarifications if necessary, and compiling a comprehensive markdown file. This ensures a thorough progression toward completion.

**Gathering the essentials**

Engaging with the product owner, I’m shaping a product requirements document. When needed, I’ll ask clarifying questions to ensure all details are captured. Let’s work together through each step methodically.

**Crafting the PRD**

Starting with the project owner's context, I’ll then pinpoint any gaps by asking targeted questions. Ensuring a thorough approach before compiling the markdown file.

**Crafting the approach**

I’m preparing to ask the product owner for their project idea to start creating a Product Requirements Document, and I’ll ensure any missing details are clarified before compiling the markdown file.

## Message 3

**Hi there!** 

To get started on creating the Product Requirements Document (PRD), I’d love to hear your vision for this project:

1. **Could you please explain the project idea to me?** 
   - What is the overall concept or “Elevator Pitch”? 
   - Who is it for (the target audience)? 
   - What are the main features or Functional Requirements you have in mind?
   - How do you imagine users will interact with it (How it Works)?
   - Do you have any preliminary ideas about how you’d like it to look (User Interface)?

Feel free to share as much as you know. If there’s anything missing after you explain, I’ll ask clarifying questions. Once I have a solid understanding, I’ll produce a concise **Markdown** PRD with the headings you provided.
