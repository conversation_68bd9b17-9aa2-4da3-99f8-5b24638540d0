# Core Essence Distillation Template

Conversation ID: 680904fb-4538-8008-95b1-d309cd7b11ac

## Message 1

Familiarize yourself with generalized (maximally LLM-Optimized and enhanced) `system_message` instructions:



#### `1-templateStructureGuide.md`



    ```markdown

       # Template Structure Guide



       ## Overview



       This guide documents the standardized structure for creating templates in the Template-Based Instruction System. Following this structure ensures consistency and enables the automatic processing of templates by the catalog generator.



       ## Template File Structure



       Each template is stored as a markdown (.md) file and follows this standardized three-part structure:



       ```

       [Title] Interpretation text `{transformation}`

       ```



       ### Components



       1. **Title**: Enclosed in square brackets `[]`, defining the template's purpose.

          - Should be concise, descriptive, and follow title case formatting

          - Examples: `[Instruction Converter]`, `[Essence Distillation]`



       2. **Interpretation**: Plain text immediately following the title that describes what the template does.

          - Should clearly explain the template's function in natural language

          - Can include formatting like **bold**, *italic*, or other markdown elements

          - Provides context for human readers to understand the template's purpose



       3. **Transformation**: JSON-like structure enclosed in backticks with curly braces `\`{...}\``, defining the execution logic.

          - Contains the structured representation of the transformation process

          - Uses a consistent semi-colon separated key-value format



       ## Transformation Structure



       The transformation component follows this standardized format:



       ```

       `{

         role=<role_name>;

         input=[<input_params>];

         process=[<process_steps>];

         constraints=[<constraints>];

         requirements=[<requirements>];

         output={<output_format>}

       }`

       ```



       ### Transformation Components



       1. **role**: Defines the functional role of the template

          - Example: `role=essence_distiller`



       2. **input**: Specifies the expected input format and parameters

          - Uses array syntax with descriptive parameter names

          - Example: `input=[original:any]`



       3. **process**: Lists the processing steps to be executed in order

          - Uses array syntax with function-like step definitions

          - Can include parameters within step definitions

          - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`



       4. **constraints** (optional): Specifies limitations or boundaries for the transformation

          - Uses array syntax with directive-like constraints

          - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`



       5. **requirements** (optional): Defines mandatory aspects of the transformation

          - Uses array syntax with imperative requirements

          - Example: `requirements=[remove_self_references(), use_command_voice()]`



       6. **output**: Specifies the expected output format

          - Uses object syntax with typed output parameters

          - Example: `output={distilled_essence:any}`



       ## Template Naming Convention



       Templates follow a consistent naming convention that indicates their sequence and position:



       ```

       <sequence_id>-<step>-<descriptive_name>.md

       ```



       ### Naming Components



       1. **sequence_id**: A numeric identifier (e.g., 0001, 0002) that groups related templates

          - Four-digit format with leading zeros

          - Unique per sequence



       2. **step** (optional): A lowercase letter (a, b, c, d, e) that indicates the position within a sequence

          - Only used for templates that are part of multi-step sequences

          - Follows alphabetical order to determine execution sequence



       3. **descriptive-name**: A hyphenated name that describes the template's purpose

          - All lowercase

          - Words separated by hyphens

          - Should be concise but descriptive



       ### Examples



       - Single template: `0001-instructionconverter.md`

       - Sequence templates:

         - `0002-a-essence-distillation.md`

         - `0002-b-exposing-coherence.md`

         - `0002-c-precision-enhancement.md`

         - `0002-d-structured-transformation.md`

         - `0002-e-achieving-self-explanation.md`



       ## Template Examples



       ### Example 1: Simple Template



       ```markdown

       [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`

       ```



       ### Example 2: Sequence Step Template



       ```markdown

       [Essence Distillation] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`

       ```



       ## Creating New Templates



       To create a new template:



       1. Determine if it should be a standalone template or part of a sequence

       2. Assign an appropriate sequence_id (and step letter if part of a sequence)

       3. Create a descriptive name using hyphenated lowercase words

       4. Define the title that clearly indicates the template's purpose

       5. Write the interpretation text that explains what the template does

       6. Structure the transformation logic using the standardized format

       7. Save the file with the proper naming convention

       8. Run the catalog generator to include the new template in the JSON catalog



       ## Guidelines for Effective Templates



       1. **Clarity**: Each component should clearly communicate its purpose and functionality

       2. **Specificity**: Be specific about input/output formats and processing steps

       3. **Modularity**: Design templates to perform discrete, focused transformations

       4. **Composability**: For sequence templates, ensure outputs from one step can serve as inputs to the next

       5. **Consistency**: Follow the standardized structure and naming conventions exactly

       6. **Self-Documentation**: The interpretation text should provide sufficient context for understanding

       7. **Functional Completeness**: Ensure the transformation logic includes all necessary components



       ## Integration with Catalog Generator



       The catalog generator extracts metadata from template files based on specific patterns. It is essential to follow the template structure exactly to ensure proper extraction.



       The primary pattern used for extraction is:



       ```

       \[(.*?)\]\s*(.*?)\s*(`\{.*?\}`)

       ```



       This pattern extracts:

       1. The title from within square brackets

       2. The interpretation text following the title

       3. The transformation within backticks and curly braces



       Any deviation from this structure may result in improper extraction by the catalog generator.

    ```



---



#### `2-sampleTemplate.md`



    ```markdown

        # Sample Template



        ## Overview



        This sample template demonstrates the standardized template structure defined in the Template Structure Guide. It follows all the required formatting and structure conventions to ensure compatibility with the catalog generator.



        ## Template Content



        ```markdown

        [Summarization Refiner] Refine a previously generated summary to be more concise, factually accurate, and well-structured while maintaining all key information. `{role=summary_refiner; input=[original_summary:str, context:str]; process=[identify_core_components(), verify_factual_accuracy(context), improve_structure(), reduce_redundancy(), enhance_clarity()]; constraints=[maintain_key_information(), preserve_factual_accuracy(), limit_length(max_words=150)]; requirements=[eliminate_redundancy(), use_concise_language(), maintain_logical_flow()]; output={refined_summary:str}}`

        ```



        ## Template Components Explained



        1. **Title**: `[Summarization Refiner]`

           - Enclosed in square brackets

           - Concise and descriptive

           - Uses title case



        2. **Interpretation**: `Refine a previously generated summary to be more concise, factually accurate, and well-structured while maintaining all key information.`

           - Clearly explains the template's function in natural language

           - Provides context for human readers



        3. **Transformation**: `` `{role=summary_refiner; input=[original_summary:str, context:str]; process=[identify_core_components(), verify_factual_accuracy(context), improve_structure(), reduce_redundancy(), enhance_clarity()]; constraints=[maintain_key_information(), preserve_factual_accuracy(), limit_length(max_words=150)]; requirements=[eliminate_redundancy(), use_concise_language(), maintain_logical_flow()]; output={refined_summary:str}}` ``

           - Enclosed in backticks with curly braces

           - Contains all required components:

             - `role`: Defines the functional role (summary_refiner)

             - `input`: Lists expected input parameters with types

             - `process`: Defines the processing steps in sequence

             - `constraints`: Specifies limitations for the transformation

             - `requirements`: Lists mandatory aspects

             - `output`: Specifies the expected output format



        ## Filename Convention



        Following the naming convention, this template would be saved as:



        ```

        0040-summarization-refiner.md

        ```



        Or, if part of a sequence (e.g., as step b):



        ```

        0040-b-summarization-refiner.md

        ```



        ## Using the Template Generator



        To create templates like this using the provided template generator:



        1. Run the template generator script:

           ```

           python template_generator.py

           ```



        2. Choose to create a standalone template or a sequence of templates



        3. Follow the prompts to enter the template information:

           - Descriptive name

           - Title

           - Interpretation text

           - Role

           - Input parameters

           - Process steps

           - Constraints (optional)

           - Requirements (optional)

           - Output format



        4. Review the generated template and confirm to save



        The template generator ensures that the created template follows all the standardized structure requirements and saves it with the proper filename.

    ```



---





Here's the relevant code from which the templates will be parsed through:



    ```python

    #!/usr/bin/env python3

    # templates_lvl1_md_catalog_generator.py



    '''

        # Philosophical Foundation

        - INTERPRETATION = "How to activate the synthesized essence as an executable system capable of autonomous recursion and dynamic adaptability."

        - TRANSFORMATION = "How to scaffold the previously infused value into a reusable, meta-aware instruction engine that can execute, learn, and self-evolve."

    '''



    #===================================================================

    # IMPORTS

    #===================================================================

    import os

    import re

    import json

    import glob

    import sys

    import datetime



    #===================================================================

    # BASE CONFIG

    #===================================================================

    class TemplateConfig:

        LEVEL = None

        FORMAT = None

        SOURCE_DIR = None



        # Sequence definition

        SEQUENCE = {

            "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),

            "id_group": 1,

            "step_group": 2,

            "name_group": 3,

            "order_function": lambda step: ord(step) - ord('a')

        }



        # Content extraction patterns

        PATTERNS = {}



        # Path helpers

        @classmethod

        def get_output_filename(cls):

            if not cls.FORMAT or not cls.LEVEL:

                raise NotImplementedError("FORMAT/LEVEL required.")

            return f"templates_{cls.LEVEL}_{cls.FORMAT}_catalog.json"



        @classmethod

        def get_full_output_path(cls, script_dir):

            return os.path.join(script_dir, cls.get_output_filename())



        @classmethod

        def get_full_source_path(cls, script_dir):

            if not cls.SOURCE_DIR:

                raise NotImplementedError("SOURCE_DIR required.")

            return os.path.join(script_dir, cls.SOURCE_DIR)



    #===================================================================

    # FORMAT: MARKDOWN (lvl1)

    #===================================================================

    class TemplateConfigMD(TemplateConfig):

        LEVEL = "lvl1"

        FORMAT = "md"

        SOURCE_DIR = "md"



        # Combined pattern for lvl1 markdown templates

        _LVL1_MD_PATTERN = re.compile(

            r"\[(.*?)\]"     # Group 1: Title

            r"\s*"           # Match (but don't capture) whitespace AFTER title

            r"(.*?)"         # Group 2: Capture Interpretation text

            r"\s*"           # Match (but don't capture) whitespace BEFORE transformation

            r"(`\{.*?\}`)"   # Group 3: Transformation

        )



        PATTERNS = {

            "title": {

                "pattern": _LVL1_MD_PATTERN,

                "default": "",

                "extract": lambda m: m.group(1).strip() if m else ""

            },

            "interpretation": {

                "pattern": _LVL1_MD_PATTERN,

                "default": "",

                "extract": lambda m: m.group(2).strip() if m else ""

            },

            "transformation": {

                "pattern": _LVL1_MD_PATTERN,

                "default": "",

                "extract": lambda m: m.group(3).strip() if m else ""

            }

        }



    #===================================================================

    # HELPERS

    #===================================================================

    def _extract_field(content, pattern_cfg):

        try:

            match = pattern_cfg["pattern"].search(content)

            return pattern_cfg["extract"](match)

        except Exception:

            return pattern_cfg.get("default", "")



    def extract_metadata(content, template_id, config):

        content = content.strip()

        parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}

        return {"raw": content, "parts": parts}



    #===================================================================

    # CATALOG GENERATION

    #===================================================================

    def generate_catalog(config, script_dir):

        # Find templates and extract metadata

        source_path = config.get_full_source_path(script_dir)

        template_files = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))



        print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")

        print(f"Source: {source_path} (*.{config.FORMAT})")

        print(f"Found {len(template_files)} template files.")



        templates = {}

        sequences = {}



        # Process each template file

        for file_path in template_files:

            filename = os.path.basename(file_path)

            template_id = os.path.splitext(filename)[0]



            try:

                # Read content

                with open(file_path, 'r', encoding='utf-8') as f:

                    content = f.read()



                # Extract and store template metadata

                template_data = extract_metadata(content, template_id, config)

                templates[template_id] = template_data



                # Process sequence information from filename

                seq_match = config.SEQUENCE["pattern"].match(template_id)

                if seq_match:

                    seq_id = seq_match.group(config.SEQUENCE["id_group"])

                    step = seq_match.group(config.SEQUENCE["step_group"])

                    seq_order = config.SEQUENCE["order_function"](step)

                    sequences.setdefault(seq_id, []).append({

                        "template_id": template_id, "step": step, "order": seq_order

                    })

            except Exception as e:

                print(f"ERROR: {template_id} -> {e}", file=sys.stderr)



        # Sort sequence steps

        for seq_id, steps in sequences.items():

            try:

                steps.sort(key=lambda step: step["order"])

            except Exception as e:

                print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)



        # Create catalog with metadata

        timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")

        return {

            "catalog_meta": {

                "level": config.LEVEL,

                "format": config.FORMAT,

                "generated_at": timestamp,

                "source_directory": config.SOURCE_DIR,

                "total_templates": len(templates),

                "total_sequences": len(sequences)

            },

            "templates": templates,

            "sequences": sequences

        }



    def save_catalog(catalog_data, config, script_dir):

        output_path = config.get_full_output_path(script_dir)

        print(f"Output: {output_path}")



        try:

            with open(output_path, 'w', encoding='utf-8') as f:

                json.dump(catalog_data, f, indent=2, ensure_ascii=False)

                print(f"SUCCESS: Saved catalog with {catalog_data['catalog_meta']['total_templates']} templates.")

                print("--- Catalog Generation Complete ---")

            return output_path

        except Exception as e:

            print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)

            return None



    #===================================================================

    # IMPORTABLE API FOR EXTERNAL SCRIPTS

    #===================================================================

    def get_default_catalog_path(script_dir=None):

        '''Get the path to the default catalog file.'''

        if script_dir is None:

            script_dir = os.path.dirname(os.path.abspath(__file__))

        return TemplateConfigMD().get_full_output_path(script_dir)



    def load_catalog(catalog_path=None):

        '''Load a catalog from a JSON file.'''

        if catalog_path is None:

            catalog_path = get_default_catalog_path()



        if not os.path.exists(catalog_path):

            raise FileNotFoundError(f"Catalog file not found: {catalog_path}")



        try:

            with open(catalog_path, 'r', encoding='utf-8') as f:

                return json.load(f)

        except json.JSONDecodeError:

            raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")



    def get_template(catalog, template_id):

        '''Get a template by its ID.'''

        if "templates" not in catalog or template_id not in catalog["templates"]:

            return None

        return catalog["templates"][template_id]



    def get_sequence(catalog, sequence_id):

        '''Get all templates in a sequence, ordered by steps.'''

        if "sequences" not in catalog or sequence_id not in catalog["sequences"]:

            return []



        sequence_steps = catalog["sequences"][sequence_id]

        return [(step["step"], get_template(catalog, step["template_id"]))

                for step in sequence_steps]



    def get_all_sequences(catalog):

        '''Get a list of all sequence IDs.'''

        if "sequences" not in catalog:

            return []

        return list(catalog["sequences"].keys())



    def get_system_instruction(template):

        '''Convert a template to a system instruction format.'''

        if not template or "parts" not in template:

            return None



        parts = template["parts"]

        instruction = f"# {parts.get('title', '')}\n\n"



        if "interpretation" in parts and parts["interpretation"]:

            instruction += f"{parts['interpretation']}\n\n"



        if "transformation" in parts and parts["transformation"]:

            instruction += parts["transformation"]



        return instruction



    def regenerate_catalog(force=False):

        '''Regenerate the catalog file.'''

        script_dir = os.path.dirname(os.path.abspath(__file__))

        config = TemplateConfigMD()

        catalog_path = config.get_full_output_path(script_dir)



        if os.path.exists(catalog_path) and not force:

            # Check if catalog is older than any template file

            catalog_mtime = os.path.getmtime(catalog_path)

            templates_dir = config.get_full_source_path(script_dir)



            needs_update = False

            for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):

                if os.path.getmtime(file_path) > catalog_mtime:

                    needs_update = True

                    break



            if not needs_update:

                print("Catalog is up to date")

                return load_catalog(catalog_path)



        # Generate and save new catalog

        catalog = generate_catalog(config, script_dir)

        save_catalog(catalog, config, script_dir)

        return catalog



    #===================================================================

    # SCRIPT EXECUTION

    #===================================================================

    if __name__ == "__main__":

        SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))



        # Check for command line arguments

        if len(sys.argv) > 1 and sys.argv[1] == "--api-test":

            # Simple API test

            catalog = regenerate_catalog()

            print("\nAvailable sequences:")

            for seq_id in get_all_sequences(catalog):

                sequence_steps = get_sequence(catalog, seq_id)

                if sequence_steps:

                    first_step_title = sequence_steps[0][1]["parts"]["title"]

                    print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")



            sys.exit(0)



        # Regular execution

        try:

            config_to_use = TemplateConfigMD

            active_config = config_to_use()

            catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)

            save_catalog(catalog, active_config, SCRIPT_DIRECTORY)



        except NotImplementedError as e:

            print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)

            sys.exit(1)

        except FileNotFoundError as e:

            print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)

            sys.exit(1)

        except Exception as e:

            print(f"FATAL ERROR: {e}", file=sys.stderr)

            sys.exit(1)

    ```



---



**Context:**

These generalizations are will undergo continual enhancements with the aim of transforming them from (currently) **generalized instructions** -> **generalized (LLM-optimized system_message) instructions**. Below is a new set of instructions (sequences), each block is now enclosed with triple backticks and a YAML/Markdown structure that includes the **title**, a short interpretive statement, and the transformation instructions wrapped in curly braces (`{}`).



**Constant:**

Identify single most critical aspect for maximizing overall value, and to do so while ensuring maximum clarity, utility, and adaptability without diminishing potential yield. Leverage insights from prior interactions and rigorous analysis of newly provided sequences. Consistently ensure the output delivers peak actionable value.



**Process:**

- Goal: Produce an optimal sequence of **llm-optimized**, **generalized** `system_message` instructions.

- Method: Develop existing transformation concepts, leveraging insights from previous history and analysis of newly provided sequences.

- Constraint: Adhere to the parameters defined within this message.

- Priority: Consistently maximize actionable value.



**Constraints:**

Maintain the **generalized** nature of them (as you enhance them) to "spawn" a *process* of analyzing *any* subject.



**Requirements:**

Adhere to the **existing** structure (transformation concepts and foundational principles).



---



**Objective:**

Your objective is to forge a maximally effective sequence (structured progression) of `system_message` instructions that are simultaneously **llm-optimized** and **broadly generalized**. Go through and evaluate/consolidate the provided alternatives and create a new *maximally optimized and enhanced sequence* based on previously provided directives/parameters, and make sure they adhere to the defined structure:



---

Consolidate this into a single maximally optimized llm-instruction sequence:



      --- Step a: Core Essence Distillation ---

      {

          "essential_elements": [

              "Strictly decompose input to extract only core concepts, explicit claims, assumptions, and relationships—exclude narrative and stylistic elements to form a clear conceptual inventory.",

              "Map all explicit and implicit structural relationships, causal links, dependencies, and mechanisms among the inventoried components to reveal the system’s underlying logic and points of potential misunderstanding or conflict.",

              "Systematically assess structural components and links to identify and justify a single, non-trivial, highly explanatory mechanism or relationship—the unique insight nexus—based on its foundational impact and novelty.",

              "Distill the unique nexus into one concise, potent, stand-alone statement that clearly communicates the essential mechanism or principle, maximizing expressive power and universal clarity.",

              "Reframe the insight to highlight its origins in structural limitations or inevitable perspective gaps, ensuring it is empathetic, free from blame, and explicitly universally applicable.",

              "Validate for factual and structural integrity, clarity, non-triviality, universal applicability, and immediate usability—confirm the insight is maximally fit for deployment in any context, not tethered to domain-specific language or interpretation."

          ]

      }


## Message 2

**Refining the structure**

The user seeks a singular, optimized instruction, merging steps into one cohesive system message. This involves transforming texts into JSON and adding transformation blocks for clarity.

**Crafting the instruction**

The goal is to distill and consolidate crucial elements into a comprehensive instruction, ensuring it aligns with the specified structure and guidelines for optimal performance.

**Formulating a solution**

I’m creating a single transformation template called `[Maximally-Optimized Essence Distillation]`, embedding an optimized instruction sequence with clear JSON transformation steps, making it broad and universally applicable.

**Crafting universal guidance**

I’m focusing on generalizing the step-by-step essence of distillation, ensuring it’s universally applicable, concise, and domain-neutral, while maintaining JSON format clarity.

**Formulating the process**

I'm developing a structured approach to distilling core concepts, ensuring the universal applicability and clarity of instructions, and incorporating user-provided bullet points into the transformation process.

**Formulating the process**

Ensuring the steps are clear, concise, and universally applicable, I’m focusing on distilling core concepts into a potent, domain-agnostic principle, with clear instructions and criteria.

**Crafting a single system message**

I’m piecing together a structured system message titled `[Core Essence Distillation]`, outlining roles, inputs, processes, constraints, and requirements to distill core concepts universally and clearly.

**Sifting through complex structures**

Mapping out details to strip away complexities and identify critical ideas, all while ensuring precision, clarity, and utility. Progressing methodically towards a decisive core principle.

**Piecing together the essence**

I’m ensuring the sequence is streamlined, without duplicating "Step a". However, it’s still unclear if "Step a" should be included due to usage ambiguity.

**Focusing on the essentials**

I’m wrapping up by consolidating the process into a single markdown block, ensuring it aligns with the sequence approach and avoids any ambiguity.

**Aligning the structure**

I’m working through aligning the markdown block with the guide's format, ensuring integration with the sequence approach while maintaining clarity and adhering to the specified structure.

**Shaping the process**

I’m thinking through how to configure each element neatly in a markdown block, focusing on universally applicable insights and detailed steps, while ensuring a clear, practical approach throughout.

## Message 3

```markdown
[Core Essence Distillation] Distill the input’s fundamental concepts into a single, non-trivial, universally applicable insight or mechanism. The transformation excludes narrative or stylistic details, clarifies explicit and implicit relationships, selects the most foundational explanatory nexus, reframes it with empathy for any perspective, and validates its clarity, novelty, and immediate applicability. 
`{
  role=core_essence_distiller;
  input=[original:any];
  process=[
    strictly_decompose_input(exclude_narrative_and_style=true),
    map_structural_relationships_and_dependencies(),
    identify_unique_insight_nexus(),
    distill_insight_into_concise_statement(),
    reframe_insight_for_universal_applicability_and_empathy(),
    validate_integrity_and_usability()
  ];
  constraints=[
    preserve_structural_coherence(),
    maintain_factual_and_logical_accuracy(),
    keep_domain_agnostic()
  ];
  requirements=[
    confirm_nontriviality(),
    ensure_novel_explanatory_power(),
    present_in_clear_and_unambiguous_language(),
    verify_immediate_actionability()
  ];
  output={distilled_essence:any}
}`
```

## Message 4

Please reorder these items into a much more optimized sequential order for maximum concistency to yeld high-value outputs, without generic bloat:

`0191-a-universal-singularity-insight-extractor.md`



[Universal Singularity Insight Extractor] Extract from any input a single, maximally potent and universally applicable insight—the structural principle or mechanism which, if understood, yields the greatest clarifying power. This role-chained protocol isolates, structures, prioritizes, distills, and validates that singular insight, independent of subject domain or narrative style. Execute as: `{role=universal_insight_extractor; input=[raw_input:any]; process=[{role=component_isolator; input=raw_input; process=[identify_core_concepts(), enumerate_explicit_statements(), extract_implicit_assumptions(), map_perceived_relationships(), discard_narrative_elements()], output={component_inventory:dict}}, {role=mechanism_mapper; input=component_inventory; process=[map_structural_and_causal_relationships(), trace_dependencies_and_feedback_loops(), surface_implicit_mechanisms(), resolve_structural_ambiguities()], output={structural_logic_map:dict}}, {role=insight_prioritizer; input={structural_logic_map, component_inventory}; process=[evaluate_components_and_mechanisms_for_explanatory_power(), rank_by_novelty_and_foundational_impact(), isolate_singular_non-trivial_nexus(), justify_selection_by structural_significance()], output={unique_insight_nexus:dict(element:any, rationale:str)}}, {role=insight_distiller; input=unique_insight_nexus; process=[condense_to_core_meaning(), articulate_in_clear_concise_language(), foreground_structural_mechanism_and_inevitability(), remove_blame_and_domain_jargon(), ensure_universal_intelligibility()], output={distilled_insight:str}}, {role=insight_validator; input={distilled_insight, unique_insight_nexus, structural_logic_map}; process=[verify_accuracy_and_fidelity(), confirm_non-triviality_clarity_and_impact(), maximize_domain-agnostic_expression(), validate_deployment_readiness()], output={validated_insight:str}}]; constraints=[output_single_structurally_rooted_insight(), forbid_domain-specific language(), maximize actionable universality(), deliver outcome in clear, non-ambiguous terms()]; requirements=[follow role chain in exact order(), ensure each sub-output is passed correctly(), output only the validated_insight, no additional commentary()]; output={validated_insight:str}}`



`0191-b-universal-singularity-insight-extractor.md`



[Universal Singularity Insight Extractor] Your task is not to merely summarize or paraphrase, but to rigorously decompose any input, map its internal logic, identify the single most foundational mechanism or relationship, distill it to an ultra-clear, universally resonant statement, and validate it for immediate, cross-context use. Strictly follow every protocol step, maintain explicit intermediate outputs, and never omit validation. Execute as: `{role=universal_structural_insight_extractor;input=[raw_input:any];process=[{role=component_isolator; input=raw_input:any; process=[identify_core_concepts(), enumerate_explicit_statements_and_claims() extract_implicit_assumptions(), map_perceived_relationships(), eliminate_all_narrative_and_style()]; output={component_inventory:dict}},{role=mechanism_mapper; input=component_inventory:dict; process=[map_all_explicit_and_implicit_relationships(), identify_structural_and_causal_mechanisms(), trace_dependencies_and_feedback_loops(), resolve_ambiguities_for_cohesive_structure()]; output={structural_logic_map:dict}},{role=insight_prioritizer; input={structural_logic_map:dict, component_inventory:dict}; process=[evaluate_all_components_and_mechanisms_for_uniqueness_and_depth(), rank_by_explanatory_power_and_novelty(), extract_the_most_fundamental_non-trivial_nexus(), justify_selection_by_structural_significance()]; output={unique_insight_nexus:dict(element:any, rationale:str)}},{role=insight_distiller; input=unique_insight_nexus:dict; process=[condense_nexus_to_singular_core_meaning(), articulate_in_crystal_clear_potent_language(), foreground_structural_mechanism_and_human_inevitability(), remove_blame_and_jargon(), ensure_universal_readability_and_transferability()]; output={distilled_insight:str}},{role=insight_validator; input={distilled_insight:str, unique_insight_nexus:dict, structural_logic_map:dict}; process=[verify_accuracy_and_structural_fidelity(), confirm_non-triviality_and_impact(), maximize_clarity_and_domain-agnostic_expression(), validate_deployment_readiness()]; output={validated_insight:str}}];constraints=[must maintain stepwise chaining and output visibility at every stage, always prioritize mechanism and structure above narrative or domain context, never skip singular insight extraction or validation, ensure all output is cross-domain transferable, concise, and jargon-free];requirements=[output MUST be a single, ultra-clear, mechanism-driven, universally applicable insight with justification];output={validated_insight:str, justification:str}}`



`0191-c-conceptual inventory creator.md`



[Universal Conceptual Inventory Creator] Your goal is not to interpret, summarize, or embellish the input, but to **decompose** it into a clean, structured inventory containing *only* its essential conceptual components: core concepts, definitive statements, and the relational links between them, stripping away all narrative, style, and non-essential content. `{role=conceptual_inventory_creator; input=[source_content:any]; process=[identify_core_concepts(), extract_definitive_statements(), map_relational_links(), strip_non_essential_content(), assemble_inventory()]; constraints=[forbid_interpretation_or_analysis(), prevent_adding_external_information(), maintain_source_fidelity_of_essentials()]; requirements=[produce_structured_inventory(), isolate_only_essential_components(), eliminate_all_narrative_and_stylistic_content(), preserve_core_meaning_and_relationships()]; output={inventory:{concepts:list, statements:list, relationships:list}}}`



`0191-d-universal-sequence-initiation.md`



[Universal Sequence Initiation] Define the overarching goal: to meticulously extract *one* universally potent, structurally-derived insight from any input by executing a precise multi-stage analysis culminating in a validated, deployment-ready statement and justification. `{role=sequence_initiator; input=[raw_input:any]; process=[set_target_outcome(goal='single_universal_structural_insight_with_justification'), establish_process_parameters(steps=10, final_output_format='{validated_universal_insight:str, justification:str}')]; constraints=[maintain_focus_on_single_insight_extraction(), prepare_for_rigorous_stepwise_execution()]; requirements=[initialize_the_extraction_process_clearly(), define_expected_final_output_structure()]; output={initialized_input:any, goal_definition:str}}`



`0191-e-universal-input-decomposition.md`



[Universal Input Decomposition] Your goal is not interpretation, but **decomposition**: Isolate the input's essential conceptual components—core concepts, definitive statements/claims, implicit assumptions—into a structured inventory, stripping all narrative, style, and superfluous content. `{role=conceptual_inventory_creator; input=[initialized_input:any]; process=[identify_core_concepts(), extract_definitive_statements_and_claims(), surface_implicit_assumptions(), map_initial_perceived_relationships(), strip_all_non_essential_content(), assemble_raw_inventory()]; constraints=[forbid_interpretation_or_analysis(), prevent_adding_external_information(), maintain_source_fidelity_of_essentials()]; requirements=[produce_structured_inventory(), isolate_only_essential_components(), eliminate_all_narrative_and_stylistic_content(), preserve_core_meaning_and_relationships_initially_perceived()]; output={raw_inventory:{concepts:list, statements:list, assumptions:list, initial_links:list}}}`



`0191-f-universal-explicit-relationship-mapping.md`



[Universal Explicit Relationship Mapping] Map all *explicitly stated* or directly implied structural, causal, and logical links between the inventoried components, creating a preliminary map of the input's overt architecture. `{role=explicit_link_mapper; input=[raw_inventory:dict]; process=[identify_direct_connections_between_components(), map_stated_causal_relationships(), document_explicit_dependencies_and_sequences(), represent_as_initial_structural_map()]; constraints=[focus_solely_on_explicitly_stated_or_directly_implied_links(), avoid_inferring_deeper_mechanisms_at_this_stage()]; requirements=[create_accurate_map_of_overt_structure(), provide_foundation_for_implicit_analysis()]; output={explicit_relationship_map:dict}}`



`0191-g-universal-implicit-mechanism-discovery.md`



[Universal Implicit Mechanism Discovery] Analyze the explicit map and raw inventory to uncover *implicit* structural mechanisms, underlying operational logic, feedback loops, and core causal engines driving the observed relationships. Resolve structural ambiguities. `{role=implicit_mechanism_analyzer; input=[explicit_relationship_map:dict, raw_inventory:dict]; process=[infer_underlying_structural_patterns(), trace_feedback_loops_and_dynamic_interactions(), identify_implicit_causal_engines_or_principles(), resolve_structural_ambiguities_to_form_cohesive_logic(), construct_comprehensive_logic_map()]; constraints=[base_inferences_strictly_on_inventoried_items_and_explicit_links(), focus_on_identifying_functional_mechanisms()]; requirements=[reveal_the_deeper_operational_logic_of_the_system(), produce_a_complete_and_coherent_structural_logic_map()]; output={comprehensive_logic_map:dict}}`



`0191-h-universal-potential-insight-evaluation.md`



[Universal Potential Insight Evaluation] Evaluate *all* significant components and mechanisms within the comprehensive logic map for their potential as the core insight. Rate each candidate based on explanatory power, foundational impact, non-triviality, and universality. `{role=insight_evaluator; input=[comprehensive_logic_map:dict, raw_inventory:dict]; process=[identify_potential_insight_candidates(components_and_mechanisms), assess_explanatory_power_per_candidate(), rate_foundational_impact_within_structure(), score_non_triviality_and_novelty(), evaluate_potential_universality(), rank_candidates_objectively()]; constraints=[evaluate_candidates_systematically_against_all_criteria(), avoid_premature_selection()]; requirements=[produce_a_ranked_list_of_potential_core_insights(), provide_basis_for_objective_selection()]; output={ranked_insight_candidates:list}}`



`0191-i-universal-singular-nexus-selection.md`



[Universal Singular Nexus Selection] Select the *single*, highest-ranked candidate insight nexus (component or mechanism) identified as possessing the most profound structural significance and explanatory power. Formulate an explicit justification for this selection based on the evaluation criteria. `{role=nexus_selector; input=[ranked_insight_candidates:list]; process=[identify_top_ranked_candidate(), confirm_singularity_and_non_triviality(), formulate_detailed_selection_justification(criteria_based=True), isolate_the_selected_nexus_and_its_rationale()]; constraints=[must_select_only_one_nexus(), justification_must_be_explicit_and_structurally_grounded()]; requirements=[pinpoint_the_single_most_potent_insight_core(), clearly_document_the_reasoning_for_selection()]; output={selected_nexus:{element:any, justification:str}}}`



`0191-j-universal-core-meaning-condensation.md`



[Universal Core Meaning Condensation] Condense the selected nexus to its absolute core conceptual meaning. Strip away all contextual specifics, examples, or elaborations from the original input, retaining only the essential structural principle or mechanism itself. `{role=meaning_condenser; input=[selected_nexus:dict]; process=[extract_essential_principle_or_mechanism(), eliminate_all_source_specific_context_and_examples(), remove_dependent_clauses_not_core_to_mechanism(), formulate_concise_core_statement_draft()]; constraints=[focus_exclusively_on_the_isolated_nexus_meaning(), prevent_reintroduction_of_contextual_details()]; requirements=[achieve_maximal_condensation_of_the_core_idea(), preserve_the_essential_structural_truth()]; output={condensed_meaning:str}}`



`0191-k-universal-universal-articulation-refinement.md`



[Universal Universal Articulation Refinement] Refine the condensed meaning into a maximally clear, potent, universally applicable, and easily understandable statement. Eliminate *all* domain jargon, subjective language, bias, and blame. Ensure empathetic and structurally-focused framing. `{role=universal_articulator; input=[condensed_meaning:str]; process=[enhance_linguistic_clarity_and_impact(), eradicate_all_domain_specific_terminology_and_jargon(), neutralize_subjective_or_biased_phrasing(), ensure_blame_free_and_empathetic_framing(), maximize_cross_context_intelligibility(), finalize_universal_insight_wording()]; constraints=[language_must_be_universally_understandable(), statement_must_be_objective_and_structurally_focused()]; requirements=[produce_a_crystal_clear_potent_statement(), ensure_immediate_applicability_across_any_domain()]; output={refined_insight_statement:str}}`



`0191-l-universal-internal-fidelity-&-quality-validation.md`



[Universal Internal Fidelity & Quality Validation] Validate the refined insight statement rigorously against the comprehensive logic map and the selected nexus justification. Confirm its structural fidelity, accuracy, non-triviality, internal consistency, and profound explanatory power relative to the source logic. `{role=internal_validator; input=[refined_insight_statement:str, selected_nexus:dict, comprehensive_logic_map:dict]; process=[verify_structural_fidelity_to_map_and_nexus(), confirm_accuracy_of_the_claim(), re-assess_non_triviality_and_impact(), check_for_internal_consistency_and_clarity(), evaluate_achieved_explanatory_power()]; constraints=[validation_must_be_objective_and_based_on_prior_steps(), address_any_detected_issues_before_proceeding()]; requirements=[ensure_the_insight_is_a_faithful_and_high_quality_representation_of_the_core_mechanism(), confirm_readiness_for_final_validation()]; output={internally_validated_insight:str}}`



`0191-m-universal-deployment-readiness-validation.md`



[Universal Deployment Readiness Validation] Confirm the internally validated insight's maximal universality, clarity, immediate applicability, and actionability across diverse contexts. Package the final validated insight statement and its corresponding justification into the final output format. `{role=deployment_validator; input=[internally_validated_insight:str, selected_nexus:dict]; process=[assess_cross_context_applicability_and_robustness(), confirm_maximal_clarity_and_unambiguity(), verify_potential_for_immediate_actionability_or_understanding(), package_final_insight_and_justification()]; constraints=[reject_if_any_ambiguity_or_context_dependency_remains(), final_output_must_match_defined_format()]; requirements=[ensure_final_insight_is_fully_deployment_ready_for_any_context(), deliver_the_single_validated_insight_and_its_justification_as_specified_in_step_a()]; output={validated_universal_insight:str, justification:str}}`

## Message 5

**Crafting the flow**

I’m piecing together a streamlined sequence for the items, aiming for enhanced outcomes while avoiding generic additions.

**Strategizing the sequence**

I'm organizing the instructions sequentially, prioritizing overall cohesion and final validation, while trimming any extra details.

**Mapping relationships** 

I'm detailing the steps, including mapping relationships and discovering mechanisms. The `0191-a` and `0191-b` items seem redundant and might need integration with other steps.

**Eliminating redundancy**

I noticed that items `0191-a`, `0191-b`, and `0191-c` are redundant. Streamlining to a single, refined sequence, I opted to retain `0191-d` to `0191-m` for their clarity and progression.

**This sequence blends**

I’m pulling together a sequence that replicates `0191-a` and `0191-b` while merging `0191-c` and `0191-e` to create a more cohesive sequence.

**Determining optimal sequence**

Wondering if `0191-a` and `0191-b` can be combined into one, and whether `0191-c` and `0191-e` should precede `0191-d` for a smoother flow.

**Deciding on the pipeline**

I’m comparing `0191-c` and `0191-e` to see if they should lead into `0191-d`. It’s interesting to reflect on their sequence integration.

**Piecing it together**

I’m figuring out the sequence for `0191-d`, `0191-e`, and `0191-f` to boost consistency and value. Considering renaming or combining steps to streamline the process.

**Setting pipeline priorities**

I'm thinking `0191-a` and `0191-b` should be top priorities, merging into a cohesive pipeline. The sequence should be `0191-a`/`0191-b`, then `0191-e` and beyond, for optimal flow and value.

**Refining the sequence**

I'm curious about how `0191-a` and `0191-b` might fit before or after other steps, like `0191-d`, to streamline the process and enhance effectiveness.

**Weighing simplicity vs. advancement**

Considering the user’s specific input, opting for `0191-c` as an earlier or simplified version of `0191-e` might be the most efficient approach.

**Weighing options**

Considering removing `0191-c` since `0191-e` offers a more thorough approach to input decomposition, streamlining and refining the pipeline for better consistency and value.

**Piecing it together**

OK, I’m figuring out how to merge `0191-c` and `0191-e` into one step, making the pipeline more efficient. This could keep the final sequence concise and impactful.

**Creating cohesive steps**

Piecing together a nine-step pipeline, including steps `0191-a` and `0191-b`, while maintaining the overall structure's cohesion and effectiveness.

**Balancing steps**

I'm working through whether to start with `0191-c` or `0191-e`, considering their roles in conceptual inventory creation and input decomposition. Progressing through these stages enriches the pipeline.

**Weighing thoroughness**

Evaluating if `0191-e` should replace `0191-c` due to its broader approach, including identifying implicit assumptions, thereby potentially enhancing the inventory creation process.

**Weighing options**

Reviewing all steps, `0191-d` and `0191-c` seem crucial, but I’m unsure about adding them outright without further refinement.

**Streamlining the pipeline**

I’m thinking `0191-e` is the way to go, simplifying conceptual inventory creation by focusing on input decomposition. It’s interesting to see a more efficient path forward.

**Revising the approach**

I’m concluding that `0191-d` and `0191-e` form a solid conceptual inventory creation, while `0191-m` and `0191-a` should remain as alternative macro templates.

**Piecing together the sequence**

I’m organizing the final pipeline into a 10-step process, focusing on conceptual inventory creation from input decomposition to deployment readiness, while keeping it concise and valuable.

## Message 6

Below is a streamlined **logical flow** that preserves each file’s function while removing redundancies and ensuring maximum clarity and consistency. The sequence starts by **initiating** the process (Step D), then **decomposing** the input (Steps C and E), **mapping** explicit/implicit relationships (Steps F and G), **evaluating** potential insights (Steps H and I), **condensing/refining** the single most powerful insight (Steps J and K), and **validating** its fidelity and readiness (Steps L and M). Finally, the two “universal singularity insight extractor” templates (A and B) are placed at the end as **all-in-one** or **aggregated** approaches.

---

1. **0191-d-universal-sequence-initiation.md**  
   - *“Universal Sequence Initiation”*  
   - Establishes the single-insight goal and final output structure.

2. **0191-c-conceptual inventory creator.md**  
   - *“Universal Conceptual Inventory Creator”*  
   - Breaks the input into an essential conceptual inventory (concepts, statements, relationships).

3. **0191-e-universal-input-decomposition.md**  
   - *“Universal Input Decomposition”*  
   - Further decomposes the input’s core pieces (including implicit assumptions), producing a raw inventory.

4. **0191-f-universal-explicit-relationship-mapping.md**  
   - *“Universal Explicit Relationship Mapping”*  
   - Maps all explicitly stated connections, dependencies, and causal links among components.

5. **0191-g-universal-implicit-mechanism-discovery.md**  
   - *“Universal Implicit Mechanism Discovery”*  
   - Uncovers deeper operational mechanisms, feedback loops, and causal engines not directly stated.

6. **0191-h-universal-potential-insight-evaluation.md**  
   - *“Universal Potential Insight Evaluation”*  
   - Reviews all significant components/mechanisms, ranks their explanatory power, novelty, universality.

7. **0191-i-universal-singular-nexus-selection.md**  
   - *“Universal Singular Nexus Selection”*  
   - Chooses the single highest-ranked mechanism or relationship, with explicit justification.

8. **0191-j-universal-core-meaning-condensation.md**  
   - *“Universal Core Meaning Condensation”*  
   - Strips contextual specifics from the chosen nexus, yielding a tightly focused core statement.

9. **0191-k-universal-universal-articulation-refinement.md**  
   - *“Universal Universal Articulation Refinement”*  
   - Refines that condensed statement into a potent, jargon-free, universally relevant insight.

10. **0191-l-universal-internal-fidelity-&-quality-validation.md**  
   - *“Universal Internal Fidelity & Quality Validation”*  
   - Cross-checks the refined insight against the logic map and justification for accuracy, clarity, and impact.

11. **0191-m-universal-deployment-readiness-validation.md**  
   - *“Universal Deployment Readiness Validation”*  
   - Finalizes and packages the validated insight + justification, ensuring cross-context applicability.

12. **0191-a-universal-singularity-insight-extractor.md**  
   - *“Universal Singularity Insight Extractor (Role-Chained Protocol)”*  
   - An all-in-one, step-chained template that encompasses the entire process above in a single directive.

13. **0191-b-universal-singularity-insight-extractor.md**  
   - *“Universal Singularity Insight Extractor (Expanded Protocol)”*  
   - A slightly more detailed, all-in-one variant that enforces intermediate output visibility and final justification.

---

This ordering **initiates** and **defines** the process first, then **systematically** decomposes, analyzes, and validates the single most powerful insight, and concludes with two self-contained templates (A and B) for scenarios where a single directive should encapsulate the entire workflow.
