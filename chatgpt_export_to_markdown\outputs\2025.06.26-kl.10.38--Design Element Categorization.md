# Design Element Categorization

Conversation ID: 685d06f0-3bc4-8008-ae1f-c51bb0e8d316

## Message 1

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-5NE8A9LtD5ki1cP7uRkbtF', 'size_bytes': 176755, 'width': 869, 'height': 1238, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
if you were going to categorize and group these into design elements (such that you can control each "type" individually through a few parameters while retaining flexibility for design changes)

## Message 2

Below is a “Synthesist-style” map of the visual building blocks I can spot in the contract.  
For each block-type I note (1) its role in the document, (2) the design levers you’d likely want to expose, and (3) optional variants you might want to keep in mind for future tweaks.

---

## 1. Global Canvas
| Element | Purpose | Key Parameters | Typical Defaults |
|---------|---------|----------------|------------------|
| **Page Frame / Grid** | Defines margins, column widths, baseline grid | `page_margin`, `column_count`, `column_gap`, `baseline_spacing` | 25 mm margin, single column, 12 pt baseline |
| **Global Typeface Stack** | Ensures hierarchy is coherent | `font_family_primary`, `font_family_secondary` | Sans for headings, Serif for body |

---

## 2. Brand Header Cluster
| Sub-Element | Why It Exists | Controls | Notes |
|-------------|--------------|----------|-------|
| **Company Name** (“Ringerike Landskap AS”) | Primary brand signal | `heading_font_weight`, `brand_color`, `font_size_lg`, `text_transform` | Usually bold, brand-green, centered |
| **Org-ID & Address Block** | Legal identification | `font_size_sm`, `tracking`, `text_align` | Often lighter weight |
| **Document Title** (“ARBEIDSKONTRAKT”) | Declares doc type | `font_size_xl`, `letter_spacing`, `title_case` | All-caps, same brand color |

*Treat this trio as a **Header Component** so you can adjust logo/branding rules once and propagate.*

---

## 3. Structural Dividers
| Element | Purpose | Controls |
|---------|---------|----------|
| **Horizontal Rule** (teal line under title) | Visual reset between header and content | `rule_color`, `rule_thickness`, `rule_margin_top/bottom` |

---

## 4. Section Heading Block
| Element | Purpose | Key Parameters |
|---------|---------|----------------|
| **Numbered Section Title** (“1. PARTENES IDENTITET”) | Navigation & hierarchy | `section_number_style`, `font_size_md`, `font_weight`, `section_color` |

*Encapsulate as **SectionHeading** so updating color or numbering format updates all.*

---

## 5. Field-Label Pair
| Sub-Element | Purpose | Controls | Variants |
|-------------|---------|----------|----------|
| **Field Label** (“Arbeidsgiver:”) | Identifies data point | `label_font_weight`, `label_color`, `after_colon_spacing` | May support right-align for tight tables |
| **Field Value** (text or blank line) | Holds data | `value_font_style`, `value_color`, `underline_thickness/spacing` | Could switch between plain text, form underline, or input box |

---

## 6. Paragraph / Body Copy
| Element | Purpose | Parameters |
|---------|---------|------------|
| **Body Text** | Explanatory sentences (e.g., place-based description of work) | `body_font_size`, `leading`, `paragraph_spacing` |

---

## 7. Key-Data Rows (Compensation Table)
Although visually similar to “Field-Label Pair,” these often group multiple subfields (e.g., Timelønn, Overtidstillegg).  
Consider a **KeyValueList** component with:

* `item_spacing`
* `value_emphasis` (normal vs bold)
* `indent_level` for nested bullets

---

## 8. Lists & Bullet Rules
| Element | Purpose | Controls |
|---------|---------|----------|
| **Bulleted/Numbered List** | Enumerates conditions (e.g., “Pauser:…”) | `bullet_style`, `indent`, `list_spacing` |

---

## 9. Blank-Input Affordances
| Element | Purpose | Controls |
|---------|---------|----------|
| **Underline Input** (e.g., “___________________”) | Signals user to fill | `underline_length`, `stroke_thickness`, `offset_from_text` |

---

## 10. Footers / Page Numbers *(not in screenshot but often required)*
Prepare a **FooterComponent** with:

* `footer_font_size`
* `footer_align`
* `page_x_of_y_toggle`

---

### How to Parameterize Efficiently
1. **Tokenize Colors** – e.g., `--brand-primary`, `--text-secondary`, `--line-muted`.  
2. **Type Scale** – define a modular scale (e.g., 12 pt base × 1.2).  
3. **Spacing System** – use multiples of a base unit (4 pt or 8 pt) for margin/padding.  
4. **Component Props** – expose only what truly varies:  
   * `SectionHeading(level, text)`  
   * `Field(label, value, variant="underline" | "static")`  
   * `KeyValueList(items, style="compact" | "table")`  

With these buckets you can redesign the contract—switch fonts, go monochrome, resize for A5 vs A4—by tweaking a handful of theme variables while each component stays semantically intact.

---  

*Role Acknowledgment*: As your Synthesist, I’ve decomposed the layout into modular, hierarchical elements, giving you clear levers for style evolution while preserving the information architecture.

## Message 3

Lets split it into as few parameters as possible (e.g. all identical "types" can be controlled with a single param)

## Message 4

### 🗺️ Ultra-Lean Design Control Map  
To keep the contract fully theme-able with **_as few knobs as possible_**, boil everything down to **six “master tokens.”** Every visual decision in the document is either one of these tokens **or a deterministic rule derived from them.**

| # | Master Token | What It Directly Sets | Key Elements Affected |
|---|--------------|-----------------------|-----------------------|
| **T1 `brand_color`** | Primary accent hue | Company name, document title, section numbers, horizontal rules | Header cluster, all section headings, rule lines |
| **T2 `text_color`** | Neutral/legible copy tone | Body copy, labels, values | All paragraphs, label/value pairs, address lines |
| **T3 `font_family`** | Global typeface stack | Every piece of text | Headings, body, labels—one stack keeps optical size harmony |
| **T4 `font_base`** *(pt)* | Base text size | Body size = `font_base` ; other sizes = scale×base | Body 12 pt → Heading Level 1 = `font_base`×1.6, etc. |
| **T5 `space_unit`** *(pt)* | Baseline & margin grid | Vertical rhythm, indent, grid gaps | Paragraph spacing = `1×space_unit`, Section gap = `3×space_unit` |
| **T6 `stroke_weight`** *(pt)* | Thickness for all lines | Horizontal rules, underline inputs | Rule under header, signature lines, blank-fill underlines |

> **Everything else is a rule, not a parameter.**  
> Example: “Underline length” = full column width minus `2×space_unit`; “Heading weight” = `700` (constant); “Brand line under header” uses `stroke_weight` and `brand_color`.

---

#### 📐 How Each Document Part Resolves From the Tokens

| Element Type | Size Rule | Color Rule | Spacing Rule |
|--------------|-----------|------------|--------------|
| **Company Name** | `font_base × 1.8`, bold | `brand_color` | Margin-bottom = `space_unit` |
| **Org-ID / Address** | `font_base × 0.85` | `text_color` (60 % opacity) | Stack gap = `0.5×space_unit` |
| **Document Title** | `font_base × 1.4`, letter-spaced | `brand_color` | Margin-bot = `space_unit` |
| **Section Heading** | `font_base × 1.1`, bold | `brand_color` | Top gap = `2×space_unit` |
| **Body Paragraph** | `font_base`, normal | `text_color` | Para gap = `space_unit` |
| **Label / Value** | Label: `bold`; Value: `normal` | Both `text_color` | Row gap = `0.5×space_unit` |
| **Rule / Underline** | Height =`stroke_weight` | `brand_color` (rules) / `text_color` 50 % (underlines) | Margin =`space_unit` |

---

### Why Six (and Not Fewer)?
1. **Two Colors** prevent accessibility clashes (all-accent or all-gray fails WCAG).  
2. **One Font Family** keeps rhythm simple while still letting you swap an entire stack.  
3. **Base Font & Spacing Units** make every other value responsive (change once, scale everywhere).  
4. **Stroke Weight** separates text sizing from line clarity (thin type ≠ thin rules).

Together these tokens can restyle the contract from corporate teal on A4 to monochrome letter-size by adjusting **six values**—yet every “identical type” (all section headers, all rules, all body copy) updates in lock-step.

---

*Role Acknowledgment*: Synthesist here—compressed the full hierarchy into the minimal controllable token set while preserving clear derivation rules, so you can tune the entire contract with six parameters and still keep full design flexibility.

## Message 5

please apply the concepts to this pdf-generation form:

    # Dir `Meta`



    ### File Structure



    ```

    ├── ArbeidskontraktGenerator.tsx

    ├── ContractPDFTemplate.tsx

    ├── MetaErrorBoundary.tsx

    ├── MetaRouter.tsx

    ├── README.md

    ├── index.tsx

    └── steps

        ├── AdvancedSettingsStep.tsx

        ├── BasicInfoStep.tsx

        └── ContractGenerationStep.tsx

    ```



    ---



    #### `ArbeidskontraktGenerator.tsx`



    ```tsx

        import { useState } from 'react';

        import { Card, Container } from '@/ui';

        import { CheckCircle, FileText, User, Settings } from 'lucide-react';

        import { ContractFormData, initialContractFormData } from '@/lib/meta/types';

        import BasicInfoStep from './steps/BasicInfoStep';

        import AdvancedSettingsStep from './steps/AdvancedSettingsStep';

        import ContractGenerationStep from './steps/ContractGenerationStep';



        const ArbeidskontraktGenerator = () => {

          const [currentStep, setCurrentStep] = useState(1);

          const [formData, setFormData] = useState<ContractFormData>(initialContractFormData);



          const updateFormData = (updates: Partial<ContractFormData>) => {

            setFormData((prev: ContractFormData) => ({ ...prev, ...updates }));

          };



          const nextStep = () => {

            if (currentStep < 3) {

              setCurrentStep(currentStep + 1);

            }

          };



          const prevStep = () => {

            if (currentStep > 1) {

              setCurrentStep(currentStep - 1);

            }

          };



          const goToStep = (step: number) => {

            setCurrentStep(step);

          };



          const steps = [

            {

              number: 1,

              title: "Grunnleggende informasjon",

              description: "Personopplysninger og stillingsinformasjon",

              icon: User,

              completed: currentStep > 1

            },

            {

              number: 2,

              title: "Avanserte innstillinger",

              description: "Bedriftsinformasjon og arbeidsvilkÃ¥r",

              icon: Settings,

              completed: currentStep > 2

            },

            {

              number: 3,

              title: "Kontraktgenerering",

              description: "Sammendrag og generering av kontrakt",

              icon: FileText,

              completed: false

            }

          ];



          const renderStep = () => {

            switch (currentStep) {

              case 1:

                return (

                  <BasicInfoStep

                    formData={formData}

                    updateFormData={updateFormData}

                    onNext={nextStep}

                  />

                );

              case 2:

                return (

                  <AdvancedSettingsStep

                    formData={formData}

                    updateFormData={updateFormData}

                    onNext={nextStep}

                    onPrev={prevStep}

                  />

                );

              case 3:

                return (

                  <ContractGenerationStep

                    formData={formData}

                    updateFormData={updateFormData}

                    onPrev={prevStep}

                  />

                );

              default:

                return null;

            }

          };



          return (

            <Container>

              <div className="max-w-6xl mx-auto py-8 sm:py-12">

                {/* Header */}

                <div className="text-center mb-6 sm:mb-8">

                  <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2">

                    Arbeidskontrakt Generator

                  </h1>

                  <p className="text-gray-600 text-sm sm:text-base px-2">

                    Generer juridisk korrekte arbeidskontrakter for Ringerike Landskap AS

                  </p>

                </div>



                {/* Progress Steps */}

                <Card title="" className="mb-6 sm:mb-8">

                  <div className="p-4 sm:p-6">

                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">

                      {steps.map((step, index) => {

                        const IconComponent = step.icon;

                        const isActive = currentStep === step.number;

                        const isCompleted = step.completed;



                        return (

                          <div key={step.number} className="flex items-center mb-4 sm:mb-0">

                            {/* Step Circle */}

                            <button

                              onClick={() => goToStep(step.number)}

                              className={`

                                flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 transition-colors flex-shrink-0

                                ${isActive

                                  ? 'bg-green-600 border-green-600 text-white'

                                  : isCompleted

                                    ? 'bg-green-100 border-green-600 text-green-600'

                                    : 'bg-gray-100 border-gray-300 text-gray-400'

                                }

                              `}

                            >

                              {isCompleted ? (

                                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6" />

                              ) : (

                                <IconComponent className="h-5 w-5 sm:h-6 sm:w-6" />

                              )}

                            </button>



                            {/* Step Info */}

                            <div className="ml-3 sm:ml-4 text-left">

                              <div className={`text-sm font-medium ${isActive ? 'text-green-600' : 'text-gray-900'}`}>

                                Steg {step.number}

                              </div>

                              <div className={`text-xs sm:text-sm ${isActive ? 'text-green-600' : 'text-gray-600'}`}>

                                {step.title}

                              </div>

                            </div>



                            {/* Connector Line - only on desktop */}

                            {index < steps.length - 1 && (

                              <div className={`

                                hidden sm:block flex-1 h-0.5 mx-4 lg:mx-6

                                ${step.completed ? 'bg-green-600' : 'bg-gray-300'}

                              `} />

                            )}

                          </div>

                        );

                      })}

                    </div>



                    {/* Progress Bar */}

                    <div className="w-full bg-gray-200 rounded-full h-2">

                      <div

                        className="bg-green-600 h-2 rounded-full transition-all duration-300"

                        style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}

                      />

                    </div>

                  </div>

                </Card>



                {/* Step Content */}

                <div className="min-h-[400px] sm:min-h-[600px]">

                  {renderStep()}

                </div>

              </div>

            </Container>

          );

        };



        export default ArbeidskontraktGenerator;

    ```



    ---



    #### `ContractPDFTemplate.tsx`



    ```tsx

        import React from 'react';

        import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';

        import { ContractFormData } from '@/lib/meta/types';



        interface ContractData {

          companyName: string;

          companyOrgNr: string;

          companyAddress: string;

          employeeName: string;

          employeeBirthDate: string;

          employeeAddress: string;

          position: string;

          positionDescription: string;

          startDate: string;

          employmentType: string;

          probationPeriod: string;

          workHours: string;

          breakTime: string;

          hourlyRate: string;

          overtimeRate: string;

          paymentDate: string;

          travelAllowance: string;

          vacationDays: string;

          vacationPay: string;

          sickPay: string;

          noticePeriod: string;

          terminationRules: string;

          pensionProvider: string;

          workInsurance: string;

          tariffAgreement: string;

          competenceDevelopment: string;

          legalReference: string;

          contractDate: string;

        }



        interface ContractPDFProps {

          formData: ContractFormData;

        }



        const styles = StyleSheet.create({

          page: {

            flexDirection: 'column',

            backgroundColor: '#ffffff',

            padding: 30,

            fontSize: 11,

            lineHeight: 1.4,

          },

          header: {

            textAlign: 'center',

            marginBottom: 24,

            paddingBottom: 16,

            borderBottomWidth: 2,

            borderBottomColor: '#059669',

          },

          companyName: {

            fontSize: 18,

            fontWeight: 'bold',

            color: '#059669',

            marginBottom: 5,

          },

          title: {

            fontSize: 16,

            fontWeight: 'bold',

            color: '#374151',

            marginTop: 15,

          },

          section: {

            marginBottom: 16,

            breakInside: 'avoid',

          },

          sectionTitle: {

            fontSize: 13,

            fontWeight: 'bold',

            color: '#059669',

            marginBottom: 10,

            paddingBottom: 3,

            borderBottomWidth: 1,

            borderBottomColor: '#d1d5db',

            breakAfter: 'avoid',

          },

          row: {

            flexDirection: 'row',

            justifyContent: 'space-between',

            marginBottom: 15,

            breakInside: 'avoid',

          },

          column: {

            flex: 1,

            paddingRight: 20,

          },

          label: {

            fontWeight: 'bold',

            marginBottom: 2,

          },

          text: {

            marginBottom: 5,

          },

          legalText: {

            fontSize: 9,

            color: '#6b7280',

            marginBottom: 20,

          },

          signatureSection: {

            flexDirection: 'row',

            justifyContent: 'space-between',

            marginTop: 40,

            paddingTop: 20,

            borderTopWidth: 1,

            borderTopColor: '#d1d5db',

          },

          signatureBox: {

            width: '45%',

            textAlign: 'center',

          },

          signatureLine: {

            borderTopWidth: 1,

            borderTopColor: '#9ca3af',

            paddingTop: 5,

            marginBottom: 10,

            height: 30,

          },

          keepTogether: {

            breakInside: 'avoid',

            orphans: 3,

            widows: 3,

          },

        });



        // Helper function to transform form data to contract data

        const transformFormData = (formData: ContractFormData): ContractData => {

          const formatDate = (dateString: string) => {

            if (!dateString) return '__.__.__';

            const date = new Date(dateString);

            return date.toLocaleDateString('no-NO', {

              day: '2-digit',

              month: '2-digit',

              year: 'numeric'

            });

          };



          return {

            companyName: formData.companyName || 'Ringerike Landskap AS',

            companyOrgNr: formData.companyOrgNumber || '***********',

            companyAddress: formData.companyAddress || 'Birchs vei 7, 3530 Røyse',

            employeeName: formData.employeeName || '________________________________',

            employeeBirthDate: formatDate(formData.employeeBirthDate),

            employeeAddress: formData.employeeAddress || '________________________________',

            position: formData.position || '________________________________',

            positionDescription: 'Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten',

            startDate: formatDate(formData.startDate),

            employmentType: formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse',

            probationPeriod: formData.probationPeriod ? `${formData.probationMonths || 6} måneder med 14 dagers gjensidig oppsigelsesfrist` : 'Ingen prøvetid',

            workHours: `${formData.workingHoursPerWeek || 37.5} timer per uke, normalt ${formData.workingTime || '07:00-15:00'}`,

            breakTime: formData.breakTime || 'Minst 30 min. ubetalt pause ved arbeidsdag >5,5 t',

            hourlyRate: `kr ${formData.hourlyRate || '___'},-`,

            overtimeRate: `${formData.overtimeRate || 40}% av timelønn`,

            paymentDate: `Den ${formData.paymentDay || 5}. hver måned til kontonummer ${formData.accountNumber || '____.____.__.____'}`,

            travelAllowance: formData.travelAllowance || 'Statens gjeldende satser (pt. 3,50 kr/km)',

            vacationDays: '5 uker per år i henhold til ferieloven',

            vacationPay: '12% av feriepengegrunnlaget',

            sickPay: 'Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom',

            noticePeriod: formData.noticePeriod || '1 måned gjensidig etter prøvetid',

            terminationRules: formData.notificationRules || 'Endringer varsles minimum 2 uker i forveien der mulig',

            pensionProvider: `${formData.pensionProvider || 'Storebrand'} (org.nr ${formData.pensionOrgNumber || '958 995 369'})`,

            workInsurance: `${formData.insuranceProvider || 'Gjensidige Forsikring ASA'} (org.nr ${formData.insuranceOrgNumber || '995 568 217'})`,

            tariffAgreement: 'Ingen tariffavtale er gjeldende per dags dato',

            competenceDevelopment: 'Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen',

            legalReference: `Denne kontrakten er utarbeidet i henhold til Arbeidsmiljøloven § 14-6 og oppfyller alle juridiske krav per ${new Date().toLocaleDateString('no-NO')}.`,

            contractDate: new Date().toLocaleDateString('no-NO'),

          };

        };



        const ContractPDF: React.FC<ContractPDFProps> = ({ formData }) => {

          const data = transformFormData(formData);



          return (

            <Document>

              <Page size="A4" style={styles.page}>

                {/* Header */}

                <View style={styles.header}>

                  <Text style={styles.companyName}>{data.companyName}</Text>

                  <Text>Org.nr: {data.companyOrgNr}</Text>

                  <Text>{data.companyAddress}</Text>

                  <Text style={styles.title}>ARBEIDSKONTRAKT</Text>

                </View>



                {/* Section 1: Party Identity */}

                <View style={styles.section}>

                  <Text style={styles.sectionTitle}>1. PARTENES IDENTITET</Text>

                  <View style={styles.row}>

                    <View style={styles.column}>

                      <Text style={styles.label}>Arbeidsgiver:</Text>

                      <Text style={styles.text}>{data.companyName}</Text>

                      <Text style={styles.label}>Org.nr:</Text>

                      <Text style={styles.text}>{data.companyOrgNr}</Text>

                      <Text style={styles.label}>Adresse:</Text>

                      <Text style={styles.text}>{data.companyAddress}</Text>

                    </View>

                    <View style={styles.column}>

                      <Text style={styles.label}>Arbeidstaker:</Text>

                      <Text style={styles.text}>{data.employeeName}</Text>

                      <Text style={styles.label}>Fødselsdato:</Text>

                      <Text style={styles.text}>{data.employeeBirthDate}</Text>

                      <Text style={styles.label}>Adresse:</Text>

                      <Text style={styles.text}>{data.employeeAddress}</Text>

                    </View>

                  </View>

                </View>



                {/* Section 2: Work Location and Tasks */}

                <View style={styles.section}>

                  <Text style={styles.sectionTitle}>2. ARBEIDSSTED OG ARBEIDSOPPGAVER</Text>

                  <Text style={styles.label}>Arbeidssted:</Text>

                  <Text style={styles.text}>Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt</Text>

                  <Text style={styles.label}>Stillingsbetegnelse:</Text>

                  <Text style={styles.text}>{data.position}</Text>

                  <Text style={styles.label}>Arbeidsoppgaver:</Text>

                  <Text style={styles.text}>{data.positionDescription}</Text>

                </View>



                {/* Section 3: Employment Terms */}

                <View style={styles.section}>

                  <Text style={styles.sectionTitle}>3. ANSETTELSESFORHOLD</Text>

                  <Text style={styles.text}><Text style={styles.label}>Tiltredelsesdato:</Text> {data.startDate}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Ansettelsestype:</Text> {data.employmentType}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Prøvetid:</Text> {data.probationPeriod}</Text>

                </View>



                {/* Section 4: Work Time and Salary */}

                <View style={styles.section}>

                  <Text style={styles.sectionTitle}>4. ARBEIDSTID OG LØNN</Text>

                  <Text style={styles.text}><Text style={styles.label}>Arbeidstid:</Text> {data.workHours}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Pauser:</Text> {data.breakTime}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Timelønn:</Text> {data.hourlyRate}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Overtidstillegg:</Text> {data.overtimeRate}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Utbetaling:</Text> {data.paymentDate}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Kjøregodtgjørelse:</Text> {data.travelAllowance}</Text>

                </View>



                {/* Section 5: Vacation and Leave */}

                <View style={[styles.section, styles.keepTogether]}>

                  <Text style={styles.sectionTitle}>5. FERIE OG PERMISJON</Text>

                  <Text style={styles.text}><Text style={styles.label}>Ferie:</Text> {data.vacationDays}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Feriepenger:</Text> {data.vacationPay}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Sykepenger:</Text> {data.sickPay}</Text>

                </View>



                {/* Section 6: Notice and Termination */}

                <View style={[styles.section, styles.keepTogether]}>

                  <Text style={styles.sectionTitle}>6. OPPSIGELSE OG ENDRINGER</Text>

                  <Text style={styles.text}><Text style={styles.label}>Oppsigelsesfrister:</Text> {data.noticePeriod}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Varslingsregler:</Text> {data.terminationRules}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Formkrav:</Text> Oppsigelse skal være skriftlig</Text>

                </View>



                {/* Section 7: Pension and Insurance */}

                <View style={[styles.section, styles.keepTogether]}>

                  <Text style={styles.sectionTitle}>7. PENSJON OG FORSIKRING</Text>

                  <Text style={styles.text}><Text style={styles.label}>Pensjon:</Text> {data.pensionProvider}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Yrkesskadeforsikring:</Text> {data.workInsurance}</Text>

                </View>



                {/* Section 8: Other Terms */}

                <View style={[styles.section, styles.keepTogether]}>

                  <Text style={styles.sectionTitle}>8. ØVRIGE BESTEMMELSER</Text>

                  <Text style={styles.text}><Text style={styles.label}>Tariffavtale:</Text> {data.tariffAgreement}</Text>

                  <Text style={styles.text}><Text style={styles.label}>Kompetanseutvikling:</Text> {data.competenceDevelopment}</Text>

                </View>



                {/* Legal Reference */}

                <Text style={styles.legalText}>{data.legalReference}</Text>



                {/* Signature Section */}

                <View style={[styles.signatureSection, styles.keepTogether]}>

                  <View style={styles.signatureBox}>

                    <View style={styles.signatureLine}></View>

                    <Text>Dato: {data.contractDate}</Text>

                    <Text style={styles.label}>Arbeidsgiver</Text>

                    <Text>{data.companyName}</Text>

                  </View>

                  <View style={styles.signatureBox}>

                    <View style={styles.signatureLine}></View>

                    <Text>Dato: {data.contractDate}</Text>

                    <Text style={styles.label}>Arbeidstaker</Text>

                    <Text>{data.employeeName}</Text>

                  </View>

                </View>

              </Page>

            </Document>

          );

        };



        export default ContractPDF;

    ```



    ---



    #### `MetaErrorBoundary.tsx`



    ```tsx

        import { Component, ErrorInfo, ReactNode } from 'react';

        import { Container, Card, Button } from '@/ui';

        import { AlertTriangle, Home, RefreshCw } from 'lucide-react';

        import { Link } from 'react-router-dom';



        interface Props {

          children: ReactNode;

          fallbackTitle?: string;

          fallbackMessage?: string;

        }



        interface State {

          hasError: boolean;

          error?: Error;

          errorInfo?: ErrorInfo;

        }



        /**

         * Error Boundary specifically for Meta Utilities

         * Prevents meta utility errors from crashing the main website

         */

        class MetaErrorBoundary extends Component<Props, State> {

          constructor(props: Props) {

            super(props);

            this.state = { hasError: false };

          }



          static getDerivedStateFromError(error: Error): State {

            // Update state so the next render will show the fallback UI

            return { hasError: true, error };

          }



          componentDidCatch(error: Error, errorInfo: ErrorInfo) {

            // Log error to console in development

            if (process.env.NODE_ENV === 'development') {

              console.error('Meta Utility Error:', error);

              console.error('Error Info:', errorInfo);

            }



            // Update state with error details

            this.setState({

              error,

              errorInfo

            });



            // In production, you might want to log this to an error reporting service

            // Example: logErrorToService(error, errorInfo);

          }



          handleRetry = () => {

            this.setState({ hasError: false, error: undefined, errorInfo: undefined });

          };



          render() {

            if (this.state.hasError) {

              const { fallbackTitle = "Meta Utility Error", fallbackMessage } = this.props;



              return (

                <Container>

                  <div className="max-w-2xl mx-auto py-16">

                    <Card title="" className="text-center">

                      <div className="p-8">

                        {/* Error Icon */}

                        <div className="flex justify-center mb-6">

                          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">

                            <AlertTriangle className="h-8 w-8 text-red-600" />

                          </div>

                        </div>



                        {/* Error Message */}

                        <h2 className="text-2xl font-bold text-gray-900 mb-4">

                          {fallbackTitle}

                        </h2>



                        <p className="text-gray-600 mb-6">

                          {fallbackMessage ||

                            "Det oppstod en feil med dette verktøyet. Hovednettsiden fungerer fortsatt normalt."

                          }

                        </p>



                        {/* Development Error Details */}

                        {process.env.NODE_ENV === 'development' && this.state.error && (

                          <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">

                            <h3 className="font-medium text-gray-900 mb-2">Development Error Details:</h3>

                            <pre className="text-xs text-gray-700 overflow-auto">

                              {this.state.error.toString()}

                              {this.state.errorInfo?.componentStack}

                            </pre>

                          </div>

                        )}



                        {/* Action Buttons */}

                        <div className="flex flex-col sm:flex-row gap-3 justify-center">

                          <Button

                            onClick={this.handleRetry}

                            className="flex items-center justify-center"

                          >

                            <RefreshCw className="h-4 w-4 mr-2" />

                            Prøv igjen

                          </Button>



                          <Link to="/meta">

                            <Button variant="outline" className="flex items-center justify-center w-full">

                              <Home className="h-4 w-4 mr-2" />

                              Tilbake til Meta Utilities

                            </Button>

                          </Link>



                          <Link to="/">

                            <Button variant="outline" className="flex items-center justify-center w-full">

                              <Home className="h-4 w-4 mr-2" />

                              Tilbake til hovedsiden

                            </Button>

                          </Link>

                        </div>



                        {/* Reassurance Message */}

                        <div className="mt-8 p-4 bg-green-50 rounded-lg">

                          <p className="text-sm text-green-800">

                            <strong>Viktig:</strong> Denne feilen påvirker kun dette interne verktøyet.

                            Hovednettsiden og alle andre funksjoner fungerer normalt.

                          </p>

                        </div>

                      </div>

                    </Card>

                  </div>

                </Container>

              );

            }



            return this.props.children;

          }

        }



        export default MetaErrorBoundary;

    ```



    ---



    #### `MetaRouter.tsx`



    ```tsx

        import { Suspense, lazy } from 'react';

        import { Routes, Route, Navigate } from 'react-router-dom';

        import { Loading } from '@/ui/Loading';

        import MetaErrorBoundary from './MetaErrorBoundary';



        // Lazy load meta utility components to prevent them from affecting main bundle

        const MetaIndexPage = lazy(() => import('@/pages/meta/MetaIndexPage'));

        const LogoPage = lazy(() => import('@/pages/meta/LogoPage'));

        const ArbeidskontraktPage = lazy(() => import('@/pages/meta/ArbeidskontraktPage'));



        /**

         * Isolated router for Meta Utilities

         * Provides error boundaries and lazy loading to protect the main site

         */

        const MetaRouter = () => {

          return (

            <MetaErrorBoundary

              fallbackTitle="Meta Utilities Error"

              fallbackMessage="Det oppstod en feil med meta utilities systemet. Dette pÃ¥virker ikke hovednettsiden."

            >

              <Suspense fallback={<Loading />}>

                <Routes>

                  {/* Meta utilities index */}

                  <Route index element={<MetaIndexPage />} />



                  {/* Individual meta utilities */}

                  <Route

                    path="logo"

                    element={

                      <MetaErrorBoundary

                        fallbackTitle="Logo Generator Error"

                        fallbackMessage="Det oppstod en feil med logo generatoren."

                      >

                        <LogoPage />

                      </MetaErrorBoundary>

                    }

                  />



                  <Route

                    path="arbeidskontrakt"

                    element={

                      <MetaErrorBoundary

                        fallbackTitle="Arbeidskontrakt Generator Error"

                        fallbackMessage="Det oppstod en feil med arbeidskontrakt generatoren."

                      >

                        <ArbeidskontraktPage />

                      </MetaErrorBoundary>

                    }

                  />



                  {/* Catch-all redirect to meta index */}

                  <Route path="*" element={<Navigate to="/meta" replace />} />

                </Routes>

              </Suspense>

            </MetaErrorBoundary>

          );

        };



        export default MetaRouter;

    ```



    ---



    #### `README.md`



    ```markdown

        # Meta Components



        This directory contains components for the Meta Utilities system - internal tools for Ringerike Landskap AS.



        ## Architecture



        The Meta components are **architecturally isolated** from the main website to ensure stability and prevent coupling.



        ## Components



        ### Core Components

        - **`MetaRouter.tsx`** - Isolated router for all meta utilities with lazy loading

        - **`MetaErrorBoundary.tsx`** - Error boundary to prevent meta utility failures from affecting main site



        ### Utility Components

        - **`ArbeidskontraktGenerator.tsx`** - Employment contract generator

        - **`steps/`** - Multi-step wizard components for contract generation



        ## Design Principles



        1. **Isolation** - Meta utilities cannot affect main website functionality

        2. **Error Boundaries** - Graceful degradation when utilities fail

        3. **Lazy Loading** - Components loaded only when needed

        4. **Type Safety** - Uses isolated type system from `@/lib/meta/types`



        ## Usage



        Meta components are accessed through the isolated router:

        ```typescript

        // In main app router

        <Route path="/meta/*" element={<MetaRouter />} />

        ```



        Individual utilities are wrapped in error boundaries:

        ```typescript

        <MetaErrorBoundary fallbackTitle="Tool Error">

          <UtilityComponent />

        </MetaErrorBoundary>

        ```



        ## Adding New Meta Utilities



        1. Create component in this directory

        2. Add types to `@/lib/meta/types`

        3. Add route to `MetaRouter.tsx`

        4. Wrap in `MetaErrorBoundary`

        5. Test error scenarios



        ## Import Patterns



        Follow established codebase conventions:

        ```typescript

        // External dependencies

        import { useState } from 'react';



        // UI components (barrel imports)

        import { Card, Button } from '@/ui';



        // Form components

        import { Input, Select } from '@/ui/Form';



        // Meta types (isolated)

        import { ContractFormData } from '@/lib/meta/types';



        // Local components (relative)

        import LocalComponent from './LocalComponent';

        ```

    ```



    ---



    #### `index.tsx`



    ```tsx

        /**

         * Meta Component

         *

         * This component is used to set metadata for SEO.

         */



        interface MetaProps {

          title: string;

          description: string;

          keywords?: string[];

          image?: string;

          schema?: any;

        }



        export const Meta = ({ title, description, keywords, image, schema }: MetaProps) => {

          // This is a mock implementation since we can't actually modify the document head in this example

          return (

            <>

              {/* This would normally update the document head */}

              {/* For demonstration purposes only */}

              <div style={{ display: 'none' }} data-testid="meta-component">

                <div data-meta="title">{title}</div>

                <div data-meta="description">{description}</div>

                {keywords && <div data-meta="keywords">{keywords.join(', ')}</div>}

                {image && <div data-meta="image">{image}</div>}

                {schema && <div data-meta="schema">{JSON.stringify(schema)}</div>}

              </div>

            </>

          );

        };



        export default Meta;

    ```



    ---



    #### `steps\AdvancedSettingsStep.tsx`



    ```tsx

        import { Card, Button } from '@/ui';

        import { Input, Select, Textarea } from '@/ui/Form';

        import { ContractStepProps, paymentDayOptions } from '@/lib/meta/types';

        import { ArrowLeft, ArrowRight, Building, Clock, DollarSign, Shield } from 'lucide-react';



        const AdvancedSettingsStep = ({ formData, updateFormData, onNext, onPrev }: ContractStepProps) => {

          const handleInputChange = (field: string, value: string | number) => {

            updateFormData({ [field]: value });

          };



          return (

            <div className="space-y-6">

              {/* Company Information */}

              <Card title="Bedriftsinformasjon" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <Building className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Arbeidsgiverens opplysninger</h3>

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Input

                      label="Bedriftsnavn"

                      value={formData.companyName}

                      onChange={(e) => handleInputChange('companyName', e.target.value)}

                    />



                    <Input

                      label="Organisasjonsnummer"

                      value={formData.companyOrgNumber}

                      onChange={(e) => handleInputChange('companyOrgNumber', e.target.value)}

                    />

                  </div>



                  <Input

                    label="Bedriftsadresse"

                    value={formData.companyAddress}

                    onChange={(e) => handleInputChange('companyAddress', e.target.value)}

                  />

                </div>

              </Card>



              {/* Working Hours */}

              <Card title="Arbeidstid og pauser" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <Clock className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Arbeidstidsordning</h3>

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Input

                      label="Timer per uke"

                      type="number"

                      step="0.5"

                      value={formData.workingHoursPerWeek}

                      onChange={(e) => handleInputChange('workingHoursPerWeek', Number(e.target.value))}

                    />



                    <Input

                      label="Arbeidstid"

                      value={formData.workingTime}

                      onChange={(e) => handleInputChange('workingTime', e.target.value)}

                      placeholder="07:00-15:00"

                    />

                  </div>



                  <Textarea

                    label="Pauseregler"

                    value={formData.breakTime}

                    onChange={(e) => handleInputChange('breakTime', e.target.value)}

                    rows={2}

                  />

                </div>

              </Card>



              {/* Compensation */}

              <Card title="Godtgjørelser og utbetaling" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <DollarSign className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Lønn og tillegg</h3>

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Input

                      label="Overtidstillegg (%)"

                      type="number"

                      value={formData.overtimeRate}

                      onChange={(e) => handleInputChange('overtimeRate', Number(e.target.value))}

                    />



                    <Select

                      label="Utbetalingsdag"

                      options={paymentDayOptions}

                      value={formData.paymentDay.toString()}

                      onChange={(e) => handleInputChange('paymentDay', Number(e.target.value))}

                    />

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Textarea

                      label="Verktøygodtgjørelse"

                      value={formData.toolAllowance}

                      onChange={(e) => handleInputChange('toolAllowance', e.target.value)}

                      rows={2}

                    />



                    <Textarea

                      label="Kjøregodtgjørelse"

                      value={formData.travelAllowance}

                      onChange={(e) => handleInputChange('travelAllowance', e.target.value)}

                      rows={2}

                    />

                  </div>

                </div>

              </Card>



              {/* Pension and Insurance */}

              <Card title="Pensjon og forsikring" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <Shield className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Pensjon og forsikringsordninger</h3>

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Input

                      label="Pensjonsleverandør"

                      value={formData.pensionProvider}

                      onChange={(e) => handleInputChange('pensionProvider', e.target.value)}

                    />



                    <Input

                      label="Pensjon org.nr"

                      value={formData.pensionOrgNumber}

                      onChange={(e) => handleInputChange('pensionOrgNumber', e.target.value)}

                    />

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Input

                      label="Forsikringsleverandør"

                      value={formData.insuranceProvider}

                      onChange={(e) => handleInputChange('insuranceProvider', e.target.value)}

                    />



                    <Input

                      label="Forsikring org.nr"

                      value={formData.insuranceOrgNumber}

                      onChange={(e) => handleInputChange('insuranceOrgNumber', e.target.value)}

                    />

                  </div>

                </div>

              </Card>



              {/* Legal Terms */}

              <Card title="Juridiske bestemmelser" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <Shield className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Oppsigelse og varslingsregler</h3>

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Textarea

                      label="Oppsigelsesfrister"

                      value={formData.noticePeriod}

                      onChange={(e) => handleInputChange('noticePeriod', e.target.value)}

                      rows={2}

                    />



                    <Textarea

                      label="Kontraktvarighet (hvis relevant)"

                      value={formData.contractDuration}

                      onChange={(e) => handleInputChange('contractDuration', e.target.value)}

                      rows={2}

                      placeholder="La stå tom for fast ansettelse"

                    />

                  </div>



                  <Textarea

                    label="Varslingsregler for endringer"

                    value={formData.notificationRules}

                    onChange={(e) => handleInputChange('notificationRules', e.target.value)}

                    rows={3}

                  />

                </div>

              </Card>



              {/* Navigation */}

              <div className="flex justify-between">

                <Button

                  onClick={onPrev}

                  variant="secondary"

                  size="lg"

                >

                  <ArrowLeft className="h-5 w-5 mr-2" />

                  Forrige steg

                </Button>



                <Button

                  onClick={onNext}

                  variant="primary"

                  size="lg"

                >

                  Generer kontrakt

                  <ArrowRight className="h-5 w-5 ml-2" />

                </Button>

              </div>

            </div>

          );

        };



        export default AdvancedSettingsStep;

    ```



    ---



    #### `steps\BasicInfoStep.tsx`



    ```tsx

        import { Card, Button } from '@/ui';

        import { Input, Select } from '@/ui/Form';

        import {

          ContractStepProps,

          positionOptions,

          employmentTypeOptions,

          probationMonthsOptions

        } from '@/lib/meta/types';

        import { ArrowRight, User, Briefcase, Calendar } from 'lucide-react';



        const BasicInfoStep = ({ formData, updateFormData, onNext }: ContractStepProps) => {

          const handleInputChange = (field: string, value: string | number | boolean) => {

            updateFormData({ [field]: value });

          };



          const handleNextClick = () => {

            if (onNext) {

              onNext();

            }

          };



          return (

            <div className="space-y-6">

              {/* Personal Information */}

              <Card title="Personopplysninger" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <User className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Grunnleggende informasjon</h3>

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Input

                      label="Fullt navn"

                      name="employeeName"

                      value={formData.employeeName}

                      onChange={(e) => handleInputChange('employeeName', e.target.value)}

                      placeholder="Ola Nordmann"

                      required

                    />



                    <Input

                      label="Adresse"

                      name="employeeAddress"

                      value={formData.employeeAddress}

                      onChange={(e) => handleInputChange('employeeAddress', e.target.value)}

                      placeholder="Gateadresse, postnummer og sted"

                      required

                    />

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Input

                      label="Fødselsdato"

                      name="employeeBirthDate"

                      type="date"

                      value={formData.employeeBirthDate}

                      onChange={(e) => handleInputChange('employeeBirthDate', e.target.value)}

                      required

                    />



                    <Input

                      label="Startdato"

                      name="startDate"

                      type="date"

                      value={formData.startDate}

                      onChange={(e) => handleInputChange('startDate', e.target.value)}

                      required

                    />

                  </div>

                </div>

              </Card>



              {/* Position Information */}

              <Card title="Stillingsinformasjon" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <Briefcase className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Stilling og lønn</h3>

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <Select

                      label="Stillingstittel"

                      name="position"

                      options={positionOptions}

                      value={formData.position}

                      onChange={(e) => handleInputChange('position', e.target.value)}

                      required

                    />



                    <Input

                      label="Timelønn (kr)"

                      name="hourlyRate"

                      type="number"

                      value={formData.hourlyRate}

                      onChange={(e) => handleInputChange('hourlyRate', Number(e.target.value))}

                      placeholder="300"

                      required

                    />

                  </div>



                  <Input

                    label="Kontonummer"

                    name="accountNumber"

                    value={formData.accountNumber}

                    onChange={(e) => handleInputChange('accountNumber', e.target.value)}

                    placeholder="1234.56.78901"

                    required

                  />

                </div>

              </Card>



              {/* Employment Terms */}

              <Card title="Ansettelsesvilkår" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <Calendar className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Ansettelsestype og vilkår</h3>

                  </div>



                  <Select

                    label="Ansettelsestype"

                    options={employmentTypeOptions}

                    value={formData.employmentType}

                    onChange={(e) => {

                      const isTemporary = e.target.value === 'midlertidig';

                      handleInputChange('employmentType', e.target.value);

                      handleInputChange('isTemporary', isTemporary);

                    }}

                  />



                  {formData.isTemporary && (

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">

                      <Input

                        label="Sluttdato for midlertidig ansettelse"

                        type="date"

                        value={formData.temporaryEndDate}

                        onChange={(e) => handleInputChange('temporaryEndDate', e.target.value)}

                      />



                      <Input

                        label="Begrunnelse for midlertidig ansettelse"

                        value={formData.temporaryReason}

                        onChange={(e) => handleInputChange('temporaryReason', e.target.value)}

                        placeholder="F.eks. vikariat, sesongarbeid, prosjekt"

                      />

                    </div>

                  )}



                  <div className="flex items-center space-x-3">

                    <input

                      type="checkbox"

                      id="probationPeriod"

                      checked={formData.probationPeriod}

                      onChange={(e) => handleInputChange('probationPeriod', e.target.checked)}

                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"

                    />

                    <label htmlFor="probationPeriod" className="text-sm font-medium text-gray-700">

                      Prøvetid

                    </label>

                  </div>



                  {formData.probationPeriod && (

                    <div className="ml-7">

                      <Select

                        label="Lengde på prøvetid"

                        options={probationMonthsOptions}

                        value={formData.probationMonths.toString()}

                        onChange={(e) => handleInputChange('probationMonths', Number(e.target.value))}

                      />

                    </div>

                  )}



                  <div className="flex items-center space-x-3">

                    <input

                      type="checkbox"

                      id="ownTools"

                      checked={formData.ownTools}

                      onChange={(e) => handleInputChange('ownTools', e.target.checked)}

                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"

                    />

                    <label htmlFor="ownTools" className="text-sm font-medium text-gray-700">

                      Ansatt skal bruke eget verktøy

                    </label>

                  </div>

                </div>

              </Card>



              {/* Navigation */}

              <div className="flex justify-end">

                <Button

                  onClick={handleNextClick}

                  variant="primary"

                  size="lg"

                >

                  Neste steg

                  <ArrowRight className="h-5 w-5 ml-2" />

                </Button>

              </div>

            </div>

          );

        };



        export default BasicInfoStep;

    ```



    ---



    #### `steps\ContractGenerationStep.tsx`



    ```tsx

        import { Card, Button } from '@/ui';

        import { ContractStepProps } from '@/lib/meta/types';

        import { ArrowLeft, Download, FileText, CheckCircle, Eye } from 'lucide-react';

        import { pdf } from '@react-pdf/renderer';

        import ContractPDF from '../ContractPDFTemplate';



        const ContractGenerationStep = ({ formData, onPrev }: ContractStepProps) => {



          const formatDate = (dateString: string) => {

            if (!dateString) return '__.__.__';

            const date = new Date(dateString);

            return date.toLocaleDateString('no-NO', {

              day: '2-digit',

              month: '2-digit',

              year: 'numeric'

            });

          };



          const generatePDFBlob = async () => {

            const blob = await pdf(<ContractPDF formData={formData} />).toBlob();

            return blob;

          };



          const handleDownload = async () => {

            try {

              const blob = await generatePDFBlob();

              const url = URL.createObjectURL(blob);

              const link = document.createElement('a');

              link.href = url;

              link.download = `Arbeidskontrakt_${formData.employeeName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;

              document.body.appendChild(link);

              link.click();

              document.body.removeChild(link);

              URL.revokeObjectURL(url);

            } catch (error) {

              console.error('Error generating PDF:', error);

              alert('Det oppstod en feil ved generering av PDF. Vennligst prøv igjen.');

            }

          };



          const handlePreview = async () => {

            try {

              const blob = await generatePDFBlob();

              const url = URL.createObjectURL(blob);

              window.open(url, '_blank');

            } catch (error) {

              console.error('Error generating PDF preview:', error);

              alert('Det oppstod en feil ved forhåndsvisning av PDF. Vennligst prøv igjen.');

            }

          };



          return (

            <div className="space-y-6">

              {/* Summary */}

              <Card title="Sammendrag av kontraktinformasjon" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Kontrakten er klar for generering</h3>

                  </div>



                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                    <div className="space-y-3">

                      <h4 className="font-medium text-gray-900">Ansatt</h4>

                      <div className="text-sm text-gray-600 space-y-1">

                        <div><span className="font-medium">Navn:</span> {formData.employeeName}</div>

                        <div><span className="font-medium">Stilling:</span> {formData.position}</div>

                        <div><span className="font-medium">Startdato:</span> {formatDate(formData.startDate)}</div>

                        <div><span className="font-medium">Timelønn:</span> kr {formData.hourlyRate},-</div>

                      </div>

                    </div>



                    <div className="space-y-3">

                      <h4 className="font-medium text-gray-900">Ansettelse</h4>

                      <div className="text-sm text-gray-600 space-y-1">

                        <div><span className="font-medium">Type:</span> {formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse'}</div>

                        <div><span className="font-medium">Arbeidstid:</span> {formData.workingHoursPerWeek} t/uke</div>

                        <div><span className="font-medium">Prøvetid:</span> {formData.probationPeriod ? `${formData.probationMonths} måneder` : 'Nei'}</div>

                        <div><span className="font-medium">Eget verktøy:</span> {formData.ownTools ? 'Ja' : 'Nei'}</div>

                      </div>

                    </div>

                  </div>

                </div>

              </Card>



              {/* Actions */}

              <Card title="Generer PDF-kontrakt" className="mb-6">

                <div className="space-y-4">

                  <div className="flex items-center mb-4">

                    <FileText className="h-5 w-5 text-green-600 mr-2" />

                    <h3 className="text-base sm:text-lg font-medium text-gray-900">Last ned eller forhåndsvis PDF-kontrakten</h3>

                  </div>



                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">

                    <div className="flex items-start">

                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />

                      <div className="text-sm">

                        <p className="text-green-800 font-medium">Kontrakten er juridisk korrekt</p>

                        <p className="text-green-700 mt-1">

                          Denne kontrakten oppfyller alle krav i Arbeidsmiljøloven § 14-6 og er klar for signering.

                        </p>

                      </div>

                    </div>

                  </div>



                  <div className="flex flex-col sm:flex-row gap-4">

                    <Button

                      onClick={handleDownload}

                      variant="primary"

                      size="lg"

                      className="flex-1"

                    >

                      <Download className="h-5 w-5 mr-2" />

                      Last ned PDF

                    </Button>



                    <Button

                      onClick={handlePreview}

                      variant="secondary"

                      size="lg"

                      className="flex-1"

                    >

                      <Eye className="h-5 w-5 mr-2" />

                      Forhåndsvis PDF

                    </Button>

                  </div>

                </div>

              </Card>



              {/* Navigation */}

              <div className="flex justify-start">

                <Button

                  onClick={onPrev}

                  variant="secondary"

                  size="lg"

                >

                  <ArrowLeft className="h-5 w-5 mr-2" />

                  Forrige steg

                </Button>

              </div>

            </div>

          );

        };



        export default ContractGenerationStep;

    ```


## Message 6

Below is a **drop-in refactor** that wires the entire PDF pipeline to the same six master tokens we defined earlier.  
You only touch **two new files** and **one existing file**; everything else keeps compiling exactly as before.

---

## 1. Add a design-tokens module (new file)

`src/lib/meta/designTokens.ts`

```ts
/**
 * 🪄 6-Token Design System for Meta utilities
 * Change these values → whole PDF + UI restyles.
 */
export const tokens = {
  /* T1 */ brandColor:   '#059669',
  /* T2 */ textColor:    '#374151',
  /* T3 */ fontFamily:   'Helvetica',          // PDF default – swap for interop’d font
  /* T4 */ fontBase:     11,                   // pt
  /* T5 */ spaceUnit:    6,                    // pt  (=~ 1 spacing step)
  /* T6 */ strokeWeight: 1,                    // pt
};

/* ---------- deterministic scale (rules, not extra params) ---------- */
export const scale = {
  companyName:  tokens.fontBase * 1.8,
  docTitle:     tokens.fontBase * 1.4,
  sectionTitle: tokens.fontBase * 1.1,
};
```

---

## 2. Patch **`ContractPDFTemplate.tsx`**

Only the **imports block and the `StyleSheet.create` call** change; the rest of the component stays intact.

```diff
- import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
+ import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
+ import { tokens, scale } from '@/lib/meta/designTokens';

...
- const styles = StyleSheet.create({
-   page: {
-     flexDirection: 'column',
-     backgroundColor: '#ffffff',
-     padding: 30,
-     fontSize: 11,
-     lineHeight: 1.4,
-   },
-   header: {
-     textAlign: 'center',
-     marginBottom: 24,
-     paddingBottom: 16,
-     borderBottomWidth: 2,
-     borderBottomColor: '#059669',
-   },
-   companyName: {
-     fontSize: 18,
-     fontWeight: 'bold',
-     color: '#059669',
-     marginBottom: 5,
-   },
-   title: {
-     fontSize: 16,
-     fontWeight: 'bold',
-     color: '#374151',
-     marginTop: 15,
-   },
-   ...
- });
+ const { brandColor, textColor, fontFamily, fontBase, spaceUnit, strokeWeight } = tokens;
+
+ const styles = StyleSheet.create({
+   /* 🔗 everything hangs off the 6 tokens */
+   page: {
+     flexDirection: 'column',
+     backgroundColor: '#ffffff',
+     padding: spaceUnit * 5,
+     fontSize: fontBase,
+     fontFamily,
+     lineHeight: 1.4,
+   },
+   header: {
+     textAlign: 'center',
+     marginBottom: spaceUnit * 4,
+     paddingBottom: spaceUnit * 3,
+     borderBottomWidth: strokeWeight * 2,
+     borderBottomColor: brandColor,
+   },
+   companyName: {
+     fontSize: scale.companyName,
+     fontWeight: 'bold',
+     color: brandColor,
+     marginBottom: spaceUnit,
+   },
+   title: {
+     fontSize: scale.docTitle,
+     fontWeight: 'bold',
+     color: textColor,
+     marginTop: spaceUnit * 2.5,
+   },
+   section: {
+     marginBottom: spaceUnit * 2.5,
+     breakInside: 'avoid',
+   },
+   sectionTitle: {
+     fontSize: scale.sectionTitle,
+     fontWeight: 'bold',
+     color: brandColor,
+     marginBottom: spaceUnit * 1.5,
+     paddingBottom: spaceUnit * 0.5,
+     borderBottomWidth: strokeWeight,
+     borderBottomColor: '#d1d5db',
+     breakAfter: 'avoid',
+   },
+   row: {
+     flexDirection: 'row',
+     justifyContent: 'space-between',
+     marginBottom: spaceUnit * 2.5,
+     breakInside: 'avoid',
+   },
+   column: {
+     flex: 1,
+     paddingRight: spaceUnit * 3.5,
+   },
+   label: {
+     fontWeight: 'bold',
+     marginBottom: spaceUnit * 0.3,
+   },
+   text: {
+     marginBottom: spaceUnit * 0.8,
+   },
+   legalText: {
+     fontSize: fontBase * 0.8,
+     color: '#6b7280',
+     marginBottom: spaceUnit * 3,
+   },
+   signatureSection: {
+     flexDirection: 'row',
+     justifyContent: 'space-between',
+     marginTop: spaceUnit * 7,
+     paddingTop: spaceUnit * 3,
+     borderTopWidth: strokeWeight,
+     borderTopColor: '#d1d5db',
+   },
+   signatureBox: {
+     width: '45%',
+     textAlign: 'center',
+   },
+   signatureLine: {
+     borderTopWidth: strokeWeight,
+     borderTopColor: '#9ca3af',
+     paddingTop: spaceUnit,
+     marginBottom: spaceUnit * 1.5,
+     height: spaceUnit * 5,
+   },
+   keepTogether: {
+     breakInside: 'avoid',
+     orphans: 3,
+     widows: 3,
+   },
+ });
```

> **Result:** swap a hex, change the base font, bump spacing, or thicken rules by editing `designTokens.ts`—and your PDF updates everywhere without hunting magic numbers.

---

## 3. (Optionally) expose the tokens to Tailwind UI

Add the same variables to `tailwind.config.js` (or your CSS `:root`):

```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        brand: 'var(--brand-color)',   // maps to T1
        text:  'var(--text-color)',    // maps to T2
      },
      borderWidth: {
        stroke: 'var(--stroke-weight)' // maps to T6
      },
      fontFamily: {
        base: ['var(--font-family)', 'sans-serif'], // maps to T3
      },
      spacing: {
        su: 'var(--space-unit)',       // maps to T5
      },
    },
  },
};
```

…and inject the CSS custom-props once, e.g.:

```css
:root {
  --brand-color:   #059669;
  --text-color:    #374151;
  --font-family:   Helvetica, sans-serif;
  --space-unit:    6pt;
  --stroke-weight: 1pt;
}
```

Your wizard screens (`BasicInfoStep`, `AdvancedSettingsStep`, etc.) can now replace
```html
text-green-600  → text-brand
bg-green-600    → bg-brand
border-green-600 → border-brand
```
and so on—**zero new theme props needed.**

---

### What changed?

| Before | After |
|--------|-------|
| Hard-coded sizes, colors, margins scattered across PDF style object | All values computed from the **same six tokens** |
| Changing a heading color required hunting for the hex | Change `brandColor` once |
| Tailwind and PDF styles lived in separate worlds | Optional root-CSS bridge lets both share the same tokens |

---

**Next steps (if you’d like more granularity)**  
*If* you need alt themes (e.g., monochrome print vs. branded digital), create **token sets** (`tokensDark`, `tokensPrint`) and pick which one to export based on context.

---

*Role acknowledgement*: Synthesist applied the “six-token” system directly to your PDF generator, demonstrating the minimal code changes needed and preserving full design flexibility.
