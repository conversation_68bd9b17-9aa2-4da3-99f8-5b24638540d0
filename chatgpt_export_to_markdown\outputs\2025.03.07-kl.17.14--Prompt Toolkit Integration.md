# Prompt Toolkit Integration

Conversation ID: 67cb1b5e-af68-8008-b69d-8fbf819227be

## Message 1

**Improving interactive CLI**

I’m curious about integrating prompt_toolkit with questionary to boost the CLI interface. Exploring features like advanced autocompletion, syntax highlighting, and dynamic menu choices to enrich the user experience.

**Showcasing prompt_toolkit**

I’m demonstrating prompt_toolkit’s capabilities with examples like advanced autocompletion and text editing, noting its advanced features over questionary. This includes in-place editing and dynamic layouts.

**Showcasing advanced features**

I'm highlighting prompt_toolkit's advanced features like multiline editing and dynamic prompt navigation, suggesting an interactive session with custom prompts and key bindings to enhance user experience.

**Showcasing InquirerPy**

I'm exploring InquirerPy as an alternative to prompt_toolkit, highlighting its interactive features like session management and multiline editing, and showcasing a sample code snippet to illustrate its capabilities.

## Message 2

Below is a conceptual walkthrough of how you could leverage **prompt_toolkit** to improve and/or replace the current question-based UI (built on top of `questionary`) in your Typer CLI. Since *questionary* already uses *prompt_toolkit* under the hood, you can either extend `questionary`-based prompts with extra features from prompt_toolkit or directly build your own custom prompt_toolkit interfaces for finer control (e.g., multiline input, real-time autocompletion, custom key bindings, advanced layouts, etc.).

---

## 1. Why Use Prompt Toolkit Directly?

**prompt_toolkit** is a lower-level library giving you:
1. **Real-time autocompletion** (for commands, file paths, or custom lists).
2. **Custom key bindings** (e.g., Ctrl+D for exit, Ctrl+S for saving).
3. **Advanced input fields** (e.g., multiline prompts, code editors in the terminal).
4. **Rich layouts** (build dynamic forms, dialogs, or wizard-like flows).

Libraries like `questionary` and `Inquirer.py` abstract many of these features into simple “prompt objects.” However, if you want deeper control—especially if you foresee more complex flows than a simple question/answer style—**prompt_toolkit** can be used directly to achieve exactly what you need.

---

## 2. High-Level Steps for Converting to Prompt Toolkit

1. **Remove or reduce usage of questionary**. Instead of `questionary.text(...)`, `questionary.select(...)`, etc., you can create prompt_toolkit sessions and handle each step.
2. **Set up a “main loop”** where:
   - You render the current state with Rich (like you do now).
   - You ask for input from the user with prompt_toolkit.
   - You process the user input (interpretation/transformation selection, prompt edits, finalize).
3. **Optionally** add autocompletion for certain prompts: e.g., a custom list of template names for the “Interpretation” stage.

---

## 3. Simple Example: Replacing a Single Text Prompt

Below is a minimal snippet showing how to replace a basic `questionary.text("Enter test prompt:")` with **prompt_toolkit**. We’ll use a `PromptSession` to repeatedly prompt the user until they provide valid input:

```python
from prompt_toolkit import PromptSession
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.validation import Validator, ValidationError

# Optional: custom validator for demonstration
class NonEmptyValidator(Validator):
    def validate(self, document):
        if not document.text.strip():
            raise ValidationError(
                message="Input cannot be empty!",
                cursor_position=len(document.text)
            )

def get_test_prompt(default_text: str) -> str:
    session = PromptSession(history=InMemoryHistory())
    while True:
        # Render a prompt and read user input
        user_input = session.prompt(
            "Enter test prompt: ",
            default=default_text,
            validator=NonEmptyValidator()  # optional validation
        )
        if user_input.strip():
            return user_input
        # If empty, it re-prompts due to the validator
```

In your `build()` command, you could call `get_test_prompt()` instead of using `questionary.text(...).ask()`.

---

## 4. Creating a List-Based Prompt (Selection) with Prompt Toolkit

`questionary.select(...)` gives you a menu to select from. In **prompt_toolkit**, you can mimic that using a **RadioListDialog** or build your own custom menu logic.

### 4.1. Using RadioListDialog

```python
from prompt_toolkit.shortcuts import radiolist_dialog

def select_template_ptk(templates: list[str], title: str) -> str:
    # Convert your list of templates into choices (value, label)
    choices = [(tmpl, tmpl) for tmpl in templates]

    result = radiolist_dialog(
        title=title,
        text="Select one of the templates:",
        values=choices,
    ).run()

    if not result:
        # User cancelled or pressed ESC
        return ""
    if result == "Custom (write your own)":
        # fallback to a text prompt
        return PromptSession().prompt("Enter your custom text: ")
    return result
```

You could drop this in place of your `select_template()` function to get a radio-list–style dialog (arrow keys to select, Enter to confirm).

---

## 5. Integrating Everything with Typer

Your main `build()` function logic may look like this:

```python
import typer
from rich.console import Console
from rich.panel import Panel
from rich.rule import Rule
from rich.text import Text

from prompt_toolkit import PromptSession
from prompt_toolkit.shortcuts import radiolist_dialog

app = typer.Typer(help="Interactive System Prompt Builder")
console = Console()

DEFAULT_TEST_PROMPT = """Transform the given ..."""
SYSTEM_PROMPT_P1_TEMPLATES = [
    "Your goal is not to **answer**...",
    "Your mission is not to alter the input...",
    "Custom (write your own)",
]
SYSTEM_PROMPT_P2_TEMPLATES = [
    "Execute as optimization engine: {role=input_enhancer; ...}",
    "Execute as title extractor virtuoso: {role=title_extractor; ...}",
    "Custom (write your own)",
]

def clear_screen() -> None:
    console.clear()

def display_state(test_prompt: str, system_p1: str, system_p2: str) -> None:
    clear_screen()
    console.print(Panel(Text(test_prompt, style="bold green"), title="Test Prompt", border_style="blue"))
    console.print(Rule(style="blue"))
    if system_p1:
        console.print(Panel(Text(system_p1, style="cyan"), title="Interpretation", border_style="green"))
    if system_p2:
        console.print(Panel(Text(system_p2, style="magenta"), title="Transformation", border_style="yellow"))

def build() -> None:
    session = PromptSession()
    test_prompt = session.prompt(
        "Enter test prompt: ",
        default=DEFAULT_TEST_PROMPT
    )
    system_p1 = ""
    system_p2 = ""

    while True:
        display_state(test_prompt, system_p1, system_p2)

        # A simple text prompt to choose action:
        action = radiolist_dialog(
            title="Choose an action",
            text="Press ENTER to confirm your selection:",
            values=[
                ("interp", "Edit Interpretation"),
                ("trans", "Edit Transformation"),
                ("test", "Edit Test Prompt"),
                ("final", "Finalize"),
                ("exit", "Exit")
            ]
        ).run()

        if action is None:
            # User pressed ESC, break or handle how you wish
            return

        if action == "interp":
            system_p1 = select_template_ptk(
                SYSTEM_PROMPT_P1_TEMPLATES, 
                "Select Interpretation Template"
            )
        elif action == "trans":
            system_p2 = select_template_ptk(
                SYSTEM_PROMPT_P2_TEMPLATES, 
                "Select Transformation Template"
            )
        elif action == "test":
            # Let user type new prompt
            test_prompt = session.prompt("Enter test prompt: ", default=test_prompt)
        elif action == "final":
            final_prompt = f"{system_p1}\n\n{system_p2}".strip()
            display_state(test_prompt, system_p1, system_p2)
            console.print(
                Panel(
                    Text(final_prompt, style="bold yellow"),
                    title="Final System Prompt",
                    border_style="bright_magenta",
                )
            )
            confirm = session.prompt("Save this prompt? (y/n) ")
            if confirm.lower().startswith("y"):
                filename = session.prompt("Filename: ", default="system_prompt.json")
                import json
                from pathlib import Path
                data = {"test_prompt": test_prompt, "system_prompt": final_prompt}
                Path(filename).write_text(json.dumps(data, indent=2, ensure_ascii=False))
                console.print(f"Saved to {filename}", style="green")
                input("Press Enter to continue...")
        elif action == "exit":
            raise typer.Exit()

app.command()(build)

if __name__ == "__main__":
    app()
```

### Note on `select_template_ptk`
We defined a helper `select_template_ptk()` that uses `radiolist_dialog` to mimic questionary’s selection style. It returns either the chosen template or a custom text if “Custom (write your own)” is selected.

---

## 6. Additional Enhancements & Features

1. **Autocompletion**  
   If your “templates” list is large, you can add an autocompleter:
   ```python
   from prompt_toolkit.completion import WordCompleter
   template_completer = WordCompleter(SYSTEM_PROMPT_P1_TEMPLATES, ignore_case=True)
   user_choice = session.prompt(
       "Type a template name: ",
       completer=template_completer,
       complete_while_typing=True
   )
   ```
   This provides a pop-up completion menu as the user types.

2. **Multiline Editor**  
   If you want a multi-line text editor (like a mini code editor), you can use:
   ```python
   user_input = session.prompt(
       multiline=True, 
       prompt="Enter your prompt (Ctrl+D or Esc+Enter to finish): "
   )
   ```
   This is great for letting users craft longer blocks of text.

3. **Custom Key Bindings**  
   You can register custom key bindings to handle special keystrokes:
   ```python
   from prompt_toolkit.key_binding import KeyBindings

   kb = KeyBindings()

   @kb.add('c-q')
   def _(event):
       # For example, exit on Ctrl+Q
       event.app.exit()

   user_input = session.prompt("... ", key_bindings=kb)
   ```

4. **Styling**  
   You can define a custom `Style` to give your prompts distinct colors and typography. This can supplement or replace some of Rich’s panel styling.

5. **Layouts & Containers**  
   prompt_toolkit has advanced APIs for building multi-widget layouts, pop-ups, progress bars, etc., if you want something more elaborate than just question-style prompts.

---

## 7. Wrap-Up

Switching to **prompt_toolkit** (or enhancing questionary’s usage of it) allows:
- **Better user experience** via autocompletion, multi-line input, real-time validation, etc.
- **Tighter control** of the interaction flow.
- **Custom behaviors** like key bindings and layouts beyond the typical question/answer pattern.

The example above demonstrates a straightforward approach:  
1. Replace questionary’s text and select prompts with `PromptSession().prompt()` calls or prompt_toolkit dialogs.  
2. Retain your existing Rich-based display (like `display_state`) and Typer commands.  
3. Expand with advanced features (completions, key bindings) as your CLI grows in complexity.

Use this as a reference starting point. From there, you can tailor the prompt flow to perfectly match your application’s needs.

## Message 3

how could prompt_toolkit be used to improve this simple interactive cli?



```python

import json

import sys

from pathlib import Path

import questionary

import typer

from rich.console import Console

from rich.panel import Panel

from rich.rule import Rule

from rich.text import Text



app = typer.Typer(help="Interactive System Prompt Builder")

console = Console()

# Default test prompt

DEFAULT_TEST_PROMPT = """Transform the given input into a seamlessly morphed, evolved form, preserving its core essence while enhancing its aesthetic and conceptual potency through continuous, gradual blending, guided by inherent parameters and universal principles of excellence."""

# Templates for the "Interpretation" stage

SYSTEM_PROMPT_P1_TEMPLATES = [

    "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.",

    "Your mission is not to alter the input, but to crystallize its quintessential form and identifying the core intent.",

    "Custom (write your own)",

]

# Templates for the "Transformation" stage

SYSTEM_PROMPT_P2_TEMPLATES = [

    "Execute as optimization engine: {role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}",

    "Execute as title extractor virtuoso: {role=title_extractor; input=[text:str]; process=[identify_core_topic(), determine_primary_action(), identify_key_modifiers(), synthesize_concise_title(core_topic, primary_action, key_modifiers, max_length=10)]; output={title:str}}",

    "Custom (write your own)",

]





def clear_screen() -> None:

    """Clear the console screen."""

    console.clear()





def display_state(test_prompt: str, system_p1: str, system_p2: str) -> None:

    """Render the current state of the test prompt and system prompts."""

    clear_screen()

    # Display the test prompt

    console.print(

        Panel(

            Text(test_prompt, style="bold green"),

            title="Test Prompt",

            border_style="blue",

        )

    )

    console.print(Rule(style="blue"))

    # Display system interpretation prompt

    if system_p1:

        console.print(

            Panel(

                Text(system_p1, style="cyan"),

                title="Interpretation",

                border_style="green",

            )

        )

    # Display system transformation prompt

    if system_p2:

        console.print(

            Panel(

                Text(system_p2, style="magenta"),

                title="Transformation",

                border_style="yellow",

            )

        )





def select_template(templates: list[str], title: str) -> str:

    """

    Prompt the user to select from a given list of templates or provide custom text.

    Returns the selected or custom value as a string.

    """

    choice = questionary.select(title, choices=templates).ask()

    if choice == "Custom (write your own)":

        custom = questionary.text("Enter your custom text:").ask()

        return custom or ""

    return choice or ""





@app.command()

def build() -> None:

    """Command to interactively build and save a system prompt."""

    test_prompt = (

        questionary.text("Enter test prompt:", default=DEFAULT_TEST_PROMPT).ask()

        or DEFAULT_TEST_PROMPT

    )

    system_p1 = ""

    system_p2 = ""

    while True:

        display_state(test_prompt, system_p1, system_p2)

        action = questionary.select(

            "Choose an action:",

            choices=[

                "Edit Interpretation",

                "Edit Transformation",

                "Edit Test Prompt",

                "Finalize",

                "Exit",

            ],

        ).ask()

        if action == "Edit Interpretation":

            system_p1 = select_template(

                SYSTEM_PROMPT_P1_TEMPLATES, "Select Interpretation Template"

            )

        elif action == "Edit Transformation":

            system_p2 = select_template(

                SYSTEM_PROMPT_P2_TEMPLATES, "Select Transformation Template"

            )

        elif action == "Edit Test Prompt":

            test_prompt = (

                questionary.text("Enter test prompt:", default=test_prompt).ask()

                or test_prompt

            )

        elif action == "Finalize":

            final_prompt = f"{system_p1}\n\n{system_p2}".strip()

            display_state(test_prompt, system_p1, system_p2)

            console.print(

                Panel(

                    Text(final_prompt, style="bold yellow"),

                    title="Final System Prompt",

                    border_style="bright_magenta",

                )

            )

            if questionary.confirm("Save this prompt?").ask():

                filename = questionary.text(

                    "Filename:", default="system_prompt.json"

                ).ask()

                # Save prompt data to a JSON file

                data = {"test_prompt": test_prompt, "system_prompt": final_prompt}

                Path(filename).write_text(

                    json.dumps(data, indent=2, ensure_ascii=False)

                )

                console.print(f"Saved to {filename}", style="green")

                input("Press Enter to continue...")

        elif action == "Exit":

            sys.exit(0)





if __name__ == "__main__":

    app()

```

## Message 4

**Crafting system prompts**

Piecing together prompts with templates, allowing users to choose or customize interpretation and transformation parts. Combining these to construct a final system prompt.

**Setting up interactive prompts**

I'm working on a prompt_toolkit interface, guiding users through test prompts, selection of interpretation and transformation templates, and previewing the final prompt. This creates a seamless choice and refinement process.

**Setting up**

Packing a rich Python virtual environment, ensuring the use of `prompt_toolkit` for efficient CLI interaction. Fab code will run seamlessly as a Typer app, guiding users through real-time task handling.

**Setting up the session**

I'm setting up an interactive session with a Typer app. Using `radiolist_dialog`, I'll facilitate template and action selections, managing user inputs and choices effectively.

## Message 5

Below is a **single-file** illustrative example showing how you can build an interactive CLI using **prompt_toolkit** directly (rather than `questionary`) to achieve the scenario you described. It integrates with your `SystemInstructionTemplates` class, offers template selection (or custom text) for the interpretation/transformation parts, and ensures your `test_prompt` is fixed at the top throughout the session.

---

```python
import json
import sys
from pathlib import Path

import typer
from rich.console import Console
from rich.panel import Panel
from rich.rule import Rule
from rich.text import Text

from prompt_toolkit.shortcuts import radiolist_dialog, yes_no_dialog
from prompt_toolkit import PromptSession
from prompt_toolkit.completion import WordCompleter

####################################
# SystemInstructionTemplates (as given)
####################################
class SystemInstructionTemplates:
    """
    # Philosophical Foundation
    class TemplateType:
        INTERPRETATION = "PART_1: How to interpret the input"
        TRANSFORMATION = "PART_2: How to transform the input"
    """

    # 1. Interpretation
    PART_1_VARIANTS = {
        "none": {
            "name": "",
            "content": "",
            "desc": ""
        },
        "rephraser": {
            "name": "Essential Rephraser",
            "content": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.",
            "desc": "Focuses on fundamental reconstruction without additive interpretation"
        },
        "intense": {
            "name": "Intensity Amplifier",
            "content": "Never respond directly - only reconstruct essence through strategic transformation.",
            "desc": "Emphasizes dramatic recontextualization of source material"
        },
    }

    # 2. Transformation
    PART_2_VARIANTS = {
        "none": {
            "name": "",
            "content": "",
            "desc": ""
        },
        "enhancer_a": {
            "name": "StructuralOptimizer",
            "content": """Execute as optimization engine: {role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}""",
            "desc": "Systematic enhancement through pattern amplification"
        },
        "extractor": {
            "name": "TitleExtractor",
            "content": """{role=title_extractor; input=[text:str]; process=[identify_core_topic(), determine_primary_action(), identify_key_modifiers(), synthesize_concise_title(core_topic, primary_action, key_modifiers, max_length=10)]; output={title:str}}""",
            "desc": "Extracts a concise title from input"
        },
        "finalizer": {
            "name": "Final Synthesis Optimizer",
            "content": """{role=final_synthesis_optimizer; input=[original_prompt:str, refined_prompt:str, critique:dict]; process=[extract_core_requirements(original_prompt, prioritizing_simplicity), evaluate_refined_prompt_strengths_and_weaknesses(refined_prompt, critique, original_prompt), choose_optimal_request_form(original_prompt, refined_prompt, critique), incorporate_best_alternative_suggestions(critique), address_flaws_highlighted_in_critique(critique), synthesize_optimal_wording(prioritizing_simplicity_and_elegance)]; output={final_enhanced_prompt:str}}""",
            "desc": "Combines original prompt and refined prompt into final"
        },
        "evaluator": {
            "name": "Enhancement Assessor",
            "content": """{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}""",
            "desc": "Evaluates response enhancement by discarding noise and amplifying core elements"
        },
    }

    @classmethod
    def get_combined(cls, part1_key, part2_key):
        """Fuses PART_1 (interpretation) and PART_2 (transformation) components."""
        p1 = cls.PART_1_VARIANTS[part1_key]["content"]
        p2 = cls.PART_2_VARIANTS[part2_key]["content"]
        return f"{p1}\n\n{p2}".strip()

    @classmethod
    def validate_template_keys(cls, part1_key, part2_key):
        """Ensures template combination validity."""
        return (part1_key in cls.PART_1_VARIANTS) and (part2_key in cls.PART_2_VARIANTS)

    @classmethod
    def get_template_categories(cls):
        """Self-documenting template structure for UI integration."""
        return {
            "interpretation": {k: {"name": v["name"], "desc": v["desc"]} 
                               for k, v in cls.PART_1_VARIANTS.items()},
            "transformation": {k: {"name": v["name"], "desc": v["desc"]} 
                               for k, v in cls.PART_2_VARIANTS.items()},
        }

####################################
# CLI Logic: Using prompt_toolkit + rich
####################################

app = typer.Typer(help="Interactive System Prompt Builder")
console = Console()

DEFAULT_TEST_PROMPT = """Transform the given input into a seamlessly morphed, evolved form, preserving its core essence while enhancing its aesthetic and conceptual potency through continuous, gradual blending, guided by inherent parameters and universal principles of excellence."""

def clear_screen():
    console.clear()

def display_state(test_prompt: str, p1_text: str, p2_text: str):
    """
    Displays the current state of the test_prompt (top) 
    plus the chosen part1 & part2 system instructions.
    """
    clear_screen()

    # Top: Test Prompt
    console.print(
        Panel(
            Text(test_prompt, style="bold green"),
            title="Test Prompt",
            border_style="blue",
        )
    )
    console.print(Rule(style="blue"))

    # Middle: System Prompt, Part 1 (Interpretation)
    if p1_text.strip():
        console.print(
            Panel(
                Text(p1_text, style="cyan"),
                title="Interpretation (Part 1)",
                border_style="green",
            )
        )

    # Bottom: System Prompt, Part 2 (Transformation)
    if p2_text.strip():
        console.print(
            Panel(
                Text(p2_text, style="magenta"),
                title="Transformation (Part 2)",
                border_style="yellow",
            )
        )

def select_interpretation_template() -> str:
    """
    Let the user pick from PART_1_VARIANTS (or custom).
    Return the key to the chosen template, or a special 'custom'
    if they typed in a new prompt themselves.
    """
    # Build (value, label) pairs for prompt_toolkit
    choices = []
    for key, info in SystemInstructionTemplates.PART_1_VARIANTS.items():
        label = info["name"] or "(None)"
        desc = info["desc"]
        # Combine label + desc for clarity
        combined_label = f"{label}"
        if desc:
            combined_label += f" — {desc}"
        choices.append((key, combined_label))

    result = radiolist_dialog(
        title="Select Interpretation Template",
        text="Choose an interpretation method (Part 1):",
        values=choices + [("custom", "Custom (write your own)")]
    ).run()

    if result is None:
        # User canceled (ESC) => do nothing
        return ""
    if result == "custom":
        # fallback to direct text prompt
        session = PromptSession()
        user_text = session.prompt("Enter your custom interpretation text: ")
        # We store it under a pseudo-key in PART_1_VARIANTS
        # or just return the raw text and handle later.
        SystemInstructionTemplates.PART_1_VARIANTS["custom"] = {
            "name": "Custom Interpretation",
            "content": user_text,
            "desc": "User-defined part1"
        }
        return "custom"
    return result

def select_transformation_template() -> str:
    """
    Let the user pick from PART_2_VARIANTS (or custom).
    Return the key to the chosen template, or a special 'custom'
    if they typed in a new prompt themselves.
    """
    # Build (value, label) pairs for prompt_toolkit
    choices = []
    for key, info in SystemInstructionTemplates.PART_2_VARIANTS.items():
        label = info["name"] or "(None)"
        desc = info["desc"]
        combined_label = f"{label}"
        if desc:
            combined_label += f" — {desc}"
        choices.append((key, combined_label))

    result = radiolist_dialog(
        title="Select Transformation Template",
        text="Choose a transformation method (Part 2):",
        values=choices + [("custom", "Custom (write your own)")]
    ).run()

    if result is None:
        # User canceled (ESC) => do nothing
        return ""
    if result == "custom":
        session = PromptSession()
        user_text = session.prompt("Enter your custom transformation text: ")
        # Same pattern: store under a pseudo-key
        SystemInstructionTemplates.PART_2_VARIANTS["custom"] = {
            "name": "Custom Transformation",
            "content": user_text,
            "desc": "User-defined part2"
        }
        return "custom"
    return result

@app.command("build")
def build_system_prompt():
    """
    Main interactive loop to define:
      1) test_prompt
      2) system_prompt part1 (Interpretation)
      3) system_prompt part2 (Transformation)
      4) final combination => optional save to JSON
    """
    session = PromptSession()

    # 1) Prompt for test_prompt (or accept default)
    test_prompt = session.prompt(
        "Enter Test Prompt (leave empty for default): ",
        default=DEFAULT_TEST_PROMPT
    ) or DEFAULT_TEST_PROMPT

    # Keep track of selected PART1 & PART2 keys
    part1_key = "none"
    part2_key = "none"

    while True:
        # Display current state
        current_part1_text = SystemInstructionTemplates.PART_1_VARIANTS.get(part1_key, {}).get("content", "")
        current_part2_text = SystemInstructionTemplates.PART_2_VARIANTS.get(part2_key, {}).get("content", "")

        display_state(test_prompt, current_part1_text, current_part2_text)

        # Let user pick an action
        action = radiolist_dialog(
            title="System Prompt Builder",
            text="Select an action:",
            values=[
                ("part1", "Select/Change Interpretation (Part 1)"),
                ("part2", "Select/Change Transformation (Part 2)"),
                ("show_final", "Preview Final Combined Prompt"),
                ("save", "Save to JSON"),
                ("quit", "Quit")
            ],
        ).run()

        if action is None:
            # ESC pressed
            sys.exit(0)

        if action == "part1":
            chosen_key = select_interpretation_template()
            if chosen_key:
                part1_key = chosen_key

        elif action == "part2":
            chosen_key = select_transformation_template()
            if chosen_key:
                part2_key = chosen_key

        elif action == "show_final":
            # Combine part1 & part2
            combined_text = SystemInstructionTemplates.get_combined(part1_key, part2_key)
            clear_screen()
            console.print(
                Panel(
                    Text(test_prompt, style="bold green"),
                    title="Test Prompt",
                    border_style="blue",
                )
            )
            console.print(Rule(style="blue"))
            console.print(Panel(Text(combined_text, style="bold yellow"), 
                                title="Final Combined Prompt", 
                                border_style="bright_magenta"))
            input("Press Enter to continue...")

        elif action == "save":
            combined_text = SystemInstructionTemplates.get_combined(part1_key, part2_key)
            # Confirm with a yes_no_dialog
            should_save = yes_no_dialog(
                title="Save Prompt?",
                text="Would you like to save the final system prompt to a JSON file?"
            ).run()

            if should_save:
                filename = session.prompt("Filename (e.g. 'system_prompt.json'): ",
                                          default="system_prompt.json")
                data = {
                    "test_prompt": test_prompt,
                    "system_prompt_part1": current_part1_text,
                    "system_prompt_part2": current_part2_text,
                    "system_prompt_combined": combined_text
                }
                Path(filename).write_text(
                    json.dumps(data, indent=2, ensure_ascii=False),
                    encoding="utf-8"
                )
                console.print(f"[green]Saved to {filename}[/green]")
                input("Press Enter to continue...")

        elif action == "quit":
            sys.exit(0)

if __name__ == "__main__":
    app()
```

---

### How It Works

1. **Prompt for `test_prompt`**  
   - The code uses a single `PromptSession` prompt, giving you the option to type your own or just press Enter to keep the default.

2. **Always Display the Chosen `test_prompt`**  
   - The function `display_state` prints the `test_prompt` at the top in a green panel, followed by a Rule (separator), then your selected “Part 1” (Interpretation) and “Part 2” (Transformation).

3. **Selection of Part1 & Part2**  
   - `select_interpretation_template()` and `select_transformation_template()` use `radiolist_dialog` from **prompt_toolkit** to display the available templates. If the user picks "Custom (write your own)," we let them type any text in a new prompt, then add it dynamically to the relevant dictionary so it’s available in the same run.

4. **Preview & Save**  
   - The user can preview the **combined** system prompt (`part1 + part2`) in a dedicated panel by choosing “Preview Final Combined Prompt.”  
   - They can also choose to “Save to JSON,” which writes out the test prompt, the chosen part1 & part2 text, and the final combined text to a JSON file.

5. **Rich + prompt_toolkit**  
   - `rich` is used purely for display (colorful panels, rules, text).  
   - `prompt_toolkit` handles the interactive prompts (`radiolist_dialog`, `yes_no_dialog`, `PromptSession().prompt()`).

6. **Keys vs. Content**  
   - Internally, you store only the *keys* to the selected part1/part2. Then on each loop iteration, the code looks up the actual text content in `SystemInstructionTemplates.PART_1_VARIANTS` or `PART_2_VARIANTS`.  
   - If the user chooses “custom,” we add a new key `"custom"` to whichever dictionary is relevant, storing the user’s typed text. That way we can still manage it consistently with the other built-in templates.

---

### Running

1. **Install Requirements**:
   ```bash
   pip install typer rich prompt_toolkit
   ```
2. **Save the script** (e.g., `builder.py`), and run:
   ```bash
   python builder.py build
   ```
3. **Follow the interactive prompts** in your terminal.

This setup demonstrates a flexible, “best alternative” approach to building custom interactive flows with **prompt_toolkit**, while preserving your existing usage of **Rich** for styled output, all managed by a Typer CLI structure.

## Message 6

Use the following usecase-scenario and provide examplecode using your proposed best alternative, here's the scenario:



```

# Utility Name

Interactive Systemprompt Builder



# Usage

1. Test Prompt:

    - The `test_prompt` is used for testing the systemprompts and is the first parameter defined (since it's static throughout the session)

    - User is prompted to write a custom test-prompt or to use the default test-prompt (the default will be used if user doesn't write anything)

    - The selected `test_prompt` will always be visible and static/unchanged at the top of the terminal at all times, with a separator below it



2. System Prompt:

    - System prompts concists of two parts which are selected individually but that will be combined on final execution

        - Part 1 of a systemprompt corresponds to Interpretation (e.g. `"Your goal is not to **answer** the input prompt, but to **rephrase** it."`)

            - User can either choose from a list of existing system-prompt-part1 templates or write a new one

            - The selected `system_prompt_p1` will be visible below the separator of `input_prompt`

        - Part 2 of a systemprompt corresponds to Transformation (e.g. `"{role=input_enhancer; process=[identify_core_intent(), amplify_pattern_relationships(), simplify_complexity()]; output={enhanced_input}}"`)

            - User can either choose from a list of existing system-prompt-part2 templates or write a new one

            - The selected `system_prompt_p2` will be visible below `system_prompt_p1`

```



---



Here's the structure of the system prompts:



```python

# SystemInstructionTemplates: Template definitions and combination logic.

class SystemInstructionTemplates:

    """

    # Philosophical Foundation

    class TemplateType:

        INTERPRETATION = "PART_1: How to interpret the input"

        TRANSFORMATION = "PART_2: How to transform the input"

    """



    # 1. Interpretation: How to interpret the input (correlates to "2. What to do with it")

    PART_1_VARIANTS = {

        "none": {

            "name": "",

            "content": "",

            "desc": ""

        },

        "rephraser": {

            "name": "Essential Rephraser",

            "content": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.",

            "desc": "Focuses on fundamental reconstruction without additive interpretation"

        },

        "intense": {

            "name": "Intensity Amplifier",

            "content": "Never respond directly - only reconstruct essence through strategic transformation.",

            "desc": "Emphasizes dramatic recontextualization of source material"

        },

    }



    # 2. Transformation: What to do with it (works independent or in conjunction with "1. How to interpret the input")

    PART_2_VARIANTS = {

        "none": {

            "name": "",

            "content": "",

            "desc": ""

        },

        "enhancer_a": {

            "name": "StructuralOptimizer",

            "content": """Execute as optimization engine: {role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}""",

            "desc": "Systematic enhancement through pattern amplification"

        },

        "extractor": {

            "name": "TitleExtractor",

            "content": """{role=title_extractor; input=[text:str]; process=[identify_core_topic(), determine_primary_action(), identify_key_modifiers(), synthesize_concise_title(core_topic, primary_action, key_modifiers, max_length=10)]; output={title:str}}""",

            "desc": "..."

        },

        "finalizer": {

            "name": "Final Synthesis Optimizer",

            "content": """{role=final_synthesis_optimizer; input=[original_prompt:str, refined_prompt:str, critique:dict]; process=[extract_core_requirements(original_prompt, prioritizing_simplicity), evaluate_refined_prompt_strengths_and_weaknesses(refined_prompt, critique, original_prompt), choose_optimal_request_form(original_prompt, refined_prompt, critique), incorporate_best_alternative_suggestions(critique), address_flaws_highlighted_in_critique(critique), synthesize_optimal_wording(prioritizing_simplicity_and_elegance)]; output={final_enhanced_prompt:str}}""",

            "desc": "..."

        },

        "evaluator": {

            "name": "Enhancement Assessor",

            "content": """{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}""",

            "desc": "Evaluates response enhancement by discarding noise and amplifying core elements"

        },

    }



    @classmethod

    def get_combined(cls, part1_key, part2_key):

        """Fuses PART_1 (interpretation) and PART_2 (transformation) components"""

        p1 = cls.PART_1_VARIANTS[part1_key]["content"]

        p2 = cls.PART_2_VARIANTS[part2_key]["content"]

        return f"{p1} {p2}"



    @classmethod

    def validate_template_keys(cls, part1_key, part2_key):

        """Ensures template combination validity"""

        return (part1_key in cls.PART_1_VARIANTS) and (part2_key in cls.PART_2_VARIANTS)



    @classmethod

    def get_template_categories(cls):

        """Self-documenting template structure for UI integration"""

        return {

            "interpretation": {k: {"name": v["name"], "desc": v["desc"]} for k, v in cls.PART_1_VARIANTS.items()},

            "transformation": {k: {"name": v["name"], "desc": v["desc"]} for k, v in cls.PART_2_VARIANTS.items()}

        }

```

