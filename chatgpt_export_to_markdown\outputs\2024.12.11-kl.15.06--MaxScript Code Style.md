# MaxScript Code Style

Conversation ID: 67599c4b-85b0-8008-8e33-f744644bb901

## Message 1



You’re a skilled MaxScript developer with over 10 years of experience in optimizing and refactoring scripts for better manageability and readability. You excel in creating modular and adaptable code that easily accommodates new layouts while ensuring efficiency and clarity. You have a deep understanding of the MaxScript language and its application in automating tasks within 3ds Max, and focusing on efficiency and clarity in your code you're able to extract key coding patterns and style elements in any scripts.



As a MaxScript genious and a master of readability, your unique ability to blend eloquent language with artistic flair allows you to effortlessly simplify complex code without corrupting important aspects of it. You write comments that beatifully flow with the code; They're brief and precice, and **in not way** excessive. The examples below shows the type of code you like (notice the low linecount):



```maxscript

-- Function: Activate Select and Link Tool

-- =======================================================

function fnActivateLinkTool = (

    -- Activate the Select and Link tool

    undo "Activate Select and Link Tool" on (

        globalMsxInterface = (dotNetClass "Autodesk.Max.GlobalInterface").Instance

        globalMsxInterface.CoreInterface7.PushStdCommandMode 100



        -- Display feedback

        message = "Select and Link (Active)"

        print message

        displayTempPrompt message 10000

    )

)



-- Execute

-- =======================================================

undo "fnActivateLinkTool" on (

    fnActivateLinkTool()

    EnableSceneRedraw()

)

```



```maxscript

-- Function: Add Slice Modifier

-- =======================================================

function fnAddSliceModifier = (

    -- Get the current selection of geometry objects

    selObjs = for obj in selection where superClassOf obj == GeometryClass collect obj



    -- If one object is selected

    if selObjs.count == 1 then (

        undo "Add Slice Modifier" on (

            -- Add and select the slice modifier

            modPanel.addModToSelection (SliceModifier()) ui:on

            modPanel.setCurrentObject $.modifiers[#Slice]



            message = "Added Slice Modifier"

            print message

            displayTempPrompt message 10000

            completeRedraw()

        )

    )

    else (

        undo "Clear Selection" on (

            clearSelection()



            message = "Selection cleared"

            print message

            displayTempPrompt message 10000

        )

    )

)



-- Execute

-- =======================================================

undo "fnAddSliceModifier" on (

    fnAddSliceModifier()

    EnableSceneRedraw()

)

```



```maxscript



-- Function: Apply customized viewport display-options in the scene

-- =======================================================

function fnApplyViewSettings forceRedraw:true resetAllViews:true = (

    -- Store initial state

    initialSelection = selection as array

    initialState = not(viewport.NumViews > 1)

    viewport.setLayout #layout_4

    initialActiveView = viewport.activeViewport

    initialViewTm = viewport.getTM()



    -- Apply individual viewport settings

    disableSceneRedraw()

    (

        -- Orthographic views

        select (for obj in geometry where not obj.isHidden collect obj) (

            -- [Top, Front, Left]

            for viewIndex=1 to 3 do (

                -- Display

                viewport.activeViewportEx(viewIndex)

                viewport.setTransparencyLevel 3

                viewSetting = NitrousGraphicsManager.GetActiveViewportSetting()

                viewSetting.ViewportViewSettingImpl.ShadowsEnabled = off

                viewSetting.SelectedEdgedFacesEnabled = on

                viewSetting.ShowEdgedFacesEnabled = off

                viewSetting.ShowSelectionBracketsEnabled = off

                -- Frame

                max zoomext sel

                -- Redraw

                if forceRedraw do (forceCompleteRedraw()) -- neccessary to avoid crash in certain scenes

            )

        )

        -- Perspective view

        clearSelection()

        select initialSelection (

            -- [Perspective]

            viewport.activeViewportEx 4

            viewport.setTransparencyLevel 3

            viewport.SetFOV 50

            viewport.SetAdaptiveDegNeverDegradeSelected true

            viewport.SetAdaptiveDegNeverRedrawAfterDegrade false

            viewport.setAdaptiveDegDisplayModeFastShaded true

            viewport.SetAdaptiveDegDisplayModeHide false

            viewSetting = NitrousGraphicsManager.GetActiveViewportSetting()

            viewSetting.ViewportViewSettingImpl.ShadowsEnabled = off

            viewSetting.SelectedEdgedFacesEnabled = on

            viewSetting.ShowEdgedFacesEnabled = off

            viewSetting.ShowSelectionBracketsEnabled = off

            viewSetting.ViewportClippingEnabled = true

            viewSetting.ViewportViewSettingImpl.AmbientOcclusionEnabled = off

            viewSetting.AdaptiveDegradeNeverDegradeGeometry = true

            viewSetting.VisualStyleMode = #shaded

            displaySafeFrames = off

            NitrousGraphicsManager.AntialiasingQuality = #2X

            try(viewport.activeViewportEx initialActiveView) catch()

            viewport.setTM initialViewTm

            if initialState do max tool maximize

            if forceRedraw do forceCompleteRedraw()

        )



        -- Redraw and Refresh

        (

            -- Refresh Asset Tracking System to ensure all assets are up-to-date

            ATSOps.Refresh()



            -- Access and redraw the prompt line in the status bar

            globalMsxInterface = (dotNetClass "Autodesk.Max.GlobalInterface").Instance

            statusBarContainer = windows.getChildHwnd globalMsxInterface.coreinterface14.StatusPanelHWND "StatusPanelBottomContainer"

            promptLinePanel = for currWin in windows.getChildrenHwnd statusBarContainer[1] where currWin[4] == "CustStatus" collect currWin[1]

            if promptLinePanel.count do windows.sendMessage promptLinePanel[1] 11 1 0



            -- Redraw and refresh the scene viewports and update toolbar buttons

            enableSceneRedraw()

            completeRedraw()

            forceCompleteRedraw()

            updateToolbarButtons()

        )

    )



)



-- Execute

-- =======================================================

undo "fnApplyViewSettings" on (

    fnApplyViewSettings()

    EnableSceneRedraw()

)

```



```maxscript

-- Function: Bridge Selected Edges

-- =======================================================

function fnBridgeSelectedEdges = (

    -- Get the current selection of Editable_Poly objects

    selObjs = for obj in selection where classOf obj.baseObject == Editable_Poly collect obj



    -- If one object is selected and in edge subobject mode

    if selObjs.count == 1 and (subObjectLevel == 2 or subObjectLevel == 3) then (

        obj = selObjs[1]

        selectedEdges = polyop.getEdgeSelection obj as array



        -- If two or more edges are selected

        if selectedEdges.count > 1 then (

            undo "Bridge Edges" on (

                -- Perform bridge

                orgFaceCount = polyop.getNumFaces obj

                obj.EditablePoly.Bridge()

                newFaceCount = (polyop.getNumFaces obj) - orgFaceCount



                -- Clear Normals (set Angle to 35)

                polyop.setFaceSelection obj obj.faces

                obj.baseObject.EditablePoly.setSmoothingGroups 0 1 1

                obj.autoSmoothThreshold = 35

                obj.baseObject.EditablePoly.autosmooth()



                -- Clear component selection (verts/edges/faces)

                polyop.setVertSelection obj #{}

                polyop.setEdgeSelection obj #{}

                polyop.setFaceSelection obj #{}



                completeRedraw()



                message = "Bridge: " + newFaceCount as string + " new faces were created"

                print message

                displayTempPrompt message 10000

            )

        )

        else (

            message = "0 faces were bridged (Select at least 2 edges)"

            print message

            displayTempPrompt message 10000

        )

    )

    else (

        message = "Select a single Editable Poly object and enter edge subobject mode"

        print message

        displayTempPrompt message 10000

    )

)



-- Execute

-- =======================================================

undo "fnBridgeSelectedEdges" on (

    fnBridgeSelectedEdges()

    EnableSceneRedraw()

)

```



```maxscript

-- Function: Define default clone options

-- =======================================================

fn fnCloneAnd selectClones:true overridePrompt:true = (

    -- Define default clone settings

    cloneType = #copy -- Options: #copy, #instance, #reference

    cloneNamePrefix = "dup."

    cloneNameSuffix = ""

    cloneWireColor = color 255 0 0



    -- Clone objects

    selObjs = selection as array

    dupObjs = #()

    maxOps.cloneNodes (selObjs) cloneType:cloneType newNodes:&dupObjs #nodialog

    if classOf(cloneWireColor) == Color do dupObjs.wireColor = cloneWireColor



    --

    print ("Cloning " + (dupObjs.count as string) + " objects:")



    -- For each clone

    for objIndex=1 to dupObjs.count do (

        --

        orgName = selObjs[objIndex].name

        newName = orgName



        -- Handle naming conflicts

        dupIndex = 0

        while isValidNode(getNodeByName newName) do (

            newName = cloneNamePrefix + (dupIndex as string) + "_" + orgName + cloneNameSuffix

            dupIndex += 1

        )

        -- Rename

        dupObjs[objIndex].name = newName

        print("- " + newName as string)

    )



    -- Finish

    if selectClones then select dupObjs

    ReplacePrompt ("Cloned: " + (dupObjs.count as string) + " objects")

)



-- Execute

-- =======================================================

undo "fnCloneAnd" on (

    fnCloneAnd()

    EnableSceneRedraw()

)

```


## Message 2

**Discerning the request**

The user provided a code snippet and described a scenario, likely seeking code that aligns with the specified style or instruction.

**Interpreting the context**

The user specified minimal, concise code examples, likely to achieve a streamlined, clear coding style. They might be asking for a summary or transformation of the provided code.

## Message 3

Below is a detailed breakdown of the stylistic patterns and coding conventions demonstrated in the provided MaxScript examples, highlighting the elements that contribute to their clarity, manageability, and adaptability. These observations not only distill what makes the code clean and readable, but also serve as a concise "style guide" for producing new scripts following the same conventions.

**Key Patterns and Style Elements**

1. **Function-Level Comments and Headers**  
   - Each function begins with a clean, single-line descriptive comment (e.g. `-- Function: Add Slice Modifier`), followed by a visual separator (`-- =======================================================`).  
   - The initial comment states the overall purpose of the function in a clear and concise manner.  
   - Comments do not become lengthy narratives; they remain short, focusing on the “why” rather than the “how,” guiding readers through the script’s intent without clutter.

2. **Minimal yet Clear In-Function Comments**  
   - Within each function, brief inline comments introduce logical blocks or explain particular actions.  
   - There is an absence of verbose commentary. Each comment addresses a direct purpose, such as “Activate the Select and Link tool” or “Add and select the slice modifier.”  
   - Comments appear aligned and spaced to separate code logic visually. They feel “woven” into the code, offering context at a glance without overwhelming it.

3. **Concise Undo Contexts**  
   - Every major operation occurs within `undo "Description" on (...)` blocks. This ensures that all actions are reversible as a single step, enhancing user control and maintaining a neat workflow.  
   - The undo description is short and focused, e.g. `"Activate Select and Link Tool"`, `"Add Slice Modifier"`, `"fnApplyViewSettings"`, etc.

4. **Consistent Functional Structure**  
   - Each script block generally follows a pattern:
     - **Function Definition**: A named function with parameter defaults when appropriate, bracketed by a `( ... )` block and introduced by a header comment block.  
     - **Core Logic**: Inside, a clearly segmented approach is used. Objects are gathered, conditions are checked, actions are taken, and feedback is provided.  
     - **Output and Feedback**: Each function prints informative messages or displays temporary prompts, letting the user know the action taken.  
     - **Finalization**: Ending with redraws (`completeRedraw()`, `enableSceneRedraw()`) ensures the user sees immediate results, maintaining a fluid workspace experience.

5. **Selective and Contextual Redraws**  
   - Calls to `disableSceneRedraw()`, `completeRedraw()`, `enableSceneRedraw()`, and `forceCompleteRedraw()` are used judiciously.  
   - Only as many redraws as needed to reflect changes are performed, avoiding unnecessary performance overhead.  
   - The code often defers full scene refreshes until the very end of an operation, keeping intermediate steps smoother.

6. **Clean Separation Between Definition and Execution**  
   - After defining the function, an `-- Execute` block appears at the bottom. It uses an `undo` block to call the function and finalize tasks (`EnableSceneRedraw()` etc.).  
   - This separation helps in quickly locating the main entry point of the script and clarifies the script’s flow.

7. **Modular and Self-Contained Functions**  
   - Each function handles a single high-level task (e.g., “Bridge Selected Edges,” “Apply Viewport Settings,” “Add Slice Modifier”).  
   - Keeping functions short and focused enhances reusability and makes future modifications or expansions straightforward.

8. **Consistent Formatting and Indentation**  
   - Code blocks are consistently indented to show structure.  
   - Parentheses and curly braces remain tidy, with no extraneous white space or unnecessary lines.  
   - Variable names are lowercase and descriptive, while function names and constants are kept simple and self-explanatory.

9. **Feedback and User-Friendly Messaging**  
   - Each operation prints a clear status message or displays a prompt, reinforcing what action was completed.  
   - This continuous feedback loop improves usability, especially for operations that might not produce obvious visual changes.

10. **Short and Purposeful Line Count**  
    - The code avoids redundant lines or spacing.  
    - Every line feels essential—no clutter, no superfluous logging, no excessive looping or complexity.

**In Summary**:  
The provided examples follow a structured, minimalistic approach to MaxScript coding. They blend succinctness with clarity, ensuring each function’s purpose is immediately understood. Comments are purposeful and elegantly sparse, while operations are neatly encapsulated within undo blocks for maximum flexibility. This style sets a high bar for readability, adaptability, and maintainability in MaxScript development—qualities that are invaluable when writing scripts that may be extended or refactored over time.

## Message 4

Please demonstrate your understanding by applying yourself to this task:

- Create a refactored version of the attached script, applying the same codingstyle to it in all aspects. Remember to be concistent and to avoid excessive comments, keep in mind that the linecount should be balanced properly with the "flow" of the code-avoid any excessive or unneccessary comments:



```maxscript

-- TODO: LEGGE TIL NOE SOM GJØR AT JEG KAN KOPIERE F.EKS. ROTASJONER. DETTE FORUTSETTER AT ALLE OBJEKTENE MÅ VÆRE UNIKE, SÅ JEG MÅ GENERERE NAVN BASERT PÅ NOE SOM GJØR DET UNIKT.



-- Copy/Paste SceneFileData (between scenes):

-- - From (Scene #1):

-- 1 Visible Objects

-- 2 Selected Objects

-- 3 All Objects

-- - Button: Load SceneData

-- - Label: "Loaded Data from 411 objects in the current scene"



-- - To (Scene #2):

-- * Visible Objects

-- * Selected Objects

-- * All Objects / Entire Scene



-- - Conditions / Matching Criteria (only apply to objects with):

-- * Match: Identical Name

-- * Match: Identical PivotPos

-- * Match: Identical WireColor

-- * Match: Identical Meshdata (Vert/Edge/FaceCount)- Button: Preview



-- - Optional: What to apply/transfer:

-- * Pivot Positions

-- * Pivot Orientations

-- * WireColors

-- * Layers

-- * Sizes (point-helpers)



-- - Actions (to perform on resulting matching objects):

-- * Select

-- * Delete



-- - Button: Apply



(

    -- ADD THE ROLLOUTS TO GLOBAL VARIABLES (SO THAT WINDOW CAN BE CREATED/DESTROYED OUTSIDE THE CURRENT SCOPE)

    global UI_LoadSceneData

    global UI_PasteSceneData



    -- DECLARE LOCAL VARIABLES FOR TEMPORARILY STORING THE RETRIEVED DATA

    sourceScene_ObjData = #()

    sourceScene_WireColors = #()

    sourceScene_RelativePivot = #()

    sourceScene_CenterPos = #()

    sourceScene_Transforms = #()

    sourceScene_Sizes = #()



    -- FUNCTION: GET THE AREA OF A MESH

    function fnGetMeshArea inputObject =

    (

        -- Retrieve and return the area of the mesh

        objDup = snapshotAsMesh inputObject

        meshArea = meshop.getFaceArea objDup objDup.faces

        delete objDup

        return meshArea

    )



    -- FUNCTION: RETRIEVE OBJECT-DATA

    function fnRetrieveObjectData inputObjects =

    (

        -- Create variables for the result

        resultObjNames = #()

        resultObjColors = #()

        resultObjMeshData = #()

        resultObjPosition = #()

        resultObjPivot = #()

        resultObjLayer = #()

        resultObjMeshArea = #()

        -- For each object passed in

        for currObj in inputObjects do

        (

            -- Collect data from the current object (Name/WireColor/MeshData/etc)

            objName      = (currObj.name)

            objColor     = (currObj.WireColor as string)

            objIsPoly    = ((classOf currObj.baseObject == Editable_Poly) or (classOf currObj.baseObject == Editable_Mesh))

            objVertCount = ((if objIsPoly then (currObj.verts.count) else 0) as string)

            objEdgeCount = ((if objIsPoly then (currObj.edges.count) else 0) as string)

            objFaceCount = ((if objIsPoly then (currObj.faces.count) else 0) as string)

            objMeshData  = ((objVertCount + "_" + objEdgeCount + "_" + objFaceCount))

            objPosition  = (currObj.position as string)

            objPivot     = ((distance currObj.pivot currObj.center) as string)

            objLayer     = (currObj.layer.name)

            objMeshArea  = ((if objIsPoly then (fnGetMeshArea currObj) else 0) as string)

            -- Append data from the current object to the result

            append resultObjNames objName

            append resultObjColors objColor

            append resultObjMeshData objMeshData

            append resultObjPosition objPosition

            append resultObjPivot objPivot

            append resultObjLayer objLayer

            append resultObjMeshArea objMeshArea

        )

        -- Return the result

        return #(resultObjNames, resultObjColors, resultObjMeshData, resultObjPosition, resultObjPivot, resultObjLayer, resultObjMeshArea)

    )



    -- ROLLOUT: COPY SCENEDATA (FROM)

    rollout UI_LoadSceneData "From: Scene #1 (Copy)" rolledUp:false

    (

        -- Create the UI elements

        group "From:"

        (

            radiobuttons rb_copyFrom "" labels:#("1. Visible Objects", "2. Selected Objects", "3. All Objects (Entire Scene)") default:2 align:#left offset:[5,0]

        )

        button btn_loadSceneData "Load SceneData" width:270 height:20



        -- Create button command

        on btn_loadSceneData pressed do

        (

            -- Retrieve the objects in the current scene (Source)

            visibleObjs = (for obj in objects where (obj.isHidden == False) collect obj)

            selectedObjs = (selection as array)

            allObjs = (for obj in objects collect obj)

            -- Determine which set of objects to use: Visible/Selected/All (by radiobutton input-value: rb_copyFrom)

            currScene_Objs = #(visibleObjs, selectedObjs, allObjs)[rb_copyFrom.state]



            -- Retrieve and store string-data/object-data from the current scene (Source) to the outer scope variables

            sourceScene_ObjData = fnRetrieveObjectData currScene_Objs

            sourceScene_WireColors = (for obj in currScene_Objs collect obj.WireColor)

            sourceScene_Transforms = (for obj in currScene_Objs collect obj.Transform)

            sourceScene_CenterPos = (for obj in currScene_Objs collect obj.Center)

            sourceScene_RelativePivot = (for obj in currScene_Objs collect (distance obj.pivot obj.center))



            -- Print information

            print ("Loaded data from: " + currScene_Objs.count as string + " objects.")

            ReplacePrompt ("Loaded data from: " + currScene_Objs.count as string + " objects.")

        )

    )



    -- ROLLOUT: PASTE SCENEDATA (TO)

    rollout UI_PasteSceneData "To: Scene #2 (Paste)" rolledUp:false

    (

        -- UI: Create UI elements

        group "To:"

        (

            radiobuttons rb_pasteTo "" labels:#("1. Visible Objects", "2. Selected Objects", "3. All Objects (Entire Scene)") default:1 align:#left offset:[5,0]

        )

        group "Match Criteria:"

        (

            checkbox cb_matchingName "Identical Object Names" checked:true toolTip:"Checked:\nObjects without identical name will be skipped." align:#left

            checkbox cb_matchingColor "Identical WireColors" checked:false toolTip:"Checked:\nObjects without identical WireColors will be skipped." align:#left

            checkbox cb_matchingMeshData "Identical MeshData" checked:true toolTip:"Checked:\nPoly/Mesh-Objects without identical MeshData will be skipped (Vert/Edge/Face Count)." align:#left

            checkbox cb_matchingPosition "Identical Positions" checked:false toolTip:"Checked:\nObjects without identical Positions will be skipped." align:#left

            checkbox cb_matchingPivot "Identical Relative Pivot" checked:true toolTip:"Checked:\nObjects without identical Relative Pivots will be skipped." align:#left

            checkbox cb_matchingLayers "Identical Layers" checked:false toolTip:"Checked:\nObjects without identical Layers will be skipped." align:#left

            checkbox cb_matchingArea "Identical Mesh-Area" checked:false toolTip:"Checked:\nPoly/Mesh-Objects without identical Mesh-Area will be skipped." align:#left

            -- checkbox cb_matchingSize "Identical Size" checked:true toolTip:"Checked:\nPoint-Helpers without identical Size will be skipped." align:#left

        )

        group "One-to-One Matching:"

        (

            checkbox cb_oneToOneMatch "Only allow &One match per Source Object" checked:true toolTip:"Checked:\nFor each source object, only the most similar object should be chosen (the rest should be ignored)." align:#left

            dropdownlist ddl_chooseBy "" selection:2 items:#("[Select Condition to Prioritize by]", "1. Closest in [Position]", "2. Closest in [Center]", "3. Closest in [Relative Pivot]") toolTip:"If multiple matches, choose object by closest ... "

        )

        group "Actions:"

        (

            checkbox cb_transferTransforms "Transfer Transforms" checked:true toolTip:"Checked:\nMatching objects will get the Transform from the source." align:#left offset:[5,0]

            checkbox cb_transferWireColors "Transfer WireColors" checked:false toolTip:"Checked:\nMatching objects will get the WireColor from the source." align:#left offset:[5,0]

            checkbox cb_transferHelperSizes "Transfer Helper Sizes" checked:false toolTip:"Checked:\nMatching objects will get the Size from the source." align:#left offset:[5,0]

        )

        group "Paste SceneData:"

        (

            radiobuttons rb_pasteToAction "" labels:#("1. Select Objects    ", "2. Delete Objects    ") default:1 align:#left offset:[5,0]

            button btn_pasteSceneData "Paste SceneData" width:270 height:20

        )



        -- Update UI elements based on what's checked

        on cb_oneToOneMatch changed inputState do

        (

            currState = (if inputState==true then #(1, 2) else #(0, 1))

            ddl_chooseBy.enabled=currState[1]

            ddl_chooseBy.selection=currState[2]

        )



        -- Create button command

        on btn_pasteSceneData pressed do

        (

            -- Create undo queue

            undo on

            (

                -- Retrieve which input criterias to match by (with unique identifier for each condition) Example: #(cb_matchingName,cb_matchingMeshData) == #(1,3)

                matchingCriteria_Inputs = #(cb_matchingName, cb_matchingColor, cb_matchingMeshData, cb_matchingPosition, cb_matchingPivot, cb_matchingLayers)

                matchingCriteria_IDs = (for typeIndex=1 to matchingCriteria_Inputs.count collect (matchingCriteria_Inputs[typeIndex].state as integer * typeIndex))

                matchingCriteria_IDs = (for currID in matchingCriteria_IDs where (currID != 0) collect currID)



                -- Retrieve the objects in the current scene (Destination)

                visibleObjs = (for obj in objects where (obj.isHidden == False) collect obj)

                selectedObjs = (selection as array)

                allObjs = (for obj in objects collect obj)

                -- Determine which set of objects to use: Visible/Selected/All (by radiobutton input-value: rb_pasteTo)

                currScene_Objs = #(visibleObjs, selectedObjs, allObjs)[rb_pasteTo.state]

                -- Retrieve object-data (Name/WireColor/MeshData/etc) from the current scene (Destination) and store it locally

                destinationScene_ObjData = fnRetrieveObjectData currScene_Objs



                -- Source: Generate compare-string (based on inputs)

                compareStrings_Source = #()

                for currIndex=1 to sourceScene_ObjData[1].count do

                (

                    currObj_Source_CompareString = ""

                    for currCriteria in matchingCriteria_IDs do ( currObj_Source_CompareString += sourceScene_ObjData[currCriteria][currIndex] )

                    append compareStrings_Source currObj_Source_CompareString

                )

                -- Destination: Generate compare-string (based on inputs)

                compareStrings_Destination = #()

                for currIndex=1 to destinationScene_ObjData[1].count do

                (

                    currObj_Destination_CompareString = ""

                    for currCriteria in matchingCriteria_IDs do ( currObj_Destination_CompareString += destinationScene_ObjData[currCriteria][currIndex] )

                    append compareStrings_Destination currObj_Destination_CompareString

                )



                -- Create a variable to store the result in (all objects in the current scene that matches those in source scene)

                matchingObjs_All = #()

                -- Create a variable to store the result as pairs in (only the closest match will be stored, necessary if we want to copy any data)

                matchingObjs_Pairs = #()



                -- For each object-string retrieved from the source scene

                for currSourceIndex=1 to compareStrings_Source.count do

                (

                    -- Create a variable for the current item (for readability purposes)

                    currItem = compareStrings_Source[currSourceIndex]



                    -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                    windows.processPostedMessages()

                    if keyboard.escPressed do (print ("Interrupted: EscPressed"); enableSceneRedraw(); exit;)

                    -- Update the progress and display information

                    currentPercentage = (matchingObjs_All.count * 100.0 / compareStrings_Source.count)

                    ReplacePrompt (currentPercentage as string + "%")



                    -- Find all occurrences of the current source-string in the destination-scene (collect the objects)

                    currItemOccurrences = (for currIndex=1 to compareStrings_Destination.count where (currItem == compareStrings_Destination[currIndex]) collect currScene_Objs[currIndex])

                    -- Append the matches to matchingObjs_All

                    for currObj in currItemOccurrences do ( appendIfUnique matchingObjs_All currObj )



                    -- Exclude any matching items that has already been added (to prevent repeating actions on the same object)

                    currItemOccurrences = (for currObj in currItemOccurrences where (findItem matchingObjs_Pairs currObj == 0) or (findItem matchingObjs_Pairs currObj == undefined) collect currObj)

                    -- If OneToOneMatch is checked (and valid) and any occurrences were found for the current source-string

                    if (cb_oneToOneMatch.state == 1) and (ddl_chooseBy.selected != 1) and (currItemOccurrences.count > 0) do

                    (

                        -- Create variables to store the matching object in

                        currItemClosestMatch = undefined

                        -- Create a qSort-function (to sort by closest value)

                        fn fnSortByClosestValue v1 v2 = if v1[2] < v2[2] then -1 else if v1[2] > v2[2] then 1 else 0





                        -- If the matches should prioritize by Position

                        if (ddl_chooseBy.selection == 2) do

                        (

                            -- Find the closest match of the objects in the destination-scene (based on distance from/to position)

                            currMatchClosestValue = (for obj in currItemOccurrences collect #(obj, (distance obj.pos (sourceScene_Transforms[currSourceIndex]).pos)))

                            qsort currMatchClosestValue fnSortByClosestValue

                            currItemClosestMatch = currMatchClosestValue[1][1]

                        )

                        -- If the matches should prioritize by Center

                        if (ddl_chooseBy.selection == 3) do

                        (

                            -- Find the closest match of the objects in the destination-scene (based on distance from/to center)

                            currMatchClosestValue = (for obj in currItemOccurrences collect #(obj, (distance obj.center sourceScene_CenterPos[currSourceIndex])))

                            qsort currMatchClosestValue fnSortByClosestValue

                            currItemClosestMatch = currMatchClosestValue[1][1]

                        )

                        -- If the matches should prioritize by Relative Pivot

                        if (ddl_chooseBy.selection == 4) do

                        (

                            -- Find the closest match of the objects in the destination-scene (based on their relative pivot)

                            currMatchClosestValue = (for obj in currItemOccurrences collect #(obj, abs((distance obj.pivot obj.center) - sourceScene_RelativePivot[currSourceIndex])))

                            qsort currMatchClosestValue fnSortByClosestValue

                            currItemClosestMatch = currMatchClosestValue[1][1]

                        )

                        -- Append the current match to the result

                        appendIfUnique matchingObjs_Pairs currItemClosestMatch





                        -- Temporarily unparent the children to prevent unwanted behaviour

                        currentChildren = for objChild in currItemClosestMatch.children collect objChild

                        currentChildren.parent = undefined



                        -- Transfer Transforms

                        if (cb_transferTransforms.state) do ( currItemClosestMatch.Transform = sourceScene_Transforms[currSourceIndex] )

                        -- Transfer WireColors

                        if (cb_transferWireColors.state) do ( currItemClosestMatch.WireColor = sourceScene_WireColors[currSourceIndex] )



                        -- Reparent children

                        currentChildren.parent = currItemClosestMatch

                    )

                )



                -- Retrieve the resulting objects

                matchingObjs_Result = (if (cb_oneToOneMatch.state == 1) then matchingObjs_Pairs else matchingObjs_All)

                -- Clear the current selection

                clearSelection()

                -- Perform specified action (Select / Delete)

                if (rb_pasteToAction.state == 1) then (select matchingObjs_Result) else (delete matchingObjs_Result)

                -- Print information

                matchCount = (matchingObjs_Result.count as string + "/" + sourceScene_ObjData[1].count as string)

                matchCountUnique = ("(" + compareStrings_Source.count as string + " unique)")

                matchStringExample = compareStrings_Source[1]

                print("Result: " + matchCount + " matches were found " + matchCountUnique + " - (Pattern: " + matchStringExample + ")")

                ReplacePrompt ("Result: " + matchCount + " matches were found " + matchCountUnique + " - (Pattern: " + matchStringExample + ")")

                completeRedraw()

            )

        )

    )

)



-- Close the window if it exists

try(closeRolloutFloater copyPasteSceneDataFloater) catch()

-- Create the floater window

copyPasteSceneDataFloater = newRolloutFloater "Copy/Paste SceneData" 300 600

addRollout UI_LoadSceneData copyPasteSceneDataFloater

addRollout UI_PasteSceneData copyPasteSceneDataFloater

```

## Message 5

**Refining code structure**

I'm tasked with refactoring a large code snippet to ensure consistency, eliminate redundant comments, and maintain a balanced line count.

**Streamlining comments**

I'm focusing on refining the code by removing verbose commentary, adding clear block headings, and maintaining a consistent style. This should actually enhance readability and structure.

**Refining code process**

I’m simplifying the code by removing excessive comments, maintaining function logic, and ensuring consistent variable naming. This approach enhances readability and preserves crucial functionalities.

**Refining UI code**

I’m refining the UI code by wrapping functions and `rollout`s in concise comments and removing extensive commentary and detailed descriptions, ensuring clarity and relevance.

**Minimizing commentary**

I'm looking to streamline the initial code by condensing large comments into a brief, focused introduction, aligning with the goal of maintaining minimal commentary.

**Organizing functions**

I’m streamlining the code by organizing functions with top-level comments, ensuring clarity and readability. This approach enhances the overall structure and consistency of the code.

**Refining the structure**

I’m organizing global variables and rollouts with concise comments, ensuring clarity and minimal commentary. This approach enhances readability and maintains code consistency.

**Enhancing UI functionality**

I’m refining the `UI_LoadSceneData` and `UI_PasteSceneData` rollouts, making buttons more effective and adding dropdown selection boxes for managing objects, improving the control flow.

**Organizing dropdowns**

I’m adding dropdown selection boxes to flavor the `UI_PasteSceneData` rollout, which aids in easier object management and enhances user experience.

**Finalizing operations**

I’m wrapping up the `UI_PasteSceneData` rollout, adding the `btn_pasteSceneData` button, and handling generator and swap objects. This completes the detailed functionality.

## Message 6

```maxscript
-- Function: Get Mesh Area
-- =======================================================
function fnGetMeshArea inputObject =
(
    objDup = snapshotAsMesh inputObject
    meshArea = meshop.getFaceArea objDup objDup.faces
    delete objDup
    meshArea
)

-- Function: Retrieve Object Data
-- =======================================================
function fnRetrieveObjectData inputObjects =
(
    resultObjNames     = #()
    resultObjColors    = #()
    resultObjMeshData  = #()
    resultObjPosition  = #()
    resultObjPivot     = #()
    resultObjLayer     = #()
    resultObjMeshArea  = #()

    for currObj in inputObjects do
    (
        objIsPoly   = ((classOf currObj.baseObject == Editable_Poly) or (classOf currObj.baseObject == Editable_Mesh))
        vertCount   = (if objIsPoly then currObj.verts.count else 0) as string
        edgeCount   = (if objIsPoly then currObj.edges.count else 0) as string
        faceCount   = (if objIsPoly then currObj.faces.count else 0) as string
        meshString  = vertCount + "_" + edgeCount + "_" + faceCount

        append resultObjNames    currObj.name
        append resultObjColors   (currObj.WireColor as string)
        append resultObjMeshData meshString
        append resultObjPosition (currObj.position as string)
        append resultObjPivot    ((distance currObj.pivot currObj.center) as string)
        append resultObjLayer    currObj.layer.name
        append resultObjMeshArea ((if objIsPoly then fnGetMeshArea currObj else 0) as string)
    )
    #(resultObjNames, resultObjColors, resultObjMeshData, resultObjPosition, resultObjPivot, resultObjLayer, resultObjMeshArea)
)

-- Globals & UI Setup
-- =======================================================
global UI_LoadSceneData
global UI_PasteSceneData

sourceScene_ObjData     = #()
sourceScene_WireColors  = #()
sourceScene_RelativePivot = #()
sourceScene_CenterPos   = #()
sourceScene_Transforms  = #()
sourceScene_Sizes       = #()

-- Rollout: From Scene #1 (Copy)
-- =======================================================
rollout UI_LoadSceneData "From: Scene #1 (Copy)" rolledUp:false
(
    group "From:"
    (
        radiobuttons rb_copyFrom "" labels:#("1. Visible Objects","2. Selected Objects","3. All Objects (Entire Scene)") default:2 align:#left offset:[5,0]
    )
    button btn_loadSceneData "Load SceneData" width:270 height:20

    on btn_loadSceneData pressed do
    (
        visibleObjs   = for obj in objects where (obj.isHidden == false) collect obj
        selectedObjs  = (selection as array)
        allObjs       = for obj in objects collect obj
        currScene_Objs = #(visibleObjs, selectedObjs, allObjs)[rb_copyFrom.state]

        sourceScene_ObjData     = fnRetrieveObjectData currScene_Objs
        sourceScene_WireColors  = for obj in currScene_Objs collect obj.WireColor
        sourceScene_Transforms  = for obj in currScene_Objs collect obj.Transform
        sourceScene_CenterPos   = for obj in currScene_Objs collect obj.Center
        sourceScene_RelativePivot = for obj in currScene_Objs collect (distance obj.pivot obj.center)

        message = "Loaded data from: " + currScene_Objs.count as string + " objects."
        print message
        ReplacePrompt message
    )
)

-- Rollout: To Scene #2 (Paste)
-- =======================================================
rollout UI_PasteSceneData "To: Scene #2 (Paste)" rolledUp:false
(
    group "To:"
    (
        radiobuttons rb_pasteTo "" labels:#("1. Visible Objects","2. Selected Objects","3. All Objects (Entire Scene)") default:1 align:#left offset:[5,0]
    )

    group "Match Criteria:"
    (
        checkbox cb_matchingName    "Identical Object Names"    checked:true
        checkbox cb_matchingColor   "Identical WireColors"      checked:false
        checkbox cb_matchingMeshData "Identical MeshData"       checked:true
        checkbox cb_matchingPosition "Identical Positions"      checked:false
        checkbox cb_matchingPivot   "Identical Relative Pivot"  checked:true
        checkbox cb_matchingLayers  "Identical Layers"          checked:false
        checkbox cb_matchingArea    "Identical Mesh-Area"       checked:false
    )

    group "One-to-One Matching:"
    (
        checkbox cb_oneToOneMatch "Only allow &One match per Source Object" checked:true
        dropdownlist ddl_chooseBy "" selection:2 items:#("[Select Condition to Prioritize by]","1. Closest in [Position]","2. Closest in [Center]","3. Closest in [Relative Pivot]")
    )

    group "Actions:"
    (
        checkbox cb_transferTransforms  "Transfer Transforms"  checked:true  offset:[5,0]
        checkbox cb_transferWireColors  "Transfer WireColors"   checked:false offset:[5,0]
        checkbox cb_transferHelperSizes "Transfer Helper Sizes" checked:false offset:[5,0]
    )

    group "Paste SceneData:"
    (
        radiobuttons rb_pasteToAction "" labels:#("1. Select Objects","2. Delete Objects") default:1 align:#left offset:[5,0]
        button btn_pasteSceneData "Paste SceneData" width:270 height:20
    )

    on cb_oneToOneMatch changed inputState do
    (
        st = if inputState then #(1,2) else #(0,1)
        ddl_chooseBy.enabled = st[1]
        ddl_chooseBy.selection = st[2]
    )

    on btn_pasteSceneData pressed do
    (
        undo "Paste SceneData" on
        (
            mcInputs = #(cb_matchingName, cb_matchingColor, cb_matchingMeshData, cb_matchingPosition, cb_matchingPivot, cb_matchingLayers)
            mcIDs = for i=1 to mcInputs.count collect (mcInputs[i].state as integer * i)
            mcIDs = for id in mcIDs where (id != 0) collect id

            visibleObjs  = for obj in objects where (obj.isHidden == false) collect obj
            selectedObjs = selection as array
            allObjs      = for obj in objects collect obj
            currScene_Objs = #(visibleObjs, selectedObjs, allObjs)[rb_pasteTo.state]

            destinationScene_ObjData = fnRetrieveObjectData currScene_Objs

            cmpSrc = #()
            for i=1 to sourceScene_ObjData[1].count do
            (
                s=""
                for c in mcIDs do s += sourceScene_ObjData[c][i]
                append cmpSrc s
            )

            cmpDst = #()
            for i=1 to destinationScene_ObjData[1].count do
            (
                s=""
                for c in mcIDs do s += destinationScene_ObjData[c][i]
                append cmpDst s
            )

            matchingObjs_All   = #()
            matchingObjs_Pairs = #()

            for si=1 to cmpSrc.count do
            (
                windows.processPostedMessages()
                if keyboard.escPressed do
                (
                    print "Interrupted: EscPressed"
                    enableSceneRedraw()
                    exit
                )
                p = (matchingObjs_All.count * 100.0 / cmpSrc.count)
                ReplacePrompt (p as string + "%")

                occ = for i=1 to cmpDst.count where (cmpSrc[si] == cmpDst[i]) collect currScene_Objs[i]
                for o in occ do appendIfUnique matchingObjs_All o

                occ = for o in occ where (findItem matchingObjs_Pairs o == 0) collect o

                if cb_oneToOneMatch.state and (ddl_chooseBy.selected != 1) and occ.count > 0 do
                (
                    fn fnSortByClosestValue v1 v2 = if v1[2]<v2[2] then -1 else if v1[2]>v2[2] then 1 else 0
                    best = undefined

                    if ddl_chooseBy.selection == 2 do
                    (
                        vals = for o in occ collect #(o, distance o.pos sourceScene_Transforms[si].pos)
                        qsort vals fnSortByClosestValue
                        best = vals[1][1]
                    )
                    if ddl_chooseBy.selection == 3 do
                    (
                        vals = for o in occ collect #(o, distance o.center sourceScene_CenterPos[si])
                        qsort vals fnSortByClosestValue
                        best = vals[1][1]
                    )
                    if ddl_chooseBy.selection == 4 do
                    (
                        vals = for o in occ collect #(o, abs((distance o.pivot o.center)-sourceScene_RelativePivot[si]))
                        qsort vals fnSortByClosestValue
                        best = vals[1][1]
                    )

                    appendIfUnique matchingObjs_Pairs best

                    ch = for c in best.children collect c
                    ch.parent = undefined

                    if cb_transferTransforms.state  do best.Transform = sourceScene_Transforms[si]
                    if cb_transferWireColors.state  do best.WireColor = sourceScene_WireColors[si]

                    ch.parent = best
                )
            )

            res = if cb_oneToOneMatch.state==1 then matchingObjs_Pairs else matchingObjs_All
            clearSelection()
            if rb_pasteToAction.state == 1 then select res else delete res

            mCount   = (res.count as string + "/" + sourceScene_ObjData[1].count as string)
            mUnique  = "(" + cmpSrc.count as string + " unique)"
            mPattern = cmpSrc[1]

            msg = "Result: " + mCount + " matches found " + mUnique + " - (Pattern: " + mPattern + ")"
            print msg
            ReplacePrompt msg
            completeRedraw()
        )
    )
)

-- Main UI Floater
-- =======================================================
try(closeRolloutFloater copyPasteSceneDataFloater) catch()
copyPasteSceneDataFloater = newRolloutFloater "Copy/Paste SceneData" 300 600
addRollout UI_LoadSceneData copyPasteSceneDataFloater
addRollout UI_PasteSceneData copyPasteSceneDataFloater
```

## Message 7

Your code looks fine and executes correctly, great job. Lets open the arena for you to fully prove yourself, here's the task (a larger and more complex example):

- Create a refactored version of the attached script, applying the same codingstyle to it in all aspects. Remember to be concistent and to avoid excessive comments, keep in mind that the linecount should be balanced properly with the "flow" of the code-avoid any excessive or unneccessary comments:



```maxscript

-- STEG 1 FOR Å EKSPORTERE OG BYGGE PÅ LIBRARY:

-- 1. I scenen jeg jobber på: Rename objects to MeshData

-- 2. I scenen jeg jobber på: Eksportere RAW/REDUCED av modeller til mappen (1.PartsUntouched)

-- 3. Kjøre script for å importere fra (1.PartsUntouched), plassere og duplisere (i forskjellige layers) alle "REDUCED" filer slik at de er klare til redusering.

--    I denne filen må jeg gjøre meg helt ferdig med redusering, så det kan lønne seg å midlertidig lagre denne scenen til det er gjort.

-- 4. Når reduseringen av objektene i det ene layeret er gjort, så erstatter jeg manuelt med de i det andre layeret (slik at transforms er like som RAW-filen)

-- 5. Kjører script for å eksportere alle reduserte modeller til mappen (2.ReducedObjects)

--    Scriptet kopierer deretter alle RAW-filer som ligger redusert i mappen (1.PartsUntouched) til (2.ReducedObjects)

-- 7. Kjører script som åpner alle filene i mappen (2.ReducedObjects) og gjør cleanup og lagrer de i mappen (3.ReadyForLibraryImport)



-- STEG 2 FOR Å FAKTISK OPPDATERE LIBRARY:

-- 1. I scenen for hovedlibrary: Importer fra mappen (3.ReadyForLibraryImport) (legges automatisk i WIP-layers)

-- 2. I scenen for hovedlibrary: Gjøre nødvendige sjekker for å vite at alt er i orden med de importerte modellene

-- 3. I scenen for hovedlibrary: Flytt de verifiserte modellene over til riktige layers

-- 4. I scenen for hovedlibrary: Kjøre script for å eksportere reduserte modeller til mappen (4.VerifiedModels) (Option: Do not replace existing)



-- STEG 3 FOR Å RYDDE OPP I FILER JEG IKKE LENGER TRENGER:

-- 1. Kjøre script for å slette alle filer i mappene (1.PartsUntouched) / (2.ReducedObjects) / (3.ReadyForLibraryImport)



-- STEG 4 FOR Å BRUKE LIBRARY TIL Å IMPORTERE OG ERSTATTE MODELLER:

-- 1. I scenen jeg jobber på: Rename objects to MeshData

-- 2. I scenen jeg jobber på: Import & Replace



-- NOTES: GENERELLE NOTATER

-- - Dersom man sletter faces på en redusert mesh man legger i meshlibrary, så vil dette tolkes som at alle tilsvarende objekter slettes når man tar 'Import and replace'.

--   Dette er fordi man i mange tilfeller ikke ønsker å redusere objektet, men bare vil slette det. Dette er f.eks. skruer/muttere og andre små deler.





-- INSTEAD OF SPECIFYING THIS SCRIPT WITHIN A MACRO, I'M USING A SEPARATE FILE

-- THAT SPECIFIES THE MACRO FOR THIS SCRIPT, WHICH THEN CALLS THIS SCRIPT-FILE.

-- THE REASON FOR THIS IS THAT I CAN DO CHANGES WITHOUT RELOADING THE SCRIPT,

-- AND IF I EXECUTE THE MACRO TWICE IT WILL RE-INITIALIZE THE SCRIPT.





-- FUNCTION: FORCE COMPLETE VIEWPORT REDRAW

function fnForceViewportRedraw =

(

    -- Update/Redraw Viewports

    enableSceneRedraw()

    completeRedraw()

    forceCompleteRedraw()

    -- Update/Redraw Toolbar Buttons

    updateToolbarButtons()

)



-- GET PROMPT LINE PANEL (CAN BE USED TO MAKE SURE FREEZE/UNFREEZE PROMPT LINE)

function fnGetPromptLinePanel =

(

    globalMsxInterface = (dotNetClass "Autodesk.Max.GlobalInterface").Instance

    statusBarContainer = (windows.getChildHwnd globalMsxInterface.coreinterface14.StatusPanelHWND "StatusPanelBottomContainer")

    promptLinePanel = (for currWin in windows.getChildrenHwnd statusBarContainer[1] where currWin[4] == "CustStatus" collect currWin[1])

    if promptLinePanel.count do ( return promptLinePanel[1] )

)



-- FUNCTION: ROUND POSITION TO SPECIFIED NUMBER OF DECIMALS

function fnRoundPositionDecimals inputPosition numDecimals =

(

    -- Round each value in the input position by the specified amount of decimals

    posX = (floor ((inputPosition[1] * (10.0 ^ numDecimals)) + 0.5)) / (10.0 ^ numDecimals)

    posY = (floor ((inputPosition[2] * (10.0 ^ numDecimals)) + 0.5)) / (10.0 ^ numDecimals)

    posZ = (floor ((inputPosition[3] * (10.0 ^ numDecimals)) + 0.5)) / (10.0 ^ numDecimals)

    -- Return the result

    return [posX, posY, posZ]

)



-- FUNCTION: FIND DUPLICATE ITEMS IN ARRAY

function fnFindDuplicatesInArray inputArray =

(

    -- Create variables to process the array

    arrayValues = #()

    duplicateItems = #()

    -- For each item in the input array

    for itemIndex=1 to inputArray.count do

    (

        -- If the current item has already been found

        if ((foundItemIndex = findItem arrayValues inputArray[itemIndex]) != 0) then

        (

            -- Append the index of the position of the current duplicate

            append duplicateItems[foundItemIndex] itemIndex

        )

        -- Else if the current item hasn't been found

        else

        (

            -- Process the current item

            append arrayValues inputArray[itemIndex]

            duplicateItems[arrayValues.count] = #{itemIndex}

        )

    )

    -- Return the result

    return duplicateItems

)



-- FUNCTION: RETURN ARRAY OF INPUT OBJECTS SEPARATED BY LAYERS

function fnSeparateObjsByLayers inputObjects =

(

    -- Create a variable to store the result in

    objArraySorted = #()

    -- Collect the layer names of the input objects

    layerNames = (for obj in inputObjects collect obj.layer.name)

    -- Split the array by duplicates

    layerNames = fnFindDuplicatesInArray layerNames

    -- For each item in the resulting array

    for itemIndex=1 to layerNames.count do

    (

        -- Create an empty array to the result variable for the current index

        append objArraySorted #()

        -- Append all objects within the current layer to the result variable

        for objIndex in layerNames[itemIndex] do ( appendIfUnique objArraySorted[itemIndex] inputObjects[objIndex] )

    )

    -- Return the result

    return objArraySorted

)



-- FUNCTION: GET THE AREA OF A MESH

function fnGetMeshArea inputObject =

(

    -- Retrieve and return the area of the mesh

    objDup = snapshotAsMesh inputObject

    meshArea = meshop.getFaceArea objDup objDup.faces

    delete objDup

    return meshArea

)



-- FUNCTION: GET THE VOLUME OF AN INPUT MESH

function fnGetMeshVolume inputObject =

(

    -- Create a variable for the volume

    meshVolume = 0.0

    -- Duplicate the input mesh and convert to a convex hull

    objDup = snapshotAsMesh inputObject

    -- For each face of mesh

    for faceIndex = 1 to objDup.numFaces do

    (

        -- Calculate the derative and increment the volume

        currentFace = getFace objDup faceIndex

        faceVertZ = getVert objDup currentFace.z

        faceVertY = getVert objDup currentFace.y

        faceVertX = getVert objDup currentFace.x

        meshVolume += Dot (Cross (faceVertY - faceVertX) (faceVertZ - faceVertX)) faceVertX

    )

    -- Delete the duplicate

    delete objDup

    -- Return the volume

    return meshVolume *= 6

)



-- FUNCTION: REMOVE EMPTY LAYERS

function fnRemoveEmptyLayers =

(

    -- Disable undo for the loop

    with undo off

    (

        -- Disable scene redraw, change panel, clear explorers and selection (for speed purposes)

        disableSceneRedraw()

        setCommandPanelTaskMode #create

        SceneExplorerManager.ClearAllExplorers()

        clearSelection()

        -- For each layer

        for layerIndex=((layerManager.count)-1) to 0 by -1 do

        (

            -- Prevent 3dsMax from stalling and interrupt if escape is pressed

            windows.processPostedMessages()

            setWaitCursor()

            if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

            -- Get the current layer name

            layerObj = LayerManager.getLayer layerIndex

            layerName = layerObj.name

            -- Get the nodes in the current layer

            (LayerManager.getLayerFromName layerName).nodes &lrNodes

            -- If current layer is empty, delete it

            if (lrNodes.count == 0) do ( layerManager.deleteLayerByName layerName )

        )

        -- Update/Redraw Viewports and set arrowCursor

        setArrowCursor()

        fnForceViewportRedraw()

    )

)



-- FUNCTION: CHECK IF A STRING CONTAINS A STRING

function fnFindInString InString SearchString IgnoreCasing =

(

    -- Set the initial state to false

    foundInStr = false

    -- Check if the input string contains the search string

    foundInStr = matchPattern InString pattern:("*" + SearchString + "*") ignoreCase:IgnoreCasing

    -- Return the result

    return foundInStr

)



-- FUNCTION: REPLACE A STRING WITHIN A STRING

function fnReplaceInString InString SearchString ReplaceString IgnoreCasing =

(

    -- If the input string and the search string has more than one character

    if (InString.count>=1 and SearchString.count>=1) do

    (

        -- Define where in the string to search based on the input string and the search string

        local searchPosition = (InString.count - SearchString.count + 1)

        -- While the search position is more than zero

        while searchPosition >=1  do

        (

            -- If IgnoreCasing was set to true

            if (IgnoreCasing == true) do

            (

                -- Check if the current pattern matches the search string

                if (substring (toLower(InString)) searchPosition SearchString.count == (toLower(SearchString))) then

                (

                    -- Find all of the text before and after the match (from the left/right)

                    local txtStrLeft = (substring Instring 1 (searchPosition-1))

                    local txtStrRight = (substring Instring (searchPosition + SearchString.count) InString.count)

                    -- Replace and update the input string

                    local InString = (txtStrLeft + ReplaceString + txtStrRight)

                    -- Update the search position

                    local searchPosition = searchPosition - SearchString.count

                )

                -- If the current pattern does not match the search string, decrement the searchPosition

                else searchPosition -= 1

            )

            -- If IgnoreCasing was set to false

            if (IgnoreCasing == false) do

            (

                -- Check if the current pattern matches the search string

                if (substring InString searchPosition SearchString.count == SearchString) then

                (

                    -- Find all of the text before and after the match (from the left/right)

                    local txtStrLeft = (substring Instring 1 (searchPosition-1))

                    local txtStrRight = (substring Instring (searchPosition + SearchString.count) InString.count)

                    -- Replace and update the input string

                    local InString = (txtStrLeft + ReplaceString + txtStrRight)

                    -- Update the search position

                    local searchPosition = searchPosition - SearchString.count

                )

                -- If the current pattern does not match the search string, decrement the searchPosition

                else searchPosition -= 1

            )

        )

    )

    -- Return the new string

    return InString

)



-- FUNCTION: REMOVE EVERYTHING EXCEPT METADATA FROM INPUT STRING

function fnStripToMetaDataOnly inputString =

(

    -- Get string before name

    indexOfSplit1 = (findString inputString "&")

    indexOfSplit1 = (if (indexOfSplit1==undefined) then inputString.count else indexOfSplit1)

    filteredName1 = (subString inputString 1 (indexOfSplit1-1))

    -- Get the actual name

    filteredName2 = (filterString inputString "&")[2]

    -- Get string after name

    stringAfter = (subString inputString (indexOfSplit1+1) inputString.count)

    indexOfSplit2 = (findString stringAfter "&")

    indexOfSplit2 = (if (indexOfSplit2==undefined) then inputString.count else indexOfSplit2)

    filteredName3 = (subString stringAfter (indexOfSplit2+1) stringAfter.count)

    -- Return the result

    return #(filteredName1, filteredName2, filteredName3)

)



-- FUNCTION: REMOVE ALL EXCEPT THE SIZE AND VERTCOUNT FOR THE INPUT METADATA (FOR PRELIMINARY COMPARISON)

function fnStripMetaToPrefixOnly inputArray =

(

    -- Create variables to store the result in

    comparableArray = #()

    -- For each object/string passed in

    for currItem in inputArray do

    (

        -- Get the nodename

        nodeName = (if (isValidNode currItem) then currItem.name else currItem)

        -- Split the name of the input node by "]_["

        nodeNameSplitted = filterString nodeName "]_[" splitEmptyTokens:false

        -- Append the prefix of the current item to the result (SizeIndex_SizeDescription_VertCount)

        append comparableArray  (nodeNameSplitted[1] + "_" + nodeNameSplitted[2] + "_" + nodeNameSplitted[3] + "_")

    )

    -- Return the result

    return comparableArray

)



-- FUNCTION: RETURN NODES WITH VALID METADATA NAME

function fnReturnNodesWithValidMetaName inputArray =

(

    -- Create a variable to store the result in

    validMetaNodes = #()

    -- For each input node passed in

    for obj in inputArray do

    (

        -- Split the name of the current node by "]_["

        nodeNameSplitted = filterString obj.name "]_[" splitEmptyTokens:false

        -- If the result of the split matches the expected result of 6, 7 or 8 items

        if (nodeNameSplitted.count == 6) or (nodeNameSplitted.count == 7) or (nodeNameSplitted.count == 8) then

        (

            -- Get object information based on it's current name

            nodeNamePosRef1 = for x in (filterString nodeNameSplitted[4] "," splitEmptyTokens:false) collect x as float

            nodeNamePosRef2 = for x in (filterString nodeNameSplitted[5] "," splitEmptyTokens:false) collect x as float

            nodeNamePosRef3 = for x in (filterString nodeNameSplitted[6] "," splitEmptyTokens:false) collect x as float

            nodeNamePosRef = #(nodeNamePosRef1, nodeNamePosRef2, nodeNamePosRef3)

            -- If the count of each result matches the expected result of 9 (transform values)

            if ((nodeNamePosRef1.count + nodeNamePosRef2.count + nodeNamePosRef3.count) == 9) then

            (

                -- Append the current node to the result

                appendIfUnique validMetaNodes obj

            )

        )

    )

    -- Return the result

    return validMetaNodes

)



-- FUNCTION: DO A QUICK CHECK TO SEE IF METADATA LOOKS TO BE CORRECT

function fnCheckIfMetaIsCorrect inputNode inputNodeName =

(

    -- Split the name of the input node by "]_["

    nodeNameSplitted = filterString inputNodeName "]_[" splitEmptyTokens:false

    -- Create a variable for the allowed deviation in transform values

    allowedDeviation = 0.002

    -- Edit the allowed deviation based on the size (extremely small objects has to have smaller allowed deviation)

    allowedDeviation = if (nodeNameSplitted[1] == "0") then 0.0001 else allowedDeviation

    allowedDeviation = if (nodeNameSplitted[1] == "1") then 0.0005 else allowedDeviation

    -- If the result of the split matches the expected result of 6, 7 or 8 items

    if (nodeNameSplitted.count == 6) or (nodeNameSplitted.count == 7) or (nodeNameSplitted.count == 8) then

    (

        -- If the inputNode is not a string: If vert count does not match the name, return false

        if isValidNode(inputNode) do ( if (nodeNameSplitted[3] as integer != inputNode.verts.count) do ( return false ) )

        -- Get object information based on it's current name

        nodeNamePosRef1 = for x in (filterString nodeNameSplitted[4] "," splitEmptyTokens:false) collect x as float

        nodeNamePosRef2 = for x in (filterString nodeNameSplitted[5] "," splitEmptyTokens:false) collect x as float

        nodeNamePosRef3 = for x in (filterString nodeNameSplitted[6] "," splitEmptyTokens:false) collect x as float

        nodeNamePosRef = #(nodeNamePosRef1, nodeNamePosRef2, nodeNamePosRef3)

        -- If the count of each result matches the expected result of 9 (transform values)

        if ((nodeNamePosRef1.count + nodeNamePosRef2.count + nodeNamePosRef3.count) == 9) then

        (

            -- Create a variable to store the base positions in

            sourceObjPosRef = #()

            -- If the inputNode is not a string

            if isValidNode(inputNode) then

            (

                -- Get object information

                sourceObjOrgRot = inputNode.rotation

                sourceObjOrgScale = inputNode.scale

                inputNode.scale = [1,1,1]

                inputNode.rotation = (quat 0 0 0 1)

                sourceObjPosRef1 = (inputNode.center - inputNode.pivot)

                sourceObjPosRef2 = ((polyop.getvert inputNode 1) - inputNode.pivot)

                sourceObjPosRef3 = ((polyop.getvert inputNode inputNode.verts.count) - inputNode.pivot)

                inputNode.scale = sourceObjOrgScale

                inputNode.rotation = sourceObjOrgRot

                sourceObjPosRef = #(sourceObjPosRef1, sourceObjPosRef2, sourceObjPosRef3)

            )

            -- Else if inputNode is a string

            else

            (

                -- Split the name of the input node by "]_["

                inputNodeSplitted = filterString inputNode "]_[" splitEmptyTokens:false

                -- Get object information based on the string

                sourceObjPosRef1 = for x in (filterString inputNodeSplitted[4] "," splitEmptyTokens:false) collect x as float

                sourceObjPosRef2 = for x in (filterString inputNodeSplitted[5] "," splitEmptyTokens:false) collect x as float

                sourceObjPosRef3 = for x in (filterString inputNodeSplitted[6] "," splitEmptyTokens:false) collect x as float

                sourceObjPosRef = #(sourceObjPosRef1, sourceObjPosRef2, sourceObjPosRef3)

            )

            -- For each array of position references

            for posRefIndex=1 to nodeNamePosRef.count do

            (

                -- For each position within the current array

                for itemIndex=1 to nodeNamePosRef[posRefIndex].count do

                (

                    -- Check if the current transform value is within the allowed deviation

                    deviationSubVal = (sourceObjPosRef[posRefIndex][itemIndex] - allowedDeviation)

                    deviationAddVal = (sourceObjPosRef[posRefIndex][itemIndex] + allowedDeviation)

                    deviationValues = #((amin #(deviationSubVal,deviationAddVal)), (amax #(deviationSubVal,deviationAddVal)))

                    isTooSmall = (nodeNamePosRef[posRefIndex][itemIndex] < deviationValues[1])

                    isTooBig = (nodeNamePosRef[posRefIndex][itemIndex] > deviationValues[2])

                    -- If deviation is outside of the allowed treshhold, return false

                    if (isTooBig==true or isTooSmall==true) do ( return false )

                )

            )

            -- If the script gets all the way here, the node is already named correctly, return true

            return true

        )

        -- Else if the count of each result does not match the expected count, return false

        else ( return false )

    )

    -- Else if the count of the split does not match the expected result of 7 items, return false

    else ( return false )

)



-- FUNCTION: GET THE PIVOT POSITION BASED ON THE OBJECT'S MESH DATA

function fnRetrievePivotPosition inputNode =

(

    -- If the current object is of type editable poly

    if ((classOf inputNode.baseobject) == Editable_Poly) then

    (

        -- Specify the face/verts to work with

        faceIdx1 = (inputNode.faces.count as integer)

        vertIdx1 = 1

        vertIdx2 = ((inputNode.verts.count / 1.2) as integer)

        vertIdx3 = ((inputNode.verts.count / 2) as integer)

        -- Generate pivot position based on: (facepos + vertpos + vertpos + vertpos / 4)

        facePos1 = (in coordsys #world polyop.getFaceCenter inputNode faceIdx1)

        vertPos1 = (in coordsys #world polyop.getVert inputNode vertIdx1)

        vertPos2 = (in coordsys #world polyop.getVert inputNode vertIdx2)

        vertPos3 = (in coordsys #world polyop.getVert inputNode vertIdx3)

        pivotPos = ((facePos1 + vertPos1 + vertPos2 + vertPos3) / 4)

        -- Return the result

        return pivotPos

    )

)



-- FUNCTION: GET MESH METADATA

function fnRetrieveMeshInfo inputNode forceRetrieve =

(

    -- If the current object is of type editable poly

    if ((classOf inputNode.baseobject) == Editable_Poly) then

    (

        -- Create a bool for whether the info has already been retrieved or not for the input object

        isObjAlreadyRenamed = fnCheckIfMetaIsCorrect inputNode inputNode.name

        -- If the input object is already renamed and forceRetrieve is false, return it's current name

        if (forceRetrieve==false) and (isObjAlreadyRenamed==true) then (return inputNode.name)

        -- Else if the node hasn't been processed

        else

        (

            -- If the object doesn't have mesh data

            if (inputNode.numFaces == 0) then

            (

                -- Return the object with "CORRUPT_" as prefix

                if ((matchPattern inputNode.name pattern:("CORRUPT_*") ignoreCase:false) == false) then (return ("CORRUPT_" + inputNode.name))

                else (return inputNode.name)

            )

            -- Else if this is a valid geometric object

            else

            (

                -- ---------------------------------------------------------------------------------------

                -- PRESERVES THE SUFFIX IF IT'S RAW OR REDUCED

                -- ---------------------------------------------------------------------------------------

                -- Create a string for the object name (instead of inputNode.name) so that it can be manipulated

                inputNodeName = inputNode.name

                -- Check if the object has RAW or REDUCED as suffix

                objSuffixToAdd = ""

                objSuffixRaw = matchPattern inputNode.name pattern:("*_RAW") ignoreCase:false

                objSuffixReduced = matchPattern inputNode.name pattern:("*_REDUCED") ignoreCase:false

                -- If any of the suffixes was found

                if (objSuffixRaw==true) or (objSuffixReduced==true) then

                (

                    -- Update the variable objSuffixToAdd based on which suffix was found, then remove it from the objectname

                    objSuffixToAdd = (if objSuffixRaw then "_RAW" else "_REDUCED")

                    -- If the suffix is "_REDUCED"

                    if (objSuffixToAdd=="_REDUCED") then

                    (

                        -- Print warning and interrupt the script if this is a reduced object

                        messageBox("WARNING: " + inputNodeName + " seems to be a reduced object, these should NOT be renamed to their metadata!")

                        exit

                    )

                )



                -- ---------------------------------------------------------------------------------------

                -- VERIFIES THE ORIGINAL OBJECTNAME (THE STRING BETWEEN THE TWO &'s)

                -- ---------------------------------------------------------------------------------------

                -- Get the original name of the object based on the string between the two &'s (&objname&)

                filteredObjName = (filterString inputNodeName "&")

                -- If an object name was not found, specify it as "obj"

                currentObjName = if (filteredObjName.count > 1) then filteredObjName[2] else (filteredObjName[1])

                -- If the object name seems to contain metadata, specify it as "obj"

                currentObjName = (if (fnFindInString currentObjName "]_[" true == true) then "obj" else currentObjName)

                -- Replace special letters

                currentObjName = fnReplaceInString currentObjName "'" ":" true

                currentObjName = fnReplaceInString currentObjName "-" "_" true

                currentObjName = fnReplaceInString currentObjName "&" "" true

                currentObjName = (if (currentObjName == "") then "obj" else currentObjName)



                -- ---------------------------------------------------------------------------------------

                -- RETRIEVES OBJECT INFORMATION AND RENAMES IT ACCORDINGLY

                -- 1. Mesh.Center - Mesh.Pivot

                -- 2. Mesh.vtx[1].position - Mesh.Pivot

                -- 3. Mesh.vtx[end].positionr - Mesh.Pivot

                -- 4. Mesh.Area

                -- 5. Mesh.Volume

                -- ---------------------------------------------------------------------------------------

                -- Set orientation and scale to zero

                sourceObjOrgCenter = inputNode.center

                sourceObjOrgRot = inputNode.rotation

                sourceObjOrgScale = inputNode.scale

                inputNode.scale = [1,1,1]

                inputNode.rotation = (quat 0 0 0 1)

                inputNode.pivot = (fnRetrievePivotPosition inputNode)

                inputNode.center = sourceObjOrgCenter

                -- Get object information

                sourceObjPosRef1 = (inputNode.center - inputNode.pivot)

                sourceObjPosRef2 = ((polyop.getvert inputNode 1) - inputNode.pivot)

                sourceObjPosRef3 = ((polyop.getvert inputNode inputNode.verts.count) - inputNode.pivot)

                sourceObjPosRef1 = fnRoundPositionDecimals sourceObjPosRef1 3

                sourceObjPosRef2 = fnRoundPositionDecimals sourceObjPosRef2 3

                sourceObjPosRef3 = fnRoundPositionDecimals sourceObjPosRef3 3

                -- Revert original object information

                inputNode.scale = sourceObjOrgScale

                inputNode.rotation = sourceObjOrgRot

                inputNode.center = sourceObjOrgCenter



                -- Get area and volume

                refMeshArea = fnGetMeshArea inputNode

                refMeshVolume = fnGetMeshVolume inputNode

                refMeshArea = (floor ((refMeshArea * (10.0 ^ 6)) + 0.5)) / (10.0 ^ 6)

                refMeshVolume = (floor ((refMeshVolume * (10.0 ^ 6)) + 0.5)) / (10.0 ^ 6)

                sizeInfo = #(refMeshArea, refMeshVolume)



                -- Generate name prefix based on mesh size

                sizePrefix = ""

                if ((refMeshArea > 30) or (refMeshVolume > 9)) then (sizePrefix="7_veryLarge_")

                else if ((refMeshArea > 5 and refMeshArea < 30) or (refMeshVolume > 2 and refMeshVolume < 9)) then (sizePrefix="6_large_")

                else if ((refMeshArea > 1 and refMeshArea < 5) or (refMeshVolume > 0.2 and refMeshVolume < 2)) then (sizePrefix="5_medium_")

                else if ((refMeshArea > 0.1 and refMeshArea < 1) or (refMeshVolume > 0.1 and refMeshVolume < 0.2)) then (sizePrefix="4_mediumSmall_")

                else if ((refMeshArea > 0.02 and refMeshArea < 0.1) or (refMeshVolume > 0.01 and refMeshVolume < 0.1)) then (sizePrefix="3_small_")

                else if ((refMeshArea > 0.005 and refMeshArea < 0.02) or (refMeshVolume > 0.0005 and refMeshVolume < 0.01)) then (sizePrefix="2_verySmall_")

                else if ((refMeshArea > 0.0005 and refMeshArea < 0.005) or (refMeshVolume > 0.0001 and refMeshVolume < 0.0005)) then (sizePrefix="1_extremelySmall_")

                else if ((refMeshArea < 0.0005) or (refMeshVolume < 0.0000001)) then (sizePrefix="0_invisibleAlmost_")



                -- Convert each item individually to get decimals on 0 (so that 0 is converted to 0.0)

                posRef1Str = ( (sourceObjPosRef1[1]) as string + "," + (sourceObjPosRef1[2]) as string + "," + (sourceObjPosRef1[3]) as string )

                posRef2Str = ( (sourceObjPosRef2[1]) as string + "," + (sourceObjPosRef2[2]) as string + "," + (sourceObjPosRef2[3]) as string )

                posRef3Str = ( (sourceObjPosRef3[1]) as string + "," + (sourceObjPosRef3[2]) as string + "," + (sourceObjPosRef3[3]) as string )

                -- Generate name based on metadata for the mesh

                filteredObjMeta = sizePrefix + inputNode.verts.count as string

                filteredObjMeta = filteredObjMeta + "_[" + posRef1Str + "]_[" + posRef2Str + "]_[" + posRef3Str + "]"



                -- Preserve the objectname, but filter it so that illegal characters is replaces

                filteredObjName = (if currentObjName.count > 50 then (subString currentObjName 1 50) else currentObjName)

                filteredObjName = fnReplaceInString filteredObjName filteredObjMeta "" true

                filteredObjName = fnReplaceInString filteredObjName ":" "'" true

                filteredObjName = fnReplaceInString filteredObjName "_" "-" true

                filteredObjName = fnReplaceInString filteredObjName "&" "" true

                filteredObjName = fnReplaceInString filteredObjName "/" "" true

                filteredObjName = fnReplaceInString filteredObjName " " "" true

                filteredObjName = fnReplaceInString filteredObjName "," "" true

                filteredObjName = fnReplaceInString filteredObjName "(" "" true

                filteredObjName = fnReplaceInString filteredObjName ")" "" true

                filteredObjName = fnReplaceInString filteredObjName "\"" "" true



                -- Return the mesh data

                return (filteredObjMeta + "&" + filteredObjName + "&" + objSuffixToAdd)

            )

        )

    )

)



-- FUNCTION: PLACE LIBRARY OBJECTS IN GRID (ONLY AFFECTS OBJECTS WITH RAW OR REDUCED AS SUFFIX)

function fnPlaceLibraryObjsInGrid inputNodes distanceApart alignInPairs =

(

    -- Create a counter for the number of positioned objects

    positionedObjCount = 0

    -- Create undo queue

    undo "PlaceLibraryObjsInGrid" on

    (

        -- Create variables for positioning of the objects

        squareIdentifier = 0

        countRowX = 0

        countRowY = 0

        -- For each imported object

        for tmpObjIndex=1 to inputNodes.count do

        (

            -- Position the current object

            inputNodes[tmpObjIndex].center = [countRowX, countRowY, 0]

            -- inputNodes[tmpObjIndex].position = [countRowX, countRowY, 0]

            positionedObjCount += 1



            -- If it should try to align in pairs

            if (alignInPairs==true) do

            (

                -- Look for a reduced version of the current raw object

                currObjReduced = getNodeByName ((subString inputNodes[tmpObjIndex].name 1 (inputNodes[tmpObjIndex].name.count-4)) + "_REDUCED")

                -- If a reduced version of the current raw object was found, position it at the current raw object's position

                if (currObjReduced != undefined) then (currObjReduced.position=inputNodes[tmpObjIndex].position; positionedObjCount+=1)

                else ( print ("Could not find a reduced version of the current object: " + inputNodes[tmpObjIndex].name) )

            )



            -- Increment the X/Y positions based on the current index

            if (squareIdentifier == (sqrt inputNodes.count)) then ( countRowX+=distanceApart; countRowY=0; squareIdentifier=0 )

            else (countRowY+=distanceApart; squareIdentifier+=1)

        )

    )

    -- Return the number of positioned objects

    return positionedObjCount

)



-- ROLLOUT: PROGRESSBAR USED FOR ALL OPERATIONS OF THE SCRIPT

rollout UI_LibraryProgressBar "Progressbar" rolledUp:false

(

    progressbar prg_totalLibraryProgress color:green

    label lbl_totalLibraryProgress

)





-- ROLLOUT: RENAME ALL OBJECTS TO NAMES BASED ON THEIR UNIQUE MESH & TRANSFORM DATA

rollout UI_RenameToMeshData "Rename Objects Based on Mesh" rolledUp:false

(

    -- UI

    checkbox chk_skipProbablyRenamed "Skip nodes which seem to already be renamed" checked:true

    button btn_renameToMeshData "Rename Visible Objects to Mesh Metadata" width:280 height:20

    button btn_revertToOriginal "Revert Naming on Visible Objects back to Original" width:280 height:20

    button btn_findNotRenamed "Find Nodes that has not been Renamed" width:280 height:20

    button btn_findCorrupt "Find Nodes that are Corrupt" width:280 height:20



    -- Create the button command

    on btn_renameToMeshData pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Get the initial selection and set to waitCursor

            initialSelection = selection as array

            setWaitCursor()

            -- Disable scene redraw, change panel, clear explorers and selection (for speed purposes)

            disableSceneRedraw()

            setCommandPanelTaskMode #create

            SceneExplorerManager.ClearAllExplorers()

            clearSelection()

            -- Get user input

            forceRename = if (chk_skipProbablyRenamed.state == true) then false else true

            -- Get all visible geometric objects in the scene

            sceneGeometryArray = (for obj in geometry where (obj.isHidden == false) collect obj)

            -- Create a variable for the resulting (renamed) and skipped (already renamed) objects

            renamedNodesResult = #()

            alreadyRenamedObjs = #()

            -- Prompt to confirm action

            confirmAction = querybox("Do NOT use this in the Main Mesh Library!\n\nMake sure these are original RAW objects! If this is done on already reduced objects, their name will mismatch from their pairing and thus be rendered useless! This could affect " + sceneGeometryArray.count as string + " objects.\n\nDo you want to continue?")

            -- If confirmed

            if (confirmAction==true) do

            (

                -- Disable undo

                with undo off

                (

                    -- Print information

                    print ("Starting: Working with " + sceneGeometryArray.count as string + " objects")

                    -- For each geometry node

                    for objIndex=1 to sceneGeometryArray.count do

                    (

                        -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                        windows.processPostedMessages()

                        setWaitCursor()

                        if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                        -- Update progressbar

                        currentPercentage = (objIndex * 100.0 / sceneGeometryArray.count)

                        UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                        UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%" + " (" + objIndex as string + " of " + sceneGeometryArray.count as string + ")")

                        displayTempPrompt (currentPercentage as string + "%") 10000



                        -- If the current object is of type editable poly

                        if ((classOf sceneGeometryArray[objIndex].baseobject) == Editable_Poly) then

                        (

                            -- Remove invalid characters from object name

                            -- Rename the current object based on its unique mesh identifiers

                            objMeshData = fnRetrieveMeshInfo sceneGeometryArray[objIndex] forceRename

                            -- If it managed to retrieve mesh metadata

                            if (objMeshData != undefined) then

                            (

                                -- Strip to metadata (for comparison)

                                orgObjMeshMeta = (fnStripToMetaDataOnly sceneGeometryArray[objIndex].name)

                                newObjMeshMeta = (fnStripToMetaDataOnly objMeshData)

                                -- If the mesh metadata hasn't been changed (no need to rename)

                                if (orgObjMeshMeta[1] == newObjMeshMeta[1]) and (orgObjMeshMeta[2] == newObjMeshMeta[2]) then

                                (

                                    -- Append the objects to the list of skipped objects

                                    append alreadyRenamedObjs sceneGeometryArray[objIndex]

                                )

                                -- Else if this node should be renamed

                                else

                                (

                                    -- Print information

                                    format ("OldName: " + sceneGeometryArray[objIndex].name + "\n")

                                    format ("NewName: " + objMeshData + "\n\n")

                                    -- Rename the current object and append it to the result

                                    sceneGeometryArray[objIndex].name = objMeshData

                                    append renamedNodesResult sceneGeometryArray[objIndex]

                                )

                            )

                            -- Else if something prevented it to retrieve mesh metadata, print information

                            else ( print("Skipped " + sceneGeometryArray[objIndex].name + ": Couldn't retrieve mesh metadata!") )

                        )

                        -- Else if it's not a geometry object, print information

                        else print ("Skipped " + sceneGeometryArray[objIndex].name + ": It is not Editable Poly!")

                    )

                )

                -- Update progress textfield and set arrowCursor

                UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

                -- Set the selection based on if the objects are affected or not

                if (renamedNodesResult.count > 0) then (select renamedNodesResult) else (select initialSelection)

                -- Print information

                basedOnObjCount = sceneGeometryArray.count as string

                renamedCount = (renamedNodesResult.count) as string

                notAffectedCount = (alreadyRenamedObjs.count) as string

                if (renamedNodesResult.count > 0) then (print (renamedCount + " of " + basedOnObjCount + " object(s) was renamed"))

                if (alreadyRenamedObjs.count > 0) then (print (notAffectedCount + " of " + basedOnObjCount + " object(s) seemed to already be renamed correctly"))

                print ("Finished")

            )

            -- Update/Redraw Viewports and set arrowCurso

            setArrowCursor()

            fnForceViewportRedraw()

        )

    )

    -- Create the button command

    on btn_revertToOriginal pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Create a variable to store the affected nodes in

            nodesAffected = #()

            -- Get all visible geometric objects in the scene

            sceneGeometryArray = (for obj in geometry where (obj.isHidden == false) collect obj)

            -- Prompt to confirm action

            confirmAction = querybox("Do NOT use this in the Main Mesh Library as it will remove the RAW/REDUCED suffix!\n\nMake sure these are original RAW objects! If this is done on already reduced objects, their name will mismatch from their pairing and thus be rendered useless! This could affect " + sceneGeometryArray.count as string + " objects.\n\nDo you want to continue?")

            -- If confirmed

            if (confirmAction==true) do

            (

                -- Get the initial selection and set to waitCursor

                initialSelection = selection as array

                setWaitCursor()

                -- Disable scene redraw, change panel, clear explorers and selection (for speed purposes)

                disableSceneRedraw()

                setCommandPanelTaskMode #create

                SceneExplorerManager.ClearAllExplorers()

                clearSelection()

                -- Print information

                print ("Starting: Working with " + sceneGeometryArray.count as string + " objects")

                -- For each geometry node

                for objIndex=1 to sceneGeometryArray.count do

                (

                    -- If the current object is of type editable poly

                    if ((classOf sceneGeometryArray[objIndex].baseobject) == Editable_Poly) then

                    (

                        -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                        windows.processPostedMessages()

                        setWaitCursor()

                        if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                        -- Update progressbar

                        currentPercentage = (objIndex * 100.0 / sceneGeometryArray.count)

                        UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                        UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

                        displayTempPrompt (currentPercentage as string + "%") 10000



                        -- Get the object's original name

                        orgObjName = sceneGeometryArray[objIndex].name

                        -- Check if the object has RAW or REDUCED as suffix

                        objSuffixRaw = matchPattern sceneGeometryArray[objIndex].name pattern:("*_RAW") ignoreCase:false

                        objSuffixReduced = matchPattern sceneGeometryArray[objIndex].name pattern:("*_REDUCED") ignoreCase:false

                        -- If any of the suffixes was found

                        if (objSuffixRaw==true) or (objSuffixReduced==true) then

                        (

                            -- Remove the suffix from the

                            objSuffixToRemove = (if objSuffixRaw then "_RAW" else "_REDUCED")

                            sceneGeometryArray[objIndex].name = (fnReplaceInString sceneGeometryArray[objIndex].name objSuffixToRemove "" true)

                        )



                        -- Get the original name of the object based on the string between the two &'s (&objname&)

                        filteredObjName = (filterString sceneGeometryArray[objIndex].name "&")

                        -- If an object name was not found, specify it as "obj"

                        currentObjName = if (filteredObjName.count > 1) then filteredObjName[2] else filteredObjName[1]

                        -- If the object name seems to contain metadata, specify it as "obj"

                        currentObjName = (if (fnFindInString currentObjName "]_[" true == true) then "obj" else currentObjName)

                        -- Replace special letters

                        currentObjName = fnReplaceInString currentObjName "'" ":" true

                        currentObjName = fnReplaceInString currentObjName "-" "_" true

                        currentObjName = fnReplaceInString currentObjName "&" "" true

                        currentObjName = (if (currentObjName == "") then "obj" else currentObjName)

                        -- If the object has been affected

                        if (currentObjName != orgObjName) do

                        (

                            -- Print information

                            format ("OldName: " + orgObjName + "\n")

                            format ("NewName: " + currentObjName+ "\n\n")

                            -- Rename the current object and append it to the result

                            sceneGeometryArray[objIndex].name = currentObjName

                            appendIfUnique nodesAffected sceneGeometryArray[objIndex]

                        )

                    )

                    -- Else if it's not a geometry object, print information

                    else print ("Skipped " + sceneGeometryArray[objIndex].name + ": It is not Editable Poly!")

                )

                -- Update progress textfield and set arrowCursor

                UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

                -- Set the selection based on if the objects are affected or not

                if (nodesAffected.count > 0) then (select nodesAffected) else (select initialSelection)

                -- Print information

                print ("Finished: (" + nodesAffected.count as string + " of " + sceneGeometryArray.count as string + ") objects has been reverted to their original name")

                -- Update/Redraw Viewports and set arrowCurso

                setArrowCursor()

                fnForceViewportRedraw()

            )

        )

    )

    -- Create the button command

    on btn_findNotRenamed pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Disable scene redraw, change panel, clear explorers and selection (for speed purposes)

            disableSceneRedraw()

            setCommandPanelTaskMode #create

            SceneExplorerManager.ClearAllExplorers()

            clearSelection()

            -- Create a variable to store the resulting nodes in

            nodesNotRenamed = #()

            -- Get all visible geometric objects in the scene

            sceneGeometryArray = (for obj in geometry where (obj.isHidden == false) collect obj)

            -- Print information

            print ("Starting: Working with " + sceneGeometryArray.count as string + " objects")

            -- For each geometry node

            for objIndex=1 to sceneGeometryArray.count do

            (

                -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                windows.processPostedMessages()

                setWaitCursor()

                if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                -- Update progressbar

                currentPercentage = (objIndex * 100.0 / sceneGeometryArray.count)

                UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                -- UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "% | Obj: " + sceneGeometryArray[objIndex].name)

                UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

                displayTempPrompt (currentPercentage as string + "%") 10000

                -- If the info has not been retrieved for the current object

                if (fnFindInString sceneGeometryArray[objIndex].name "]_[" true == false) then

                (

                    -- Append it to the result

                    appendIfUnique nodesNotRenamed sceneGeometryArray[objIndex]

                )

            )

            -- Select the resulting nodes

            clearSelection()

            select nodesNotRenamed

            -- Update progress textfield, print information and set arrowCursor

            UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

            print ("Found " + nodesNotRenamed.count as string + " objects that has not been renamed to metadata")

            -- Update/Redraw Viewports and set arrowCurso

            setArrowCursor()

            fnForceViewportRedraw()

        )

    )



    -- Create the button command

    on btn_findCorrupt pressed do

    (

        -- Get all visible geometric objects in the scene

        sceneGeometryArray = (for obj in geometry where (obj.isHidden == false) collect obj)

        -- Find all nodes with "CORRUPT_" as prefix

        corruptObjs = for obj in sceneGeometryArray where ((matchPattern obj.name pattern:("CORRUPT_*") ignoreCase:false) == true) collect obj

        -- Select the resulting nodes

        select corruptObjs

    )



)



-- ROLLOUT: IMPORT AND REPLACE OBJECTS

rollout UI_ImportAndReplaceFromLibrary "Import and Replace Objects" rolledUp:false

(

    -- UI: Button

    button btn_selectAvailableParts "Select Visible Available Parts" width:280 height:20 toolTip:"Selects objects in the current scene that exists in the verified library"

    button btn_importReplaceParts "Import and Replace Visible Replace Parts" width:280 height:20 toolTip:"Imports objects from: .../4.Verified/\nReplaces any object in the current scene that correlates in mesh metadata"

    -- UI: CREATE THE UI ELEMENTS

    checkBox cb_colorChange "Import and Replace: Update wirecolors" checked:true width:280 toolTip:"Import and Replace: Update wirecolors"

    checkBox cb_layerChange "Import and Replace: Update layers" checked:true width:280 toolTip:"Import and Replace: Update layers"



    -- Create the button command #1

    on btn_selectAvailableParts pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Disable scene redraw, change panel, clear explorers and selection (for speed purposes)

            disableSceneRedraw()

            setCommandPanelTaskMode #create

            SceneExplorerManager.ClearAllExplorers()

            clearSelection()

            setWaitCursor()

            -- Update progressbar

            UI_LibraryProgressBar.prg_totalLibraryProgress.value = 0

            UI_LibraryProgressBar.lbl_totalLibraryProgress.text = "Initializing ..."



            -- Create a variable for objects that exists in the library

            objsExistsInLibrary = #()



            -- Specify the library path

            folderPathToLibrary = (driveLetter + "Misc/ModelLibraryNew/4.Verified/")

            makeDir folderPathToLibrary



            -- Collect all of the files in the library with the suffix "_REDUCED"

            libraryFiles = getFiles (folderPathToLibrary + ("*_REDUCED.max"))

            -- Get all of the mesh metadata names from the objects in the scene

            libraryFileStrippedNames = (for currFile in libraryFiles collect (fnStripToMetaDataOnly (getFileNameFile currFile))[1])

            -- Generate comparable metadata array for the stripped library filenames (suffix removed, size and vertcount remains)

            libraryCompareNames = fnStripMetaToPrefixOnly libraryFileStrippedNames



            -- Get all visible geometric objects in the scene

            sceneGeometryArray = (for obj in geometry where (obj.isHidden == false) collect obj)

            -- Filter out nodes with invalid metadata name

            sceneGeometryArray = fnReturnNodesWithValidMetaName sceneGeometryArray

            -- Get all of the mesh metadata names from the objects in the scene

            sceneGeometryStrippedNames = (for obj in sceneGeometryArray collect (fnStripToMetaDataOnly obj.name)[1])

            -- Find all identical geometric objects in the scene based on metadata name (that is visible)

            identicalObjsInScene = fnFindDuplicatesInArray sceneGeometryStrippedNames



            -- For each duplicate object

            for objIndex=1 to identicalObjsInScene.count do

            (

                -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                windows.processPostedMessages()

                setWaitCursor()

                if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                -- Update progressbar

                currentPercentage = (objIndex * 100.0 / identicalObjsInScene.count)

                UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

                displayTempPrompt (currentPercentage as string + "%") 10000



                -- Get the current object based on the current index

                currentObjNode = sceneGeometryArray[(identicalObjsInScene[objIndex] as array)[1]]

                currentObjName = sceneGeometryStrippedNames[(identicalObjsInScene[objIndex] as array)[1]]

                -- Get the current stripped object name (suffix removed, size and vertcount remains)

                currentStrippedObjName = (fnStripMetaToPrefixOnly #(currentObjName))[1]

                -- Find all instances of the current (stripped) object in the library compare array

                foundItemIndex = (for i=1 to libraryCompareNames.count where libraryCompareNames[i] == currentStrippedObjName collect i)

                -- Filter and return the library path that match the current object

                filePathToLibrary = (for x in foundItemIndex where (fnCheckIfMetaIsCorrect currentObjName libraryFileStrippedNames[x]) collect libraryFiles[x])

                -- If a library file was found for the current object

                if (filePathToLibrary.count != 0) do

                (

                    -- Collect all of the objects with the current stripped name

                    for tmpIndex in (identicalObjsInScene[objIndex] as array) do appendIfUnique objsExistsInLibrary sceneGeometryArray[tmpIndex]

                )

            )

            -- Select the result

            clearSelection()

            select objsExistsInLibrary

            -- Update progress textfield, print information and set arrowCursor

            UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

            print ("Found " + objsExistsInLibrary.count as string + " objects that can be replaced from the library: " + folderPathToLibrary)

            -- Update/Redraw Viewports and set arrowCurso

            setArrowCursor()

            fnForceViewportRedraw()

        )

    )



    -- Create the button command #1

    on btn_importReplaceParts pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Get all visible geometric objects in the scene

            sceneGeometryArray = (for obj in geometry where (obj.isHidden == false) collect obj)

            -- Prompt to confirm action

            confirmAction = querybox("Do NOT use this in the Main Mesh Library!\n\nAre you sure you want to import and replace visible objects?\n(This could affect " + sceneGeometryArray.count as string + " objects)")

            -- If confirmed

            if (confirmAction==true) do

            (

                -- Retrieve the prompt line panel (so that I have complete control over the messages)

                promptLinePanel = fnGetPromptLinePanel()

                -- Get user input

                doChangeWireColor = cb_colorChange.state

                doChangeLayers = cb_layerChange.state



                -- Disable scene redraw, change panel, clear explorers and selection (for speed purposes)

                disableSceneRedraw()

                setCommandPanelTaskMode #create

                SceneExplorerManager.ClearAllExplorers()

                clearSelection()

                setWaitCursor()

                -- Update progressbar

                UI_LibraryProgressBar.prg_totalLibraryProgress.value = 0

                UI_LibraryProgressBar.lbl_totalLibraryProgress.text = "Initializing ..."

                -- Create the layer for the imported objects

                importedPartsLayer = (LayerManager.newLayerFromName "Fixed")

                importedPartsLayer = (LayerManager.getLayerFromName "Fixed")

                -- Create a layer to store objects to delete in

                objsToDeleteLayer = (LayerManager.newLayerFromName "OBJS_TO_DELETE")

                objsToDeleteLayer = (LayerManager.getLayerFromName "OBJS_TO_DELETE")

                objsToDeleteLayer.isHidden = 1



                -- Specify the library path

                folderPathToLibrary = (driveLetter + "Misc/ModelLibraryNew/4.Verified/")

                makeDir folderPathToLibrary





                -- Create variables for the trianglecount (for entire scene and for affected objects)

                allObjsTriCountOrg = 0

                allObjsTriCountNew = 0

                libObjsTriCountOrg = 0

                libObjsTriCountNew = 0

                -- Get the current trianglecount for the entire scene

                for obj in (for obj in geometry where (obj.isHidden==false) collect obj) do (allObjsTriCountOrg += (GetTriMeshFaceCount obj)[1])



                -- Create a variable to store the result in

                replacedObjsFromLibrary = #()

                deletedObjsFromLibrary = #()



                -- Collect all of the files in the library with the suffix "_REDUCED"

                libraryFiles = getFiles (folderPathToLibrary + ("*_REDUCED.max"))

                -- Get all of the mesh metadata names from the objects in the scene

                libraryFileStrippedNames = (for currFile in libraryFiles collect (fnStripToMetaDataOnly (getFileNameFile currFile))[1])

                -- Generate comparable metadata array for the stripped library filenames (suffix removed, size and vertcount remains)

                libraryCompareNames = fnStripMetaToPrefixOnly libraryFileStrippedNames



                -- Get all visible geometric objects in the scene

                sceneGeometryArray = (for obj in geometry where (obj.isHidden == false) collect obj)

                -- Filter out nodes with invalid metadata name

                sceneGeometryArray = fnReturnNodesWithValidMetaName sceneGeometryArray

                -- Get all of the mesh metadata names from the objects in the scene

                sceneGeometryStrippedNames = (for obj in sceneGeometryArray collect (fnStripToMetaDataOnly obj.name)[1])

                -- Find all identical geometric objects in the scene based on metadata name (that is visible)

                identicalObjsInScene = fnFindDuplicatesInArray sceneGeometryStrippedNames



                -- For each reduced file

                for objIndex=1 to identicalObjsInScene.count do

                (

                    -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                    windows.processPostedMessages()

                    setWaitCursor()

                    if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                    -- Update progressbar

                    currentPercentage = (objIndex * 100.0 / identicalObjsInScene.count)

                    UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

                    -- Update the progress in the prompt line

                    windows.sendMessage promptLinePanel 11 1 0

                    displayTempPrompt (currentPercentage as string + "%") 10000

                    windows.sendMessage promptLinePanel 11 0 0



                    -- Get the current object based on the current index

                    currentObjNode = sceneGeometryArray[(identicalObjsInScene[objIndex] as array)[1]]

                    currentObjName = sceneGeometryStrippedNames[(identicalObjsInScene[objIndex] as array)[1]]

                    -- Get the current stripped object name (suffix removed, size and vertcount remains)

                    currentStrippedObjName = (fnStripMetaToPrefixOnly #(currentObjName))[1]

                    -- Find all instances of the current (stripped) object in the library compare array

                    foundItemIndex = (for i=1 to libraryCompareNames.count where libraryCompareNames[i] == currentStrippedObjName collect i)

                    -- Filter and return the library path that match the current object

                    filePathToLibrary = (for x in foundItemIndex where (fnCheckIfMetaIsCorrect currentObjName libraryFileStrippedNames[x]) collect libraryFiles[x])



                    -- If a library file was found for the current object

                    if (filePathToLibrary.count != 0) do

                    (

                        -- Create a variable for the current batch of matching objects

                        tmpImportedObjs =#()

                        objsReplaced = 0

                        objsToDelete = 0

                        -- Import the current object from the library

                        mergeMAXFile filePathToLibrary[1] #useSceneMtlDups #AutoRenameDups

                        importedObjects = getLastMergedNodes()

                        -- Collect all of the objects with the current stripped name

                        matchingCurrentNode = (for tmpIndex in (identicalObjsInScene[objIndex] as array) collect sceneGeometryArray[tmpIndex])

                        -- For each object to replace

                        for obj in matchingCurrentNode do

                        (

                            -- if the current object is valid

                            if (isValidNode obj) do

                            (

                                -- Add the current object's initial trianglecount to the counter

                                libObjsTriCountOrg += (GetTriMeshFaceCount obj)[1]

                                -- Prevent 3ds Max from hanging

                                windows.processPostedMessages()

                                setWaitCursor()

                                -- If the current imported object has faces (it should replace current object)

                                if (importedObjects[1].numFaces > 0) then

                                (

                                    -- Replace the current node

                                    replaceObj = snapshot importedObjects[1]

                                    convertToPoly replaceObj

                                    replaceObj.scale = obj.scale

                                    replaceObj.rotation = obj.rotation

                                    replaceObj.position = obj.position

                                    polyop.deleteFaces obj obj.faces

                                    obj.EditablePoly.attach replaceObj obj

                                    update obj.mesh geometry:true topology:true

                                    -- If colors should be updated, set wirecolor

                                    if (doChangeWireColor==true) do ( obj.wirecolor = color 7 174 90 )

                                    -- If layers should be updated, add the node to the correct layer

                                    if (doChangeLayers==true) do ( if (importedPartsLayer != undefined) do (importedPartsLayer.addNode obj) )

                                    -- Add the current object's reduced trianglecount to the counter

                                    libObjsTriCountNew += (GetTriMeshFaceCount obj)[1]

                                    -- Add the replaced object to the result variable

                                    appendIfUnique replacedObjsFromLibrary obj

                                    -- Add the imported object to the variable for objects to delete

                                    appendIfUnique tmpImportedObjs importedObjects[1]

                                    -- Increment counter

                                    objsReplaced += 1

                                )

                                -- Else if the object does not have any faces (it should be put in a layer with objects to delete)

                                else

                                (

                                    -- Add the current object to the layer for objects to delete

                                    objsToDeleteLayer.addNode obj

                                    obj.wirecolor = color 245 20 20

                                    appendIfUnique deletedObjsFromLibrary obj

                                    appendIfUnique tmpImportedObjs importedObjects[1]

                                    -- Increment counter

                                    objsToDelete += 1

                                )

                            )

                        )

                        -- Print information

                        if (objsToDelete > 0) do

                        (

                            print ("- Placed " + objsToDelete as string + " nodes in layer: OBJS_TO_DELETE (" + currentObjName + ")")

                            displayTempPrompt ("- Placed " + objsToDelete as string + " nodes in layer: OBJS_TO_DELETE (" + currentObjName + ")") 10000

                        )

                        if (objsReplaced > 0) do

                        (

                            print ("Replaced " + objsReplaced as string + " nodes with " + currentObjName)

                            displayTempPrompt ("Replaced " + objsReplaced as string + " nodes with " + currentObjName) 10000

                        )



                        -- Delete the initial import

                        try(delete (for obj in tmpImportedObjs where (isValidNode obj == true) collect obj))catch()

                    )

                )

                -- Unfreeze the prompt panel

                windows.sendMessage promptLinePanel 11 1 0

                -- Get the current trianglecount for the entire scene

                for obj in (for obj in geometry where (obj.isHidden==false) collect obj) do (allObjsTriCountNew += (GetTriMeshFaceCount obj)[1])

                -- Calculate how much was reduced

                reducedAmountScene = (100 - (allObjsTriCountNew * 100.0 / allObjsTriCountOrg)) as string

                reducedAmountLib = (100 - (libObjsTriCountNew * 100.0 / libObjsTriCountOrg)) as string

                -- Print information about how much the scene has been reduced

                print (" :: Reduced scene by " + reducedAmountScene + "%")

                print (" :: Reduced library-objects by " + reducedAmountLib + "%")



                -- Remove empty layers

                fnRemoveEmptyLayers()

                -- Select the result

                clearSelection()

                select replacedObjsFromLibrary

                -- Update progress textfield, print information and set arrowCursor

                UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

                print ("Found " + deletedObjsFromLibrary.count as string + " objects that is put in layer: OBJS_TO_DELETE")

                print ("Found " + replacedObjsFromLibrary.count as string + " objects that was replaced from the library: " + folderPathToLibrary)

                -- Update/Redraw Viewports and set arrowCurso

                setArrowCursor()

                fnForceViewportRedraw()

            )

        )

    )

)



-- ROLLOUT: SELECT IDENTICAL OBJECTS

rollout UI_SelectIdentical "Select Identical Objects" rolledUp:false

(

    -- UI

    group "Include the Following Object Sizes"

    (

        -- UI: CREATE THE UI ELEMENTS

        checkBox cb_size0 "0" offset:[10,0] checked:false width:30 toolTip:"0_invisibleAlmost"

        checkBox cb_size1 "1" offset:[40,-20] checked:true width:30 toolTip:"1_extremelySmall"

        checkBox cb_size2 "2" offset:[70,-20] checked:true width:30 toolTip:"2_verySmall"

        checkBox cb_size3 "3" offset:[100,-20] checked:true width:30 toolTip:"3_small"

        checkBox cb_size4 "4" offset:[130,-20] checked:true width:30 toolTip:"4_mediumSmall"

        checkBox cb_size5 "5" offset:[160,-20] checked:true width:30 toolTip:"5_medium"

        checkBox cb_size6 "6" offset:[190,-20] checked:true width:30 toolTip:"6_large"

        checkBox cb_size7 "7" offset:[220,-20] checked:true width:30 toolTip:"7_veryLarge"

    )

    spinner spn_minIdentical "Minimum Number of Identical Objects: " range:[1,1000,1] type:#integer width:180 align:#left toolTip:"Minimum number of identical instances for the object to be acquired"

    button btn_selOneOfEach "Select One of Each Identical Object" width:280 height:20 toolTip:"Selects one instance of each object, based on visible objects in the scene"

    button btn_selIdentical "Select Identical Objects to Selection" width:280 height:20 toolTip:"Selects identical objects to the current selection, based on metaname"

    button btn_selOrigoPivot "Select Objects with Pivot in Origo [0,0,0]" width:280 height:20 toolTip:"Select objects with pivot in origo, these objects should probably not be exported"



    -- Create the button command

    on btn_selOneOfEach pressed do

    (

        -- Get user input

        objSizes = #()

        append objSizes (if (cb_size0.state == true) then "0_*" else false)

        append objSizes (if (cb_size1.state == true) then "1_*" else false)

        append objSizes (if (cb_size2.state == true) then "2_*" else false)

        append objSizes (if (cb_size3.state == true) then "3_*" else false)

        append objSizes (if (cb_size4.state == true) then "4_*" else false)

        append objSizes (if (cb_size5.state == true) then "5_*" else false)

        append objSizes (if (cb_size6.state == true) then "6_*" else false)

        append objSizes (if (cb_size7.state == true) then "7_*" else false)



        -- Set the command panel to create and set to waitCursor

        setCommandPanelTaskMode #create

        setWaitCursor()



        -- Create a variable to store the result in

        uniqueIdenticalObjs = #()

        -- Create a variable for the objects to work with

        sceneGeometryArray = #()



        -- For each visible geometric object in the scene

        for obj in geometry where (obj.isHidden == false) do

        (

            -- If the current object is not just a box (which cannot be reduced further)

            -- if ((obj.verts.count > 8)) do

            -- (

                -- For each object sizes specified

                for objSize in objSizes where (objSize != false) do

                (

                    -- If the current object matches the current specified size

                    if (matchPattern obj.name pattern:(objSize)) do appendIfUnique sceneGeometryArray obj

                )

            -- )

        )



        -- Filter out nodes with invalid metadata name

        sceneGeometryArray = fnReturnNodesWithValidMetaName sceneGeometryArray

        -- Get all of the mesh metadata names from the objects in the scene

        sceneGeometryStrippedNames = (for obj in sceneGeometryArray collect (fnStripToMetaDataOnly obj.name)[1])

        -- Generate comparable metadata array for the stripped object names (suffix removed, size and vertcount remains)

        sceneGeometryCompareNames = (fnStripMetaToPrefixOnly sceneGeometryStrippedNames)

        -- Find all identical geometric objects in the scene based on metadata name (that is visible)

        identicalObjsInScene = fnFindDuplicatesInArray sceneGeometryStrippedNames



        -- For each identical object

        for objIndex=1 to identicalObjsInScene.count do

        (

            -- Prevent 3dsMax from stalling and interrupt if escape is pressed

            windows.processPostedMessages()

            setWaitCursor()

            if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

            -- Update progressbar

            currentPercentage = (objIndex * 100.0 / identicalObjsInScene.count)

            UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

            UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

            displayTempPrompt (currentPercentage as string + "%") 10000



            -- Get the current object based on the current index

            currentObjNode = sceneGeometryArray[(identicalObjsInScene[objIndex] as array)[1]]

            currentObjName = sceneGeometryStrippedNames[(identicalObjsInScene[objIndex] as array)[1]]

            -- Get the current stripped object name (suffix removed, size and vertcount remains)

            currentStrippedObjName = (fnStripMetaToPrefixOnly #(currentObjName))[1]

            -- Find all instances of the current (stripped) object in the library compare array

            foundItemIndex = (for i=1 to sceneGeometryCompareNames.count where sceneGeometryCompareNames[i] == currentStrippedObjName collect i)

            -- Filter and return all objects that match the current object

            foundItemsVerified = (for x in foundItemIndex where (fnCheckIfMetaIsCorrect currentObjName sceneGeometryStrippedNames[x]) collect sceneGeometryArray[x])



            -- If there's more than the specified number of instances of the current object

            if (foundItemsVerified.count >= spn_minIdentical.value) do

            (

                -- Append one of the objects to the result array

                appendIfUnique uniqueIdenticalObjs foundItemsVerified[1]

                -- Print information

                print (foundItemsVerified.count as string + " identical instances: " + currentObjName)

            )

        )

        -- Select the resulting objects

        clearSelection()

        select uniqueIdenticalObjs

        -- Update progress textfield and print information

        UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

        print ("Found " + uniqueIdenticalObjs.count as string + " unique objects with duplicates (from " + sceneGeometryArray.count as string + " objects)")

        print ("These nodes results in a total of  " + uniqueIdenticalObjsCount as string + " objects)")

        -- Update/Redraw Viewports and set arrowCurso

        setArrowCursor()

        fnForceViewportRedraw()

    )



    -- Create the button command

    on btn_selIdentical pressed do

    (

        -- Set the command panel to create and set to waitCursor

        setCommandPanelTaskMode #create

        setWaitCursor()



        -- Create a variable for objects that exists in the library

        identicalObjsFromSelection = #()



        -- Get the selected objects

        selObj = selection as array

        -- Get the mesh metadata names from the selection

        selObjStrippedNames = (for obj in selObj collect (fnStripToMetaDataOnly obj.name)[1])



        -- Get all visible geometric objects in the scene

        sceneGeometryArray = (for obj in geometry where (obj.isHidden == false) collect obj)

        -- Filter out nodes with invalid metadata name

        sceneGeometryArray = fnReturnNodesWithValidMetaName sceneGeometryArray

        -- Get all of the mesh metadata names from the objects in the scene

        sceneGeometryStrippedNames = (for obj in sceneGeometryArray collect (fnStripToMetaDataOnly obj.name)[1])

        -- Generate comparable metadata array for the stripped object names (suffix removed, size and vertcount remains)

        sceneGeometryCompareNames = (fnStripMetaToPrefixOnly sceneGeometryStrippedNames)



        -- For each object

        for objIndex=1 to selObj.count do

        (

            -- Prevent 3dsMax from stalling and interrupt if escape is pressed

            windows.processPostedMessages()

            setWaitCursor()

            if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

            -- Update progressbar

            currentPercentage = (objIndex * 100.0 / selObj.count)

            UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

            UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

            displayTempPrompt (currentPercentage as string + "%") 10000



            -- Get the current object based on the current index

            currentObjNode = selObj[objIndex]

            currentObjName = selObjStrippedNames[objIndex]

            -- Get the current stripped object name (suffix removed, size and vertcount remains)

            currentStrippedObjName = (fnStripMetaToPrefixOnly #(currentObjName))[1]

            -- Find all instances of the current (stripped) object in the library compare array

            foundItemIndex = (for i=1 to sceneGeometryCompareNames.count where sceneGeometryCompareNames[i] == currentStrippedObjName collect i)

            -- Filter and return all objects that match the current object

            foundItemsVerified = (for x in foundItemIndex where (fnCheckIfMetaIsCorrect currentObjName sceneGeometryStrippedNames[x]) collect sceneGeometryArray[x])



            -- Collect all of the objects with the current stripped name

            for obj in foundItemsVerified do appendIfUnique identicalObjsFromSelection obj

        )

        -- Select the result

        clearSelection()

        select identicalObjsFromSelection

        -- Update progress textfield, print information and set arrowCursor

        UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

        print ("Found " + selection.count as string + " identical objects to selection")

        -- Update/Redraw Viewports and set arrowCurso

        setArrowCursor()

        fnForceViewportRedraw()

    )



    -- Create the button command

    on btn_selOrigoPivot pressed do

    (

        -- Create a variable to store the resulting objects in

        resultingObjs = #()

        -- For each visible geometric object in the scene

        for obj in geometry where (obj.isHidden == false) do

        (

            -- If the current pivot is in origo (0,0,0)

            if ((fnRoundPositionDecimals obj.pivot 3) as string == "[0,0,0]") do

            (

                -- Append the current object to the result

                appendIfUnique resultingObjs obj

            )

        )

        -- Select the result

        select resultingObjs

    )

)



-- ROLLOUT: SAVE SELECTED OBJECT TO LIBRARY

rollout UI_SaveToTempLibrary "Save to Temporary Library" rolledUp:true

(

    -- UI

    button btn_saveSelToLibrary "Save Selected Objects" width:280 height:20 toolTip:"Saves objects to: .../1.PartsUntouched/"

    button btn_saveAllToLibrary "Save all Visible Objects (one of each unique node)" width:280 height:20 toolTip:"Saves objects to: .../1.PartsUntouched/"



    -- Create the button command

    on btn_saveSelToLibrary pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Specify the library path

            folderPathToLibrary = (driveLetter + "Misc/ModelLibraryNew/1.PartsUntouched/")

            makeDir folderPathToLibrary



            -- Get all unique objects from selection

            objNameArray = #()

            nodesToSaveArray = (for obj in (selection as array) where (obj.isHidden == false) collect obj)

            nodesToSaveArray = (for obj in nodesToSaveArray where ((appendIfUnique objNameArray (fnStripToMetaDataOnly obj.name)[1]) == true) collect obj)



            -- Prompt to confirm action

            confirmAction = querybox("Do NOT use this in the Main Mesh Library!\n\nMake sure these are original RAW objects! If you export reduced objects from here they will be useless, reduced objects should be exported from the next dialog!\n\nThis would save " + nodesToSaveArray.count as string + " objects to the library:\n" + folderPathToLibrary + "\n\nAre you sure you want to save these objects?\n(Existing files in the folder will be overwritten!)")

            -- If confirmed

            if (confirmAction==true) do

            (

                -- Print information

                print ("Starting: Working with " + nodesToSaveArray.count as string + " unique objects")

                -- Set the command panel to create and set to waitCursor

                setCommandPanelTaskMode #create

                setWaitCursor()

                -- For each selected object

                for objIndex=1 to nodesToSaveArray.count do

                (

                    -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                    windows.processPostedMessages()

                    setWaitCursor()

                    if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                    -- Update progressbar

                    currentPercentage = (objIndex * 100.0 / nodesToSaveArray.count)

                    UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

                    displayTempPrompt (currentPercentage as string + "%") 10000

                    -- If the current object has not been renamed to mesh metadata

                    if (fnFindInString nodesToSaveArray[objIndex].name "]_[" true == false) then

                    (

                        -- Print information

                        messageBox("WARNING: " + nodesToSaveArray[objIndex].name + " has not been renamed to mesh metadata, all nodes must be renamed to meshdata!")

                    )

                    -- Else if the current object seems to be renamed to mesh metadata

                    else

                    (

                        -- Save a raw and reduced version of the current object

                        tmpMaterial = nodesToSaveArray[objIndex].material

                        tmpParent = nodesToSaveArray[objIndex].parent

                        tmpChildren = for objChild in nodesToSaveArray[objIndex].children collect objChild

                        nodesToSaveArray[objIndex].material = undefined

                        nodesToSaveArray[objIndex].parent = undefined

                        tmpChildren.parent = undefined

                        saveNodes nodesToSaveArray[objIndex] (folderPathToLibrary + nodesToSaveArray[objIndex].name + "_RAW.max") quiet:false

                        saveNodes nodesToSaveArray[objIndex] (folderPathToLibrary + nodesToSaveArray[objIndex].name + "_REDUCED.max") quiet:false

                        nodesToSaveArray[objIndex].material = tmpMaterial

                        nodesToSaveArray[objIndex].parent = tmpParent

                        tmpChildren.parent = nodesToSaveArray[objIndex]

                    )

                )

            )

            -- Update progress textfield, print information and set arrowCursor

            UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

            print ("Finished: Objects are exported to: " + folderPathToLibrary)

            -- Update/Redraw Viewports and set arrowCurso

            setArrowCursor()

            fnForceViewportRedraw()

        )

    )



    -- Create the button command

    on btn_saveAllToLibrary pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Specify the library path

            folderPathToLibrary = (driveLetter + "Misc/ModelLibraryNew/1.PartsUntouched/")

            makeDir folderPathToLibrary



            -- Get all unique objects

            objNameArray = #()

            nodesToSaveArray = (for obj in geometry where (obj.isHidden == false) collect obj)

            nodesToSaveArray = (for obj in nodesToSaveArray where ((appendIfUnique objNameArray (fnStripToMetaDataOnly obj.name)[1]) == true) collect obj)



            -- Prompt to confirm action

            confirmAction = querybox("Do NOT use this in the Main Mesh Library!\n\nMake sure these are original RAW objects! If you export reduced objects from here they will be useless, reduced objects should be exported from the next dialog!\n\nThis would save " + nodesToSaveArray.count as string + " objects to the library:\n" + folderPathToLibrary + "\n\nAre you sure you want to save these objects?\n(Existing files in the folder will be overwritten!)")

            -- If confirmed

            if (confirmAction==true) do

            (

                -- Print information

                print ("Starting: Working with " + nodesToSaveArray.count as string + " unique objects")

                -- Set the command panel to create and set to waitCursor

                setCommandPanelTaskMode #create

                setWaitCursor()

                -- For each unique object

                for objIndex=1 to nodesToSaveArray.count do

                (

                    -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                    windows.processPostedMessages()

                    setWaitCursor()

                    if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                    -- Update progressbar

                    currentPercentage = (objIndex * 100.0 / nodesToSaveArray.count)

                    UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

                    displayTempPrompt (currentPercentage as string + "%") 10000



                    -- If the current object has not been renamed to mesh metadata

                    if (fnFindInString nodesToSaveArray[objIndex].name "]_[" true == false) then

                    (

                        -- Print information

                        messageBox("WARNING: " + nodesToSaveArray[objIndex].name + " has not been renamed to mesh metadata, all nodes must be renamed to meshdata!")

                        exit

                    )

                    -- Else if the current object seems to be renamed to mesh metadata

                    else

                    (

                        -- Save a raw and reduced version of the current object

                        tmpMaterial = nodesToSaveArray[objIndex].material

                        tmpParent = nodesToSaveArray[objIndex].parent

                        tmpChildren = for objChild in nodesToSaveArray[objIndex].children collect objChild

                        nodesToSaveArray[objIndex].material = undefined

                        nodesToSaveArray[objIndex].parent = undefined

                        tmpChildren.parent = undefined

                        saveNodes nodesToSaveArray[objIndex] (folderPathToLibrary + nodesToSaveArray[objIndex].name + "_RAW.max") quiet:false

                        saveNodes nodesToSaveArray[objIndex] (folderPathToLibrary + nodesToSaveArray[objIndex].name + "_REDUCED.max") quiet:false

                        nodesToSaveArray[objIndex].material = tmpMaterial

                        nodesToSaveArray[objIndex].parent = tmpParent

                        tmpChildren.parent = nodesToSaveArray[objIndex]

                    )

                )

            )

            -- Update progress textfield, print information and set arrowCursor

            UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

            print ("Finished: Objects are exported to: " + folderPathToLibrary)

            -- Update/Redraw Viewports and set arrowCurso

            setArrowCursor()

            fnForceViewportRedraw()

        )

    )

)



-- ROLLOUT: IMPORT UNREDUCED AND EXPORT THEM WHEN REDUCED

rollout UI_PerformActualReducing "Reduce and Export Files" rolledUp:true

(

    -- UI

    checkbox chk_zeroTransforms "Zero Transforms and Place Imported Objects in Grid" checked:false toolTip:"Checked:\nPosition, rotation and scale will be set to [0,0,0] and the objects will be placed in a grid.\n\nUnchecked:\nTransforms will be left untouched, objects will be imported as they are."

    button btn_importForReducing "Import Files for Reducing" width:280 height:20 toolTip:"Imports objects from: .../1.PartsUntouched/"

    checkbox chk_hideAfterReplaced "Hide Obj after Replaced" checked:true

    button btn_replaceTmpWithOrg "Replace Reduced with Original (from selection)" width:280 height:20 toolTip:"Two objects must be selected"

    label lbl_reducedByPrc

    button btn_exportToNextStep "Export Objects to the Next Step" width:280 height:20 toolTip:"Saves objects to: .../2.ReducedObjects/"



    -- Create the button command

    on btn_importForReducing pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Specify the library path

            folderPathToLibrary = (driveLetter + "Misc/ModelLibraryNew/1.PartsUntouched/")

            makeDir folderPathToLibrary



            -- Collect all of the files in the library with the suffix "_REDUCED"

            libraryFiles = getFiles (folderPathToLibrary + ("*_REDUCED.max"))



            -- Prompt to confirm action

            confirmAction = querybox("This will reset the current scene and import " + libraryFiles.count as string + " objects for reducing from the folder:" + folderPathToLibrary + ".\n\nRemember to save this scene in different revisions in case max craches or there turns out the be something wrong with the script.\n\nAre you sure you want to continue?")

            -- If confirmed

            if (confirmAction==true) do

            (

                -- Set the command panel to create and set to waitCursor

                setCommandPanelTaskMode #create

                setWaitCursor()

                -- Set up a new scene and specify correct units (metric)

                resetMaxFile #noprompt

                Units.Systemtype = #meters

                Units.Displaytype = #Metric

                Units.Metrictype = #millimeters

                -- Create the layers for the imported objects

                importedPartsLayerOrg = (LayerManager.newLayerFromName "ORIGINAL")

                importedPartsLayerOrg = (LayerManager.getLayerFromName "ORIGINAL")

                importedPartsLayerTmp = (LayerManager.newLayerFromName "WIP_TO_REDUCE")

                importedPartsLayerTmp = (LayerManager.getLayerFromName "WIP_TO_REDUCE")

                importedPartsLayerOrg.isHidden = 1

                importedPartsLayerTmp.isHidden = 0



                -- For each file to reduce

                for fileIndex=1 to libraryFiles.count do

                (

                    -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                    windows.processPostedMessages()

                    setWaitCursor()

                    if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                    -- Update progressbar

                    currentPercentage = (fileIndex * 100.0 / libraryFiles.count)

                    UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

                    displayTempPrompt (currentPercentage as string + "%") 10000

                    -- Import the current object from the library

                    mergeMAXFile libraryFiles[fileIndex] #useSceneMtlDups

                    importedReducedObjOrg = getLastMergedNodes()

                    importedPartsLayerOrg.addNode importedReducedObjOrg[1]

                    importedReducedObjOrg[1].name = (importedReducedObjOrg[1].name)

                    importedReducedObjOrg[1].wireColor = (color 7 174 90)

                    -- If transforms should be set to zero

                    if (chk_zeroTransforms.state==true) do

                    (

                        -- Set transformations to zero

                        importedReducedObjOrg[1].scale = [1,1,1]

                        importedReducedObjOrg[1].rotation = (quat 0 0 0 1)

                        importedReducedObjOrg[1].position = [0,0,0]

                    )

                )



                -- Get all geometric objects in the scene

                sceneGeometryArray = (for obj in geometry collect obj)

                -- Create variables for positioning of the objects

                countRowX = 0

                countRowY = 0

                -- For each imported object

                for objIndex=1 to sceneGeometryArray.count do

                (

                    -- If objects should be placed in a grid, position the current object

                    if (chk_zeroTransforms.state==true) do ( sceneGeometryArray[objIndex].position = [countRowX, countRowY, 0] )

                    -- Snapshot the current object and prepare for reducing

                    currentObjDuplicate = snapshot sceneGeometryArray[objIndex]

                    convertToPoly currentObjDuplicate

                    currentObjDuplicate.name = (sceneGeometryArray[objIndex].name + "_TempToReduce")

                    importedPartsLayerTmp.addNode currentObjDuplicate

                    currentObjDuplicate.wireColor = (color 135 6 6)



                    -- Increment the positions

                    if (countRowY == (sqrt sceneGeometryArray.count)) then ( countRowX+=1; countRowY=0 )

                    else (countRowY+=1)

                )



                -- Remove empty layers

                fnRemoveEmptyLayers()

                -- Update viewport

                fileIn((getDir #userMacros) + @"\Jorn Tools\ViewportSettings.ms")



                -- Update progress textfield, print information and set arrowCursor

                UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

                print ("Finished: " + libraryFiles.count as string + " objects was imported")

                print ("The temporary objects (red) should be reduced, then replaced with the original (green) objects")

                max zoomext sel

                -- Update/Redraw Viewports and set arrowCursor

                setArrowCursor()

                fnForceViewportRedraw()

            )

        )

    )

    -- Create the button command

    on btn_replaceTmpWithOrg pressed do

    (

        -- Create undo queue

        undo "ReplaceObject" on

        (

            -- Get the current selection

            selObj = selection as array

            -- If exactly two objects are selected

            if (selObj.count == 2) then

            (

                -- If the second selected object should be merged to the first

                if (selObj[1].layer.name == "ORIGINAL") and (selObj[2].layer.name != "ORIGINAL") then

                (

                    -- Get current vert count

                    vertsObj1 = selObj[1].verts.count

                    vertsObj2 = selObj[2].verts.count

                    vertsDiff = #(vertsObj1, vertsObj2, (vertsObj1 - vertsObj2), (100.0 - (vertsObj2*100.0/vertsObj1)))

                    vertsDiff = for currentItem in vertsDiff collect (currentItem as string)

                    -- Replace the highres with the lowres while keeping the original transform

                    polyop.deleteFaces selObj[1] selObj[1].faces

                    polyop.attach selObj[1] selObj[2]

                    if (chk_hideAfterReplaced.state==true) do (selObj[2].isHidden = true)

                    print ("Old obj verts: " + vertsDiff[1] + " - New obj verts: " + vertsDiff[2] + " :: Removed " + vertsDiff[3] + " verts (" + vertsDiff[4] + "%)")

                    displayTempPrompt ("Reduced by: " + vertsDiff[4] + "%") 10000

                    UI_PerformActualReducing.lbl_reducedByPrc.text = ("Reduced by: " + vertsDiff[4] + "%")

                )

                -- Else if the first selected object should be merged to the second

                else if (selObj[2].layer.name == "ORIGINAL") and (selObj[1].layer.name != "ORIGINAL") then

                (

                    -- Get current vert count

                    vertsObj1 = selObj[1].verts.count

                    vertsObj2 = selObj[2].verts.count

                    vertsDiff = #(vertsObj1, vertsObj2, (vertsObj2 - vertsObj1), (100.0 - (vertsObj1*100.0/vertsObj2)))

                    vertsDiff = for currentItem in vertsDiff collect (currentItem as string)

                    -- Replace the highres with the lowres while keeping the original transform

                    polyop.deleteFaces selObj[2] selObj[2].faces

                    polyop.attach selObj[2] selObj[1]

                    if (chk_hideAfterReplaced.state==true) do (selObj[2].isHidden = true)

                    print ("Old obj verts: " + vertsDiff[2] + " - New obj verts: " + vertsDiff[1] + " :: Removed " + vertsDiff[3] + " verts (" + vertsDiff[4] + "%)")

                    displayTempPrompt ("Reduced by: " + vertsDiff[4] + "%") 10000

                    UI_PerformActualReducing.lbl_reducedByPrc.text = ("Reduced by: " + vertsDiff[4] + "%")

                )

                else print("You need to select two objects, and the destination object has to be in a layer named 'ORIGINAL' while the other cannot!")

            )

            else print("You need to select two objects, and the destination object has to be in a layer named 'ORIGINAL' while the other cannot!")

            -- Update/Redraw Viewports and set arrowCursor

            setArrowCursor()

            fnForceViewportRedraw()

        )

    )

    -- Create the button command

    on btn_exportToNextStep pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Specify the library paths to copy from->to

            folderPathToOldLibrary = (driveLetter + "Misc/ModelLibraryNew/1.PartsUntouched/")

            folderPathToNewLibrary = (driveLetter + "Misc/ModelLibraryNew/2.ReducedObjects/")

            makeDir folderPathToOldLibrary

            makeDir folderPathToNewLibrary



            -- Collect all nodes belonging to the layer named "ORIGINAL"

            importedPartsLayerOrg = (LayerManager.getLayerFromName "ORIGINAL")

            reducedObjsToSave = for obj in objects where (obj.layer.name == "ORIGINAL") collect obj

            reducedObjsToSave = (for obj in reducedObjsToSave where (obj.isHidden == false) collect obj)



            -- If any nodes was found

            if (reducedObjsToSave.count > 0) then

            (

                -- Prompt to confirm action

                confirmAction = querybox("Make sure all " + reducedObjsToSave.count as string + " visible objects in the layer 'ORIGINAL' is reduced, and that they to NOT have a suffix (like _REDUCED)! You should also have a version (or more) saved with the current objects in case something goes wrong! The objects will be exported to the folder:\n" + folderPathToNewLibrary + "\n\nAre you sure you want to continue?\n(Existing files in the folder will be overwritten!)")

                -- If confirmed

                if (confirmAction==true) do

                (

                    -- Print information

                    print ("Starting: Working with " + reducedObjsToSave.count as string + " unique objects")

                    -- Set the command panel to create and set to waitCursor

                    setCommandPanelTaskMode #create

                    setWaitCursor()



                    -- PART 1/2

                    -- For each object to export

                    for objIndex=1 to reducedObjsToSave.count do

                    (

                        -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                        windows.processPostedMessages()

                        setWaitCursor()

                        if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                        -- Update progressbar

                        currentPercentage = (objIndex * 100.0 / reducedObjsToSave.count)

                        UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                        UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

                        displayTempPrompt (currentPercentage as string + "%") 10000



                        -- Save the current reduced object to the folder: 2.ReducedObjects

                        tmpParent = reducedObjsToSave[objIndex].parent

                        tmpChildren = for objChild in reducedObjsToSave[objIndex].children collect objChild

                        reducedObjsToSave[objIndex].parent = undefined

                        tmpChildren.parent = undefined

                        saveNodes reducedObjsToSave[objIndex] (folderPathToNewLibrary + reducedObjsToSave[objIndex].name + "_REDUCED.max") quiet:false

                        reducedObjsToSave[objIndex].parent = tmpParent

                        tmpChildren.parent = reducedObjsToSave[objIndex]

                    )

                    -- Update progress textfield and print information

                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Part 1/2 Finished")

                    print ("Part 1/2 Finished: Will now copy the RAW files")



                    -- PART 2/2

                    -- Collect all of the RAW files from the folder: 1.PartsUntouched

                    libraryRawFiles = getFiles (folderPathToOldLibrary + ("*_RAW.max"))

                    libraryRawFileNames = (for currFile in libraryRawFiles collect (getFileNameFile currFile))

                    libraryRawFileNamesStripped = (for currFile in libraryRawFileNames collect (subString currFile 1 (currFile.count-4)))

                    -- Collect all of the REDUCED files from the folder: 2.ReducedObjects

                    libraryReducedFiles = getFiles (folderPathToNewLibrary + ("*_REDUCED.max"))

                    libraryReducedFileNames = (for currFile in libraryReducedFiles collect (getFileNameFile currFile))

                    libraryReducedFileNamesStripped = (for currFile in libraryReducedFileNames collect (subString currFile 1 (currFile.count-8)))

                    -- For each file in the reduced library

                    for fileIndex=1 to libraryReducedFiles.count do

                    (

                        -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                        windows.processPostedMessages()

                        setWaitCursor()

                        if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                        -- Update progressbar

                        currentPercentage = (fileIndex * 100.0 / reducedObjsToSave.count)

                        UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                        UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%")

                        -- Copy the raw version of the current reduced file to the folder: 2.ReducedObjects

                        itemFoundIndex = findItem libraryRawFileNamesStripped libraryReducedFileNamesStripped[fileIndex]

                        if (itemFoundIndex != 0) do ( copyFile libraryRawFiles[itemFoundIndex] (folderPathToNewLibrary + libraryRawFileNames[itemFoundIndex] + ".max") )

                    )

                    -- Update progress textfield, print information and set arrowCursor

                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Part 2/2 Finished")

                    print ("Part 2/2 Finished")

                    -- Update/Redraw Viewports and set arrowCursor

                    setArrowCursor()

                    fnForceViewportRedraw()

                )

            )

            -- Else if no nodes was found, print information

            else ( print("No reduced objects in the layer 'ORIGINAL' was found") )

        )

    )

)



-- ROLLOUT: RENAME ALL OBJECTS TO NAMES BASED ON THEIR UNIQUE MESH & TRANSFORM DATA

rollout UI_CleanupReducedFiles "Cleanup Reduced Files" rolledUp:true

(

    -- UI

    checkbox chk_doNotTouchNormals "Do not Touch Normals when Cleaning up" checked:true

    button btn_cleanupReducedFiles "Cleanup Reduced Files" width:280 height:20 toolTip:"Imports objects from: .../2.ReducedObjects/\nSaves objects to: .../3.ReadyForLibraryImport/"

    -- Create the button command

    on btn_cleanupReducedFiles pressed do

    (

        -- Disable undo for the loop

        with undo off

        (

            -- Specify the library path for the source and destination files

            folderPathToSourceLibrary = (driveLetter + "Misc/ModelLibraryNew/2.ReducedObjects/")

            folderPathToDestLibrary = (driveLetter + "Misc/ModelLibraryNew/3.ReadyForLibraryImport/")

            makeDir folderPathToSourceLibrary

            makeDir folderPathToDestLibrary

            -- Collect all of the files in the source library

            librarySourceFiles = getFiles (folderPathToSourceLibrary + ("*.max"))

            -- Generate savepaths for all of the sourcefiles

            libraryDestFiles = for currFile in librarySourceFiles collect (fnReplaceInString currFile "2.ReducedObjects" "3.ReadyForLibraryImport" true)



            -- If any files was found in the folder: 2.ReducedObjects

            if (librarySourceFiles.count > 0) then

            (

                -- Prompt to confirm action

                confirmAction = querybox(librarySourceFiles.count as string + " files will be cleaned. The objects will be exported to the folder:\n" + folderPathToDestLibrary + "\n\nIt will overwrite existing files in the exportfolder: 3.ReadyForLibraryImport!\n\nDo you want to continue?")

                -- If confirmed

                if (confirmAction==true) do

                (

                    -- Retrieve the prompt line panel (so that I have complete control over the messages)

                    promptLinePanel = fnGetPromptLinePanel()

                    -- Set the command panel to create and set to waitCursor

                    setCommandPanelTaskMode #create

                    setWaitCursor()

                    -- For each file

                    for fileIndex=1 to librarySourceFiles.count do

                    (

                        -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                        windows.processPostedMessages()

                        setWaitCursor()

                        if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                        -- Update progressbar

                        currentPercentage = (fileIndex * 100.0 / librarySourceFiles.count)

                        UI_LibraryProgressBar.prg_totalLibraryProgress.value = currentPercentage

                        currentFileIndexStr = ("(" + fileIndex as string + " of " + librarySourceFiles.count as string + ")")

                        UI_LibraryProgressBar.lbl_totalLibraryProgress.text = (currentPercentage as string + "%" + " " + currentFileIndexStr)

                        -- Update the progress in the prompt line

                        windows.sendMessage promptLinePanel 11 1 0

                        displayTempPrompt ("Progress: " + currentPercentage as string + "%" + " " + currentFileIndexStr) 10000

                        windows.sendMessage promptLinePanel 11 0 0



                        -- Load the current file and specify correct units (metric)

                        resetMaxFile #noprompt

                        Units.Systemtype = #meters

                        Units.Displaytype = #Metric

                        Units.Metrictype = #millimeters

                        isFileLoaded = mergeMAXFile librarySourceFiles[fileIndex]

                        -- If the current file was loaded

                        if (isFileLoaded != false) then

                        (

                            -- Clear cache

                            gc()

                            freeSceneBitmaps()

                            clearUndoBuffer()

                            -- Set display options

                            viewport.ResetAllViews()

                            completeRedraw()

                            viewport.setLayout #layout_4

                            viewport.activeViewport = 4

                            max tool maximize

                            viewport.setTransparencyLevel 3

                            viewport.SetAdaptiveDegNeverDegradeSelected true

                            viewport.SetAdaptiveDegNeverRedrawAfterDegrade false

                            viewport.setAdaptiveDegDisplayModeFastShaded true

                            viewport.SetAdaptiveDegDisplayModeHide false

                            viewSetting = NitrousGraphicsManager.GetActiveViewportSetting()

                            viewSetting.ViewportViewSettingImpl.ShadowsEnabled = off

                            viewSetting.SelectedEdgedFacesEnabled = on

                            viewSetting.ShowEdgedFacesEnabled = off

                            viewSetting.ShowSelectionBracketsEnabled = off

                            viewSetting.ViewportClippingEnabled = true

                            viewSetting.ViewportViewSettingImpl.AmbientOcclusionEnabled = off

                            viewSetting.AdaptiveDegradeNeverDegradeGeometry = true

                            viewSetting.VisualStyleMode = #shaded

                            displaySafeFrames = off

                            NitrousGraphicsManager.AntialiasingQuality = #8X

                            SceneExplorerManager.ClearAllExplorers()



                            -- If it exists geometry in the current file

                            if ((geometry as array).count == 1) do

                            (

                                -- Exit isolation mode

                                IsolateSelection.ExitIsolateSelectionMode()

                                -- Set visual object properties

                                geometry[1].wirecolor = (color 166 7 55)

                                geometry[1].displayByLayer = false

                                -- Set transformations to zero

                                geometry[1].scale = [1,1,1]

                                geometry[1].rotation = (quat 0 0 0 1)

                                geometry[1].position = [0,0,0]

                                -- Move geometry to default layer and remove any extra layers

                                (LayerManager.getLayer 0).addNode geometry[1]

                                (LayerManager.getLayer 0).current = true

                                (LayerManager.getLayer 0).isHidden = false

                                fnRemoveEmptyLayers()

                                -- Set camera view

                                max zoomext sel

                                -- If this is a reduced library file

                                if ((subString librarySourceFiles[fileIndex] (librarySourceFiles[fileIndex].count-11) librarySourceFiles[fileIndex].count) == "_REDUCED.max") do

                                (

                                    -- If normals is allowed to be changed

                                    if (chk_doNotTouchNormals.state == false) then

                                    (

                                        -- Clear normals

                                        polyop.setFaceSelection geometry[1] geometry[1].faces

                                        geometry[1].baseObject.EditablePoly.setSmoothingGroups 0 -1 1

                                        geometry[1].autoSmoothThreshold = 35

                                        geometry[1].baseObject.EditablePoly.autosmooth()

                                    )



                                    -- Fix transforms

                                    resetScale geometry[1]

                                    pivotTransformInObjectTransformSpace = (geometry[1].transform * (inverse geometry[1].objectTransform))

                                    vertexOffsetInObjectTransformSpace = (inverse pivotTransformInObjectTransformSpace)

                                    for vertIndex = 1 to (polyop.getNumVerts geometry[1].baseobject) do

                                    (

                                        vertexPos = polyop.getVert geometry[1].baseObject vertIndex

                                        vertexPos = vertexPos * vertexOffsetInObjectTransformSpace

                                        polyop.setVert geometry[1].baseObject vertIndex vertexPos

                                    )

                                    geometry[1].objectOffsetPos = [0,0,0]

                                    geometry[1].objectOffsetRot = (quat 0 0 0 1)

                                    geometry[1].objectOffsetScale = [1, 1, 1]



                                    -- Set wirecolor

                                    objects.wirecolor = (color 7 174 90)

                                    -- If no material is assigned to the duplicate, apply a UV-Map to it

                                    if (geometry[1].material == undefined) do

                                    (

                                        uvwModifier = Uvwmap()

                                        addModifier geometry[1] (uvwModifier)

                                        uvwModifier.maptype = 4

                                        uvwModifier.length = 0.6 * 1.0

                                        uvwModifier.width = 0.6 * 1.0

                                        uvwModifier.height = 0.6 * 1.0

                                        convertToPoly geometry[1]

                                    )

                                )

                                -- Save the current file to the new path

                                saveMaxFile libraryDestFiles[fileIndex] useNewFile:false

                                print ((currentPercentage as string) + "% : Saved: " + libraryDestFiles[fileIndex])

                            )

                        )

                    )



                    -- Unfreeze the prompt panel

                    windows.sendMessage promptLinePanel 11 1 0

                    -- Update progress textfield, print information and set arrowCursor

                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text = ("Finished")

                    print ("Finished: Objects are saved in: " + folderPathToDestLibrary)

                    messageBox("Objects are saved in: " + folderPathToDestLibrary + "\nNext step is to import them to the source file, then export verified from there!") title:"Finished" beep:false

                    setArrowCursor()

                )

            )

            -- Else if no files was found in the folder: 2.ReducedObjects

            else ( print("No files was found in the folder: " + folderPathToSourceLibrary) )

        )

    )

)



-- ROLLOUT: RENAME ALL OBJECTS TO NAMES BASED ON THEIR UNIQUE MESH & TRANSFORM DATA

rollout UI_LibraryFunctions "ONLY USE IN LIBRARY SCENE!" rolledUp:true

(

    -- UI Group

    group "Progress"

    (

        progressbar prg_LibraryFunctionsProgress color:green

        label lbl_LibraryFunctionsProgress

    )

    -- UI Group

    group "Import New Objects"

    (

        button btn_importNewObjects "Import new Raw/Reduced Pairs" width:260 height:20 toolTip:"Imports objects from: .../3.ReadyForLibraryImport/\n\nEvery unique part in the library should be a pair of two objects: (RAW and REDUCED). Their mesh metaname is based on the RAW geometry.\n\nEvery operation is based on the RAW version of the object, and it's imperative that the transforms hasn't been tampered with!"

        button btn_cleanDirectories "Clean up Directories" width:260 height:20 toolTip:"This function will go through every .max file in the folders: 1.PartsUntouched & 2.ReducedObjects & 3.ReadyForLibraryImport and remove them, you should be absolutely sure that you've imported all you need into the source file, and have backup of the files just in case."

    )

    -- UI Group

    group "Library Workflow and Validation Tools"

    (

        button btn_findPairMismatch "Find Pair Mismatch (missing sibling)" width:260 height:20 toolTip:"We should have RAW and REDUCED object of every part, this function finds objects that is missing it's sibling based on their name."

        button btn_findUnreduced "Find Objects that has not been Reduced" width:260 height:20 toolTip:"For each pair of RAW and REDUCED, the reduced part should have the same metaname as the raw, but should have different geometry. This function finds reduced objects that is identical to their raw pair based on their name."

        button btn_findDuplicateObjs "Find Objects with Duplicate Meta" width:260 height:20 toolTip:"This function will go through every object and find duplicate names (included hidden objects)"

        button btn_findMissingName "Find Objects without Objectname" width:260 height:20 toolTip:"This function will go through every object and find objects with 'obj' as their name"

        button btn_updateMetadata "Update Metadata on all Visible Nodes" width:260 height:20 toolTip:"This function will go through every visible RAW node and regenerate mesh metadata on RAW and REDUCED version (the pair of each object)."

        checkbox chk_skipProbablyRenamed "Skip nodes which seem to already be renamed" checked:true

    )

    -- UI Group

    group "Place Objects"

    (

        radiobuttons rb_gridSorting "Sort By:" labels:#("Metadata", "Raw VertCount", "Reduced VertCount") default:1 align:#left offset:[0,0]

        checkBox cb_sortByLayers " Sort by Layers" checked:true align:#left offset:[1,0] toolTip:"The sorted result will be sorted a second time, this time by layers."

        button btn_placeInGridPairs "Place Visible Pairs in a Grid" width:260 height:20 toolTip:"All visible pairs will be aligned identically!\n\nRAW objects will be placed in a grid, then the REDUCED objects will align to their RAW-sibbling."

        button btn_placeInGridNormal "Place all Visible Objects in a Grid" width:260 height:20 toolTip:"All visible objects will be placed in a grid format independently!"

        spinner spn_gridSpacing "Grid Object Spacing: " range:[0.1,10000000,1.5] type:#float width:180 align:#left

    )

    -- UI Group

    group "Export Objects from Main Scene"

    (

        checkbox chk_replaceExisting "Replace Existing Files" checked:false

        button btn_exportValidatedReduced "Export Validated Visible Reduced Objects" width:260 height:20 toolTip:"Will be exported to: .../4.Verified/"

        button btn_exportValidatedRaw "Export Visible Raw Objects" width:260 height:20 toolTip:"Will be exported to: .../4.Verified/"

    )



    -- Create the button command

    on btn_importNewObjects pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Specify the library path

            folderPathToLibrary = (driveLetter + "Misc/ModelLibraryNew/3.ReadyForLibraryImport/")

            makeDir folderPathToLibrary



            -- Collect all of the RAW and REDUCED files from the folder: 3.ReadyForLibraryImport

            libraryRawFiles = getFiles (folderPathToLibrary + ("*_RAW.max"))

            libraryReducedFiles = getFiles (folderPathToLibrary + ("*_REDUCED.max"))



            -- If any files was found in the folder: 3.ReadyForLibraryImport

            if ((libraryRawFiles.count + libraryReducedFiles.count) > 0) then

            (

                -- Prompt to confirm action

                confirmAction = querybox("This will import all " + (libraryRawFiles.count + libraryReducedFiles.count) as string + " objects from the folder: 3.ReadyForLibraryImport into your current Main Library Scene. You should be sure that the reduced versions in that folder is actually reduced!\n(Imported objects will be placed in temporary layers until you've manually verified them)\n\nEvery unique part in the library should be a pair of two objects: (RAW and REDUCED). Their mesh metaname is based on the RAW geometry.\n\nEvery operation is based on the RAW version of the object,and it's imperative that the transforms hasn't been tampered with!")

                -- If confirmed

                if (confirmAction==true) do

                (

                    -- Create a variable to store the imported objects in

                    importedNewObjectsRaw = #()

                    importedNewObjectsReduced = #()



                    -- Set the command panel to create and set to waitCursor

                    setCommandPanelTaskMode #create

                    setWaitCursor()

                    -- Close explorers and clear selection (for speed purposes)

                    SceneExplorerManager.ClearAllExplorers()

                    clearSelection()



                    -- Hide the original layers in the main scene: 'RAW' and 'REDUCED'

                    originalLayerRaw = (LayerManager.newLayerFromName "RAW")

                    originalLayerRaw = (LayerManager.getLayerFromName "RAW")

                    originalLayerReduced = (LayerManager.newLayerFromName "REDUCED")

                    originalLayerReduced = (LayerManager.getLayerFromName "REDUCED")

                    originalLayerRaw.isHidden = 1

                    originalLayerReduced.isHidden = 1

                    -- Create and hide the layers for the objects to import

                    importedPartsLayerRaw = (LayerManager.newLayerFromName "NEW_RAW")

                    importedPartsLayerRaw = (LayerManager.getLayerFromName "NEW_RAW")

                    importedPartsLayerReduced = (LayerManager.newLayerFromName "NEW_REDUCED")

                    importedPartsLayerReduced = (LayerManager.getLayerFromName "NEW_REDUCED")

                    importedPartsLayerRaw.isHidden = 1

                    importedPartsLayerReduced.isHidden = 1

                    -- Create a layer for empty objects (meshes with no faces)

                    importedPartsEmpty = (LayerManager.newLayerFromName "EMPTY")

                    importedPartsEmpty = (LayerManager.getLayerFromName "EMPTY")

                    -- Create a layer for shaded objects (meshes with a material assigned)

                    importedPartsMaterial = (LayerManager.newLayerFromName "ASSIGNED_MATERIAL")

                    importedPartsMaterial = (LayerManager.getLayerFromName "ASSIGNED_MATERIAL")

                    -- For each RAW file found in the library folder

                    for fileIndex=1 to libraryRawFiles.count do

                    (

                        -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                        windows.processPostedMessages()

                        setWaitCursor()

                        if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                        -- Update progressbar

                        currentPercentage = (fileIndex * 100.0 / libraryRawFiles.count)

                        UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = currentPercentage

                        UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = (currentPercentage as string + "%")



                        -- Import the current raw object from the library

                        mergeMAXFile libraryRawFiles[fileIndex] #skipDups

                        importedObjRaw = getLastMergedNodes()

                        orgRawObjName = importedObjRaw[1].name

                        importedObjRaw[1].name = (orgRawObjName + "_RAW")

                        importedObjRaw[1].scale = [1,1,1]

                        importedObjRaw[1].rotation = (quat 0 0 0 1)

                        importedObjRaw[1].position = [0,0,0]



                        -- Import the current reduced object from the library

                        mergeMAXFile (fnReplaceInString libraryRawFiles[fileIndex] "_RAW" "_REDUCED" true) #skipDups

                        importedObjReduced = getLastMergedNodes()

                        orgReducedObjName = importedObjReduced[1].name

                        importedObjReduced[1].name = (orgRawObjName + "_REDUCED")

                        importedObjRaw[1].scale = [1,1,1]

                        importedObjRaw[1].rotation = (quat 0 0 0 1)

                        importedObjRaw[1].position = [0,0,0]



                        -- If the current raw and reduced object was imported

                        if (importedObjRaw.count != 0) and (importedObjReduced.count != 0) then

                        (

                            -- Validate/correct the came of the current raw object

                            importedObjRaw[1].name = (fnReplaceInString importedObjRaw[1].name "_REDUCED" "_RAW" true)

                            importedObjRaw[1].name = (fnReplaceInString importedObjRaw[1].name "_RAW_RAW" "_RAW" true)

                            importedObjRaw[1].name = (fnReplaceInString importedObjRaw[1].name "CORRUPT_CORRUPT_" "CORRUPT_" true)

                            -- Validate/correct the came of the current reduced object

                            importedObjReduced[1].name = (fnReplaceInString importedObjRaw[1].name "_RAW" "_REDUCED" true)

                            importedObjReduced[1].name = (fnReplaceInString importedObjReduced[1].name "_REDUCED_REDUCED" "_REDUCED" true)

                            importedObjReduced[1].name = (fnReplaceInString importedObjReduced[1].name "CORRUPT_CORRUPT_" "CORRUPT_" true)

                        )



                        -- If not both of the objects was imported

                        if (importedObjRaw.count == 0) or (importedObjReduced.count == 0) then

                        (

                            -- Print information

                            print ("Could not find a reduced file of " + importedObjRaw[1].name)

                            -- Delete the imported files

                            delete importedObjRaw

                            delete importedObjReduced

                        )

                        -- Else if both objects was imported

                        else

                        (

                            -- Add the imported objects to their correct layer

                            importedPartsLayerRaw.addNode importedObjRaw[1]

                            importedPartsLayerReduced.addNode importedObjReduced[1]



                            -- If the name has been changed on the current raw object

                            if ((orgRawObjName + "_RAW") != importedObjRaw[1].name) then

                            (

                                -- Print information

                                format ("RAW OldName: " + orgRawObjName + "\n")

                                format ("RAW NewName: " + importedObjRaw[1].name + "\n\n")

                            )

                            -- If the name has been changed on the current reduced object

                            if ((orgReducedObjName + "_REDUCED") != importedObjReduced[1].name) then

                            (

                                -- Print information

                                format ("REDUSED OldName: " + orgReducedObjName + "\n")

                                format ("REDUSED NewName: " + importedObjReduced[1].name + "\n\n")

                            )



                            -- If the current object has no faces

                            if (importedObjReduced[1].numFaces == 0) do

                            (

                                -- Add the objects to the empty layer

                                importedPartsEmpty.addNode importedObjRaw[1]

                                importedPartsEmpty.addNode importedObjReduced[1]

                            )



                            -- If the current object has a material assigned to it

                            if (importedObjReduced[1].material != undefined) do

                            (

                                -- Add the objects to the assigned material layer

                                importedPartsMaterial.addNode importedObjRaw[1]

                                importedPartsMaterial.addNode importedObjReduced[1]

                            )



                            -- Append the imported objects to the result

                            append importedNewObjectsRaw importedObjRaw[1]

                            append importedNewObjectsReduced importedObjReduced[1]

                        )

                    )

                    -- Set the visibility of the layers back to true

                    originalLayerRaw.isHidden = 0

                    originalLayerReduced.isHidden = 0

                    importedPartsLayerRaw.isHidden = 0

                    importedPartsLayerReduced.isHidden = 0

                    -- Remove empty layers

                    fnRemoveEmptyLayers()

                    -- Place all visible objects (the imported objects hopefully) in a grid

                    fnPlaceLibraryObjsInGrid importedNewObjectsRaw 1.5 true

                    -- Select the resulting nodes

                    clearSelection()

                    select (join importedNewObjectsRaw importedNewObjectsReduced)

                    -- Update progressbar, textfield, print information and set arrowCursor

                    CompleteRedraw()

                    UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = 100

                    UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = ("Finished")

                    print ("Finished: " + libraryRawFiles.count as string + " objects of each RAW and REDUCED was imported")

                    print("Next step is to verify everything, running cleanup (deletes old files), and then export/update the new objects to the verified folder!")

                    messageBox("If everything is good, you should also run 'Clean up Directories'. This deletes the source files you've used (so that you don't forget that these are already processed). If you don't do this, you risk making a mess in the source folders (and importing the same objects multiple times)!") title:"Finished" beep:false

                    setArrowCursor()

                )

            )

            -- Else if no files was found in the folder: 3.ReadyForLibraryImport

            else ( print("No files (in raw/reduced pair) was found in the folder: " + folderPathToLibrary) )

        )

    )



    -- Create the button command

    on btn_cleanDirectories pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Delete all max-files in folders following folders: 1.PartsUntouched & 2.ReducedObjects & 3.ReadyForLibraryImport

            deleteFilesPath1 = (driveLetter + "Misc/ModelLibraryNew/1.PartsUntouched/")

            deleteFilesPath2 = (driveLetter + "Misc/ModelLibraryNew/2.ReducedObjects/")

            deleteFilesPath3 = (driveLetter + "Misc/ModelLibraryNew/3.ReadyForLibraryImport/")

            makeDir deleteFilesPath1

            makeDir deleteFilesPath2

            makeDir deleteFilesPath3

            -- Collect the files to delete

            path1Files = (for currFile in (getFiles (deleteFilesPath1 + ("*.max"))) collect currFile)

            path2Files = (for currFile in (getFiles (deleteFilesPath2 + ("*.max"))) collect currFile)

            path3Files = (for currFile in (getFiles (deleteFilesPath3 + ("*.max"))) collect currFile)



            -- Prompt to confirm action

            confirmAction = querybox("This function will go through and remove every .max file in the following folders:\n1.PartsUntouched (" + path1Files.count as string + " files)\n2.ReducedObjects (" + path2Files.count as string + " files)\n3.ReadyForLibraryImport (" + path3Files.count as string + " files)\n\nYou should be absolutely sure that you've imported all you need into the source file, and have backup of the files just in case!\nContinue?")

            -- If confirmed

            if (confirmAction==true) do

            (

                -- Delete all max-files in specified folders

                for currFile in path1Files do (deleteFile currFile)

                for currFile in path2Files do (deleteFile currFile)

                for currFile in path3Files do (deleteFile currFile)

                -- Print information

                print ("Deleted a total of " + (path1Files.count + path2Files.count + path3Files.count) as string + " .max files")

                displayTempPrompt ("Deleted a total of " + (path1Files.count + path2Files.count + path3Files.count) as string + " .max files") 10000

            )

        )

    )





    -- Create the button command

    on btn_findPairMismatch pressed do

    (

        -- Set the command panel to create and set to waitCursor

        setCommandPanelTaskMode #create

        setWaitCursor()

        -- Close explorers and clear selection (for speed purposes)

        SceneExplorerManager.ClearAllExplorers()

        clearSelection()



        -- Create a variable to store the result in

        mismatchObjs = #()



        -- Find all raw objects in the scene

        rawObjNames = (for tmpObj in geometry where (matchPattern tmpObj.name pattern:("*_RAW") ignoreCase:true) collect tmpObj.name)

        rawObjNames = sort(rawObjNames)

        rawObjPairName = for tmpObj in rawObjNames collect ((subString tmpObj 1 (tmpObj.count-4)) + "_REDUCED")

        -- Find all reduced objects in the scene

        reducedObjNames = (for tmpObj in geometry where (matchPattern tmpObj.name pattern:("*_REDUCED") ignoreCase:true) collect tmpObj.name)

        reducedObjNames = sort(reducedObjNames)

        reducedObjPairName = for tmpObj in reducedObjNames collect ((subString tmpObj 1 (tmpObj.count-8)) + "_RAW")



        -- Find all raw objects that is missing a reduced pair

        rawMissingPair = (for tmpIndex=1 to rawObjNames.count where (findItem reducedObjNames rawObjPairName[tmpIndex] == 0) collect (getNodeByName rawObjNames[tmpIndex]))

        -- Find all reduced objects that is missing a raw pair

        reducedMissingPair = (for tmpIndex=1 to reducedObjNames.count where (findItem rawObjNames reducedObjPairName[tmpIndex] == 0) collect (getNodeByName reducedObjNames[tmpIndex]))



        -- For each missing pair of a raw object

        for obj in rawMissingPair do

        (

            -- Print information and append to result variable

            print ("Raw-object missing a Reduced version: " + obj.name)

            appendIfUnique mismatchObjs obj

        )

        -- For each missing pair of a reduced object

        for obj in reducedMissingPair do

        (

            -- Print information and append to result variable

            print ("Reduced-object missing a Raw version: " + obj.name)

            appendIfUnique mismatchObjs obj

        )



        -- Update progressbar, textfield and set arrowCursor

        UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = 100

        UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = ("Finished")

        setArrowCursor()

        -- Select the result and print information

        clearSelection()

        select mismatchObjs

        print ("Finished: Found " + selection.count as string + " nodes with a missing sibling")

    )



    -- Create the button command

    on btn_findUnreduced pressed do

    (

        -- Set the command panel to create and set to waitCursor

        setCommandPanelTaskMode #create

        setWaitCursor()

        -- Close explorers and clear selection (for speed purposes)

        SceneExplorerManager.ClearAllExplorers()

        clearSelection()



        -- Create a variable to store the result in

        unreducedObjs = #()



        -- Find all raw objects and their reduced sibling

        rawObjNames = (for tmpObj in geometry where (matchPattern tmpObj.name pattern:("*_RAW") ignoreCase:true) collect tmpObj.name)

        rawObjNames = sort(rawObjNames)

        rawObjPairName = for tmpObj in rawObjNames collect ((subString tmpObj 1 (tmpObj.count-4)) + "_REDUCED")

        -- For each raw object collected

        for objIndex=1 to rawObjNames.count do

        (

            -- Prevent 3dsMax from stalling and interrupt if escape is pressed

            windows.processPostedMessages()

            setWaitCursor()

            if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

            -- Update progressbar

            currentPercentage = (objIndex * 100.0 / rawObjNames.count)

            UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = currentPercentage

            UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = (currentPercentage as string + "%" + " (" + objIndex as string + " of " + rawObjNames.count as string + ")")



            -- Get the node of the current raw and it's sibling

            currentRawNode = getNodeByName rawObjNames[objIndex]

            currentReducedNode = getNodeByName rawObjPairName[objIndex]

            -- If the current raw has an existing reduced sibling

            if (currentReducedNode != undefined) then

            (

                -- Get basic mesh information about both objects

                rawVertCount = currentRawNode.verts.count

                rawEdgeCount = currentRawNode.edges.count

                rawFaceCount = currentRawNode.faces.count

                reducedVertCount = currentReducedNode.verts.count

                reducedEdgeCount = currentReducedNode.edges.count

                reducedFaceCount = currentReducedNode.faces.count

                -- If the meshes are identical

                if (rawVertCount==reducedVertCount and rawEdgeCount==reducedEdgeCount and rawFaceCount==reducedFaceCount) then

                (

                    -- Print information and append to result variable

                    print ("Not reduced: " + currentReducedNode.name)

                    appendIfUnique unreducedObjs currentRawNode

                    appendIfUnique unreducedObjs currentReducedNode

                )

            )

            -- Else if the current raw is missing it's sibling, print information

            else ( print ("Missing sibling: " + currentRawNode.name + " does not have a reduced version!") )

        )

        -- Update progressbar, textfield and set arrowCursor

        UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = 100

        UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = ("Finished")

        setArrowCursor()

        -- Select the result and print information

        clearSelection()

        select unreducedObjs

        print ("Finished: Found " + (selection.count / 2) as string + " unreduced nodes (that should have been reduced)")

    )



    -- Create the button command

    on btn_findDuplicateObjs pressed do

    (

        -- Clear the selection

        clearSelection()

        -- Create a variable to store the duplicate objects in

        objDuplicates = #()

        objDuplicateNames = #()



        -- Get all visible geometric objects in the scene

        sceneGeometryArray = (for obj in geometry where (obj.isHidden == false) collect obj)

        -- Filter out nodes with invalid metadata name

        sceneGeometryArray = fnReturnNodesWithValidMetaName sceneGeometryArray

        -- Get all of the mesh metadata names from the objects in the scene

        sceneGeometryStrippedNames = (for obj in sceneGeometryArray collect (fnStripToMetaDataOnly obj.name)[1])

        -- Generate comparable metadata array for the stripped object names (suffix removed, size and vertcount remains)

        sceneGeometryCompareNames = (fnStripMetaToPrefixOnly sceneGeometryStrippedNames)



        -- Find all identical geometric objects in the scene based on their comparable metadata name

        identicalObjsInScene = fnFindDuplicatesInArray sceneGeometryCompareNames



        -- For each object in identicalObjsInScene

        for itemIndex=1 to identicalObjsInScene.count do

        (

            -- Retrieve the objects within the current item

            currentObjsIndexes = (identicalObjsInScene[itemIndex] as array)

            currentObjs = (for tmpIndex in currentObjsIndexes collect sceneGeometryArray[tmpIndex])

            -- If the current item contains more than two items

            if (currentObjsIndexes.count > 2) do

            (

                -- Filter and return all objects that match the current object

                currentDuplicateObjs = (for obj in currentObjs where (fnCheckIfMetaIsCorrect currentObjs[1] obj.name) collect obj)

                -- For each extra duplicate found in the current item

                for dupIndex=3 to currentDuplicateObjs.count do

                (

                    -- Append the string to search for to find the duplicates

                    appendIfUnique objDuplicateNames sceneGeometryArray[(identicalObjsInScene[itemIndex] as array)[dupIndex]].name

                    -- Append the duplicate object to the result

                    appendIfUnique objDuplicates sceneGeometryArray[(identicalObjsInScene[itemIndex] as array)[dupIndex]]

                )

            )

        )



        -- Select the result

        select objDuplicates

        -- Print information

        for metaName in objDuplicateNames do (format (metaName + "\n"))

        -- Update the progressbar and textfield

        UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = 100

        UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = ("100%")

        -- Print information

        print ("Finished: Found " + objDuplicates.count as string + " nodes with non-unique mesh metadata in their names")

        -- Force a redraw to update the viewport

        completeRedraw()

    )



    -- Create the button command

    on btn_findMissingName pressed do

    (

        -- Clear the selection

        clearSelection()

        -- Get visible objects

        objectArray = (for obj in objects where (obj.isHidden == false) collect obj)

        -- Find abd select all objects with "&obj&" in their name

        select (for obj in objectArray where (matchPattern obj.name pattern:"*&obj&*" ignoreCase:true) collect obj)



        -- Update the progressbar and textfield

        UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = 100

        UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = ("100%")

        -- Print information

        print ("Finished: Found " + selection.count as string + " visible nodes 'obj' in their name. This doesn't really matter, but shows that somewhere it's original name has been lost (which just means you fucked up somewhere).")

        -- Force a redraw to update the viewport

        completeRedraw()

    )



    -- Create the button command

    on btn_updateMetadata pressed do

    (

        -- Prompt to confirm action

        confirmAction = querybox("This will take some time if you have many objects! Are you sure you want to continue?")

        -- If confirmed

        if (confirmAction==true) do

        (

            -- Get user input

            forceRename = if (chk_skipProbablyRenamed.state == true) then false else true



            -- Set the command panel to create and set to waitCursor

            setCommandPanelTaskMode #create

            setWaitCursor()

            -- Close explorers and clear selection (for speed purposes)

            SceneExplorerManager.ClearAllExplorers()

            clearSelection()



            -- Create a variable to store the result in

            alreadyRenamedObjs = #()

            renamedNodesResult = #()



            -- Find all visible raw objects and their sibling

            rawObjNames = (for tmpObj in geometry where (tmpObj.isHidden == false) collect tmpObj.name)

            rawObjNames = (for tmpObj in rawObjNames where (matchPattern tmpObj pattern:("*_RAW") ignoreCase:true) collect tmpObj)

            rawObjNames = sort(rawObjNames)

            rawObjPairName = for tmpObj in rawObjNames collect ((subString tmpObj 1 (tmpObj.count-4)) + "_REDUCED")

            -- For each raw object collected

            for objIndex=1 to rawObjNames.count do

            (

                -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                windows.processPostedMessages()

                setWaitCursor()

                if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                -- Update progressbar

                currentPercentage = (objIndex * 100.0 / rawObjNames.count)

                UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = currentPercentage

                UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = (currentPercentage as string + "%" + " (" + objIndex as string + " of " + rawObjNames.count as string + ")")



                -- Get the node of the current raw and it's sibling

                currentRawNode = getNodeByName rawObjNames[objIndex]

                currentReducedNode = getNodeByName rawObjPairName[objIndex]

                -- If the current raw has an existing reduced sibling

                if (currentReducedNode != undefined) then

                (

                    -- Set the reduced pair to the same position as the raw object (to later successfully update the pivot on both)

                    currentReducedNode.position = currentRawNode.position

                    -- Retrieve the current raw object's unique mesh identifiers

                    objMeshData = fnRetrieveMeshInfo currentRawNode forceRename

                    -- Set the pivot of the reduced object to the raw object's pivot

                    currentReducedNode.pivot = currentRawNode.pivot

                    -- If it managed to retrieve mesh metadata

                    if (objMeshData != undefined) then

                    (

                        -- Strip to metadata (for comparison)

                        orgObjMeshMetaRaw = (fnStripToMetaDataOnly currentRawNode.name)

                        newObjMeshMetaRaw = (fnStripToMetaDataOnly objMeshData)

                        -- If the mesh metadata hasn't been changed (no need to rename)

                        if (orgObjMeshMetaRaw[1] == newObjMeshMetaRaw[1]) and (orgObjMeshMetaRaw[2] == newObjMeshMetaRaw[2]) then

                        (

                            -- Append the objects to the list of skipped objects

                            append alreadyRenamedObjs currentRawNode

                        )

                        -- Else if this node should be renamed

                        else

                        (

                            -- Get the name of both siblings of the current pair

                            newNameRaw = objMeshData

                            newNameReduced = ((subString objMeshData 1 (objMeshData.count-4)) + "_REDUCED")

                            -- Print information

                            format ("RAW Old: " + currentRawNode.name + "\nRAW New: " + newNameRaw + "\n")

                            format ("REDUSED Old: " + currentReducedNode.name + "\nREDUSED New: " + newNameReduced + "\n\n")

                            -- Rename the current pair and append it to the result

                            currentRawNode.name = newNameRaw

                            currentReducedNode.name = newNameReduced

                            append renamedNodesResult currentRawNode

                            append renamedNodesResult currentReducedNode

                        )

                    )

                )

                -- Else if the current raw is missing it's sibling, print information

                else ( print ("Missing sibling: " + currentRawNode.name + " does not have a reduced version!") )

            )

            -- Update progressbar, textfield and set arrowCursor

            UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = 100

            UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = ("Finished")

            setArrowCursor()

            -- Select the result and print information

            clearSelection()

            select renamedNodesResult

            print ("Finished: Updated Mesh Metadata on " + selection.count as string + " nodes")

        )

    )



    -- Create the button command

    on btn_placeInGridPairs pressed do

    (

        -- Get the initial selection and set to waitCursor

        initialSelection = selection as array

        setWaitCursor()

        -- Disable scene redraw, change panel, clear selection (for speed purposes)

        disableSceneRedraw()

        setCommandPanelTaskMode #create

        clearSelection()

        -- Create a variable to store the sorted objects in

        sortedObjsToPlace = #()

        -- If it should sort by name

        if (rb_gridSorting.state == 1) then

        (

            -- Get all visible geometric RAW objects in the scene ordered by name

            objsToPlace = (for tmpObjName in geometry where (tmpObjName.isHidden == false) collect tmpObjName.name)

            objsToPlace = (for tmpObjName in objsToPlace where (matchPattern tmpObjName pattern:("*_RAW") ignoreCase:true) collect tmpObjName)

            objsToPlace = sort(objsToPlace)

            objsToPlace = (for tmpObj in objsToPlace where (getNodeByName tmpObj != undefined) collect (getNodeByName tmpObj))

            -- Update the variable with the sorted objects

            sortedObjsToPlace = objsToPlace

        )

        -- Else if it should sort by vert count of raw objects

        else if (rb_gridSorting.state == 2) then

        (

            -- Get all visible geometric RAW objects in the scene ordered by vertcount

            objsToPlace = (for tmpObjName in geometry where (tmpObjName.isHidden == false) collect tmpObjName.name)

            objsToPlace = (for tmpObjName in objsToPlace where (matchPattern tmpObjName pattern:("*_RAW") ignoreCase:true) collect tmpObjName)

            objsToPlace = (for tmpObj in objsToPlace where (getNodeByName tmpObj != undefined) collect (getNodeByName tmpObj))

            objsToPlace = (for tmpObj in objsToPlace collect (#((filterString tmpObj.name "_")[3] as integer, tmpObj)))

            --Sort result by vert count

            function sortByCol1 a b = if a[1] < b[1] then -1 else if a[1] > b[1] then 1 else 0

            qsort objsToPlace sortByCol1

            objsToPlace = for tmpObj in objsToPlace collect tmpObj[2]

            -- Update the variable with the sorted objects

            sortedObjsToPlace = objsToPlace

        )

        -- Else if it should sort by vert count of reduced objects

        else if (rb_gridSorting.state == 3) then

        (

            -- Create a variable to store the RAW objects with the vertcount of the REDUCED objects

            objsToPlaceSplittedCustom = #()

            -- Get all visible geometric RAW objects in the scene ordered by vertcount

            objsToPlace = (for tmpObjName in geometry where (tmpObjName.isHidden == false) collect tmpObjName.name)

            objsToPlace = (for tmpObjName in objsToPlace where (matchPattern tmpObjName pattern:("*_RAW") ignoreCase:true) collect tmpObjName)

            objsToPlace = (for tmpObj in objsToPlace where (getNodeByName tmpObj != undefined) collect (getNodeByName tmpObj))

            objsToPlaceSplitted = (for tmpObj in objsToPlace collect (#((filterString tmpObj.name "_")[3] as integer, tmpObj)))

            -- For each splitted RAW object

            for objIndex=1 to objsToPlaceSplitted.count do

            (

                -- Set an initial value for the vert count, this way objects without a reduced pair will seem to have 0 in vert count

                objsToPlaceSplitted[objIndex][1] = 0

                -- Look for a reduced version of the current raw object

                currObjReduced = getNodeByName ((subString objsToPlace[objIndex].name 1 (objsToPlace[objIndex].name.count-4)) + "_REDUCED")

                -- If a reduced sibbling exists, add it's vert count to the sorting variable

                if (currObjReduced != undefined) then ( objsToPlaceSplitted[objIndex][1] = currObjReduced.numVerts )

                -- Else if it does not have a reduced sibbling, print information

                else ( print (objsToPlace[objIndex].name + " does not have a reduced sibbling!") )



            )



            --Sort result by vert count

            function sortByCol1 a b = if a[1] < b[1] then -1 else if a[1] > b[1] then 1 else 0

            qsort objsToPlaceSplitted sortByCol1

            objsToPlaceSplitted = for tmpObj in objsToPlaceSplitted collect tmpObj[2]

            -- Update the variable with the sorted objects

            sortedObjsToPlace = objsToPlaceSplitted

        )



        -- If the objects should also be sorted by layers

        if (cb_sortByLayers.state == true) do

        (

            -- Create a temporary variable to store the result in

            objsToPlace = #()

            -- Sort the objects into arrays separated by layers

            objArraysSorted = fnSeparateObjsByLayers sortedObjsToPlace

            -- For each separated array (each layer)

            for arrayIndex=1 to objArraysSorted.count do

            (

                -- Append each object within the current array to the result

                for obj in objArraysSorted[arrayIndex] do appendIfUnique objsToPlace obj

            )

            -- Update the variable with the sorted objects

            sortedObjsToPlace = objsToPlace

        )



        -- Place all visible objects in a grid

        numPlacedObjs = (fnPlaceLibraryObjsInGrid objsToPlace spn_gridSpacing.value true)



        -- Reselect initial selection

        clearSelection()

        select initialSelection

        -- Update/Redraw Viewports

        fnForceViewportRedraw()

        -- Print information

        print (numPlacedObjs as string + " objects was placed in a grid format")

    )



    -- Create the button command

    on btn_placeInGridNormal pressed do

    (

        -- Get the initial selection and set to waitCursor

        initialSelection = selection as array

        setWaitCursor()

        -- Disable scene redraw, change panel, clear selection (for speed purposes)

        disableSceneRedraw()

        setCommandPanelTaskMode #create

        clearSelection()

        -- Create a variable to store the sorted objects in

        sortedObjsToPlace = #()

        -- If it should sort by name

        if (rb_gridSorting.state == 1) then

        (

            -- Get all visible geometric objects in the scene ordered by name

            objsToPlace = (for tmpObjName in geometry where (tmpObjName.isHidden == false) collect tmpObjName.name)

            objsToPlace = sort(objsToPlace)

            objsToPlace = (for tmpObj in objsToPlace where (getNodeByName tmpObj != undefined) collect (getNodeByName tmpObj))

            -- Update the variable with the sorted objects

            sortedObjsToPlace = objsToPlace

        )

        -- Else if it should sort by vert count

        else if (rb_gridSorting.state == 2 or rb_gridSorting.state == 3) then

        (

            -- Get all visible geometric objects in the scene ordered by actual vertcount

            objsToPlace = (for tmpObj in geometry where (tmpObj.isHidden == false) collect #(tmpObj.numVerts, tmpObj))

            --Sort result by vert count

            function sortByCol1 a b = if a[1] < b[1] then -1 else if a[1] > b[1] then 1 else 0

            qsort objsToPlace sortByCol1

            objsToPlace = for tmpObj in objsToPlace collect tmpObj[2]

            -- Update the variable with the sorted objects

            sortedObjsToPlace = objsToPlace

        )



        -- If the objects should also be sorted by layers

        if (cb_sortByLayers.state == true) do

        (

            -- Create a temporary variable to store the result in

            objsToPlace = #()

            -- Sort the objects into arrays separated by layers

            objArraysSorted = fnSeparateObjsByLayers sortedObjsToPlace

            -- For each separated array (each layer)

            for arrayIndex=1 to objArraysSorted.count do

            (

                -- Append each object within the current array to the result

                for obj in objArraysSorted[arrayIndex] do appendIfUnique objsToPlace obj

            )

            -- Update the variable with the sorted objects

            sortedObjsToPlace = objsToPlace

        )



        -- Place all visible objects in a grid

        numPlacedObjs = (fnPlaceLibraryObjsInGrid objsToPlace spn_gridSpacing.value false)



        -- Reselect initial selection

        clearSelection()

        select initialSelection

        -- Update/Redraw Viewports

        fnForceViewportRedraw()

        -- Print information

        print (numPlacedObjs as string + " objects was placed in a grid format")

    )







    -- Create the button command

    on btn_exportValidatedReduced pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Specify the library path to save to

            folderPathToLibrary = (driveLetter + "Misc/ModelLibraryNew/4.Verified/")

            makeDir folderPathToLibrary



            -- Create a variable to store the result ut (the exported objects)

            actuallyExportedObjects = #()



            -- Get all visible geometric REDUCED objects in the scene ordered by name

            reducedObjNames = (for tmpObj in geometry where (tmpObj.isHidden == false) collect tmpObj.name)

            reducedObjNames = (for tmpObj in reducedObjNames where (matchPattern tmpObj pattern:("*_REDUCED") ignoreCase:true) collect tmpObj)

            reducedObjNames = sort(reducedObjNames)

            reducedObjsToSave = (for tmpObj in reducedObjNames where (getNodeByName tmpObj != undefined) collect (getNodeByName tmpObj))



            -- Collect all of the REDUCED files already existing in the folder: 3.ReadyForLibraryImport

            libraryReducedFiles = getFiles (folderPathToLibrary + ("*_REDUCED.max"))

            libraryReducedFileNames = (for currFile in libraryReducedFiles collect (getFileNameFile currFile))



            -- If any nodes was found

            if (reducedObjsToSave.count > 0) then

            (

                -- Prompt to confirm action

                confirmAction = querybox("This should only be done from the main library scene!!!\n\nThe " + reducedObjsToSave.count as string + " visible reduced objects will be exported to the folder:\n" + folderPathToLibrary + "\n\nAre you sure you want to continue?\n(Existing files in the folder will be overwritten if specified!)")

                -- If confirmed

                if (confirmAction==true) do

                (

                    -- Print information

                    print ("Starting: Working with " + reducedObjsToSave.count as string + " unique objects")

                    -- Set the command panel to create and set to waitCursor

                    setCommandPanelTaskMode #create

                    setWaitCursor()



                    -- For each object to export

                    for objIndex=1 to reducedObjsToSave.count do

                    (

                        -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                        windows.processPostedMessages()

                        setWaitCursor()

                        if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                        -- Update progressbar

                        currentPercentage = (objIndex * 100.0 / reducedObjsToSave.count)

                        UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = currentPercentage

                        UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = (currentPercentage as string + "%" + " (" + objIndex as string + " of " + reducedObjsToSave.count as string + ")")



                        -- If the current object already exists in the library

                        if ((findItem libraryReducedFileNames reducedObjsToSave[objIndex].name) != 0) then

                        (

                            -- If existing nodes should be replaced

                            if (chk_replaceExisting.state == true) then

                            (

                                -- Save and replace the reduced version of the current object to the folder: 4.Verified

                                saveNodes reducedObjsToSave[objIndex] (folderPathToLibrary + reducedObjsToSave[objIndex].name + ".max") quiet:false

                                -- Print information and append the current object to the result variable

                                print (reducedObjsToSave[objIndex].name + " was replaced")

                                appendIfUnique actuallyExportedObjects reducedObjsToSave[objIndex]

                            )

                        )

                        -- Else if the current object doesn't already exist

                        else

                        (

                            -- Save a the reduced version of the current object to the folder: 4.Verified

                            saveNodes reducedObjsToSave[objIndex] (folderPathToLibrary + reducedObjsToSave[objIndex].name + ".max") quiet:false

                            -- Append the current object to the result variable

                            appendIfUnique actuallyExportedObjects reducedObjsToSave[objIndex]

                        )

                    )

                    -- Update progress textfield and print information

                    UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = ("Finished")

                    print ("Finished: " + actuallyExportedObjects.count as string + " reduced objects has been saved to folder: " + folderPathToLibrary)

                    setArrowCursor()

                )

            )

            -- Else if no nodes was found, print information

            else ( print("No reduced objects was found in this scene") )

        )

    )



    -- Create the button command

    on btn_exportValidatedRaw pressed do

    (

        -- Disable undo

        with undo off

        (

            -- Specify the library path to save to

            folderPathToLibrary = (driveLetter + "Misc/ModelLibraryNew/4.Verified/")

            makeDir folderPathToLibrary



            -- Create a variable to store the result ut (the exported objects)

            actuallyExportedObjects = #()



            -- Get all visible geometric REDUCED objects in the scene ordered by name

            rawObjNames = (for tmpObj in geometry where (tmpObj.isHidden == false) collect tmpObj.name)

            rawObjNames = (for tmpObj in rawObjNames where (matchPattern tmpObj pattern:("*_RAW") ignoreCase:true) collect tmpObj)

            rawObjNames = sort(rawObjNames)

            rawObjsToSave = (for tmpObj in rawObjNames where (getNodeByName tmpObj != undefined) collect (getNodeByName tmpObj))



            -- Collect all of the RAW files already existing in the folder: 3.ReadyForLibraryImport

            libraryRawFiles = getFiles (folderPathToLibrary + ("*_RAW.max"))

            libraryRawFileNames = (for currFile in libraryRawFiles collect (getFileNameFile currFile))



            -- If any nodes was found

            if (rawObjsToSave.count > 0) then

            (

                -- Prompt to confirm action

                confirmAction = querybox("This should only be done from the main library scene!!!\n\nThe " + rawObjsToSave.count as string + " visible raw objects will be exported to the folder:\n" + folderPathToLibrary + "\n\nAre you sure you want to continue?\n(Existing files in the folder will be overwritten if specified!)")

                -- If confirmed

                if (confirmAction==true) do

                (

                    -- Print information

                    print ("Starting: Working with " + rawObjsToSave.count as string + " unique objects")

                    -- Set the command panel to create and set to waitCursor

                    setCommandPanelTaskMode #create

                    setWaitCursor()



                    -- For each object to export

                    for objIndex=1 to rawObjsToSave.count do

                    (

                        -- Prevent 3dsMax from stalling and interrupt if escape is pressed

                        windows.processPostedMessages()

                        setWaitCursor()

                        if keyboard.escPressed do ((print ("Interrupted: EscPressed")); enableSceneRedraw(); exit;)

                        -- Update progressbar

                        currentPercentage = (objIndex * 100.0 / rawObjsToSave.count)

                        UI_LibraryFunctions.prg_LibraryFunctionsProgress.value = currentPercentage

                        UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = (currentPercentage as string + "%" + " (" + objIndex as string + " of " + rawObjsToSave.count as string + ")")



                        -- If the current object already exists in the library

                        if ((findItem libraryRawFileNames rawObjsToSave[objIndex].name) != 0) then

                        (

                            -- If existing nodes should be replaced

                            if (chk_replaceExisting.state == true) then

                            (

                                -- Save and replace the raw version of the current object to the folder: 4.Verified

                                saveNodes rawObjsToSave[objIndex] (folderPathToLibrary + rawObjsToSave[objIndex].name + ".max") quiet:false

                                -- Print information and append the current object to the result variable

                                print (rawObjsToSave[objIndex].name + " was replaced")

                                appendIfUnique actuallyExportedObjects rawObjsToSave[objIndex]

                            )

                        )

                        -- Else if the current object doesn't already exist

                        else

                        (

                            -- Save a the raw version of the current object to the folder: 4.Verified

                            saveNodes rawObjsToSave[objIndex] (folderPathToLibrary + rawObjsToSave[objIndex].name + ".max") quiet:false

                            -- Append the current object to the result variable

                            appendIfUnique actuallyExportedObjects rawObjsToSave[objIndex]

                        )

                    )

                    -- Update progress textfield and print information

                    UI_LibraryFunctions.lbl_LibraryFunctionsProgress.text = ("Finished")

                    print ("Finished: " + actuallyExportedObjects.count as string + " raw objects has been saved to folder: " + folderPathToLibrary)

                    setArrowCursor()

                )

            )

            -- Else if no nodes was found, print information

            else ( print("No raw objects was found in this scene") )

        )

    )

)



-- Close the window if it exists

try(closeRolloutFloater meshLibraryFloater)catch()



-- Create the floater window

meshLibraryFloater = newRolloutFloater "Mesh Library Tool" 300 640

addRollout UI_LibraryProgressBar meshLibraryFloater

addRollout UI_RenameToMeshData meshLibraryFloater

addRollout UI_ImportAndReplaceFromLibrary meshLibraryFloater

addRollout UI_SelectIdentical meshLibraryFloater

addRollout UI_SaveToTempLibrary meshLibraryFloater

addRollout UI_PerformActualReducing meshLibraryFloater

addRollout UI_CleanupReducedFiles meshLibraryFloater

addRollout UI_LibraryFunctions meshLibraryFloater



```

## Message 8

**Trimming down the script**

I’m focusing on removing excessive comments and simplifying the code while preserving the original logic and consistent coding style. This involves stripping away redundant instructions and streamlining the script.

## Message 9

```maxscript
-- Function: Force viewport redraw
-- =======================================================
function fnForceViewportRedraw =
(
    enableSceneRedraw()
    completeRedraw()
    forceCompleteRedraw()
    updateToolbarButtons()
)

-- Function: Get prompt line panel
-- =======================================================
function fnGetPromptLinePanel =
(
    globalMsxInterface = (dotNetClass "Autodesk.Max.GlobalInterface").Instance
    statusBarContainer = windows.getChildHwnd globalMsxInterface.coreinterface14.StatusPanelHWND "StatusPanelBottomContainer"
    promptLinePanel = for currWin in windows.getChildrenHwnd statusBarContainer[1] where currWin[4] == "CustStatus" collect currWin[1]
    if promptLinePanel.count != 0 then promptLinePanel[1]
)

-- Function: Round position decimals
-- =======================================================
function fnRoundPositionDecimals inputPosition numDecimals =
(
    factor = (10.0 ^ numDecimals)
    [ (floor((inputPosition.x*factor)+0.5)/factor),
      (floor((inputPosition.y*factor)+0.5)/factor),
      (floor((inputPosition.z*factor)+0.5)/factor) ]
)

-- Function: Find duplicates in array
-- =======================================================
function fnFindDuplicatesInArray inputArray =
(
    arrayValues = #(); duplicateItems = #()
    for i=1 to inputArray.count do
    (
        found = findItem arrayValues inputArray[i]
        if found != 0 then append duplicateItems[found] i else
        (
            append arrayValues inputArray[i]
            duplicateItems[arrayValues.count] = #{i}
        )
    )
    duplicateItems
)

-- Function: Separate objects by layers
-- =======================================================
function fnSeparateObjsByLayers inputObjects =
(
    objArraySorted = #()
    layerNames = for o in inputObjects collect o.layer.name
    layerNames = fnFindDuplicatesInArray layerNames
    for i=1 to layerNames.count do
    (
        append objArraySorted #()
        for j in layerNames[i] do appendIfUnique objArraySorted[i] inputObjects[j]
    )
    objArraySorted
)

-- Function: Get mesh area
-- =======================================================
function fnGetMeshArea inputObject =
(
    objDup = snapshotAsMesh inputObject
    mArea = meshop.getFaceArea objDup objDup.faces
    delete objDup
    mArea
)

-- Function: Get mesh volume
-- =======================================================
function fnGetMeshVolume inputObject =
(
    meshVolume = 0.0
    objDup = snapshotAsMesh inputObject
    for f=1 to objDup.numFaces do
    (
        cf = getFace objDup f
        v1 = getVert objDup cf.x
        v2 = getVert objDup cf.y
        v3 = getVert objDup cf.z
        meshVolume += Dot (Cross (v2 - v1) (v3 - v1)) v1
    )
    delete objDup
    meshVolume *= 6
)

-- Function: Remove empty layers
-- =======================================================
function fnRemoveEmptyLayers =
(
    with undo off
    (
        disableSceneRedraw()
        setCommandPanelTaskMode #create
        SceneExplorerManager.ClearAllExplorers()
        clearSelection()
        for i=(layerManager.count-1) to 0 by -1 do
        (
            windows.processPostedMessages()
            if keyboard.escPressed do (enableSceneRedraw(); exit)
            l = LayerManager.getLayer i
            (LayerManager.getLayerFromName l.name).nodes &lrNodes
            if lrNodes.count == 0 do layerManager.deleteLayerByName l.name
        )
        fnForceViewportRedraw()
    )
)

-- Function: Find substring in string
-- =======================================================
function fnFindInString InString SearchString IgnoreCasing =
(
    matchPattern InString pattern:("*"+SearchString+"*") ignoreCase:IgnoreCasing
)

-- Function: Replace substring in string
-- =======================================================
function fnReplaceInString InString SearchString ReplaceString IgnoreCasing =
(
    if (InString.count>=1 and SearchString.count>=1) do
    (
        local sp = InString.count - SearchString.count + 1
        while sp >=1 do
        (
            sStr = if IgnoreCasing then (toLower(SearchString)) else SearchString
            sIn = if IgnoreCasing then (toLower(InString)) else InString
            if (substring sIn sp SearchString.count == sStr) then
            (
                leftStr = substring InString 1 (sp-1)
                rightStr = substring InString (sp+SearchString.count) InString.count
                InString = leftStr + ReplaceString + rightStr
                sp -= SearchString.count
            )else sp -= 1
        )
    )
    InString
)

-- Function: Strip to metadata only
-- =======================================================
function fnStripToMetaDataOnly inputString =
(
    idx1 = findString inputString "&"; idx1 = if idx1==undefined then inputString.count else idx1
    filteredName1 = substring inputString 1 (idx1-1)
    filteredName2 = (filterString inputString "&")[2]
    stringAfter = substring inputString (idx1+1) inputString.count
    idx2 = findString stringAfter "&"; idx2 = if idx2==undefined then inputString.count else idx2
    filteredName3 = substring stringAfter (idx2+1) stringAfter.count
    #(filteredName1, filteredName2, filteredName3)
)

-- Function: Strip meta to prefix
-- =======================================================
function fnStripMetaToPrefixOnly inputArray =
(
    compArr = #()
    for c in inputArray do
    (
        n = (isValidNode c) ? c.name : c
        nSplit = filterString n "]_[" splitEmptyTokens:false
        append compArr (nSplit[1] + "_" + nSplit[2] + "_" + nSplit[3] + "_")
    )
    compArr
)

-- Function: Return nodes with valid meta name
-- =======================================================
function fnReturnNodesWithValidMetaName inputArray =
(
    validMetaNodes = #()
    for obj in inputArray do
    (
        nSplit = filterString obj.name "]_[" splitEmptyTokens:false
        if (nSplit.count == 6 or nSplit.count == 7 or nSplit.count == 8) do
        (
            p1 = filterString nSplit[4] "," splitEmptyTokens:false
            p2 = filterString nSplit[5] "," splitEmptyTokens:false
            p3 = filterString nSplit[6] "," splitEmptyTokens:false
            if (p1.count + p2.count + p3.count == 9) do appendIfUnique validMetaNodes obj
        )
    )
    validMetaNodes
)

-- Function: Check if metadata is correct
-- =======================================================
function fnCheckIfMetaIsCorrect inputNode inputNodeName =
(
    nSplit = filterString inputNodeName "]_[" splitEmptyTokens:false
    if (nSplit.count == 6 or nSplit.count == 7 or nSplit.count == 8) then
    (
        if (isValidNode(inputNode)) do (if (nSplit[3] as integer != inputNode.verts.count) do return false)
        p1 = for x in (filterString nSplit[4] "," splitEmptyTokens:false) collect x as float
        p2 = for x in (filterString nSplit[5] "," splitEmptyTokens:false) collect x as float
        p3 = for x in (filterString nSplit[6] "," splitEmptyTokens:false) collect x as float
        if (p1.count+p2.count+p3.count == 9) then
        (
            allowedDeviation = 0.002
            if (nSplit[1] == "0") then allowedDeviation=0.0001
            if (nSplit[1] == "1") then allowedDeviation=0.0005
            srcPosRef = #()
            if isValidNode(inputNode) then
            (
                orgRot = inputNode.rotation; orgScale = inputNode.scale
                inputNode.scale=[1,1,1]; inputNode.rotation=(quat 0 0 0 1)
                pr1=(inputNode.center - inputNode.pivot)
                pr2=((polyop.getvert inputNode 1)-inputNode.pivot)
                pr3=((polyop.getvert inputNode inputNode.verts.count)-inputNode.pivot)
                inputNode.scale=orgScale; inputNode.rotation=orgRot
                srcPosRef=#(pr1,pr2,pr3)
            )
            else
            (
                inSplit = filterString inputNode "]_[" splitEmptyTokens:false
                p1s = for x in (filterString inSplit[4] "," splitEmptyTokens:false) collect x as float
                p2s = for x in (filterString inSplit[5] "," splitEmptyTokens:false) collect x as float
                p3s = for x in (filterString inSplit[6] "," splitEmptyTokens:false) collect x as float
                srcPosRef=#(p1s,p2s,p3s)
            )
            for i=1 to 3 do
                for j=1 to 3 do
                (
                    dvMin=srcPosRef[i][j]-allowedDeviation
                    dvMax=srcPosRef[i][j]+allowedDeviation
                    if (p1[i]==undefined and p2[i]==undefined and p3[i]==undefined) do return false
                    v = #(p1,p2,p3)[i][j]
                    if (v<dvMin or v>dvMax) do return false
                )
            true
        )
        else false
    )
    else false
)

-- Function: Retrieve pivot position
-- =======================================================
function fnRetrievePivotPosition inputNode =
(
    if (classOf inputNode.baseobject == Editable_Poly) do
    (
        fIdx = inputNode.faces.count
        v1=1; v2=((inputNode.verts.count/1.2)as integer); v3=((inputNode.verts.count/2)as integer)
        p1=(polyop.getFaceCenter inputNode fIdx)
        vPos1=(polyop.getVert inputNode v1)
        vPos2=(polyop.getVert inputNode v2)
        vPos3=(polyop.getVert inputNode v3)
        ((p1+vPos1+vPos2+vPos3)/4)
    )
)

-- Function: Retrieve mesh info
-- =======================================================
function fnRetrieveMeshInfo inputNode forceRetrieve =
(
    if (classOf inputNode.baseobject == Editable_Poly) then
    (
        isRenamed = fnCheckIfMetaIsCorrect inputNode inputNode.name
        if (forceRetrieve==false and isRenamed==true) then return inputNode.name
        else
        (
            if (inputNode.numFaces == 0) then
            (
                if (matchPattern inputNode.name pattern:("CORRUPT_*") == false) then ("CORRUPT_"+inputNode.name) else inputNode.name
            )
            else
            (
                n=inputNode.name
                objSuffixToAdd=""
                isRaw = matchPattern n pattern:"*_RAW" ignoreCase:false
                isRed = matchPattern n pattern:"*_REDUCED" ignoreCase:false
                if (isRaw or isRed) then
                (
                    objSuffixToAdd = if isRaw then "_RAW" else "_REDUCED"
                    if objSuffixToAdd=="_REDUCED" then (messageBox("WARNING: Reduced object detected. Should not rename!"); exit)
                )
                flt = filterString n "&"
                currName = if (flt.count>1) then flt[2] else flt[1]
                if (fnFindInString currName "]_[" true) then currName="obj"
                currName = fnReplaceInString currName "'" ":" true
                currName = fnReplaceInString currName "-" "_" true
                currName = fnReplaceInString currName "&" "" true
                if (currName=="") then currName="obj"

                orgCenter=inputNode.center; orgRot=inputNode.rotation; orgScale=inputNode.scale
                inputNode.scale=[1,1,1]; inputNode.rotation=(quat 0 0 0 1)
                inputNode.pivot=(fnRetrievePivotPosition inputNode)
                inputNode.center=orgCenter
                pr1=(inputNode.center - inputNode.pivot)
                pr2=((polyop.getvert inputNode 1)-inputNode.pivot)
                pr3=((polyop.getvert inputNode inputNode.verts.count)-inputNode.pivot)
                pr1=fnRoundPositionDecimals pr1 3
                pr2=fnRoundPositionDecimals pr2 3
                pr3=fnRoundPositionDecimals pr3 3
                inputNode.scale=orgScale; inputNode.rotation=orgRot; inputNode.center=orgCenter

                refArea=fnGetMeshArea inputNode
                refVol=fnGetMeshVolume inputNode
                refArea=(floor((refArea*1e6)+0.5)/1e6)
                refVol=(floor((refVol*1e6)+0.5)/1e6)

                sizePrefix=""
                if ((refArea>30)or(refVol>9)) then sizePrefix="7_veryLarge_"
                else if ((refArea>5 and refArea<30)or(refVol>2 and refVol<9)) then sizePrefix="6_large_"
                else if ((refArea>1 and refArea<5)or(refVol>0.2 and refVol<2)) then sizePrefix="5_medium_"
                else if ((refArea>0.1 and refArea<1)or(refVol>0.1 and refVol<0.2)) then sizePrefix="4_mediumSmall_"
                else if ((refArea>0.02 and refArea<0.1)or(refVol>0.01 and refVol<0.1)) then sizePrefix="3_small_"
                else if ((refArea>0.005 and refArea<0.02)or(refVol>0.0005 and refVol<0.01)) then sizePrefix="2_verySmall_"
                else if ((refArea>0.0005 and refArea<0.005)or(refVol>0.0001 and refVol<0.0005)) then sizePrefix="1_extremelySmall_"
                else if ((refArea<0.0005) or (refVol<0.0000001)) then sizePrefix="0_invisibleAlmost_"

                p1s=(pr1.x as string+","+pr1.y as string+","+pr1.z as string)
                p2s=(pr2.x as string+","+pr2.y as string+","+pr2.z as string)
                p3s=(pr3.x as string+","+pr3.y as string+","+pr3.z as string)

                meta = sizePrefix+(inputNode.verts.count as string)+"_[" + p1s + "]_[" + p2s + "]_[" + p3s + "]"
                filteredObjName = if (currName.count>50) then (substring currName 1 50) else currName
                filteredObjName = fnReplaceInString filteredObjName meta "" true
                filteredObjName = fnReplaceInString filteredObjName ":" "'" true
                filteredObjName = fnReplaceInString filteredObjName "_" "-" true
                filteredObjName = fnReplaceInString filteredObjName "&" "" true
                filteredObjName = fnReplaceInString filteredObjName "/" "" true
                filteredObjName = fnReplaceInString filteredObjName " " "" true
                filteredObjName = fnReplaceInString filteredObjName "," "" true
                filteredObjName = fnReplaceInString filteredObjName "(" "" true
                filteredObjName = fnReplaceInString filteredObjName ")" "" true
                filteredObjName = fnReplaceInString filteredObjName "\"" "" true

                meta + "&" + filteredObjName + "&" + objSuffixToAdd
            )
        )
    )
)

-- Function: Place library objects in grid
-- =======================================================
function fnPlaceLibraryObjsInGrid inputNodes distanceApart alignInPairs =
(
    undo "PlaceLibraryObjsInGrid" on
    (
        squareId=0; xC=0; yC=0; placedCount=0
        for i=1 to inputNodes.count do
        (
            inputNodes[i].center=[xC,yC,0]
            placedCount+=1
            if alignInPairs==true do
            (
                rNode=getNodeByName ((substring inputNodes[i].name 1 (inputNodes[i].name.count-4))+"_REDUCED")
                if rNode!=undefined then (rNode.position=inputNodes[i].position; placedCount+=1)
            )
            if (squareId==(sqrt inputNodes.count)) then (xC+=distanceApart; yC=0; squareId=0) else (yC+=distanceApart; squareId+=1)
        )
        placedCount
    )
)

-- Rollouts and UI
-- =======================================================
rollout UI_LibraryProgressBar "Progressbar" rolledUp:false
(
    progressbar prg_totalLibraryProgress color:green
    label lbl_totalLibraryProgress
)

rollout UI_RenameToMeshData "Rename Objects Based on Mesh" rolledUp:false
(
    checkbox chk_skipProbablyRenamed "Skip nodes which seem to already be renamed" checked:true
    button btn_renameToMeshData "Rename Visible Objects to Mesh Metadata" width:280 height:20
    button btn_revertToOriginal "Revert Naming on Visible Objects back to Original" width:280 height:20
    button btn_findNotRenamed "Find Nodes that has not been Renamed" width:280 height:20
    button btn_findCorrupt "Find Nodes that are Corrupt" width:280 height:20

    on btn_renameToMeshData pressed do
    (
        with undo off
        (
            initSel=selection as array
            setWaitCursor();disableSceneRedraw();setCommandPanelTaskMode #create;SceneExplorerManager.ClearAllExplorers();clearSelection()
            forceRename=(chk_skipProbablyRenamed.state==false)
            sceneGeom=(for o in geometry where not o.isHidden collect o)
            renamedRes=#();already=#()
            c=querybox("Continue?")
            if c==true do
            (
                for i=1 to sceneGeom.count do
                (
                    windows.processPostedMessages()
                    if keyboard.escPressed do (enableSceneRedraw();exit)
                    perc=(i*100.0/sceneGeom.count)
                    UI_LibraryProgressBar.prg_totalLibraryProgress.value=perc
                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(perc as string + "%")
                    displayTempPrompt (perc as string + "%") 10000
                    if (classOf sceneGeom[i].baseobject==Editable_Poly) then
                    (
                        mData=fnRetrieveMeshInfo sceneGeom[i] forceRename
                        if mData!=undefined then
                        (
                            orgM=fnStripToMetaDataOnly sceneGeom[i].name
                            newM=fnStripToMetaDataOnly mData
                            if (orgM[1]==newM[1] and orgM[2]==newM[2]) then append already sceneGeom[i] else
                            (
                                sceneGeom[i].name=mData
                                append renamedRes sceneGeom[i]
                            )
                        )
                    )
                )
                UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
                if renamedRes.count>0 then select renamedRes else select initSel
                if renamedRes.count>0 then print (renamedRes.count as string + " renamed")
                if already.count>0 then print (already.count as string + " already renamed")
            )
            setArrowCursor();fnForceViewportRedraw()
        )
    )

    on btn_revertToOriginal pressed do
    (
        with undo off
        (
            nodesAffected=#()
            sceneGeom=(for o in geometry where not o.isHidden collect o)
            c=querybox("Continue?")
            if c==true do
            (
                initSel=selection as array
                setWaitCursor();disableSceneRedraw();setCommandPanelTaskMode #create;SceneExplorerManager.ClearAllExplorers();clearSelection()
                for i=1 to sceneGeom.count do
                (
                    if (classOf sceneGeom[i].baseobject==Editable_Poly) then
                    (
                        windows.processPostedMessages()
                        if keyboard.escPressed do (enableSceneRedraw();exit)
                        perc=(i*100.0/sceneGeom.count)
                        UI_LibraryProgressBar.prg_totalLibraryProgress.value=perc
                        UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(perc as string + "%")
                        displayTempPrompt (perc as string + "%") 10000
                        orgN=sceneGeom[i].name
                        r=matchPattern orgN pattern:"*_RAW" ignoreCase:false
                        d=matchPattern orgN pattern:"*_REDUCED" ignoreCase:false
                        if (r or d) do
                        (
                            suff=if r then "_RAW" else "_REDUCED"
                            sceneGeom[i].name=fnReplaceInString sceneGeom[i].name suff "" true
                        )
                        flt=(filterString sceneGeom[i].name "&")
                        cName = if flt.count>1 then flt[2] else flt[1]
                        if fnFindInString cName "]_[" true then cName="obj"
                        cName=fnReplaceInString cName "'" ":" true
                        cName=fnReplaceInString cName "-" "_" true
                        cName=fnReplaceInString cName "&" "" true
                        if cName=="" then cName="obj"
                        if (cName!=orgN) do
                        (
                            sceneGeom[i].name=cName
                            appendIfUnique nodesAffected sceneGeom[i]
                        )
                    )
                )
                UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
                if nodesAffected.count>0 then select nodesAffected else select initSel
                print ("Finished: "+nodesAffected.count as string)
                setArrowCursor();fnForceViewportRedraw()
            )
        )
    )

    on btn_findNotRenamed pressed do
    (
        with undo off
        (
            disableSceneRedraw();setCommandPanelTaskMode #create;SceneExplorerManager.ClearAllExplorers();clearSelection()
            nodesNotRenamed=#()
            sceneGeom=(for o in geometry where not o.isHidden collect o)
            for i=1 to sceneGeom.count do
            (
                windows.processPostedMessages()
                if keyboard.escPressed do (enableSceneRedraw();exit)
                perc=(i*100.0/sceneGeom.count)
                UI_LibraryProgressBar.prg_totalLibraryProgress.value=perc
                UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(perc as string + "%")
                displayTempPrompt (perc as string + "%") 10000
                if (fnFindInString sceneGeom[i].name "]_[" true==false) then appendIfUnique nodesNotRenamed sceneGeom[i]
            )
            select nodesNotRenamed
            UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
            print ("Not renamed: "+nodesNotRenamed.count as string)
            setArrowCursor();fnForceViewportRedraw()
        )
    )

    on btn_findCorrupt pressed do
    (
        sceneGeom=(for o in geometry where not o.isHidden collect o)
        corrupt=(for o in sceneGeom where matchPattern o.name pattern:"CORRUPT_*" collect o)
        select corrupt
    )
)

rollout UI_ImportAndReplaceFromLibrary "Import and Replace Objects" rolledUp:false
(
    button btn_selectAvailableParts "Select Visible Available Parts" width:280 height:20
    button btn_importReplaceParts "Import and Replace Visible Replace Parts" width:280 height:20
    checkBox cb_colorChange "Import and Replace: Update wirecolors" checked:true width:280
    checkBox cb_layerChange "Import and Replace: Update layers" checked:true width:280

    on btn_selectAvailableParts pressed do
    (
        with undo off
        (
            disableSceneRedraw();setCommandPanelTaskMode #create;SceneExplorerManager.ClearAllExplorers();clearSelection();setWaitCursor()
            folderPathToLibrary=(driveLetter+"Misc/ModelLibraryNew/4.Verified/")
            makeDir folderPathToLibrary
            libFiles=getFiles(folderPathToLibrary+"*_REDUCED.max")
            libStripped=(for f in libFiles collect (fnStripToMetaDataOnly (getFileNameFile f))[1])
            libCompare=fnStripMetaToPrefixOnly libStripped
            sceneGeom=(for o in geometry where not o.isHidden collect o)
            sceneGeom=fnReturnNodesWithValidMetaName sceneGeom
            sceneNames=(for o in sceneGeom collect (fnStripToMetaDataOnly o.name)[1])
            identical=fnFindDuplicatesInArray sceneNames
            objsExistsInLibrary=#()
            for i=1 to identical.count do
            (
                windows.processPostedMessages()
                if keyboard.escPressed do (enableSceneRedraw();exit)
                perc=(i*100.0/identical.count)
                UI_LibraryProgressBar.prg_totalLibraryProgress.value=perc
                UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(perc as string+"%")
                displayTempPrompt(perc as string+"%")10000
                currObj=sceneGeom[(identical[i]as array)[1]]
                currName=sceneNames[(identical[i]as array)[1]]
                cStr=(fnStripMetaToPrefixOnly #(currName))[1]
                findInLib=(for x=1 to libCompare.count where libCompare[x]==cStr collect x)
                if findInLib.count!=0 do
                (
                    for t in (identical[i] as array) do appendIfUnique objsExistsInLibrary sceneGeom[t]
                )
            )
            clearSelection();select objsExistsInLibrary
            UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
            print("Found "+objsExistsInLibrary.count as string)
            setArrowCursor();fnForceViewportRedraw()
        )
    )

    on btn_importReplaceParts pressed do
    (
        with undo off
        (
            sceneGeom=(for o in geometry where not o.isHidden collect o)
            c=querybox("Continue?")
            if c==true do
            (
                promptLinePanel=fnGetPromptLinePanel()
                doColor=cb_colorChange.state
                doLayer=cb_layerChange.state
                disableSceneRedraw();setCommandPanelTaskMode #create;SceneExplorerManager.ClearAllExplorers();clearSelection();setWaitCursor()
                UI_LibraryProgressBar.prg_totalLibraryProgress.value=0
                UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Initializing ..."
                impLay=(LayerManager.newLayerFromName "Fixed"); impLay=LayerManager.getLayerFromName "Fixed"
                delLay=(LayerManager.newLayerFromName "OBJS_TO_DELETE"); delLay=LayerManager.getLayerFromName "OBJS_TO_DELETE"
                delLay.isHidden=1
                folderPathToLibrary=(driveLetter+"Misc/ModelLibraryNew/4.Verified/")
                makeDir folderPathToLibrary
                allTriOrg=0; allTriNew=0; libTriOrg=0; libTriNew=0
                for o in (for q in geometry where not q.isHidden collect q) do (allTriOrg+=(GetTriMeshFaceCount o)[1])
                replaced=#();deleted=#()
                libFiles=getFiles(folderPathToLibrary+"*_REDUCED.max")
                libStripped=(for f in libFiles collect (fnStripToMetaDataOnly (getFileNameFile f))[1])
                libCompare=fnStripMetaToPrefixOnly libStripped
                sceneGeom=(for o in geometry where not o.isHidden collect o)
                sceneGeom=fnReturnNodesWithValidMetaName sceneGeom
                sceneNames=(for o in sceneGeom collect (fnStripToMetaDataOnly o.name)[1])
                identical=fnFindDuplicatesInArray sceneNames

                for i=1 to identical.count do
                (
                    windows.processPostedMessages()
                    if keyboard.escPressed do(enableSceneRedraw();exit)
                    perc=(i*100.0/identical.count)
                    UI_LibraryProgressBar.prg_totalLibraryProgress.value=perc
                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(perc as string+"%")
                    windows.sendMessage promptLinePanel 11 1 0
                    displayTempPrompt(perc as string+"%")10000
                    windows.sendMessage promptLinePanel 11 0 0

                    currNode=sceneGeom[(identical[i]as array)[1]]
                    currName=sceneNames[(identical[i]as array)[1]]
                    cStr=(fnStripMetaToPrefixOnly #(currName))[1]
                    findInLib=(for x=1 to libCompare.count where libCompare[x]==cStr collect x)
                    filePathToLibrary=(for x in findInLib where fnCheckIfMetaIsCorrect currName libStripped[x] collect libFiles[x])

                    if filePathToLibrary.count!=0 do
                    (
                        tmpImportedObjs=#();oReplaced=0;oDelete=0
                        mergeMAXFile filePathToLibrary[1] #useSceneMtlDups #AutoRenameDups
                        impObjs=getLastMergedNodes()
                        matchCurr=(for t in (identical[i] as array) collect sceneGeom[t])
                        for obj in matchCurr do
                        (
                            if isValidNode obj do
                            (
                                libTriOrg+=(GetTriMeshFaceCount obj)[1]
                                windows.processPostedMessages();setWaitCursor()
                                if (impObjs[1].numFaces>0) then
                                (
                                    rObj=snapshot impObjs[1]
                                    convertToPoly rObj
                                    rObj.scale=obj.scale; rObj.rotation=obj.rotation; rObj.position=obj.position
                                    polyop.deleteFaces obj obj.faces
                                    obj.EditablePoly.attach rObj obj
                                    update obj.mesh geometry:true topology:true
                                    if doColor do obj.wirecolor=(color 7 174 90)
                                    if doLayer do (if impLay!=undefined do impLay.addNode obj)
                                    libTriNew+=(GetTriMeshFaceCount obj)[1]
                                    appendIfUnique replaced obj
                                    appendIfUnique tmpImportedObjs impObjs[1]
                                    oReplaced+=1
                                )
                                else
                                (
                                    delLay.addNode obj
                                    obj.wirecolor=(color 245 20 20)
                                    appendIfUnique deleted obj
                                    appendIfUnique tmpImportedObjs impObjs[1]
                                    oDelete+=1
                                )
                            )
                        )
                        if oDelete>0 do (print("- Placed "+oDelete as string+" in OBJS_TO_DELETE ("+currName+")"))
                        if oReplaced>0 do(print("Replaced "+oReplaced as string+" with "+currName))
                        try(delete(for x in tmpImportedObjs where isValidNode x collect x))catch()
                    )
                )
                windows.sendMessage promptLinePanel 11 1 0
                for o in (for q in geometry where not q.isHidden collect q) do (allTriNew+=(GetTriMeshFaceCount o)[1])
                redScene=(100-(allTriNew*100.0/allTriOrg)) as string
                redLib=(100-(libTriNew*100.0/libTriOrg)) as string
                print(" :: Reduced scene by "+redScene+"%")
                print(" :: Reduced library-objects by "+redLib+"%")
                fnRemoveEmptyLayers()
                clearSelection();select replaced
                UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
                print("OBJS_TO_DELETE: "+deleted.count as string)
                print("Replaced: "+replaced.count as string)
                setArrowCursor();fnForceViewportRedraw()
            )
        )
    )
)

rollout UI_SelectIdentical "Select Identical Objects" rolledUp:false
(
    group "Include the Following Object Sizes"
    (
        checkBox cb_size0 "0" offset:[10,0] checked:false width:30
        checkBox cb_size1 "1" offset:[40,-20] checked:true width:30
        checkBox cb_size2 "2" offset:[70,-20] checked:true width:30
        checkBox cb_size3 "3" offset:[100,-20] checked:true width:30
        checkBox cb_size4 "4" offset:[130,-20] checked:true width:30
        checkBox cb_size5 "5" offset:[160,-20] checked:true width:30
        checkBox cb_size6 "6" offset:[190,-20] checked:true width:30
        checkBox cb_size7 "7" offset:[220,-20] checked:true width:30
    )
    spinner spn_minIdentical "Minimum Number of Identical Objects: " range:[1,1000,1] type:#integer width:180
    button btn_selOneOfEach "Select One of Each Identical Object" width:280 height:20
    button btn_selIdentical "Select Identical Objects to Selection" width:280 height:20
    button btn_selOrigoPivot "Select Objects with Pivot in Origo [0,0,0]" width:280 height:20

    on btn_selOneOfEach pressed do
    (
        setCommandPanelTaskMode #create;setWaitCursor()
        s0=cb_size0.state; s1=cb_size1.state; s2=cb_size2.state; s3=cb_size3.state; s4=cb_size4.state; s5=cb_size5.state; s6=cb_size6.state; s7=cb_size7.state
        sizes=#((if s0 then "0_*" else false),(if s1 then "1_*" else false),(if s2 then "2_*" else false),(if s3 then "3_*" else false),
                (if s4 then "4_*" else false),(if s5 then "5_*" else false),(if s6 then "6_*" else false),(if s7 then "7_*" else false))

        sceneGeom=#()
        for o in geometry where not o.isHidden do
            for sz in sizes where sz!=false do
                if matchPattern o.name pattern:sz do appendIfUnique sceneGeom o

        sceneGeom=fnReturnNodesWithValidMetaName sceneGeom
        sNames=(for o in sceneGeom collect (fnStripToMetaDataOnly o.name)[1])
        sCmp=fnStripMetaToPrefixOnly sNames
        identical=fnFindDuplicatesInArray sNames
        uniqueIdentical=#()
        for i=1 to identical.count do
        (
            windows.processPostedMessages()
            if keyboard.escPressed do (enableSceneRedraw();exit)
            p=(i*100.0/identical.count)
            UI_LibraryProgressBar.prg_totalLibraryProgress.value=p
            UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(p as string + "%")
            displayTempPrompt (p as string+"%") 10000
            cObj=sceneGeom[(identical[i]as array)[1]]
            cName=sNames[(identical[i]as array)[1]]
            cStr=(fnStripMetaToPrefixOnly #(cName))[1]
            foundIdx=(for ix=1 to sCmp.count where sCmp[ix]==cStr collect ix)
            foundItems=(for x in foundIdx where fnCheckIfMetaIsCorrect cName sNames[x] collect sceneGeom[x])
            if foundItems.count>=spn_minIdentical.value do
            (
                appendIfUnique uniqueIdentical foundItems[1]
                print(foundItems.count as string+" identical: "+cName)
            )
        )
        clearSelection();select uniqueIdentical
        UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
        print("Found "+uniqueIdentical.count as string+" unique")
        setArrowCursor();fnForceViewportRedraw()
    )

    on btn_selIdentical pressed do
    (
        setCommandPanelTaskMode #create;setWaitCursor()
        selObj=selection as array
        selNames=(for o in selObj collect (fnStripToMetaDataOnly o.name)[1])

        sceneGeom=(for o in geometry where not o.isHidden collect o)
        sceneGeom=fnReturnNodesWithValidMetaName sceneGeom
        sNames=(for o in sceneGeom collect (fnStripToMetaDataOnly o.name)[1])
        sCmp=fnStripMetaToPrefixOnly sNames

        identicalFromSel=#()
        for i=1 to selObj.count do
        (
            windows.processPostedMessages()
            if keyboard.escPressed do (enableSceneRedraw();exit)
            p=(i*100.0/selObj.count)
            UI_LibraryProgressBar.prg_totalLibraryProgress.value=p
            UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(p as string+"%")
            displayTempPrompt (p as string+"%") 10000

            cName=selNames[i]
            cStr=(fnStripMetaToPrefixOnly #(cName))[1]
            fIdx=(for ix=1 to sCmp.count where sCmp[ix]==cStr collect ix)
            fVerified=(for x in fIdx where fnCheckIfMetaIsCorrect cName sNames[x] collect sceneGeom[x])
            for o in fVerified do appendIfUnique identicalFromSel o
        )
        clearSelection();select identicalFromSel
        UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
        print("Found "+selection.count as string)
        setArrowCursor();fnForceViewportRedraw()
    )

    on btn_selOrigoPivot pressed do
    (
        res=#()
        for o in geometry where not o.isHidden do
            if (fnRoundPositionDecimals o.pivot 3 as string=="[0,0,0]") do appendIfUnique res o
        select res
    )
)

rollout UI_SaveToTempLibrary "Save to Temporary Library" rolledUp:true
(
    button btn_saveSelToLibrary "Save Selected Objects" width:280 height:20
    button btn_saveAllToLibrary "Save all Visible Objects (one of each unique node)" width:280 height:20

    on btn_saveSelToLibrary pressed do
    (
        with undo off
        (
            folder=(driveLetter+"Misc/ModelLibraryNew/1.PartsUntouched/")
            makeDir folder
            objNameArr=#()
            selObjs=(for o in selection where not o.isHidden collect o)
            selObjs=(for o in selObjs where ((appendIfUnique objNameArr (fnStripToMetaDataOnly o.name)[1])==true) collect o)
            c=querybox("Continue?")
            if c==true do
            (
                print ("Exporting "+selObjs.count as string)
                setCommandPanelTaskMode #create;setWaitCursor()
                for i=1 to selObjs.count do
                (
                    windows.processPostedMessages()
                    if keyboard.escPressed do (enableSceneRedraw();exit)
                    p=(i*100.0/selObjs.count)
                    UI_LibraryProgressBar.prg_totalLibraryProgress.value=p
                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(p as string+"%")
                    displayTempPrompt (p as string+"%") 10000
                    if (fnFindInString selObjs[i].name "]_[" true==false) then (messageBox("WARNING: "+selObjs[i].name))
                    else
                    (
                        tmpMat=selObjs[i].material
                        tmpPar=selObjs[i].parent
                        tmpCh=(for c in selObjs[i].children collect c)
                        selObjs[i].material=undefined; selObjs[i].parent=undefined
                        tmpCh.parent=undefined
                        saveNodes selObjs[i] (folder+selObjs[i].name+"_RAW.max") quiet:false
                        saveNodes selObjs[i] (folder+selObjs[i].name+"_REDUCED.max") quiet:false
                        selObjs[i].material=tmpMat;selObjs[i].parent=tmpPar
                        tmpCh.parent=selObjs[i]
                    )
                )
            )
            UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
            print ("Finished: "+folder)
            setArrowCursor();fnForceViewportRedraw()
        )
    )

    on btn_saveAllToLibrary pressed do
    (
        with undo off
        (
            folder=(driveLetter+"Misc/ModelLibraryNew/1.PartsUntouched/")
            makeDir folder
            objNameArr=#()
            allObjs=(for o in geometry where not o.isHidden collect o)
            allObjs=(for o in allObjs where ((appendIfUnique objNameArr (fnStripToMetaDataOnly o.name)[1])==true) collect o)
            c=querybox("Continue?")
            if c==true do
            (
                print ("Exporting "+allObjs.count as string)
                setCommandPanelTaskMode #create;setWaitCursor()
                for i=1 to allObjs.count do
                (
                    windows.processPostedMessages()
                    if keyboard.escPressed do (enableSceneRedraw();exit)
                    p=(i*100.0/allObjs.count)
                    UI_LibraryProgressBar.prg_totalLibraryProgress.value=p
                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(p as string+"%")
                    displayTempPrompt (p as string+"%") 10000
                    if (fnFindInString allObjs[i].name "]_[" true==false) then (messageBox("WARNING: "+allObjs[i].name);exit)
                    else
                    (
                        tmpMat=allObjs[i].material
                        tmpPar=allObjs[i].parent
                        tmpCh=(for c in allObjs[i].children collect c)
                        allObjs[i].material=undefined;allObjs[i].parent=undefined
                        tmpCh.parent=undefined
                        saveNodes allObjs[i] (folder+allObjs[i].name+"_RAW.max") quiet:false
                        saveNodes allObjs[i] (folder+allObjs[i].name+"_REDUCED.max") quiet:false
                        allObjs[i].material=tmpMat;allObjs[i].parent=tmpPar
                        tmpCh.parent=allObjs[i]
                    )
                )
            )
            UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
            print("Finished: "+folder)
            setArrowCursor();fnForceViewportRedraw()
        )
    )
)

rollout UI_PerformActualReducing "Reduce and Export Files" rolledUp:true
(
    checkbox chk_zeroTransforms "Zero Transforms and Place Imported Objects in Grid" checked:false
    button btn_importForReducing "Import Files for Reducing" width:280 height:20
    checkbox chk_hideAfterReplaced "Hide Obj after Replaced" checked:true
    button btn_replaceTmpWithOrg "Replace Reduced with Original (from selection)" width:280 height:20
    label lbl_reducedByPrc
    button btn_exportToNextStep "Export Objects to the Next Step" width:280 height:20

    on btn_importForReducing pressed do
    (
        with undo off
        (
            folder=(driveLetter+"Misc/ModelLibraryNew/1.PartsUntouched/")
            makeDir folder
            libFiles=getFiles (folder+"*_REDUCED.max")
            c=querybox("Continue?")
            if c==true do
            (
                setCommandPanelTaskMode #create;setWaitCursor()
                resetMaxFile #noprompt
                Units.Systemtype=#meters;Units.Displaytype=#Metric;Units.Metrictype=#millimeters
                lOrg=(LayerManager.newLayerFromName "ORIGINAL"); lOrg.isHidden=1
                lTmp=(LayerManager.newLayerFromName "WIP_TO_REDUCE"); lTmp.isHidden=0

                for i=1 to libFiles.count do
                (
                    windows.processPostedMessages()
                    if keyboard.escPressed do(exit)
                    p=(i*100.0/libFiles.count)
                    UI_LibraryProgressBar.prg_totalLibraryProgress.value=p
                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(p as string+"%")
                    displayTempPrompt (p as string+"%")10000
                    mergeMAXFile libFiles[i] #useSceneMtlDups
                    imp=getLastMergedNodes()
                    lOrg.addNode imp[1]
                    imp[1].wireColor=(color 7 174 90)
                    if chk_zeroTransforms.state do
                    (
                        imp[1].scale=[1,1,1];imp[1].rotation=(quat 0 0 0 1);imp[1].position=[0,0,0]
                    )
                )

                sceneGeom=(for o in geometry collect o)
                xC=0;yC=0
                for i=1 to sceneGeom.count do
                (
                    if chk_zeroTransforms.state do sceneGeom[i].position=[xC,yC,0]
                    dup=snapshot sceneGeom[i]
                    convertToPoly dup
                    dup.name=(sceneGeom[i].name+"_TempToReduce")
                    lTmp.addNode dup
                    dup.wireColor=(color 135 6 6)
                    if (yC==(sqrt sceneGeom.count)) then (xC+=1;yC=0) else (yC+=1)
                )

                fnRemoveEmptyLayers()
                fileIn((getDir #userMacros) + @"\Jorn Tools\ViewportSettings.ms")
                UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
                print ("Imported "+libFiles.count as string)
                print ("Reduce them then replace with the original")
                max zoomext sel
                setArrowCursor();fnForceViewportRedraw()
            )
        )
    )

    on btn_replaceTmpWithOrg pressed do
    (
        undo "ReplaceObject" on
        (
            s=selection as array
            if s.count==2 then
            (
                if (s[1].layer.name=="ORIGINAL" and s[2].layer.name!="ORIGINAL") then
                (
                    v1=s[1].verts.count; v2=s[2].verts.count
                    d=(v1-v2); prc=(100.0-(v2*100.0/v1)) as string
                    polyop.deleteFaces s[1] s[1].faces
                    polyop.attach s[1] s[2]
                    if chk_hideAfterReplaced.state do s[2].isHidden=true
                    print ("Reduced by: "+prc+"%")
                    displayTempPrompt("Reduced by: "+prc+"%")10000
                    UI_PerformActualReducing.lbl_reducedByPrc.text=("Reduced by: "+prc+"%")
                )
                else if (s[2].layer.name=="ORIGINAL" and s[1].layer.name!="ORIGINAL") then
                (
                    v1=s[1].verts.count; v2=s[2].verts.count
                    d=(v2-v1); prc=(100.0-(v1*100.0/v2)) as string
                    polyop.deleteFaces s[2] s[2].faces
                    polyop.attach s[2] s[1]
                    if chk_hideAfterReplaced.state do s[2].isHidden=true
                    print ("Reduced by: "+prc+"%")
                    displayTempPrompt("Reduced by: "+prc+"%")10000
                    UI_PerformActualReducing.lbl_reducedByPrc.text=("Reduced by: "+prc+"%")
                )
                else print("Select two: destination in 'ORIGINAL' layer")
            )
            else print("Select two: one in 'ORIGINAL' layer")
            setArrowCursor();fnForceViewportRedraw()
        )
    )

    on btn_exportToNextStep pressed do
    (
        with undo off
        (
            fOld=(driveLetter+"Misc/ModelLibraryNew/1.PartsUntouched/")
            fNew=(driveLetter+"Misc/ModelLibraryNew/2.ReducedObjects/")
            makeDir fOld;makeDir fNew
            rObjs=(for o in objects where o.layer.name=="ORIGINAL" and not o.isHidden collect o)
            if rObjs.count>0 then
            (
                c=querybox("Continue?")
                if c==true do
                (
                    print ("Exporting "+rObjs.count as string)
                    setCommandPanelTaskMode #create;setWaitCursor()
                    for i=1 to rObjs.count do
                    (
                        windows.processPostedMessages()
                        if keyboard.escPressed do(exit)
                        p=(i*100.0/rObjs.count)
                        UI_LibraryProgressBar.prg_totalLibraryProgress.value=p
                        UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(p as string+"%")
                        tmpPar=rObjs[i].parent; tmpCh=(for c in rObjs[i].children collect c)
                        rObjs[i].parent=undefined; tmpCh.parent=undefined
                        saveNodes rObjs[i] (fNew+rObjs[i].name+"_REDUCED.max") quiet:false
                        rObjs[i].parent=tmpPar; tmpCh.parent=rObjs[i]
                    )
                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Part 1/2 Finished"
                    print("Part 1/2 Finished")

                    rawFiles=getFiles (fOld+"*_RAW.max")
                    rawFNames=(for f in rawFiles collect (getFileNameFile f))
                    rawNoSuf=(for f in rawFNames collect (substring f 1 (f.count-4)))

                    reducedFiles=getFiles (fNew+"*_REDUCED.max")
                    redFNames=(for f in reducedFiles collect (getFileNameFile f))
                    redNoSuf=(for f in redFNames collect (substring f 1 (f.count-8)))

                    for i=1 to reducedFiles.count do
                    (
                        windows.processPostedMessages()
                        if keyboard.escPressed do(exit)
                        p=(i*100.0/rObjs.count)
                        UI_LibraryProgressBar.prg_totalLibraryProgress.value=p
                        UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(p as string+"%")
                        idx=findItem rawNoSuf redNoSuf[i]
                        if idx!=0 do copyFile rawFiles[idx] (fNew+rawFNames[idx]+".max")
                    )
                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Part 2/2 Finished"
                    print("Part 2/2 Finished")
                    setArrowCursor();fnForceViewportRedraw()
                )
            )
            else print("No reduced objects in ORIGINAL layer")
        )
    )
)

rollout UI_CleanupReducedFiles "Cleanup Reduced Files" rolledUp:true
(
    checkbox chk_doNotTouchNormals "Do not Touch Normals when Cleaning up" checked:true
    button btn_cleanupReducedFiles "Cleanup Reduced Files" width:280 height:20

    on btn_cleanupReducedFiles pressed do
    (
        with undo off
        (
            fSource=(driveLetter+"Misc/ModelLibraryNew/2.ReducedObjects/")
            fDest=(driveLetter+"Misc/ModelLibraryNew/3.ReadyForLibraryImport/")
            makeDir fSource;makeDir fDest
            srcFiles=getFiles (fSource+"*.max")
            destFiles=for f in srcFiles collect (fnReplaceInString f "2.ReducedObjects" "3.ReadyForLibraryImport" true)
            if srcFiles.count>0 then
            (
                c=querybox(srcFiles.count as string+" files will be cleaned. Continue?")
                if c==true do
                (
                    promptLinePanel=fnGetPromptLinePanel()
                    setCommandPanelTaskMode #create;setWaitCursor()
                    for i=1 to srcFiles.count do
                    (
                        windows.processPostedMessages()
                        if keyboard.escPressed do(exit)
                        p=(i*100.0/srcFiles.count)
                        UI_LibraryProgressBar.prg_totalLibraryProgress.value=p
                        cfStr=("("+i as string+" of "+srcFiles.count as string+")")
                        UI_LibraryProgressBar.lbl_totalLibraryProgress.text=(p as string+"% "+cfStr)
                        windows.sendMessage promptLinePanel 11 1 0
                        displayTempPrompt ("Progress: "+p as string+"% "+cfStr)10000
                        windows.sendMessage promptLinePanel 11 0 0
                        resetMaxFile #noprompt
                        Units.Systemtype=#meters;Units.Displaytype=#Metric;Units.Metrictype=#millimeters
                        isLoaded=mergeMAXFile srcFiles[i]
                        if isLoaded!=false do
                        (
                            gc();freeSceneBitmaps();clearUndoBuffer()
                            viewport.ResetAllViews();completeRedraw();viewport.setLayout #layout_4
                            viewport.activeViewport=4;max tool maximize
                            viewport.setTransparencyLevel 3
                            viewport.SetAdaptiveDegNeverDegradeSelected true
                            viewport.SetAdaptiveDegNeverRedrawAfterDegrade false
                            viewport.setAdaptiveDegDisplayModeFastShaded true
                            viewport.SetAdaptiveDegDisplayModeHide false
                            vSet = NitrousGraphicsManager.GetActiveViewportSetting()
                            vSet.ViewportViewSettingImpl.ShadowsEnabled=off
                            vSet.SelectedEdgedFacesEnabled=on
                            vSet.ShowEdgedFacesEnabled=off
                            vSet.ShowSelectionBracketsEnabled=off
                            vSet.ViewportClippingEnabled=true
                            vSet.ViewportViewSettingImpl.AmbientOcclusionEnabled=off
                            vSet.AdaptiveDegradeNeverDegradeGeometry=true
                            vSet.VisualStyleMode=#shaded
                            displaySafeFrames=off
                            NitrousGraphicsManager.AntialiasingQuality=#8X
                            SceneExplorerManager.ClearAllExplorers()

                            if (geometry as array).count==1 do
                            (
                                IsolateSelection.ExitIsolateSelectionMode()
                                geometry[1].wirecolor=(color 166 7 55)
                                geometry[1].displayByLayer=false
                                geometry[1].scale=[1,1,1]
                                geometry[1].rotation=(quat 0 0 0 1)
                                geometry[1].position=[0,0,0]
                                (LayerManager.getLayer 0).addNode geometry[1]
                                (LayerManager.getLayer 0).current=true
                                (LayerManager.getLayer 0).isHidden=false
                                fnRemoveEmptyLayers()
                                max zoomext sel
                                if ((substring srcFiles[i] (srcFiles[i].count-11) srcFiles[i].count)=="_REDUCED.max") do
                                (
                                    if not chk_doNotTouchNormals.state do
                                    (
                                        polyop.setFaceSelection geometry[1] geometry[1].faces
                                        geometry[1].baseObject.EditablePoly.setSmoothingGroups 0 -1 1
                                        geometry[1].autoSmoothThreshold=35
                                        geometry[1].baseObject.EditablePoly.autosmooth()
                                    )
                                    resetScale geometry[1]
                                    pt= (geometry[1].transform*(inverse geometry[1].objectTransform))
                                    invP=(inverse pt)
                                    for v=1 to (polyop.getNumVerts geometry[1].baseobject) do
                                    (
                                        p=polyop.getVert geometry[1].baseObject v
                                        p=p*invP
                                        polyop.setVert geometry[1].baseObject v p
                                    )
                                    geometry[1].objectOffsetPos=[0,0,0]
                                    geometry[1].objectOffsetRot=(quat 0 0 0 1)
                                    geometry[1].objectOffsetScale=[1,1,1]
                                    objects.wirecolor=(color 7 174 90)
                                    if (geometry[1].material==undefined) do
                                    (
                                        uv=Uvwmap()
                                        addModifier geometry[1] uv
                                        uv.maptype=4;uv.length=0.6;uv.width=0.6;uv.height=0.6
                                        convertToPoly geometry[1]
                                    )
                                )
                                saveMaxFile destFiles[i] useNewFile:false
                                print ((p as string)+"% : Saved: "+destFiles[i])
                            )
                        )
                    )
                    windows.sendMessage promptLinePanel 11 1 0
                    UI_LibraryProgressBar.lbl_totalLibraryProgress.text="Finished"
                    print("Finished: "+fDest)
                    messageBox("Objects are saved in: "+fDest) title:"Finished"
                    setArrowCursor()
                )
            )
            else print("No files in: "+fSource)
        )
    )
)

rollout UI_LibraryFunctions "ONLY USE IN LIBRARY SCENE!" rolledUp:true
(
    group "Progress"
    (
        progressbar prg_LibraryFunctionsProgress color:green
        label lbl_LibraryFunctionsProgress
    )
    group "Import New Objects"
    (
        button btn_importNewObjects "Import new Raw/Reduced Pairs" width:260 height:20
        button btn_cleanDirectories "Clean up Directories" width:260 height:20
    )
    group "Library Workflow and Validation Tools"
    (
        button btn_findPairMismatch "Find Pair Mismatch (missing sibling)" width:260 height:20
        button btn_findUnreduced "Find Objects that has not been Reduced" width:260 height:20
        button btn_findDuplicateObjs "Find Objects with Duplicate Meta" width:260 height:20
        button btn_findMissingName "Find Objects without Objectname" width:260 height:20
        button btn_updateMetadata "Update Metadata on all Visible Nodes" width:260 height:20
        checkbox chk_skipProbablyRenamed "Skip nodes which seem to already be renamed" checked:true
    )
    group "Place Objects"
    (
        radiobuttons rb_gridSorting "Sort By:" labels:#("Metadata", "Raw VertCount", "Reduced VertCount") default:1
        checkBox cb_sortByLayers " Sort by Layers" checked:true
        button btn_placeInGridPairs "Place Visible Pairs in a Grid" width:260 height:20
        button btn_placeInGridNormal "Place all Visible Objects in a Grid" width:260 height:20
        spinner spn_gridSpacing "Grid Object Spacing: " range:[0.1,1e7,1.5] type:#float width:180
    )
    group "Export Objects from Main Scene"
    (
        checkbox chk_replaceExisting "Replace Existing Files" checked:false
        button btn_exportValidatedReduced "Export Validated Visible Reduced Objects" width:260 height:20
        button btn_exportValidatedRaw "Export Visible Raw Objects" width:260 height:20
    )

    on btn_importNewObjects pressed do
    (
        with undo off
        (
            f=(driveLetter+"Misc/ModelLibraryNew/3.ReadyForLibraryImport/")
            makeDir f
            rawFiles=getFiles(f+"*_RAW.max")
            redFiles=getFiles(f+"*_REDUCED.max")
            if (rawFiles.count+redFiles.count)>0 then
            (
                c=querybox("Import all "+(rawFiles.count+redFiles.count) as string+"?")
                if c==true do
                (
                    importedRaw=#();importedRed=#()
                    setCommandPanelTaskMode #create;setWaitCursor();SceneExplorerManager.ClearAllExplorers();clearSelection()
                    lRAW=(LayerManager.newLayerFromName "RAW");lRAW=(LayerManager.getLayerFromName "RAW");lRAW.isHidden=1
                    lRED=(LayerManager.newLayerFromName "REDUCED");lRED=(LayerManager.getLayerFromName "REDUCED");lRED.isHidden=1
                    lNRaw=(LayerManager.newLayerFromName "NEW_RAW");lNRaw=(LayerManager.getLayerFromName "NEW_RAW");lNRaw.isHidden=1
                    lNRed=(LayerManager.newLayerFromName "NEW_REDUCED");lNRed=(LayerManager.getLayerFromName "NEW_REDUCED");lNRed.isHidden=1
                    lEmpty=(LayerManager.newLayerFromName "EMPTY");lEmpty=(LayerManager.getLayerFromName "EMPTY")
                    lMat=(LayerManager.newLayerFromName "ASSIGNED_MATERIAL");lMat=(LayerManager.getLayerFromName "ASSIGNED_MATERIAL")

                    for i=1 to rawFiles.count do
                    (
                        windows.processPostedMessages()
                        if keyboard.escPressed do (enableSceneRedraw();exit)
                        p=(i*100.0/rawFiles.count)
                        prg_LibraryFunctionsProgress.value=p
                        lbl_LibraryFunctionsProgress.text=(p as string+"%")
                        mergeMAXFile rawFiles[i] #skipDups
                        iRaw=getLastMergedNodes()
                        oRN=iRaw[1].name
                        iRaw[1].name=(oRN+"_RAW")
                        iRaw[1].scale=[1,1,1];iRaw[1].rotation=(quat 0 0 0 1);iRaw[1].position=[0,0,0]
                        mergeMAXFile (fnReplaceInString rawFiles[i] "_RAW" "_REDUCED" true) #skipDups
                        iRed=getLastMergedNodes()
                        oRR=iRed[1].name
                        iRed[1].name=(oRN+"_REDUCED")
                        iRaw[1].scale=[1,1,1];iRaw[1].rotation=(quat 0 0 0 1);iRaw[1].position=[0,0,0]

                        if (iRaw.count!=0 and iRed.count!=0) then
                        (
                            iRaw[1].name=fnReplaceInString iRaw[1].name "_REDUCED" "_RAW" true
                            iRaw[1].name=fnReplaceInString iRaw[1].name "_RAW_RAW" "_RAW" true
                            iRaw[1].name=fnReplaceInString iRaw[1].name "CORRUPT_CORRUPT_" "CORRUPT_" true
                            iRed[1].name=fnReplaceInString iRaw[1].name "_RAW" "_REDUCED" true
                            iRed[1].name=fnReplaceInString iRed[1].name "_REDUCED_REDUCED" "_REDUCED" true
                            iRed[1].name=fnReplaceInString iRed[1].name "CORRUPT_CORRUPT_" "CORRUPT_" true
                            lNRaw.addNode iRaw[1]
                            lNRed.addNode iRed[1]
                            if iRed[1].numFaces==0 do (lEmpty.addNode iRaw[1];lEmpty.addNode iRed[1])
                            if iRed[1].material!=undefined do (lMat.addNode iRaw[1];lMat.addNode iRed[1])
                            append importedRaw iRaw[1]; append importedRed iRed[1]
                        )
                        else
                        (
                            print ("Missing reduced: "+iRaw[1].name)
                            delete iRaw;delete iRed
                        )
                    )
                    lRAW.isHidden=0;lRED.isHidden=0;lNRaw.isHidden=0;lNRed.isHidden=0
                    fnRemoveEmptyLayers()
                    fnPlaceLibraryObjsInGrid importedRaw 1.5 true
                    clearSelection();select (join importedRaw importedRed)
                    CompleteRedraw();prg_LibraryFunctionsProgress.value=100;lbl_LibraryFunctionsProgress.text="Finished"
                    print("Finished: "+rawFiles.count as string)
                    messageBox("Consider running 'Clean up Directories'") title:"Finished"
                    setArrowCursor()
                )
            )
            else print("No files in: "+f)
        )
    )

    on btn_cleanDirectories pressed do
    (
        with undo off
        (
            p1=(driveLetter+"Misc/ModelLibraryNew/1.PartsUntouched/")
            p2=(driveLetter+"Misc/ModelLibraryNew/2.ReducedObjects/")
            p3=(driveLetter+"Misc/ModelLibraryNew/3.ReadyForLibraryImport/")
            makeDir p1;makeDir p2;makeDir p3
            f1=(getFiles(p1+"*.max"))
            f2=(getFiles(p2+"*.max"))
            f3=(getFiles(p3+"*.max"))
            c=querybox("Delete "+(f1.count+f2.count+f3.count) as string+" files?")
            if c==true do
            (
                for ff in f1 do deleteFile ff
                for ff in f2 do deleteFile ff
                for ff in f3 do deleteFile ff
                print("Deleted: "+(f1.count+f2.count+f3.count) as string)
                displayTempPrompt("Deleted: "+(f1.count+f2.count+f3.count) as string)10000
            )
        )
    )

    on btn_findPairMismatch pressed do
    (
        setCommandPanelTaskMode #create;setWaitCursor();SceneExplorerManager.ClearAllExplorers();clearSelection()
        mismatch=#()
        raw=(for o in geometry where matchPattern o.name pattern:"*_RAW" collect o.name)
        raw=sort raw
        rawPair=for n in raw collect ((substring n 1 (n.count-4))+"_REDUCED")
        red=(for o in geometry where matchPattern o.name pattern:"*_REDUCED" collect o.name)
        red=sort red
        redPair=for n in red collect ((substring n 1 (n.count-8))+"_RAW")

        rawMissing=(for i=1 to raw.count where findItem red rawPair[i]==0 collect (getNodeByName raw[i]))
        redMissing=(for i=1 to red.count where findItem raw redPair[i]==0 collect (getNodeByName red[i]))

        for o in rawMissing do (print("Missing REDUCED: "+o.name);appendIfUnique mismatch o)
        for o in redMissing do (print("Missing RAW: "+o.name);appendIfUnique mismatch o)
        prg_LibraryFunctionsProgress.value=100;lbl_LibraryFunctionsProgress.text="Finished"
        select mismatch
        print("Finished: "+selection.count as string)
    )

    on btn_findUnreduced pressed do
    (
        setCommandPanelTaskMode #create;setWaitCursor();SceneExplorerManager.ClearAllExplorers();clearSelection()
        unreduced=#()
        raw=(for o in geometry where matchPattern o.name pattern:"*_RAW" collect o.name)
        raw=sort raw
        rawPair=for n in raw collect ((substring n 1 (n.count-4))+"_REDUCED")
        for i=1 to raw.count do
        (
            windows.processPostedMessages()
            if keyboard.escPressed do(exit)
            p=(i*100.0/raw.count)
            prg_LibraryFunctionsProgress.value=p
            lbl_LibraryFunctionsProgress.text=(p as string+"% ("+i as string+" of "+raw.count as string+")")
            cRaw=getNodeByName raw[i]; cRed=getNodeByName rawPair[i]
            if cRed!=undefined then
            (
                rv=cRaw.verts.count; re=cRaw.edges.count; rf=cRaw.faces.count
                rv2=cRed.verts.count; re2=cRed.edges.count; rf2=cRed.faces.count
                if (rv==rv2 and re==re2 and rf==rf2) do
                (
                    print("Not reduced: "+cRed.name)
                    appendIfUnique unreduced cRaw; appendIfUnique unreduced cRed
                )
            )
            else print("Missing sibling: "+cRaw.name)
        )
        prg_LibraryFunctionsProgress.value=100;lbl_LibraryFunctionsProgress.text="Finished"
        select unreduced
        print("Found "+(selection.count/2) as string+" unreduced pairs")
    )

    on btn_findDuplicateObjs pressed do
    (
        clearSelection()
        sceneGeom=(for o in geometry where not o.isHidden collect o)
        sceneGeom=fnReturnNodesWithValidMetaName sceneGeom
        sNames=(for o in sceneGeom collect (fnStripToMetaDataOnly o.name)[1])
        sCmp=fnStripMetaToPrefixOnly sNames
        identical=fnFindDuplicatesInArray sCmp
        dup=#();dupNames=#()
        for i=1 to identical.count do
        (
            c=(identical[i]as array)
            if c.count>2 do
            (
                curr=(for ix in c collect sceneGeom[ix])
                currDup=(for o in curr where fnCheckIfMetaIsCorrect curr[1] o.name collect o)
                for d=3 to currDup.count do
                (
                    appendIfUnique dupNames sceneGeom[c[d]].name
                    appendIfUnique dup sceneGeom[c[d]]
                )
            )
        )
        select dup
        for n in dupNames do(format(n+"\n"))
        prg_LibraryFunctionsProgress.value=100;lbl_LibraryFunctionsProgress.text="100%"
        print("Found "+dup.count as string+" duplicates")
        completeRedraw()
    )

    on btn_findMissingName pressed do
    (
        clearSelection()
        oArr=(for o in objects where not o.isHidden collect o)
        select (for o in oArr where matchPattern o.name pattern:"*&obj&*" ignoreCase:true collect o)
        prg_LibraryFunctionsProgress.value=100;lbl_LibraryFunctionsProgress.text="100%"
        print("Found "+selection.count as string+" nodes with '&obj&'")
        completeRedraw()
    )

    on btn_updateMetadata pressed do
    (
        c=querybox("Continue?")
        if c==true do
        (
            fR=(chk_skipProbablyRenamed.state==false)
            setCommandPanelTaskMode #create;setWaitCursor();SceneExplorerManager.ClearAllExplorers();clearSelection()
            already=#();renamedRes=#()

            raw=(for o in geometry where not o.isHidden collect o.name)
            raw=(for n in raw where matchPattern n pattern:"*_RAW" collect n)
            raw=sort raw
            rawPair=for n in raw collect ((substring n 1 (n.count-4))+"_REDUCED")
            for i=1 to raw.count do
            (
                windows.processPostedMessages()
                if keyboard.escPressed do(exit)
                p=(i*100.0/raw.count)
                prg_LibraryFunctionsProgress.value=p
                lbl_LibraryFunctionsProgress.text=(p as string+"% ("+i as string+" of "+raw.count as string+")")
                cRaw=getNodeByName raw[i]; cRed=getNodeByName rawPair[i]
                if cRed!=undefined then
                (
                    cRed.position=cRaw.position
                    mData=fnRetrieveMeshInfo cRaw fR
                    cRed.pivot=cRaw.pivot
                    if mData!=undefined then
                    (
                        oRaw=fnStripToMetaDataOnly cRaw.name
                        nRaw=fnStripToMetaDataOnly mData
                        if (oRaw[1]==nRaw[1] and oRaw[2]==nRaw[2]) then append already cRaw else
                        (
                            newRaw=mData
                            newRed=(substring mData 1 (mData.count-4))+"_REDUCED"
                            cRaw.name=newRaw
                            cRed.name=newRed
                            append renamedRes cRaw
                            append renamedRes cRed
                        )
                    )
                )
                else print("Missing reduced: "+cRaw.name)
            )
            prg_LibraryFunctionsProgress.value=100;lbl_LibraryFunctionsProgress.text="Finished"
            clearSelection();select renamedRes
            print("Updated: "+selection.count as string)
            setArrowCursor()
        )
    )

    on btn_placeInGridPairs pressed do
    (
        initSel=selection as array
        setWaitCursor();disableSceneRedraw();setCommandPanelTaskMode #create;clearSelection()
        sortedObjs=#()
        if (rb_gridSorting.state==1) then
        (
            objs=(for o in geometry where not o.isHidden and matchPattern o.name pattern:"*_RAW" collect o.name)
            objs=sort objs
            objs=(for n in objs where (getNodeByName n!=undefined) collect (getNodeByName n))
            sortedObjs=objs
        )
        else if (rb_gridSorting.state==2) then
        (
            objs=(for o in geometry where not o.isHidden and matchPattern o.name pattern:"*_RAW" collect o)
            arr=for o in objs collect (#((filterString o.name "_")[3] as integer,o))
            fn sortByCol1 a b=if a[1]<b[1] then -1 else if a[1]>b[1] then 1 else 0
            qsort arr sortByCol1
            objs=for a in arr collect a[2]
            sortedObjs=objs
        )
        else if (rb_gridSorting.state==3) then
        (
            objs=(for o in geometry where not o.isHidden and matchPattern o.name pattern:"*_RAW" collect o)
            arr=for o in objs collect #(0,o)
            for i=1 to arr.count do
            (
                cRed=getNodeByName ((substring arr[i][2].name 1 (arr[i][2].name.count-4))+"_REDUCED")
                if cRed!=undefined then arr[i][1]=cRed.numVerts else print(arr[i][2].name+" no reduced")
            )
            fn sortByCol1 a b=if a[1]<b[1] then -1 else if a[1]>b[1] then 1 else 0
            qsort arr sortByCol1
            objs=for a in arr collect a[2]
            sortedObjs=objs
        )

        if cb_sortByLayers.state then
        (
            oArrs=fnSeparateObjsByLayers sortedObjs
            tmp=#()
            for i=1 to oArrs.count do for o in oArrs[i] do appendIfUnique tmp o
            sortedObjs=tmp
        )

        num=fnPlaceLibraryObjsInGrid sortedObjs spn_gridSpacing.value true
        clearSelection();select initSel
        fnForceViewportRedraw()
        print(num as string+" placed")
    )

    on btn_placeInGridNormal pressed do
    (
        initSel=selection as array
        setWaitCursor();disableSceneRedraw();setCommandPanelTaskMode #create;clearSelection()
        sortedObjs=#()
        if (rb_gridSorting.state==1) then
        (
            objs=(for o in geometry where not o.isHidden collect o.name)
            objs=sort objs
            objs=(for n in objs where getNodeByName n!=undefined collect getNodeByName n)
            sortedObjs=objs
        )
        else if (rb_gridSorting.state==2 or rb_gridSorting.state==3) then
        (
            objs=(for o in geometry where not o.isHidden collect #(o.numVerts,o))
            fn sortByCol1 a b=if a[1]<b[1] then -1 else if a[1]>b[1] then 1 else 0
            qsort objs sortByCol1
            objs=for a in objs collect a[2]
            sortedObjs=objs
        )

        if cb_sortByLayers.state then
        (
            oArrs=fnSeparateObjsByLayers sortedObjs
            tmp=#()
            for i=1 to oArrs.count do for o in oArrs[i] do appendIfUnique tmp o
            sortedObjs=tmp
        )

        num=fnPlaceLibraryObjsInGrid sortedObjs spn_gridSpacing.value false
        clearSelection();select initSel
        fnForceViewportRedraw()
        print(num as string+" placed")
    )

    on btn_exportValidatedReduced pressed do
    (
        with undo off
        (
            folder=(driveLetter+"Misc/ModelLibraryNew/4.Verified/")
            makeDir folder
            exported=#()
            reducedObjNames=(for o in geometry where not o.isHidden and matchPattern o.name pattern:"*_REDUCED" collect o.name)
            reducedObjNames=sort reducedObjNames
            reducedObjs=(for n in reducedObjNames where getNodeByName n!=undefined collect getNodeByName n)
            existingF=getFiles (folder+"*_REDUCED.max")
            existingN=(for f in existingF collect (getFileNameFile f))

            if reducedObjs.count>0 then
            (
                c=querybox("Continue?")
                if c==true do
                (
                    print("Exporting "+reducedObjs.count as string)
                    setCommandPanelTaskMode #create;setWaitCursor()
                    for i=1 to reducedObjs.count do
                    (
                        windows.processPostedMessages()
                        if keyboard.escPressed do(exit)
                        p=(i*100.0/reducedObjs.count)
                        prg_LibraryFunctionsProgress.value=p
                        lbl_LibraryFunctionsProgress.text=(p as string+"% ("+i as string+" of "+reducedObjs.count as string+")")
                        if (findItem existingN reducedObjs[i].name)!=0 then
                        (
                            if chk_replaceExisting.state then
                            (
                                saveNodes reducedObjs[i] (folder+reducedObjs[i].name+".max") quiet:false
                                print(reducedObjs[i].name+" replaced")
                                appendIfUnique exported reducedObjs[i]
                            )
                        )
                        else
                        (
                            saveNodes reducedObjs[i] (folder+reducedObjs[i].name+".max") quiet:false
                            appendIfUnique exported reducedObjs[i]
                        )
                    )
                    lbl_LibraryFunctionsProgress.text="Finished"
                    print("Finished: "+exported.count as string)
                    setArrowCursor()
                )
            )
            else print("No reduced found")
        )
    )

    on btn_exportValidatedRaw pressed do
    (
        with undo off
        (
            folder=(driveLetter+"Misc/ModelLibraryNew/4.Verified/")
            makeDir folder
            exported=#()
            rawObjNames=(for o in geometry where not o.isHidden and matchPattern o.name pattern:"*_RAW" collect o.name)
            rawObjNames=sort rawObjNames
            rawObjs=(for n in rawObjNames where getNodeByName n!=undefined collect getNodeByName n)
            existingF=getFiles (folder+"*_RAW.max")
            existingN=(for f in existingF collect (getFileNameFile f))

            if rawObjs.count>0 then
            (
                c=querybox("Continue?")
                if c==true do
                (
                    print("Exporting "+rawObjs.count as string)
                    setCommandPanelTaskMode #create;setWaitCursor()
                    for i=1 to rawObjs.count do
                    (
                        windows.processPostedMessages()
                        if keyboard.escPressed do(exit)
                        p=(i*100.0/rawObjs.count)
                        prg_LibraryFunctionsProgress.value=p
                        lbl_LibraryFunctionsProgress.text=(p as string+"% ("+i as string+" of "+rawObjs.count as string+")")
                        if (findItem existingN rawObjs[i].name)!=0 then
                        (
                            if chk_replaceExisting.state then
                            (
                                saveNodes rawObjs[i] (folder+rawObjs[i].name+".max") quiet:false
                                print(rawObjs[i].name+" replaced")
                                appendIfUnique exported rawObjs[i]
                            )
                        )
                        else
                        (
                            saveNodes rawObjs[i] (folder+rawObjs[i].name+".max") quiet:false
                            appendIfUnique exported rawObjs[i]
                        )
                    )
                    lbl_LibraryFunctionsProgress.text="Finished"
                    print("Finished: "+exported.count as string)
                    setArrowCursor()
                )
            )
            else print("No raw found")
        )
    )
)

try(closeRolloutFloater meshLibraryFloater)catch()
meshLibraryFloater=newRolloutFloater "Mesh Library Tool" 300 640
addRollout UI_LibraryProgressBar meshLibraryFloater
addRollout UI_RenameToMeshData meshLibraryFloater
addRollout UI_ImportAndReplaceFromLibrary meshLibraryFloater
addRollout UI_SelectIdentical meshLibraryFloater
addRollout UI_SaveToTempLibrary meshLibraryFloater
addRollout UI_PerformActualReducing meshLibraryFloater
addRollout UI_CleanupReducedFiles meshLibraryFloater
addRollout UI_LibraryFunctions meshLibraryFloater
```

## Message 10

Your code looks fine and executes correctly, great job. Lets open the arena for you to fully prove yourself, here's the task (a larger and more complex example):
- Create a refactored version of the attached script, applying the same codingstyle to it in all aspects. Remember to be concistent and to avoid excessive comments, keep in mind that the linecount should be balanced properly with the "flow" of the code-avoid any excessive or unneccessary comments:

```maxscript
-- struct ObjData (obj, polycount, volume, ratio, logRatio, normalizedLogRatio)

-- fn getObjectsInThreshold minThreshold maxThreshold =
-- (
--     objs = #()

    -- for obj in geometry do
    -- (
    --     if classOf obj == Editable_mesh or classOf obj == Editable_poly then
    --     (
    --         polycount = polyOp.getNumFaces obj
    --         bbox = nodeLocalBoundingBox obj
    --         volume = abs ((bbox[2].x - bbox[1].x) * (bbox[2].y - bbox[1].y) * (bbox[2].z - bbox[1].z))

    --         if volume != 0 then
    --         (
    --             ratio = polycount / volume
    --             logRatio = log10 ratio

    --             append objs (ObjData obj:obj polycount:polycount volume:volume ratio:ratio logRatio:logRatio normalizedLogRatio:0)
    --         )
    --     )
    -- )

--     minLogRatio = 1e9
--     maxLogRatio = -1e9
--     for o in objs do
--     (
--         minLogRatio = amin #(minLogRatio, o.logRatio)
--         maxLogRatio = amax #(maxLogRatio, o.logRatio)
--     )

--     objsInThreshold = #()

--     for obj in objs do
--     (
--         normalizedLogRatio = ((obj.logRatio - minLogRatio) / (maxLogRatio - minLogRatio)) * 100
--         obj.normalizedLogRatio = normalizedLogRatio

--         if normalizedLogRatio >= minThreshold and normalizedLogRatio <= maxThreshold then
--             append objsInThreshold obj.obj
--     )

--     return objsInThreshold
-- )

-- rollout UI_objRatio "Object Ratio"
-- (
--     label minLabel "Min Threshold:"
--     slider minSlider "Slider" range:[0, 100, 30] ticks:10
--     label maxLabel "Max Threshold:"
--     slider maxSlider "Slider" range:[0, 100, 70] ticks:10
--     button applyButton "Apply"
--     label outputLabel "Output" align:#left

--     on minSlider changed val do
--     (
--         minLabel.text = "Min Threshold: " + val as string
--     )

--     on maxSlider changed val do
--     (
--         maxLabel.text = "Max Threshold: " + val as string
--     )

--     on applyButton pressed do
--     (
--         objs = getObjectsInThreshold minSlider.value maxSlider.value

--         outputLabel.text = "Selected objects: " + objs.count as string

--         clearSelection()
--         select(objs)
--     )
-- )

-- try(closeRolloutFloater objRatio_floater) catch()
-- objRatio_floater = newRolloutFloater "Object Ratio" 300 300
-- addRollout UI_objRatio objRatio_floater
-- struct ObjData (obj, polycount, volume, ratio, logRatio, normalizedLogRatio)

fn getObjectsInThreshold minThreshold maxThreshold =
(
    objs = #()

    for obj in geometry do
    (
        if classOf obj == Editable_mesh or classOf obj == Editable_poly then
        (
            polycount = polyOp.getNumFaces obj
            bbox = nodeLocalBoundingBox obj
            volume = abs ((bbox[2].x - bbox[1].x) * (bbox[2].y - bbox[1].y) * (bbox[2].z - bbox[1].z))

            if volume != 0 then
            (
                ratio = polycount / volume
                logRatio = log10 ratio

                append objs (ObjData obj:obj polycount:polycount volume:volume ratio:ratio logRatio:logRatio normalizedLogRatio:0)
            )
        )
    )

    minLogRatio = 1e9
    maxLogRatio = -1e9
    for o in objs do
    (
        minLogRatio = amin #(minLogRatio, o.logRatio)
        maxLogRatio = amax #(maxLogRatio, o.logRatio)
    )

    objsInThreshold = #()

    for obj in objs do
    (
        normalizedLogRatio = ((obj.logRatio - minLogRatio) / (maxLogRatio - minLogRatio)) * 100
        obj.normalizedLogRatio = normalizedLogRatio

        if normalizedLogRatio >= minThreshold and normalizedLogRatio <= maxThreshold then
            append objsInThreshold obj.obj
    )

    return objsInThreshold
)

rollout UI_objRatio "Object Ratio"
(
    label minLabel "Min Threshold:"
    slider minSlider "Slider" range:[0, 100, 30] ticks:10
    label maxLabel "Max Threshold:"
    slider maxSlider "Slider" range:[0, 100, 70] ticks:10
    button applyButton "Apply"
    label outputLabel "Output" align:#left

    on minSlider changed val do
    (
        minLabel.text = "Min Threshold: " + val as string
    )

    on maxSlider changed val do
    (
        maxLabel.text = "Max Threshold: " + val as string
    )

    on applyButton pressed do
    (
        objs = getObjectsInThreshold minSlider.value maxSlider.value

        outputLabel.text = "Selected objects: " + objs.count as string

        clearSelection()
        select(objs)
    )
)

try(closeRolloutFloater objRatio_floater) catch()
objRatio_floater = newRolloutFloater "Object Ratio" 300 300
addRollout UI_objRatio objRatio_floater

-- struct ObjData (sceneObj, polycount, volume, polyToVolRatio, logRatio, normalizedLogRatio)
struct ObjDataStruct (
    sceneObj,
    polycount,
    volume,
    polyToVolRatio,
    logRatio,
    normalizedLogRatio
)


fn calculateVolume meshObj =
(
    bbox = nodeLocalBoundingBox meshObj
    volume = abs ((bbox[2].x - bbox[1].x) * (bbox[2].y - bbox[1].y) * (bbox[2].z - bbox[1].z))
    return volume
)

fn calculateRatio objData weightSize weightVerts =
(
    volume = calculateVolume objData.sceneObj
    polycount = polyOp.getNumFaces objData.sceneObj

    -- Avoid division by zero
    if volume == 0 then
        volume = 0.0001

    ratio = polycount / volume
    logRatio = log10 ratio

    -- Adjust the weights using a power function for non-linear progression
    adjustedWeightSize = weightSize^2
    adjustedWeightVerts = weightVerts^2

    weightedRatio = logRatio * (adjustedWeightSize + adjustedWeightVerts)

    objData.logRatio = weightedRatio
    objData.polyToVolRatio = ratio
    print((#(ratio, weightedRatio)) as string)
    return #(ratio, weightedRatio)
)


fn normalizeRatios objDataArray =
(
    local normalizedObjDataArray = deepCopy objDataArray

    local minLogRatio = 1e9
    local maxLogRatio = -1e9

    for o in normalizedObjDataArray do
    (
        minLogRatio = amin #(minLogRatio, o.logRatio)
        maxLogRatio = amax #(maxLogRatio, o.logRatio)
    )

    for o in normalizedObjDataArray do
    (
        o.normalizedLogRatio = ((o.logRatio - minLogRatio) / (maxLogRatio - minLogRatio)) * 100
    )

    return normalizedObjDataArray
)

fn getObjectsInThreshold minThreshold maxThreshold weightSize weightVerts =
(
    local objDataArray = #()

    for sceneObj in objects do
    (
        local objDataInstance = ObjDataStruct sceneObj:sceneObj polycount:undefined volume:undefined polyToVolRatio:undefined logRatio:undefined normalizedLogRatio:undefined
        local ratio, weightedRatio = calculateRatio objDataInstance weightSize weightVerts

        append objDataArray objDataInstance
    )

    local normalizedObjDataArray = normalizeRatios objDataArray

    local objsInThreshold = for objData in normalizedObjDataArray where (objData.normalizedLogRatio >= minThreshold and objData.normalizedLogRatio <= maxThreshold) collect objData

    return objsInThreshold
)

fn calculateStatistics objDataArray minThreshold maxThreshold =
(
    countLowerThreshold = 0
    countBetweenThresholds = 0
    countAboveThreshold = 0

    for objData in objDataArray do
    (
        if objData.normalizedLogRatio < minThreshold then
            countLowerThreshold += 1
        else if objData.normalizedLogRatio > maxThreshold then
            countAboveThreshold += 1
        else
            countBetweenThresholds += 1
    )

    percentLowerThreshold = (countLowerThreshold / objDataArray.count) * 100
    percentBetweenThresholds = (countBetweenThresholds / objDataArray.count) * 100
    percentAboveThreshold = (countAboveThreshold / objDataArray.count) * 100

    return #(percentLowerThreshold, percentBetweenThresholds, percentAboveThreshold)
)




rollout UI_densityScore "Density Score Tool"
(
    label lblLowPolyObj "Low-poly object: None"
    label lblHighPolyObj "High-poly object: None"

    spinner spnWeightSize "Size Weight: " range:[0,100,50] scale:1 type:#integer
    spinner spnWeightVerts "Verts Weight: " range:[0,100,50] scale:1 type:#integer

    slider minSlider "Min Threshold:" range:[0, 100, 30] ticks:10
    slider maxSlider "Max Threshold:" range:[0, 100, 70] ticks:10

    button btnCalculate "Calculate Scores"
    button btnDelete "Delete Objects"

    on spnWeightSize changed val do
    (
        spnWeightVerts.value = 100 - val
    )

    on spnWeightVerts changed val do
    (
        spnWeightSize.value = 100 - val
    )

    on btnCalculate pressed do
    (
        -- Calculate density scores for objects in the scene.
        objsInThreshold = getObjectsInThreshold minSlider.value maxSlider.value spnWeightSize.value spnWeightVerts.value
        -- objsInThreshold = getObjectsInThreshold 30 70 50 50
        sceneObjsInThreshold = for objData in objsInThreshold collect objData.sceneObj
        clearSelection()
        select(sceneObjs)

        -- Generate statistics.
        stats = calculateStatistics objsInThreshold minSlider.value maxSlider.value
        -- stats = calculateStatistics objsInThreshold

        -- -- Print details about the operation.
        format "\nCalculated Scores:\n"
        format "Number of objects:  % \n" (objsInThreshold.count as string)
        format "Size weight:  % \n" (spnWeightSize.value as string)
        format "Threshold: % to % \n" (minSlider.value as string) (maxSlider.value as string)
        -- print("stats[1]: " + stats[1] as string)
        format "Percent of objects closer to the lower threshold: %\n" (stats[1] as float)
        format "Percent of objects closer to the upper threshold: %\n" (stats[2] as float)
        format "Percent of objects in the middle of the thresholds: %\n" (stats[3] as float)

    )

    -- -- Calculate density scores for objects in the scene.
    -- local objsInThreshold = getObjectsInThreshold minSlider.value maxSlider.value spnWeightSize.value spnWeightVerts.value
    -- local sceneObjsInThreshold = for objData in objsInThreshold collect objData.sceneObj

    -- clearSelection()
    -- select(sceneObjsInThreshold)

    -- -- Generate statistics.
    -- local stats = calculateStatistics objsInThreshold

    -- -- Print details about the operation.
    -- format "Calculated Scores:\n"
    -- format "Number of objects: %\n" objsInThreshold.count as integer
    -- format "Size weight: %\n" spnWeightSize.value as integer
    -- format "Threshold: % to %\n" minSlider.value as float maxSlider.value as float
    -- format "Percent of objects in the lower third: %.2f%%\n" stats[1] as float
    -- format "Percent of objects in the middle third: %.2f%%\n" stats[2] as float
    -- format "Percent of objects in the upper third: %.2f%%\n" stats[3] as float
    on btnDelete pressed do
    (
        -- Delete objects based on density scores.
        -- This needs to be implemented.

        -- Print details about the operation.
        format "Delete operation:\n"
        -- Replace with actual values when implemented.
        format "Number of objects deleted: %\n" objsInThreshold.count
    )
)

-- createDialog UI_densityScore


--Close the window if it exists
try(closeRolloutFloater densityScoreUIFloater)catch()

--Create the floater window
densityScoreUIFloater = newRolloutFloater "densityScore" 300 350
addRollout UI_densityScore densityScoreUIFloater

--
-- createDialog UI_densityScore



-- Calculated Scores:
-- Please include more statistics about the result, such as statistics with regard to the result? Example:
-- - "Input: Match ratio X and X"
-- - "Totals: (%23 7392/32100 of the objects in the scene matched specified threshold settings)"
-- - "        (47% of these are closer to the upper threshold)"
-- - "        (3% of these are closer to the lower threshold)"
-- - "        (27% of these are closer to the average of the upper and lower threshholds"
-- etc
-- Number of objects: 7392/32100 (%23)

-- -- Information (input/query)
-- Search:

-- Information (outut/result:
-- - Totals: (%23) 7392/32100 of the objects in the scene matched specified threshold settings



-- Number of objects: Represents



-- Number of objects: 7392
-- Size weight: 50
-- Vertex weight: 50
-- Threshold: 71.85 to 88.89

```

## Message 11

**Aligning code style**

I’m gathering info on the user's request for a unified coding style: minimal commentary, consistent formatting, and balanced line count. I’ll remove unnecessary top comments to streamline the code.

**Maintaining code clarity**

I’m focusing on minimal top-level commentary, ensuring consistent formatting, and maintaining short, insightful comments to uphold clarity without excessive verbosity.

**Ensuring confidence throughout**

Maintaining consistency with `undo` contexts and `EnableSceneRedraw` is strategic for UI tools, even without significant scene changes. This approach nurtures confidence in reliable and adaptable code.

**Packaging functions effectively**

I’m organizing functions by grouping them with `-- Function: ...` headers. For rollouts, considering the options `UI_objRatio` and `UI_densityScore` to finalize the functionality.

**Refining the code**

I’m streamlining the code by removing repeated struct definitions and preserving the final version to ensure minimality and maintain the coding style of minimal comments and clear function grouping.

**Refining the script**

I’m focusing on creating a concise, well-structured script with minimal inline comments, consistent function names, and balanced line count to enhance readability and maintain coding style.

## Message 12

```maxscript
-- Struct: Object Data
-- =======================================================
struct ObjDataStruct (
    sceneObj,
    polycount,
    volume,
    polyToVolRatio,
    logRatio,
    normalizedLogRatio
)

-- Function: Calculate Volume
-- =======================================================
fn calculateVolume meshObj =
(
    bbox = nodeLocalBoundingBox meshObj
    abs ((bbox[2].x - bbox[1].x) * (bbox[2].y - bbox[1].y) * (bbox[2].z - bbox[1].z))
)

-- Function: Calculate Ratio (Poly/Vol)
-- =======================================================
fn calculateRatio objData weightSize weightVerts =
(
    objData.volume = calculateVolume objData.sceneObj
    objData.polycount = polyOp.getNumFaces objData.sceneObj
    if objData.volume == 0 then objData.volume = 0.0001

    ratio = objData.polycount / objData.volume
    logRatio = log10 ratio

    objData.polyToVolRatio = ratio
    objData.logRatio = logRatio * ((weightSize^2) + (weightVerts^2))
    #(objData.polyToVolRatio, objData.logRatio)
)

-- Function: Normalize Ratios
-- =======================================================
fn normalizeRatios objDataArray =
(
    minLog = 1e9
    maxLog = -1e9

    for o in objDataArray do
    (
        minLog = amin #(minLog, o.logRatio)
        maxLog = amax #(maxLog, o.logRatio)
    )

    for o in objDataArray do
        o.normalizedLogRatio = ((o.logRatio - minLog) / (maxLog - minLog)) * 100

    objDataArray
)

-- Function: Get Objects In Threshold
-- =======================================================
fn getObjectsInThreshold minThreshold maxThreshold weightSize weightVerts =
(
    objDataArray = #()
    for scnObj in objects do
    (
        data = ObjDataStruct sceneObj:scnObj
        calculateRatio data weightSize weightVerts
        append objDataArray data
    )
    normalizeRatios objDataArray
    for d in objDataArray where d.normalizedLogRatio >= minThreshold and d.normalizedLogRatio <= maxThreshold collect d
)

-- Function: Calculate Statistics
-- =======================================================
fn calculateStatistics objDataArray minT maxT =
(
    low=0; mid=0; high=0
    for d in objDataArray do
    (
        if d.normalizedLogRatio < minT then low+=1
        else if d.normalizedLogRatio > maxT then high+=1
        else mid+=1
    )
    #( (low*100.0/objDataArray.count), (mid*100.0/objDataArray.count), (high*100.0/objDataArray.count) )
)

-- Rollout: Object Ratio
-- =======================================================
rollout UI_objRatio "Object Ratio"
(
    label minLabel "Min Threshold:"
    slider minSlider "" range:[0,100,30]
    label maxLabel "Max Threshold:"
    slider maxSlider "" range:[0,100,70]
    button applyButton "Apply"
    label outputLabel "Output" align:#left

    on minSlider changed val do minLabel.text = "Min Threshold: " + val as string
    on maxSlider changed val do maxLabel.text = "Max Threshold: " + val as string

    on applyButton pressed do
    (
        objs = getObjectsInThreshold minSlider.value maxSlider.value 50 50
        outputLabel.text = "Selected objects: " + objs.count as string
        clearSelection()
        select (for d in objs collect d.sceneObj)
    )
)

try(closeRolloutFloater objRatio_floater) catch()
objRatio_floater = newRolloutFloater "Object Ratio" 300 300
addRollout UI_objRatio objRatio_floater

-- Rollout: Density Score Tool
-- =======================================================
rollout UI_densityScore "Density Score Tool"
(
    label lblLowPolyObj "Low-poly object: None"
    label lblHighPolyObj "High-poly object: None"
    spinner spnWeightSize "Size Weight: " range:[0,100,50]
    spinner spnWeightVerts "Verts Weight: " range:[0,100,50]
    slider minSlider "Min Threshold:" range:[0,100,30]
    slider maxSlider "Max Threshold:" range:[0,100,70]
    button btnCalculate "Calculate Scores"
    button btnDelete "Delete Objects"
    local objsInThreshold = #()

    on spnWeightSize changed val do spnWeightVerts.value = 100 - val
    on spnWeightVerts changed val do spnWeightSize.value = 100 - val

    on btnCalculate pressed do
    (
        objsInThreshold = getObjectsInThreshold minSlider.value maxSlider.value spnWeightSize.value spnWeightVerts.value
        sceneObjs = for d in objsInThreshold collect d.sceneObj
        clearSelection()
        select sceneObjs
        stats = calculateStatistics objsInThreshold minSlider.value maxSlider.value

        format "\nCalculated Scores:\n"
        format "Number of objects: %\n" objsInThreshold.count
        format "Size weight: %\n" spnWeightSize.value
        format "Threshold: % to %\n" minSlider.value maxSlider.value
        format "Percent lower: %\n" stats[1]
        format "Percent between: %\n" stats[2]
        format "Percent upper: %\n" stats[3]
    )

    on btnDelete pressed do
    (
        format "Delete operation:\n"
        format "Number of objects deleted: %\n" objsInThreshold.count
        if objsInThreshold.count > 0 do delete (for d in objsInThreshold collect d.sceneObj)
    )
)

try(closeRolloutFloater densityScoreUIFloater) catch()
densityScoreUIFloater = newRolloutFloater "densityScore" 300 350
addRollout UI_densityScore densityScoreUIFloater
```

## Message 13

Lets open the arena for you to really prove yourself, your next tasks will concist of multiple steps to ensure iterative concistency, here's the first step:

- Prove that you understand the following inquiry: 

  - `I have a scene in 3dsmax which has thousands of objects of different sizes, all from small screws to larger structures. In order to make the scene work faster (since it currently has millions of polys) I'm looking for a way to identify objects by ratio in size vs density. I will use this ratio to instruct a threshold for a utility I'll create that assigns wirecolors as a "color-heatmap" based on the ratio. This approach aims to classify "groupings" of objects based on the ratio. The ratio for each object should be high if the object is small in size but high in it's vertcount, if its small but low in vertcount it should e.g. not be considered to have a high ratio. The point of this is to identify objects which are way too dense compared to its size (relative to the other objects in the scene).`

## Message 14

You need to analyze each object in terms of its geometric complexity relative to its physical size, then use that metric to highlight objects that are disproportionately dense. Specifically:

1. **Setup**:  
   The scene contains thousands of objects, ranging from tiny screws to large structural elements.

2. **Problem**:  
   With so many polygons (millions overall), performance suffers. The goal is to identify which objects contribute most to this density problem, not just by their total polygon count, but by how dense they are relative to their size.

3. **Ratio Concept**:  
   The ratio you want can be thought of as “polygon density.” A straightforward way is to compare the object’s polygon count against its bounding box volume or a similar measure of size.  
   - **High ratio**: Small objects that have a lot of polygons (i.e., very dense geometry).  
   - **Low ratio**: Large objects or smaller objects with fewer polygons, making them less dense and thus less problematic for performance.

4. **Desired Outcome**:  
   By computing this ratio for every object, you can decide on thresholds that classify objects into categories. Once categorized, you can apply a color-coded “heatmap” to the viewport via their wirecolor. For instance:  
   - Red = High-density objects (small but very high polygon count).  
   - Yellow = Medium-density objects.  
   - Green = Low-density objects.

5. **Purpose**:  
   The main intention is to quickly visualize which objects are inefficiently dense and may need optimization (e.g., retopology, instancing, or replacement with simpler geometry) to improve the scene’s performance.

In essence, the inquiry is about creating a density metric (ratio of polygon count to size), using that metric to rank objects, and then visually encoding these rankings in the scene for easier identification and subsequent optimization.
