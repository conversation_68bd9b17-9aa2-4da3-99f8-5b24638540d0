# Default Output Directory Config

Conversation ID: 68026e49-e3bc-8008-9251-bf54aac8ac38

## Message 1

Update this with the option to specify default output directory:



    ```python

    #!/usr/bin/env python3



    # =============================================================================

    # SECTION 1: Core Imports & Environment Setup

    # =============================================================================

    import asyncio

    import json

    import os

    import sys

    import argparse

    from datetime import datetime

    from typing import Dict, List, Any, Optional, Callable



    # External Dependencies

    from pydantic import BaseModel, Field  # For data validation and structuring

    import litellm                         # Abstraction layer for LLM API calls



    # Internal Dependencies

    from templates.lvl1.templates_lvl1_md_catalog_generator import (

        load_catalog,               # Loads the template catalog structure

        get_sequence,               # Retrieves a specific sequence of templates

        get_all_sequences,          # Lists all available sequence IDs

        get_system_instruction,     # Extracts the system instruction from a template part

        regenerate_catalog          # Regenerates the catalog if needed

    )





    # =============================================================================

    # SECTION 2: Centralized Configuration Management

    # =============================================================================

    class Config:

        """

        Manages LLM provider/model selection, parameters, and LiteLLM settings.

        Designed for clarity and ease of modification via centralized definitions.

        """



        # Environment Setup

        @staticmethod

        def _ensure_utf8_encoding():

            """Ensures UTF-8 output encoding for terminals to prevent character errors."""

            for stream in (sys.stdout, sys.stderr):

                if hasattr(stream, "reconfigure"):

                    try:

                        stream.reconfigure(encoding="utf-8", errors="replace")

                    except Exception as e:

                        pass



        # Provider Selection

        PROVIDER_ANTHROPIC = "anthropic"

        PROVIDER_DEEPSEEK  = "deepseek"

        PROVIDER_GOOGLE    = "google"

        PROVIDER_OPENAI    = "openai"



        # Default Provider Selection (last assignment determines the active default)

        DEFAULT_PROVIDER = PROVIDER_DEEPSEEK

        DEFAULT_PROVIDER = PROVIDER_ANTHROPIC

        DEFAULT_PROVIDER = PROVIDER_GOOGLE

        DEFAULT_PROVIDER = PROVIDER_OPENAI



        # Provider-Specific Model Selection (last entry becomes provider's default).

        PROVIDER_MODELS = {

            PROVIDER_ANTHROPIC: {

                "model_name": "claude-3-opus-20240229",

                "model_name": "claude-3-sonnet-20240229",

                "model_name": "claude-3-haiku-20240307",

                "model_name": "openrouter/anthropic/claude-3.7-sonnet:beta",

            },

            PROVIDER_DEEPSEEK: {

                "model_name": "deepseek/deepseek-reasoner",

                "model_name": "deepseek/deepseek-coder",

                "model_name": "deepseek/deepseek-chat",

            },

            PROVIDER_GOOGLE: {

                "model_name": "gemini/gemini-1.5-flash-latest",

                "model_name": "gemini/gemini-2.0-flash",

                "model_name": "gemini/gemini-2.5-pro-preview-03-25",

            },

            PROVIDER_OPENAI: {

                "model_name": "gpt-4o",

                "model_name": "gpt-4o-mini",

                "model_name": "gpt-3.5-turbo",

                "model_name": "gpt-3.5-turbo-instruct",

                "model_name": "gpt-3.5-turbo-1106",

                "model_name": "o3-mini",

                "model_name": "gpt-4.1",

            },

        }





        # Model Registry (maps user-friendly names to actual LiteLLM model IDs)

        MODEL_REGISTRY = {

            # OpenAI

            "gpt-4o": "gpt-4o",

            "gpt-4-turbo": "gpt-4-turbo",

            "gpt-4": "gpt-4",

            "gpt-3.5-turbo": "gpt-3.5-turbo",

            "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",

            # Anthropic

            "claude-3-opus": "anthropic/claude-3-opus-20240229",

            "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",

            "claude-3-haiku": "anthropic/claude-3-haiku-20240307",

            "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",

            # Google

            "gemini-pro": "gemini/gemini-1.5-pro",

            "gemini-flash": "gemini/gemini-1.5-flash-latest",

            "gemini-2-flash": "gemini/gemini-2.0-flash",

            "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",

            # Deepseek

            "deepseek-reasoner": "deepseek/deepseek-reasoner",

            "deepseek-coder": "deepseek/deepseek-coder",

            "deepseek-chat": "deepseek/deepseek-chat",

        }



        # Model Selection Logic

        @classmethod

        def get_default_model(cls, provider=None):

            """Get the default model for a provider based on configuration."""

            provider = provider or cls.DEFAULT_PROVIDER

            provider_config = cls.PROVIDER_MODELS.get(provider, {})



            if "model_name" in provider_config:

                return provider_config["model_name"]



            # Fallbacks for each provider if no model is explicitly set

            fallbacks = {

                cls.PROVIDER_OPENAI: "gpt-3.5-turbo",

                cls.PROVIDER_ANTHROPIC: "anthropic/claude-3-haiku-20240307",

                cls.PROVIDER_GOOGLE: "gemini/gemini-1.5-flash-latest",

                cls.PROVIDER_DEEPSEEK: "deepseek/deepseek-chat"

            }



            # Ultimate fallback

            return fallbacks.get(provider, "gpt-3.5-turbo")



        # Model Parameter Resolution ---

        @classmethod

        def get_model_params(cls, model_name=None, provider=None):

            """Resolves user-friendly model name to its LiteLLM ID via MODEL_REGISTRY."""

            provider = provider or cls.DEFAULT_PROVIDER

            model_name = model_name or cls.get_default_model(provider)



            provider_defaults = cls.PROVIDER_MODELS.get(provider, {})

            params = {k: v for k, v in provider_defaults.items() if k != "model_name"}



            actual_model_id = cls.MODEL_REGISTRY.get(model_name, model_name)

            return {"model": actual_model_id, **params}



        # Available Models Listing

        @classmethod

        def get_available_models(cls):

            """Returns a structured list of registered models, grouped by provider."""

            result = {}



            for provider, config in cls.PROVIDER_MODELS.items():

                # Get the selected model for this provider

                selected_model = cls.get_default_model(provider)



                # Get all models from the registry that belong to this provider

                provider_models = []

                for name, model_id in cls.MODEL_REGISTRY.items():

                    if (provider == cls.PROVIDER_OPENAI and not '/' in model_id) or \

                       (provider != cls.PROVIDER_OPENAI and model_id.startswith(f"{provider}/")):

                        provider_models.append({

                            "name": name,

                            "model_id": model_id,

                            "is_default": (name == selected_model or model_id == selected_model)

                        })



                result[provider] = provider_models



            return result



        # LiteLLM Initialization

        @classmethod

        def configure_litellm(cls):

            """Configures global LiteLLM settings and terminal encoding."""

            litellm.drop_params = True     # Prevent errors from unsupported parameters

            litellm.num_retries = 3        # Retry failed API calls

            litellm.request_timeout = 120  # Set API request timeout

            litellm.set_verbose = False    # Reduce LiteLLM's own console output

            litellm.callbacks = []         # Disable default callbacks unless explicitly configured



            # Set terminal encoding

            cls._ensure_utf8_encoding()



            # Additional configuration can be added here

            print(f"\n[Config] LiteLLM Initialized:")

            print(f"- Default Provider: {cls.DEFAULT_PROVIDER}")

            print(f"- Default Model: {cls.get_default_model()}")

            print(f"- Retries: {litellm.num_retries}, Timeout: {litellm.request_timeout}s")



    ```


## Message 2

**Setting an output directory**

I'm thinking through how to add an argument in a Python script to set a default output directory. Considering an `argparse` option or a new config field for flexibility.

**Setting up parsing logic**

I'm outlining how to integrate argument parsing in a Python script, specifically setting a default output directory with `argparse`. This involves adding a `DEFAULT_OUTPUT_DIR` to a config class and parsing the `--output-dir` argument.

## Message 3

Below is one example of how you might introduce a configurable default output directory. The key additions are:

1. **A new class attribute** `DEFAULT_OUTPUT_DIR` in `Config`.
2. **A helper method** `set_default_output_dir` to set (or override) it.
3. **Command-line argument** `--output-dir` (or `-o`) to allow users to specify the directory at runtime.

Everything else in your script remains unchanged except for the added logic near the bottom for argument parsing and updating the config.

```python
#!/usr/bin/env python3

# =============================================================================
# SECTION 1: Core Imports & Environment Setup
# =============================================================================
import asyncio
import json
import os
import sys
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

# External Dependencies
from pydantic import BaseModel, Field  # For data validation and structuring
import litellm                         # Abstraction layer for LLM API calls

# Internal Dependencies
from templates.lvl1.templates_lvl1_md_catalog_generator import (
    load_catalog,               # Loads the template catalog structure
    get_sequence,               # Retrieves a specific sequence of templates
    get_all_sequences,          # Lists all available sequence IDs
    get_system_instruction,     # Extracts the system instruction from a template part
    regenerate_catalog          # Regenerates the catalog if needed
)


# =============================================================================
# SECTION 2: Centralized Configuration Management
# =============================================================================
class Config:
    """
    Manages LLM provider/model selection, parameters, and LiteLLM settings.
    Designed for clarity and ease of modification via centralized definitions.
    """

    # Environment Setup
    @staticmethod
    def _ensure_utf8_encoding():
        """Ensures UTF-8 output encoding for terminals to prevent character errors."""
        for stream in (sys.stdout, sys.stderr):
            if hasattr(stream, "reconfigure"):
                try:
                    stream.reconfigure(encoding="utf-8", errors="replace")
                except Exception:
                    pass

    # Provider Selection
    PROVIDER_ANTHROPIC = "anthropic"
    PROVIDER_DEEPSEEK  = "deepseek"
    PROVIDER_GOOGLE    = "google"
    PROVIDER_OPENAI    = "openai"

    # Default Provider Selection (last assignment determines the active default)
    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
    DEFAULT_PROVIDER = PROVIDER_GOOGLE
    DEFAULT_PROVIDER = PROVIDER_OPENAI

    # Provider-Specific Model Selection (last entry becomes provider's default).
    PROVIDER_MODELS = {
        PROVIDER_ANTHROPIC: {
            "model_name": "claude-3-opus-20240229",
            "model_name": "claude-3-sonnet-20240229",
            "model_name": "claude-3-haiku-20240307",
            "model_name": "openrouter/anthropic/claude-3.7-sonnet:beta",
        },
        PROVIDER_DEEPSEEK: {
            "model_name": "deepseek/deepseek-reasoner",
            "model_name": "deepseek/deepseek-coder",
            "model_name": "deepseek/deepseek-chat",
        },
        PROVIDER_GOOGLE: {
            "model_name": "gemini/gemini-1.5-flash-latest",
            "model_name": "gemini/gemini-2.0-flash",
            "model_name": "gemini/gemini-2.5-pro-preview-03-25",
        },
        PROVIDER_OPENAI: {
            "model_name": "gpt-4o",
            "model_name": "gpt-4o-mini",
            "model_name": "gpt-3.5-turbo",
            "model_name": "gpt-3.5-turbo-instruct",
            "model_name": "gpt-3.5-turbo-1106",
            "model_name": "o3-mini",
            "model_name": "gpt-4.1",
        },
    }

    # Model Registry (maps user-friendly names to actual LiteLLM model IDs)
    MODEL_REGISTRY = {
        # OpenAI
        "gpt-4o": "gpt-4o",
        "gpt-4-turbo": "gpt-4-turbo",
        "gpt-4": "gpt-4",
        "gpt-3.5-turbo": "gpt-3.5-turbo",
        "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",
        # Anthropic
        "claude-3-opus": "anthropic/claude-3-opus-20240229",
        "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
        "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
        "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",
        # Google
        "gemini-pro": "gemini/gemini-1.5-pro",
        "gemini-flash": "gemini/gemini-1.5-flash-latest",
        "gemini-2-flash": "gemini/gemini-2.0-flash",
        "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",
        # Deepseek
        "deepseek-reasoner": "deepseek/deepseek-reasoner",
        "deepseek-coder": "deepseek/deepseek-coder",
        "deepseek-chat": "deepseek/deepseek-chat",
    }

    # ---------------------------
    #  ( New ) Default Output Dir
    # ---------------------------
    DEFAULT_OUTPUT_DIR = os.getenv("DEFAULT_OUTPUT_DIR", "./output")

    @classmethod
    def set_default_output_dir(cls, directory: str):
        """
        Allows overriding the default output directory programmatically or via CLI.
        """
        cls.DEFAULT_OUTPUT_DIR = directory

    # ---------------------------
    #   Model Selection Logic
    # ---------------------------
    @classmethod
    def get_default_model(cls, provider=None):
        """Get the default model for a provider based on configuration."""
        provider = provider or cls.DEFAULT_PROVIDER
        provider_config = cls.PROVIDER_MODELS.get(provider, {})

        if "model_name" in provider_config:
            return provider_config["model_name"]

        # Fallbacks for each provider if no model is explicitly set
        fallbacks = {
            cls.PROVIDER_OPENAI: "gpt-3.5-turbo",
            cls.PROVIDER_ANTHROPIC: "anthropic/claude-3-haiku-20240307",
            cls.PROVIDER_GOOGLE: "gemini/gemini-1.5-flash-latest",
            cls.PROVIDER_DEEPSEEK: "deepseek/deepseek-chat"
        }

        # Ultimate fallback
        return fallbacks.get(provider, "gpt-3.5-turbo")

    @classmethod
    def get_model_params(cls, model_name=None, provider=None):
        """
        Resolves user-friendly model name to its LiteLLM ID via MODEL_REGISTRY.
        """
        provider = provider or cls.DEFAULT_PROVIDER
        model_name = model_name or cls.get_default_model(provider)

        provider_defaults = cls.PROVIDER_MODELS.get(provider, {})
        params = {k: v for k, v in provider_defaults.items() if k != "model_name"}

        actual_model_id = cls.MODEL_REGISTRY.get(model_name, model_name)
        return {"model": actual_model_id, **params}

    @classmethod
    def get_available_models(cls):
        """Returns a structured list of registered models, grouped by provider."""
        result = {}

        for provider, config in cls.PROVIDER_MODELS.items():
            # Get the selected model for this provider
            selected_model = cls.get_default_model(provider)

            # Get all models from the registry that belong to this provider
            provider_models = []
            for name, model_id in cls.MODEL_REGISTRY.items():
                if (provider == cls.PROVIDER_OPENAI and not '/' in model_id) or \
                   (provider != cls.PROVIDER_OPENAI and model_id.startswith(f"{provider}/")):
                    provider_models.append({
                        "name": name,
                        "model_id": model_id,
                        "is_default": (name == selected_model or model_id == selected_model)
                    })

            result[provider] = provider_models

        return result

    # ---------------------------
    #   LiteLLM Initialization
    # ---------------------------
    @classmethod
    def configure_litellm(cls):
        """Configures global LiteLLM settings and terminal encoding."""
        litellm.drop_params = True     # Prevent errors from unsupported parameters
        litellm.num_retries = 3        # Retry failed API calls
        litellm.request_timeout = 120  # Set API request timeout
        litellm.set_verbose = False    # Reduce LiteLLM's own console output
        litellm.callbacks = []         # Disable default callbacks unless explicitly configured

        # Set terminal encoding
        cls._ensure_utf8_encoding()

        # Additional configuration can be added here
        print(f"\n[Config] LiteLLM Initialized:")
        print(f"- Default Provider: {cls.DEFAULT_PROVIDER}")
        print(f"- Default Model: {cls.get_default_model()}")
        print(f"- Retries: {litellm.num_retries}, Timeout: {litellm.request_timeout}s")
        print(f"- Default Output Directory: {cls.DEFAULT_OUTPUT_DIR}")


# =============================================================================
# SECTION 3: Script Entry Point & Argument Parsing
# =============================================================================
def parse_args():
    parser = argparse.ArgumentParser(description="Example script with output-dir option.")
    parser.add_argument(
        "--output-dir", "-o",
        help="Specify default output directory for any generated files.",
        default=Config.DEFAULT_OUTPUT_DIR
    )
    parser.add_argument(
        "--provider",
        help="Override the default provider.",
        default=Config.DEFAULT_PROVIDER
    )
    parser.add_argument(
        "--model",
        help="Override the default model name (if desired).",
        default=None
    )
    parser.add_argument(
        "--list-models",
        action="store_true",
        help="List available models (grouped by provider) and exit."
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()

    # Update the default output directory if specified
    if args.output_dir:
        Config.set_default_output_dir(args.output_dir)

    # Optionally override the default provider
    if args.provider and args.provider != Config.DEFAULT_PROVIDER:
        Config.DEFAULT_PROVIDER = args.provider

    # Optionally override the default model
    if args.model:
        # This doesn't permanently update get_default_model but you could pass it along
        pass

    # Configure LiteLLM (prints summary info, including the new output directory)
    Config.configure_litellm()

    # If the user just wants to list models, show them and exit
    if args.list_models:
        all_models = Config.get_available_models()
        print("\nAvailable Models by Provider:")
        for provider, model_list in all_models.items():
            print(f"  {provider}:")
            for m in model_list:
                marker = "[DEFAULT]" if m["is_default"] else ""
                print(f"    - {m['name']} -> {m['model_id']} {marker}")
        sys.exit(0)

    # At this point, you can proceed with script logic, 
    # and if you need to write output, use Config.DEFAULT_OUTPUT_DIR
    # For example:
    os.makedirs(Config.DEFAULT_OUTPUT_DIR, exist_ok=True)
    # [ Your logic here ]
```

### How it works

1. **`DEFAULT_OUTPUT_DIR`**: Declares `Config.DEFAULT_OUTPUT_DIR`, which you can set via environment variable or let it default to `./output`.
2. **Argument Parsing**: The new `--output-dir` argument lets users override `Config.DEFAULT_OUTPUT_DIR` on the command line (`python script.py --output-dir=/my/custom/path`).
3. **Access in Your Code**: Anywhere else in your script, you can do `os.makedirs(Config.DEFAULT_OUTPUT_DIR, exist_ok=True)` to ensure the directory is present, or otherwise reference `Config.DEFAULT_OUTPUT_DIR` to save files.

## Message 4

instead of using `os.getenv("DEFAULT_OUTPUT_DIR", "./output")`, please make it output to a subfolder within the directory of the script itself

## Message 5

Below is an updated version that sets the default output directory to a subfolder (`./output`) relative to where the script itself lives, instead of using an environment variable. Everything else remains essentially the same, but note the key change in how `DEFAULT_OUTPUT_DIR` is derived:

```python
#!/usr/bin/env python3

# =============================================================================
# SECTION 1: Core Imports & Environment Setup
# =============================================================================
import asyncio
import json
import os
import sys
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

# External Dependencies
from pydantic import BaseModel, Field  # For data validation and structuring
import litellm                         # Abstraction layer for LLM API calls

# Internal Dependencies
from templates.lvl1.templates_lvl1_md_catalog_generator import (
    load_catalog,               # Loads the template catalog structure
    get_sequence,               # Retrieves a specific sequence of templates
    get_all_sequences,          # Lists all available sequence IDs
    get_system_instruction,     # Extracts the system instruction from a template part
    regenerate_catalog          # Regenerates the catalog if needed
)


# =============================================================================
# SECTION 2: Centralized Configuration Management
# =============================================================================
class Config:
    """
    Manages LLM provider/model selection, parameters, and LiteLLM settings.
    Designed for clarity and ease of modification via centralized definitions.
    """

    # Environment Setup
    @staticmethod
    def _ensure_utf8_encoding():
        """Ensures UTF-8 output encoding for terminals to prevent character errors."""
        for stream in (sys.stdout, sys.stderr):
            if hasattr(stream, "reconfigure"):
                try:
                    stream.reconfigure(encoding="utf-8", errors="replace")
                except Exception:
                    pass

    # Provider Selection
    PROVIDER_ANTHROPIC = "anthropic"
    PROVIDER_DEEPSEEK  = "deepseek"
    PROVIDER_GOOGLE    = "google"
    PROVIDER_OPENAI    = "openai"

    # Default Provider Selection (last assignment determines the active default)
    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
    DEFAULT_PROVIDER = PROVIDER_GOOGLE
    DEFAULT_PROVIDER = PROVIDER_OPENAI

    # Provider-Specific Model Selection (last entry becomes provider's default).
    PROVIDER_MODELS = {
        PROVIDER_ANTHROPIC: {
            "model_name": "claude-3-opus-20240229",
            "model_name": "claude-3-sonnet-20240229",
            "model_name": "claude-3-haiku-20240307",
            "model_name": "openrouter/anthropic/claude-3.7-sonnet:beta",
        },
        PROVIDER_DEEPSEEK: {
            "model_name": "deepseek/deepseek-reasoner",
            "model_name": "deepseek/deepseek-coder",
            "model_name": "deepseek/deepseek-chat",
        },
        PROVIDER_GOOGLE: {
            "model_name": "gemini/gemini-1.5-flash-latest",
            "model_name": "gemini/gemini-2.0-flash",
            "model_name": "gemini/gemini-2.5-pro-preview-03-25",
        },
        PROVIDER_OPENAI: {
            "model_name": "gpt-4o",
            "model_name": "gpt-4o-mini",
            "model_name": "gpt-3.5-turbo",
            "model_name": "gpt-3.5-turbo-instruct",
            "model_name": "gpt-3.5-turbo-1106",
            "model_name": "o3-mini",
            "model_name": "gpt-4.1",
        },
    }

    # Model Registry (maps user-friendly names to actual LiteLLM model IDs)
    MODEL_REGISTRY = {
        # OpenAI
        "gpt-4o": "gpt-4o",
        "gpt-4-turbo": "gpt-4-turbo",
        "gpt-4": "gpt-4",
        "gpt-3.5-turbo": "gpt-3.5-turbo",
        "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",
        # Anthropic
        "claude-3-opus": "anthropic/claude-3-opus-20240229",
        "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
        "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
        "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",
        # Google
        "gemini-pro": "gemini/gemini-1.5-pro",
        "gemini-flash": "gemini/gemini-1.5-flash-latest",
        "gemini-2-flash": "gemini/gemini-2.0-flash",
        "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",
        # Deepseek
        "deepseek-reasoner": "deepseek/deepseek-reasoner",
        "deepseek-coder": "deepseek/deepseek-coder",
        "deepseek-chat": "deepseek/deepseek-chat",
    }

    # -----------------------------------------------------------------------------
    # (New) Default Output Directory: subfolder within the script's own directory.
    # -----------------------------------------------------------------------------
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
    DEFAULT_OUTPUT_DIR = os.path.join(SCRIPT_DIR, "output")

    @classmethod
    def set_default_output_dir(cls, directory: str):
        """
        Allows overriding the default output directory programmatically or via CLI.
        """
        cls.DEFAULT_OUTPUT_DIR = directory

    # ---------------------------
    #   Model Selection Logic
    # ---------------------------
    @classmethod
    def get_default_model(cls, provider=None):
        """Get the default model for a provider based on configuration."""
        provider = provider or cls.DEFAULT_PROVIDER
        provider_config = cls.PROVIDER_MODELS.get(provider, {})

        if "model_name" in provider_config:
            return provider_config["model_name"]

        # Fallbacks for each provider if no model is explicitly set
        fallbacks = {
            cls.PROVIDER_OPENAI: "gpt-3.5-turbo",
            cls.PROVIDER_ANTHROPIC: "anthropic/claude-3-haiku-20240307",
            cls.PROVIDER_GOOGLE: "gemini/gemini-1.5-flash-latest",
            cls.PROVIDER_DEEPSEEK: "deepseek/deepseek-chat"
        }

        # Ultimate fallback
        return fallbacks.get(provider, "gpt-3.5-turbo")

    @classmethod
    def get_model_params(cls, model_name=None, provider=None):
        """
        Resolves user-friendly model name to its LiteLLM ID via MODEL_REGISTRY.
        """
        provider = provider or cls.DEFAULT_PROVIDER
        model_name = model_name or cls.get_default_model(provider)

        provider_defaults = cls.PROVIDER_MODELS.get(provider, {})
        params = {k: v for k, v in provider_defaults.items() if k != "model_name"}

        actual_model_id = cls.MODEL_REGISTRY.get(model_name, model_name)
        return {"model": actual_model_id, **params}

    @classmethod
    def get_available_models(cls):
        """Returns a structured list of registered models, grouped by provider."""
        result = {}

        for provider, config in cls.PROVIDER_MODELS.items():
            # Get the selected model for this provider
            selected_model = cls.get_default_model(provider)

            # Get all models from the registry that belong to this provider
            provider_models = []
            for name, model_id in cls.MODEL_REGISTRY.items():
                if (provider == cls.PROVIDER_OPENAI and not '/' in model_id) or \
                   (provider != cls.PROVIDER_OPENAI and model_id.startswith(f"{provider}/")):
                    provider_models.append({
                        "name": name,
                        "model_id": model_id,
                        "is_default": (name == selected_model or model_id == selected_model)
                    })

            result[provider] = provider_models

        return result

    # ---------------------------
    #   LiteLLM Initialization
    # ---------------------------
    @classmethod
    def configure_litellm(cls):
        """Configures global LiteLLM settings and terminal encoding."""
        litellm.drop_params = True     # Prevent errors from unsupported parameters
        litellm.num_retries = 3        # Retry failed API calls
        litellm.request_timeout = 120  # Set API request timeout
        litellm.set_verbose = False    # Reduce LiteLLM's own console output
        litellm.callbacks = []         # Disable default callbacks unless explicitly configured

        # Set terminal encoding
        cls._ensure_utf8_encoding()

        # Additional configuration can be added here
        print(f"\n[Config] LiteLLM Initialized:")
        print(f"- Default Provider: {cls.DEFAULT_PROVIDER}")
        print(f"- Default Model: {cls.get_default_model()}")
        print(f"- Retries: {litellm.num_retries}, Timeout: {litellm.request_timeout}s")
        print(f"- Default Output Directory: {cls.DEFAULT_OUTPUT_DIR}")


# =============================================================================
# SECTION 3: Script Entry Point & Argument Parsing
# =============================================================================
def parse_args():
    parser = argparse.ArgumentParser(description="Example script with output-dir option.")
    parser.add_argument(
        "--output-dir", "-o",
        help="Specify default output directory for any generated files.",
        default=Config.DEFAULT_OUTPUT_DIR
    )
    parser.add_argument(
        "--provider",
        help="Override the default provider.",
        default=Config.DEFAULT_PROVIDER
    )
    parser.add_argument(
        "--model",
        help="Override the default model name (if desired).",
        default=None
    )
    parser.add_argument(
        "--list-models",
        action="store_true",
        help="List available models (grouped by provider) and exit."
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()

    # Update the default output directory if specified
    if args.output_dir:
        Config.set_default_output_dir(args.output_dir)

    # Optionally override the default provider
    if args.provider and args.provider != Config.DEFAULT_PROVIDER:
        Config.DEFAULT_PROVIDER = args.provider

    # Optionally override the default model
    if args.model:
        # This doesn't permanently update get_default_model but you could pass it along
        pass

    # Configure LiteLLM (prints summary info, including the new output directory)
    Config.configure_litellm()

    # If the user just wants to list models, show them and exit
    if args.list_models:
        all_models = Config.get_available_models()
        print("\nAvailable Models by Provider:")
        for provider, model_list in all_models.items():
            print(f"  {provider}:")
            for m in model_list:
                marker = "[DEFAULT]" if m["is_default"] else ""
                print(f"    - {m['name']} -> {m['model_id']} {marker}")
        sys.exit(0)

    # Proceed with the rest of your script logic
    os.makedirs(Config.DEFAULT_OUTPUT_DIR, exist_ok=True)
    # e.g. writing files to that directory:
    # with open(os.path.join(Config.DEFAULT_OUTPUT_DIR, "example.txt"), "w", encoding="utf-8") as f:
    #     f.write("Hello, world!")
```
