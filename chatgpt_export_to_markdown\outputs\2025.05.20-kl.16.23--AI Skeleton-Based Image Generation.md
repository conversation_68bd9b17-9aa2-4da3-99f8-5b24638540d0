# AI Skeleton-Based Image Generation

Conversation ID: 682c9072-5d4c-8008-a3c3-4c0541a16050

## Message 1

To guide the research properly, could you clarify a few points:

1. Are you interested in **state-of-the-art tools and models** (e.g., ControlNet, AnimateDiff) that allow image/video generation from input skeletons or masks?
2. Should the focus be on **research papers**, **open-source projects**, **commercial tools**, or a mix of all?
3. Is your primary use case **image generation**, **video generation**, or both?
4. Any particular application domain? (e.g., filmmaking, game design, advertising)

Once I have this, I can dive into current trends, key models, and useful tools tailored to your interest.

## Message 2

Understood. I’ll research the most current practices and prompt engineering strategies for guiding AI image/video generation using BW input skeletons—especially in cinematic, photorealistic, or stylized animation contexts. The aim will be to synthesize a generalized, professional-grade prompt template that aligns with your goals as an animator and prompt engineer.

I’ll pull from the latest AI tools, prompt engineering best practices, and artistic use cases to inform a template that ensures consistent, high-quality output while allowing flexible interpretation of abstract guide inputs.

I’ll update you shortly with the findings and template.

## Message 3

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-ABSzTKF4yG9GAwNifV7grK', 'size_bytes': 21339, 'width': 276, 'height': 272, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
[media pointer="file-service://file-JamQq9th94ATPE8vobzX4R"]

# Context



when working on ai-assisted image and video generation, what are the recent trends with regards to generating images/videos based on an input "skeleton" (e.g. animated black/white alpha to make the generated output use the skeleton/bw-alpha only as input from which to guide the ai?



I've attached an image to illustrate the concept:



White Alpha (Circle Area): Represents a circular lens or portal.



Black Alpha (Background): Represents the environment seen through (or around) this circular element.





# Example



Visual Concept & Style:



1. The Circle (White Alpha): Treat it as a highly realistic, transparent lens or portal. Include subtle reflections or refractions indicating glass or polished metal. Add cinematic lighting effects—soft glows, faint lens flares, and sharp highlights where the light source hits the lens surface.





2. The Environment (Black Alpha): Depict a dramatic, immersive scene visible through and around the circle. Go for a cinematic, hyper-realistic vibe. Consider any setting that resonates with the idea of looking through a lens or portal.





3. Animation/Video Movement (if generating a video): Start with the circular lens in close-up, then gradually reveal the environment in the background. Incorporate subtle camera panning or dolly movement to emphasize three-dimensionality.


## Message 4





I'm already using multiple ai image and video generators, but probably runway the most. The reason for my inquiry is that i want to develop a systematic, generalized and concistent way of expressing the prompts such that i can produce my own animated bw-inputs (i work as an animator and prompt engineer) and pass them in as "skeletons" (completely regenerate, but to do so guided by the animated input) to concistently produce high-quality videos *that use the inputs only as guide. 



Please write a generalized prompt template that embodies these concepts inherently (to use in combination with a bw-input).The goal of the instruction templates is to demonstrate this ability when used as prompt with an accompanied bw-input guide/skeleton (such as the provided example)



Be less explicit with regards to what the shapes represent (but rather emphasize a generalized direction towards a professional artist/photographer/videographer), put more focus in on inherently conveying llm-optimized rules/instructions that ensures correct interpretation/use of the attached bw-inputs



We need to take everything collectively learned within the topic and leverage the generalized interpretations og the concepts. Instead of trying to reinvent the wheel by generating completely new sequences/approaches, lets rather leverage everything we can and apply it.








## Message 5

# Guiding AI Generators with B/W Skeletons: Prompt Template & Best Practices

## Introduction  
AI image and video models can produce stunning visuals, but guiding their **composition and motion** with precision is challenging when relying on text alone【25†L90-L98】. To address this, animators use **black-and-white (B/W) “skeleton” guides** – simple animations or silhouettes where **white indicates the main subject or motion path** and **black represents background or empty space**. In art terms, the white areas are the **positive space** (primary forms) and the black areas are the **negative space** around them【28†L177-L185】. These guides act like an **abstract storyboard**, conveying **layout, framing, and movement** without detailing the appearance. The key is to ensure the AI interprets these B/W inputs as **cinematic direction cues** – shaping composition and action – rather than literally drawing white and black shapes. 

## Using B/W Guides as Visual Direction  
When you provide a B/W guide image or animation alongside a prompt, think of it as giving the model a **skeleton to build on**. The **white regions** in your guide serve as **visual anchors** for the model – where important subjects or motion should be – and the **black regions** denote background or **empty context**. In other words, *“the area around the primary objects…is known as negative space, while the space occupied by the primary objects is positive space.”*【28†L177-L185】 By leveraging this principle, we instruct the AI that **white shapes = subject presence**, **black = open space**. 

Modern diffusion models support this concept through specialized tools. For example, Stable Diffusion’s **ControlNet** has a *scribble mode* that *“interprets basic shapes and patterns from your scribbles, using them as structural guides for the AI to create a more detailed, refined image.”*【3†L171-L179】 In practice, this means a crude B/W outline (scribble) can lead to a polished image following the same layout. The AI “sees” the white sketch as an **outline to refine** rather than a final object【3†L171-L179】. Similarly, **video generation models** can use sequences of sketches or masks per frame to maintain coherent motion – essentially providing an **animatic** for the AI to follow. The animator’s job is to word the prompt so that the AI respects these guides as composition/motion instructions.

## Best Practices & Relevant Techniques for Structural Guidance  
Incorporating B/W guides effectively often means combining **good prompting techniques** with the right model tools:

- **ControlNet & T2I Adapters:** ControlNet is a game-changer for structure control. It can *“turn a scribble into a professional image.”*【38†L37-L41】 When using a B/W guide, you would choose a relevant ControlNet model (e.g. “scribble” or “segmentation”) so the diffusion model is explicitly conditioned on your guide. Research from 2023 introduced **T2I-Adapter**, a lightweight module that provides *“extra conditioning (sketch, depth maps, etc.)”* to give **accurate structural guidance** to diffusion models【25†L90-L98】. This means the model doesn’t have to guess your intended layout from text alone – it learns to align with the guide image. The recently released **IP-Adapter (Image Prompt Adapter)** goes further by allowing you to **inject composition** from an image while ignoring its original content【19†L43-L51】. For example, feeding a simple silhouette of a person waving can result in *“a completely different person waving with the same pose”*【19†L43-L51】. Unlike ControlNet which tightly locks the output to the input image, the IP-Adapter’s **composition mode** is more flexible – it guides layout and pose but still lets the model be creative with details【19†L66-L74】. Leverage these tools if available: they improve reliability by ensuring the AI *must* follow the white-on-black structure.

- **Motion Control for Video:** For animation, consistency across frames is vital. Tools like **AnimateDiff** (an extension of Stable Diffusion for video) separate content and motion, allowing the model to learn motion priors and apply them independently【7†L25-L33】【7†L98-L100】. One technique is pairing AnimateDiff with ControlNet using depth or edge maps across frames to lock down camera moves or object paths【7†L25-L33】. Recent research **MotionCtrl (2024)** even introduces dedicated control modules for camera vs. object motion, accepting explicit pose trajectories as input【35†L85-L93】. This means an animator can provide a path for the camera and a path for the subject, and the model will generate video with *“background and object movements reflecting the specified camera poses and trajectories”*【35†L85-L93】. In our context, a white shape moving across black frames *is* essentially a trajectory for an object – modern motion controllers can use that to guide the animation precisely. Whether through these advanced methods or simpler frame-by-frame ControlNet, the principle is the same: the white form’s motion is treated as a **movement arc** or blocking, not a literal white object to render. 

- **Cinematic Prompting:** Regardless of tools, your text prompt should reinforce that the generation is **cinematic and artistic**. Include descriptors for **camera framing, lighting, and style** that match your vision. For instance, photography terms like *“wide-angle shot”*, *“close-up portrait framing”*, or *“low-angle perspective”* cue the model to arrange the scene accordingly【33†L144-L152】. Lighting keywords are crucial too: *“dramatic backlighting,” “soft diffused light with dynamic shadows,” “golden-hour glow,”* etc., guide the AI’s rendering of light and shadow【33†L175-L184】【33†L177-L185】. The B/W guide already suggests where the subject is and where empty space is; by adding *cinematic lighting* instructions, you ensure the model fills that subject with life (for example, illuminating the form against a darker background for contrast). **Lens and depth-of-field cues** (e.g. *“shallow depth of field, bokeh background”*) further push a photographic or filmic look, making the output feel like a professional shot rather than a flat drawing.

- **Abstract Language over Literal:** When describing the guide in the prompt, **avoid naming shapes or colors directly**. If the prompt says *“a white circle on a black background,”* many models will literally draw a white circle on black – not what we want. Instead, use abstract language that conveys the intent of the guide. Words like **“silhouette,” “outline,” “motion curve,” “frame”** can be useful if phrased carefully. For example: *“composition guided by an **underlying silhouette**”* or *“following an **invisible motion arc** in the scene”*. This tells the model there is a structural concept without introducing a visible object. You can also hint at the **arrangement** rather than the shape: e.g. *“subject is **foregrounded against empty space**,” “a dynamic diagonal flow from lower left to upper right”*, or *“the scene centers on the subject, surrounded by open space for contrast.”* Such phrasing aligns with the guide’s intent (subject in one area, emptiness in another) without saying “there is a white blob there.” Always emphasize **the outcome** (what the viewer should see) not the guide itself. The prompt should describe the **intended final image/video** as if the guide’s influence is implicit. 

- **Negative Prompting (optional):** In many image generators you can use negative prompts to rule out unwanted interpretations. It can help to add terms like **“(no simple outline), (no flat silhouette), (not cartoonish)”** to ensure the model doesn’t give you a flat graphical result. For instance, a negative prompt of *“silhouette, stick figure, drawing, logo, abstract pattern”* will push the AI away from rendering your guide as a plain shape. Instead, your positive prompt should encourage **full detail and realism** for the subject. Balancing this is important – you want the model to use the silhouette for composition, but still generate a rich image.

With these practices in mind, let’s construct a **generalized prompt template** that you can adapt for various scenes. This template will be modular, meaning you can swap in your specific subject, style, and details, while the core instructions about the B/W guide remain consistent.

## Prompt Template Framework  
Below is a prompt template that incorporates **artistic intent, cinematic detail, and instructions to use the B/W guide structurally**. You can adjust each [variable] to fit your scenario:

```markdown
A [**style/genre**] scene of [**subject description**] in [**setting/environment**]. The composition is guided by an **abstract high-contrast framing** – **bright forms** indicate the focal subject and **dark areas** denote negative space. [**Subject**] [**additional action/pose**] amidst [**context**], portrayed with **professional cinematic quality**: [**lighting**] and [**camera perspective**] emphasize the subject. **Lens effects** like [**lens behavior**] add depth. The **white guide elements act as invisible structural cues** (not visible in the final image), ensuring the subject stands out against the background. The result is a **[desired visual style]**, with **rich details**, **dynamic composition**, and a **[mood/atmosphere]** that follows the guide’s direction.
```

Let’s break down how to use this template:

- **[Style/Genre]:** Begin by specifying the overall style or genre to set the tone. For example, *“a **photorealistic** scene”*, *“a **film noir** scene”*, *“a **surreal fantasy** scene”*, or *“an **anime-style** scene”*. This cues the model’s output style (photographic, painterly, cinematic, etc.). Emphasizing *“cinematic”* or *“ultra-realistic”* here can help, since we want a high-quality result.

- **[Subject Description] in [Setting]:** Clearly state what the main subject is and where. For instance: *“a lone dancer”*, *“an antique car”*, *“a futuristic city skyline”*, *“a medieval knight”*, etc., followed by *“in a misty forest”*, *“on a bustling street at night”*, *“under a stormy sky”*, or any setting. This grounds the AI in what it’s actually depicting, beyond just shapes. Keep it to one main subject if that’s what your white area represents. (If your white guide has multiple elements, you could mention multiple subjects, but be cautious – more complexity might reduce the clarity of the guide’s effect.)

- **Composition guided by abstract high-contrast framing...:** This is the crucial part instructing the model how to use the B/W guide. Phrasing like *“guided by an abstract high-contrast framing”* tells the model there is an underlying visual plan. We explicitly say *“bright forms indicate the focal subject and dark areas denote negative space”* – this maps **white to subject, black to background** in words. Notice we don’t say *“white”* or *“black”* outright in the prompt; instead **“bright”** and **“dark”** are used as adjectives. Models typically understand *bright* vs *dark* in context of lighting and composition, so it reinforces that the subject should be lit/focused (where the white guide was) and the surrounding can be darker or de-emphasized. This line ensures the AI knows the layout is intentional: the subject will occupy the area corresponding to the white guide, and elsewhere should remain background. Essentially, we’re mirroring the idea from art theory that the subject stands out from its negative space【28†L177-L185】, but in natural language.

- **[Subject] [additional action/pose] amidst [context]:** After setting up the composition rule, continue describing the scene. Here you can elaborate on what the subject is doing or how they appear, and what the environment is like, without referencing the guide. For example: *“The knight **raises his sword** amidst **ancient oak trees and swirling mist**,”* or *“The dancer **mid-leap** amidst **dramatic stage lighting and smoke**.”* This part should align with the guide’s motion if applicable (e.g., if your white shape shows movement from left to right, you might describe *“running”* or *“gliding”*). But keep it general if the motion is hard to put in words; the ControlNet or guide itself will handle exact positioning. The goal is to give the AI rich **visual details to fill in** for the white shape: what is it, what is it doing, what’s around it.

- **Professional cinematic quality: [Lighting] and [Camera Perspective]:** Here we infuse cinematic artistry. Mention the **lighting setup** – this could be *“bathed in soft golden-hour light,” “pierced by sharp neon glow,” “under soft moonlight with deep shadows,”* etc., whatever suits your scene’s mood. Also specify a **camera angle or behavior**: e.g. *“wide-angle view,” “dramatic low-angle shot looking up,” “over-the-shoulder perspective,” “tracking shot feel”*. This aligns with filmmaking terms and helps the model understand the framing. For instance, *“dynamic handheld camera feel”* might convey some motion blur or Dutch angle. The combination of lighting and camera cues makes the output more engaging and less likely to be a flat rendition of the shapes. Remember, as prompt guides note, *“specifying composition and framing allows control over the image’s layout and perspective”*【33†L144-L152】 – so don’t hesitate to include those terms even though you have a visual guide; it reinforces the effect. 

- **Lens effects like [Lens Behavior] add depth:** This is an optional slot to mention any lens or post-processing effects for realism. Common examples: *“shallow depth of field (background softly blurred)”*, *“motion blur trails following the movement”*, *“lens flare from the setting sun”*, *“bokeh sparkles in the background”*. These not only make the image/video look high-quality but also implicitly tell the AI the style is photographic/cinematic. If you’re doing a video and your white guide suggests fast motion, mentioning *motion blur* can help the model understand it’s a dynamic action. If the scene is stationary, you might omit this or swap in something like *“crisp focus throughout”*. This modular piece can be tailored or left out, but it often enhances the 3D feel of the result.

- **White guide elements act as invisible structural cues (not visible):** This phrase is a direct instruction to *not literally draw the guide*. We clarify that the **guide is invisible in the final image** – it’s only there to ensure the structure. By stating “not visible in the final image,” we strongly push the model to *use* but *not depict* the white silhouette. We call them **structural cues**, which is language an LLM or possibly the diffusion model might interpret as “this is a guide for you, not something to render.” Essentially, this part is like saying: *“Follow the shape but render the scene naturally.”* You might even phrase it as *“the guide is purely for composition”* in case the model is very advanced in instruction-following. This sentence can be adjusted if needed (for example, some users shorten it to “following an unseen guide shape”), but the provided wording is explicit and works as a safeguard.

- **Result is [desired visual style], with rich details, dynamic composition, [mood]:** Conclude by reiterating the outcome you want. This is where you can plug in adjectives like *“ultra-detailed,” “4K UHD,” “highly detailed textures,” “masterpiece level quality”* if you desire (though some models react to “masterpiece” clichés differently). Also reaffirm the composition is **dynamic** or **balanced** – this echoes that the structure was important. Mention the **mood or atmosphere** to tie it all together: e.g. *“an eerie, suspenseful atmosphere”*, *“a vibrant, energetic mood”*, *“serene and peaceful feeling”*. This helps the AI infuse the final touches (color grading, contrast, expressions) consistent with the story you have in mind. By ending on the note that the scene *“follows the guide’s direction,”* you full-circle remind that everything described should adhere to that initial B/W plan.

### Using the Template Across Scenarios  
This template is deliberately **abstract and modular**. You can apply it to many situations by swapping in the content details while the structural phrasing remains. For example:

- **Fantasy example:** *“A **high-fantasy** scene of **a dragon swooping** over **a misty canyon**. The composition is guided by an abstract high-contrast framing – bright forms indicate the focal subject and dark areas denote negative space. **The dragon arcs through the sky** amidst wisps of cloud, portrayed with professional cinematic quality: **shafts of golden sunlight pierce through the clouds** and a **wide-angle panoramic view** emphasizes the scale. Lens effects like a slight motion blur on the wingtips add depth. The white guide elements act as invisible structural cues (not visible in the final image), ensuring the dragon stands out against the open sky. The result is an **ultra-detailed, photorealistic** scene, with dynamic composition and a **majestic, awe-inspiring** atmosphere.”* – Here we inserted a subject (dragon), an action (swooping), setting (misty canyon sky), and specific cinematic lighting (sun shafts) and angle. We didn’t name any shape like “curve” or “line”, but the phrase “arcs through the sky” and the rest implies the motion path. A diffusion model with a ControlNet guide of a white swooping curve would match this prompt to produce a dragon following that arc, lit by sun with a panoramic shot.

- **Urban live-action example:** *“A **gritty urban** scene of **a car speeding** down **a rainy night street**. The composition is guided by an abstract high-contrast framing – bright forms indicate the focal subject and dark areas denote negative space. **The sports car skids through puddles** amidst neon signs and wet pavement, portrayed with professional cinematic quality: **headlights cutting through the darkness** and a **low-angle chase camera** emphasize the action. Lens effects like **motion blur and raindrop bokeh** add depth. The white guide elements act as invisible structural cues, not visible in the final image, ensuring the car is the clear focus against the dim street. The result is a **stylized realism** frame, with razor-sharp detail, dynamic composition, and a **tense, high-energy** mood.”* – In this, the white shape might have been a blob moving left-to-right (car motion). The prompt uses cinematic terms (low-angle chase cam, headlights, motion blur) to reinforce that dynamic. The model should translate the white shape into the car and the black into the night street.

- **Portrait/static example:** If your B/W guide is a static pose or composition: *“An **artistic portrait** of **a woman standing** in **a sunlit room**. The composition is guided by an abstract high-contrast framing – bright forms indicate the focal subject and dark areas denote negative space. **The woman stands by a window** with dust motes in the air, portrayed with professional cinematic quality: **soft diffused morning light from the window** and a **medium close-up perspective** draw attention to her figure. Lens effects like **shallow depth of field** (the background falls off into gentle blur) add depth. The white guide elements act as invisible structural cues, not visible in the final image, ensuring she is illuminated against a subtle backdrop. The result is a **photorealistic** image, with finely rendered details, a balanced composition, and a **calm, reflective** atmosphere.”* – Here, although nothing is moving, the white shape might have defined her pose and position in frame. We focus on lighting and detail so the model doesn’t output a mere silhouette.

These examples show how flexible the template is. In each case, we keep the key phrases about **abstract framing** and **bright vs dark**, but we plug in different subjects, actions, settings, and styles. The AI, especially when using something like ControlNet or IP-Adapter with your B/W image sequence, will interpret that prompt in context of the guide: producing the detailed, well-lit subjects we described, arranged exactly where the white silhouette indicated.

## How and Why This Template Works  
This prompt framework is designed to **generalize across shapes, motions, and scenes** because it never assumes a specific form – it only reinforces the concept of **guided composition**. By not explicitly naming the shape (“circle”, “line”, etc.), we avoid literal drawing of that shape. Instead, terms like *guide, framing, bright forms, negative space* communicate to the model that the **layout is intentional**. We’re effectively telling the AI: *“There is an underlying plan – follow it!”* 

At the same time, the template strongly emphasizes **artistic and cinematic qualities** so that the output transcends the simple guide. Phrases about lighting, lens, and dynamic angles stimulate the model’s learned knowledge of photographs and films, making it fill the scene with realism and style. This steers the generation towards being an actual cinematic shot *informed by* the guide, rather than a schematic illustration *of* the guide. Including these elements is a known best practice in prompt engineering – e.g. focusing on style, composition, and lighting yields richer results【31†L41-L49】【33†L144-L152】.

Another advantage of this template is its **modularity**. An animator can easily tweak the mood or subject without losing the structural instructions. For instance, if the next scene’s guide has a different motion, you might change “amidst wisps of cloud” to “zig-zagging between buildings” or such, but you’d still keep “bright forms = subject, dark = space”. This consistency in language helps maintain a stable interpretation of guides across many generations, which is useful if you’re doing a long sequence of shots. You develop a kind of **prompt discipline** that the model comes to expect: every time, the prompt says there’s a guide and cinematic setup, so every time the model uses the input accordingly. 

It’s also compatible with various **AI platforms**. If using **LLMs to generate prompts** (e.g. instructing ChatGPT or another model to fill in details for each scene), the template provides a clear structure to follow. You could feed the LLM meta-information like “white shape moves diagonally; scene is outdoors at sunset; subject is a cyclist” and have it produce a prompt by populating this template. Since the template is in natural language, not a rigid form, it works well with LLMs which then output a description ready for the image model.

Finally, this approach integrates well with **ControlNet, IP-Adapter, and motion control techniques**. The text prompt by itself carries the cinematic/artistic intent, while the B/W skeleton (with the help of a controller model) carries the structural intent. Research has shown that combining text with additional visual constraints leads to better fidelity: text alone might not nail a complex layout【30†L66-L74】, but text+structure yields both correctness and creativity. For example, the AnimateDiff+ControlNet depth method yielded higher quality video than text or inversion alone【7†L25-L33】. By following our template, you ensure the text side of this pairing is as strong and specific as the visual side. The prompt tells the model exactly how to treat that extra conditioning – “use it for composition, make it cinematic” – so you get the most out of your B/W guides.

In summary, the template and tips above serve as an **instructional framework** for treating B/W guides as **cinematic directions**. The white parts of your input direct *what/where* to draw, and the prompt’s language directs *how* to draw it (style, mood, quality). By consistently applying this method, an experienced animator can plug in any number of B/W “skeleton” inputs and reliably get high-quality images or clips that realize the intended composition and motion. The AI will effectively understand that your simple white shapes are *not the art itself* but the **invisible stage directions** behind a compelling visual story. 

## References and Further Reading  
- Stable Diffusion ControlNet – *Using scribbles and sketches as structural guides*【3†L171-L179】  
- Art Composition Principles – *Positive (filled) vs Negative (empty) space*【28†L177-L185】  
- T2I-Adapter Paper – *Using learned adapters for structure conditioning*【25†L90-L98】  
- IP-Adapter (Composition) – *Injecting composition from an image, separate from style/content*【19†L43-L51】【19†L66-L74】  
- AnimateDiff & ControlNet – *Depth maps to enforce motion consistency in video generation*【7†L25-L33】  
- MotionCtrl (2024) – *Advanced control of camera vs object motion via trajectories*【35†L85-L93】  
- Prompt Engineering Guide (2025) – *Key elements like style, composition, lighting in prompts*【31†L41-L49】【33†L144-L152】
