# Delay on error exit

Conversation ID: 67fa1ad8-83f8-8008-8898-d9a6728e0941

## Message 1

**Implementing error handling**

I’m integrating a delay before script termination, evaluating a 5-second pause after an exception prints, and considering its effectiveness in various scenarios.

**Balancing exception handling**

I'm working through adding a delay before script termination. This consideration will ensure that errors are handled more gracefully, especially with 'ImportError' or when 'rich' isn't installed.

## Message 2

Below is the same script with a **5-second delay** before exit when an error triggers a `sys.exit(1)`. Specifically, this occurs in the `except ImportError` block for interactive mode. 

```python
#!/usr/bin/env python3
"""
Aggregate and organize log files from various applications into a structured folder format.
Utilizes intelligent timestamp detection to sort logs chronologically.
Supports interactive configuration when --prompt is used.
"""

import os
import sys
import codecs
import shutil
import glob
import json
import argparse
import time  # <-- Added for 5-second delay
from pathlib import Path
from datetime import datetime

if sys.stdout.encoding != 'utf-8':
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# =======================================================
# --- DEFAULTS ---
# =======================================================
DEFAULT_OUTPUT_DIR = Path(f"{Path()}/consolidated_logs")

# =======================================================
# --- SOURCES ---
# =======================================================
def define_log_source(name, dirs, patterns, output_dir, split_conversations=False):
    """
    Define log sources with their own output directory.
    Added split_conversations parameter to control message splitting.
    """
    return {
        "name": name,
        "dirs": [dirs] if isinstance(dirs, (str, Path)) else dirs,
        "patterns": [patterns] if isinstance(patterns, str) else patterns,
        "output_dir": Path(output_dir),
        "split_conversations": split_conversations  # New parameter
    }

LOG_SOURCES = [
    define_log_source(
        name="vscode_cline",
        dirs=f"{Path.home()}/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_vscode/exe/data/user-data/User/globalStorage/saoudrizwan.claude-dev/tasks",
        patterns=["*conversation_history*.json"],
        output_dir=Path(f"{DEFAULT_OUTPUT_DIR}"),  # Use base directory, no source-specific subdirectory
        split_conversations=True  # Enable splitting by default
    ),
    define_log_source(
        name="vscode_roocode",
        dirs=f"{Path.home()}/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_vscode/exe/data/user-data/User/globalStorage/rooveterinaryinc.roo-cline/tasks",
        patterns=["*conversation_history*.json"],
        output_dir=Path(f"{DEFAULT_OUTPUT_DIR}"),  # Use base directory, no source-specific subdirectory
        split_conversations=True  # Enable splitting by default
    ),
]

# =======================================================
# --- CONVERSATION SPLITTING ---
# =======================================================
def split_conversation_file(file_path):
    """
    Split a conversation JSON file into individual message files.
    
    Args:
        file_path: Path to the JSON conversation file
        
    Returns:
        List of created files or None if splitting failed
    """
    try:
        # Parse the original filename to extract components
        original_path = Path(file_path)
        file_stem = original_path.stem
        source_name = ""
        
        # Detect source from filename
        if ".vscode_cline" in file_stem:
            source_name = "vscode_cline"
        elif ".vscode_roocode" in file_stem:
            source_name = "vscode_roocode"
        
        # Create directory for split messages
        split_dir = original_path.parent / file_stem
        split_dir.mkdir(exist_ok=True)
        
        # Load the JSON content
        with open(original_path, 'r', encoding='utf-8') as f:
            try:
                conversation = json.load(f)
            except json.JSONDecodeError as e:
                print(f"! Failed to parse JSON: {file_path} ({e})")
                return None
        
        # Handle different JSON structures based on source
        split_files = []
        
        # If not a list but a single message (possible for some logs)
        if not isinstance(conversation, list):
            if isinstance(conversation, dict):
                # Convert single message to a list for uniform processing
                conversation = [conversation]
            else:
                print(f"! Invalid conversation format: {file_path}")
                return None
            
        # Process each message
        for i, message in enumerate(conversation, 1):
            # Extract the base part (before .chat)
            base_part = file_stem.split('.chat')[0]
            
            # Create the new message filename
            message_filename = f"{base_part}.chat--messages_{i:03d}.json"
            message_path = split_dir / message_filename
            
            # Add timestamp to filename if available in the message
            if source_name == "vscode_roocode" and isinstance(message, dict) and "ts" in message:
                ts_ms = message["ts"]
                dt = datetime.fromtimestamp(ts_ms / 1000.0)
                time_str = dt.strftime("%H.%M.%S")
                message_filename = f"{base_part}.chat--messages_{i:03d}--{time_str}.json"
                message_path = split_dir / message_filename
            
            # Write the individual message
            with open(message_path, 'w', encoding='utf-8') as f:
                json.dump(message, f, ensure_ascii=False, indent=4)
                
            split_files.append(message_path)
            
        return split_files
    except Exception as e:
        print(f"! Failed to split conversation: {file_path} ({e})")
        return None

# =======================================================
# --- TIMESTAMP UTILITIES ---
# =======================================================
def parse_timestamp(value, *, from_path_part=False):
    """
    Attempt to parse a timestamp from a string with flexible format support.
    Enhanced to handle more timestamp formats.
    """
    try:
        value = str(value).strip()

        if from_path_part and not any(c.isdigit() for c in value):
            return None

        # Handle numeric timestamps (Unix timestamps)
        if value.isdigit():
            timestamp = int(value)
            if len(value) >= 13:  # Milliseconds
                return datetime.fromtimestamp(timestamp / 1000.0)
            if len(value) >= 10:  # Seconds
                return datetime.fromtimestamp(timestamp)
        
        # Handle ISO format timestamps
        try:
            return datetime.fromisoformat(value)
        except ValueError:
            pass
        
        # Handle common date formats
        import re
        
        # Try to match YYYY.MM.DD format (used in our output paths)
        date_match = re.match(r'(\d{4})\.(\d{2})\.(\d{2})', value)
        if date_match:
            year, month, day = map(int, date_match.groups())
            return datetime(year, month, day)
        
        # Try to match YYYY-MM-DD format
        date_match = re.match(r'(\d{4})-(\d{2})-(\d{2})', value)
        if date_match:
            year, month, day = map(int, date_match.groups())
            return datetime(year, month, day)
            
    except (ValueError, OSError, OverflowError) as e:
        print(f"  ↳ Timestamp parsing error for '{value}': {e}")
        return None
    
    return None

def find_timestamp(path, source_name=""):
    """
    Attempt to extract a timestamp from various parts of a file path.
    If that fails and the file is a JSON, try to extract timestamp from content.
    """
    # First try to extract from path parts
    if dt := parse_timestamp(path.parts[0]):
        return dt  # First try directory name (most common case)

    for part in path.stem.split('_'):
        if dt := parse_timestamp(part, from_path_part=True):
            return dt  # Then try filename parts (next most likely)

    for part in path.parts:
        if dt := parse_timestamp(part, from_path_part=True):
            return dt  # Finally try all path parts
    
    # If path-based extraction failed and file is JSON, try content-based extraction
    if path.suffix.lower() == '.json':
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = json.load(f)
                
                # For Roo logs, extract timestamp from the 'ts' field
                if source_name == "vscode_roocode":
                    # Handle different possible JSON structures
                    if isinstance(content, list):
                        # If it's a list, find the earliest timestamp
                        timestamps = []
                        for msg in content:
                            if isinstance(msg, dict) and "ts" in msg:
                                timestamps.append(msg["ts"])
                        
                        if timestamps:
                            earliest_ts = min(timestamps)
                            return datetime.fromtimestamp(earliest_ts / 1000.0)
                    
                    elif isinstance(content, dict) and "ts" in content:
                        # If it's a single message with a timestamp
                        ts_ms = content["ts"]
                        return datetime.fromtimestamp(ts_ms / 1000.0)
                
                # For other JSON files with timestamps, add extraction logic here
                
        except (json.JSONDecodeError, KeyError, TypeError, ValueError) as e:
            # If any error occurs during JSON parsing, just continue
            print(f"  ↳ JSON parsing error: {e}")
            pass
    
    return None

def build_output_path(original_path, dt, source_name):
    """
    Create an output path using new format: YYYY.MM.DD/YYYY.MM.DD-kl.HH.MM.chat.SOURCE_NAME
    """
    date_str = dt.strftime("%Y.%m.%d")
    time_str = dt.strftime("%H.%M")
    
    # Create the new filename with source_name
    new_filename = f"{date_str}-kl.{time_str}.chat.{source_name}{original_path.suffix}"
    
    # Return path with date directory and new filename
    return Path(date_str, new_filename)

# =======================================================
# --- FILE PROCESSING ---
# =======================================================
def find_matching_files(source_def):
    """
    Find all files matching defined patterns in specified directories.
    """
    return [
        match
        for directory in source_def["dirs"]
        for pattern in source_def["patterns"]
        for match in glob.glob(
            str(Path(directory) / "**" / pattern),
            recursive=True
        )
    ]

def process_file(file_path, source_base_dir, output_dir, split_conversations=False, source_name=""):
    """
    Process a single file: extract timestamp, build new path, copy file.
    Now with optional conversation splitting.
    
    Args:
        file_path: Path to the original file
        source_base_dir: Base directory for relative path calculation
        output_dir: Directory where consolidated files are stored
        split_conversations: Whether to split conversation files into individual messages
        source_name: Source identifier to include in the filename
        
    Returns:
        Path to the processed file or None if processing failed
    """
    try:
        original = Path(file_path)
        relative = original.relative_to(source_base_dir)

        # Pass source_name to find_timestamp to enable source-specific extraction
        timestamp = find_timestamp(original, source_name)
        if not timestamp:
            raise ValueError("No valid timestamp found")

        destination = output_dir / build_output_path(original, timestamp, source_name)
        destination.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy2(original, destination)

        # Add conversation splitting step if enabled
        if split_conversations and destination.suffix.lower() == '.json':
            split_files = split_conversation_file(destination)
            if split_files:
                print(f"  ↳ Split into {len(split_files)} messages")

        return destination

    except Exception as e:
        print(f"! Failed: {file_path} ({e})")
        return None

def consolidate_logs(sources=None):
    """
    Iterate over all log sources and consolidate their files into source-specific output directories.

    Args:
        sources: Optional list of source definitions. If None, uses LOG_SOURCES.
    """
    for source_def in (sources or LOG_SOURCES):
        print(f"\nProcessing {source_def['name']}...")
        
        # Check if conversation splitting is enabled
        split_conversations = source_def.get("split_conversations", False)
        if split_conversations:
            print("Conversation splitting: ENABLED")

        for file_path in find_matching_files(source_def):
            # Identify the base directory for relative path calculation
            base_dir = next(
                Path(d) for d in source_def["dirs"]
                if file_path.startswith(str(Path(d)))
            )
            # Process the file with splitting if enabled
            if destination := process_file(
                file_path,
                base_dir,
                source_def["output_dir"],
                split_conversations,  # Pass the splitting setting
                source_def["name"]    # Pass the source name
            ):
                print(f"✓ {destination}")
            else:
                # If processing failed, try again with a more aggressive approach for Roo logs
                if source_def["name"] == "vscode_roocode":
                    print(f"  ↳ Retrying with direct content extraction for Roo logs...")
                    try:
                        original = Path(file_path)
                        # Try to extract timestamp directly from file content
                        with open(original, 'r', encoding='utf-8') as f:
                            try:
                                content = json.load(f)
                                timestamp = None
                                
                                # Extract first message timestamp
                                if isinstance(content, list) and len(content) > 0:
                                    first_msg = content[0]
                                    if isinstance(first_msg, dict) and "ts" in first_msg:
                                        ts_ms = first_msg["ts"]
                                        timestamp = datetime.fromtimestamp(ts_ms / 1000.0)
                                
                                # If we found a timestamp, process the file
                                if timestamp:
                                    destination = source_def["output_dir"] / build_output_path(original, timestamp, source_def["name"])
                                    destination.parent.mkdir(parents=True, exist_ok=True)
                                    shutil.copy2(original, destination)
                                    
                                    # Add conversation splitting step if enabled
                                    if split_conversations and destination.suffix.lower() == '.json':
                                        split_files = split_conversation_file(destination)
                                        if split_files:
                                            print(f"  ↳ Split into {len(split_files)} messages")
                                    
                                    print(f"✓ {destination} (retry successful)")
                            except json.JSONDecodeError as e:
                                print(f"  ↳ JSON parsing error: {e}")
                    except Exception as e:
                        print(f"  ↳ Retry failed: {e}")

# =======================================================
# --- INTERACTIVE CONFIGURATION ---
# =======================================================
def prompt_for_source_settings(source_def, console):
    """
    Interactively configure a log source with conditional defaults.
    """
    print_section = lambda title: console.print(f"\n[bold blue] --- {title} ---[/bold blue]")
    print_section(f"Source: {source_def['name']}")

    if not Confirm.ask("Use default settings?", default=True):
        # Handle directories
        dirs_list = []
        for i, current_dir in enumerate(source_def["dirs"], 1):
            new_dir = Prompt.ask(f"Source directory {i}?", default=str(current_dir))
            dirs_list.append(new_dir)

            if i == len(source_def["dirs"]) and Confirm.ask("Add another directory?", default=False):
                while True:
                    i += 1
                    new_dir = Prompt.ask(f"Source directory {i}?")
                    dirs_list.append(new_dir)
                    if not Confirm.ask("Add another directory?", default=False):
                        break
        source_def["dirs"] = dirs_list

        # Handle patterns
        patterns_list = []
        for i, current_pattern in enumerate(source_def["patterns"], 1):
            new_pattern = Prompt.ask(f"File pattern {i}?", default=current_pattern)
            patterns_list.append(new_pattern)

            if i == len(source_def["patterns"]) and Confirm.ask("Add another pattern?", default=False):
                while True:
                    i += 1
                    new_pattern = Prompt.ask(f"File pattern {i}?")
                    patterns_list.append(new_pattern)
                    if not Confirm.ask("Add another pattern?", default=False):
                        break
        source_def["patterns"] = patterns_list

        # Output directory
        new_output = Prompt.ask("Output directory?", default=str(source_def["output_dir"]))
        source_def["output_dir"] = Path(new_output)
        
        # Add conversation splitting option
        split_conversations = Confirm.ask(
            "Split conversation files into individual messages?", 
            default=source_def.get("split_conversations", False)
        )
        source_def["split_conversations"] = split_conversations

    return source_def

def prompt_for_new_source(console):
    """
    Prompt for a new log source configuration.
    """
    print_section = lambda title: console.print(f"\n[bold blue] --- {title} ---[/bold blue]")
    print_section("New Source Configuration")

    name = Prompt.ask("Source name")

    # Collect directories
    dirs = []
    while True:
        dir_path = Prompt.ask(f"Source directory {len(dirs) + 1}")
        dirs.append(dir_path)
        if not Confirm.ask("Add another directory?", default=False):
            break

    # Collect patterns
    patterns = []
    while True:
        pattern = Prompt.ask(f"File pattern {len(patterns) + 1}")
        patterns.append(pattern)
        if not Confirm.ask("Add another pattern?", default=False):
            break

    # Output directory
    output_dir = Prompt.ask("Output directory", default=str(DEFAULT_OUTPUT_DIR))
    
    # Add conversation splitting option for new sources
    split_conversations = Confirm.ask(
        "Split conversation files into individual messages?", 
        default=True
    )

    return define_log_source(
        name=name,
        dirs=dirs,
        patterns=patterns,
        output_dir=output_dir,
        split_conversations=split_conversations
    )

def prompt_for_settings(sources):
    """
    Configure log sources through interactive prompts.
    """
    console = Console()
    modified_sources = []

    console.print("\n[bold blue]=== Log Source Configuration ===[/bold blue]")

    # Configure existing sources
    for source in sources:
        modified_source = prompt_for_source_settings(source.copy(), console)
        modified_sources.append(modified_source)

    # Option to add new sources
    while Confirm.ask("\nAdd a new log source?", default=False):
        new_source = prompt_for_new_source(console)
        modified_sources.append(new_source)

    return modified_sources

# =======================================================
# --- RUN ---
# =======================================================
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Aggregate and organize log files with optional interactive configuration.")
    parser.add_argument('--prompt', action='store_true', help='Prompt for log source configuration')
    args = parser.parse_args()

    if args.prompt:
        try:
            from rich.console import Console
            from rich.prompt import Prompt, Confirm
            sources = prompt_for_settings(LOG_SOURCES)
            consolidate_logs(sources)
        except ImportError:
            print("Error: The 'rich' package is required for interactive mode.")
            print("Please install it with: pip install rich")
            time.sleep(5)  # <-- Wait 5 seconds before exiting
            sys.exit(1)
    else:
        consolidate_logs()
```

**Key change**  
- Added the line `time.sleep(5)` right before `sys.exit(1)` in the `except ImportError` block to **delay the exit by 5 seconds** in the event of an error.

## Message 3

please modify this to not exit immediately if error, instead make it wait 5 seconds before exiting if error:

#!/usr/bin/env python3

"""

Aggregate and organize log files from various applications into a structured folder format.

Utilizes intelligent timestamp detection to sort logs chronologically.

Supports interactive configuration when --prompt is used.

"""



import os

import sys

import codecs

import shutil

import glob

import json

import argparse

from pathlib import Path

from datetime import datetime



if sys.stdout.encoding != 'utf-8':

    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')

    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')



# =======================================================

# --- DEFAULTS ---

# =======================================================

DEFAULT_OUTPUT_DIR = Path(f"{Path()}/consolidated_logs")



# =======================================================

# --- SOURCES ---

# =======================================================

def define_log_source(name, dirs, patterns, output_dir, split_conversations=False):

    """

    Define log sources with their own output directory.

    Added split_conversations parameter to control message splitting.

    """

    return {

        "name": name,

        "dirs": [dirs] if isinstance(dirs, (str, Path)) else dirs,

        "patterns": [patterns] if isinstance(patterns, str) else patterns,

        "output_dir": Path(output_dir),

        "split_conversations": split_conversations  # New parameter

    }



LOG_SOURCES = [

    define_log_source(

        name="vscode_cline",

        dirs=f"{Path.home()}/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_vscode/exe/data/user-data/User/globalStorage/saoudrizwan.claude-dev/tasks",

        patterns=["*conversation_history*.json"],

        output_dir=Path(f"{DEFAULT_OUTPUT_DIR}"),  # Use base directory, no source-specific subdirectory

        split_conversations=True  # Enable splitting by default

    ),

    define_log_source(

        name="vscode_roocode",

        dirs=f"{Path.home()}/Desktop/User/Nas_Flow_Jorn/__GOTO__/Apps/app_vscode/exe/data/user-data/User/globalStorage/rooveterinaryinc.roo-cline/tasks",

        patterns=["*conversation_history*.json"],

        output_dir=Path(f"{DEFAULT_OUTPUT_DIR}"),  # Use base directory, no source-specific subdirectory

        split_conversations=True  # Enable splitting by default

    ),

]



# =======================================================

# --- CONVERSATION SPLITTING ---

# =======================================================

def split_conversation_file(file_path):

    """

    Split a conversation JSON file into individual message files.

    

    Args:

        file_path: Path to the JSON conversation file

        

    Returns:

        List of created files or None if splitting failed

    """

    try:

        # Parse the original filename to extract components

        original_path = Path(file_path)

        file_stem = original_path.stem

        source_name = ""

        

        # Detect source from filename

        if ".vscode_cline" in file_stem:

            source_name = "vscode_cline"

        elif ".vscode_roocode" in file_stem:

            source_name = "vscode_roocode"

        

        # Create directory for split messages

        split_dir = original_path.parent / file_stem

        split_dir.mkdir(exist_ok=True)

        

        # Load the JSON content

        with open(original_path, 'r', encoding='utf-8') as f:

            try:

                conversation = json.load(f)

            except json.JSONDecodeError as e:

                print(f"! Failed to parse JSON: {file_path} ({e})")

                return None

        

        # Handle different JSON structures based on source

        split_files = []

        

        # If not a list but a single message (possible for some logs)

        if not isinstance(conversation, list):

            if isinstance(conversation, dict):

                # Convert single message to a list for uniform processing

                conversation = [conversation]

            else:

                print(f"! Invalid conversation format: {file_path}")

                return None

            

        # Process each message

        for i, message in enumerate(conversation, 1):

            # Extract the base part (before .chat)

            base_part = file_stem.split('.chat')[0]

            

            # Create the new message filename

            message_filename = f"{base_part}.chat--messages_{i:03d}.json"

            message_path = split_dir / message_filename

            

            # Add timestamp to filename if available in the message

            if source_name == "vscode_roocode" and isinstance(message, dict) and "ts" in message:

                ts_ms = message["ts"]

                dt = datetime.fromtimestamp(ts_ms / 1000.0)

                time_str = dt.strftime("%H.%M.%S")

                message_filename = f"{base_part}.chat--messages_{i:03d}--{time_str}.json"

                message_path = split_dir / message_filename

            

            # Write the individual message

            with open(message_path, 'w', encoding='utf-8') as f:

                json.dump(message, f, ensure_ascii=False, indent=4)

                

            split_files.append(message_path)

            

        return split_files

    except Exception as e:

        print(f"! Failed to split conversation: {file_path} ({e})")

        return None



# =======================================================

# --- TIMESTAMP UTILITIES ---

# =======================================================

def parse_timestamp(value, *, from_path_part=False):

    """

    Attempt to parse a timestamp from a string with flexible format support.

    Enhanced to handle more timestamp formats.

    """

    try:

        value = str(value).strip()



        if from_path_part and not any(c.isdigit() for c in value):

            return None



        # Handle numeric timestamps (Unix timestamps)

        if value.isdigit():

            timestamp = int(value)

            if len(value) >= 13:  # Milliseconds

                return datetime.fromtimestamp(timestamp / 1000.0)

            if len(value) >= 10:  # Seconds

                return datetime.fromtimestamp(timestamp)

        

        # Handle ISO format timestamps

        try:

            return datetime.fromisoformat(value)

        except ValueError:

            pass

        

        # Handle common date formats

        import re

        

        # Try to match YYYY.MM.DD format (used in our output paths)

        date_match = re.match(r'(\d{4})\.(\d{2})\.(\d{2})', value)

        if date_match:

            year, month, day = map(int, date_match.groups())

            return datetime(year, month, day)

        

        # Try to match YYYY-MM-DD format

        date_match = re.match(r'(\d{4})-(\d{2})-(\d{2})', value)

        if date_match:

            year, month, day = map(int, date_match.groups())

            return datetime(year, month, day)

            

    except (ValueError, OSError, OverflowError) as e:

        print(f"  ↳ Timestamp parsing error for '{value}': {e}")

        return None

    

    return None



def find_timestamp(path, source_name=""):

    """

    Attempt to extract a timestamp from various parts of a file path.

    If that fails and the file is a JSON, try to extract timestamp from content.

    """

    # First try to extract from path parts

    if dt := parse_timestamp(path.parts[0]):

        return dt # First try directory name (most common case)



    for part in path.stem.split('_'):

        if dt := parse_timestamp(part, from_path_part=True):

            return dt # Then try filename parts (next most likely)



    for part in path.parts:

        if dt := parse_timestamp(part, from_path_part=True):

            return dt # Finally try all path parts

    

    # If path-based extraction failed and file is JSON, try content-based extraction

    if path.suffix.lower() == '.json':

        try:

            with open(path, 'r', encoding='utf-8') as f:

                content = json.load(f)

                

                # For Roo logs, extract timestamp from the 'ts' field

                if source_name == "vscode_roocode":

                    # Handle different possible JSON structures

                    if isinstance(content, list):

                        # If it's a list, find the earliest timestamp

                        timestamps = []

                        for msg in content:

                            if isinstance(msg, dict) and "ts" in msg:

                                timestamps.append(msg["ts"])

                        

                        if timestamps:

                            earliest_ts = min(timestamps)

                            return datetime.fromtimestamp(earliest_ts / 1000.0)

                    

                    elif isinstance(content, dict) and "ts" in content:

                        # If it's a single message with a timestamp

                        ts_ms = content["ts"]

                        return datetime.fromtimestamp(ts_ms / 1000.0)

                

                # For other JSON files with timestamps, add extraction logic here

                

        except (json.JSONDecodeError, KeyError, TypeError, ValueError) as e:

            # If any error occurs during JSON parsing, just continue

            print(f"  ↳ JSON parsing error: {e}")

            pass

    

    return None



def build_output_path(original_path, dt, source_name):

    """

    Create an output path using new format: YYYY.MM.DD/YYYY.MM.DD-kl.HH.MM.chat.SOURCE_NAME

    """

    date_str = dt.strftime("%Y.%m.%d")

    time_str = dt.strftime("%H.%M")

    

    # Create the new filename with source_name

    new_filename = f"{date_str}-kl.{time_str}.chat.{source_name}{original_path.suffix}"

    

    # Return path with date directory and new filename

    return Path(date_str, new_filename)



# =======================================================

# --- FILE PROCESSING ---

# =======================================================

def find_matching_files(source_def):

    """

    Find all files matching defined patterns in specified directories.

    """

    return [

        match

        for directory in source_def["dirs"]

        for pattern in source_def["patterns"]

        for match in glob.glob(

            str(Path(directory) / "**" / pattern),

            recursive=True

        )

    ]



def process_file(file_path, source_base_dir, output_dir, split_conversations=False, source_name=""):

    """

    Process a single file: extract timestamp, build new path, copy file.

    Now with optional conversation splitting.

    

    Args:

        file_path: Path to the original file

        source_base_dir: Base directory for relative path calculation

        output_dir: Directory where consolidated files are stored

        split_conversations: Whether to split conversation files into individual messages

        source_name: Source identifier to include in the filename

        

    Returns:

        Path to the processed file or None if processing failed

    """

    try:

        original = Path(file_path)

        relative = original.relative_to(source_base_dir)



        # Pass source_name to find_timestamp to enable source-specific extraction

        timestamp = find_timestamp(original, source_name)

        if not timestamp:

            raise ValueError("No valid timestamp found")



        destination = output_dir / build_output_path(original, timestamp, source_name)

        destination.parent.mkdir(parents=True, exist_ok=True)

        shutil.copy2(original, destination)



        # Add conversation splitting step if enabled

        if split_conversations and destination.suffix.lower() == '.json':

            split_files = split_conversation_file(destination)

            if split_files:

                print(f"  ↳ Split into {len(split_files)} messages")



        return destination



    except Exception as e:

        print(f"! Failed: {file_path} ({e})")

        return None



def consolidate_logs(sources=None):

    """

    Iterate over all log sources and consolidate their files into source-specific output directories.



    Args:

        sources: Optional list of source definitions. If None, uses LOG_SOURCES.

    """

    for source_def in (sources or LOG_SOURCES):

        print(f"\nProcessing {source_def['name']}...")

        

        # Check if conversation splitting is enabled

        split_conversations = source_def.get("split_conversations", False)

        if split_conversations:

            print("Conversation splitting: ENABLED")



        for file_path in find_matching_files(source_def):

            # Identify the base directory for relative path calculation

            base_dir = next(

                Path(d) for d in source_def["dirs"]

                if file_path.startswith(str(Path(d)))

            )

            # Process the file with splitting if enabled

            if destination := process_file(

                file_path,

                base_dir,

                source_def["output_dir"],

                split_conversations,  # Pass the splitting setting

                source_def["name"]    # Pass the source name

            ):

                print(f"✓ {destination}")

            else:

                # If processing failed, try again with a more aggressive approach for Roo logs

                if source_def["name"] == "vscode_roocode":

                    print(f"  ↳ Retrying with direct content extraction for Roo logs...")

                    try:

                        original = Path(file_path)

                        # Try to extract timestamp directly from file content

                        with open(original, 'r', encoding='utf-8') as f:

                            try:

                                content = json.load(f)

                                timestamp = None

                                

                                # Extract first message timestamp

                                if isinstance(content, list) and len(content) > 0:

                                    first_msg = content[0]

                                    if isinstance(first_msg, dict) and "ts" in first_msg:

                                        ts_ms = first_msg["ts"]

                                        timestamp = datetime.fromtimestamp(ts_ms / 1000.0)

                                

                                # If we found a timestamp, process the file

                                if timestamp:

                                    destination = source_def["output_dir"] / build_output_path(original, timestamp, source_def["name"])

                                    destination.parent.mkdir(parents=True, exist_ok=True)

                                    shutil.copy2(original, destination)

                                    

                                    # Add conversation splitting step if enabled

                                    if split_conversations and destination.suffix.lower() == '.json':

                                        split_files = split_conversation_file(destination)

                                        if split_files:

                                            print(f"  ↳ Split into {len(split_files)} messages")

                                    

                                    print(f"✓ {destination} (retry successful)")

                            except json.JSONDecodeError as e:

                                print(f"  ↳ JSON parsing error: {e}")

                    except Exception as e:

                        print(f"  ↳ Retry failed: {e}")



# =======================================================

# --- INTERACTIVE CONFIGURATION ---

# =======================================================

def prompt_for_source_settings(source_def, console):

    """

    Interactively configure a log source with conditional defaults.

    """

    print_section = lambda title: console.print(f"\n[bold blue] --- {title} ---[/bold blue]")

    print_section(f"Source: {source_def['name']}")



    if not Confirm.ask("Use default settings?", default=True):

        # Handle directories

        dirs_list = []

        for i, current_dir in enumerate(source_def["dirs"], 1):

            new_dir = Prompt.ask(f"Source directory {i}?", default=str(current_dir))

            dirs_list.append(new_dir)



            if i == len(source_def["dirs"]) and Confirm.ask("Add another directory?", default=False):

                while True:

                    i += 1

                    new_dir = Prompt.ask(f"Source directory {i}?")

                    dirs_list.append(new_dir)

                    if not Confirm.ask("Add another directory?", default=False):

                        break

        source_def["dirs"] = dirs_list



        # Handle patterns

        patterns_list = []

        for i, current_pattern in enumerate(source_def["patterns"], 1):

            new_pattern = Prompt.ask(f"File pattern {i}?", default=current_pattern)

            patterns_list.append(new_pattern)



            if i == len(source_def["patterns"]) and Confirm.ask("Add another pattern?", default=False):

                while True:

                    i += 1

                    new_pattern = Prompt.ask(f"File pattern {i}?")

                    patterns_list.append(new_pattern)

                    if not Confirm.ask("Add another pattern?", default=False):

                        break

        source_def["patterns"] = patterns_list



        # Output directory

        new_output = Prompt.ask("Output directory?", default=str(source_def["output_dir"]))

        source_def["output_dir"] = Path(new_output)

        

        # Add conversation splitting option

        split_conversations = Confirm.ask(

            "Split conversation files into individual messages?", 

            default=source_def.get("split_conversations", False)

        )

        source_def["split_conversations"] = split_conversations



    return source_def



def prompt_for_new_source(console):

    """

    Prompt for a new log source configuration.

    """

    print_section = lambda title: console.print(f"\n[bold blue] --- {title} ---[/bold blue]")

    print_section("New Source Configuration")



    name = Prompt.ask("Source name")



    # Collect directories

    dirs = []

    while True:

        dir_path = Prompt.ask(f"Source directory {len(dirs) + 1}")

        dirs.append(dir_path)

        if not Confirm.ask("Add another directory?", default=False):

            break



    # Collect patterns

    patterns = []

    while True:

        pattern = Prompt.ask(f"File pattern {len(patterns) + 1}")

        patterns.append(pattern)

        if not Confirm.ask("Add another pattern?", default=False):

            break



    # Output directory

    output_dir = Prompt.ask("Output directory", default=str(DEFAULT_OUTPUT_DIR))

    

    # Add conversation splitting option for new sources

    split_conversations = Confirm.ask(

        "Split conversation files into individual messages?", 

        default=True

    )



    return define_log_source(

        name=name,

        dirs=dirs,

        patterns=patterns,

        output_dir=output_dir,

        split_conversations=split_conversations

    )



def prompt_for_settings(sources):

    """

    Configure log sources through interactive prompts.

    """

    console = Console()

    modified_sources = []



    console.print("\n[bold blue]=== Log Source Configuration ===[/bold blue]")



    # Configure existing sources

    for source in sources:

        modified_source = prompt_for_source_settings(source.copy(), console)

        modified_sources.append(modified_source)



    # Option to add new sources

    while Confirm.ask("\nAdd a new log source?", default=False):

        new_source = prompt_for_new_source(console)

        modified_sources.append(new_source)



    return modified_sources



# =======================================================

# --- RUN ---

# =======================================================

if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="Aggregate and organize log files with optional interactive configuration.")

    parser.add_argument('--prompt', action='store_true', help='Prompt for log source configuration')

    args = parser.parse_args()



    if args.prompt:

        try:

            from rich.console import Console

            from rich.prompt import Prompt, Confirm

            sources = prompt_for_settings(LOG_SOURCES)

            consolidate_logs(sources)

        except ImportError:

            print("Error: The 'rich' package is required for interactive mode.")

            print("Please install it with: pip install rich")

            sys.exit(1)

    else:

        consolidate_logs()

