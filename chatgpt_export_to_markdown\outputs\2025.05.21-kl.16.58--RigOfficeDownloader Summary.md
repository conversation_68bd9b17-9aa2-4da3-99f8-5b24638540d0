# RigOfficeDownloader Summary

Conversation ID: 682dea05-3760-8008-8251-57d9fc18c933

## Message 1

Condense and clarify:

# RigOfficeDownloader



Automate document retrieval from RigOffice, eliminating tedious manual tasks.



---



## Overview



**Problem:** Engineers frequently waste valuable time navigating cumbersome applications to manually collect documentation, delaying actual 3D project work.



**Solution:** A Python-based automation utility leveraging Selenium that:



* Scrapes document metadata from RigOffice (RigDoc).

* Extracts associated file metadata.

* Downloads and organizes files based on user-defined criteria.



---



## Benefits



* Reduce time spent on repetitive, error-prone manual processes.

* Maintain continuous workflow, minimizing disruptions.

* Standardize document retrieval and organization, ensuring consistency.

* Enable engineers to focus on core tasks instead of administrative chores.



---



## How it Works



RigOfficeDownloader follows a structured, interactive workflow:



1. **Configure Parameters:** Set your rig number, search URLs, and custom filters.

2. **Fetch Documents:** Scrape document metadata into JSON (`<rig>-a-docs.json`).

3. **Review & Select Documents:** Export to Markdown (`<rig>-a-docs.md`), marking desired documents for inclusion.

4. **Fetch Files:** Retrieve metadata for selected documents into JSON (`<rig>-b-files.json`).

5. **Review & Select Files:** Export to Markdown (`<rig>-b-files.md`), marking files for download.

6. **Download Files:** Download and organize selected files automatically into structured directories.



---



## Usage



### Setup



1. Run `py_venv_init.bat` to initialize the Python environment.

2. Launch the utility with `RigOfficeDownloader.bat`.



### Interactive Menu



```

[0] Change search parameters

[1] Configure filters

[2] Fetch docs

[3] Export docs (Markdown)

[4] Import updated docs

[5] Fetch files

[6] Export files (Markdown)

[7] Import updated files

[8] Download files

```



### Command-Line Modes



* **Full Automation:** `RigOfficeDownloader.bat --auto`

* **Interactive Menu:** `RigOfficeDownloader.bat --interactive`

* **Edit Filters:** `RigOfficeDownloader.bat --config`



---



## Technical Highlights



* Uses **Python** and **Selenium** with Chrome WebDriver.

* Supports structured naming conventions and directory organization.

* Allows user intervention at key workflow points via Markdown files.

* Implements robust deduplication and error handling.



---



## Directory Structure



```

outputs/

├── data/

│   ├── <rig>-a-docs.json

│   ├── <rig>-a-docs.md

│   ├── <rig>-b-files.json

│   └── <rig>-b-files.md

└── downloads/

    └── <rig>/

        └── (organized downloaded files)

```



---



## Key Features



* **Automatic Scraping:** Efficiently fetch document and file metadata.

* **Markdown-Based Editing:** User-friendly document and file selection.

* **Custom Naming and Organization:** Generate meaningful file names and folder structures.

* **Progress Feedback:** Clear, real-time console messages during operations.



---



Automate, streamline, and enhance your RigOffice workflow with RigOfficeDownloader.


## Message 2

Here is a condensed and clarified version of the original text:

---

# RigOfficeDownloader

Automate document retrieval from RigOffice to save time and reduce manual effort.

---

## Summary

**Problem:** Engineers lose time manually collecting documents from RigOffice, slowing down project progress.

**Solution:** A Python and Selenium-based tool that:

- Scrapes and extracts document and file metadata.
- Allows selection via Markdown.
- Downloads and organizes files based on user criteria.

---

## Key Benefits

- Saves time and reduces errors.
- Keeps workflows uninterrupted.
- Ensures consistent document organization.
- Frees engineers to focus on engineering, not admin tasks.

---

## Workflow Overview

1. **Set Parameters:** Choose rig, URLs, and filters.
2. **Fetch Docs:** Export metadata to JSON.
3. **Select Docs:** Mark desired entries in Markdown.
4. **Fetch Files:** Get file metadata in JSON.
5. **Select Files:** Mark files in Markdown.
6. **Download Files:** Automatically organize files in folders.

---

## Getting Started

### Setup

1. Run `py_venv_init.bat` to set up the Python environment.
2. Start with `RigOfficeDownloader.bat`.

### Modes

- `--auto`: Full automation
- `--interactive`: Menu-based control
- `--config`: Edit filters

### Menu Options

```
[0] Change search parameters
[1] Configure filters
[2] Fetch docs
[3] Export docs
[4] Import edited docs
[5] Fetch files
[6] Export files
[7] Import edited files
[8] Download files
```

---

## Technical Features

- Python + Selenium + Chrome WebDriver
- Structured file naming and directory layout
- Editable Markdown-based selections
- Built-in deduplication and error handling

---

## Output Structure

```
outputs/
├── data/
│   ├── <rig>-a-docs.json / .md
│   └── <rig>-b-files.json / .md
└── downloads/
    └── <rig>/
        └── organized files
```

---

## Highlights

- Fast, automated metadata scraping
- Human-readable selection via Markdown
- Custom file naming and folder structure
- Real-time progress updates

---

**Streamline your document workflow with RigOfficeDownloader.**

Let me know if you'd like an even shorter version or one tailored for a specific audience (e.g., engineers, managers, developers).

## Message 3

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message
































Prompt-Logic-Instruction


Check the following prompt-part for logical consistency and clarity of the given instruction. Assign a score 
between 0% to 100%. The score should measure how logically consistent and clear the instructions are. 
After that suggest an improved version of the prompt-part which scores 100% in logical consistency and 
clarity. Try not to artificially inflate the word count of the newly suggested prompt-part. I prefer short and 
consistent, logically correct prompt suggestions. Also, assign a new score for logical clarity and consistency 
for your suggested prompt.
Prompt-part ="{{Prompt part}}




## Message 4

All the files uploaded by the user have been fully loaded. Searching won't provide additional information.
