# Python LLM Project Structure

Conversation ID: 67f7d239-0914-8008-bba7-3b4888c15ef7

## Message 1

What the most effective way to structure a python project that utilize the litellm python package to interact with different llm providers/models, the utility's purpose is to execute sequential instruction templates (automatically read from text files within a specific folder)? What the most effective way to structure a python project that utilize the litellm python package (to interact with different llm providers/models), the utility's purpose is to execute sequential instruction templates (automatically read from text files within a specific folder) through a custom class designed for the purpose (named LLMQueryExecutor). The point of this class is to provide a simple, flexible and concistent interface for prompting, designed in a generalized manner such that it can be imported and used from other scripts

## Message 2

User is working on a Python project that uses the `litellm` package to interact with different LLM providers and models. The project centers around a class named `LLMQueryExecutor`, which executes sequential instruction templates automatically read from text files in a specific folder. The goal is to provide a simple, flexible, and consistent interface for prompting, designed in a generalized and importable manner.

## Message 3

To structure your project effectively with a generalized `LLMQueryExecutor` using `litellm`, the focus should be on **modularity**, **extensibility**, and **clarity**. Here's a suggested layout that adheres to those principles:

---

### **1. Recommended Project Structure**

```
llm_executor_project/
│
├── llm_executor/                   # Core logic lives here
│   ├── __init__.py
│   ├── executor.py                # Contains LLMQueryExecutor class
│   ├── config.py                  # Config loading: API keys, defaults
│   ├── template_loader.py        # For loading and parsing templates
│   ├── model_router.py           # Abstracts litellm model interactions
│
├── templates/                     # Folder containing instruction templates
│   ├── 001_intro.txt
│   ├── 002_followup.txt
│   └── ...
│
├── scripts/                       # Example usage or separate scripts
│   └── run_executor_example.py
│
├── tests/                         # Unit tests
│   ├── test_executor.py
│   └── ...
│
├── .env                           # Environment variables (API keys etc.)
├── requirements.txt               # Python dependencies
└── README.md                      # Documentation
```

---

### **2. Core Component Highlights**

#### `executor.py` – `LLMQueryExecutor` Class
- Accepts: model alias, template path, config options.
- Loads instruction files in order.
- Handles state across sequential calls (e.g., `previous_response`).

```python
class LLMQueryExecutor:
    def __init__(self, model_name: str, template_dir: str):
        self.model_name = model_name
        self.template_dir = template_dir
        self.templates = self._load_templates()
    
    def _load_templates(self):
        # Sorted loading of .txt files
        return sorted(Path(self.template_dir).glob("*.txt"))

    def run(self, context: dict = {}):
        response = None
        for template_path in self.templates:
            prompt = template_path.read_text().format(**context)
            response = self._query_model(prompt)
            context['previous_response'] = response
        return response

    def _query_model(self, prompt: str) -> str:
        import litellm
        return litellm.completion(model=self.model_name, messages=[{"role": "user", "content": prompt}]).choices[0].message['content']
```

---

### **3. Modular Utilities**
- `model_router.py`: Lets you swap model providers (OpenAI, Anthropic, etc.) through `litellm`.
- `template_loader.py`: Adds preprocessing logic like variable injection or comment stripping.
- `config.py`: Reads from `.env` or config file for defaults.

---

### **4. Design Principles**
- **Encapsulation**: All prompting logic is in `LLMQueryExecutor`.
- **Separation of concerns**: Model routing, config, and template parsing are modular.
- **Reusability**: Any script or app can import `LLMQueryExecutor`.

---

Would you like me to generate the starter code for `executor.py`, `template_loader.py`, and `model_router.py` to match this layout?

## Message 4

Model set context updated.
