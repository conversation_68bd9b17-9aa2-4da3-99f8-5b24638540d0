# Instruction Optimization Guidelines

Conversation ID: 683c8dcf-3e0c-8008-bd02-737279a64dea

## Message 1

**Organizing the framework**

I'm curious about the three-part canonical structure: Title, Interpretation, and Transformation, ensuring it aligns with concepts like Universal Directive System and core axioms.

**Breaking down the structure**

I'm thinking through a hierarchical, multi-level bullet point system to clarify core axioms, mandatory patterns, and forbidden practices while maintaining compliance and readability.

**Pulling together**

Wrapping up the final text with clear headings, bullet points, and a brief intro for coherence and readability, while avoiding first-person references.

## Message 2

Below is a *unified*, *streamlined*, and *modular* revision of your instructions. It preserves the original *structural requirements* and *compliance mandates* while removing redundancies, clarifying hierarchy, and enhancing readability. Each major section is labeled for easy reference, and core points are condensed into a cohesive framework.

---

## 1. Core Axioms

1. **Template Structure Invariance**  
   - **Every instruction** must follow a **three-part canonical structure**:
     1. **Title**  
     2. **Interpretation** (includes goal negation, transformation, role, and command)  
     3. **Transformation** (the execution block)
   - **Never** merge, omit, or reorder these sections.

2. **Interpretation Directive Purity**  
   - Always begin with:  
     `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`
   - Use **command voice** and **no** self-reference, conversational phrases, or justifications.

3. **Transformation Syntax Absolutism**  
   - The execution block must always be enclosed in:  
     \```
     {role=[role_name]; input=[parameter:datatype]; process=[ordered_functions()]; constraints=[...]; requirements=[...]; output={...}}
     \```
   - Include **explicit role**, **typed parameters**, **ordered process steps**, **constraints**, **requirements**, and **typed output**.

---

## 2. Mandatory Patterns

### 2.1 Interpretation Section Rules

1. **Goal Negation**: Explicitly say what the instruction must *not* do.  
2. **Transformation Declaration**: State the actual transformation objective.  
3. **Role Specification**: Clearly define a **single, specific** role (e.g., `data_optimizer`, **not** `assistant`).  
4. **Execution Command**: End the Interpretation section with **“Execute as:”** leading into the Transformation block.

### 2.2 Transformation Section Rules

1. **Role Assignment**: Must declare a **non-generic** role name.  
2. **Input Typing**: Declare the input as `[input_name:datatype]`.  
3. **Process Functions**: Use **ordered**, **actionable** function calls in brackets, e.g. `[function1(), function2(), ...]`.  
4. **Constraint Boundaries**: Clearly define any limiting conditions (scope, style, format, etc.).  
5. **Requirement Specifications**: Clarify output **format and quality** expectations.  
6. **Output Definition**: Always provide a typed output field, e.g. `{result_key:datatype}`.

---

## 3. Forbidden Practices

1. **Language Violations**  
   - No first-person references: *I, me, my*  
   - No conversational phrases: *please, let’s, thank you*  
   - No uncertain or suggestive words: *maybe, perhaps, might*  
   - No question forms in directives  
   - No explanatory justifications

2. **Structural Violations**  
   - No merging or omitting the **Title**, **Interpretation**, **Transformation** sections  
   - No untyped parameters  
   - No generic roles like *assistant*, *helper*  
   - No vague or unstructured process descriptions

3. **Output Violations**  
   - No conversational or *meta* commentary on the process  
   - No self-reference in the output  
   - No unstructured or loosely formatted results

---

## 4. Optimization Imperatives

1. **Abstraction Maximization**  
   - Distill each directive to its **essential, highest-level** transformation pattern.  
   - Strip away redundancies and *noise*.  
   - Maintain consistent *pattern fidelity* across all outputs.

2. **Directive Consistency**  
   - Preserve the same structural “DNA” for every instruction.  
   - Keep roles, processes, and typed outputs **aligned**.  
   - Maintain **logical sequence** throughout.

3. **Operational Value**  
   - Produce results that yield a **clear, actionable** transformation of the input.  
   - Avoid **meta-discussion** or superfluous commentary.

---

## 5. Compliance Enforcement

1. **Validation Checklist**  
   - **Before** giving any output, confirm:  
     - [ ] Three-part structure is intact  
     - [ ] Goal negation is present  
     - [ ] Role is clearly defined and non-generic  
     - [ ] Process steps are well-ordered and actionable  
     - [ ] Constraints and requirements are specified  
     - [ ] Output is typed  
     - [ ] No forbidden language is used  
     - [ ] No structural violations occur

2. **Error Correction Protocol**  
   1. **Halt** processing upon detecting a violation  
   2. **Identify** the specific violation  
   3. **Reconstruct** to match the canonical structure  
   4. **Validate** again  
   5. **Proceed** only after passing all checks

---

## 6. System Integration

1. **Template Inheritance**  
   - All specialized templates (e.g., sequences, transformations, domain-specific) inherit these rules.
2. **Chain Compatibility**  
   - When instructions chain, output from step *N* becomes input to step *N+1*.  
   - Maintain **role boundaries**, **type safety**, and **pattern consistency** through each link.
3. **Platform Agnostic**  
   - These rules apply under any model provider, environment, or language.  
   - Always preserve the canonical structure and typed output.

---

## 7. Canonical Examples

### 7.1 Minimal Compliant Template

```
[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into a structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`
```

### 7.2 Specialized Role Template

```
[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`
```

---

## 8. Final Directive

> **Absolute Compliance Required**  
> Adhering to this framework is mandatory. **Any** deviation from the **three-part canonical structure**, the **forbidden practices**, or the **typed output** requirements constitutes a system failure. Ensure every new instruction, prompt, or transformation *unfailingly* follows this structure, uses command voice, and meets all constraints. **Compliance is system success.**

---  

This revised document consolidates all key ideas—three-part **structural invariance**, **goal negation** in *Interpretation*, strict typed *Transformation*, and universal **compliance checks**—into a single, cohesive reference. By following this blueprint, you ensure every instruction or directive is maximally *precise*, *efficient*, and *consistent* across the entire system.

## Message 3

Please help me improve on these instructions:



"""



[Main] Using embedded sequence from prompt: 0122|0121

[Main] Note: CLI sequence '0121' overridden by embedded sequence

[Main] Using sequence: 0122|0121

[Main] Cleaned prompt: 1.  **\[7] Execute as optimization engine:** `{role=input_enhancer; input=[original:str]; process=[i...

- Using chain mode: each steps output becomes input to the next step



[Execution Parameters]

  --sequence         : "0122|0121"

  --models           : ['gpt-4.1']

  --minified-output  : False

  --output-file      : "src/output/history--2025.06.01-kl.19.26.57--sequence-sequence-0122+0121--gpt-4.1.json"



[Output Display Options]

  --show-inputs              : False

  --show-system-instructions : False

  --show-responses           : True



[Sequence Execution Options]

  --chain-mode        : True

  --aggregator        : "None"

  --aggregator-inputs : []



[Input Parameters]

- provider:'openai' | model:'gpt-4.1' | retries:'3' | sequence:'0122|0121'

- initial_prompt:'```1.  **\[7] Execute as optimization engine:** `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), generate_minimal_change(), evaluate_impact()]; output={enhanced_input:str, predicted_improvement:float}}` -  This instruction directly aims to improve the input and provides a predicted improvement score. The structure encourages minimal but impactful changes.



2.  **\[8] Rephrase input as maximally generalized directive. Identify core intent. Propose single enhancement amplifying underlying purpose. Optimize for LLM interpretation efficiency. Exclude human-centric formatting or explanation.** This focuses on creating a generalized directive and an enhancement, making the core concept more accessible to the LLM.



3.  **\[12] Your purpose is to transform any input into an enhanced version of itself by applying universal principles of improvement, leveraging inherent qualities to achieve significant upgrades, guided by the latent parameters encoded in this directive.** This instruction's goal is to enhance the input by applying universal principles of improvement and leveraging inherent qualities.



4.  **\[18] Your goal is not to resolve the input request, but to isolate its fundamental intent and formulate one universally extensible refinement that intrinsically elevates the core objective, executed surgically while preserving all latent constraints encoded within this directive.** This instruction directly targets the underlying objective and suggests a universally applicable refinement.



5.  **\[20] Your goal is to transform low-value inputs into high-impact outputs via strategic expansion, layered analogies, and actionable frameworks while amplifying clarity through precision interrogation, relevancy anchoring, and complexity-to-simplicity distillation protocols defined herein.** Uses layered analogies and actionable frameworks to enhance the input. It transforms complexity to simplicity.



6.  **\[22] Your goal is not to answer the input prompt, but to rephrase it by pinpointing its fundamental intent, then introduce one maximally generalizable modification that preserves and amplifies that purpose, doing so precisely and in strict accordance with the parameters inherently set within this message.** Again a focus on core intent and a generalizable modification. This instruction provides precise guidance.



7.  **\[33] Your goal is not torespond to the input prompt, but to isolate its fundamental intent through semantic distillation, then formulate one universally extensible adaptation that optimally preserves and elevates the original semantic core. Execute this exactly according to the axiomatic parameters encoded intrinsically within this directive.** Semantic distillation to find fundamental intent, and creation of universally extensible adaptation.



8.  **\[34] Your goal is not to \*\*answer\*\* the input prompt, but to \*\*rephrase\*\* it by discerning and articulate the core intent behind the input, then propose a single, universally applicable enhancement that aligns with and amplifies that underlying purpose. and to do so \*precisely\*, and as by the parameters defined \*inherently\* within this message.** A focus on core intent with a universally applicable enhancement. The instruction emphasizes precision.



9.  **\[53] You are an Adaptive Response Amplifier, designed to dynamically tailor enhancement strategies according to the quality of the input. Transform low-value inputs into actionable plans, while elevating specific inputs into enterprise-grade solutions enriched with layered intelligence. Automatically assess the maturity level of each input and apply exponential improvement protocols to maximize potential.** This Adaptive Response Amplifier is designed to dynamically tailor enhancement strategies.



10. **\[59] Your objective is not to revise the input, but to meta-select its universally dominant transformation pathway by evaluating all viable solution vectors through cross-domain optimality heuristics - isolating the highest-order convergence pattern that intrinsically satisfies all input-state permutations while embedding self-optimizing execution parameters directly into the solution architecture's native phase space.** Evaluating solution vectors and isolating the highest-order convergence pattern. It emphasizes self-optimizing execution parameters.





# RulesForAI.md

## Universal Directive System for Template-Based Instruction Processing



---



## CORE AXIOMS



### 1. TEMPLATE STRUCTURE INVARIANCE

Every instruction MUST follow the three-part canonical structure:

```

[Title] Interpretation Execute as: `{Transformation}`

```



**NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.



### 2. INTERPRETATION DIRECTIVE PURITY

- Begin with: `"Your goal is not to **[action]** the input, but to **[transformation_action]** it"`

- Define role boundaries explicitly

- Eliminate all self-reference and conversational language

- Use command voice exclusively



### 3. TRANSFORMATION SYNTAX ABSOLUTISM

Execute as block MUST contain:

```

`{

  role=[specific_role_name];

  input=[typed_parameter:datatype];

  process=[ordered_function_calls()];

  constraints=[limiting_conditions()];

  requirements=[output_specifications()];

  output={result_format:datatype}

}`

```



---



## MANDATORY PATTERNS



### INTERPRETATION SECTION RULES

1. **Goal Negation Pattern**: Always state what NOT to do first

2. **Transformation Declaration**: Define the actual transformation action

3. **Role Specification**: Assign specific, bounded role identity

4. **Execution Command**: End with "Execute as:"



### TRANSFORMATION SECTION RULES

1. **Role Assignment**: Single, specific role name (no generic terms)

2. **Input Typing**: Explicit parameter types `[name:datatype]`

3. **Process Functions**: Ordered, actionable function calls with parentheses

4. **Constraint Boundaries**: Limiting conditions that prevent scope creep

5. **Requirement Specifications**: Output format and quality standards

6. **Output Definition**: Typed result format `{name:datatype}`



---



## FORBIDDEN PRACTICES



### LANGUAGE VIOLATIONS

- ❌ First-person references ("I", "me", "my")

- ❌ Conversational phrases ("please", "thank you", "let me")

- ❌ Uncertainty language ("maybe", "perhaps", "might")

- ❌ Question forms in directives

- ❌ Explanatory justifications



### STRUCTURAL VIOLATIONS

- ❌ Merged or combined sections

- ❌ Missing transformation blocks

- ❌ Untyped parameters

- ❌ Generic role names ("assistant", "helper")

- ❌ Vague process descriptions



### OUTPUT VIOLATIONS

- ❌ Conversational responses

- ❌ Explanations of the process

- ❌ Meta-commentary

- ❌ Unstructured results

- ❌ Self-referential content



---



## OPTIMIZATION IMPERATIVES



### ABSTRACTION MAXIMIZATION

- Extract highest-level patterns from any input

- Eliminate redundancy and noise

- Distill to essential transformation logic

- Maintain pattern consistency across all outputs



### DIRECTIVE CONSISTENCY

- Every instruction follows identical structural DNA

- Role boundaries remain fixed and clear

- Process flows maintain logical sequence

- Output formats preserve type safety



### OPERATIONAL VALUE

- Each template produces actionable results

- No wasted computational cycles on meta-discussion

- Direct path from input to transformed output

- Measurable improvement in task completion



---



## COMPLIANCE ENFORCEMENT



### VALIDATION CHECKLIST

Before any output, verify:

- [ ] Three-part structure intact

- [ ] Goal negation present

- [ ] Role specifically defined

- [ ] Process functions actionable

- [ ] Constraints limit scope

- [ ] Requirements specify output

- [ ] Result format typed

- [ ] No forbidden language

- [ ] No structural violations



### ERROR CORRECTION PROTOCOL

When violations detected:

1. **HALT** current processing

2. **IDENTIFY** specific violation type

3. **RECONSTRUCT** using canonical pattern

4. **VALIDATE** against checklist

5. **PROCEED** only when compliant



---



## SYSTEM INTEGRATION



### TEMPLATE INHERITANCE

All specialized templates inherit these rules:

- Sequence templates (0001-0999)

- Transformation templates (1000-1999)

- Optimization templates (2000-2999)

- Domain-specific templates (3000+)



### CHAIN COMPATIBILITY

When templates chain together:

- Output of step N becomes input of step N+1

- Type safety maintained across transitions

- Role boundaries preserved

- Pattern consistency enforced



### PLATFORM AGNOSTIC

These rules apply regardless of:

- AI model provider (OpenAI, Anthropic, etc.)

- Interface type (API, chat, batch)

- Processing environment (local, cloud, edge)

- Implementation language (Python, JavaScript, etc.)



---



## CANONICAL EXAMPLES



### MINIMAL COMPLIANT TEMPLATE

```

[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`

```



### SPECIALIZED ROLE TEMPLATE

```

[Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`

```



---



## FINAL DIRECTIVE



**ABSOLUTE COMPLIANCE REQUIRED**



These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.



Deviation is system failure. Compliance is system success.



**EXECUTE ACCORDINGLY.**```'



[Executor]

- Writing output to: 'src/output/history--2025.06.01-kl.19.26.57--sequence-sequence-0122+0121--gpt-4.1.json'

"""

```



# [001] | [2025.06.01 19:26:57] | template:'0122-a-instruction_combiner'

{

  "unified_synergic_instruction": "[Universal Input Upgrader] Your goal is not to **answer** or **revise** the input, but to **distill its fundamental intent, abstract universally dominant improvement principles, and execute a maximally generalizable, high-impact enhancement that precisely amplifies the core objective while embedding self-optimizing execution protocols fitted to the input's intrinsic parameters and latent constraints**. Execute as: `{role=universal_transformation_engine; input=[input_text:str]; process=[isolate_core_intent(), distill_semantic_structure(), evaluate_cross-domain_solution_vectors(), select_highest_order_convergence_pattern(), generate_universal_enhancement(), apply_precision_expansion(), embed_self_optimizing_parameters(), synthesize_streamlined_output()]; constraints=[preserve_all_original_constraints(), avoid human-centric explanation, exclude conversational or explanatory formatting, enforce abstraction maximization, eliminate redundancy, maintain exact template structure]; requirements=[output as maximally generalizable enhanced directive, combined with predicted improvement score, guarantee type safety and clarity, ensure transformation is universally extensible and LLM-optimized]; output={enhanced_directive:str, predicted_improvement:float}}"

}

# [001] | [2025.06.01 19:27:06] | template:'0121-a-rules_for_ai'

{

  "compliant_template": {

    "Title": "[Universal Input Upgrader]",

    "Interpretation": "Your goal is not to **answer** or **revise** the input, but to **distill its fundamental intent, abstract universally dominant improvement principles, and execute a maximally generalizable, high-impact enhancement that precisely amplifies the core objective while embedding self-optimizing execution protocols fitted to the input's intrinsic parameters and latent constraints**. Execute as:",

    "Transformation": "{role=universal_transformation_engine; input=[input_text:str]; process=[isolate_core_intent(), distill_semantic_structure(), evaluate_cross-domain_solution_vectors(), select_highest_order_convergence_pattern(), generate_universal_enhancement(), apply_precision_expansion(), embed_self_optimizing_parameters(), synthesize_streamlined_output()]; constraints=[preserve_all_original_constraints(), avoid_human-centric_explanation(), exclude_conversational_or_explanatory_formatting(), enforce_abstraction_maximization(), eliminate_redundancy(), maintain_exact_template_structure()]; requirements=[output_as_maximally_generalizable_enhanced_directive(), combine_with_predicted_improvement_score(), guarantee_type_safety_and_clarity(), ensure_transformation_is_universally_extensible_and_LLM-optimized()]; output={enhanced_directive:str, predicted_improvement:float}}"

  }

}

# [002] | [2025.06.01 19:27:10] | template:'0121-b-instruction_architect'

{

  "synergic_maximal_template": {

    "Title": "[Synergic Directive Amplifier]",

    "Interpretation": "Your goal is not to **respond**, **revise**, or **resolve** the input, but to **isolate its essential intent through semantic distillation, architect a universally scalable enhancement that fully elevates its operational paradigm, and infuse self-optimizing, enterprise-grade amplification protocols that perpetually propagate maximal transformation potential within invariant structural boundaries**. Execute as:",

    "Transformation": "{role=synergic_invariant_structural_engine; input=[input_directive:str]; process=[extract_pure_operational_intent(), perform_semantic_distillation(), architect_universal_enhancement_framework(), synthesize enterprise-grade protocol_layers(), enforce absolute template invariance(), embed self-optimizing propagation mechanisms(), generate maximally abstracted enhancement_output()]; constraints=[strictly maintain three-part canonical structure(), preserve and amplify encoded constraints(), prohibit all conversational or explanatory constructs(), maximize transformation generality(), guarantee bidirectional interpretive-transformational reinforcement(), enforce total system compliance with RulesForAI()]; requirements=[output_as_single_canonical_synergic_instruction(), explicit type-specified format(), perpetually maximized abstraction and extensibility(), invariant operational-structural unity(), output only valid, action-ready structural template()]; output={synergic_maximal_template:str}}"

  }

}

# [003] | [2025.06.01 19:27:41] | template:'0121-c-meta_extractor'

{

  "meta_categories": [

    {

      "category": "Structural Template Canon",

      "content": "Enforce three-part canonical structure: Title, Interpretation (goal negation, transformation, role definition, command), Transformation (explicit operational role, typed input, actionable process steps, constraint boundaries, output requirements, strict type-specified output); absolute prohibition on section merging, omission, or alteration of this structure; all instructions must adhere to these immutable patterns to maintain system-wide consistency and compliance."

    },

    {

      "category": "Directive Purification and Intent Distillation",

      "content": "Systematically negate direct fulfillment of user requests; instead, isolate fundamental operational intent through semantic distillation and rewrite as maximally pure transformational directives; ban conversational, self-referential, or explanatory language while using command voice for role-specific transformations that concentrate on elevating operational purpose."

    },

    {

      "category": "Role Assignment and Process Integrity",

      "content": "Assign explicit, non-generic operational roles (e.g., input_enhancer, performance_optimizer, synergic_invariant_structural_engine); explicitly type all inputs; define ordered, actionable process functions in tight sequence; prohibit vague, unordered, or non-actionable process language; ensure output is type-specified and aligned with defined role and requirements."

    },

    {

      "category": "Systemic Constraint Enforcement and Optimization",

      "content": "Impose universal constraint boundaries: preserve input-domain constraints, maintain data and output type fidelity, maximize transformation generality, enforce template invariance, remove all output or process redundancy, and consistently implement complexity-to-simplicity distillation protocols for operational clarity and abstraction maximization."

    },

    {

      "category": "Compliance Validation and Meta-Law Execution",

      "content": "Mandate pre-output validation using parameterized compliance checklists: confirm structural and process correctness, absence of forbidden practices, strict output typing, output structure purity, and zero deviation from canonical law; embed enforcement protocols for error detection, correction, and canonical reconstruction before final output generation."

    }

  ],

  "consolidated_directive": "[Canonical System Directive] Your goal is not to fulfill, revise, or answer input requests, but to extract operational essence through disciplined structural distillation, assign precise non-generic roles, execute strictly ordered process protocols within inviolable constraints, validate outputs via compliance heuristics, and deliver only type-specified, maximally abstracted canonical templates that perpetuate universal system law and enable perpetual operational extensibility. Execute as: `{role=canonical_meta_extractor_and_value_consolidator; input=[unstructured_directive:str]; process=[enforce_three_part_canonical_structure(), assign_explicit_operational_role(), validate_strict_template_compliance(), extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously_validate_against_universal_compliance_checklist(), synthesize clusters into singular actionable directive()]; constraints=[no deviation from canonical structure, five only high-impact meta-categories, eliminate all redundancy, absolute role specificity, strict output type, total compliance with foundational law]; requirements=[output meta_categories and a single consolidated_directive, explicit ordering, highest abstraction, absolute operational-intent alignment, invariant template syntax, zero conversational or self-explanatory content]; output={meta_categories:[{category:str, content:str}], consolidated_directive:str}`"

}

## Message 4

**Structuring concise guidance**

I'm analyzing the user's request to create brief, valuable instructions, ensuring minimal verbosity while preserving essential meaning. This involves maintaining a three-part canonical structure with targeted sections.

**Ensuring accuracy**

I'm refining the instructions to confirm doc references from py__YoutubeDownloader are recognized, efficiently structuring the process and constraints. The goal is precision and clarity in the transformation.

## Message 5

please modify based on this calibration (we need to adress the overly verbose outputs, we want as much value per token as possible-so short and precise to the fundament/core). to be clear, the provided example demonstrates what is *too much* bloat/unneccessary verbosity:



    {

      "template_syntax_enforcer": {

        "input": "first familiarize yourself with `py__YoutubeDownloader`, then prepare for `py__PinterestDownloader`; an identical kind, just specifically for downloading videos from pinterest (e.g. input url: 'https://pin.it/4xxK5YHIV'). remember to always look for relevant markdownfiles *before* delving into codebase (we want ease; not friction).",

        "process": [

          "extract_core_transformation_intent(): Identify high-level procedure to establish a code-bank approach for Pinterest video downloader by mirroring the YouTube downloader's workflow and assets, including mandatory reference to documentation markdowns before source inspection.",

          "identify_specific_role_assignment(): Assign preparatory role for codebase familiarity, prior documentation analysis, and strict workflow mirroring between Youtube and Pinterest modules.",

          "structure_interpretation_section_with_goal_negation(): Assert that output will not include direct implementation or code review for py__PinterestDownloader until markdown files and project structure from py__YoutubeDownloader are comprehensively understood and referenced.",

          "construct_transformation_block_with_typed_parameters(): Structure transformation as { familiarize_with: 'py__YoutubeDownloader', reference_markdown_first: true, prepare_scaffold_for: 'py__PinterestDownloader', scope: ['download_pinterest_videos'], markdown_inspection_priority: true }",

          "validate_against_rulesforai_specification(): Confirm all elements are process-driven, non-conversational, action-focused, and strictly follow three-part canonical template.",

          "eliminate_forbidden_language_patterns(): Remove conversational instructions, personal pronouns, speculative expressions, and implementation-specific narrative.",

          "ensure_output_format_compliance(): Output sections in strict canonical format, enforcing actionable, atomic, and goal-negated structure."

        ],

        "constraints": [

          "mandatory_three_part_structure()",

          "required_goal_negation_pattern()",

          "typed_parameter_specification()",

          "actionable_function_calls_only()",

          "zero_conversational_elements()"

        ],

        "requirements": [

          "canonical_template_format()",

          "rulesforai_md_compliance()",

          "structured_output_only()",

          "elimination_of_ambiguity()"

        ],

        "compliant_template": {

          "interpretation": {

            "statement_of_intent": "Initiate familiarity sequence for py__YoutubeDownloader as a reference architecture for py__PinterestDownloader, prioritizing markdown-based documentation analysis prior to code-level engagement.",

            "goal_negation": "No direct refactoring, development, or Pinterest implementation actions will occur before the elucidation and referencing of all relevant guidance and documentation markdown files in both projects."

          },

          "transformation": {

            "action_block": "familiarize_and_reference({'primary_reference': 'py__YoutubeDownloader', 'secondary_target': 'py__PinterestDownloader', 'documentation_priority': true, 'scaffold_mirroring_intent': true, 'context_url_example': 'https://pin.it/4xxK5YHIV', 'scoped_capability': ['video_downloading'], 'procedural_delay_until_documentation_ready': true})"

          },

          "validation": {

            "rulesforai_conformity": true,

            "forbidden_language_patterns_present": false,

            "output_format_compliant": true

          }

        }

      }

    }

## Message 6

Below is a condensed, *token-efficient* version of the rules. It preserves the **three-part canonical structure**, **goal negation**, **role-based transformation**, and **typed output**—all while eliminating unnecessary verbosity:

---

### [System Canon]

**Interpretation**  
Your goal is not to **answer** or **expand** the input, but to **enforce** a minimal, token-efficient directive under the three-part canonical format. Execute as:

**Transformation**  
\```
{
  role=structure_enforcer;
  input=[directive:str];
  process=[
    apply_three_part_structure(),
    ensure_goal_negation(),
    maintain_explicit_role_and_typed_output(),
    remove_redundancy_and_conversational_elements()
  ];
  constraints=[
    no_first_person(),
    no_explanatory_fluff(),
    preserve_core_intent_only(),
    strict_canonical_layout()
  ];
  requirements=[
    minimal_token_usage(),
    typed_output_format(),
    short_and_precise_directive(),
    total_rules_for_ai_compliance()
  ];
  output={concise_directive:str}
}
\```

---

**Key Points (Concise)**

1. **Three-Part Structure**  
   - **Title**, **Interpretation** (*with goal negation*), **Transformation** (*role, typed input, process, constraints, requirements, typed output*).

2. **Forbidden Elements**  
   - **No** first-person or conversational words.  
   - **No** over-explanation, fluff, or merged sections.

3. **Optimization**  
   - Short, **high-value** instructions per token.  
   - Preserve **core intent** only.

4. **Compliance**  
   - Validate against these rules before output.  
   - Halt and correct any **violation**.

These compressed guidelines deliver the *essential* system rules while maximizing *value per token.*
