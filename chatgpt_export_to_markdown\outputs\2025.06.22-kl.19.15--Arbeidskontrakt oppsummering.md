# Arbeidskontrakt oppsummering

Conversation ID: 68583a30-6b90-8008-a7ef-569003d7c3fe

## Message 1

# KONTEKST



Gjør det kjent med arbeidskontrakt basert på dokumentet beskrevet nedenfor (inkludert lenker):



    Et annet ord for arbeidskontrakt, er arbeidsavtale. Alle som jobber, som er arbeidstakere, skal ha en arbeidskontrakt. Det gjelder selv om du bare jobber midlertidig. Hvis du og arbeidsgiveren ikke er enige om arbeidstiden din, arbeidsoppgavene eller andre forhold med jobben din, kan dere se i kontrakten. Dere bør signere to like kontrakter, og beholde en del hver. På den måten kan ingen endre avtalen i etterkant.



    ## INNHOLD I ARBEIDSKONTRAKTEN



    Arbeidskontrakten skal være skriftlig. Det er arbeidsgivers ansvar å lage en arbeidskontrakt. Hensikten med kontrakten er at du skal vite hva slags arbeidsoppgaver du har, når du skal jobbe og hvor mye lønn du skal ha. I tillegg skal den inneholde:



    -   Ditt navn og navnet på arbeidsplassen.

    -   En beskrivelse av arbeidet du skal gjøre, og hvilken stilling du har.

    -   Når arbeidsforholdet begynner.

    -   Dersom du har prøvetid, skal det står noe om det også.

    -   Ferie, feriepenger og hvordan det blir bestemt når du skal ha ferie.

    -   Regler for oppsigelse.

    -   Hvor mye lønn du skal få (og eventuelt pensjonsinnbetaling, kostgodtgjørelse og andre ting).

    -   Når og hvordan lønnen blir utbetalt.

    -   Hvordan du skal jobbe i løpet av en dag og i løpet av en uke.

    -   Hvor lange pauser du skal ha.

    -   Hvis det er fleksitid, må det stå noe om det også.

    -   Opplysninger om tariffavtaler.



    Hvis du er midlertidig ansatt, skal kontrakten også inneholde informasjon om hvor lenge du skal jobbe. Det må også stå hvorfor du er midlertidig ansatt.



    Ingen arbeidsforhold er like. Noen jobber fem dager i uken, andre jobber bare noen timer hver uke. Derfor er alle arbeidskontrakter forskjellige.



    Hvis du trenger hjelp til å forstå arbeidskontrakten din, kan du kontakte Arbeidstilsynet. De har ansvaret for at alle følger reglene som gjelder for kontrakter. Hos Arbeidstilsynet kan du også finne eksempler på arbeidskontrakter.



    ### Nyttige lenker



    -   Du kan lese mer om hva en arbeidskontrakt skal inneholde på [nettsidene til Arbeidstilsynet](https://www.arbeidstilsynet.no/arbeidsforhold/arbeidsavtale/).



    ## HVEM SKAL HA ARBEIDSKONTRAKT?



    Alle som utfører arbeid skal ha arbeidskontrakt. Den skal være skriftlig. Det er arbeidsgiveren sitt ansvar at det finnes en kontrakt. Det er også vanlig at arbeidsgiveren lager utkastet til arbeidskontrakt. Når du får kontrakten, er det viktig å lese nøye igjennom hele kontrakten. Dersom det er noe du ikke forstår, må du ikke undertegne kontrakten.



    Noen ganger skal man ha et kort arbeidsforhold. Kanskje man bare skal hjelpe til noen uker før jul, eller ha en midlertidig sommerjobb. Da må arbeidskontrakten være inngått før arbeidet starter. Hvis du skal jobbe et sted lenger enn en måned, holder det at arbeidskontrakten er inngått senest en måned etter at du begynte å jobbe. Det er lovens grenser, men det kan likevel være lurt å inngå avtalen med en gang, så blir det ingen uenigheter.



    Noen ganger endrer arbeidsforholdet seg. Kanskje man får ny lønn eller får nye arbeidsoppgaver. Da må kontrakten endres. En enkel måte å gjøre dette på, er å lage vedlegg. Da slipper man skrive en hel kontrakt på nytt.



    ### Nyttige lenker



    -   På nettsidene til Arbeidstilsynet kan du se et eksempel på [arbeidskontrakt på mange forskjellige språk](https://www.arbeidstilsynet.no/arbeidsforhold/arbeidsavtale/maler-for-arbeidsavtaler/).



# SCENARIO



Jeg er i ferd med å lage en arbeidskontrakt for en ny ansatt, og vurderer nå finne en bedre måte å gjøre dette på (enn å manuelt editere en .docx fil). Dette er fordi det er et veldig lite hensiktmessig valg (å bruke word, libreoffice, e.l.) når det kommer til fleksibilitet, og representerer et unødvendig mellomledd (f.eks. i kontekst av å generere arbeidskontrakt), men også et svært lite fremtidsrettet valg med tanke at; det er et "dumt" og utdatert program som fortsatt eksisterer nesten utelukkende fordi de dannet markedsdominans, og har en "kundegruppe" som representerer de lengst bak når det kommer til teknologi. Det at man må "lære et verktøy" for å kunne generere en ansettelskontrakt (f.eks. basert på forskjellige maler) er komplett unødvendig, fordi nå har eksplosjonen av utvikling gjort at; alle problemer er allerede løst, nå handler det først og fremst om gode idèer.



Jeg er en utvikler, og har kjenskap til alle platformer og språk - nå er mitt mål å definere den beste konseptuelle strukturen for å generere disse arbeidskontraktene via. kode (som kan åpnes i et tekstprogram). Jeg kommer til å bruke mitt nåværende scenario som "pilot", som er å lage en kortfattet ansettelseskontrakt for bruk i firmaet __Ringerike Landskap As__. Kontrakten skal oppfylle minimumskravene i arbeidsmiljøloven § 14-6, samtidig som den i fullstendig versjon kan representered på **èn** A4-side (inkludert underskrift). Før jeg bestemmer meg for hvilken metode jeg kommer til å bruke for den fullstendige prossen, så starter jeg med å lage den i markdown slik at jeg enkelt kan definere teksten først. En fordel med markdown er at det kan genereres html fra de, noe som gjør at det er veldig enkelt å "prototype" på denne måten. Nedenfor har jeg lagt ved essensiell informasjon om firmaet:

```



### File Structure



```

├── README.md

├── 01_core_objectives.md

├── 02_company_profile.md

├── 03_legal_framework.md

├── 04_content_structure.md

├── 05_design_principles.md

├── 06_implementation_insights.md

└── 07_contract_template.md

```



---



#### `README.md`



```markdown

    # Arbeidskontrakt v6 - Markdown-First Foundation



    ## Overview



    This directory contains the distilled essence of the arbeidskontrakt project, focusing on a markdown-first approach for generating Norwegian employment contracts for Ringerike Landskap AS.



    ## File Structure (Sequential Order)



    1. **`01_core_objectives.md`** - Primary goals and success criteria

    2. **`02_company_profile.md`** - Ringerike Landskap AS business context

    3. **`03_legal_framework.md`** - Norwegian employment law requirements

    4. **`04_content_structure.md`** - Validated contract section organization

    5. **`05_design_principles.md`** - Guidelines for implementation approach

    6. **`06_implementation_insights.md`** - Lessons learned from v1-v5

    7. **`07_contract_template.md`** - Foundational markdown contract template



    ## Key Principles



    1. **Content First**: Perfect the markdown content before considering output formats

    2. **Legal Compliance**: Meet all Arbeidsmiljøloven § 14-6 requirements

    3. **Single Page**: Fit complete contract on one A4 page including signatures

    4. **Flexibility**: Support both permanent and temporary employment

    5. **Simplicity**: Learn from v5's success - avoid over-engineering



    ## Strategic Value



    - **Decontextualized**: All content abstracted from implementation details

    - **Modular**: Each file addresses a single conceptual component

    - **Actionable**: Ready for immediate markdown-based development

    - **Future-Resilient**: Foundation supports multiple output formats

    - **Chain-Expandable**: Structured for systematic enhancement



    ## Evolution Summary



    - **v1-v2**: Over-engineered PDF solutions

    - **v3-v4**: Markdown prototyping and content refinement

    - **v5**: Simple python-docx success

    - **v6**: Markdown-first foundation with format flexibility



    ## Next Steps



    This foundation is prepared for:

    - Content refinement and legal validation

    - Template variable substitution logic

    - Multiple output format generation (HTML, PDF, DOCX)

    - Interactive data collection interface



    **The distilled wisdom**: Start simple, focus on content accuracy, and build only what's needed.

```



---



#### `01_core_objectives.md`



```markdown

    # Core Objectives



    ## Primary Goal

    Generate legally compliant Norwegian employment contracts (arbeidskontrakt) for Ringerike Landskap AS that meet all requirements while fitting on a single A4 page.



    ## Legal Compliance Requirements

    - Must satisfy Arbeidsmiljøloven § 14-6 minimum requirements

    - Include all mandatory sections for Norwegian employment law

    - Support both permanent and temporary/seasonal employment types

    - Maintain legal precision while ensuring readability



    ## Business Context

    - **Company**: Ringerike Landskap AS (landscaping/construction)

    - **Industry**: Anleggsgartner (landscape gardening) and grunnarbeid (groundwork)

    - **Work locations**: Ringerike and surrounding areas, project-based

    - **Employment patterns**: Both permanent staff and seasonal workers



    ## Format Constraints

    - Single A4 page including signatures

    - Professional appearance suitable for legal documents

    - Clear structure for both employer and employee understanding

    - Flexible enough to accommodate different employment scenarios



    ## Success Criteria

    - Legal compliance verified

    - Practical usability for HR processes

    - Professional presentation

    - Efficient generation workflow

```



---



#### `02_company_profile.md`



```markdown

    # Company Profile: Ringerike Landskap AS



    ## Official Company Information

    - **Name**: Ringerike Landskap AS

    - **Organization Number**: ***********

    - **Address**: Birchs vei 7, 3530 Røyse

    - **Business Type**: Anleggsgartner og maskinentreprenør



    ## Business Operations

    - **Primary Services**: Landscaping, groundwork, construction support

    - **Typical Projects**: Private outdoor spaces, retaining walls, drainage, machine contractor work

    - **Geographic Scope**: Ringerike and surrounding municipalities

    - **Work Pattern**: Project-based with seasonal variations



    ## Employment Characteristics

    - **Typical Roles**: Anleggsgartner, grunnarbeider, fagarbeider

    - **Work Schedule**: 37.5 hours/week, typically 07:00-15:00

    - **Compensation Structure**: Hourly wage (baseline 300 NOK/hour)

    - **Travel Requirements**: Use of personal vehicles with mileage compensation

    - **Seasonal Considerations**: Higher activity in construction season



    ## Industry Context

    - **Sector**: Construction and landscaping services

    - **Regulatory Environment**: Norwegian labor law, safety regulations

    - **Professional Requirements**: HMS training, equipment certification

    - **Market Position**: Growing regional contractor

```



---



#### `03_legal_framework.md`



```markdown

    # Legal Framework for Norwegian Employment Contracts



    ## Arbeidsmiljøloven § 14-6 Requirements

    Mandatory contract elements that must be included:



    ### 1. Party Information

    - Full names and addresses of employer and employee

    - Organization number for employer

    - Personal identification for employee



    ### 2. Employment Details

    - Job title and description of work

    - Start date of employment

    - Duration (if temporary employment)

    - Justification for temporary employment (if applicable)

    - Probation period terms (if applicable)



    ### 3. Workplace Information

    - Primary work location

    - Indication if work is performed at multiple locations

    - Travel requirements and compensation



    ### 4. Working Conditions

    - Weekly working hours

    - Daily schedule and break arrangements

    - Overtime compensation terms

    - Flexibility arrangements



    ### 5. Compensation Structure

    - Salary amount and payment method

    - Payment schedule and dates

    - Additional compensation (overtime, travel, etc.)

    - Pension and insurance arrangements



    ### 6. Leave and Benefits

    - Vacation entitlement (5 weeks standard)

    - Vacation pay percentage (12%)

    - Sick leave arrangements

    - Other statutory benefits



    ### 7. Termination Provisions

    - Notice periods for both parties

    - Termination procedures

    - Special provisions for probation period



    ### 8. Additional Terms

    - Collective agreement status

    - Confidentiality requirements

    - Equipment and safety obligations

    - Regulatory compliance references



    ## Key Legal References

    - **Arbeidsmiljøloven**: Primary employment law

    - **Ferieloven**: Vacation and vacation pay regulations

    - **Folketrygdloven**: Social security and benefits

    - **Tariffavtaler**: Collective agreements (if applicable)

```



---



#### `04_content_structure.md`



```markdown

    # Contract Content Structure



    ## Validated Section Organization

    Based on legal requirements and practical testing:



    ### Header Section

    - Contract title

    - Company information block

    - Employee information block (with fill-in fields)



    ### 1. Employment Relationship (Ansettelsesforhold)

    - Start date

    - Employment type (permanent/temporary)

    - Duration and justification (if temporary)

    - Probation period terms



    ### 2. Workplace (Arbeidssted)

    - Primary location: Ringerike and surrounding areas

    - Project-based work arrangement

    - Meeting point coordination



    ### 3. Position and Tasks (Stilling og oppgaver)

    - Job title field

    - Core responsibilities: landscaping and groundwork

    - Additional duties within company scope



    ### 4. Working Hours (Arbeidstid)

    - Standard: 37.5 hours/week

    - Schedule: 07:00-15:00 typical

    - Break arrangements per AML

    - Flexibility provisions



    ### 5. Salary and Compensation (Lønn og godtgjørelse)

    - Hourly rate: 300 NOK baseline

    - Payment date: 5th of each month

    - Overtime provisions per AML § 10-6

    - Travel compensation: state rates



    ### 6. Vacation and Vacation Pay (Ferie og feriepenger)

    - 5 weeks vacation per ferieloven

    - 12% vacation pay

    - Payment timing for short-term employment



    ### 7. Termination (Oppsigelse)

    - Probation period: 14 days

    - Post-probation: 1 month mutual



    ### 8. Miscellaneous (Diverse)

    - Equipment provision by employer

    - Instruction compliance

    - No collective agreement status



    ### Signature Section

    - Date and location

    - Employer signature line

    - Employee signature line

```



---



#### `05_design_principles.md`



```markdown

    # Design Principles for Contract Generation



    ## Content-First Approach

    - **Markdown Foundation**: Start with structured markdown for content clarity

    - **Format Flexibility**: Enable multiple output formats (HTML, PDF, DOCX) from single source

    - **Legal Precision**: Prioritize accuracy and compliance over aesthetic concerns

    - **Readability**: Ensure contracts are accessible to both legal and non-legal readers



    ## Structural Guidelines



    ### Brevity and Clarity

    - One A4 page maximum including signatures

    - Essential information only - eliminate redundancy

    - Clear section headers and logical flow

    - Concise language while maintaining legal validity



    ### Visual Organization

    - **Tables**: Use markdown tables for structured data presentation

    - **Emphasis**: Bold for critical terms and amounts

    - **Separation**: Clear visual breaks between sections

    - **Fill-in Fields**: Consistent formatting for variable content



    ### Modularity

    - **Reusable Components**: Standardized sections that can be combined

    - **Variable Content**: Clear separation of fixed vs. customizable elements

    - **Template Logic**: Support for conditional content (permanent vs. temporary)

    - **Validation Points**: Built-in checks for required information



    ## Technical Considerations



    ### Markdown Advantages

    - Version control friendly

    - Human readable in source form

    - Multiple rendering options

    - Easy content iteration and refinement

    - Separation of content from presentation



    ### Output Format Flexibility

    - **HTML**: Web preview and browser printing

    - **PDF**: Official document archival

    - **DOCX**: Microsoft Word compatibility

    - **Plain Text**: Fallback and accessibility



    ### User Experience

    - **Interactive Input**: Guided data collection

    - **Validation**: Real-time checking of required fields

    - **Preview**: Show formatted result before finalization

    - **Export Options**: Multiple format choices based on need



    ## Quality Assurance

    - **Legal Review**: Regular validation against current law

    - **Practical Testing**: Real-world usage verification

    - **Stakeholder Feedback**: Input from HR and legal users

    - **Continuous Improvement**: Iterative refinement based on usage patterns

```



---



#### `06_implementation_insights.md`



```markdown

    # Implementation Insights from Previous Iterations



    ## Evolution Pattern Analysis



    ### v1-v2: Over-Engineering Phase

    - **Lesson**: Complex PDF generation frameworks created unnecessary complexity

    - **Issue**: Multiple library dependencies without clear benefit

    - **Result**: Functional but overly complicated solutions



    ### v3-v4: Content Refinement Phase

    - **Breakthrough**: Markdown-first approach for content development

    - **Success**: Clear separation of content from presentation concerns

    - **Validation**: Legal content structure solidified through iteration



    ### v5: Simplification Success

    - **Achievement**: Working solution with minimal dependencies

    - **Technology**: python-docx proved sufficient for DOCX generation

    - **User Experience**: Interactive CLI provided practical usability



    ## Key Technical Learnings



    ### What Works

    - **Single Purpose Libraries**: python-docx for DOCX, simple and reliable

    - **Interactive Prompts**: User-friendly data collection

    - **Template-Based Generation**: Structured approach to content insertion

    - **Minimal Dependencies**: Fewer moving parts, more reliable operation



    ### What Doesn't Work

    - **Complex PDF Engines**: Over-engineered solutions for simple requirements

    - **Multiple Format Libraries**: Unnecessary complexity for single output need

    - **Analysis Paralysis**: Extensive reverse-engineering without clear benefit

    - **Framework Building**: Creating abstractions before understanding requirements



    ## Strategic Insights



    ### Simplicity Principle

    - Start with the simplest solution that meets requirements

    - Add complexity only when clearly justified

    - Prefer proven, stable libraries over cutting-edge alternatives

    - Focus on user needs rather than technical elegance



    ### Content-First Development

    - Establish legal content accuracy before technical implementation

    - Use markdown for rapid content iteration

    - Separate content structure from output formatting

    - Validate with stakeholders early and often



    ### User-Centered Design

    - Interactive mode essential for practical adoption

    - Clear error messages and guidance

    - Flexible input handling (optional vs. required fields)

    - Multiple output options to meet different use cases



    ## Recommended v6 Approach



    ### Phase 1: Content Foundation

    - Perfect markdown template with all legal requirements

    - Validate content structure with legal review

    - Test readability and completeness



    ### Phase 2: Generation Logic

    - Implement simple template substitution

    - Add interactive data collection

    - Create validation and error handling



    ### Phase 3: Output Formats

    - Start with HTML rendering for preview

    - Add PDF generation if needed

    - Consider DOCX for compatibility

    - Maintain markdown as source of truth



    ### Success Metrics

    - Legal compliance verified

    - User adoption in real scenarios

    - Maintenance simplicity

    - Output quality and professionalism

```



---



#### `07_contract_template.md`



```markdown

    # ARBEIDSAVTALE



    ## Parter



    | **Arbeidsgiver** | **Arbeidstaker** |

    |------------------|------------------|

    | **Ringerike Landskap AS** | **Navn:** _________________________ |

    | **Org.nr:** *********** | **Adresse:** ______________________ |

    | **Adresse:** Birchs vei 7, 3530 Røyse | **Fødselsdato:** ___________________ |



    ---



    ## 1. Ansettelsesforhold



    | Element | Detaljer |

    |---------|----------|

    | **Startdato** | _________________________ |

    | **Type** | ☐ Fast ☐ Midlertidig t.o.m. _____________ |

    | **Grunnlag** (hvis midlertidig) | _________________________ |

    | **Prøvetid** | ☐ Ingen ☐ _____ måneder (maks 6) |



    ---



    ## 2. Arbeidssted



    Ringerike og omegn, oppmøtested etter prosjekt.



    ---



    ## 3. Stilling og oppgaver



    | **Stilling** | _________________________________ |

    |--------------|-----------------------------------|

    | **Oppgaver** | Anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten. |



    ---



    ## 4. Arbeidstid



    37,5 t/uke, normalt kl. 07–15. Pauser og fleksibilitet ihht. AML.



    ---



    ## 5. Lønn og godtgjørelse



    | Element | Detaljer |

    |---------|----------|

    | **Timesats** | Kr 300,- |

    | **Utbetaling** | Den 5. hver måned |

    | **Overtid** | Etter AML § 10-6 |

    | **Kjøring** | Statens satser |



    ---



    ## 6. Ferie og feriepenger



    5 uker ferie iht. ferieloven. Feriepenger 12%. Utbetales ved fratreden ved kort ansettelse.



    ---



    ## 7. Oppsigelse



    | Periode | Frist |

    |---------|-------|

    | **I prøvetid** | 14 dager |

    | **Etter prøvetid** | 1 måned gjensidig |



    ---



    ## 8. Diverse



    Arbeidstaker følger instrukser, arbeidsgiver leverer arbeidstøy/verktøy. Ingen tariffavtale pr. dato.



    ---



    ## Signatur



    | **Sted/dato:** Røyse, _____________ | |

    |-----------------------------------|---|

    | **Arbeidsgiver:** ________________ | **Arbeidstaker:** ________________ |



    ---



    *Arbeidsavtale i henhold til arbeidsmiljøloven. Begge parter beholder kopi.*

```



Her er nåværende versjon:



```

* __Arbeidsgiver:__

    * Ringerike Landskap AS

    * ***********

    * Birchs vei 7, 3530 Røyse



* __Arbeidstaker:__

    * Navn: `________________________________`

    * Adresse: `_____________________________`

    * Fødselsdato: `_________________________`



* __Oppstart:__

    * **Startdato:** `__.__.____`

    * **Ansettelsestype:** `☐` Fast `☐` Midlertidig

    * Hvis midlertidig, varighet: `_________________________________________`

    * Grunnlag for midlertidig ansettelse: `________________________________`

    * **Prøvetid:** `☐` Ingen prøvetid `☐ _____` måneder (maks 6 måneder)

    * Hvis prøvetid gjelder `14 dagers gjensidig oppsigelsesfrist`.



* __Arbeidssted:__

    * Ringerike og omegn; oppmøtested avtales per prosjekt.



* __Arbeidsoppgaver:__

    * Stillingstittel: `____________________________________________________________`

    * Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten.



* __Lønn:__

    * Timesats: **kr 300,-**.

    * Lønn utbetales til konto nr.: `_________________________` den 5. i påfølgende måned.

    * Overtidsgodtgjørelse iht. AML § 10-6 (40 % tillegg).

    * Kjøring med egen bil: Statens skattefrie sats (pt. 3,50 kr/km).

    * Pensjonsinnbetaling skjer i obligatorisk tjenestepensjon (OTP).



* __Arbeidstid:__

    * Ordinær arbeidstid er mandag–fredag kl. 07:00 – 15:00.

    * Det forekommer per i dag ikke skift-, natt- eller søndags-arbeid.

    * Endringer krever skriftlig avtaletillegg før iverksettelse (§ 14-8).

    * Minst 30 min. pause pr. dag ved arbeidsdag >5,5 t.



* __Overtid:__

    * Tillegg 40 % av timelønn iht. AML § 10-6.



* __Kjøregodtgjørelse:__

    * Kjøring i tjenesten med egen bil godtgjøres etter statens trekkfrie sats (pt. 3,50 kr/km)..



* __Ferie:__

    * 5 uker ferie + 12 % feriepenger. Ferietidspunkt fastsettes av arbeidsgiver etter drøfting, jf. ferieloven § 6.



* __Pensjon og forsikring:__

    * Obligatorisk tjenestepensjon i **Storebrand** (org.nr. 958 995 369).

    * Yrkesskadeforsikring i **Gjensidige Forsikring ASA** (org.nr. 995 568 217).



* __Fravær:__

    * Betalt fravær: Sykepenger/foreldrepenger etter folketrygdloven; arbeidsgiver dekker full lønn i arbeidsgiverperioden (16 dager).



* __Kompetanse:__

    * Arbeidsgiver dekker påkrevd HMS- og maskinopplæring; øvrig kompetanseutvikling avtales individuelt (§ 14-6 p).



* __Oppsigelse:__

    * Under prøvetid: 14 kalenderdager.

    * Etter prøvetid: 1 måned gjensidig.

    * Oppsigelse skal skje skriftlig; prosedyre følger AML §§ 15-1 — 15-4.



* __Diverse:__

    * Arbeidsforholdet er ikke regulert av tariffavtale.

    * Arbeidsforholdet er for øvrig regulert av bestemmelsene i arbeidsmiljøloven.

    * Endringer i arbeidsforholdet dokumenteres skriftlig som tillegg til denne avtalen.

```



# OPPGAVE



Vennligst gjør deg kjent med vedlagt informasjon og foreslå en komplett versjon av arbeidskontrakten som er tilpasset Ringerike Landskap As. Kontrakten skal oppfylle minimumskravene i arbeidsmiljøloven § 14-6, samtidig som den i fullstendig versjon kan representeres på **èn** A4-side (inkludert underskrift). Det er viktig at kontrakten både fungerer som lettleselig og oversiktlig når vist som ren tekst, men også at den skrives på en måte som gjør at det ser pent ut som html.



Oppsummering av krav:

- Får plass på én A4-side

- Oppfyller alle juridiske minstekrav

- Er spesialtilpasset **Ringerike Landskap AS**

- Egner seg for både fast og midlertidig (inkl. sesongbasert) ansettelse

- Pent og strukturert formatering både i tekstfil og når rendret som html

## Message 2

I have provided a concise, one-page contract template (in pure Markdown) that covers every point Arbeidsmiljøloven § 14-6 requires—party details, varighet/grunnlag ved midlertidighet, arbeidssted, oppgaver, arbeidstid, lønn, ferie, oppsigelse, m.m.—and reflects the July 2024 tightening of information-duties (bl.a. stillingsandel og klarere prøvetids-vilkår). The template also embeds 2025-satser for kjøregodtgjørelse (kr 5,00/km) and holder 12 % feriepenger, 37,5 t uke m/fradragsfri pause, 40 % overtidstillegg, samt tydelig OTP- og yrkesskade-dekning. You can paste it directly into any `.md` file, render it to HTML, or feed it to a static-site/PDF pipeline without further tweaks.

---

## 1  Kildene som styrer kontrakten

| Nøkkeltema | Sentrale rettskilder |
|------------|---------------------|
| Minstekrav til innhold | AML § 14-6 bokstav a–l ─ skriftlig avtale, alle vesentlige vilkår citeturn4search0 |
| Frist for å lage avtalen | AML § 14-5 (senest 7 dager) citeturn4search0 |
| Nytt informasjons­krav (fra 1. juli 2024) | NHO/Arbinn veiledning citeturn0search6 |
| Ferie/Feriepenger | Ferieloven §§ 4–11, sats 12 % citeturn0search2turn0news60 |
| Arbeidstid og pauser | AML kap. 10 + Arbeidstilsynet om 37,5 t avtalt uke citeturn2search1 |
| Overtidstillegg | AML § 10-6 (min. 40 %) citeturn2search1turn0search4 |
| Kjøregodtgjørelse 2025 | Statens trekkfrie sats kr 5,00/km citeturn2search0turn2search3 |
| Prøvetids-oppsigelse 14 d | AML § 15-3 (7) og juridisk praksis citeturn2search2turn2search5 |
| OTP-plikt | Skatteetaten veiledning citeturn0search5 |
| Mal-struktur | Arbeidstilsynet & Signform-maler citeturn0search0turn0search1 |

---

## 2  Design-valg for én A4-side

1. **Ultra-kompakt header & tabeller** – redusert whitespace sikrer at alle §§ får plass på < 4 000 tegn (≈ ½ A4) selv etter utfylling.  
2. **Checkbox-logikk** – én avtale håndterer fast og midlertidig ansettelse uten separate maler.  
3. **Én linje pr. lovpålagt punkt** – samsvarer direkte mot § 14-6-listen, så HR eller tilsyn lett kan krysse av.  
4. **Klart skille mellom faste og variable data** (underscores/blanklinjer) gjør programmatisk flette‐inn av JSON-variabler til ren string-substitution.  
5. **Markdown-konform avsnitts­deling** og korte, respons-vennlige tabeller sikrer pen HTML-rendring i alle engines (GitHub, Pandoc, Statisk-site, etc.).  

---

## 3  Ferdig kontraktsmal (kopier → `07_contract_template.md`)

```markdown
# ARBEIDSAVTALE

**Ringerike Landskap AS** – Org.nr. ***********  
Birchs vei 7, 3530 Røyse  

---

## Parter
| Arbeidsgiver | Arbeidstaker |
|--------------|--------------|
| Ringerike Landskap AS | Navn: ___________________________ |
| Org.nr.: *********** | Adresse: _________________________ |
| | Fødselsdato (DD.MM.ÅÅÅÅ): _______ |

---

## 1  Ansettelsesforhold
| Felt | Utfylling |
|------|-----------|
| Startdato | _______________ |
| Stillingsprosent | _____ % |
| Stilling | ___________________________ |
| Ansettelsestype | ☑ Fast   ☐ Midlertidig til: ________ |
| Grunnlag midl. | ___________________________ |
| Prøvetid | ☐ Ingen   ☐ _____ mnd (maks 6) |

---

## 2  Arbeidssted
Primært Ringerike og nærliggende kommuner. Oppmøtested avtales pr. prosjekt.  
Arbeid kan forekomme på flere kundeadresser.

---

## 3  Arbeidsoppgaver
Landskaps- og grunnarbeid inkl. maskinføring, steinsetting, drenering samt øvrige oppgaver naturlig tilknyttet virksomheten. Endringer kan skje innen tilsvarende fagområde etter behov.

---

## 4  Arbeidstid
37,5 t per uke, normalt kl. 07:00–15:00 (Man–Fre).  
Min. 30 min pause ved dag > 5,5 t. Overtid og fleksitid etter AML kap. 10.

---

## 5  Lønn og godtgjørelse
| Element | Sats / vilkår |
|---------|---------------|
| Timelønn | kr ____________ |
| Utbetaling | 5. hver måned til konto nr. __________________ |
| Overtidstillegg | 40 % iht. AML § 10-6 |
| Reise egen bil | Statens sats kr 5,00/km |
| Pensjon | OTP i **Storebrand** |
| Forsikring | Yrkesskade i **Gjensidige** |

---

## 6  Ferie og feriepenger
5 uker ferie (ferieloven). Feriepenger 12 %. Opptjente feriepenger utbetales ved fratreden hvis arbeidsforholdet varer < 1 år.

---

## 7  Sykdom og permisjon
Rettigheter etter folketrygdloven. Arbeidsgiver betaler sykepenger i arbeidsgiverperioden (16 dager).

---

## 8  Oppsigelse
| Periode | Frist |
|---------|-------|
| I prøvetid | 14 kal.dager |
| Etter prøvetid | 1 måned |

Oppsigelse skal gis skriftlig (AML kap. 15).

---

## 9  Diverse bestemmelser
Ingen tariffavtale gjelder p.t. Endringer dokumenteres skriftlig som tillegg til denne avtalen.  
Arbeidstaker skal følge alle HMS-rutiner; arbeidsgiver stiller nødvendig verne- og arbeidsutstyr.

---

## Signaturer
| Sted/dato: Røyse, ______________ | |
|---------------------------------|---|
| **Arbeidsgiver:** _____________________ | **Arbeidstaker:** _____________________ |

*Arbeidsavtalen er inngått i to eksemplarer – ett til hver part.*
```

---

### Slik kan du generere kontrakten programmatisk

1. **Lag et JSON-objekt** med alle felt (navn, konto, type, lønn m.m.).  
2. **Bruk en enkel string-template-engine** (f.eks. `str.format`, Jinja2, eller f-string) til å erstatte blankfeltene/checkbox-logikken.  
3. **Markdown → HTML/PDF/DOCX**: Pandoc eller `python-docx` (v5-erfaringen) for hurtig konvertering.  
4. **Inkluder validering** (manglende felt, stillingsprosent ≤ 100 %, startdato format) før generering.  
5. **Hold `07_contract_template.md` som «source of truth»**; bygg nye utgaver fra Git-taggede commits etter endringer i lov/firmaopplysninger.

Med denne mal-strukturen kan du i praksis gå fra dataskjema til ferdig PDF-kontrakt på under et sekund – helt uten «dumme» kontorprogram-avhengigheter. Lykke til med automatiseringen!
