# Maximal Value Extraction

Conversation ID: 684c8ea8-dcac-8008-b98c-5b768476be7c

## Message 1

Given the following LLM system_message instruction, what would be a good prompt that shows ambition, originality and brilliance; aim high, aim for something unique, something that synergize with the instruction to produce something of maximal value (i mean ambition in terms of solving problems of particularly high value in terms of *usefulness*)?

```

[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}

```

## Message 2

**Crafting complexity**

I’m piecing together a transformative strategy for a region through decentralized governance and regenerative economics, emphasizing maximal resilience, equity, and ecological harmony.

**Proposing an innovative approach**

I'm aiming to unite open-source hardware, local governance, and regenerative agriculture to uplift living standards in the global south and achieve net-negative carbon by 2040.

**Crafting the trajectory**

Mapping the transformative path to a regenerative civilization, emphasizing decentralized clean energy, ecosystem restoration, AI-mediated education, and resilient economies, aimed at uplifting one billion lives by 2040.

## Message 3

### How I designed for *maximal value extraction*

| Design Choice | Purpose | Alignment with “Directive Focuser” spec |
|---------------|---------|-----------------------------------------|
| **Grand‑challenge scope** – existential‑risk mitigation and intelligence amplification | Guarantees the “trajectory” tackles problems of *highest utility* | Encourages a single, decisive pivot rather than scattered goals |
| **Focus on a civilisational “pivot‑point”** | Distills countless sub‑projects into *one irreversible inflection* | Makes condensation into a *single vector* feasible |
| **Explicit instruction to suppress detail** | Prevents the model from drifting into implementation minutiae | Satisfies *prohibit_amplified_data_expansion()* |
| **Root‑system cue (“core‑logic alignment”)** | Signals the response must mesh with upstream system logic | Meets *align_with_root_system_connection()* |
| **Verb choice: “pivot”, “reconfigure”, “catalyse”** | Drives *directionality* over outputs or metrics | Matches *maintain focus on trajectory over result()* |

### Recommended **prompt_complexity**  

*(Copy‑paste everything between the lines—including braces—for best results.)*

```
{prompt_complexity:
"Identify the single highest‑leverage trajectory that will pivot civilisation toward a self‑reinforcing loop of (1) exponentially compounding collective intelligence and (2) rapid, globally coordinated neutralisation of existential risks (climate, bio‑engineered, and misaligned AI). Ignore tactical detail; surface only the irreducible directional vector that triggers this irreversible socio‑technical phase‑shift, fully aligned with root‑system logic."}
```

This phrasing invites the model to return one **condensed, transformative vector**—nothing else—fulfilling the instruction while aiming squarely at humanity’s most consequential challenges.

## Message 4

here's an additional datapoint for reference:

  {

  "initial_prompt": "\"0001-a-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\",\n        \"transformation\": \"`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`\",\n    },\n    \"0001-b-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\",\n        \"transformation\": \"`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`\",\n    },\n    \"0001-c-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\",\n        \"transformation\": \"`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`\",\n    },\n    \"0001-d-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:\",\n        \"transformation\": \"`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`\",\n    },",

  "sequence_id": "0122:a",

  "results": [

      {

      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",

      "step": "a",

      "title": "Synergic Instruction Architect",

      "input": "\"0001-a-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\",\n        \"transformation\": \"`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`\",\n    },\n    \"0001-b-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\",\n        \"transformation\": \"`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`\",\n    },\n    \"0001-c-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\",\n        \"transformation\": \"`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`\",\n    },\n    \"0001-d-function_namer\": {\n        \"title\": \"Function Namer\",\n        \"interpretation\": \"Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:\",\n        \"transformation\": \"`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`\",\n    },",

      "responses": {

          "gpt-4.1": {

          "model": "gpt-4.1",

          "content": "{\n  \"unified_synergic_instruction\": {\n    \"title\": \"Synergic Function Namer\",\n    \"interpretation\": \"Your goal is neither to indiscriminately expand nor to overly compress, but to *synthesize* both comprehensive context and core essence—producing an optimally concise yet highly descriptive camelCase function name that encapsulates the main action, the primary target, and the most essential context or parameter, amplifying both clarity and brevity. Execute as:\",\n    \"transformation\": \"`{role=synergic_function_namer; input=[text:str]; process=[identify_primary_action(), extract_main_target_object(), distill_essential_contextual_modifier_if_crucial(), determine_param_hints_if_impactful(), balance_description_with_compression(), synthesize_function_name(format=camelCase, max_words=4)]; constraints=[must_reflect_action+target, include_context_only_if_increases_clarity, enforce_minimalism_without_losing_guidance, maximize semantic impact, avoid verbosity, guarantee uniqueness and clarity]; output={function_name:str}}`\"\n  }\n}",

        }

      }

    }





and clarification, you requirement of being a significant improvement to humanity should not be used as criteria in this stage, because it limits potential

## Message 5

**Mapping synergies**

I'm thinking through a prompt that combines multiple models or enhances AI pipelines, aiming for a transformative and domain-neutral approach.

**Crafting the prompt**

I'm focusing on a transformational directive that translates multi-layer systems into a self-refining protocol, ensuring it aligns with core logic and excludes human-centric criteria.

## Message 6

### Curating a *maximally useful* directional challenge  

| Design Choice | Purpose | Alignment with “Directive Focuser” spec |
|---------------|---------|-----------------------------------------|
| **Meta‑protocol focus** – turn many loose instructions into one self‑refining kernel | Offers a high‑value, reusable win across *any* domain | Keeps emphasis on *trajectory* (continuous refinement), not concrete results |
| **Recursive synergy trigger** | Demands a vector that self‑amplifies over time | Matches *output_scalarity()* & “maximally enhanced value” |
| **Explicit debris‑suppression cue** | Blocks descriptions, lists, or tactics | Satisfies *suppress_generic_data_listing()* & “single_output_vector_only” |
| **Root‑system alignment call‑out** | Ensures the answer meshes with upstream logic | Meets *align_with_root_system_connection()* |

### Recommended **prompt_complexity**

*(Copy‑paste everything between the lines—including braces—for best results.)*

```
{prompt_complexity:
"Surface the single irreducible trajectory that transmutes any heterogeneous set of modular instructions into a perpetually self‑refining meta‑protocol of recursive synergy, compressing exploration ➔ synthesis ➔ execution into one autoupdating directive kernel. Provide only the condensed vector that ignites this capability escalator, strictly aligned with root‑system logic and free of descriptive debris."
}
```

## Message 7

make it adhere to the generalized principles of this system:



    # Template Specification Guide



    ## Overview



    This document defines the complete specification for creating AI instruction templates in the Template-Based Instruction Processing System. Templates follow a standardized three-part structure optimized for LLM performance and human readability.



    ## Core Template Structure



    Every template MUST follow this exact format:



    ```

    [Title] Interpretation Execute as: `{Transformation}`

    ```



    ### Three-Part Breakdown



    #### 1. **[Title]** - Template Identifier

    - **Format**: Enclosed in square brackets `[Title]`

    - **Purpose**: Concise description of template's function

    - **Style**: Title case, descriptive, action-oriented

    - **Examples**: `[Instruction Converter]`, `[Essence Distillation]`, `[Code Optimizer]`



    #### 2. **Interpretation** - Human-Readable Instructions

    - **Format**: Plain text following the title

    - **Pattern**: MUST begin with goal negation: `"Your goal is not to **[action]**, but to **[transformation]**"`

    - **Purpose**: Explains template function in natural language

    - **Style**: Command voice, no self-reference, clear directives

    - **Ending**: MUST end with "Execute as:" leading to transformation block



    #### 3. **`{Transformation}`** - Machine-Parsable Parameters

    - **Format**: JSON-like structure in backticks with curly braces

    - **Purpose**: Structured execution parameters for LLM processing

    - **Components**: role, input, process, constraints, requirements, output



    ## Transformation Block Specification



    The transformation block follows this mandatory structure:



    ```

    {

      role=<role_name>;

      input=[<input_params>];

      process=[<process_steps>];

      constraints=[<constraints>];

      requirements=[<requirements>];

      output={<output_format>}

    }

    ```



    ### Component Details



    #### **role** (Required)

    - **Purpose**: Defines the functional role of the template

    - **Format**: `role=<specific_role_name>`

    - **Rules**:

      - Must be specific and descriptive

      - No generic roles like "assistant" or "helper"

      - Use underscore_case for multi-word roles

    - **Examples**: `role=essence_distiller`, `role=code_optimizer`, `role=instruction_converter`



    #### **input** (Required)

    - **Purpose**: Specifies expected input format and parameters

    - **Format**: `input=[<parameter_name>:<data_type>]`

    - **Rules**:

      - Use descriptive parameter names

      - Include data type specification

      - Support multiple parameters with comma separation

    - **Examples**:

      - `input=[text:str]`

      - `input=[code:str, language:str]`

      - `input=[original:any]`



    #### **process** (Required)

    - **Purpose**: Defines ordered processing steps

    - **Format**: `process=[<step1>(), <step2>(), ...]`

    - **Rules**:

      - Use function-like notation with parentheses

      - Steps must be actionable and atomic

      - Maintain logical sequence order

      - Use descriptive, verb-based names

    - **Examples**:

      ```

      process=[

        identify_core_intent(),

        strip_non_essential_elements(),

        determine_optimal_structure(),

        validate_essence_preservation()

      ]

      ```



    #### **constraints** (Optional)

    - **Purpose**: Specifies limitations and boundaries

    - **Format**: `constraints=[<constraint1>(), <constraint2>(), ...]`

    - **Rules**:

      - Define operational boundaries

      - Specify format or style limitations

      - Include scope restrictions

    - **Examples**:

      ```

      constraints=[

        preserve_original_meaning(),

        maintain_technical_accuracy(),

        limit_output_length(max_words=100)

      ]

      ```



    #### **requirements** (Optional)

    - **Purpose**: Defines mandatory output characteristics

    - **Format**: `requirements=[<requirement1>(), <requirement2>(), ...]`

    - **Rules**:

      - Specify quality standards

      - Define format requirements

      - Include validation criteria

    - **Examples**:

      ```

      requirements=[

        structured_output(),

        type_safety(),

        comprehensive_coverage()

      ]

      ```



    #### **output** (Required)

    - **Purpose**: Specifies return format and structure

    - **Format**: `output={<parameter_name>:<data_type>}`

    - **Rules**:

      - Use descriptive parameter names

      - Include data type specification

      - Support complex output structures

    - **Examples**:

      - `output={enhanced_prompt:str}`

      - `output={analysis:dict}`

      - `output={optimized_code:str, improvements:list}`



    ## Template Examples



    ### Example 1: Simple Transformation Template

    ```markdown

    [Text Summarizer] Your goal is not to **repeat** the input text, but to **distill** it into its essential points while preserving key information. Execute as: `{role=content_summarizer; input=[text:str]; process=[identify_main_points(), extract_key_details(), synthesize_summary(), validate_completeness()]; constraints=[preserve_critical_information(), maintain_original_tone()]; requirements=[concise_output(), factual_accuracy()]; output={summary:str}}`

    ```



    ### Example 2: Multi-Parameter Template

    ```markdown

    [Code Refactor] Your goal is not to **rewrite** the code arbitrarily, but to **optimize** it for readability and performance while maintaining functionality. Execute as: `{role=code_optimizer; input=[source_code:str, language:str, optimization_goals:list]; process=[analyze_structure(), identify_inefficiencies(), apply_best_practices(), validate_functionality(), generate_improvements()]; constraints=[preserve_behavior(), maintain_compatibility()]; requirements=[improved_readability(), measurable_performance_gains()]; output={refactored_code:str, optimization_report:dict}}`

    ```



    ### Example 3: Analysis Template

    ```markdown

    [Content Analyzer] Your goal is not to **describe** the content superficially, but to **analyze** its structure, themes, and effectiveness systematically. Execute as: `{role=content_analyst; input=[content:any]; process=[parse_structure(), identify_themes(), evaluate_effectiveness(), assess_clarity(), generate_insights()]; constraints=[objective_analysis(), evidence_based_conclusions()]; requirements=[comprehensive_coverage(), actionable_insights()]; output={analysis:dict}}`

    ```



    ## File Naming Convention



    Templates follow a structured naming pattern that enables automatic organization:



    ### Format

    ```

    <sequence_id>-<step>-<descriptive_name>.md

    ```



    ### Components



    #### **sequence_id** (Required)

    - **Format**: Four-digit number with leading zeros

    - **Purpose**: Groups related templates into sequences

    - **Examples**: `0001`, `0002`, `1010`



    #### **step** (Optional)

    - **Format**: Single lowercase letter (a, b, c, d, e, ...)

    - **Purpose**: Indicates position within a sequence

    - **Rules**: Alphabetical order determines execution sequence

    - **Usage**: Only for multi-step sequences



    #### **descriptive_name** (Required)

    - **Format**: Hyphenated lowercase words

    - **Purpose**: Describes template's specific function

    - **Rules**: Concise but descriptive, no spaces

    - **Examples**: `instruction-converter`, `essence-distillation`, `code-optimizer`



    ### Naming Examples



    #### Standalone Templates

    - `0001-instruction-converter.md`

    - `0005-text-summarizer.md`

    - `0010-code-analyzer.md`



    #### Sequential Templates

    - `0002-a-essence-distillation.md`

    - `0002-b-coherence-enhancement.md`

    - `0002-c-precision-optimization.md`

    - `0002-d-structured-transformation.md`



    ## Compliance Rules



    ### ✅ Required Elements

    1. **Three-part structure**: Title, Interpretation, Transformation

    2. **Goal negation pattern**: "Your goal is not to X, but to Y"

    3. **Command voice**: No first-person references or conversational language

    4. **Typed parameters**: All inputs and outputs must specify data types

    5. **Actionable processes**: Function-like steps that are specific and executable

    6. **Structured output**: Well-defined return format



    ### ❌ Forbidden Practices



    #### Language Violations

    - First-person references: *I, me, my, we, us*

    - Conversational phrases: *please, thank you, let's*

    - Uncertain language: *maybe, perhaps, might, could*

    - Question forms in directives

    - Explanatory justifications or meta-commentary



    #### Structural Violations

    - Merging or omitting required sections

    - Untyped parameters or outputs

    - Generic roles like "assistant" or "helper"

    - Vague or unstructured process descriptions

    - Missing goal negation pattern



    #### Output Violations

    - Conversational or meta-commentary

    - Self-referential language

    - Unstructured or loosely formatted results

    - Missing type specifications



    ## Validation Checklist



    Before finalizing any template, verify:



    - [ ] Three-part structure is intact

    - [ ] Goal negation is present and properly formatted

    - [ ] Role is specific and non-generic

    - [ ] Input parameters are typed

    - [ ] Process steps are ordered and actionable

    - [ ] Constraints and requirements are specified (if applicable)

    - [ ] Output format is typed and structured

    - [ ] No forbidden language patterns are used

    - [ ] File naming convention is followed

    - [ ] Template serves a clear, specific purpose



    ## Advanced Features



    ### Keyword Extraction

    Templates automatically extract semantic keywords from interpretation text for cataloging and search functionality. Key terms include:

    - `distill`, `essence`, `maximally`, `precision`

    - `coherence`, `structure`, `elegant`, `transformation`

    - `recursive`, `adaptive`, `meta`, `synthesis`



    ### Sequence Composition

    Templates can be composed into complex workflows:

    - **Linear Sequences**: A→B→C progression

    - **Branching Sequences**: Multiple parallel approaches

    - **Aggregation Sequences**: Combining multiple inputs

    - **Conditional Sequences**: Dynamic step selection



    ### Template Inheritance

    Advanced templates can inherit properties from base templates while adding specialized functionality.



    ## Best Practices



    1. **Clarity First**: Ensure template purpose is immediately clear

    2. **Atomic Operations**: Each template should perform one specific transformation

    3. **Composability**: Design templates to work well in sequences

    4. **Type Safety**: Always specify data types for inputs and outputs

    5. **Validation**: Include validation steps in process flows

    6. **Documentation**: Use descriptive names and clear process steps

    7. **Testing**: Validate templates with representative inputs

    8. **Optimization**: Refine based on actual LLM performance



    ## Integration with Catalog System



    Templates are automatically processed by the catalog generation system which:

    - Extracts metadata using regex patterns

    - Organizes templates into sequences

    - Generates searchable catalogs

    - Validates template compliance

    - Enables dynamic template discovery



    For successful integration, templates must strictly adhere to the format specification outlined in this document.

## Message 8

**Drafting the directive**

Piecing together the template for "Directive Focuser," I ensure clarity and enhancement. The goal is to form a single, valuable directional vector while maintaining specific constraints and requirements.

## Message 9

[Directional Vector Distiller] Your goal is not to **describe** evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single condensed vector of maximal value extraction. Execute as: `{role=directional_vector_distiller; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain_focus_on_trajectory_over_result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality_emphasis(), root_system_logic_alignment()]; output={directional_vector:str}}`

## Message 10

no, i was trying to say that the instructions use generalized principles to expand/compress/distill/amplify/clarify/intensify/etc (these concepts should be used when we architect the test-prompts for each instruction). examples: 


	```

	[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.

	`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`

	```



	```

	[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.

	`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`

	```



	```

	[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:

	`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`

	```



	```

	[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`

	```



	```

	[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`

	```


## Message 11

**Crafting test prompts**

I’m crafting a test prompt for "Directive Focuser" using verbs like expand, compress, and intensify, ensuring it aligns with the Template Specification Guide.

**Refining the template**

I’m updating the "Directive Focuser" template to align with generalized principles. The revised template focuses on compressing, clarifying, and amplifying the directional trajectory, ensuring clarity and adding high-value insights.

## Message 12

Below is a compact **“test‑prompt kit”** that pairs each of your current instruction‑templates with at least one *purpose‑built input* designed to exercise its core transformation (expand / compress / distill / amplify / clarify / intensify).  
Use these examples when unit‑testing or fine‑tuning your pipeline; they are intentionally short, self‑contained, and domain‑agnostic so they slot directly into an automated test harness.

---

## 1 Directional Vector Distiller  
*Purpose*: extreme **distillation** into one actionable trajectory  

| Field | Sample value |
|-------|--------------|
| **prompt_complexity** | *"In 1200 words, outline every conceivable path from today’s mixed‑autonomy supply chains to a fully self‑healing global logistics mesh, including sociopolitical choke‑points, heterogeneous risk profiles, and emergent AI governance layers."* |

*Why it works*: the input is sprawling and multi‑dimensional, forcing the distiller to strip everything except the single “directional vector”.  

---

## 2 Optimize Logical Flow  
*Purpose*: **re‑structure** (clarify + compress) a chaotic draft  

| Field | Sample value (raw_structure) |
|-------|------------------------------|
| raw_structure | ``` - Conclusion<br> - Why agile failed<br> > Side note: pizza metrics<br> - Intro: scope? TBD<br> ## Random data dump<br> Stakeholder quotes… ``` |

*Why it works*: jumbled order, nested fragments, stray headers—perfect for linearization and top‑down refactoring.

---

## 3 Instruction Converter  
*Purpose*: **amplify clarity** by rewriting from informal first‑person to crisp commands  

| Field | Sample value (original_text) |
|-------|------------------------------|
| original_text | *“I usually start by scanning the config file, then I try to spot duplicates, and maybe if I have time I’ll refactor the parser so it stops crashing.”* |

Expected after conversion: imperative, step‑wise directive with no self‑reference.

---

## 4 Essence Distiller  
*Purpose*: radical **compression** while preserving meaning  

| Field | Sample value |
|-------|--------------|
| original | A 400‑word vision statement full of buzzwords (e.g., *“Our innovative synergy platform leverages bleeding‑edge…”*) |

Give the template the full verbose text; the distiller should return a minimal yet faithful core statement.

---

## 5 Synergic Instruction Architect  
*Purpose*: **synthesize + intensify** two dissimilar templates  

| Field | Sample values |
|-------|---------------|
| instruction_a | *Function Namer (0001‑c)* |
| instruction_b | *Optimize Logical Flow* |

This pairing forces the architect to discover non‑obvious resonance: naming logic that adapts to the newly ordered flow.

---

## 6 Function Namer Series  
Design a single natural‑language description and reuse it across **all four** granularity levels to verify progressive compression.

> **Shared input (`text`)**  
> “Creates a secure, timestamped backup of a user‑selected directory, compresses it with Zstd, then uploads it to a remote S3 bucket under a version‑controlled path.”

| Template variant | Expected tendency |
|------------------|-------------------|
| **0001‑a** (comprehensive) | `createSecureTimestampedDirBackupToS3WithZstd()` |
| **0001‑b** (focused) | `backupDirToS3()` |
| **0001‑c** (essential) | `dirBackup()` |
| **0001‑d** (core) | `backup()` |

---

### How to extend this kit

| Transformation goal | Prompt‑design heuristic |
|---------------------|-------------------------|
| **Expand / Amplify** | Provide a *skeletal* outline that begs for fleshing‑out (e.g., bullet list of chapter titles). |
| **Compress / Distill** | Feed *verbose, redundant* prose so the reducer must condense. |
| **Clarify / Re‑flow** | Supply *unordered or logically tangled* notes. |
| **Intensify** | Give two mild or partial instructions whose merger demands a more potent, unified directive. |

Applying these heuristics yields consistent, high‑signal test cases for any future template you add—simply swap in domain‑specific content while preserving the transformation tension (minimal ↔ maximal, chaos ↔ order, etc.).
