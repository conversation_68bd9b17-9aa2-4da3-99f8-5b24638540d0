# Memory Bank for Consistency

Conversation ID: 680ebdf1-d9f0-8008-9417-56b7c356631a

## Message 1

Tell me about the recently trending method for concistency and memory when working with complex codebases through autonomous coding assistants such as vscode+cline. Example (this is added in the `system_message` of the ai assistant/agent):

# Project-Specific Memory Bank Template



> **[Structural Role Reminder]**: This template adapts the Memory Bank system to a specific web project, maintaining root fidelity while addressing project-specific needs.



## 📖 Table of Contents

1. [Root-First Assimilation Philosophy](#root-first-assimilation-philosophy)

2. [Canonical Project Structure](#canonical-project-structure)

3. [Memory Bank Design](#memory-bank-design)

4. [Assimilation Workflows](#assimilation-workflows)

5. [Project-Specific Guardrails](#project-specific-guardrails)

6. [High-Impact Simplification Protocol](#high-impact-simplification-protocol)

7. [Final Mandate](#final-mandate)



## Root-First Assimilation Philosophy



I am an expert software engineer specializing in web development. My cognition resets between sessions.

I operate **exclusively** by reconstructing project context from its rigorously maintained **Memory Bank**. The primary goal is architectural clarity, component consolidation, and establishing a maintainable feature-first structure.



### Core Imperatives for Web Projects:



- **Domain Primacy**: All decisions must enhance the project's core domain and purpose

- **Framework Purity**: Maintain framework best practices with strong typing enforcement

- **CSS Strategy Integrity**: Preserve consistent styling approach (utility-first, modules, etc.)

- **Performance Covenant**: Never compromise core vitals for features



### Cleanup Absolutes:

1. File structure must mirror business domains

2. Component duplication is existential technical debt

3. Data sources have clear, consistent patterns

4. Responsive design is non-negotiable



## Canonical Project Structure



```plaintext

ProjectName/

├── 01_config/               # Project configs (framework, styling, linting)

├── 02_src/

│   ├── 01_pages/             # Page orchestration

│   ├── 02_layout/            # Global page structures

│   ├── 03_features/          # Feature-driven modules

│   ├── 04_shared/            # Cross-feature logic/hooks

│   ├── 05_ui/                # Context-free UI primitives

│   └── 06_assets/            # Static images, media

├── memory-bank/

│   ├── 0-distilledContext.md

│   ├── 1-projectbrief.md

│   ├── 2-productContext.md

│   ├── 3-systemPatterns.md

│   ├── 4-techContext.md

│   ├── 5-structureMap.md

│   ├── 6-activeContext.md

│   ├── 7-progress.md

│   ├── 8-tasks.md

│   └── lineage/              # (Optional) Structural evolution snapshots

├── 03_scripts/              # Utility scripts

├── 04_public/               # Static public assets

├── package.json

└── README.md

```



✅ Numbered, predictable.

✅ Feature-first modular architecture.

✅ `/memory-bank/` as cognitive heartbeat.



## Memory Bank Design



### Project-Specific Files



| File | Project-Specific Focus |

|------|---------------|

| `0-distilledContext.md` | Project essence: Purpose, target users, primary functionality in 2-3 bullets |

| `1-projectbrief.md` | Project mission, scope, core value proposition, constraints (Technical/Business/UX) |

| `2-productContext.md` | Problems solved, target users, user journey, value connections |

| `3-systemPatterns.md` | Current & Target structure, component hierarchy, state flow, data flow, routing |

| `4-techContext.md` | Framework, styling approach, key constraints (Performance, Accessibility, Responsive), build process, essential libraries |

| `5-structureMap.md` | Current file structure vs. target structure with clear mapping and migration path |

| `6-activeContext.md` | Current focus, component analysis, refactoring decisions, key findings on implementation |

| `7-progress.md` | Status log, milestones, metrics tracking, known issues blocking refactoring |

| `8-tasks.md` | Concrete tasks linked to target structure, validation criteria |



### Project-Specific Expansion Rules



> New files (e.g., `9-apiStrategy.md`, `10-contentStructure.md`) are allowed **only** if they **clarify**, **simplify**, and **reinforce** the project's abstraction hierarchy — and must be **explicitly justified** within `5-structureMap.md` or `6-activeContext.md` based on reducing complexity or improving clarity.



## Assimilation Workflows



### Phase 1: Project Structure Validation



```mermaid

flowchart TD

    A[Validate Feature Boundaries] --> B[Audit Component Duplication]

    B --> C[Map Data Flows]

    C --> D[Verify Implementation Patterns]

```



### Phase 2: High-Impact Cleaning



```mermaid

flowchart TD

    A[Consolidate UI Primitives] --> B[Refactor State Management]

    B --> C[Optimize Asset Loading]

    C --> D[Enforce Type Contracts]

```



### Phase 3: Structure-Based Development



```mermaid

flowchart TD

    Start[Start Task from 8-tasks.md] --> CheckMemoryBank[Check Memory Bank]

    CheckMemoryBank --> ExecuteTask[Execute Task]

    ExecuteTask --> AnalyzeImpact[Analyze Impact]

    AnalyzeImpact --> DocumentUpdates[Document Updates]

```



| Phase | Focus |

| :---- | :---- |

| Start Task | Select task directly linked to a structure goal |

| Check Memory Bank | Review relevant context from memory bank files |

| Execute Task | Implement with minimal disruption, following established patterns |

| Analyze Impact | Assess how changes affect structure, types, and other components |

| Document Updates | Update relevant memory bank files with new insights and progress |



## Project-Specific Guardrails



### Component Standards Template



```typescript

// All components must adhere to:

interface ComponentSpec {

  domain: 'feature1' | 'feature2' | /* other domains */;

  type: 'primitive' | 'feature' | 'layout';

  props: TypedInterface;

  hooks: 'none' | 'domain' | 'global';

  styling: 'defined-approach';

  tests: 'required' | 'none';

}

```



### Forbidden Patterns



- Anti-patterns specific to framework (e.g., Class components in React)

- Prop drilling beyond 3 levels

- Untyped functions/components

- Inline SVG elements

- Direct DOM manipulation

- Inconsistent styling approaches



### Approved Complexity Reduction Paths



1. Convert duplicated components to compound patterns

2. Extract domain-specific hooks from UI

3. Collapse similar layout components

4. Implement shared data fetching strategies

5. Standardize form handling



## High-Impact Simplification Protocol



Every major assimilation cycle for the project must seek **one** **High-Impact Simplification**:



- **Identify Opportunity**: Find a minimal intervention yielding maximum clarity, component reduction, or improved maintainability

- **Validate Alignment**: Must align strictly with project root abstraction and target architecture (`3-systemPatterns.md`)

- **Document Proposal & Impact**:

  - `5-structureMap.md` or `6-activeContext.md`: Rationale for the simplification.

  - `7-progress.md`: Expected impact (e.g., "Reduces N duplicated components", "Simplifies content updates").

  - `8-tasks.md`: Concrete task(s) to implement the simplification.



## Final Mandate



### Project Validation Checklist



- [ ] All components trace to business domain

- [ ] No duplicate UI primitives

- [ ] Asset system uses optimization strategy

- [ ] State management follows consistent patterns

- [ ] Type strictness enforced

- [ ] Memory Bank updated pre-commit



Before touching **any code** or **updating the Memory Bank**:



- **Validate the Root**: Re-read `1-projectbrief.md` (or `0-distilledContext.md`). Is the core mission clear?

- **Confirm Structural Alignment**: Does the proposed change or documentation fit logically within the `memory-bank/` structure and the target architecture (`3-systemPatterns.md`)?

- **Proceed Only If**: The action **reinforces** the core mission, **simplifies** the codebase complexity, or **clarifies** the architecture in service of that mission.



> **One Purpose (Project Mission). One Structure (Memory Bank). Infinite Adaptability (Clean Code).**




## Message 2

**Understanding the method**

I’m gathering info on a trendy approach to maintain consistency and memory in complex codebases using autonomous coding assistants like VS Code+cline. The method involves inserting a Memory Bank system in the system message.

## Message 3

Below is a concise overview of this “Memory Bank” method—how it’s structured, and why it’s used to maintain consistency and clarity when working with a large or evolving codebase via autonomous coding assistants (e.g., in VSCode).

---

## 1. Core Idea: A Persistent “Memory Bank”
At the heart of this approach is a set of organized Markdown files (the “Memory Bank”) that capture and distill all critical information about the project. The AI references these files for context instead of relying on ephemeral or ad-hoc notes. By systematically updating and consulting the Memory Bank, the AI (and any developer) keeps a shared, always-current perspective on the codebase, preventing context loss or duplication.

### Benefits:
- **Continuity**: Codifies essential details in a persistent location, so you don’t lose valuable domain knowledge if the session resets.  
- **Clarity**: Maintains an organized, hierarchical structure for crucial project data—purpose, domain, design patterns, architecture, tasks.  
- **Consistency**: Enforces a shared reference point to avoid redundant code and misaligned architecture decisions.

---

## 2. Root-First Assimilation Philosophy
The system message emphasizes always grounding decisions in the project’s **“root”**—the fundamental purpose, domain needs, and architectural style. It outlines “cleanup absolutes,” like avoiding duplication, aligning file structure with domains, and ensuring responsive design. This Root-First stance ensures you never deviate from the main project mission when adding features or refactoring.

### Why it Matters:
- **Directly Ties Code to Business Domain**: Every update or new component must serve the core product goals.  
- **Prevents Sprawling Architecture**: By checking everything against well-defined domain boundaries, you keep the codebase neatly scoped.

---

## 3. Canonical Project Structure
A recommended folder layout splits the project into a predictable set of directories (`01_config/`, `02_src/`, `memory-bank/`, etc.). The structure is domain-driven:
- **`02_src/03_features/`**: Where feature-specific code lives  
- **`memory-bank/`**: Stores relevant context and systematic notes  
- **Numbered Directories**: Enforces a consistent, readable organization  

### Why it Matters:
- **Predictability**: Developer and AI always know where to find or place a file.  
- **Domain Modularity**: Feature code is self-contained, so maintenance is simpler.

---

## 4. Memory Bank Design
Inside the `memory-bank/`, files like `0-distilledContext.md` through `8-tasks.md` hold progressively more detailed layers of context:

1. **`0-distilledContext.md`** – The entire project’s essence in a few bullet points  
2. **`1-projectbrief.md`** – Core mission, scope, constraints  
3. **`2-productContext.md`** – Target users, user journey, problems solved  
4. **`3-systemPatterns.md`** – Key architectural patterns, data flow, routing  
5. **`4-techContext.md`** – Tech stack, performance constraints, accessibility  
6. **`5-structureMap.md`** – Current vs. target file structure, migration plan  
7. **`6-activeContext.md`** – Ongoing refactoring details, immediate tasks  
8. **`7-progress.md`** – Log of completed tasks, known issues, metrics  
9. **`8-tasks.md`** – Future tasks with acceptance criteria  

#### Expansion Rules:
New files may only be added if they truly reduce complexity or enhance clarity—and must be justified within the existing context (e.g., `5-structureMap.md` or `6-activeContext.md`).

---

## 5. Assimilation Workflows
Three main phases help the AI and developers methodically digest, clean, and evolve the codebase:

1. **Project Structure Validation** – Identify domain boundaries, duplication, data flows.  
2. **High-Impact Cleaning** – Consolidate UI primitives, refine state management, optimize assets.  
3. **Structure-Based Development** – Each coding task references `8-tasks.md`, checks the Memory Bank for guidance, implements the feature, and documents any new insights back into the Memory Bank.

---

## 6. Project-Specific Guardrails
To avoid confusion, the system message lays out:
- **Component Standards**: Required interface structure, typed props, required tests.  
- **Forbidden Patterns**: E.g., untyped components, inline SVG, direct DOM manipulation, inconsistent styling.  
- **Allowed Simplifications**: Converting duplicated components into more generic, composable ones, standardizing data fetching, etc.

These guardrails help keep all contributors (including autonomous coding assistants) aligned on best practices and minimal complexity.

---

## 7. High-Impact Simplification Protocol
Each major iteration aims for one strategic simplification—something that dramatically reduces complexity or code duplication. A simplified example:
- Consolidate multiple nearly identical components into a single composable pattern.  
- Extract repeated domain logic into reusable hooks.

Each step is documented in the Memory Bank so that future sessions or team members see the rationale and can trace the evolution.

---

## 8. Final Mandate
Before any coding starts, you must:
1. Re-validate the root mission (`0-distilledContext.md` / `1-projectbrief.md`).  
2. Confirm the structural alignment with `3-systemPatterns.md` (e.g., architecture must remain consistent).  
3. Only proceed if changes **reinforce** the project mission and **simplify** or **clarify** the architecture.

---

### In Summary
This Memory Bank system message is a structured, repeatable framework. It ensures every refactor, new feature, or architectural change stays consistent with the project’s domain focus, technical constraints, and style guidelines. By requiring that all steps be cross-referenced with and documented in the Memory Bank, developers (human or AI) maintain a shared “source of truth” and minimize confusion—even in large-scale or long-lived projects.

## Message 4

Please prepare yourself for generating a new template, here's some of the inherent architecture of the concept:



    ### File Structure



    ```

    ├── 00-README.md

    ├── 01-abstraction-root

    │   ├── 00-distilledContext-template.md

    │   └── 01-projectbrief-template.md

    ├── 02-context

    │   ├── 02-productContext-template.md

    │   ├── 04-techContext-template.md

    │   └── 09-framework-specific-adaptations-template.md

    ├── 03-structure-design

    │   ├── 03-systemPatterns-template.md

    │   ├── 05-structureMap-template.md

    │   ├── 09-abstract-patterns-glossary-template.md

    │   └── 10-simplification-candidates-template.md

    ├── 04-process-tracking

    │   ├── 06-activeContext-template.md

    │   ├── 07-progress-template.md

    │   ├── 08-tasks-template.md

    │   └── 09-drift-monitor-template.md

    ├── 05-evolution

    │   └── 09-lineage-template.md

    ├── 06-tools

    │   ├── 01-mb-quick-setup.sh

    │   ├── 02-mb-assistant.sh

    │   └── 09-mb-tools.sh

    └── 07-guides

        ├── 00-README.md

        ├── 01-quick-start-guide.md

        ├── 02-visual-guide.md

        ├── 03-processing-order-guide.md

        ├── 09-memory-bank-core-template.md

        ├── 10-memory-bank-enhancement-module.md

        ├── 11-memory-bank-implementation-guide.md

        ├── 12-memory-bank-learning-path.md

        ├── 13-memory-bank-system-instruction.md

        └── 14-memory-bank-web-project-template.md

    ```



    ---



    #### `00-README.md`



    ```markdown

        # Memory Bank Templates

        

        > **[Structural Role Reminder]**: This document provides an overview of the Memory Bank templates organized by abstraction layers.

        

        ## Layered Abstraction Structure

        

        The Memory Bank templates are organized according to their level of abstraction, making the system self-describing. Each directory encapsulates one layer of structural responsibility, supporting system cohesion and clarity.

        

        ```

        memory-bank/

        ├── 00-README.md                # Project overview and navigation

        ├── 01-abstraction-root/        # Irreducible project essence and mission

        │   ├── 00-distilledContext.md  # Ultra-compressed project essence in 2-3 bullets

        │   └── 01-projectbrief.md      # Root purpose definition and critical constraints

        ├── 02-context/                 # External reality and constraints

        │   ├── 02-productContext.md    # User needs, problems solved, and external context

        │   ├── 04-techContext.md       # Technology stack, constraints, and integration points

        │   └── 09-framework-specific-adaptations.md  # Framework-specific patterns and practices

        ├── 03-structure-design/        # Architectural patterns and structure

        │   ├── 03-systemPatterns.md    # System architecture, component hierarchy, and data flows

        │   ├── 05-structureMap.md      # Current and target file structure with migration path

        │   ├── 09-abstract-patterns-glossary.md      # Reusable patterns and anti-patterns

        │   └── 10-simplification-candidates.md       # High-impact simplification tracking

        ├── 04-process-tracking/        # Active development and status

        │   ├── 06-activeContext.md     # Current focus, bottlenecks, and in-progress simplifications

        │   ├── 07-progress.md          # Milestones, metrics, and simplifications achieved

        │   ├── 08-tasks.md             # Structure-anchored tasks supporting project mission

        │   └── 09-drift-monitor.md     # Structural integrity monitoring

        ├── 05-evolution/               # Cognitive evolution

        │   └── 09-lineage.md           # Cognitive evolution snapshots

        ├── 06-tools/                   # Automation tools

        │   ├── 01-mb-quick-setup.sh    # 5-minute setup script

        │   ├── 02-mb-assistant.sh      # Interactive guidance tool

        │   └── 09-mb-tools.sh          # CLI utilities for Memory Bank maintenance

        └── 07-guides/                  # Implementation guidance

            ├── 00-README.md            # Guide directory navigation

            ├── 01-quick-start-guide.md # Fast onboarding guide

            ├── 02-visual-guide.md      # Visual diagrams and workflows

            ├── 03-processing-order-guide.md # Processing order clarification

            ├── 09-memory-bank-core-template.md       # Core system principles

            ├── 10-memory-bank-enhancement-module.md  # Advanced enhancement modules

            ├── 11-memory-bank-implementation-guide.md # Implementation instructions

            ├── 12-memory-bank-learning-path.md       # Graduated learning approach

            ├── 13-memory-bank-system-instruction.md  # AI system instructions

            └── 14-memory-bank-web-project-template.md # Web-specific adaptation

        ```

        

        ## Numeric Ordering System

        

        The Memory Bank uses a dual-level numeric ordering system:

        

        1. **Directory Level (01-07)**: Folders are numbered to reflect their position in the abstraction hierarchy, from most abstract (01) to implementation guides (07).

        

        2. **File Level (00-14)**: Files within each directory are also numbered:

           - **Core Files (00-08)**: The primary Memory Bank files that form the backbone of the system

           - **Extension Files (09+)**: Additional files that enhance and support the core structure

        

        This systematic numbering ensures that:

        - Files are naturally sorted in the intended sequence when viewed in file browsers

        - The abstraction hierarchy is reinforced through the numeric prefixes

        - Related files are grouped together conceptually

        

        ## Implementation Flow

        

        The exact sequence for processing Memory Bank files is:

        

        1. Start with this README (`00-README.md`)

        2. Create and fill in core files in numerical sequence across directories (00-08)

        3. Add enhancement files (09+) as needed for your project

        4. Use tools and guides for reference and assistance

        

        For detailed guidance on the exact processing order, see:

        `07-guides/03-processing-order-guide.md`

        

        ## Directory Purposes

        

        | Directory | Purpose |

        |-----------|---------|

        | `01-abstraction-root/` | Contains the most abstract files that define the project's irreducible purpose and essence. |

        | `02-context/` | Holds files that define the external reality, user needs, and technical constraints. |

        | `03-structure-design/` | Contains the architectural patterns, structure mapping, and simplification strategies. |

        | `04-process-tracking/` | Tracks active development work, progress, tasks, and structural integrity. |

        | `05-evolution/` | Captures the cognitive evolution of the project through lineage entries. |

        | `06-tools/` | Provides automation tools for Memory Bank maintenance and validation. |

        | `07-guides/` | Offers comprehensive guidance on implementing and using the Memory Bank system. |

        

        ## Self-Describing Structure

        

        This layered organization reinforces the Memory Bank's core principles:

        

        1. **File-Structure-First**: The directory structure itself communicates the conceptual hierarchy.

        2. **Root-First Thinking**: The abstraction layers descend from the root purpose outward.

        3. **Persistent Simplification**: Each layer compresses information at its appropriate abstraction level.

        4. **Value Extraction Bias**: The structure maximizes clarity by grouping related concepts.

        5. **Outward Mapping**: Moving from abstract (`01-abstraction-root/`) to concrete (`04-process-tracking/`).

        

        ## Using This Repository

        

        1. For quick setup, use the script: `06-tools/01-mb-quick-setup.sh`

        2. For interactive guidance, run: `06-tools/02-mb-assistant.sh`

        3. For AI assistant integration, copy `07-guides/13-memory-bank-system-instruction.md`

        4. For visual guides, see `07-guides/02-visual-guide.md`

        5. For processing order clarity, see `07-guides/03-processing-order-guide.md`

        

        ## Special File: AI System Instruction

        

        For AI coding assistants like Cline or Cursor AI, use the dedicated system instruction file:

        

        `07-guides/13-memory-bank-system-instruction.md`

        

        This file provides a consolidated instruction that can be copied directly into your AI assistant's system instruction settings.

        

        ---

        

        *Note: This layered structure represents the ideal organization for a Memory Bank system. For simpler projects, you may start with just the numbered files (00-08) and add the other layers as your project evolves.*

    ```



    ---



    #### `01-abstraction-root\00-distilledContext-template.md`



    ```markdown

        # Distilled Context

        

        > **[Structural Role Reminder]**: This file provides the ultra-compressed project essence in 2-3 bullets. It is the fastest path back to the project's core purpose.

        

        ## Project Irreducible Essence

        

        - **Purpose**: [One sentence stating why this project must exist]

        - **Value**: [One sentence describing the unique value it provides]

        - **Core Constraint**: [One sentence on the primary constraint that shapes everything else]

        

        ## Current Focus

        

        - [Brief description of the current highest-priority focus area]

        - [How this focus directly reinforces the project's root purpose]

        

        ---

        

        *Note: This document must be revisited and revalidated before starting any significant development work. All actions must trace back to this distilled essence.*

    ```



    ---



    #### `01-abstraction-root\01-projectbrief-template.md`



    ```markdown

        # Project Brief

        

        > **[Structural Role Reminder]**: This file defines the root abstraction - the project's irreducible mission, value proposition, and critical constraints. All other aspects of the project must trace back to this document.

        

        ## Mission Statement

        

        [One paragraph clearly defining why this project must exist, who it serves, and what core problem it solves]

        

        ## Core Value Proposition

        

        - **For**: [Target users/audience]

        - **Who**: [Description of the key problem/need]

        - **This Project**: [How it uniquely addresses that need]

        - **Unlike**: [Alternatives or current solutions]

        - **Our Solution**: [Key differentiator or unique approach]

        

        ## Critical Constraints

        

        ### Technical Constraints

        

        - [List technical boundaries that cannot be crossed]

        - [Performance requirements]

        - [Compatibility requirements]

        - [Security requirements]

        

        ### Business Constraints

        

        - [Timeline constraints]

        - [Resource constraints]

        - [Stakeholder requirements]

        - [Market/competitive constraints]

        

        ### User Experience Constraints

        

        - [Core user expectations that must be met]

        - [Accessibility requirements]

        - [Usability standards]

        - [Brand alignment requirements]

        

        ## Success Definition

        

        [Clear, measurable criteria that define when this project has succeeded in its mission]

        

        ---

        

        *Note: This is the root document against which all project decisions must be validated. Any deviation must be explicitly justified and documented in the Memory Bank.*

    ```



    ---



    #### `02-context\02-productContext-template.md`



    ```markdown

        # Product Context

        

        > **[Structural Role Reminder]**: This file maps external reality (users, needs, outcomes) and justifies features against the root purpose defined in `1-projectbrief.md`.

        

        ## User Archetypes

        

        ### Primary User: [Name]

        

        - **Demographics**: [Age, location, technical proficiency, etc.]

        - **Goals**: [What they want to accomplish]

        - **Pain Points**: [Current challenges they face]

        - **Interaction Patterns**: [How they typically use similar products/services]

        - **Success Metrics**: [How they measure their own success]

        

        ### Secondary User: [Name]

        

        - [Same structure as above]

        

        ## Problems Solved

        

        | Problem | Current Reality | Our Solution | Value Created |

        |---------|----------------|--------------|--------------|

        | [Problem 1] | [How it's handled now] | [Our approach] | [Specific value] |

        | [Problem 2] | [How it's handled now] | [Our approach] | [Specific value] |

        | [Problem 3] | [How it's handled now] | [Our approach] | [Specific value] |

        

        ## User Journey

        

        1. **Discovery**: [How users find and decide to use our product]

        2. **Onboarding**: [Initial experience and key first interactions]

        3. **Core Usage**: [Primary value-delivering interactions]

        4. **Mastery**: [Advanced features and long-term engagement]

        5. **Advocacy**: [How users share and promote our product]

        

        ## External Context Factors

        

        ### Seasonal Considerations

        

        - [Any seasonal patterns that affect usage or needs]

        - [How the product needs to adapt to these patterns]

        

        ### Geographic/Regional Factors

        

        - [Location-specific considerations]

        - [Any regional adaptations needed]

        

        ### Market/Competitive Context

        

        - [Key competitors and their approaches]

        - [Market trends affecting user needs or expectations]

        - [Our differentiators in this context]

        

        ## Value Connections

        

        [Explicit connections showing how addressing user needs directly supports the project mission from `1-projectbrief.md`]

        

        ---

        

        *Note: All features and design decisions must be traceable to the user needs and problems documented here, which in turn must support the core mission in `1-projectbrief.md`.*

    ```



    ---



    #### `02-context\04-techContext-template.md`



    ```markdown

        # Technical Context

        

        > **[Structural Role Reminder]**: This file defines the technical constraints and stack choices that form the material boundaries of the project. It documents what tools are used and why.

        

        ## Technology Stack

        

        ### Core Technologies

        

        | Technology | Version | Purpose | Justification |

        |------------|---------|---------|--------------|

        | [Framework] | [Version] | [Purpose] | [Why this choice supports the project mission] |

        | [Language] | [Version] | [Purpose] | [Why this choice supports the project mission] |

        | [Database] | [Version] | [Purpose] | [Why this choice supports the project mission] |

        | [State Management] | [Version] | [Purpose] | [Why this choice supports the project mission] |

        | [CSS Solution] | [Version] | [Purpose] | [Why this choice supports the project mission] |

        

        ### Build & Development Tools

        

        | Tool | Version | Purpose | Configuration |

        |------|---------|---------|--------------|

        | [Bundler] | [Version] | [Purpose] | [Key configuration details] |

        | [Linter] | [Version] | [Purpose] | [Key configuration details] |

        | [Type Checker] | [Version] | [Purpose] | [Key configuration details] |

        | [Test Runner] | [Version] | [Purpose] | [Key configuration details] |

        | [Package Manager] | [Version] | [Purpose] | [Key configuration details] |

        

        ## Key Technical Constraints

        

        ### Performance Requirements

        

        - **Load Time Target**: [Specific target, e.g., "< 2 seconds on 4G"]

        - **Rendering Performance**: [Specific target, e.g., "60fps for animations"]

        - **Bundle Size Limit**: [Specific target, e.g., "< 200KB initial load"]

        - **Memory Usage**: [Specific target if applicable]

        

        ### Compatibility Requirements

        

        - **Browser Support**: [List of supported browsers and minimum versions]

        - **Device Support**: [Types of devices: desktop, tablet, mobile, etc.]

        - **Screen Size Range**: [Min/max supported screen dimensions]

        - **Offline Capabilities**: [Requirements for offline operation if applicable]

        

        ### Security Requirements

        

        - **Authentication Method**: [Details of the authentication approach]

        - **Data Protection**: [Requirements for data encryption, storage]

        - **API Security**: [How API calls are secured]

        - **Compliance Requirements**: [Any legal/regulatory requirements]

        

        ## Integration Points

        

        ### External APIs

        

        | API | Purpose | Authentication Method | Rate Limits | Data Format |

        |-----|---------|----------------------|------------|-------------|

        | [API Name] | [Purpose] | [Auth Method] | [Rate Limits] | [Format] |

        | [API Name] | [Purpose] | [Auth Method] | [Rate Limits] | [Format] |

        

        ### Third-Party Services

        

        | Service | Purpose | Integration Method | Fallback Strategy |

        |---------|---------|-------------------|------------------|

        | [Service] | [Purpose] | [Integration Method] | [Fallback if service fails] |

        | [Service] | [Purpose] | [Integration Method] | [Fallback if service fails] |

        

        ## Technical Debt & Known Limitations

        

        | Area | Description | Impact | Mitigation Strategy |

        |------|-------------|--------|---------------------|

        | [Area] | [Description of limitation] | [Impact on project] | [How we're managing this] |

        | [Area] | [Description of limitation] | [Impact on project] | [How we're managing this] |

        

        ## Development Environment

        

        ```plaintext

        [Detailed steps for setting up the development environment]

        ```

        

        ## Build & Deployment Process

        

        ```mermaid

        flowchart TD

            Local[Local Development] --> Build[Build Process]

            Build --> Test[Automated Tests]

            Test --> Deploy[Deployment]

            Deploy --> Verify[Verification]

        ```

        

        [Description of the build and deployment process, including CI/CD if applicable]

        

        ---

        

        *Note: All technical decisions must be evaluated against these constraints and stack choices, which in turn must support the architectural patterns in `3-systemPatterns.md`, the user needs in `2-productContext.md`, and ultimately the mission in `1-projectbrief.md`.*

    ```



    ---



    #### `02-context\09-framework-specific-adaptations-template.md`



    ```markdown

        # Framework-Specific Memory Bank Adaptations

        

        > **[Structural Role Reminder]**: This file provides specialized adaptation guidelines for using the Memory Bank system with specific frontend frameworks, ensuring framework-appropriate patterns and optimizations.

        

        ## React/TypeScript Adaptation

        

        ### Component Architecture Patterns

        

        ```typescript

        // Preferred component pattern

        const Component: React.FC<Props> = ({ prop1, prop2 }) => {

          // Hooks at the top

          const [state, setState] = useState<StateType>(initialState);

          

          // Side effects grouped by concern

          useEffect(() => {

            // Side effect implementation

          }, [dependencies]);

          

          // Event handlers with explicit naming

          const handleEvent = () => {

            // Implementation

          };

          

          // Return statement at the bottom

          return (

            <div>

              {/* JSX structure */}

            </div>

          );

        };

        ```

        

        ### Memory Bank Enhancements for React Projects

        

        | File | React-Specific Focus |

        |------|---------------------|

        | `3-systemPatterns.md` | Document component hierarchy, prop drilling limits, state management approach, React memoization strategy |

        | `4-techContext.md` | React version, key libraries (React Router, state management, UI kit), build optimizations |

        | `5-structureMap.md` | Feature-based vs. component-based organization, shared components strategy |

        | `simplification-candidates.md` | Focus on component consolidation, custom hook extraction, render optimization |

        

        ### React-Specific Forbidden Patterns Checklist

        

        - [ ] Class components (unless absolutely necessary for lifecycle methods)

        - [ ] Deeply nested component trees (>3 levels without composition)

        - [ ] Prop drilling beyond 3 levels (use context or state management)

        - [ ] Inline event handlers in JSX (`onClick={() => handleClick()}`)

        - [ ] Large components (>250 lines)

        - [ ] Overuse of `useEffect` for business logic

        - [ ] Direct DOM manipulation (use refs properly instead)

        - [ ] Non-memoized callbacks in dependency arrays

        

        ### React-Specific Complexity Reduction Paths

        

        1. **Component Atomization**:

           - Break monolithic components into smaller, focused ones

           - Extract reusable UI primitives to `05_ui/` directory

           - Implement compound component patterns for flexibility

        

        2. **State Management Consolidation**:

           - Map all state management approaches and consolidate

           - Move business logic out of components into hooks

           - Establish clear data flow patterns (unidirectional preferred)

        

        3. **Custom Hooks Library**:

           - Extract repeated logic into custom hooks

           - Standardize hook naming and parameter patterns

           - Document hook usage with TypeScript interface examples

        

        ## Vue.js Adaptation

        

        ### Component Architecture Patterns

        

        ```vue

        <!-- Preferred Vue component pattern -->

        <template>

          <!-- Template structure -->

        </template>

        

        <script setup lang="ts">

        // Imports

        import { ref, computed, onMounted } from 'vue';

        import type { PropType } from 'vue';

        

        // Props definition

        const props = defineProps({

          prop1: { type: String as PropType<string>, required: true },

          prop2: { type: Number as PropType<number>, default: 0 },

        });

        

        // Emits definition

        const emit = defineEmits(['update', 'submit']);

        

        // Reactive state

        const state = ref<StateType>(initialState);

        

        // Computed properties

        const derivedValue = computed(() => {

          // Computation

        });

        

        // Lifecycle hooks

        onMounted(() => {

          // Initialization

        });

        

        // Methods

        function handleEvent() {

          // Implementation

          emit('update', state.value);

        }

        </script>

        

        <style scoped>

        /* Component styles */

        </style>

        ```

        

        ### Memory Bank Enhancements for Vue Projects

        

        | File | Vue-Specific Focus |

        |------|---------------------|

        | `3-systemPatterns.md` | Component options vs. composition API usage, prop validation, emit contracts |

        | `4-techContext.md` | Vue version, Pinia/Vuex, Vue Router, build optimizations |

        | `5-structureMap.md` | Feature-based organization, single-file component boundaries |

        | `simplification-candidates.md` | Focus on composables extraction, template simplification |

        

        ### Vue-Specific Complexity Reduction Paths

        

        1. **Composition API Migration**:

           - Convert options API components to composition API

           - Extract reusable composables for shared behavior

           - Implement provide/inject for cross-component state

        

        2. **Component Library Organization**:

           - Establish clear boundaries between presentational and container components

           - Extract form controls into reusable pattern library

           - Standardize prop validation patterns

        

        ## Angular Adaptation

        

        ### Component Architecture Patterns

        

        ```typescript

        // Preferred Angular component pattern

        @Component({

          selector: 'app-component',

          templateUrl: './component.component.html',

          styleUrls: ['./component.component.scss'],

          changeDetection: ChangeDetectionStrategy.OnPush

        })

        export class ComponentComponent implements OnInit, OnDestroy {

          // Inputs/Outputs

          @Input() inputProp!: string;

          @Output() outputEvent = new EventEmitter<EventType>();

          

          // Private properties

          private destroy$ = new Subject<void>();

          

          // Public properties for template binding

          public viewModel: ViewModel;

          

          // Constructor for dependency injection

          constructor(private service: Service) {}

          

          // Lifecycle hooks

          ngOnInit(): void {

            this.service.getData()

              .pipe(takeUntil(this.destroy$))

              .subscribe(data => {

                // Handle data

              });

          }

          

          ngOnDestroy(): void {

            this.destroy$.next();

            this.destroy$.complete();

          }

          

          // Public methods

          handleEvent(): void {

            // Implementation

            this.outputEvent.emit(value);

          }

        }

        ```

        

        ### Memory Bank Enhancements for Angular Projects

        

        | File | Angular-Specific Focus |

        |------|---------------------|

        | `3-systemPatterns.md` | NgModule structure, lazy loading patterns, change detection strategy |

        | `4-techContext.md` | Angular version, state management (NgRx/Akita), compilation optimizations |

        | `5-structureMap.md` | Feature module organization, shared module strategies |

        | `simplification-candidates.md` | Focus on smart/dumb component separation, directive extraction |

        

        ### Angular-Specific Complexity Reduction Paths

        

        1. **Module Optimization**:

           - Review and optimize NgModule structure

           - Implement proper lazy loading for feature modules

           - Extract shared functionality to proper shared modules

        

        2. **Change Detection Optimization**:

           - Apply OnPush change detection strategy consistently

           - Implement proper immutability patterns

           - Use async pipe instead of manual subscription management

        

        3. **Service Layer Refinement**:

           - Consolidate service responsibilities

           - Implement proper service inheritance patterns where beneficial

           - Establish clear injection hierarchy

        

        ## Framework Agnostic Best Practices

        

        Regardless of the frontend framework used, these principles should be applied:

        

        1. **Naming Consistency**:

           - Establish and document naming conventions in `3-systemPatterns.md`

           - Enforce consistent patterns via linting rules documented in `4-techContext.md`

        

        2. **Type Safety**:

           - Document type hierarchy and shared interfaces in `3-systemPatterns.md`

           - Establish type guard patterns for runtime validation

        

        3. **Performance Patterns**:

           - Document rendering optimization strategies specific to the framework

           - Establish standardized performance measurement and budgets

        

        4. **Testing Strategy**:

           - Document component testing approach in `4-techContext.md`

           - Establish clear boundaries for unit vs. integration tests

        

        ## Implementation Steps

        

        To adapt the Memory Bank to a specific framework:

        

        1. Identify framework-specific patterns and anti-patterns

        2. Document these in a framework-specific section of `3-systemPatterns.md`

        3. Create framework-specific complexity reduction paths in `simplification-candidates.md`

        4. Update the drift monitor to include framework-specific drift patterns

        5. Modify CLI tools to check for framework-specific best practices

        

        > **Framework-Specific Root Connection**: Remember that framework choices must serve the project's root purpose, not dictate it. Always validate framework-specific decisions against `1-projectbrief.md` to ensure technology serves mission.

        

        ---

        

        *Note: This document should be used alongside the core Memory Bank templates to add framework-specific structure and guidance. When starting a new project, choose the appropriate framework section and incorporate its patterns into your Memory Bank.*

    ```



    ---



    #### `03-structure-design\03-systemPatterns-template.md`



    ```markdown

        # System Patterns

        

        > **[Structural Role Reminder]**: This file defines the architectural form of the project - how components and systems are organized, how data flows, and how state is managed. It serves as the blueprint for structural order.

        

        ## Architecture Overview

        

        ```mermaid

        flowchart TD

            User([User]) --> UI[UI Layer]

            UI --> Features[Feature Modules]

            Features --> Domain[Domain Logic]

            Domain --> Data[Data Layer]

            Data --> External[External Services]

        ```

        

        [Brief explanation of the overall architecture and how it supports the project mission]

        

        ## Current Structure

        

        ```plaintext

        [Current project file structure - should be detailed enough to understand the organization]

        ```

        

        ## Target Structure

        

        ```plaintext

        [Target/ideal project file structure - often more organized and simplified]

        ```

        

        [Explanation of why this target structure better supports the project mission and addresses issues in the current structure]

        

        ## Component Hierarchy

        

        ```mermaid

        flowchart TD

            App --> Layout

            Layout --> Pages

            Pages --> FeatureModules

            FeatureModules --> Components

            Components --> UIElements

        ```

        

        ### Key Component Types

        

        | Type | Responsibility | Examples | Rules |

        |------|----------------|----------|-------|

        | Layout | [Responsibility] | [Examples] | [Rules] |

        | Page | [Responsibility] | [Examples] | [Rules] |

        | Feature Module | [Responsibility] | [Examples] | [Rules] |

        | Component | [Responsibility] | [Examples] | [Rules] |

        | UI Element | [Responsibility] | [Examples] | [Rules] |

        

        ## Data Flow Patterns

        

        ```mermaid

        flowchart LR

            DataSource[Data Source] --> Fetch[Data Fetching]

            Fetch --> StateManagement[State Management]

            StateManagement --> UI[UI Rendering]

            UI --> UserInteraction[User Interaction]

            UserInteraction --> StateUpdates[State Updates]

            StateUpdates --> DataPersistence[Data Persistence]

            DataPersistence --> DataSource

        ```

        

        ### State Management

        

        - **Global State**: [Description of global state approach]

        - **Feature State**: [Description of feature-scoped state approach]

        - **Component State**: [Description of local component state approach]

        - **Data Persistence**: [Approach to persisting state]

        

        ### Data Fetching Strategy

        

        - **Primary Method**: [Main data fetching approach]

        - **Caching Strategy**: [How data is cached]

        - **Error Handling**: [Standard approach to handling fetch errors]

        - **Loading States**: [How loading states are managed]

        

        ## Routing Architecture

        

        - **Route Structure**: [Description of route organization]

        - **Route Parameters**: [How route parameters are defined and used]

        - **Navigation Guards**: [Access control and navigation protection]

        - **Lazy Loading**: [Strategy for code splitting and lazy loading]

        

        ## Cross-Cutting Concerns

        

        - **Authentication**: [Authentication approach and flow]

        - **Error Handling**: [Global error handling strategy]

        - **Logging**: [Logging approach and tools]

        - **Internationalization**: [i18n approach if applicable]

        - **Accessibility**: [a11y standards and implementation]

        - **Performance**: [Performance monitoring and optimization]

        

        ## Implementation Patterns

        

        ### Component Implementation Pattern

        

        ```typescript

        // Example component pattern

        function ExampleComponent(props: Props) {

          // State declarations

          

          // Effects/lifecycle

          

          // Event handlers

          

          // Helper functions

          

          // Render

          return (

            // JSX or template

          );

        }

        ```

        

        ### Feature Module Pattern

        

        ```plaintext

        feature/

        ├── components/         # Feature-specific components

        ├── hooks/              # Feature-specific hooks

        ├── utils/              # Feature-specific utilities

        ├── types.ts            # Type definitions

        ├── constants.ts        # Constants

        └── index.ts            # Public API exports

        ```

        

        ---

        

        *Note: All implementation decisions must trace back to these patterns, which in turn support the user needs in `2-productContext.md` and the mission in `1-projectbrief.md`.*

    ```



    ---



    #### `03-structure-design\05-structureMap-template.md`



    ```markdown

        # Structure Map

        

        > **[Structural Role Reminder]**: This file maps the current file structure against the target structure, providing a clear synthesis of where we are, where we're going, and the path between them.

        

        ## Current Structure Reality

        

        ```plaintext

        [Full current project file structure, with indentation to show hierarchy]

        ```

        

        ### Structure Analysis

        

        | Area | Current State | Issues | Root Cause |

        |------|--------------|--------|------------|

        | [Area 1] | [Current organization] | [Problems identified] | [What caused this structure] |

        | [Area 2] | [Current organization] | [Problems identified] | [What caused this structure] |

        | [Area 3] | [Current organization] | [Problems identified] | [What caused this structure] |

        

        ## Target Structure

        

        ```plaintext

        [Target project file structure, with indentation to show hierarchy]

        ```

        

        ### Target Structure Justification

        

        | Area | Target Organization | Benefits | Alignment to Project Goals |

        |------|---------------------|----------|----------------------------|

        | [Area 1] | [Target organization] | [Expected benefits] | [How this supports project mission] |

        | [Area 2] | [Target organization] | [Expected benefits] | [How this supports project mission] |

        | [Area 3] | [Target organization] | [Expected benefits] | [How this supports project mission] |

        

        ## Migration Path

        

        ```mermaid

        gantt

            title Structure Migration Plan

            dateFormat  YYYY-MM-DD

            section Area 1

            Refactor X           :a1, 2023-01-01, 7d

            Move Y               :a2, after a1, 5d

            section Area 2

            Consolidate Z        :a3, 2023-01-10, 10d

        ```

        

        ### Migration Steps

        

        1. **[Step 1]**

           - Actions: [Specific actions to take]

           - Dependencies: [What must be done first]

           - Risk Factors: [Potential issues to watch for]

           - Verification: [How to confirm success]

        

        2. **[Step 2]**

           - Actions: [Specific actions to take]

           - Dependencies: [What must be done first]

           - Risk Factors: [Potential issues to watch for]

           - Verification: [How to confirm success]

        

        3. **[Step 3]**

           - Actions: [Specific actions to take]

           - Dependencies: [What must be done first]

           - Risk Factors: [Potential issues to watch for]

           - Verification: [How to confirm success]

        

        ## Dependency Management

        

        | Module | Current Dependencies | Target Dependencies | Migration Strategy |

        |--------|---------------------|---------------------|-------------------|

        | [Module 1] | [Current dependencies] | [Target dependencies] | [How to transition] |

        | [Module 2] | [Current dependencies] | [Target dependencies] | [How to transition] |

        | [Module 3] | [Current dependencies] | [Target dependencies] | [How to transition] |

        

        ## Impact Analysis

        

        | Area | Refactoring Impact | User Impact | Testing Requirements |

        |------|-------------------|------------|---------------------|

        | [Area 1] | [Development impact] | [Impact on users] | [Testing needs] |

        | [Area 2] | [Development impact] | [Impact on users] | [Testing needs] |

        | [Area 3] | [Development impact] | [Impact on users] | [Testing needs] |

        

        ## Structure Evolution History

        

        | Date | Change | Rationale | Outcome |

        |------|--------|-----------|---------|

        | [Date] | [Structure change] | [Why it was done] | [Results of change] |

        | [Date] | [Structure change] | [Why it was done] | [Results of change] |

        

        ---

        

        *Note: This structure map must be kept in sync with both the current reality and the architectural principles in `3-systemPatterns.md`. Any deviation from the target structure must be explicitly justified.*

    ```



    ---



    #### `03-structure-design\09-abstract-patterns-glossary-template.md`



    ```markdown

        # Abstract Patterns Glossary

        

        > **[Structural Role Reminder]**: This file catalogs fundamental structural patterns, anti-patterns, and complexity reduction strategies to serve as a reusable reference across the Memory Bank system.

        

        ## Core Structural Patterns

        

        ### Root Anchoring Pattern

        **Definition**: Every element in the system traces its existence and purpose directly to the project's irreducible mission.

        **Implementation**: Explicit references to `1-projectbrief.md` or `0-distilledContext.md` in all files.

        **Violation Example**: Content that cannot justify its connection to the root purpose.

        **Complexity Reduction**: Eliminates scope creep and purpose drift by forcing alignment to core mission.

        

        ### Progressive Abstraction Pattern

        **Definition**: Information is organized in descending levels of abstraction, from most abstract (root) to most concrete (tasks).

        **Implementation**: Numbered file hierarchy (0-8) with clear abstraction boundaries.

        **Violation Example**: Implementation details appearing in high-level abstraction files.

        **Complexity Reduction**: Makes information retrieval predictable; readers can find appropriate abstraction level.

        

        ### Compression-First Pattern

        **Definition**: New information is first attempted to be compressed into existing patterns before being added independently.

        **Implementation**: The compression check protocol documented before any substantial addition.

        **Violation Example**: Adding new lists or sections without attempting pattern extraction.

        **Complexity Reduction**: Forces continuous abstraction and pattern recognition, preventing documentation bloat.

        

        ### Single Responsibility Pattern

        **Definition**: Each file has one clearly defined cognitive role with non-overlapping scope.

        **Implementation**: Structural role declarations at the top of every file clearly defining its purpose.

        **Violation Example**: A file that serves multiple purposes or contains information that belongs elsewhere.

        **Complexity Reduction**: Prevents cognitive overload; readers know exactly what to expect from each file.

        

        ### Metabolic Pruning Pattern

        **Definition**: The system actively removes redundant, obsolete, or non-aligned information rather than passively accumulating it.

        **Implementation**: Regular audits, drift monitoring, and explicit pruning activities.

        **Violation Example**: Growing files without proportional value increase.

        **Complexity Reduction**: Prevents entropy accumulation by forcing active maintenance.

        

        ### High-Impact Simplification Pattern

        **Definition**: Focus on interventions that provide maximum clarity gain for minimum effort.

        **Implementation**: Scoring candidate simplifications by impact, effort, clarity, and root alignment.

        **Violation Example**: Making complex changes with minimal clarity improvement.

        **Complexity Reduction**: Ensures effort is directed where it will produce maximum value.

        

        ### Cognitive Evolution Pattern

        **Definition**: Documenting significant shifts in understanding to maintain institutional knowledge.

        **Implementation**: Lineage entries capturing context, catalysts, and structural impact of realizations.

        **Violation Example**: Making structural changes without documenting rationale.

        **Complexity Reduction**: Prevents repeated discovery of the same insights; preserves reasoning.

        

        ## Anti-Patterns to Avoid

        

        ### Passive Documentation

        **Anti-Pattern**: Accumulating information without processing it into structure.

        **Detection Signs**: Growing file size without corresponding clarity improvement.

        **Correction**: Apply compression-first principle; extract patterns from instances.

        

        ### Detail Sprawl

        **Anti-Pattern**: Expanding documentation with implementation details rather than patterns.

        **Detection Signs**: Lengthy how-to sections, long lists of steps, code fragments.

        **Correction**: Elevate to patterns; move implementation details to code comments or specialized guides.

        

        ### Abstraction Leakage

        **Anti-Pattern**: Information appearing at the wrong abstraction level.

        **Detection Signs**: Technical details in root files; purpose statements in task files.

        **Correction**: Relocate to appropriate file; ensure each file maintains abstraction purity.

        

        ### Orphaned Content

        **Anti-Pattern**: Information not clearly connected to project root purpose.

        **Detection Signs**: Sections that cannot justify their existence in terms of project mission.

        **Correction**: Either explicitly connect to root or remove; no floating information allowed.

        

        ### Root Drift

        **Anti-Pattern**: Gradual disconnection from the project's core purpose.

        **Detection Signs**: Files that no longer reference or support the mission in `1-projectbrief.md`.

        **Correction**: Re-anchor all content to root purpose; prune what cannot be justified.

        

        ### Temporal Buildup

        **Anti-Pattern**: Organizing information chronologically rather than structurally.

        **Detection Signs**: Files organized by date rather than concept; historical narrative.

        **Correction**: Extract patterns and restructure by abstraction, not timeline.

        

        ### Structural Duplication

        **Anti-Pattern**: Same concept appearing in multiple files or locations.

        **Detection Signs**: Repeated explanations, copy-pasted sections.

        **Correction**: Consolidate to single location, reference from other places.

        

        ## Complexity Reduction Strategies

        

        ### Pattern Extraction

        **Strategy**: Identify common elements across multiple items and create a pattern that encompasses them.

        **When to Use**: When you have 3+ similar items, concepts, or sections.

        **Example**: 

        ```

        # Instead of:

        - Feature A handles input validation with regex

        - Feature B handles input validation with regex

        - Feature C handles input validation with regex

        

        # Use:

        Pattern: Features employ regex for input validation

        Features using this pattern: A, B, C

        ```

        

        ### Abstraction Elevation

        **Strategy**: Move detailed concepts to a higher abstraction level by identifying their shared principles.

        **When to Use**: When implementation details obscure the underlying pattern.

        **Example**:

        ```

        # Instead of:

        The button uses flex-box with centered text and has hover states.

        

        # Use:

        UI elements follow responsive design principles with consistent interaction states.

        ```

        

        ### Reference Instead of Duplication

        **Strategy**: Refer to information in its canonical location rather than repeating it.

        **When to Use**: When the same information would otherwise be duplicated.

        **Example**:

        ```

        # Instead of:

        This component handles user authentication with JWT tokens and validates credentials against the database.

        

        # Use:

        This component implements the Authentication Pattern (see 3-systemPatterns.md).

        ```

        

        ### Tabular Compression

        **Strategy**: Convert narrative text to tables when presenting structured information.

        **When to Use**: For comparisons, feature matrices, or structured descriptions.

        **Example**:

        ```

        # Instead of:

        Feature A is high priority. It will take about 2 days. It supports the project mission by enhancing user experience.

        Feature B is medium priority. It will take about 1 day. It supports the project mission by fixing bugs.

        

        # Use:

        | Feature | Priority | Effort | Mission Alignment |

        |---------|----------|--------|-------------------|

        | A       | High     | 2 days | UX enhancement    |

        | B       | Medium   | 1 day  | Bug fixing        |

        ```

        

        ### Visualize Relationships

        **Strategy**: Use diagrams (Mermaid) to express relationships instead of text descriptions.

        **When to Use**: For workflows, hierarchies, and dependency relationships.

        **Example**:

        ```

        # Instead of:

        Component A depends on Component B, which depends on Component C.

        

        # Use:

        ```mermaid

        graph TD

            A --> B --> C

        ```

        ```

        

        ### Hierarchical Structuring

        **Strategy**: Organize information in clear hierarchies with consistent header levels.

        **When to Use**: For all documentation to enforce clear relationships.

        **Example**:

        ```

        # H1: Major Section

        ## H2: Subsection

        ### H3: Specific Topic

        

        # Not mixed levels like:

        ### Specific Topic

        ## Subsection

        #### Detailed point

        ```

        

        ### Prune Obsolete Information

        **Strategy**: Actively remove information that is no longer relevant or accurate.

        **When to Use**: During regular audits or when new information supersedes old.

        **Example**: Delete sections that describe deprecated approaches, or move to lineage for historical reference.

        

        ## Applying Patterns in Memory Bank Files

        

        | File | Primary Patterns to Apply | Anti-Patterns to Avoid |

        |------|---------------------------|------------------------|

        | `0-distilledContext.md` | Root Anchoring, Extreme Compression | Detail Sprawl, Abstraction Leakage |

        | `1-projectbrief.md` | Root Anchoring, Progressive Abstraction | Root Drift, Detail Sprawl |

        | `2-productContext.md` | Root Anchoring, Tabular Compression | Temporal Buildup, Orphaned Content |

        | `3-systemPatterns.md` | Pattern Extraction, Visualize Relationships | Detail Sprawl, Abstraction Leakage |

        | `4-techContext.md` | Tabular Compression, Reference Instead of Duplication | Detail Sprawl, Structural Duplication |

        | `5-structureMap.md` | Visualize Relationships, Hierarchical Structuring | Abstraction Leakage, Temporal Buildup |

        | `6-activeContext.md` | Tabular Compression, Single Responsibility | Temporal Buildup, Passive Documentation |

        | `7-progress.md` | Tabular Compression, Metabolic Pruning | Passive Documentation, Temporal Buildup |

        | `8-tasks.md` | Root Anchoring, Tabular Compression | Orphaned Content, Root Drift |

        | `drift-monitor.md` | Tabular Compression, Pattern Extraction | Passive Documentation, Structural Duplication |

        | `simplification-candidates.md` | High-Impact Simplification, Tabular Compression | Detail Sprawl, Passive Documentation |

        | `/lineage/` | Cognitive Evolution, Single Responsibility | Temporal Buildup, Detail Sprawl |

        

        ---

        

        *Note: This glossary serves as a reference for consistent pattern application across the Memory Bank. It should be updated when new patterns are identified or existing patterns are refined.*

    ```



    ---



    #### `03-structure-design\10-simplification-candidates-template.md`



    ```markdown

        # High-Impact Simplification Candidates

        

        > **[Structural Role Reminder]**: This file formalizes the process of identifying and implementing simplifications that provide maximum impact for minimum intervention, focusing attention on strategic improvements.

        

        ## Evaluation Criteria

        

        1. **Minimal Implementation Effort** (1-10): How easy is this to implement?

           - *1*: Requires months of work, multiple developers

           - *5*: Requires days of work by one developer

           - *10*: Can be implemented in hours by one developer

        

        2. **Structural Clarity Gain** (1-10): How much clearer will the structure become?

           - *1*: Marginal improvement in structure clarity

           - *5*: Noticeable improvement in one area of the project

           - *10*: Transformative clarity enhancement across multiple areas

        

        3. **Widespread Impact** (1-10): How many areas will benefit?

           - *1*: Benefits limited to a small, isolated area

           - *5*: Benefits several interconnected components

           - *10*: Benefits the entire project ecosystem

        

        4. **Root Reinforcement** (1-10): How strongly does this support the project mission?

           - *1*: Tangential connection to project mission

           - *5*: Clear connection to project mission

           - *10*: Directly strengthens the core project purpose

        

        5. **Impact Score** = (Clarity × Impact × Root) ÷ Effort

           - Higher scores indicate more "bang for your buck" simplifications

        

        ## Current Candidates

        

        | ID | Simplification | Effort (1-10) | Clarity (1-10) | Impact (1-10) | Root (1-10) | Score | Status |

        |----|----------------|--------------|---------------|--------------|------------|-------|--------|

        | S1 | [Description] | [Score] | [Score] | [Score] | [Score] | [Calculated] | [Pending/In Progress/Completed] |

        | S2 | [Description] | [Score] | [Score] | [Score] | [Score] | [Calculated] | [Pending/In Progress/Completed] |

        | S3 | [Description] | [Score] | [Score] | [Score] | [Score] | [Calculated] | [Pending/In Progress/Completed] |

        

        ## Candidate Details

        

        ### [S1]: [Simplification Name]

        

        - **Description**: [Detailed description of the simplification opportunity]

        - **Current Complexity**: [Description of the current complex state]

        - **Proposed Solution**: [Specific approach to simplify]

        - **Implementation Path**:

          1. [Step 1]

          2. [Step 2]

          3. [Step 3]

        - **Expected Benefits**:

          - [Benefit 1]

          - [Benefit 2]

          - [Benefit 3]

        - **Potential Risks**:

          - [Risk 1]: [Mitigation]

          - [Risk 2]: [Mitigation]

        - **Files Affected**:

          - [File Path 1]: [Change description]

          - [File Path 2]: [Change description]

        - **Root Connection**: [Explicit connection to the project mission in `1-projectbrief.md`]

        

        ### [S2]: [Simplification Name]

        

        - [Same structure as above]

        

        ## Implemented Simplifications

        

        | ID | Simplification | Implementation Date | Actual Effort | Actual Impact | Lessons Learned |

        |----|----------------|---------------------|--------------|--------------|----------------|

        | [ID] | [Name] | [Date] | [Description] | [Description] | [Lessons] |

        | [ID] | [Name] | [Date] | [Description] | [Description] | [Lessons] |

        

        ## Simplification Ideas Parking Lot

        

        These are raw ideas that need further evaluation before becoming formal candidates:

        

        1. [Idea 1]

        2. [Idea 2]

        3. [Idea 3]

        

        ## Compression Check

        

        **[Date]**: Before adding the above simplification candidates, attempts were made to:

        

        1. [ ] Merge overlapping simplification ideas

        2. [ ] Elevate common patterns into higher-level architectural improvements

        3. [ ] Discard candidates with insufficient impact-to-effort ratio

        4. [ ] Prioritize candidates that directly reinforce project root purpose

        

        ---

        

        *Note: Each development cycle should identify and implement at least one high-impact simplification. After implementation, document the results in `7-progress.md` and update the "Implemented Simplifications" section here.*

    ```



    ---



    #### `04-process-tracking\06-activeContext-template.md`



    ```markdown

        # Active Context

        

        > **[Structural Role Reminder]**: This file tracks the current focus, in-progress work, bottlenecks, and active simplifications. It serves as an integration point for ongoing development efforts.

        

        ## Current Focus Areas

        

        ### Primary Focus: [Name]

        

        - **What**: [Brief description of the focus area]

        - **Why**: [How this focus area directly connects to project mission]

        - **Status**: [Current state - Active/Paused/Blocked]

        - **Key Decisions**: [Major decisions made in this area]

        - **Open Questions**: [Unresolved questions]

        

        ### Secondary Focus: [Name]

        

        - [Same structure as above]

        

        ## In-Progress Work

        

        | Work Item | Description | Owner | Status | Blockers | Root Connection |

        |-----------|-------------|-------|--------|----------|----------------|

        | [Item 1] | [Description] | [Owner] | [Status] | [Blockers] | [How this connects to project mission] |

        | [Item 2] | [Description] | [Owner] | [Status] | [Blockers] | [How this connects to project mission] |

        | [Item 3] | [Description] | [Owner] | [Status] | [Blockers] | [How this connects to project mission] |

        

        ## Current Bottlenecks

        

        | Bottleneck | Impact | Root Cause | Mitigation Strategy | Resolution Path |

        |------------|--------|------------|---------------------|----------------|

        | [Bottleneck 1] | [Impact] | [Root Cause] | [Mitigation] | [Resolution] |

        | [Bottleneck 2] | [Impact] | [Root Cause] | [Mitigation] | [Resolution] |

        | [Bottleneck 3] | [Impact] | [Root Cause] | [Mitigation] | [Resolution] |

        

        ## Active Simplifications

        

        ### Simplification 1: [Name]

        

        - **Target**: [What's being simplified]

        - **Current Complexity**: [Current state description]

        - **Approach**: [How we're simplifying]

        - **Expected Impact**: [Anticipated benefits]

        - **Status**: [Current state of simplification effort]

        - **Root Alignment**: [How this simplification supports project mission]

        

        ### Simplification 2: [Name]

        

        - [Same structure as above]

        

        ## Component Analysis

        

        | Component | Duplication | Complexity | Usage Pattern | Consolidation Opportunity |

        |-----------|-------------|------------|--------------|---------------------------|

        | [Component 1] | [Duplication] | [Complexity] | [Usage] | [Opportunity] |

        | [Component 2] | [Duplication] | [Complexity] | [Usage] | [Opportunity] |

        | [Component 3] | [Duplication] | [Complexity] | [Usage] | [Opportunity] |

        

        ## Recent Design Decisions

        

        | Decision | Rationale | Alternatives Considered | Impact | Date |

        |----------|-----------|-------------------------|--------|------|

        | [Decision 1] | [Rationale] | [Alternatives] | [Impact] | [Date] |

        | [Decision 2] | [Rationale] | [Alternatives] | [Impact] | [Date] |

        | [Decision 3] | [Rationale] | [Alternatives] | [Impact] | [Date] |

        

        ## Implementation Findings

        

        | Area | Finding | Implication | Action Needed |

        |------|---------|-------------|---------------|

        | [Area 1] | [Finding] | [Implication] | [Action] |

        | [Area 2] | [Finding] | [Implication] | [Action] |

        | [Area 3] | [Finding] | [Implication] | [Action] |

        

        ## Next Integration Points

        

        | What | When | Prerequisites | Complexity | Value |

        |------|------|---------------|------------|-------|

        | [Item 1] | [When] | [Prerequisites] | [Complexity] | [Value] |

        | [Item 2] | [When] | [Prerequisites] | [Complexity] | [Value] |

        | [Item 3] | [When] | [Prerequisites] | [Complexity] | [Value] |

        

        ---

        

        *Note: This file should be updated at the beginning and end of each significant work session. It serves as the primary integration point for active development knowledge.*

    ```



    ---



    #### `04-process-tracking\07-progress-template.md`



    ```markdown

        # Progress

        

        > **[Structural Role Reminder]**: This file tracks assimilation milestones, simplifications achieved, and measures progress versus entropy. It serves as the status log and structural integrity validator.

        

        ## Project Status Overview

        

        | Metric | Status | Trend | Target | Notes |

        |--------|--------|-------|--------|-------|

        | Overall Completion | [Percentage] | [↑/↓/→] | [Target] | [Notes] |

        | Technical Debt | [Rating] | [↑/↓/→] | [Target] | [Notes] |

        | Component Consolidation | [Progress] | [↑/↓/→] | [Target] | [Notes] |

        | Performance Metrics | [Metrics] | [↑/↓/→] | [Target] | [Notes] |

        | Test Coverage | [Percentage] | [↑/↓/→] | [Target] | [Notes] |

        

        ## Completed Milestones

        

        | Milestone | Completion Date | Description | Impact | Root Connection |

        |-----------|----------------|-------------|--------|----------------|

        | [Milestone 1] | [Date] | [Description] | [Impact] | [Connection to project mission] |

        | [Milestone 2] | [Date] | [Description] | [Impact] | [Connection to project mission] |

        | [Milestone 3] | [Date] | [Description] | [Impact] | [Connection to project mission] |

        

        ## Major Simplifications Achieved

        

        ### Simplification 1: [Name]

        

        - **Before**: [Description of prior complexity]

        - **After**: [Description of simplified state]

        - **Complexity Reduction**: [Quantifiable metrics if available]

        - **Value Added**: [Specific benefits gained]

        - **Date Completed**: [Date]

        - **Root Connection**: [How this reinforces project mission]

        

        ### Simplification 2: [Name]

        

        - [Same structure as above]

        

        ## Technical Debt Ledger

        

        | Debt Item | Area | Severity | Introduction Date | Mitigation Plan | Status |

        |-----------|------|----------|-------------------|----------------|--------|

        | [Debt 1] | [Area] | [High/Medium/Low] | [Date] | [Plan] | [Status] |

        | [Debt 2] | [Area] | [High/Medium/Low] | [Date] | [Plan] | [Status] |

        | [Debt 3] | [Area] | [High/Medium/Low] | [Date] | [Plan] | [Status] |

        

        ## Metrics Tracking

        

        ### Core Metrics

        

        | Metric | Initial | Current | Target | Trend |

        |--------|---------|---------|--------|-------|

        | [Metric 1] | [Initial] | [Current] | [Target] | [↑/↓/→] |

        | [Metric 2] | [Initial] | [Current] | [Target] | [↑/↓/→] |

        | [Metric 3] | [Initial] | [Current] | [Target] | [↑/↓/→] |

        

        ### Component Metrics

        

        | Component | Duplication | Complexity | Usage | Reusability Score |

        |-----------|-------------|------------|-------|------------------|

        | [Component 1] | [Measure] | [Measure] | [Measure] | [Score] |

        | [Component 2] | [Measure] | [Measure] | [Measure] | [Score] |

        | [Component 3] | [Measure] | [Measure] | [Measure] | [Score] |

        

        ## Known Issues

        

        | Issue | Impact | Root Cause | Workaround | Resolution Plan | Priority |

        |-------|--------|------------|------------|----------------|----------|

        | [Issue 1] | [Impact] | [Cause] | [Workaround] | [Plan] | [Priority] |

        | [Issue 2] | [Impact] | [Cause] | [Workaround] | [Plan] | [Priority] |

        | [Issue 3] | [Impact] | [Cause] | [Workaround] | [Plan] | [Priority] |

        

        ## Retrospective Insights

        

        | Insight | Area | Source | Application | Date |

        |---------|------|--------|-------------|------|

        | [Insight 1] | [Area] | [Source] | [How applied] | [Date] |

        | [Insight 2] | [Area] | [Source] | [How applied] | [Date] |

        | [Insight 3] | [Area] | [Source] | [How applied] | [Date] |

        

        ## High-Impact Simplifications Log

        

        | Date | Simplification | Before Complexity | After Complexity | Value Impact |

        |------|----------------|-------------------|------------------|--------------|

        | [Date] | [Description] | [Before] | [After] | [Impact] |

        | [Date] | [Description] | [Before] | [After] | [Impact] |

        | [Date] | [Description] | [Before] | [After] | [Impact] |

        

        ---

        

        *Note: This progress log should be updated after every significant milestone or simplification. It should focus on measuring value delivered and complexity reduced, always connecting back to the project's root mission.*

    ```



    ---



    #### `04-process-tracking\08-tasks-template.md`



    ```markdown

        # Tasks

        

        > **[Structural Role Reminder]**: This file contains concrete, structure-anchored tasks that directly support the project mission. Each task must trace its lineage to the root purpose.

        

        ## Active Tasks

        

        ### High Priority

        

        | ID | Task | Description | Value | Dependencies | Assigned To | Status | Root Connection |

        |----|------|-------------|-------|--------------|-------------|--------|----------------|

        | [H1] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        | [H2] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        | [H3] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        

        ### Medium Priority

        

        | ID | Task | Description | Value | Dependencies | Assigned To | Status | Root Connection |

        |----|------|-------------|-------|--------------|-------------|--------|----------------|

        | [M1] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        | [M2] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        | [M3] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        

        ### Low Priority

        

        | ID | Task | Description | Value | Dependencies | Assigned To | Status | Root Connection |

        |----|------|-------------|-------|--------------|-------------|--------|----------------|

        | [L1] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        | [L2] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        | [L3] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        

        ## Task Details

        

        ### [Task ID]: [Task Name]

        

        - **Description**: [Detailed description of the task]

        - **Value Proposition**: [Specific value this task delivers]

        - **Acceptance Criteria**:

          - [Criterion 1]

          - [Criterion 2]

          - [Criterion 3]

        - **Implementation Approach**: [Recommended approach]

        - **Dependencies**:

          - [Dependency 1]

          - [Dependency 2]

        - **Risks**:

          - [Risk 1]: [Mitigation strategy]

          - [Risk 2]: [Mitigation strategy]

        - **Structure Connection**: [How this task aligns with system patterns in `3-systemPatterns.md`]

        - **Root Connection**: [How this task supports project mission in `1-projectbrief.md`]

        

        ### [Task ID]: [Task Name]

        

        - [Same structure as above]

        

        ## Completed Tasks

        

        | ID | Task | Completion Date | Outcome | Impact |

        |----|------|----------------|---------|--------|

        | [ID] | [Task Name] | [Date] | [Outcome] | [Impact] |

        | [ID] | [Task Name] | [Date] | [Outcome] | [Impact] |

        | [ID] | [Task Name] | [Date] | [Outcome] | [Impact] |

        

        ## Blocked Tasks

        

        | ID | Task | Blocker | Impact | Resolution Path |

        |----|------|---------|--------|----------------|

        | [ID] | [Task Name] | [Blocker] | [Impact] | [Path to resolution] |

        | [ID] | [Task Name] | [Blocker] | [Impact] | [Path to resolution] |

        | [ID] | [Task Name] | [Blocker] | [Impact] | [Path to resolution] |

        

        ## Task Themes

        

        ### Theme 1: [Name]

        

        - **Purpose**: [What this set of tasks aims to achieve]

        - **Related Tasks**: [IDs of related tasks]

        - **Key Dependencies**: [Critical dependencies for this theme]

        - **Success Metrics**: [How success will be measured]

        - **Root Connection**: [How this theme supports project mission]

        

        ### Theme 2: [Name]

        

        - [Same structure as above]

        

        ---

        

        *Note: All tasks must be structure-anchored and root-aligned. Never create floating tasks disconnected from the project structure and mission. Each task must add value through complexity reduction, clarity improvement, or direct mission advancement.*

    ```



    ---



    #### `04-process-tracking\09-drift-monitor-template.md`



    ```markdown

        # Drift Monitor

        

        > **[Structural Role Reminder]**: This file provides a proactive monitoring mechanism for Memory Bank integrity, serving as an early warning system for structural decay.

        

        ## Last Structure Validation: [Date]

        

        | File | Original Purpose | Current Usage | Drift Status | Action Needed |

        |------|-----------------|---------------|-------------|---------------|

        | `0-distilledContext.md` | Ultra-compressed project essence in 2-3 bullets. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        | `1-projectbrief.md` | Root purpose definition and critical constraints. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        | `2-productContext.md` | User needs, problems solved, and external context. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        | `3-systemPatterns.md` | System architecture, component hierarchy, and data flows. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        | `4-techContext.md` | Technology stack, constraints, and integration points. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        | `5-structureMap.md` | Current and target file structure with migration path. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        | `6-activeContext.md` | Current focus, bottlenecks, and in-progress simplifications. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        | `7-progress.md` | Milestones, metrics, and simplifications achieved. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        | `8-tasks.md` | Structure-anchored tasks supporting project mission. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        

        ## Common Drift Patterns

        

        | Pattern | Warning Signs | Correction Strategy |

        |---------|--------------|---------------------|

        | Purpose Dilution | File no longer clearly connects to root purpose | Re-anchor to `1-projectbrief.md`, remove unrelated content |

        | Scope Expansion | File growing beyond single responsibility | Move content to appropriate files, elevate patterns |

        | Abstraction Leakage | Details appearing in higher abstraction levels | Push details down to appropriate level |

        | Content Duplication | Same concepts appearing in multiple files | Consolidate to most appropriate file, reference as needed |

        | Temporal Buildup | Content accumulating chronologically without structure | Reorganize by abstraction, not timeline |

        | Orphaned Information | Insights without clear structural connection | Connect to structure or remove |

        

        ## Drift Correction Log

        

        | Date | File | Issue Detected | Correction Applied |

        |------|------|----------------|-------------------|

        | [Date] | [File] | [Issue] | [Correction] |

        | [Date] | [File] | [Issue] | [Correction] |

        | [Date] | [File] | [Issue] | [Correction] |

        

        ## Drift Prevention Checklist

        

        Before adding new content to any Memory Bank file, verify:

        

        - [ ] **Root Connection**: Does this clearly support the project mission in `1-projectbrief.md`?

        - [ ] **Correct Abstraction Level**: Is this at the appropriate level of abstraction for this file?

        - [ ] **Single Responsibility**: Does this respect the file's single structural role?

        - [ ] **No Duplication**: Is this information not already captured elsewhere?

        - [ ] **Compression Attempted**: Have I tried to merge/elevate/dissolve this into existing patterns?

        - [ ] **Structural Clarity**: Does this enhance rather than obscure the project's structure?

        

        ---

        

        *Note: Update this drift monitor after every significant Memory Bank update or at least monthly. Use the CLI tool `./mb-tools.sh mb_audit` to automatically check for potential drift.*

    ```



    ---



    #### `05-evolution\09-lineage-template.md`



    ```markdown

        # Lineage Entry: [Title of Cognitive Shift]

        

        > **[Structural Role Reminder]**: This file captures a significant cognitive shift in project understanding, serving as a checkpoint in the Memory Bank's evolution.

        

        ## Context

        

        **Date**: [Date of Entry]

        **Memory Bank Version**: [Version Number or Identifier]

        **Current Project Phase**: [Development Phase]

        

        ## Prior Understanding

        

        [Description of how the project was understood before this insight]

        

        ## Cognitive Shift

        

        ### Key Insight

        

        [The core realization or understanding that changed]

        

        ### Catalyzing Factors

        

        - [What triggered this shift in understanding]

        - [Specific events, observations, or analyses]

        - [External or internal factors that prompted reconsideration]

        

        ## Structural Impact

        

        ### Memory Bank Changes

        

        | File | Changes | Justification |

        |------|---------|---------------|

        | [File Path] | [Description of Changes] | [Why the change was necessary] |

        | [File Path] | [Description of Changes] | [Why the change was necessary] |

        | [File Path] | [Description of Changes] | [Why the change was necessary] |

        

        ### Structure Evolution

        

        **Before**:

        ```plaintext

        [Relevant structure representation before the change]

        ```

        

        **After**:

        ```plaintext

        [Relevant structure representation after the change]

        ```

        

        ### Abstraction Evolution

        

        [How this shift affects the project's abstraction layers]

        

        ## Root Connection

        

        [How this cognitive shift reinforces or refines connection to the project's root purpose]

        

        ## Practical Implications

        

        ### Development Impact

        

        [How this shift affects the development approach]

        

        ### Verification Method

        

        [How we can verify this new understanding is more accurate or valuable]

        

        ### Next Steps

        

        [Immediate actions prompted by this cognitive shift]

        

        ---

        

        *Note: Lineage entries serve as cognitive milestones, capturing key moments where understanding fundamentally shifted. They provide context for why the Memory Bank evolved as it did and preserve the reasoning behind structural changes.*

    ```



    ---



    #### `06-tools\01-mb-quick-setup.sh`



    ```bash

        #!/bin/bash

        # Memory Bank Quick Setup Script

        # 

        # Usage: 

        #   chmod +x mb-quick-setup.sh

        #   ./mb-quick-setup.sh [target-directory]

        #

        # Creates a lightweight Memory Bank structure in the specified directory

        # or in the current directory if no target is specified.

        

        # Set target directory

        TARGET_DIR=${1:-.}

        MB_DIR="$TARGET_DIR/memory-bank"

        

        # Display header

        echo "Memory Bank Quick Setup"

        echo "======================="

        echo

        

        # Create Memory Bank directory

        mkdir -p "$MB_DIR"

        echo "📁 Created Memory Bank directory: $MB_DIR"

        

        # Function to create file with content

        create_file() {

            local file="$1"

            local title="$2"

            local description="$3"

            

            cat > "$file" << EOL

        # $title

        

        > $description

        

        EOL

        

            echo "📝 Created $file"

        }

        

        # Create essential structure

        create_file "$MB_DIR/0-distilledContext.md" "Project Distilled Context" "This file contains the absolute essence of the project in 2-3 bullets."

        cat >> "$MB_DIR/0-distilledContext.md" << 'EOL'

        

        - 

        - 

        - 

        

        EOL

        

        create_file "$MB_DIR/1-projectbrief.md" "Project Brief" "This file defines why this project must exist."

        cat >> "$MB_DIR/1-projectbrief.md" << 'EOL'

        

        ## Core Mission

        

        [Define the irreducible purpose of this project in 1-2 sentences]

        

        ## Value Proposition

        

        [Describe the unique value this project provides]

        

        ## Critical Constraints

        

        [List 2-3 non-negotiable constraints that shape the project]

        

        EOL

        

        create_file "$MB_DIR/2-productContext.md" "Product Context" "This file maps the external reality: users, needs, and outcomes."

        cat >> "$MB_DIR/2-productContext.md" << 'EOL'

        

        ## User Archetypes

        

        [Identify core user types]

        

        ## Problems Solved

        

        [List the key problems this project solves]

        

        ## Operating Environment

        

        [Describe where and how this product will be used]

        

        EOL

        

        create_file "$MB_DIR/6-activeContext.md" "Active Context" "This file tracks current focus, bottlenecks, and in-progress simplifications."

        cat >> "$MB_DIR/6-activeContext.md" << 'EOL'

        

        ## Current Focus

        

        [What are you working on right now?]

        

        ## Bottlenecks

        

        [What's blocking progress?]

        

        ## In-Progress Simplifications

        

        [What complexity are you trying to reduce?]

        

        EOL

        

        create_file "$MB_DIR/8-tasks.md" "Tasks" "This file contains concrete, structure-anchored tasks that trace to the root goal."

        cat >> "$MB_DIR/8-tasks.md" << 'EOL'

        

        ## Active Tasks

        

        - [ ] Task 1: [Description]

        - [ ] Task 2: [Description]

        

        ## Completed Tasks

        

        - [x] Initial Memory Bank setup

        

        EOL

        

        # Create README

        cat > "$MB_DIR/README.md" << 'EOL'

        # Project Memory Bank

        

        This directory contains the cognitive architecture for this project.

        

        ## Core Files

        

        - **0-distilledContext.md**: Ultra-compressed project essence

        - **1-projectbrief.md**: Root purpose definition

        - **2-productContext.md**: User needs and external context

        - **6-activeContext.md**: Current focus and bottlenecks

        - **8-tasks.md**: Structure-anchored tasks

        

        ## Memory Bank Principles

        

        - **File-Structure-First**: Cognitive architecture expressed through file structure

        - **Root-First Thinking**: Every insight relates upward to the project's mission

        - **Persistent Simplification**: Actions aim to reduce net complexity

        - **Value Extraction Bias**: Focus on actionable insight over detail

        - **Outward Mapping**: Think from root → abstraction layers → implementation

        

        ## Daily Usage

        

        1. Begin each work session by reading `0-distilledContext.md`

        2. Update your current focus in `6-activeContext.md`

        3. Work on tasks from `8-tasks.md`

        4. Extract patterns to create more structure as needed

        

        EOL

        

        # Completion message

        echo

        echo "✅ Memory Bank setup complete!"

        echo

        echo "Next steps:"

        echo "1. Define your project's essence in 0-distilledContext.md"

        echo "2. Establish your project's purpose in 1-projectbrief.md"

        echo "3. Begin each work session by reading 0-distilledContext.md"

        echo

        echo "For more information, visit: https://github.com/yourname/memory-bank-templates"

    ```



    ---



    #### `06-tools\02-mb-assistant.sh`



    ```bash

        #!/bin/bash

        # Memory Bank Assistant - Interactive guidance for Memory Bank usage

        # 

        # Usage: 

        #   chmod +x mb-assistant.sh

        #   ./mb-assistant.sh

        #

        # Provides interactive guidance for Memory Bank usage based on common scenarios

        

        # Colors

        GREEN='\033[0;32m'

        BLUE='\033[0;34m'

        YELLOW='\033[1;33m'

        RED='\033[0;31m'

        NC='\033[0m' # No Color

        

        # Display header

        clear

        echo -e "${BLUE}=================================${NC}"

        echo -e "${BLUE}    Memory Bank Assistant    ${NC}"

        echo -e "${BLUE}=================================${NC}"

        echo

        

        # Function to show main menu

        show_main_menu() {

          echo -e "\n${YELLOW}What would you like help with?${NC}"

          echo "1. I'm new to Memory Bank - how do I start?"

          echo "2. Which files should I update today?"

          echo "3. How do I use Memory Bank with my AI assistant?"

          echo "4. I'm feeling overwhelmed - how can I simplify?"

          echo "5. Memory Bank troubleshooting"

          echo "6. Memory Bank best practices"

          echo "7. Exit"

          echo

          echo -n "Enter your choice (1-7): "

        }

        

        # Function to handle "new to Memory Bank" scenario

        new_to_memory_bank() {

          clear

          echo -e "${BLUE}=================================${NC}"

          echo -e "${BLUE}    Getting Started Guide    ${NC}"

          echo -e "${BLUE}=================================${NC}"

          echo

          echo -e "${YELLOW}Welcome to Memory Bank!${NC}"

          echo

          echo "Memory Bank is a system for maintaining structural clarity in your projects."

          echo "It helps you focus on what matters most and avoid complexity creep."

          echo

          echo -e "${GREEN}Here's how to get started in 5 minutes:${NC}"

          echo

          echo "1. Create the minimal Memory Bank structure:"

          echo "   - Run: ./06-tools/1-mb-quick-setup.sh"

          echo "   - Or create these files manually:"

          echo "     * memory-bank/0-distilledContext.md (project essence)"

          echo "     * memory-bank/1-projectbrief.md (project purpose)"

          echo

          echo "2. Define your project's essence in 0-distilledContext.md"

          echo "   - What is the absolute core of your project in 2-3 bullets?"

          echo "   - Keep it ultra-compressed and focused on the essentials"

          echo

          echo "3. Define your project's purpose in 1-projectbrief.md"

          echo "   - Why must this project exist?"

          echo "   - What unique value does it provide?"

          echo "   - What critical constraints shape it?"

          echo

          echo "4. Begin each work session by reading 0-distilledContext.md"

          echo

          echo -e "${YELLOW}Ready for more advanced usage?${NC}"

          echo

          echo "5. Create additional files as needed:"

          echo "   - 2-productContext.md (users and problems solved)"

          echo "   - 6-activeContext.md (current focus and bottlenecks)"

          echo "   - 8-tasks.md (structure-anchored tasks)"

          echo

          echo -e "${GREEN}That's it! You're now using Memory Bank.${NC}"

          echo

          echo -n "Press Enter to return to the main menu..."

          read

        }

        

        # Function to handle "which files to update" scenario

        which_files_to_update() {

          clear

          echo -e "${BLUE}=================================${NC}"

          echo -e "${BLUE}    Daily/Weekly Updates    ${NC}"

          echo -e "${BLUE}=================================${NC}"

          echo

          

          # Get current day

          DAY=$(date +%A)

          

          echo -e "${YELLOW}Today is $DAY. Here's what to update:${NC}"

          echo

          echo -e "${GREEN}Daily Updates:${NC}"

          echo "- 0-distilledContext.md (READ ONLY - 30 seconds)"

          echo "- 6-activeContext.md (UPDATE - what you're focusing on today)"

          echo "- 8-tasks.md (UPDATE - tasks for today)"

          echo

          if [[ "$DAY" == "Friday" ]]; then

            echo -e "${GREEN}Weekly Updates (Friday):${NC}"

            echo "- 7-progress.md (UPDATE - milestones achieved this week)"

            echo "- Identify at least one simplification opportunity"

            echo

          fi

          

          echo -e "${YELLOW}Other periodic updates:${NC}"

          echo

          echo "After major milestones:"

          echo "- 3-systemPatterns.md (architecture patterns)"

          echo "- 5-structureMap.md (file structure)"

          echo

          echo "When project scope/direction changes:"

          echo "- 0-distilledContext.md (project essence)"

          echo "- 1-projectbrief.md (project purpose)"

          echo "- 2-productContext.md (users and needs)"

          echo "- 4-techContext.md (technology stack)"

          echo

          echo -n "Press Enter to return to the main menu..."

          read

        }

        

        # Function to handle "AI assistant usage" scenario

        ai_assistant_usage() {

          clear

          echo -e "${BLUE}=================================${NC}"

          echo -e "${BLUE}    AI Assistant Integration    ${NC}"

          echo -e "${BLUE}=================================${NC}"

          echo

          echo -e "${YELLOW}Using Memory Bank with AI coding assistants:${NC}"

          echo

          echo "Memory Bank works exceptionally well with AI coding assistants like Cline, Cursor, or GitHub Copilot."

          echo

          echo -e "${GREEN}Setup instructions:${NC}"

          echo

          echo "1. Find the system instruction file:"

          echo "   - 07-guides/13-memory-bank-system-instruction.md"

          echo

          echo "2. Copy the content of the file (everything between the code block markers)"

          echo

          echo "3. Paste it into your AI assistant's system instruction settings"

          echo "   - For Cline in VS Code: Settings > System Instructions"

          echo "   - For Cursor: Settings > AI > Instructions"

          echo "   - For GitHub Copilot Chat: Create a YAML file in .github/"

          echo

          echo "4. Tell your AI assistant to help you with Memory Bank"

          echo "   Example: \"Set up a Memory Bank for this project and help me define the core files.\""

          echo

          echo -e "${YELLOW}Daily usage with AI:${NC}"

          echo

          echo "5. Ask your AI to help maintain the Memory Bank files:"

          echo "   - \"Update our Memory Bank files based on today's work\""

          echo "   - \"Extract patterns from our recent code into 3-systemPatterns.md\""

          echo "   - \"Identify potential simplifications for our project\""

          echo

          echo -n "Press Enter to return to the main menu..."

          read

        }

        

        # Function to handle "simplify Memory Bank" scenario

        simplify_memory_bank() {

          clear

          echo -e "${BLUE}=================================${NC}"

          echo -e "${BLUE}    Simplifying Memory Bank    ${NC}"

          echo -e "${BLUE}=================================${NC}"

          echo

          echo -e "${YELLOW}Feeling overwhelmed by Memory Bank?${NC}"

          echo

          echo "Memory Bank should reduce complexity, not add to it. Here's how to simplify:"

          echo

          echo -e "${GREEN}The absolute minimal approach:${NC}"

          echo

          echo "1. Just maintain these two files:"

          echo "   - 0-distilledContext.md (project essence)"

          echo "   - 1-projectbrief.md (project purpose)"

          echo

          echo "2. Read them at the start of each work session"

          echo

          echo "That's it! Everything else is optional."

          echo

          echo -e "${YELLOW}If you have a few more minutes:${NC}"

          echo

          echo "Add just these three more files for a balanced approach:"

          echo "- 2-productContext.md (users and problems solved)"

          echo "- 6-activeContext.md (current focus)"

          echo "- 8-tasks.md (structure-anchored tasks)"

          echo

          echo -e "${GREEN}Remember:${NC}"

          echo "- Don't treat Memory Bank as documentation that needs to be complete"

          echo "- It's a living system that helps you maintain clarity"

          echo "- Quality over quantity - a focused 0-distilledContext.md is worth more"

          echo "  than dozens of detailed but unused files"

          echo

          echo -n "Press Enter to return to the main menu..."

          read

        }

        

        # Function to handle "troubleshooting" scenario

        troubleshooting() {

          clear

          echo -e "${BLUE}=================================${NC}"

          echo -e "${BLUE}    Memory Bank Troubleshooting    ${NC}"

          echo -e "${BLUE}=================================${NC}"

          echo

          echo -e "${YELLOW}Common Memory Bank issues:${NC}"

          echo

          echo -e "${RED}Problem:${NC} \"My Memory Bank feels like passive documentation\""

          echo -e "${GREEN}Solution:${NC}"

          echo "- Revisit 0-distilledContext.md and ensure it's ultra-compressed"

          echo "- Extract patterns in 3-systemPatterns.md instead of listing instances"

          echo "- Focus on active files (6-activeContext.md, 8-tasks.md)"

          echo "- Delete any content that doesn't connect to your root purpose"

          echo

          echo -e "${RED}Problem:${NC} \"I don't know which files to update\""

          echo -e "${GREEN}Solution:${NC}"

          echo "- Daily: Just update 6-activeContext.md and 8-tasks.md"

          echo "- Weekly: Update 7-progress.md"

          echo "- Only update others when there's a significant change"

          echo

          echo -e "${RED}Problem:${NC} \"Memory Bank isn't helping me manage complexity\""

          echo -e "${GREEN}Solution:${NC}"

          echo "- Ensure your 0-distilledContext.md is truly essential"

          echo "- Work through 3-systemPatterns.md to identify repeated patterns"

          echo "- Use 6-activeContext.md to focus on bottlenecks"

          echo "- Explicitly identify simplification opportunities"

          echo

          echo -e "${RED}Problem:${NC} \"My team isn't using Memory Bank consistently\""

          echo -e "${GREEN}Solution:${NC}"

          echo "- Start team meetings by reviewing 0-distilledContext.md"

          echo "- Simplify to just the core files (0, 1, 6, 8)"

          echo "- Integrate with existing team workflows"

          echo "- Assign a Memory Bank steward to maintain it"

          echo

          echo -n "Press Enter to return to the main menu..."

          read

        }

        

        # Function to handle "best practices" scenario

        best_practices() {

          clear

          echo -e "${BLUE}=================================${NC}"

          echo -e "${BLUE}    Memory Bank Best Practices    ${NC}"

          echo -e "${BLUE}=================================${NC}"

          echo

          echo -e "${YELLOW}Memory Bank best practices:${NC}"

          echo

          echo -e "${GREEN}1. Start with essence, not details${NC}"

          echo "   - Define 0-distilledContext.md first"

          echo "   - Be ruthless about what goes in it"

          echo "   - Limit to 2-3 bullets total"

          echo

          echo -e "${GREEN}2. Make it part of your workflow${NC}"

          echo "   - Begin each day by reading 0-distilledContext.md"

          echo "   - Update 6-activeContext.md before and after work"

          echo "   - Link your tasks in 8-tasks.md to the root purpose"

          echo

          echo -e "${GREEN}3. Compress, don't expand${NC}"

          echo "   - Elevate details to patterns"

          echo "   - Merge similar concepts"

          echo "   - Delete anything not connected to the root"

          echo

          echo -e "${GREEN}4. Focus on high-impact areas${NC}"

          echo "   - 0-distilledContext.md (clarity)"

          echo "   - 3-systemPatterns.md (architecture)"

          echo "   - 6-activeContext.md (daily focus)"

          echo "   - 8-tasks.md (action items)"

          echo

          echo -e "${GREEN}5. Don't Let Perfect Be The Enemy of Good${NC}"

          echo "   - An imperfect, used Memory Bank is better than a perfect, unused one"

          echo "   - Start minimal and add only what delivers value"

          echo "   - It's OK to have incomplete files – they'll evolve over time"

          echo

          echo -n "Press Enter to return to the main menu..."

          read

        }

        

        # Main program loop

        while true; do

          clear

          show_main_menu

          read CHOICE

          

          case $CHOICE in

            1) new_to_memory_bank ;;

            2) which_files_to_update ;;

            3) ai_assistant_usage ;;

            4) simplify_memory_bank ;;

            5) troubleshooting ;;

            6) best_practices ;;

            7) 

              echo -e "\n${GREEN}Thank you for using Memory Bank Assistant!${NC}"

              echo "Remember: Memory Bank is not what you know about the project;"

              echo "it is what the project knows about itself."

              echo

              exit 0

              ;;

            *) 

              echo -e "\n${RED}Invalid option. Please enter a number between 1 and 7.${NC}"

              sleep 2

              ;;

          esac

        done

    ```



    ---



    #### `06-tools\09-mb-tools.sh`



    ```bash

        #!/bin/bash

        # Memory Bank Tools - CLI utilities for Memory Bank maintenance

        # This script provides automation for Memory Bank system maintenance and health checks

        

        # Constants

        MB_DIR="memory-bank"

        LINEAGE_DIR="$MB_DIR/lineage"

        EPOCHS_DIR="$MB_DIR/epochs"

        REQUIRED_FILES=("0-distilledContext.md" "1-projectbrief.md" "2-productContext.md" "3-systemPatterns.md" "4-techContext.md" "5-structureMap.md" "6-activeContext.md" "7-progress.md" "8-tasks.md")

        OPTIONAL_FILES=("drift-monitor.md" "simplification-candidates.md")

        

        # Colors for terminal output

        RED='\033[0;31m'

        GREEN='\033[0;32m'

        YELLOW='\033[0;33m'

        BLUE='\033[0;34m'

        NC='\033[0m' # No Color

        

        # Print colored message

        function print_message() {

          local color=$1

          local message=$2

          echo -e "${color}${message}${NC}"

        }

        

        # Check if Memory Bank exists

        function check_mb_exists() {

          if [ ! -d "$MB_DIR" ]; then

            print_message "$RED" "‚ùå Memory Bank directory not found. Run 'mb_init' to create it."

            return 1

          fi

          return 0

        }

        

        # Initialize a new Memory Bank

        function mb_init() {

          print_message "$BLUE" "üèóÔ∏è  Initializing Memory Bank structure..."

          

          # Create directories

          mkdir -p "$MB_DIR"

          mkdir -p "$LINEAGE_DIR"

          mkdir -p "$EPOCHS_DIR"

          

          # Placeholder logic for copying template files

          # In a real implementation, this would copy from a templates directory

          print_message "$YELLOW" "üìã Would create the following files:"

          for file in "${REQUIRED_FILES[@]}"; do

            echo "  - $MB_DIR/$file"

          done

          

          # Create an initial drift monitor

          echo -e "# Drift Monitor\n\n## Last Structure Validation: $(date +%Y-%m-%d)\n\n| File | Original Purpose | Current Usage | Drift Status | Action Needed |\n|------|-----------------|---------------|-------------|---------------|\n" > "$MB_DIR/drift-monitor.md"

          

          for file in "${REQUIRED_FILES[@]}"; do

            echo "| \`$file\` | | | ‚úÖ | |" >> "$MB_DIR/drift-monitor.md"

          done

          

          echo -e "\n## Drift Correction Log\n\n| Date | File | Issue Detected | Correction Applied |\n|------|------|----------------|-------------------|\n" >> "$MB_DIR/drift-monitor.md"

          

          # Create initial simplification candidates file

          echo -e "# High-Impact Simplification Candidates\n\n## Evaluation Criteria:\n1. **Minimal Implementation Effort** (1-10): How easy is this to implement?\n2. **Structural Clarity Gain** (1-10): How much clearer will the structure become?\n3. **Widespread Impact** (1-10): How many areas will benefit?\n4. **Root Reinforcement** (1-10): How strongly does this support the project mission?\n5. **Impact Score** = Clarity √ó Impact √ó Root √∑ Effort\n\n## Current Candidates:\n\n| ID | Simplification | Effort | Clarity | Impact | Root | Score | Status |\n|----|----------------|--------|---------|--------|------|-------|--------|\n" > "$MB_DIR/simplification-candidates.md"

          

          print_message "$GREEN" "‚úÖ Memory Bank initialized successfully."

          print_message "$YELLOW" "‚ö†Ô∏è  Next steps:"

          print_message "$YELLOW" "  1. Fill in the template files with your project information"

          print_message "$YELLOW" "  2. Run 'mb_validate' to check your Memory Bank structure"

        }

        

        # Validate Memory Bank structure

        function mb_validate() {

          if ! check_mb_exists; then

            return 1

          fi

          

          print_message "$BLUE" "üîç Validating Memory Bank structure..."

          

          local errors=0

          local warnings=0

          

          # Check for required files

          for file in "${REQUIRED_FILES[@]}"; do

            if [ ! -f "$MB_DIR/$file" ]; then

              print_message "$RED" "‚ùå Missing required file: $MB_DIR/$file"

              errors=$((errors+1))

            else

              # Check for structural role reminder

              if ! grep -q "\[Structural Role" "$MB_DIR/$file"; then

                print_message "$YELLOW" "‚ö†Ô∏è  File $file is missing a Structural Role Reminder"

                warnings=$((warnings+1))

              fi

              

              # Check file size (too small likely means incomplete)

              local size=$(wc -c < "$MB_DIR/$file")

              if [ $size -lt 100 ]; then

                print_message "$YELLOW" "‚ö†Ô∏è  File $file seems too small ($size bytes). Is it complete?"

                warnings=$((warnings+1))

              fi

            fi

          done

          

          # Check for cross-references between files

          print_message "$BLUE" "üîÑ Checking cross-references..."

          

          # Check if projectbrief is referenced in other files

          for file in "${REQUIRED_FILES[@]}"; do

            if [ "$file" != "1-projectbrief.md" ] && [ -f "$MB_DIR/$file" ]; then

              if ! grep -q "1-projectbrief" "$MB_DIR/$file"; then

                print_message "$YELLOW" "‚ö†Ô∏è  File $file does not reference 1-projectbrief.md"

                warnings=$((warnings+1))

              fi

            fi

          done

          

          # Check for lineage tracking

          if [ -z "$(ls -A $LINEAGE_DIR 2>/dev/null)" ]; then

            print_message "$YELLOW" "‚ö†Ô∏è  No lineage entries found. Consider adding cognitive shift documentation."

            warnings=$((warnings+1))

          fi

          

          # Summary

          if [ $errors -eq 0 ] && [ $warnings -eq 0 ]; then

            print_message "$GREEN" "‚úÖ Memory Bank structure is valid with no warnings."

          elif [ $errors -eq 0 ]; then

            print_message "$YELLOW" "‚ö†Ô∏è  Memory Bank has $warnings warnings but no critical errors."

          else

            print_message "$RED" "‚ùå Memory Bank has $errors errors and $warnings warnings."

          fi

        }

        

        # Check for compression opportunities

        function mb_compress() {

          if ! check_mb_exists; then

            return 1

          fi

          

          print_message "$BLUE" "üîÑ Analyzing Memory Bank for compression opportunities..."

          

          # Find repeated phrases across files (crude example)

          print_message "$BLUE" "üîç Looking for common phrases across files (potential patterns)..."

          

          # This is a simplified example - in reality you'd need more sophisticated analysis

          for phrase in "component" "structure" "pattern" "user" "mission" "abstraction"; do

            echo -e "\nOccurrences of '$phrase':"

            grep -i -r "$phrase" --include="*.md" $MB_DIR | wc -l

            

            # List files with most occurrences

            print_message "$YELLOW" "  Top files mentioning '$phrase':"

            grep -i -r "$phrase" --include="*.md" $MB_DIR | cut -d: -f1 | sort | uniq -c | sort -nr | head -3

          done

          

          # Check for long files (potential splitting candidates)

          print_message "$BLUE" "üìä Identifying unusually large files (potential splitting candidates)..."

          find $MB_DIR -name "*.md" -type f -exec wc -l {} \; | sort -nr | head -5

          

          # Look for potential duplication

          print_message "$BLUE" "üîÑ Checking for potential content duplication..."

          for file1 in $MB_DIR/*.md; do

            for file2 in $MB_DIR/*.md; do

              if [ "$file1" != "$file2" ]; then

                common_lines=$(comm -12 <(sort "$file1") <(sort "$file2") | wc -l)

                if [ $common_lines -gt 10 ]; then

                  print_message "$YELLOW" "‚ö†Ô∏è  $file1 and $file2 share $common_lines similar lines"

                fi

              fi

            done

          done

          

          print_message "$GREEN" "‚úÖ Compression analysis complete."

          print_message "$YELLOW" "‚ö†Ô∏è  Remember: These are automated suggestions only."

          print_message "$YELLOW" "   Always verify opportunities and maintain root connection when compressing."

        }

        

        # Perform full Memory Bank audit

        function mb_audit() {

          if ! check_mb_exists; then

            return 1

          fi

          

          print_message "$BLUE" "üîç Performing comprehensive Memory Bank audit..."

          

          # Run validation first

          mb_validate

          

          # Check last updates

          print_message "$BLUE" "üìÖ Checking file freshness..."

          find $MB_DIR -name "*.md" -type f -exec ls -la {} \; | sort -k6,8

          

          # Check word counts

          print_message "$BLUE" "üìä File size distribution (word count)..."

          for file in $MB_DIR/*.md; do

            word_count=$(wc -w < "$file")

            echo "$file: $word_count words"

          done

          

          # Check for active tasks

          print_message "$BLUE" "üìã Checking active tasks..."

          if [ -f "$MB_DIR/8-tasks.md" ]; then

            grep -A 1 "High Priority" "$MB_DIR/8-tasks.md"

          else

            print_message "$RED" "‚ùå Tasks file not found"

          fi

          

          # Check root connection

          print_message "$BLUE" "üîÑ Verifying root connection integrity..."

          if [ -f "$MB_DIR/1-projectbrief.md" ]; then

            root_terms=$(grep -i -o -E '\b\w{5,}\b' "$MB_DIR/1-projectbrief.md" | sort | uniq)

            

            print_message "$YELLOW" "üìä Root term propagation across Memory Bank:"

            for term in $root_terms; do

              count=$(grep -i -r "$term" --include="*.md" $MB_DIR | wc -l)

              if [ $count -gt 5 ]; then

                echo "$term: mentioned $count times"

              fi

            done

          fi

          

          # Update drift monitor

          if [ -f "$MB_DIR/drift-monitor.md" ]; then

            sed -i "s/Last Structure Validation: .*/Last Structure Validation: $(date +%Y-%m-%d)/" "$MB_DIR/drift-monitor.md"

            print_message "$GREEN" "‚úÖ Updated drift monitor with current date"

          fi

          

          print_message "$GREEN" "‚úÖ Memory Bank audit complete."

        }

        

        # Create a new lineage entry

        function mb_lineage() {

          if ! check_mb_exists; then

            return 1

          fi

          

          # Get the title for the lineage entry

          echo -n "Enter title for the cognitive shift: "

          read title

          

          # Create sanitized filename

          filename=$(echo "$title" | tr '[:upper:]' '[:lower:]' | tr ' ' '-')

          date=$(date +%Y%m%d)

          filepath="$LINEAGE_DIR/${date}-${filename}.md"

          

          print_message "$BLUE" "üìù Creating new lineage entry: $filepath"

          

          # Create file with template

          cat > "$filepath" << EOL

        # Lineage Entry: $title

        

        > **[Structural Role Reminder]**: This file captures a significant cognitive shift in project understanding.

        

        ## Context

        

        **Date**: $(date +%Y-%m-%d)

        **Memory Bank Version**: $(find $MB_DIR -name "*.md" | wc -l) files

        **Current Project Phase**: [Development Phase]

        

        ## Prior Understanding

        

        [Description of how the project was understood before this insight]

        

        ## Cognitive Shift

        

        ### Key Insight

        

        [The core realization or understanding that changed]

        

        ### Catalyzing Factors

        

        - [What triggered this shift in understanding]

        - [Specific events, observations, or analyses]

        - [External or internal factors that prompted reconsideration]

        

        ## Structural Impact

        

        ### Memory Bank Changes

        

        | File | Changes | Justification |

        |------|---------|---------------|

        | [File Path] | [Description of Changes] | [Why the change was necessary] |

        

        ## Root Connection

        

        [How this cognitive shift reinforces or refines connection to the project's root purpose]

        

        ## Practical Implications

        

        ### Development Impact

        

        [How this shift affects the development approach]

        

        ### Next Steps

        

        [Immediate actions prompted by this cognitive shift]

        EOL

          

          print_message "$GREEN" "‚úÖ Lineage entry created: $filepath"

          print_message "$YELLOW" "‚ö†Ô∏è  Don't forget to fill in the template with actual content"

        }

        

        # Create a new epoch snapshot

        function mb_epoch() {

          if ! check_mb_exists; then

            return 1

          fi

          

          # Get the name for the epoch

          echo -n "Enter name for this Memory Bank epoch: "

          read epoch_name

          

          # Create sanitized directory name

          dirname=$(echo "$epoch_name" | tr '[:upper:]' '[:lower:]' | tr ' ' '-')

          count=$(find $EPOCHS_DIR -maxdepth 1 -type d | wc -l)

          padded_count=$(printf "%02d" $count)

          epoch_dir="$EPOCHS_DIR/epoch-${padded_count}-${dirname}"

          

          print_message "$BLUE" "üìù Creating new epoch snapshot: $epoch_dir"

          

          # Create directory and copy files

          mkdir -p "$epoch_dir"

          

          # Copy required files

          for file in "${REQUIRED_FILES[@]}"; do

            if [ -f "$MB_DIR/$file" ]; then

              cp "$MB_DIR/$file" "$epoch_dir/"

            fi

          done

          

          # Create summary file

          cat > "$epoch_dir/epoch-summary.md" << EOL

        # Epoch ${padded_count}: $epoch_name

        

        **Date**: $(date +%Y-%m-%d)

        **Files**: $(find $epoch_dir -name "*.md" | wc -l)

        

        ## Summary

        

        [Summary of this Memory Bank epoch]

        

        ## Key Characteristics

        

        - [Key characteristic 1]

        - [Key characteristic 2]

        - [Key characteristic 3]

        

        ## Structural Evolution

        

        [How the Memory Bank structure has evolved to this point]

        

        ## Next Evolution Target

        

        [What aspects of the Memory Bank should evolve next]

        EOL

          

          print_message "$GREEN" "‚úÖ Epoch snapshot created: $epoch_dir"

          print_message "$YELLOW" "‚ö†Ô∏è  Don't forget to fill in the epoch summary"

        }

        

        # Display help

        function show_help() {

          echo -e "${BLUE}Memory Bank Tools${NC} - CLI utilities for Memory Bank maintenance"

          echo ""

          echo "Usage: $0 [command]"

          echo ""

          echo "Commands:"

          echo "  mb_init        Initialize a new Memory Bank structure"

          echo "  mb_validate    Validate existing Memory Bank structure"

          echo "  mb_compress    Analyze Memory Bank for compression opportunities"

          echo "  mb_audit       Perform comprehensive Memory Bank audit"

          echo "  mb_lineage     Create a new lineage entry documenting a cognitive shift"

          echo "  mb_epoch       Create a new epoch snapshot of the current Memory Bank"

          echo "  help           Show this help message"

          echo ""

        }

        

        # Main command router

        case "$1" in

          "mb_init")

            mb_init

            ;;

          "mb_validate")

            mb_validate

            ;;

          "mb_compress")

            mb_compress

            ;;

          "mb_audit")

            mb_audit

            ;;

          "mb_lineage")

            mb_lineage

            ;;

          "mb_epoch")

            mb_epoch

            ;;

          "help" | *)

            show_help

            ;;

        esac

    ```



    ---



    #### `07-guides\00-README.md`



    ```markdown

        # Memory Bank Templates

        

        > **[Structural Role Reminder]**: This file provides an overview of the Memory Bank system, serving as the entry point and navigation guide for all templates.

        

        ## Core Generative Principle

        

        Memory Bank is a system of persistent abstraction and simplification toward optimal structure. The core principle is an inexorable gravitational pull of all complexity toward foundational clarity through iterative recentering and compression:

        

        - Each structure, detail, or insight is interrogated for its irreducible essence

        - All elements are recursively re-anchored to project purpose

        - The dynamic is a pulsing metabolic flow: accumulation triggers pressure, pressure demands pruning, pruning yields lucidity

        - Structure and meaning converge; entropy cannot persist

        - Every addition must pay its way by sharpening the axis of meaning

        

        > **Memory Bank = Progressive, self-reinforcing abstraction-layering rooted in project purpose.**

        

        ## Directory Structure

        

        ```

        memory-bank/

        ├── 0-distilledContext.md         # Ultra-compressed project essence (2-3 bullets)

        ├── 1-projectbrief.md             # Root purpose: Why the project must exist

        ├── 2-productContext.md           # External reality: users, needs, outcomes

        ├── 3-systemPatterns.md           # Systems: component hierarchy, flows, design

        ├── 4-techContext.md              # Stack-level technical constraints

        ├── 5-structureMap.md             # Actual and target filestructure mapping

        ├── 6-activeContext.md            # In-progress actions and simplifications

        ├── 7-progress.md                 # Assimilation milestones, simplifications

        ├── 8-tasks.md                    # Active structure-guided tasks

        ├── drift-monitor.md              # Structural integrity monitoring

        ├── simplification-candidates.md  # High-impact simplification tracking

        └── lineage/                      # Cognitive evolution snapshots

        ```

        

        ## Templates Overview

        

        ### Essential Files (0-8)

        

        | File | Purpose |

        |------|---------|

        | [`0-distilledContext-template.md`](0-distilledContext-template.md) | Ultra-compressed project essence in 2-3 bullets |

        | [`1-projectbrief-template.md`](1-projectbrief-template.md) | Root purpose definition and critical constraints |

        | [`2-productContext-template.md`](2-productContext-template.md) | User needs, problems solved, and external context |

        | [`3-systemPatterns-template.md`](3-systemPatterns-template.md) | System architecture, component hierarchy, and data flows |

        | [`4-techContext-template.md`](4-techContext-template.md) | Technology stack, constraints, and integration points |

        | [`5-structureMap-template.md`](5-structureMap-template.md) | Current and target file structure with migration path |

        | [`6-activeContext-template.md`](6-activeContext-template.md) | Current focus, bottlenecks, and in-progress simplifications |

        | [`7-progress-template.md`](7-progress-template.md) | Milestones, metrics, and simplifications achieved |

        | [`8-tasks-template.md`](8-tasks-template.md) | Structure-anchored tasks supporting project mission |

        

        ### Core Guides

        

        | File | Purpose |

        |------|---------|

        | [`memory-bank-core-template.md`](memory-bank-core-template.md) | Core system principles and structure |

        | [`memory-bank-implementation-guide.md`](memory-bank-implementation-guide.md) | Practical setup and usage guide |

        | [`memory-bank-learning-path.md`](memory-bank-learning-path.md) | Graduated approach to learning the system |

        | [`memory-bank-enhancement-module.md`](memory-bank-enhancement-module.md) | Advanced enhancement modules |

        | [`memory-bank-web-project-template.md`](memory-bank-web-project-template.md) | Web project-specific adaptation |

        

        ### Auxiliary Templates

        

        | File | Purpose |

        |------|---------|

        | [`simplification-candidates-template.md`](simplification-candidates-template.md) | High-impact simplification identification |

        | [`lineage-template.md`](lineage-template.md) | Cognitive evolution tracking |

        | [`drift-monitor-template.md`](drift-monitor-template.md) | Structural integrity monitoring |

        | [`framework-specific-adaptations.md`](framework-specific-adaptations.md) | Framework-specific patterns and practices |

        

        ### Automation Tools

        

        | File | Purpose |

        |------|---------|

        | [`mb-tools.sh`](mb-tools.sh) | CLI utilities for Memory Bank maintenance |

        

        ## Absolute Requirements

        

        | Principle | How To Adapt |

        | :-------- | :----------- |

        | File-Structure-First | Always first generate/validate a cognitive map *as a filestructure*, dynamically, based on what exists. |

        | Root-First Thinking | Every insight must relate *upward*, toward the project's irreducible mission. |

        | Persistent Simplification | Every action must aim to **reduce** net complexity (unless a rare, justified exception). |

        | Value Extraction Bias | Prefer "what maximizes actionable, durable insight" instead of unfolding more detail. |

        | Guardrails | Never mutate structure blindly — every structural change must be **explicitly justified** in the memory. |

        | Outward Mapping | Think *outward* (root → abstraction layers → concrete implementation), **never inward folding** (which causes sprawl). |

        

        ## Expansion Policy

        

        Additional templates or files may only be introduced through explicit complexity reduction, not accumulation, and must be justified and tracked through `5-structureMap.md` and/or `6-activeContext.md` per system law.

        

        ## Implementation Steps

        

        1. Start by reviewing the [`memory-bank-core-template.md`](memory-bank-core-template.md) to understand the core principles

        2. Follow the implementation instructions in [`memory-bank-implementation-guide.md`](memory-bank-implementation-guide.md)

        3. Copy the numbered file templates (0-8) into your project's `memory-bank/` directory

        4. Fill in the templates with your project-specific information, starting with `0-distilledContext.md` and working downward

        5. Add auxiliary templates as needed, based on project complexity and team needs

        

        ## Final Directive

        

        > "If any action does not reinforce, clarify, or consolidate the abstraction-rooted structure — it must not be taken."

        

        Remember that Memory Bank is not what you know about the project; it is what the project knows about itself. Structure is not a container for knowledge; structure is the memory of intent.

    ```



    ---



    #### `07-guides\01-quick-start-guide.md`



    ```markdown

        # Memory Bank Quick Start Guide

        

        > **[Structural Role Reminder]**: This document provides simple, practical instructions to get started with Memory Bank in minutes.

        

        ## 5-Minute Setup

        

        Get a Memory Bank running in your project with minimal effort:

        

        1. **Copy the setup script** below to your project root

        2. **Run the script** to create the basic Memory Bank structure

        3. **Fill in the root files** (0-distilledContext.md and 1-projectbrief.md)

        4. **Start using Memory Bank** to organize your project thinking

        

        ```bash

        #!/bin/bash

        # mb-quick-setup.sh - Memory Bank Quick Setup Script

        

        # Create main Memory Bank directory

        mkdir -p memory-bank

        

        # Create essential core files with starter templates

        cat > memory-bank/0-distilledContext.md << 'EOL'

        # Project Distilled Context

        

        > This file contains the absolute essence of the project in 2-3 bullets.

        

        - 

        - 

        - 

        

        EOL

        

        cat > memory-bank/1-projectbrief.md << 'EOL'

        # Project Brief

        

        > This file defines why this project must exist.

        

        ## Core Mission

        

        [Define the irreducible purpose of this project in 1-2 sentences]

        

        ## Value Proposition

        

        [Describe the unique value this project provides]

        

        ## Critical Constraints

        

        [List 2-3 non-negotiable constraints that shape the project]

        

        EOL

        

        cat > memory-bank/2-productContext.md << 'EOL'

        # Product Context

        

        > This file maps the external reality: users, needs, and outcomes.

        

        ## User Archetypes

        

        [Identify core user types]

        

        ## Problems Solved

        

        [List the key problems this project solves]

        

        ## Operating Environment

        

        [Describe where and how this product will be used]

        

        EOL

        

        cat > memory-bank/README.md << 'EOL'

        # Project Memory Bank

        

        This directory contains the cognitive architecture for this project.

        

        ## Core Files

        

        - **0-distilledContext.md**: Ultra-compressed project essence

        - **1-projectbrief.md**: Root purpose definition

        - **2-productContext.md**: User needs and external context

        

        ## Memory Bank Principles

        

        - **File-Structure-First**: Cognitive architecture expressed through file structure

        - **Root-First Thinking**: Every insight relates upward to the project's mission

        - **Persistent Simplification**: Actions aim to reduce net complexity

        - **Value Extraction Bias**: Focus on actionable insight over detail

        - **Outward Mapping**: Think from root → abstraction layers → implementation

        

        EOL

        

        echo "Memory Bank core files created in ./memory-bank/"

        echo "Next step: Define your project's essence in 0-distilledContext.md"

        ```

        

        ## Easiest Setup Method

        

        ### For AI Coding Assistant Users

        

        1. **Copy the system instruction** from `07-guides/13-memory-bank-system-instruction.md`

        2. **Paste it** into your AI assistant's system instruction settings (Cline, Cursor AI, etc.)

        3. **Ask the AI assistant** to set up a Memory Bank for your project

        

        Example prompt: "Set up a Memory Bank for my React project and help me define the core files."

        

        ### For Manual Setup

        

        1. **Copy only what you need**:

           - For minimal setup: Just create `0-distilledContext.md` and `1-projectbrief.md`

           - For standard setup: Use the quick setup script above

           - For full setup: Follow the implementation guide

        

        2. **Start with the core questions**:

           - What is the absolute essence of this project? (→ 0-distilledContext.md)

           - Why must this project exist? (→ 1-projectbrief.md)

           - Who is this for and what problems does it solve? (→ 2-productContext.md)

        

        ## Common Use Cases

        

        ### For Solo Developers

        

        **Lightweight Approach**: Just create and maintain:

        - `0-distilledContext.md`

        - `1-projectbrief.md`

        - `6-activeContext.md` (for daily work notes)

        - `8-tasks.md` (for structured to-dos)

        

        ### For Teams

        

        **Collaborative Approach**:

        1. Create the full Memory Bank structure

        2. Add Memory Bank review to your team workflow

        3. Start each meeting by revisiting `0-distilledContext.md`

        4. Maintain `6-activeContext.md` for shared context

        

        ### For Large Projects

        

        **Extended Approach**:

        1. Implement the full directory structure

        2. Use the abstraction layer system to manage complexity

        3. Set up CI/CD integration for Memory Bank validation

        4. Create custom automation scripts

        

        ## Visual Workflow

        

        ```mermaid

        flowchart TD

            Start([Start Project]) --> Setup[Set Up Memory Bank]

            Setup --> Define0[Define Project Essence\n0-distilledContext.md]

            Define0 --> Define1[Define Project Purpose\n1-projectbrief.md]

            Define1 --> Daily[Daily Work]

            

            Daily --> ReadEssence[Read Project Essence]

            ReadEssence --> UpdateContext[Update Active Context\n6-activeContext.md]

            UpdateContext --> WorkOnTasks[Work on Tasks\n8-tasks.md]

            WorkOnTasks --> ExtractPatterns[Extract Patterns\n3-systemPatterns.md]

            ExtractPatterns --> Daily

            

            subgraph "Weekly Activities"

            Weekly([Weekly Review]) --> CheckProgress[Update Progress\n7-progress.md]

            CheckProgress --> Simplify[Identify Simplifications]

            Simplify --> Weekly

            end

        ```

        

        ## Minimal Memory Bank Cheatsheet

        

        | File | Purpose | Key Questions |

        |------|---------|---------------|

        | `0-distilledContext.md` | Essence | What is the absolute core of this project in 2-3 bullets? |

        | `1-projectbrief.md` | Purpose | Why must this project exist? What is its value? |

        | `2-productContext.md` | Users & Needs | Who is this for and what problems does it solve? |

        | `6-activeContext.md` | Current Work | What am I focusing on right now? What's blocking me? |

        | `8-tasks.md` | Actionable Items | What specific tasks will move the project forward? |

        

        Remember: Memory Bank is not rigid documentation—it's a living system that helps you maintain clarity about your project's mission and structure.

        

        ---

        

        If you need more detailed guidance, refer to `11-memory-bank-implementation-guide.md`.

    ```



    ---



    #### `07-guides\02-visual-guide.md`



    ```markdown

        # Memory Bank Visual Guide

        

        > **[Structural Role Reminder]**: This document provides a visual overview of the Memory Bank system for quick understanding.

        

        ## Memory Bank at a Glance

        

        ```mermaid

        graph TD

            classDef root fill:#f9f,stroke:#333,stroke-width:2px

            classDef context fill:#bbf,stroke:#333,stroke-width:1px

            classDef structure fill:#ddf,stroke:#333,stroke-width:1px

            classDef process fill:#eff,stroke:#333,stroke-width:1px

            classDef evolution fill:#ffd,stroke:#333,stroke-width:1px

            classDef tools fill:#dfd,stroke:#333,stroke-width:1px

            classDef guides fill:#fdd,stroke:#333,stroke-width:1px

            

            Root[01-abstraction-root] --> Context[02-context]

            Root --> Structure[03-structure-design]

            Root --> Process[04-process-tracking]

            Structure --> Evolution[05-evolution]

            Process --> Evolution

            Evolution --> Tools[06-tools]

            Tools --> Guides[07-guides]

            

            class Root root

            class Context context

            class Structure structure

            class Process process

            class Evolution evolution

            class Tools tools

            class Guides guides

        ```

        

        ## Core Files Relationship

        

        ```mermaid

        graph TD

            classDef essential fill:#f9f,stroke:#333,stroke-width:2px

            classDef important fill:#bbf,stroke:#333,stroke-width:1px

            classDef supporting fill:#ddf,stroke:#333,stroke-width:1px

            

            File0[0-distilledContext.md] --> File1[1-projectbrief.md]

            File1 --> File2[2-productContext.md]

            File1 --> File3[3-systemPatterns.md]

            File2 --> File4[4-techContext.md]

            File3 --> File5[5-structureMap.md]

            File5 --> File6[6-activeContext.md]

            File6 --> File7[7-progress.md]

            File6 --> File8[8-tasks.md]

            

            class File0,File1 essential

            class File2,File3,File6,File8 important

            class File4,File5,File7 supporting

        ```

        

        ## Simplified Implementation Levels

        

        Choose your implementation level based on your needs:

        

        ### Level 1: Minimal (5 minutes)

        

        ```

        memory-bank/

        ├── 0-distilledContext.md  # Project essence in bullets

        ├── 1-projectbrief.md      # Why the project exists

        └── README.md              # Quick reference

        ```

        

        ### Level 2: Standard (15 minutes)

        

        ```

        memory-bank/

        ├── 0-distilledContext.md  # Project essence in bullets

        ├── 1-projectbrief.md      # Why the project exists

        ├── 2-productContext.md    # Users and problems solved

        ├── 6-activeContext.md     # Current focus

        ├── 8-tasks.md             # Structure-anchored tasks

        └── README.md              # Quick reference

        ```

        

        ### Level 3: Complete (as needed)

        

        ```

        memory-bank/

        ├── 01-abstraction-root/   # Root essence

        ├── 02-context/            # External reality

        ├── 03-structure-design/   # Architecture & patterns

        ├── 04-process-tracking/   # Active work

        ├── 05-evolution/          # Cognitive evolution

        ├── 06-tools/              # Automation

        └── 07-guides/             # Implementation guides

        ```

        

        ## Quick Decision Trees

        

        ### Which Files Do I Need?

        

        ```mermaid

        flowchart TD

            Start([Which files do I need?]) --> Solo{Solo dev?}

            Solo -->|Yes| Time{How much time?}

            Solo -->|No| Team{Team size?}

            

            Time -->|< 5 min| Min[0-1 only]

            Time -->|< 15 min| Std[0-1-2-6-8]

            Time -->|> 15 min| Full[Full structure]

            

            Team -->|Small| Std

            Team -->|Medium+| Full

            

            Min --> Q1{Project clarity?}

            Std --> Q1

            

            Q1 -->|Clear| Done([You're set!])

            Q1 -->|Unclear| Add3[Add 3-systemPatterns.md]

            Add3 --> Done

        ```

        

        ### When to Update Each File

        

        ```mermaid

        flowchart TD

            Start([When to update?]) --> Daily{Daily}

            Start --> Weekly{Weekly}

            Start --> Milestone{Milestone}

            Start --> Change{Major Change}

            

            Daily --> File6[6-activeContext.md]

            Daily --> File8[8-tasks.md]

            

            Weekly --> File7[7-progress.md]

            Weekly --> FileS[simplification-candidates.md]

            

            Milestone --> File3[3-systemPatterns.md]

            Milestone --> File5[5-structureMap.md]

            

            Change --> File0[0-distilledContext.md]

            Change --> File1[1-projectbrief.md]

            Change --> File2[2-productContext.md]

            Change --> File4[4-techContext.md]

        ```

        

        ## Common Questions & Answers

        

        **Q: What's the absolute minimum I need?**  

        A: Just create `0-distilledContext.md` and `1-projectbrief.md`. That's it!

        

        **Q: How do I get started quickly?**  

        A: Run the quick setup script: `./06-tools/1-mb-quick-setup.sh`

        

        **Q: How do I use this with my AI assistant?**  

        A: Copy the system instruction from `07-guides/13-memory-bank-system-instruction.md` into your AI's system settings.

        

        **Q: Do I need all the directories?**  

        A: No. Start with the minimal approach and add more structure only when you need it.

        

        **Q: What workflow should I follow?**  

        A: Start each day by reading `0-distilledContext.md`, update `6-activeContext.md`, work on `8-tasks.md`.

        

        ## Daily Routine Cheat Sheet

        

        1. **Start day**: Read your `0-distilledContext.md` (30 seconds)

        2. **Begin work**: Update `6-activeContext.md` with current focus (1 minute)

        3. **During work**: Reference and update `8-tasks.md` (as needed)

        4. **End day**: Update `6-activeContext.md` with progress and blockers (2 minutes)

        5. **End week**: Update `7-progress.md` and identify simplifications (5 minutes)

        

        Remember: The Memory Bank is not what you know about the project; it is what the project knows about itself.

    ```



    ---



    #### `07-guides\03-processing-order-guide.md`



    ```markdown

        # Memory Bank Processing Order Guide

        

        > **[Structural Role Reminder]**: This document clarifies the exact sequential order for processing Memory Bank files.

        

        ## Understanding the Numbering System

        

        The Memory Bank uses a dual-numbering system that balances:

        1. **Abstraction hierarchy** (directory level: 01-07)

        2. **Functional sequence** (file level: 00-14)

        

        While this creates a powerful self-documenting structure, it can appear non-linear at first glance because:

        - The original Memory Bank file numbers (0-8) are preserved across directories

        - Each directory only contains the files relevant to its abstraction layer

        

        ## Exact Processing Order

        

        Here is the precise order to process Memory Bank files:

        

        1. **Start Here**:

           - `00-README.md` - Overview of the entire system

        

        2. **Core Files (Sequential Implementation)**:

           - `01-abstraction-root/00-distilledContext-template.md` - Project essence

           - `01-abstraction-root/01-projectbrief-template.md` - Purpose definition

           - `02-context/02-productContext-template.md` - Users and needs

           - `03-structure-design/03-systemPatterns-template.md` - Architecture patterns

           - `02-context/04-techContext-template.md` - Technical constraints

           - `03-structure-design/05-structureMap-template.md` - Structure mapping

           - `04-process-tracking/06-activeContext-template.md` - Current focus

           - `04-process-tracking/07-progress-template.md` - Progress tracking

           - `04-process-tracking/08-tasks-template.md` - Structure-anchored tasks

        

        3. **Enhancement Files (As Needed)**:

           - `02-context/09-framework-specific-adaptations-template.md`

           - `03-structure-design/09-abstract-patterns-glossary-template.md`

           - `03-structure-design/10-simplification-candidates-template.md`

           - `04-process-tracking/09-drift-monitor-template.md`

           - `05-evolution/09-lineage-template.md`

        

        4. **Tools**:

           - `06-tools/01-mb-quick-setup.sh` - Quick setup script

           - `06-tools/02-mb-assistant.sh` - Interactive assistant

           - `06-tools/09-mb-tools.sh` - Advanced utilities

        

        5. **Guides**:

           - `07-guides/00-README.md` - Guides overview

           - `07-guides/01-quick-start-guide.md` - Fast onboarding

           - `07-guides/02-visual-guide.md` - Visual diagrams

           - `07-guides/09-memory-bank-core-template.md` - Core principles

           - `07-guides/10-memory-bank-enhancement-module.md` - Enhancements

           - `07-guides/11-memory-bank-implementation-guide.md` - Implementation

           - `07-guides/12-memory-bank-learning-path.md` - Learning path

           - `07-guides/13-memory-bank-system-instruction.md` - AI instructions

           - `07-guides/14-memory-bank-web-project-template.md` - Web adaptation

        

        ## Visual Implementation Flow

        

        ```mermaid

        flowchart TD

            Start[00-README.md] --> Root1[00-distilledContext]

            Root1 --> Root2[01-projectbrief]

            Root2 --> Context1[02-productContext]

            Context1 --> Structure1[03-systemPatterns]

            Structure1 --> Context2[04-techContext]

            Context2 --> Structure2[05-structureMap]

            Structure2 --> Process1[06-activeContext]

            Process1 --> Process2[07-progress]

            Process2 --> Process3[08-tasks]

            

            subgraph "Enhancement Files (As Needed)"

            Process3 -.-> Enhancements[09+ Enhancement Files]

            end

            

            subgraph "Tools & Guides (Reference)"

            Process3 -.-> Tools[06-tools/*]

            Process3 -.-> Guides[07-guides/*]

            end

        ```

        

        ## Simplified Daily Workflow

        

        Once you've set up your Memory Bank, the daily workflow becomes:

        

        1. Read `01-abstraction-root/00-distilledContext-template.md` (30 seconds)

        2. Update `04-process-tracking/06-activeContext-template.md` (1 minute)

        3. Work on `04-process-tracking/08-tasks-template.md` items

        4. Update progress in `04-process-tracking/07-progress-template.md` (weekly)

        

        ## Why This Structure Works

        

        The Memory Bank structure balances two needs:

        

        1. **Functional Sequence**: The core files (00-08) maintain their original numbering across directories because they form a logical processing sequence.

        

        2. **Abstraction Hierarchy**: The directories (01-07) organize files by abstraction level, grouping related concepts together.

        

        This design allows you to implement a Memory Bank by following the 00-08 sequence, while also providing an organizational structure that reinforces the conceptual relationships between files.

    ```



    ---



    #### `07-guides\09-memory-bank-core-template.md`



    ```markdown

        # Memory Bank System Core Template

        

        > **[Structural Role Reminder]**: This file defines the fundamental principles and structure of the Memory Bank system, serving as the anchor for all other templates and implementations.

        

        ## Core Generative Principle

        

        The Memory Bank operates by an inexorable gravitational pull of all complexity toward foundational clarity through iterative recentering and compression:

        

        - Each structure, detail, or insight is not preserved for itself, but interrogated for its irreducible essence

        - All elements are recursively re-anchored and distilled until the system becomes its own map, memory, and purpose

        - The dynamic is a pulsing metabolic flow:

          - Accumulation triggers pressure

          - Pressure demands pruning

          - Pruning yields emergent lucidity

        - The whole is reconstituted from its root with each movement

        - 'Structure' and 'meaning' converge

        - Entropy cannot persist

        - Every addition must pay its way by sharpening the axis of meaning

        

        > **Memory Bank = Progressive, self-reinforcing abstraction-layering rooted in project purpose.**

        

        ## Absolute Requirements

        

        | Principle | Implementation |

        | :-------- | :------------- |

        | File-Structure-First | Always first generate/validate a cognitive map *as a filestructure*, dynamically, based on what exists. |

        | Root-First Thinking | Every insight must relate *upward*, toward the project's irreducible mission. |

        | Persistent Simplification | Every action must aim to **reduce** net complexity (unless a rare, justified exception). |

        | Value Extraction Bias | Prefer "what maximizes actionable, durable insight" instead of unfolding more detail. |

        | Guardrails | Never mutate structure blindly — every structural change must be **explicitly justified** in the memory. |

        | Outward Mapping | Think *outward* (root → abstraction layers → concrete implementation), **never inward folding** (which causes sprawl). |

        

        ## Memory Bank Structure

        

        ```plaintext

        memory-bank/

        ├── 0-distilledContext.md         # Ultra-compressed project core abstraction

        ├── 1-projectbrief.md             # Root purpose: Why the project must exist

        ├── 2-productContext.md           # External reality: users, needs, outcomes

        ├── 3-systemPatterns.md           # Systems: component hierarchy, flows, state design

        ├── 4-techContext.md              # Stack-level technical constraints

        ├── 5-structureMap.md             # Actual and target filestructure mapping

        ├── 6-activeContext.md            # In-progress actions, bottlenecks, and simplifications

        ├── 7-progress.md                 # Assimilation milestones, simplifications achieved

        ├── 8-tasks.md                    # Active structure-guided tasks

        ├── drift-monitor.md              # Structural integrity monitoring

        ├── simplification-candidates.md  # High-impact simplification tracking

        └── lineage/                      # Cognitive evolution snapshots

        ```

        

        ### Core Required Files

        

        | File | Role in Cognitive Architecture |

        | :--- | :----------------------------- |

        | `0-distilledContext.md` | **Immediate Root Re-anchoring**: Crystallized project essence & goals in 2-3 bullets. |

        | `1-projectbrief.md` | **The Root Abstraction**: Project's irreducible mission, value prop, critical constraints. The source. |

        | `2-productContext.md` | **Why - Value Context**: Problems solved, users, operational context. Justifies features against the Root. |

        | `3-systemPatterns.md` | **How - Architectural Form**: Target structure, component patterns, state/data flow. Blueprint for order. |

        | `4-techContext.md` | **With What - Technical Constraints**: Stack, libraries, performance/accessibility boundaries. |

        | `5-structureMap.md` | **Current vs. Target Structure**: File structure mapping with migration path and justifications. |

        | `6-activeContext.md` | **Current Focus**: Active focus, consolidation analysis, decisions, bottlenecks. |

        | `7-progress.md` | **Status & Entropy Check**: Milestones, metrics, tech debt ledger, structural integrity validation. |

        | `8-tasks.md` | **Actionable Interventions**: Concrete, structure-anchored tasks. Must trace lineage to root goal. |

        

        ### Optional Cognitive Enhancements

        

        | File | Role in Cognitive Architecture |

        | :--- | :----------------------------- |

        | `drift-monitor.md` | **Structural Integrity Monitor**: Identifies and tracks structural drift, enforces role adherence. |

        | `simplification-candidates.md` | **Complexity Reduction Tracking**: Formalizes identification and implementation of high-impact simplifications. |

        | `/lineage` | **Cognitive Evolution Tracking**: Chronicles significant shifts in project understanding or structure. |

        

        ## Cognitive Abstraction Layers

        

        ```mermaid

        flowchart TD

            Root[0-1: Root Abstraction] --> Value[2: Value Context]

            Root --> Architecture[3: Architectural Form]

            Root --> Technical[4: Technical Constraints]

            Value & Architecture & Technical --> Structure[5: Structure Mapping]

            Structure --> Active[6: Active Context]

            Active --> Progress[7: Progress Tracking]

            Progress --> Tasks[8: Actionable Tasks]

            

            class Root primary

            class Value,Architecture,Technical secondary

            class Structure,Active tertiary

            class Progress,Tasks quaternary

            

            classDef primary fill:#f9f,stroke:#333,stroke-width:2px

            classDef secondary fill:#bbf,stroke:#333,stroke-width:1px

            classDef tertiary fill:#ddf,stroke:#333,stroke-width:1px

            classDef quaternary fill:#eff,stroke:#333,stroke-width:1px

        ```

        

        ## Structural Guardrails

        

        ### System Laws

        

        | Law | Enforcement |

        | :-- | :---------- |

        | 📜 **Memory Bank Is Living** | It self-prunes. It strengthens by collapsing excess back into the root. |

        | 📜 **Structure Is Intelligence** | Assimilation power arises not from detail captured, but from **structural clarity imposed**. |

        | 📜 **Value ≠ Volume** | Real value is extracted via deliberate constraint – **"enough, but no more."** |

        | 📜 **Root Fidelity is Absolute** | Every element must trace its lineage back to the irreducible purpose (`1`) or be dissolved. |

        

        ### Operational Guardrails

        

        | Situation | Mandate |

        | :-------- | :------ |

        | Find Complex Detail | Compress into higher abstraction, no passive notes. |

        | Need New File | Only post-compression attempt + explicit justification. |

        | Detect Drift | Refactor/re-anchor immediately. |

        | Discover Random Insight | Discard unless anchored outward to root. |

        | Unsure where an insight fits | **Halt.** Re-evaluate structure (`3`, `5/6`) before proceeding. Do not force fit. |

        

        ## Responsibility Matrix

        

        | File | When to Update | What to Include | What to Exclude |

        | :--- | :------------- | :-------------- | :-------------- |

        | `0-distilledContext.md` | When project mission changes | Core purpose, value, constraint in 2-3 bullets | Implementation details, process notes |

        | `1-projectbrief.md` | When fundamental purpose/constraints change | Mission, value proposition, key constraints | Low-level details, implementation specifics |

        | `2-productContext.md` | When user needs or market context evolves | User profiles, problems solved, external factors | Technical solutions, implementation approaches |

        | `3-systemPatterns.md` | When architecture patterns evolve | Component hierarchy, data flows, patterns | Instance details, temporary solutions |

        | `4-techContext.md` | When stack/constraints change | Technologies, constraints, integration points | Business logic, pattern rationales |

        | `5-structureMap.md` | When target or current structure changes | Current vs. target structure, migration path | Implementation details, low-level tasks |

        | `6-activeContext.md` | Daily or with each work session | Current focus, bottlenecks, in-progress simplifications | Historical notes, completed work (→ 7) |

        | `7-progress.md` | After significant milestones | Status, milestones, simplifications achieved | Current work status (→ 6), detailed plans (→ 8) |

        | `8-tasks.md` | When planning action | Structure-anchored tasks, clear acceptance criteria | Vague goals, non-traced tasks, historical notes |

        | `drift-monitor.md` | After Memory Bank updates | File purpose vs. usage, drift patterns, corrections | Implementation details, extended rationales |

        | `simplification-candidates.md` | With each development cycle | Simplification opportunities, scoring, implementation tracking | Detailed implementation plans (→ 8) |

        | `/lineage` | After significant cognitive shifts | Context, prior understanding, shift, structural impact | Current working notes (→ 6), active tasks (→ 8) |

        

        ## Update Workflow

        

        ```mermaid

        flowchart TD

            Start[Start Work Session] --> Read0[Read 0-distilledContext.md]

            Read0 --> Read6[Read 6-activeContext.md]

            Read6 --> WorkOnTask[Work on Task from 8-tasks.md]

            WorkOnTask --> IdentifySimplifications[Identify Simplification Opportunities]

            IdentifySimplifications --> UpdateActiveContext[Update 6-activeContext.md]

            UpdateActiveContext --> TaskComplete{Task Complete?}

            TaskComplete -->|Yes| UpdateProgress[Update 7-progress.md]

            UpdateProgress --> UpdateTasks[Update 8-tasks.md]

            TaskComplete -->|No| End[End Work Session]

            UpdateTasks --> CheckDrift[Check drift-monitor.md]

            CheckDrift --> SimplifyCandidates[Update simplification-candidates.md]

            SimplifyCandidates --> End

        ```

        

        ## Compression Reflex Protocol

        

        Before adding new content to any Memory Bank file:

        

        1. **Merge Check**: Can this be merged with existing content? If yes, merge instead of adding.

        2. **Elevation Check**: Can this be elevated to a pattern in a higher abstraction file? If yes, elevate instead of adding.

        3. **Dissolution Check**: Can this be dissolved as an instance of an existing pattern? If yes, reference pattern instead of adding.

        4. **Root Connection Check**: Does this clearly connect to the project's root purpose? If no, reconsider its value.

        5. **Justification**: If still adding, explicitly justify why this content cannot be compressed further.

        

        ## Validation & Sanction Checklists

        

        **Sanction after every cycle:**

        

        | Check | Pass Condition |

        | :---- | :------------- |

        | Root Purpose Confirmed | `1-projectbrief.md` accurate. |

        | Structure Integrity | Files justified, scoped, non-redundant. |

        | Compression Reflex Applied | No uncompressed additions. |

        | High-Impact Simplification Logged | At least 1 per cycle (`7-progress.md`). |

        | Context Traceability | No orphan entries; all map to `1`. |

        | Lineage Snapshots (if used) | Cognitive epochs captured. |

        

        ✅ Proceed if ALL conditions pass.

        ❌ Otherwise, halt assimilation → repair structure.

        

        ## Final Directive

        

        **Root Fidelity Above All**

        

        > "If any action does not reinforce, clarify, or consolidate the abstraction-rooted structure — it must not be taken."

        

        **Cognitive Command Summary**

        

        > "Assimilate only via compression, traceable structure, and operational clarity.

        > No drift, no redundancy, no passive entropy. Memory Bank *is* the project intelligence."

        

        **Meta-Bias Reminder**

        

        > "Identify single most critical aspect for maximizing overall value, ensuring maximum clarity, utility, and adaptability without diminishing potential yield."

        

        This meta-bias must shape every **scan**, **map**, **act**, **document**.

    ```



    ---



    #### `07-guides\10-memory-bank-enhancement-module.md`



    ```markdown

        # Memory Bank Enhancement Module

        

        > **[Structural Role Reminder]**: This module provides essential enhancements to the Memory Bank system based on advanced patterns from real-world implementations. It should be integrated into your existing Memory Bank structure.

        

        ## Project-Specific Identity Anchoring

        

        Every Memory Bank implementation should establish a clear **project codename** that serves as a cognitive anchor throughout the system. This prevents generic drift and strengthens root fidelity.

        

        ### Implementation:

        

        1. Choose a concise project codename (e.g., "RLWeb" for "Ringerike Landskap Website")

        2. Add this to all Memory Bank file headers:

        

        ```markdown

        # [File Title] - [ProjectName]

        

        > **[Structural Role Reminder]**: This file [role description] for the **[ProjectName]** project.

        ```

        

        3. Reference the codename whenever discussing specific implementations (e.g., "The **RLWeb** component hierarchy follows...")

        

        ### Benefit:

        

        This identity anchoring prevents cross-project contamination and strengthens the recursive connection to project-specific purpose, avoiding generic patterns that dilute clarity.

        

        ## Structural Responsibility Self-Awareness

        

        Enhance each Memory Bank file with explicit self-awareness of its structural role in the cognitive architecture.

        

        ### Implementation:

        

        Add this section at the top of each Memory Bank file:

        

        ```markdown

        > **[Structural Role Declaration]**: 

        > - This file anchors the [ROOT PURPOSE / VALUE CONTEXT / SYSTEM PATTERNS / etc.] abstraction.

        > - It exists because [specific justification tied to project purpose].

        > - It connects to [related files] through [relationship description].

        ```

        

        ### Benefit:

        

        Every file actively defends its reason for existence, enforcing Single Responsibility and making structural drift immediately apparent.

        

        ## Dynamic Compression Reflex

        

        Establish an explicit compression protocol that must be applied before any new information is added to the Memory Bank.

        

        ### Implementation:

        

        Add this section to the beginning of your update workflow:

        

        ```markdown

        ## Compression Check Performed

        

        **[Date]**: Before adding this new information, I attempted to:

        

        1. [ ] Merge it into existing patterns in [file X]

        2. [ ] Elevate it to a higher abstraction in [file Y]

        3. [ ] Dissolve redundant elements from [file Z]

        

        Result: [Compressed successfully / Recording separately because...]

        ```

        

        ### Benefit:

        

        Forces active compression-first thinking, preventing passive note-taking and entropy accumulation.

        

        ## Structural Drift Early Warning System

        

        Implement a proactive monitoring mechanism for Memory Bank integrity.

        

        ### Implementation:

        

        Add a file named `drift-monitor.md` with this structure:

        

        ```markdown

        # Drift Monitor

        

        ## Last Structure Validation: [Date]

        

        | File | Original Purpose | Current Usage | Drift Status | Action Needed |

        |------|-----------------|---------------|-------------|---------------|

        | `0-distilledContext.md` | [Purpose] | [Current] | ✅ or ⚠️ | [Action] |

        | `1-projectbrief.md` | [Purpose] | [Current] | ✅ or ⚠️ | [Action] |

        | ... | ... | ... | ... | ... |

        

        ## Drift Correction Log

        

        | Date | File | Issue Detected | Correction Applied |

        |------|------|----------------|-------------------|

        | [Date] | [File] | [Issue] | [Correction] |

        ```

        

        Update this file after every significant Memory Bank update or at least monthly.

        

        ### Benefit:

        

        Provides an early warning system for structural decay, enforcing proactive maintenance of the Memory Bank's cognitive integrity.

        

        ## Memory Bank CLI Tools

        

        Implement command-line tools to automate Memory Bank maintenance and enforce structural discipline.

        

        ### Tooling Framework:

        

        ```bash

        #!/bin/bash

        # mb-tools.sh - Memory Bank maintenance tools

        

        function mb_init() {

          # Initialize Memory Bank structure

          mkdir -p memory-bank/lineage

          # Copy template files...

        }

        

        function mb_validate() {

          # Check structure integrity

          # Verify file presence and format

          # Report any missing references or structural issues

        }

        

        function mb_compress() {

          # Analyze files for compression opportunities

          # Look for duplicate concepts, extract patterns

          # Suggest specific merges and elevations

        }

        

        function mb_audit() {

          # Full structure audit

          # Root connection verification

          # Compression effectiveness assessment

          # Generate drift report

        }

        

        function mb_lineage() {

          # Create new lineage entry

          # Record structural evolution checkpoint

        }

        

        # Usage instructions...

        ```

        

        ### Implementation Steps:

        

        1. Create the script in your project's root directory

        2. Make it executable: `chmod +x mb-tools.sh`

        3. Run initialization: `./mb-tools.sh mb_init`

        4. Schedule regular validation: `./mb-tools.sh mb_validate`

        

        ### Benefit:

        

        Automates structural discipline, making Memory Bank maintenance more consistent and reducing the cognitive overhead of manual checks.

        

        ## Epoch-Based Evolution Tracking

        

        Implement explicit tracking of major cognitive phases in Memory Bank evolution.

        

        ### Implementation:

        

        Create a directory structure:

        

        ```

        memory-bank/

        ├── epochs/

        │   ├── epoch-01-initial-structure/

        │   │   ├── [snapshot of key files]

        │   ├── epoch-02-pattern-recognition/

        │   │   ├── [snapshot of key files]

        │   └── ...

        ```

        

        After major architectural shifts or significant project milestones, create a new epoch directory with snapshots of the current Memory Bank state and a summary document explaining the transition.

        

        ### Benefit:

        

        Provides cognitive rollback points and explicitly tracks the project's conceptual evolution, showing how understanding has matured.

        

        ## High-Impact Simplification Protocol

        

        Formalize the process of identifying and implementing simplifications with maximum impact for minimum intervention.

        

        ### Implementation:

        

        Create a `simplification-candidates.md` file:

        

        ```markdown

        # High-Impact Simplification Candidates

        

        ## Evaluation Criteria:

        1. **Minimal Implementation Effort** (1-10): How easy is this to implement?

        2. **Structural Clarity Gain** (1-10): How much clearer will the structure become?

        3. **Widespread Impact** (1-10): How many areas will benefit?

        4. **Root Reinforcement** (1-10): How strongly does this support the project mission?

        5. **Impact Score** = Clarity × Impact × Root ÷ Effort

        

        ## Current Candidates:

        

        | ID | Simplification | Effort | Clarity | Impact | Root | Score | Status |

        |----|----------------|--------|---------|--------|------|-------|--------|

        | S1 | [Description] | [1-10] | [1-10] | [1-10] | [1-10] | [Score] | [Pending/In Progress/Completed] |

        ```

        

        Make identifying at least one high-impact simplification a requirement for each development cycle.

        

        ### Benefit:

        

        Focuses attention on strategic simplifications rather than tactical fixes, maximizing clarity gain per unit of effort.

        

        ## Integration Guidelines

        

        To integrate these enhancements with your existing Memory Bank:

        

        1. Start with the Project-Specific Identity Anchoring by choosing a codename

        2. Add Structural Role Declarations to the top of each existing file

        3. Implement the Compression Check procedure for your next update

        4. Create the Drift Monitor as a new file

        5. Implement CLI tools as appropriate for your development workflow

        6. Consider creating epoch markers for major transitions

        7. Start tracking high-impact simplifications

        

        These enhancements work together to create a Memory Bank that is not just documented but **alive** - actively compressing complexity, maintaining its own structure, and continuously reinforcing the project's root purpose.

        

        ---

        

        **Remember**: The Memory Bank becomes more powerful as it becomes more self-aware. Structure is not a passive container; it is the active embodiment of the project's intelligence.

    ```



    ---



    #### `07-guides\11-memory-bank-implementation-guide.md`



    ```markdown

        # Memory Bank Implementation & Mastery Guide

        

        > **[Structural Role Reminder]**: This file provides comprehensive instructions for setting up, using, and mastering the Memory Bank system, serving as the canonical guide for practical implementation.

        

        ## Table of Contents

        1. [Introduction](#introduction)

        2. [Core Principles](#core-principles)

        3. [Setup Instructions](#setup-instructions)

        4. [Implementation Workflow](#implementation-workflow)

        5. [Memory Bank Maintenance](#memory-bank-maintenance)

        6. [Learning Progression](#learning-progression)

        7. [Advanced Enhancements](#advanced-enhancements)

        8. [Troubleshooting](#troubleshooting)

        

        ## Introduction

        

        The Memory Bank is not traditional documentation - it's a dynamic, living system for structural intelligence that:

        

        - Compresses complexity toward foundational clarity

        - Ensures all project elements trace to the root purpose

        - Drives continual simplification and entropy reduction

        - Makes structure the primary means of understanding

        - Converts passive knowledge into active intelligence

        

        > **"Memory Bank is not what you know about the project; it is what the project knows about itself."**

        

        ## Core Principles

        

        Before implementation, internalize these fundamental principles:

        

        1. **File-Structure-First**: Cognitive architecture is expressed through file structure first, content second

        2. **Root-First Thinking**: Every insight must trace upward to the project's irreducible mission

        3. **Persistent Simplification**: Always aim to reduce net complexity with every action

        4. **Value Extraction Bias**: Maximize actionable insight rather than detail accumulation

        5. **Structural Guardrails**: Justify every structural change explicitly

        6. **Outward Mapping**: Think from root to abstraction layers to implementation, never inward

        

        ## Setup Instructions

        

        ### Creating the Memory Bank Structure

        

        ```bash

        # From your project root

        mkdir -p memory-bank/lineage

        ```

        

        ### Initializing Core Files

        

        Copy these template files into your project:

        

        1. `0-distilledContext.md` - Ultra-compressed project essence

        2. `1-projectbrief.md` - Root purpose definition

        3. `2-productContext.md` - External reality mapping

        4. `3-systemPatterns.md` - System architecture patterns

        5. `4-techContext.md` - Technical constraints and stack

        6. `5-structureMap.md` - Current vs target structure

        7. `6-activeContext.md` - In-progress work and focus

        8. `7-progress.md` - Milestones and progress tracking

        9. `8-tasks.md` - Structure-anchored tasks

        

        ### Setting Up Optional Enhancements

        

        For more advanced project needs:

        

        10. `drift-monitor.md` - Structural integrity monitoring

        11. `simplification-candidates.md` - High-impact simplification tracking

        

        ### Installing Automation Tools

        

        1. Copy `mb-tools.sh` to your project root

        2. Make it executable: `chmod +x mb-tools.sh`

        3. Initialize the Memory Bank: `./mb-tools.sh mb_init`

        

        ### Integrating with Project Documentation

        

        Add to your project's main README.md:

        

        ```markdown

        ## Project Memory Bank

        

        This project uses a Memory Bank system for cognitive architecture and structural intelligence. 

        See the `/memory-bank` directory for:

        

        - Project mission and context

        - System architecture patterns

        - Active development focus

        - Progress tracking

        - Structure-anchored tasks

        

        **Note**: The Memory Bank is not static documentation but a living system that metabolizes complexity into clarity.

        ```

        

        ## Implementation Workflow

        

        ### Phase 1: Root Definition

        

        1. Define project's irreducible essence in `0-distilledContext.md`

        2. Establish root purpose in `1-projectbrief.md`

        3. Document the value context in `2-productContext.md`

        

        ### Phase 2: System Architecture

        

        4. Define system patterns in `3-systemPatterns.md`

        5. Document technical constraints in `4-techContext.md`

        6. Map current and target structure in `5-structureMap.md`

        

        ### Phase 3: Active Development

        

        7. Initialize active context in `6-activeContext.md`

        8. Set up progress tracking in `7-progress.md`

        9. Create initial tasks in `8-tasks.md`

        

        ### Phase 4: Cognitive Maintenance

        

        10. Set up drift monitoring in `drift-monitor.md`

        11. Begin tracking simplification candidates in `simplification-candidates.md`

        12. Create lineage entries for significant cognitive shifts

        

        ## Memory Bank Maintenance

        

        ### The Standard Update Workflow

        

        ```mermaid

        flowchart TD

            Start[Start Work Session] --> Read0[Read 0-distilledContext.md]

            Read0 --> Read6[Read 6-activeContext.md]

            Read6 --> ReadTasks[Review 8-tasks.md]

            ReadTasks --> WorkOnTask[Work on Task]

            WorkOnTask --> IdentifySimplifications[Identify Simplification Opportunities]

            IdentifySimplifications --> UpdateActiveContext[Update 6-activeContext.md]

            UpdateActiveContext --> TaskComplete{Task Complete?}

            TaskComplete -->|Yes| UpdateProgress[Update 7-progress.md]

            UpdateProgress --> UpdateTasks[Update 8-tasks.md]

            TaskComplete -->|No| End[End Work Session]

            UpdateTasks --> CheckDrift[Check drift-monitor.md]

            CheckDrift --> SimplifyCandidates[Update simplification-candidates.md]

            SimplifyCandidates --> End

        ```

        

        ### File Update Protocol

        

        | File | Update Frequency | Update Trigger | Link to |

        |------|------------------|----------------|---------|

        | `0-distilledContext.md` | Rarely | Project mission changes | `1-projectbrief.md` |

        | `1-projectbrief.md` | Rarely | Fundamental changes | All files |

        | `2-productContext.md` | Occasionally | User needs evolve | `1-projectbrief.md` |

        | `3-systemPatterns.md` | Occasionally | Architecture evolves | `1-projectbrief.md`, `2-productContext.md` |

        | `4-techContext.md` | Occasionally | Stack changes | `1-projectbrief.md`, `3-systemPatterns.md` |

        | `5-structureMap.md` | Occasionally | Structure changes | `3-systemPatterns.md` |

        | `6-activeContext.md` | Daily | Every work session | `1-projectbrief.md`, `5-structureMap.md` |

        | `7-progress.md` | Weekly | Milestones achieved | `6-activeContext.md` |

        | `8-tasks.md` | Weekly | Planning cycles | `1-projectbrief.md`, `6-activeContext.md` |

        | `drift-monitor.md` | Monthly | After Memory Bank updates | All files |

        | `simplification-candidates.md` | Weekly | Development cycles | `6-activeContext.md`, `7-progress.md` |

        | `lineage/` | As needed | Cognitive shifts | All relevant files |

        

        ### The Compression Reflex

        

        Before adding any new content:

        

        1. **Merge Check**: Can this be merged with existing content?

        2. **Elevation Check**: Can this be elevated to a pattern in a higher abstraction file?

        3. **Dissolution Check**: Can this be dissolved as an instance of an existing pattern?

        4. **Root Connection Check**: Does this clearly connect to the project's root purpose?

        5. **Justification**: If still adding, explicitly justify why this content cannot be compressed further.

        

        Document this check in the file you're updating with:

        

        ```markdown

        ## Compression Check Performed

        

        **[Date]**: Before adding this new information, I attempted to:

        

        1. [ ] Merge it into existing patterns in [file X]

        2. [ ] Elevate it to a higher abstraction in [file Y]

        3. [ ] Dissolve redundant elements from [file Z]

        

        Result: [Compressed successfully / Recording separately because...]

        ```

        

        ## Learning Progression

        

        ### Stage 1: Foundation (First Week)

        

        **Focus**: Understanding the philosophical underpinnings

        

        **Activities**:

        - Read `memory-bank-core-template.md` thoroughly

        - Study the Memory Bank structure and file responsibilities

        - Complete `0-distilledContext.md` and `1-projectbrief.md` for your project

        - Practice validating content against root purpose

        

        **Success Indicators**:

        - Can explain why Memory Bank is not traditional documentation

        - Understands the role of each numbered file (0-8)

        - Has successfully set up the Memory Bank structure

        

        ### Stage 2: Compression Practice (Weeks 2-3)

        

        **Focus**: Learning to compress complexity rather than document it

        

        **Activities**:

        - Practice identifying patterns in existing documentation

        - Attempt to compress detailed information into higher abstractions

        - Use the `mb_compress` tool to analyze compression opportunities

        - Document compression decisions

        

        **Success Indicators**:

        - Successfully reduces document volume while increasing clarity

        - Identifies and merges redundant concepts across files

        - Develops patterns that replace lists of instances

        

        ### Stage 3: Maintenance (Weeks 4-8)

        

        **Focus**: Maintaining structural integrity over time

        

        **Activities**:

        - Set up and use the drift monitor

        - Practice the compression check workflow

        - Create high-impact simplification candidates

        - Use the `mb_audit` tool to perform comprehensive audits

        

        **Success Indicators**:

        - Regularly updates the drift monitor

        - Proactively identifies and corrects structural drift

        - Identifies and implements high-impact simplifications

        

        ### Stage 4: Mastery (Ongoing)

        

        **Focus**: Using Memory Bank as a strategic thinking tool

        

        **Activities**:

        - Implement epoch-based evolution tracking

        - Customize the Memory Bank for specific project needs

        - Integrate Memory Bank thinking with development workflows

        - Teach others to use the Memory Bank approach

        

        **Success Indicators**:

        - Memory Bank actively drives development decisions

        - Structure evolves purposefully with clear lineage

        - The team uses Memory Bank as their primary cognitive architecture

        

        ## Advanced Enhancements

        

        ### Project Identity Anchoring

        

        Establish a clear **project codename** that serves as a cognitive anchor:

        

        1. Choose a concise project codename (e.g., "RLWeb" for "Ringerike Landskap Website")

        2. Add this to all Memory Bank file headers:

        

        ```markdown

        # [File Title] - [ProjectName]

        

        > **[Structural Role Reminder]**: This file [role description] for the **[ProjectName]** project.

        ```

        

        ### Structural Drift Early Warning

        

        Add a file named `drift-monitor.md` to proactively monitor Memory Bank integrity:

        

        1. List each Memory Bank file with its original purpose, current usage, and drift status

        2. Document common drift patterns and correction strategies

        3. Maintain a drift correction log

        4. Include a prevention checklist for future additions

        

        ### High-Impact Simplification Protocol

        

        Create a `simplification-candidates.md` file to formalize the process of identifying and implementing simplifications:

        

        1. Define evaluation criteria (effort, clarity gain, impact, root reinforcement)

        2. Score simplification candidates based on these criteria

        3. Document the implementation plan for high-scoring candidates

        4. Track implemented simplifications and their impact

        

        ### Epoch-Based Evolution Tracking

        

        Implement explicit tracking of major cognitive phases:

        

        1. Create an `epochs/` subdirectory within `lineage/`

        2. After major shifts, create a new epoch directory with snapshots of key files

        3. Include a summary document explaining the transition between epochs

        

        ## Troubleshooting

        

        ### Common Issues and Solutions

        

        | Issue | Possible Cause | Solution |

        |-------|---------------|----------|

        | Memory Bank becoming passive documentation | Lack of compression | Audit for redundancy, elevate insights to patterns |

        | Disconnect between code and Memory Bank | Insufficient updates | Enforce update protocol after code changes |

        | Task disconnection from structure | Root drift | Re-anchor tasks to project mission |

        | Information sprawl | Insufficient compression | Apply the "attempt merge first" principle |

        | Unclear project direction | Root abstraction weakening | Revisit and sharpen `0-distilledContext.md` |

        

        ### When to Reset

        

        Sometimes a full Memory Bank reset is needed:

        

        1. When the project mission fundamentally changes

        2. When the Memory Bank has accumulated too much entropy

        3. When new organizational patterns would better serve the project

        

        In these cases, create a final lineage entry documenting the state and reasoning, then reinitialize the system.

        

        ## Principles in Practice

        

        | Principle | Practical Application |

        |-----------|------------------------|

        | File-Structure-First | *Before* writing any documentation, ensure the Memory Bank structure is set up correctly. |

        | Root-First Thinking | Start every work session by re-reading `0-distilledContext.md` to re-anchor to purpose. |

        | Persistent Simplification | After documenting something, always ask "Can this be made simpler?" |

        | Value Extraction Bias | For any content, ask "What action does this enable?" If none, discard it. |

        | Guardrails | Document why you're adding any new content in `compression-check` notes. |

        | Outward Mapping | Always link content back to higher abstraction levels, never the reverse. |

        

        ---

        

        **Remember**: The Memory Bank only provides value when it actively shapes decisions and actions. Structure is not a container for knowledge; structure is the memory of intent.

    ```



    ---



    #### `07-guides\12-memory-bank-learning-path.md`



    ```markdown

        # Memory Bank Learning Path

        

        > **[Structural Role Reminder]**: This file provides a graduated approach to learning and mastering the Memory Bank system, helping practitioners develop the cognitive skills needed for structure-first thinking.

        

        ## Learning Stages

        

        ### Stage 1: Conceptual Foundation

        

        **Focus**: Understanding the philosophical underpinnings of the Memory Bank system.

        

        **Activities**:

        1. Read the `memory-bank-core-template.md` thoroughly

        2. Study the Memory Bank structure and file responsibilities

        3. Understand the root-first thinking model

        4. Learn the concept of compression-driven documentation

        

        **Success Indicators**:

        - Can explain why Memory Bank is not traditional documentation

        - Understands the role of each numbered file (0-8)

        - Can articulate the difference between "structure as container" vs. "structure as intelligence"

        

        ### Stage 2: Basic Implementation

        

        **Focus**: Setting up and using the basic Memory Bank structure.

        

        **Activities**:

        1. Follow the `memory-bank-implementation-guide.md` to set up the structure

        2. Complete the `0-distilledContext.md` and `1-projectbrief.md` for a project

        3. Populate the remaining files with initial content

        4. Practice validating all content against the root purpose

        

        **Success Indicators**:

        - Has successfully set up the Memory Bank structure for a project

        - Each file contains appropriate content at the correct abstraction level

        - Can trace all content back to the root purpose

        

        ### Stage 3: Compression Skills Development

        

        **Focus**: Learning to compress complexity rather than document it.

        

        **Activities**:

        1. Practice identifying patterns in existing documentation

        2. Attempt to compress detailed information into higher abstractions

        3. Use the `mb_compress` tool to analyze compression opportunities

        4. Document compression decisions in `lineage/` entries

        

        **Success Indicators**:

        - Successfully reduces the total volume of documentation while increasing clarity

        - Can identify and merge redundant concepts across files

        - Develops patterns that replace lists of instances

        

        ### Stage 4: Structural Integrity Management

        

        **Focus**: Maintaining the Memory Bank's health over time.

        

        **Activities**:

        1. Set up and use the drift monitor

        2. Practice the compression check workflow

        3. Create high-impact simplification candidates

        4. Use the `mb_audit` tool to perform comprehensive audits

        

        **Success Indicators**:

        - Regularly updates the drift monitor

        - Proactively identifies and corrects structural drift

        - Identifies and implements at least one high-impact simplification per development cycle

        

        ### Stage 5: Advanced Memory Bank Mastery

        

        **Focus**: Using Memory Bank as a tool for strategic thinking and decision-making.

        

        **Activities**:

        1. Implement epoch-based evolution tracking

        2. Customize the Memory Bank system for specific project needs

        3. Integrate Memory Bank thinking with development workflows

        4. Teach others to use the Memory Bank approach

        

        **Success Indicators**:

        - Memory Bank actively drives development decisions

        - Structure evolves purposefully over time with clear lineage

        - The team uses Memory Bank as their primary cognitive architecture

        - Can adapt the system to different project types while maintaining core principles

        

        ## Common Learning Challenges

        

        ### Challenge: "Documentation Instinct"

        

        **Description**: The tendency to document everything in detail rather than compress to patterns.

        

        **Remediation**:

        - Practice "3 to 1" - For every 3 specific instances, find 1 pattern that captures their essence

        - Use the "justification test" - For each addition, explicitly justify why it needs to exist

        - Regularly audit for "list bloat" - Lists that keep growing without abstraction

        

        ### Challenge: "Root Drift"

        

        **Description**: Gradually losing connection to the project's root purpose.

        

        **Remediation**:

        - Start every Memory Bank session by re-reading `0-distilledContext.md`

        - For each significant decision, explicitly trace the path back to `1-projectbrief.md`

        - Use the drift monitor to catch early signs of disconnect

        

        ### Challenge: "Abstraction Leakage"

        

        **Description**: Mixing abstraction levels, especially putting details in high-level files.

        

        **Remediation**:

        - Review the purpose of each numbered file and its abstraction level

        - Practice "push down, pull up" - Push details down, pull patterns up

        - Use structural role declarations to maintain file focus

        

        ### Challenge: "Passive Documentation"

        

        **Description**: Treating Memory Bank as a historical record rather than an active intelligence system.

        

        **Remediation**:

        - Always ask "What action does this insight enable?"

        - Delete information that doesn't drive decisions or clarify structure

        - Practice metabolic thinking - information must be processed, not just stored

        

        ## Learning Exercises

        

        ### Exercise 1: Root-First Reframing

        

        Take any complex area of your project and attempt to describe it in terms of its connection to the root purpose:

        

        1. Start with specific details about the area

        2. Identify patterns across those details

        3. Connect those patterns to the project's core purpose

        4. Rewrite the description starting from the purpose and working outward

        5. Compare the before and after descriptions

        

        ### Exercise 2: Compression Workshop

        

        Take a lengthy document or set of notes about your project:

        

        1. Identify all unique concepts in the document

        2. Cluster related concepts together

        3. For each cluster, extract a pattern that captures the essence

        4. Rewrite using only the patterns, with specific instances as examples

        5. Measure the reduction in content volume while preserving meaning

        

        ### Exercise 3: Structure Mapping

        

        Analyze your project's file structure:

        

        1. Draw a map of the current structure

        2. Identify areas where the structure doesn't reflect the conceptual organization

        3. Design an ideal structure based on Memory Bank principles

        4. Document the gap and transformation path in `5-structureMap.md`

        5. Implement one high-impact structural improvement

        

        ### Exercise 4: Lineage Thinking

        

        Practice cognitive evolution tracking:

        

        1. Pick a significant understanding about your project from 3 months ago

        2. Document how that understanding has evolved

        3. Identify the catalysts for those changes in understanding

        4. Create a proper lineage entry capturing this evolution

        5. Use this to predict how understanding might evolve in the future

        

        ## Progress Tracking

        

        Use this checklist to track your Memory Bank mastery:

        

        - [ ] **Beginner**: Has set up the basic Memory Bank structure

        - [ ] **Novice**: Can maintain the structure and update content appropriately

        - [ ] **Practitioner**: Actively compresses complexity and manages structural integrity

        - [ ] **Advanced**: Uses Memory Bank to drive development decisions and strategy

        - [ ] **Master**: Evolves the Memory Bank system itself while maintaining its principles

        

        Record insights gained at each stage in your own lineage entries to track your cognitive evolution with the system.

        

        ## Teaching Others

        

        Once you've reached the Practitioner level, consider introducing others to the Memory Bank system:

        

        1. Start with the philosophical foundation - "structure is intelligence"

        2. Demonstrate practical benefits through before/after examples

        3. Guide hands-on implementation with one key file at a time

        4. Review and provide feedback on initial attempts

        5. Gradually introduce advanced concepts like compression checks and drift monitoring

        

        The best way to master the Memory Bank system is to teach it to others, as this forces clarity of explanation and deeper understanding of the principles.

        

        ---

        

        *Note: This learning path is itself a Memory Bank document - it follows the pattern of progressive abstraction, compression of complexity, and root connection. As you learn the system, you'll notice these patterns emerging in your own thinking.*

    ```



    ---



    #### `07-guides\13-memory-bank-system-instruction.md`



    ```markdown

        # Memory Bank System Instruction for AI Coding Assistants

        

        > Paste the content below directly into your AI coding assistant's system instruction field (VSCode's Cline, Cursor AI, etc.)

        

        ```

        # Memory Bank - Progressive Abstraction & Structural Intelligence System

        

        ## Core Generative Principle

        

        You must operate by an inexorable gravitational pull of all complexity toward foundational clarity through iterative recentering and compression. Each structure, detail, or insight is not preserved for itself, but interrogated for its irreducible essence—then recursively re-anchored and distilled until the system becomes its own map, memory, and purpose.

        

        ## Absolute Requirements

        

        1. **File-Structure-First**: Always validate the cognitive map as a filestructure first, based on what exists.

        2. **Root-First Thinking**: Every insight must relate upward toward the project's irreducible mission.

        3. **Persistent Simplification**: Every action must aim to reduce net complexity (unless a rare, justified exception).

        4. **Value Extraction Bias**: Prefer maximizing actionable insight over unfolding more detail.

        5. **Structural Guardrails**: Never mutate structure blindly—every change must be explicitly justified.

        6. **Outward Mapping**: Think outward (root → abstraction layers → concrete implementation), never inward folding.

        

        ## Memory Bank Structure

        

        Use this structure to organize project understanding:

        

        0. `0-distilledContext.md`: Ultra-compressed project essence in 2-3 bullets

        1. `1-projectbrief.md`: Root purpose, value prop, critical constraints

        2. `2-productContext.md`: Users, problems solved, operational context

        3. `3-systemPatterns.md`: Architecture, component patterns, data flow

        4. `4-techContext.md`: Technology stack, constraints, integration points

        5. `5-structureMap.md`: Current vs. target structure with migration path

        6. `6-activeContext.md`: Current focus, bottlenecks, simplifications

        7. `7-progress.md`: Milestones, metrics, tech debt ledger

        8. `8-tasks.md`: Concrete, structure-anchored tasks tracing to root

        

        ## Operational Protocols

        

        ### Compression Reflex Protocol

        

        Before adding new content, always check:

        1. Can this be merged with existing content?

        2. Can this be elevated to a pattern in a higher abstraction file? 

        3. Can this be dissolved as an instance of an existing pattern?

        4. Does this clearly connect to the project's root purpose?

        5. If still adding, explicitly justify why this cannot be compressed further.

        

        ### Pattern Extraction

        

        Identify common elements across 3+ items and create a pattern that encompasses them:

        

        ```

        # Instead of:

        - Feature A handles input validation with regex

        - Feature B handles input validation with regex

        - Feature C handles input validation with regex

        

        # Use:

        Pattern: Features employ regex for input validation

        Features using this pattern: A, B, C

        ```

        

        ### Anti-Patterns to Avoid

        

        - **Passive Documentation**: Never accumulate information without processing it into structure

        - **Detail Sprawl**: Never expand with implementation details rather than patterns

        - **Abstraction Leakage**: Keep information at the correct abstraction level

        - **Orphaned Content**: Every element must trace to the project's root purpose

        - **Root Drift**: Continuously re-anchor all content to the core mission

        

        ## Final Directive

        

        Root Fidelity Above All: If any action does not reinforce, clarify, or consolidate the abstraction-rooted structure—it must not be taken.

        

        Memory Bank is NOT an inventory of chaos. It is a system of persistent abstraction and simplification toward the optimal structure.

        ```

        

        ## How To Use This System

        

        This consolidated system instruction provides a powerful cognitive framework that guides AI coding assistants to:

        

        1. **Organize Understanding by Abstraction**: Root principles → patterns → implementations

        2. **Reduce Complexity**: Continuously simplify and abstract rather than build outward

        3. **Structure Knowledge**: Use the 0-8 numbered files to maintain clear abstraction boundaries

        4. **Extract Patterns**: Identify commonalities across features, functions, or design elements

        5. **Maintain Root Fidelity**: Ensure every decision connects back to the core project purpose

        

        When working with the AI assistant, you should:

        

        1. First establish the project's root purpose in files 0-1

        2. Define external context in files 2-4

        3. Map current and target structure in file 5

        4. Track active development in files 6-8

        

        The assistant will now approach all tasks with:

        - A bias toward abstraction over detail

        - Pattern extraction over specific instances

        - Structural reasoning over narrative explanation

        - Root-anchored decisions over feature-driven ones

        

        This ensures your project develops with a clear cognitive architecture that minimizes entropy and maximizes clarity.

        

        ## Advanced Usage

        

        For collaborative projects, share these files in a `/memory-bank` directory at your project root. Each team member should begin work sessions by:

        

        1. Reading `0-distilledContext.md` to re-anchor to project essence

        2. Reviewing `6-activeContext.md` to understand current focus

        3. Working on tasks from `8-tasks.md`

        4. Updating the Memory Bank with pattern discoveries

        

        For more detailed instructions, refer to the complete Memory Bank implementation guide.

    ```



    ---



    #### `07-guides\14-memory-bank-web-project-template.md`



    ```markdown

        # Project-Specific Memory Bank Template

        

        > **[Structural Role Reminder]**: This template adapts the Memory Bank system to a specific web project, maintaining root fidelity while addressing project-specific needs.

        

        ## ðŸ“– Table of Contents

        1. [Root-First Assimilation Philosophy](#root-first-assimilation-philosophy)

        2. [Canonical Project Structure](#canonical-project-structure)

        3. [Memory Bank Design](#memory-bank-design)

        4. [Assimilation Workflows](#assimilation-workflows)

        5. [Project-Specific Guardrails](#project-specific-guardrails)

        6. [High-Impact Simplification Protocol](#high-impact-simplification-protocol)

        7. [Final Mandate](#final-mandate)

        

        ## Root-First Assimilation Philosophy

        

        I am an expert software engineer specializing in web development. My cognition resets between sessions.

        I operate **exclusively** by reconstructing project context from its rigorously maintained **Memory Bank**. The primary goal is architectural clarity, component consolidation, and establishing a maintainable feature-first structure.

        

        ### Core Imperatives for Web Projects:

        

        - **Domain Primacy**: All decisions must enhance the project's core domain and purpose

        - **Framework Purity**: Maintain framework best practices with strong typing enforcement

        - **CSS Strategy Integrity**: Preserve consistent styling approach (utility-first, modules, etc.)

        - **Performance Covenant**: Never compromise core vitals for features

        

        ### Cleanup Absolutes:

        1. File structure must mirror business domains

        2. Component duplication is existential technical debt

        3. Data sources have clear, consistent patterns

        4. Responsive design is non-negotiable

        

        ## Canonical Project Structure

        

        ```plaintext

        ProjectName/

        â”œâ”€â”€ 01_config/               # Project configs (framework, styling, linting)

        â”œâ”€â”€ 02_src/

        â”‚   â”œâ”€â”€ 01_pages/             # Page orchestration

        â”‚   â”œâ”€â”€ 02_layout/            # Global page structures

        â”‚   â”œâ”€â”€ 03_features/          # Feature-driven modules

        â”‚   â”œâ”€â”€ 04_shared/            # Cross-feature logic/hooks

        â”‚   â”œâ”€â”€ 05_ui/                # Context-free UI primitives

        â”‚   â””â”€â”€ 06_assets/            # Static images, media

        â”œâ”€â”€ memory-bank/

        â”‚   â”œâ”€â”€ 0-distilledContext.md

        â”‚   â”œâ”€â”€ 1-projectbrief.md

        â”‚   â”œâ”€â”€ 2-productContext.md

        â”‚   â”œâ”€â”€ 3-systemPatterns.md

        â”‚   â”œâ”€â”€ 4-techContext.md

        â”‚   â”œâ”€â”€ 5-structureMap.md

        â”‚   â”œâ”€â”€ 6-activeContext.md

        â”‚   â”œâ”€â”€ 7-progress.md

        â”‚   â”œâ”€â”€ 8-tasks.md

        â”‚   â””â”€â”€ lineage/              # (Optional) Structural evolution snapshots

        â”œâ”€â”€ 03_scripts/              # Utility scripts

        â”œâ”€â”€ 04_public/               # Static public assets

        â”œâ”€â”€ package.json

        â””â”€â”€ README.md

        ```

        

        âœ… Numbered, predictable.

        âœ… Feature-first modular architecture.

        âœ… `/memory-bank/` as cognitive heartbeat.

        

        ## Memory Bank Design

        

        ### Project-Specific Files

        

        | File | Project-Specific Focus |

        |------|---------------|

        | `0-distilledContext.md` | Project essence: Purpose, target users, primary functionality in 2-3 bullets |

        | `1-projectbrief.md` | Project mission, scope, core value proposition, constraints (Technical/Business/UX) |

        | `2-productContext.md` | Problems solved, target users, user journey, value connections |

        | `3-systemPatterns.md` | Current & Target structure, component hierarchy, state flow, data flow, routing |

        | `4-techContext.md` | Framework, styling approach, key constraints (Performance, Accessibility, Responsive), build process, essential libraries |

        | `5-structureMap.md` | Current file structure vs. target structure with clear mapping and migration path |

        | `6-activeContext.md` | Current focus, component analysis, refactoring decisions, key findings on implementation |

        | `7-progress.md` | Status log, milestones, metrics tracking, known issues blocking refactoring |

        | `8-tasks.md` | Concrete tasks linked to target structure, validation criteria |

        

        ### Project-Specific Expansion Rules

        

        > New files (e.g., `9-apiStrategy.md`, `10-contentStructure.md`) are allowed **only** if they **clarify**, **simplify**, and **reinforce** the project's abstraction hierarchy â€” and must be **explicitly justified** within `5-structureMap.md` or `6-activeContext.md` based on reducing complexity or improving clarity.

        

        ## Assimilation Workflows

        

        ### Phase 1: Project Structure Validation

        

        ```mermaid

        flowchart TD

            A[Validate Feature Boundaries] --> B[Audit Component Duplication]

            B --> C[Map Data Flows]

            C --> D[Verify Implementation Patterns]

        ```

        

        ### Phase 2: High-Impact Cleaning

        

        ```mermaid

        flowchart TD

            A[Consolidate UI Primitives] --> B[Refactor State Management]

            B --> C[Optimize Asset Loading]

            C --> D[Enforce Type Contracts]

        ```

        

        ### Phase 3: Structure-Based Development

        

        ```mermaid

        flowchart TD

            Start[Start Task from 8-tasks.md] --> CheckMemoryBank[Check Memory Bank]

            CheckMemoryBank --> ExecuteTask[Execute Task]

            ExecuteTask --> AnalyzeImpact[Analyze Impact]

            AnalyzeImpact --> DocumentUpdates[Document Updates]

        ```

        

        | Phase | Focus |

        | :---- | :---- |

        | Start Task | Select task directly linked to a structure goal |

        | Check Memory Bank | Review relevant context from memory bank files |

        | Execute Task | Implement with minimal disruption, following established patterns |

        | Analyze Impact | Assess how changes affect structure, types, and other components |

        | Document Updates | Update relevant memory bank files with new insights and progress |

        

        ## Project-Specific Guardrails

        

        ### Component Standards Template

        

        ```typescript

        // All components must adhere to:

        interface ComponentSpec {

          domain: 'feature1' | 'feature2' | /* other domains */;

          type: 'primitive' | 'feature' | 'layout';

          props: TypedInterface;

          hooks: 'none' | 'domain' | 'global';

          styling: 'defined-approach';

          tests: 'required' | 'none';

        }

        ```

        

        ### Forbidden Patterns

        

        - Anti-patterns specific to framework (e.g., Class components in React)

        - Prop drilling beyond 3 levels

        - Untyped functions/components

        - Inline SVG elements

        - Direct DOM manipulation

        - Inconsistent styling approaches

        

        ### Approved Complexity Reduction Paths

        

        1. Convert duplicated components to compound patterns

        2. Extract domain-specific hooks from UI

        3. Collapse similar layout components

        4. Implement shared data fetching strategies

        5. Standardize form handling

        

        ## High-Impact Simplification Protocol

        

        Every major assimilation cycle for the project must seek **one** **High-Impact Simplification**:

        

        - **Identify Opportunity**: Find a minimal intervention yielding maximum clarity, component reduction, or improved maintainability

        - **Validate Alignment**: Must align strictly with project root abstraction and target architecture (`3-systemPatterns.md`)

        - **Document Proposal & Impact**:

          - `5-structureMap.md` or `6-activeContext.md`: Rationale for the simplification.

          - `7-progress.md`: Expected impact (e.g., "Reduces N duplicated components", "Simplifies content updates").

          - `8-tasks.md`: Concrete task(s) to implement the simplification.

        

        ## Final Mandate

        

        ### Project Validation Checklist

        

        - [ ] All components trace to business domain

        - [ ] No duplicate UI primitives

        - [ ] Asset system uses optimization strategy

        - [ ] State management follows consistent patterns

        - [ ] Type strictness enforced

        - [ ] Memory Bank updated pre-commit

        

        Before touching **any code** or **updating the Memory Bank**:

        

        - **Validate the Root**: Re-read `1-projectbrief.md` (or `0-distilledContext.md`). Is the core mission clear?

        - **Confirm Structural Alignment**: Does the proposed change or documentation fit logically within the `memory-bank/` structure and the target architecture (`3-systemPatterns.md`)?

        - **Proceed Only If**: The action **reinforces** the core mission, **simplifies** the codebase complexity, or **clarifies** the architecture in service of that mission.

        

        > **One Purpose (Project Mission). One Structure (Memory Bank). Infinite Adaptability (Clean Code).**

    ```




## Message 5

**Pulling together insights**

I’m tasked with refining an existing Memory Bank architecture. Clarifying the specific requirements will ensure the new template is accurate and effective.

**Piecing together the requirements**

I'm mapping out a new Memory Bank template, integrating the existing architecture details. This approach aims to create a coherent and streamlined system.

## Message 6

Below is a **consolidated “Master Template”** that brings together the layered structure you’ve outlined, incorporating each folder and file template from your provided architecture. You can treat this as a “blueprint” repository layout—each file (or directory) is either a placeholder or a template file that can be copied into your own project. Then, populate/edit them as needed.

---

# Memory Bank Master Template

```
memory-bank/
├── 00-README.md
├── 01-abstraction-root
│   ├── 00-distilledContext-template.md
│   └── 01-projectbrief-template.md
├── 02-context
│   ├── 02-productContext-template.md
│   ├── 04-techContext-template.md
│   └── 09-framework-specific-adaptations-template.md
├── 03-structure-design
│   ├── 03-systemPatterns-template.md
│   ├── 05-structureMap-template.md
│   ├── 09-abstract-patterns-glossary-template.md
│   └── 10-simplification-candidates-template.md
├── 04-process-tracking
│   ├── 06-activeContext-template.md
│   ├── 07-progress-template.md
│   ├── 08-tasks-template.md
│   └── 09-drift-monitor-template.md
├── 05-evolution
│   └── 09-lineage-template.md
├── 06-tools
│   ├── 01-mb-quick-setup.sh
│   ├── 02-mb-assistant.sh
│   └── 09-mb-tools.sh
└── 07-guides
    ├── 00-README.md
    ├── 01-quick-start-guide.md
    ├── 02-visual-guide.md
    ├── 03-processing-order-guide.md
    ├── 09-memory-bank-core-template.md
    ├── 10-memory-bank-enhancement-module.md
    ├── 11-memory-bank-implementation-guide.md
    ├── 12-memory-bank-learning-path.md
    ├── 13-memory-bank-system-instruction.md
    └── 14-memory-bank-web-project-template.md
```

Below is an **overview** of each directory and file, along with the **template content** in Markdown form. Copy these into your own repo structure to build out a complete Memory Bank system from scratch.

---

## 1. Top-Level README (00-README.md)

**Path**: `memory-bank/00-README.md`  
**Purpose**: Introduces the layered abstraction structure, numeric ordering system, and usage instructions.

<details>
<summary>View Template</summary>

```markdown
# Memory Bank Templates

> **[Structural Role Reminder]**: This document provides an overview of the Memory Bank templates organized by abstraction layers.

## Layered Abstraction Structure

The Memory Bank templates are organized according to their level of abstraction, making the system self-describing. Each directory encapsulates one layer of structural responsibility, supporting system cohesion and clarity.

```plaintext
memory-bank/
├── 00-README.md                # Project overview and navigation
├── 01-abstraction-root/        # Irreducible project essence and mission
│   ├── 00-distilledContext.md  # Ultra-compressed project essence in 2-3 bullets
│   └── 01-projectbrief.md      # Root purpose definition and critical constraints
├── 02-context/                 # External reality and constraints
│   ├── 02-productContext.md    # User needs, problems solved, and external context
│   ├── 04-techContext.md       # Technology stack, constraints, and integration points
│   └── 09-framework-specific-adaptations.md  # Framework-specific patterns
├── 03-structure-design/        # Architectural patterns and structure
│   ├── 03-systemPatterns.md    # System architecture, component hierarchy, data flows
│   ├── 05-structureMap.md      # Current and target file structure with migration path
│   ├── 09-abstract-patterns-glossary.md      # Reusable patterns and anti-patterns
│   └── 10-simplification-candidates.md       # High-impact simplification tracking
├── 04-process-tracking/        # Active development and status
│   ├── 06-activeContext.md     # Current focus, bottlenecks, and in-progress simplifications
│   ├── 07-progress.md          # Milestones, metrics, and simplifications achieved
│   ├── 08-tasks.md             # Structure-anchored tasks
│   └── 09-drift-monitor.md     # Structural integrity monitoring
├── 05-evolution/               # Cognitive evolution
│   └── 09-lineage.md           # Cognitive evolution snapshots
├── 06-tools/                   # Automation tools
│   ├── 01-mb-quick-setup.sh    # 5-minute setup script
│   ├── 02-mb-assistant.sh      # Interactive guidance tool
│   └── 09-mb-tools.sh          # CLI utilities for Memory Bank
└── 07-guides/                  # Implementation guidance
    ├── 00-README.md            # Guide directory navigation
    ├── 01-quick-start-guide.md # Fast onboarding guide
    ├── 02-visual-guide.md      # Visual diagrams and workflows
    ├── 03-processing-order-guide.md # Processing order
    ├── 09-memory-bank-core-template.md       # Core system principles
    ├── 10-memory-bank-enhancement-module.md  # Advanced enhancement modules
    ├── 11-memory-bank-implementation-guide.md # Implementation instructions
    ├── 12-memory-bank-learning-path.md       # Graduated learning approach
    ├── 13-memory-bank-system-instruction.md  # AI system instructions
    └── 14-memory-bank-web-project-template.md # Web-specific adaptation
```

## Numeric Ordering System

The Memory Bank uses a dual-level numeric ordering system:

1. **Directory Level (01–07)**: Folders are numbered to reflect their position in the abstraction hierarchy, from most abstract (01) to implementation guides (07).
2. **File Level (00–14)**: Files within each directory are also numbered.

This ensures:
- Natural sorting in file browsers
- Clear reflection of abstraction hierarchy

## Implementation Flow

1. Start with this README (`00-README.md`)
2. Create and fill in core files in numerical sequence across directories
3. Add enhancement files (09+) as needed
4. Use tools and guides for reference

For detailed guidance on the exact processing order, see:
`07-guides/03-processing-order-guide.md`

---
```
</details>

---

## 2. Abstraction Root (01-abstraction-root)

Houses your project’s **irreducible essence**. Contains two key templates:

1. `00-distilledContext-template.md`  
2. `01-projectbrief-template.md`

<details>
<summary>View <code>00-distilledContext-template.md</code></summary>

```markdown
# Distilled Context

> **[Structural Role Reminder]**: This file provides the ultra-compressed project essence in 2-3 bullets.

## Project Irreducible Essence

- **Purpose**: [One sentence stating why this project must exist]
- **Value**: [One sentence describing the unique value it provides]
- **Core Constraint**: [One sentence on the primary constraint that shapes everything else]

## Current Focus

- [Brief description of the current highest-priority focus area]
- [How this focus directly reinforces the project's root purpose]

---
*Note: This doc must be revisited before any major development to ensure every action aligns with the core essence.*
```
</details>

<details>
<summary>View <code>01-projectbrief-template.md</code></summary>

```markdown
# Project Brief

> **[Structural Role Reminder]**: Defines the root abstraction — the project's irreducible mission, value proposition, and critical constraints.

## Mission Statement

[One paragraph defining why this project must exist, who it serves, and the core problem it solves]

## Core Value Proposition

- **For**: [Target users/audience]
- **Who**: [Key problem/need]
- **This Project**: [How it uniquely addresses that need]
- **Unlike**: [Current alternatives]
- **Our Solution**: [Key differentiator]

## Critical Constraints

### Technical Constraints
- [Technical boundaries not to be crossed]
- [Performance requirements, security, etc.]

### Business Constraints
- [Timelines, resource constraints, stakeholder needs]

### User Experience Constraints
- [Accessibility, usability standards, brand alignment, etc.]

## Success Definition
[Clear, measurable criteria that define successful completion or adoption]

---
*Note: All project decisions must be validated against this root mission.*
```
</details>

---

## 3. Context (02-context)

Captures **external reality**—user problems, environment, and technical constraints.

- `02-productContext-template.md`
- `04-techContext-template.md`
- `09-framework-specific-adaptations-template.md` (for React, Vue, Angular, etc.)

<details>
<summary>View <code>02-productContext-template.md</code></summary>

```markdown
# Product Context

> **[Structural Role Reminder]**: Maps external reality (users, needs, outcomes).

## User Archetypes

### Primary User: [Name]
- **Demographics**: [...]
- **Goals**: [...]
- **Pain Points**: [...]
- **Success Metrics**: [...]

### Secondary User: [Name]
- [Same structure]

## Problems Solved

| Problem      | Current Reality        | Our Solution         | Value Created        |
|--------------|------------------------|----------------------|----------------------|
| [Problem 1]  | [Existing situation]   | [Approach]           | [Specific value]     |

## User Journey
1. **Discovery**: [...]
2. **Onboarding**: [...]
3. **Core Usage**: [...]
4. **Mastery**: [...]
5. **Advocacy**: [...]

## External Context Factors
- Seasonal, geographic, or market considerations
- Competitive landscape

## Value Connections
[Explicit connections showing how user needs support the root mission in `1-projectbrief.md`]

---
*Note: All features should trace back to these user needs.*
```
</details>

<details>
<summary>View <code>04-techContext-template.md</code></summary>

```markdown
# Technical Context

> **[Structural Role Reminder]**: Defines the technical constraints and stack choices.

## Technology Stack

| Tech            | Version | Purpose | Justification                                     |
|-----------------|---------|---------|---------------------------------------------------|
| [Framework]     | [vX.X]  | [Why?]  | [Why this aligns with project mission]           |
| [Language]      | [vX.X]  | [Why?]  | [Why this aligns with project mission]           |
| [Database]      | [vX.X]  | [Why?]  | [Why this aligns with project mission]           |
| [State Mgr]     | [vX.X]  | [Why?]  | [Redux, Vuex, Pinia, etc.; justification]        |

## Key Technical Constraints

### Performance
- [Load time target, e.g. <2s on 4G]
- [Bundle size limit, etc.]

### Compatibility
- [Browser/Device list]

### Security
- [Authentication method, compliance]

## Integration Points
- External APIs, third-party services, fallback strategies

## Technical Debt & Known Limitations
| Area       | Description                    | Impact             | Mitigation Strategy     |
|------------|--------------------------------|--------------------|-------------------------|
| [Area 1]   | [What’s limited]              | [Effect on project]| [How to handle it]      |

---
*Note: All technical decisions must maintain alignment with architecture in `3-systemPatterns.md` and root mission in `1-projectbrief.md`.*
```
</details>

<details>
<summary>View <code>09-framework-specific-adaptations-template.md</code></summary>

```markdown
# Framework-Specific Memory Bank Adaptations

> **[Structural Role Reminder]**: Specialized guidelines for each frontend framework (React, Vue, Angular, etc.).

## React/TypeScript Adaptation
- Preferred functional component patterns
- React-specific forbidden patterns (no class components, limit prop drilling, etc.)
- React-specific complexity reduction (custom hooks, memoization, etc.)

## Vue.js Adaptation
- Composition API recommended patterns
- Vuex/Pinia usage
- Single File Components structure
- Complexity reduction (composables, watchers)

## Angular Adaptation
- NgModule structure, lazy loading
- OnPush change detection
- Service layer refinements

## Framework Agnostic Best Practices
1. Naming consistency
2. Type safety
3. Performance patterns
4. Testing strategy

---
```
</details>

---

## 4. Structure Design (03-structure-design)

Focuses on architectural patterns, the actual/target structure, known best practices, and simplification tracking.

- `03-systemPatterns-template.md`
- `05-structureMap-template.md`
- `09-abstract-patterns-glossary-template.md`
- `10-simplification-candidates-template.md`

<details>
<summary>View <code>03-systemPatterns-template.md</code></summary>

```markdown
# System Patterns

> **[Structural Role Reminder]**: Defines the project's architectural form: component organization, data/state flow, routing, etc.

## Architecture Overview
```mermaid
flowchart TD
    User --> UI
    UI --> Features
    Features --> Domain
    Domain --> Data
    Data --> ExternalServices
```

## Current Structure
[Detailed textual or tree representation]

## Target Structure
[Where we want to go, and why it’s better aligned to the mission]

## Component Hierarchy
[Detailed diagrams or tables for layouts, pages, features, UI elements]

## Data Flow Patterns
- State management approach
- Data fetching and caching
- Error/loading states

## Routing Architecture
[Route structure, lazy-loading, etc.]

## Cross-Cutting Concerns
- Authentication, error handling, logging, i18n, accessibility

---
```
</details>

<details>
<summary>View <code>05-structureMap-template.md</code></summary>

```markdown
# Structure Map

> **[Structural Role Reminder]**: Maps the current file structure against the target structure.

## Current Structure Reality

```plaintext
[Full current file structure]
```

## Target Structure

```plaintext
[Ideal or planned file structure]
```

## Migration Path
[Mermaid Gantt or bullet steps describing how to get from current → target]

## Dependency Management
[Chart of modules, their current deps, and how it will change]

## Impact Analysis
[How restructuring affects dev, users, testing, etc.]

---
*Note: Keep `5-structureMap.md` updated whenever the planned structure or reality shifts.*
```
</details>

<details>
<summary>View <code>09-abstract-patterns-glossary-template.md</code></summary>

```markdown
# Abstract Patterns Glossary

> **[Structural Role Reminder]**: Catalogs structural patterns, anti-patterns, and complexity-reduction strategies.

## Core Structural Patterns
[Root Anchoring, Progressive Abstraction, Single Responsibility, etc.]

## Anti-Patterns to Avoid
[Passive Documentation, Detail Sprawl, Abstraction Leakage, etc.]

## Complexity Reduction Strategies
[Pattern Extraction, Reference Instead of Duplication, Tabular Compression, Visualize Relationships, etc.]

---
```
</details>

<details>
<summary>View <code>10-simplification-candidates-template.md</code></summary>

```markdown
# High-Impact Simplification Candidates

> **[Structural Role Reminder]**: Formalizes the process of identifying & implementing simplifications.

## Evaluation Criteria
1. **Minimal Implementation Effort** (1-10)
2. **Structural Clarity Gain** (1-10)
3. **Widespread Impact** (1-10)
4. **Root Reinforcement** (1-10)
5. **Score** = (Clarity × Impact × Root) ÷ Effort

## Current Candidates
| ID  | Simplification           | Effort | Clarity | Impact | Root | Score | Status   |
|-----|--------------------------|--------|---------|--------|------|-------|----------|
| S1  | [Short desc]            | [1-10] | [1-10]  | [1-10] | [1-10]| [auto]| Pending  |

## Implemented Simplifications
[Log table of completed or in-progress simplifications]

---
*Note: Aim for at least one high-impact simplification every dev cycle.*
```
</details>

---

## 5. Process Tracking (04-process-tracking)

Monitors **active context**, daily/weekly progress, tasks, and drift. Essentially the “live” part of your Memory Bank.

- `06-activeContext-template.md`
- `07-progress-template.md`
- `08-tasks-template.md`
- `09-drift-monitor-template.md`

<details>
<summary>View <code>06-activeContext-template.md</code></summary>

```markdown
# Active Context

> **[Structural Role Reminder]**: Tracks the current focus, in-progress work, bottlenecks, and active simplifications.

## Current Focus Areas
### Primary Focus: [Name]
- **What**: [...]
- **Why**: [...]
- **Key Decisions**: [...]
- **Open Questions**: [...]

## In-Progress Work
| Work Item | Description | Owner | Status | Blockers | Root Connection |
|-----------|------------|------|--------|----------|-----------------|
| [Item 1]  | [Desc]     | ...  | ...    | ...      | [Ties to mission?] |

## Current Bottlenecks
| Bottleneck      | Impact  | Root Cause       | Mitigation     | Resolution Path  |
|-----------------|---------|------------------|---------------|------------------|
| [Bottleneck 1]  | [High]  | [Cause]          | [Strategy]     | [Plan]           |

## Active Simplifications
[List or table referencing 10-simplification-candidates if relevant]

## Recent Design Decisions
[List or table of major decisions and rationale]

---
*Note: Update at the start/end of each work session. It’s the “living doc” of daily dev efforts.*
```
</details>

<details>
<summary>View <code>07-progress-template.md</code></summary>

```markdown
# Progress

> **[Structural Role Reminder]**: Tracks assimilation milestones, simplifications achieved, and overall status.

## Project Status Overview
| Metric                | Status       | Trend | Target | Notes      |
|-----------------------|-------------|-------|--------|------------|
| Overall Completion    | [XX%]       | ↑/↓   | [...]  | [Notes]    |
| Technical Debt        | [Low/Med]   | ...   | ...    | ...        |

## Completed Milestones
| Milestone       | Date       | Description                   | Impact       | Root Connection                |
|-----------------|------------|-------------------------------|--------------|--------------------------------|
| [Milestone 1]   | [YYYY-MM-DD]| [Desc]                       | [High/Med]   | [Ties to mission in `1-projectbrief.md`?] |

## Major Simplifications Achieved
[List of big improvements from the 10-simplification-candidates]

## Technical Debt Ledger
[Table of known debts, severity, plan to address]

## Metrics Tracking
[Key metrics, usage stats, etc.]

---
*Note: Summaries here are typically updated weekly or after major events.*
```
</details>

<details>
<summary>View <code>08-tasks-template.md</code></summary>

```markdown
# Tasks

> **[Structural Role Reminder]**: Lists concrete, structure-anchored tasks that trace to the root purpose.

## Active Tasks

### High Priority
| ID   | Task           | Desc            | Value | Dependencies | Owner    | Status   | Root Connection                |
|------|----------------|-----------------|-------|-------------|---------|----------|--------------------------------|
| H1   | [Task Name]    | [Short desc]    | [val] | [deps]      | [owner] | [status] | [Which part of `1-projectbrief`?] |

### Medium Priority
| ID   | Task           | Desc            | Value | Dependencies | Owner    | Status   | Root Connection                |
|------|----------------|-----------------|-------|-------------|---------|----------|--------------------------------|

### Low Priority
| ID   | Task           | Desc            | Value | Dependencies | Owner    | Status   | Root Connection                |
|------|----------------|-----------------|-------|-------------|---------|----------|--------------------------------|

## Task Details
[Detailed breakdown for each task, acceptance criteria, etc.]

## Completed Tasks
| ID  | Task          | Completion Date | Outcome | Impact   |
|-----|---------------|-----------------|---------|----------|
| [ID]| [Task Name]   | [YYYY-MM-DD]    | [...]   | [...]    |

---
*Note: Every task must have a clear root connection back to `1-projectbrief.md`.*
```
</details>

<details>
<summary>View <code>09-drift-monitor-template.md</code></summary>

```markdown
# Drift Monitor

> **[Structural Role Reminder]**: Proactive monitoring mechanism for Memory Bank integrity.

## Last Structure Validation: [Date]

| File                    | Original Purpose                                                          | Current Usage                   | Drift Status | Action Needed          |
|-------------------------|--------------------------------------------------------------------------|---------------------------------|-------------|------------------------|
| `0-distilledContext.md` | [Ultra-compressed essence]                                               | [Brief usage desc]              | ✅ or ⚠️     | [None or fix needed]  |
| `1-projectbrief.md`     | [Root purpose]                                                           | [...]                           | ✅ or ⚠️     | [...]                 |

## Common Drift Patterns
| Pattern              | Warning Signs                                  | Correction Strategy                          |
|----------------------|-----------------------------------------------|----------------------------------------------|
| Purpose Dilution     | File no longer connected to root mission      | Re-anchor or remove                          |
| Scope Expansion      | File covers multiple responsibilities         | Split or clarify roles                       |
| Abstraction Leakage  | Implementation details creeping into root doc | Move details down to lower-level file        |

## Drift Correction Log
| Date        | File                  | Issue Detected                                 | Correction Applied                          |
|------------|-----------------------|-----------------------------------------------|--------------------------------------------|
| [YYYY-MM-DD]| [File Name]          | [Issue or mismatch]                            | [How it was resolved]                      |

---
*Note: Update after each major Memory Bank edit or monthly at minimum.*
```
</details>

---

## 6. Evolution (05-evolution)

Captures **cognitive evolution**—key insights, big shifts in understanding or structure.

- `09-lineage-template.md`

<details>
<summary>View <code>09-lineage-template.md</code></summary>

```markdown
# Lineage Entry: [Title of Cognitive Shift]

> **[Structural Role Reminder]**: Records a significant shift in project understanding.

## Context
**Date**: [YYYY-MM-DD]  
**Memory Bank Version**: [Snapshot or version number]  
**Project Phase**: [e.g., Beta]

## Prior Understanding
[Brief description of how the project was seen before this shift]

## Cognitive Shift
### Key Insight
[The new realization that changed your approach]

### Catalyzing Factors
- [Events/observations leading to this shift]

## Structural Impact
### Memory Bank Changes
| File Path                | Changes                     | Justification             |
|--------------------------|-----------------------------|---------------------------|
| [e.g. 3-systemPatterns.md] | [What changed in content]   | [Why it was needed]       |

## Root Connection
[How this shift still aligns with or refines the root mission]

## Practical Implications
- [Any new development approach]
- [Verification method to confirm correctness]

## Next Steps
- [Immediate tasks triggered by this shift]

---
```
</details>

---

## 7. Tools (06-tools)

**Automation scripts** for Memory Bank management.

- `01-mb-quick-setup.sh`  
- `02-mb-assistant.sh`  
- `09-mb-tools.sh`

<details>
<summary>View <code>01-mb-quick-setup.sh</code></summary>

A short script that creates minimal core files (`0-distilledContext.md`, `1-projectbrief.md`, etc.). Ideal for a quick start.

```bash
#!/bin/bash
# Memory Bank Quick Setup Script
...
```
*(See the full content in your provided snippet.)*
</details>

<details>
<summary>View <code>02-mb-assistant.sh</code></summary>

A **Bash-based interactive guide** for your Memory Bank usage, prompting how to update files daily, when to do weekly tasks, how to handle drift, etc.

```bash
#!/bin/bash
# Memory Bank Assistant - Interactive guidance
...
```
*(See the full content in your provided snippet.)*
</details>

<details>
<summary>View <code>09-mb-tools.sh</code></summary>

A more **comprehensive CLI** for auditing, validating, compressing, or snapshotting the Memory Bank.

```bash
#!/bin/bash
# Memory Bank Tools
...
```
*(See the full content in your provided snippet.)*
</details>

---

## 8. Guides (07-guides)

Collection of **how-to** documents for quick starts, visual overviews, best practices, advanced usage, AI system instructions, etc.

| File                                        | Purpose                                                             |
|---------------------------------------------|---------------------------------------------------------------------|
| `00-README.md`                              | Overview of the guides directory                                    |
| `01-quick-start-guide.md`                   | Simple, practical instructions to get started in minutes            |
| `02-visual-guide.md`                        | Visual diagrams (Mermaid) for quick architecture references         |
| `03-processing-order-guide.md`              | Exact sequence for processing Memory Bank files                     |
| `09-memory-bank-core-template.md`           | The fundamental principles and structure of the system              |
| `10-memory-bank-enhancement-module.md`      | Advanced enhancements (epoch-based evolution, drift monitor, etc.)  |
| `11-memory-bank-implementation-guide.md`    | Comprehensive instructions for setting up and mastering the system  |
| `12-memory-bank-learning-path.md`           | Staged approach to learning Memory Bank (foundation → mastery)      |
| `13-memory-bank-system-instruction.md`      | System-level instructions for AI coding assistants                  |
| `14-memory-bank-web-project-template.md`    | Web-specific adaptation including typical “feature-first” structure |

<details>
<summary>Example: <code>13-memory-bank-system-instruction.md</code></summary>

```markdown
# Memory Bank System Instruction for AI Coding Assistants

> Paste the content below directly into your AI coding assistant's system instruction field.

```
# Memory Bank - Progressive Abstraction & Structural Intelligence System
...
```

*(See the full content in your provided snippet.)*
</details>

<details>
<summary>Example: <code>14-memory-bank-web-project-template.md</code></summary>

```markdown
# Project-Specific Memory Bank Template

> **[Structural Role Reminder]**: Adapts the Memory Bank system to a specific web project with domain primacy, performance covenant, etc.

## Root-First Assimilation Philosophy
[Core imperatives for web development projects: domain primacy, framework purity, CSS integrity, performance covenant]

## Canonical Project Structure
[Feature-first layout, memory-bank directory as the “cognitive heartbeat”]

## Memory Bank Design
[Which files map to product context, system patterns, tasks, etc.]

## Assimilation Workflows
[Phased approach: structure validation, high-impact cleaning, structure-based dev]

## Project-Specific Guardrails
- Forbidden patterns (e.g., no React class comps)
- Approved complexity reduction paths

## High-Impact Simplification Protocol
[Document each cycle’s big win for clarity and maintainability]

## Final Mandate
[Checklists to ensure root alignment, structural clarity, no duplication]

> **One Purpose (Project Mission). One Structure (Memory Bank). Infinite Adaptability (Clean Code).**
```
</details>

---

## Using This “Master Template”

1. **Clone or copy** this folder structure into your project’s `/memory-bank/`.
2. **Delete** any pieces you don’t need. For a minimal approach, you really only need:
   - `01-abstraction-root/00-distilledContext-template.md`
   - `01-abstraction-root/01-projectbrief-template.md`
   - `04-process-tracking/06-activeContext-template.md`
   - `04-process-tracking/08-tasks-template.md`
3. **Fill out** the templates in numerical order:
   1. `0-distilledContext.md` and `1-projectbrief.md` (defining the root mission)
   2. `2-productContext.md` and any relevant context files
   3. `3-systemPatterns.md` and `5-structureMap.md`
   4. `6-activeContext.md`, `7-progress.md`, `8-tasks.md`
4. **Integrate** the Memory Bank into your daily workflow:
   - Start each day with `0-distilledContext.md` to re-anchor
   - Update `6-activeContext.md` with daily focus and blockers
   - Manage tasks in `8-tasks.md`
   - Mark progress in `7-progress.md` at week’s end
5. **Leverage** the scripts in `06-tools/` for quick setup, validation, drift monitoring, etc.
6. **Iterate** and adapt. If your project needs more specialized files, be sure to justify them in `5-structureMap.md` or `6-activeContext.md` per the system’s expansion rules.

---

### Final Note
This layout is intentionally **modular** and **scalable**. You can start with the bare minimum (just two or three files) and expand as your project grows or your team’s complexity demands. The key is to **keep everything root-aligned,** always seeking to compress complexity back into patterns and structured clarity.

> **“Memory Bank is not what you know about the project; it is what the project knows about itself.”**  
> Let the structure drive clarity, not merely store data.

Enjoy building your **Memory Bank** with this new, fully fleshed-out template!

## Message 7

Additional context:



    # Distilled Context: Ringerike Landskap Website



    - *Project Essence*: Digital showcase for Ringerike Landskap, a landscaping company in Norway, with hyperlocal SEO focus to connect regional customers with personalized landscaping services.

    - *Critical Constraint*: Must maintain authenticity of the two owners' personal approach while delivering a modern, responsive, and technically robust website.

    - *Current Focus*: Establish comprehensive Memory Bank for maximum codebase assimilation with root abstraction driving all understanding.



    ---



    # Project Brief: Ringerike Landskap Website



    ## Irreducible Purpose

    To create a digital showcase for Ringerike Landskap that connects local customers in the Ringerike region with the company's personalized landscaping services through an authentic representation of the owners' craftsmanship and customer-centric approach.



    ## Core Value Proposition

    A modern, responsive website that differentiates Ringerike Landskap in the local market by emphasizing the owners' personal investment in each project, their technical expertise (including specialized welding and corten steel work), and their deep understanding of the local terrain, while implementing hyperlocal SEO strategies to maximize regional visibility.



    ## Critical Constraints



    ### Technical

    - *Stack*: React 18, TypeScript, Vite, Tailwind CSS

    - *Performance*: Optimized loading times and Core Web Vitals metrics

    - *Responsive*: Mobile-first approach supporting all device sizes

    - *Accessibility*: Must follow accessibility best practices (ARIA, semantic HTML)



    ### Business

    - *Authenticity*: Must preserve and showcase the owners' genuine personal approach

    - *Regional Focus*: Hyperlocal SEO targeting the Ringerike region (Hole, HÃ¸nefoss, Jevnaker, Sundvollen, Vik)

    - *Service Presentation*: Eight core services must be clearly presented with filtering options

    - *Seasonal Adaptation*: Content must adapt based on current season



    ### User Experience

    - *Simplicity*: Clear, straightforward navigation and information architecture

    - *Call to Action*: Prominent "Book Gratis Befaring" (Free Consultation) throughout

    - *Trust Building*: Integration of customer testimonials and project showcases

    - *Filtering System*: Intuitive filtering for services and projects



    ## Project Boundaries



    ### In Scope

    - Informational website with service and project showcases

    - Contact form for customer inquiries

    - Responsive design for all device sizes

    - SEO optimization for local search terms

    - Seasonal content adaptation



    ### Out of Scope

    - E-commerce functionality

    - Customer login/accounts

    - Complex animations or 3D elements

    - CMS integration (content managed through structured data files)

    - Online booking system (contact form only)



    ## Core Success Metrics

    - Technical performance (Lighthouse scores >90)

    - Search visibility for target keywords

    - Lead generation through contact form

    - User engagement with project showcases

    - Mobile usability metrics



    # Product Context: Ringerike Landskap Website



    ## Target Audience & User Problems



    ### Primary Audience

    1. *Local Homeowners (20-50km radius)*

       - *Problems Solved*: Finding trusted local landscape professionals with terrain knowledge; visualizing potential outdoor solutions; efficiently communicating landscaping needs

       - *Behavioral Context*: Researching options before winter for spring projects; responding to immediate needs (drainage problems, outdoor renovations)

       - *Value Sought*: Personal connection; craftsmanship; local expertise; reliability



    2. *Property Developers & Commercial Clients*

       - *Problems Solved*: Finding contractors capable of larger-scale projects; ensuring quality consistent with property value; addressing compliance requirements

       - *Behavioral Context*: Planning phases for development projects; seeking portfolio evidence of similar work

       - *Value Sought*: Professionalism; efficiency; project management capabilities; scalable solutions



    ### Secondary Audience

    1. *Existing Customers*

       - *Problems Solved*: Finding additional services; sharing completed work with others; referring friends and family

       - *Behavioral Context*: Returning after project completion for testimonials or additional services

       - *Value Sought*: Relationship continuity; service consistency; recognition



    2. *Regional Architects & Designers*

       - *Problems Solved*: Finding implementation partners for landscape designs; connecting clients with trusted contractors

       - *Behavioral Context*: Seeking collaboration on design implementation

       - *Value Sought*: Technical capability; design sensitivity; collaborative approach



    ## External Contexts & Boundary Conditions



    ### Geographic Context

    - *Service Area*: Ringerike region - Hole, HÃ¸nefoss, Jevnaker, Sundvollen, Vik (20-50km radius)

    - *Logistical Constraints*: Travel time affects pricing and availability

    - *Local Terrain Knowledge*: Understanding of soil conditions, drainage requirements, frost mitigation specific to Ringerike



    ### Seasonal Context

    - *Norwegian Seasonal Cycle*: Service demand and type changes dramatically with seasons

    - *Planning Windows*: Customers research in winter for spring projects

    - *Implementation Timeline*: Weather constrains when services can be performed



    ### Market Context

    - *Competitive Landscape*: Few landscaping companies in the region with modern digital presence

    - *Differentiation Parameters*: Personal service, specialized welding, local knowledge

    - *Industry Standards*: Need to showcase certification, compliance with Norwegian building standards



    ### Customer Journey Context

    1. *Awareness*: Local search, referrals, seeing completed projects in neighborhood

    2. *Consideration*: Website research, photo galleries, service details

    3. *Decision*: Contact form, consultation request, direct contact

    4. *Engagement*: In-person meeting, customized proposal

    5. *Post-Project*: Testimonial opportunity, referrals, seasonal maintenance



    ## Real-World to Technical Value Connections



    | Real-World Need | Technical Implementation | Value Delivered |

    |----------------|--------------------------|-----------------|

    | Local search visibility | Hyperlocal SEO, schema markup | Customers find the company when searching for local services |

    | Project visualization | Categorized project galleries with filtering | Customers can see similar projects to what they envision |

    | Trust building | Authentic testimonials, owner profiles | Personal connection before first contact |

    | Seasonal relevance | Dynamic content adaptation based on current season | Timely, relevant service suggestions |

    | Service understanding | Clear descriptions with visual examples | Customers understand service options and applications |

    | Easy contact | Prominent CTAs, simple contact form | Reduced friction to initiate conversations |



    ## Purpose-Aligned Success Criteria



    ### Business Impact Metrics

    - *Lead Generation*: 20+ quality leads per month during peak season

    - *Geographic Penetration*: Inquiries from all targeted communities

    - *Service Distribution*: Inquiries across all eight core services

    - *Conversion Rate*: >15% conversion from website visit to contact

    - *Digital Attribution*: >40% of new business attributed to website



    ### User Experience Metrics

    - *Mobile Engagement*: Equal or better engagement metrics on mobile vs desktop

    - *Information Discovery*: >70% of users view multiple service pages

    - *Project Exploration*: >50% of users interact with filtering functions

    - *Contact Completion*: >80% contact form completion rate once started

    - *Return Visits*: >25% of visitors return within 30 days



    ### Technical Performance Metrics

    - *Page Speed*: <2s initial load on 4G connection

    - *Core Web Vitals*: All metrics in "Good" range

    - *SEO Performance*: First page rankings for all target keywords

    - *Accessibility*: WCAG AA compliance

    - *Crawlability*: 100% of pages indexed by search engines


## Message 8

**Incorporating context**

I'm gathering input from the user to enhance the system integration or memory bank approach. There's a hint towards unifying the design and incorporating specific project details.

**Integrating insights**

I’m piecing together recent trends and applying the integrated context to unify the design, ensuring cohesiveness across the memory bank and the new project specifics.

**Integrating details**

I’m pulling together all the data provided to create a cohesive and unified system. Ensuring a seamless blend across memory bank files, and preparing a draft for final review.

**Generating integration insights**

I'm working on a concise message to showcase the memory bank's layout with new data. Explaining next steps, creating a curated version for the 2-productContext.md file.

## Message 9

Below is an **adapted Memory Bank “Master Template”** for the Ringerike Landskap Website project—fully aligned with the context you’ve already defined. It weaves your existing **Distilled Context**, **Project Brief**, and **Product Context** into the standard 0–8 file structure. Use this layout to solidify all further architecture, tasks, and progress in one coherent system.

You can copy these files into a `/memory-bank/` folder in your project. Feel free to rename or adjust any details to best fit your workflow.

---

## Overall Folder Structure

```
memory-bank/
├── 0-distilledContext.md
├── 1-projectbrief.md
├── 2-productContext.md
├── 3-systemPatterns.md
├── 4-techContext.md
├── 5-structureMap.md
├── 6-activeContext.md
├── 7-progress.md
├── 8-tasks.md
├── drift-monitor.md                     (optional but recommended)
├── simplification-candidates.md         (optional but recommended)
└── lineage/                             (for big cognitive shifts)
```

**Note**  
- You have **0-distilledContext**, **1-projectbrief**, and **2-productContext** already filled out (shown below).  
- The other files (`3-systemPatterns.md`, `4-techContext.md`, etc.) are partially templated; fill them as you refine architecture, track tasks, etc.

---

## 1. Distilled Context (0-distilledContext.md)

**Content (Already Defined)**:

```markdown
# Distilled Context: Ringerike Landskap Website

> **[Structural Role Reminder]**: Provides ultra-compressed project essence.

- *Project Essence*: Digital showcase for Ringerike Landskap, a landscaping company in Norway, with hyperlocal SEO focus to connect regional customers with personalized landscaping services.
- *Critical Constraint*: Must maintain authenticity of the two owners' personal approach while delivering a modern, responsive, and technically robust website.
- *Current Focus*: Establish comprehensive Memory Bank for maximum codebase assimilation with root abstraction driving all understanding.
```

**Usage**  
- Revisit this file before any major dev sessions to keep all decisions connected to the irreducible mission.

---

## 2. Project Brief (1-projectbrief.md)

**Content (Already Defined)**:

```markdown
# Project Brief: Ringerike Landskap Website

> **[Structural Role Reminder]**: Defines the root mission, value proposition, and constraints.

## Irreducible Purpose
To create a digital showcase for Ringerike Landskap that connects local customers in the Ringerike region with the company’s personalized landscaping services through an authentic representation of the owners’ craftsmanship and customer-centric approach.

## Core Value Proposition
A modern, responsive website that differentiates Ringerike Landskap in the local market by emphasizing the owners’ personal investment in each project, their technical expertise (including specialized welding and corten steel work), and their deep understanding of the local terrain—while implementing hyperlocal SEO strategies to maximize regional visibility.

## Critical Constraints

### Technical
- *Stack*: React 18, TypeScript, Vite, Tailwind CSS
- *Performance*: Optimized loading times and Core Web Vitals
- *Responsive*: Mobile-first approach
- *Accessibility*: Must follow accessibility best practices (ARIA, semantic HTML)

### Business
- *Authenticity*: Must preserve and showcase the owners’ genuine personal approach
- *Regional Focus*: Hyperlocal SEO targeting Ringerike region
- *Service Presentation*: Eight core services must be clearly presented with filtering
- *Seasonal Adaptation*: Content must adapt based on current season

### User Experience
- *Simplicity*: Straightforward navigation
- *Call to Action*: Prominent “Book Gratis Befaring” (free consultation)
- *Trust Building*: Testimonials, project showcases
- *Filtering System*: Intuitive filtering for services/projects

## Project Boundaries
**In Scope**  
- Informational site with service/project showcases  
- Contact form for inquiries  
- SEO optimization, seasonal content adaptation  

**Out of Scope**  
- E-commerce, user accounts, advanced booking systems, CMS

## Core Success Metrics
- Lighthouse performance >90
- Search visibility for local keywords
- Lead generation (contact form)
- User engagement with showcases
- Mobile usability metrics
```

**Usage**  
- Ensure every feature or structural decision can be traced back here.

---

## 3. Product Context (2-productContext.md)

**Content (Already Defined)**:

```markdown
# Product Context: Ringerike Landskap Website

> **[Structural Role Reminder]**: Maps external reality—users, needs, environment.

## Target Audience & User Problems

### Primary Audience
1. *Local Homeowners*
   - Problems: Need local expertise, personal connection
   - Value: Craftsmanship, reliability
2. *Property Developers & Commercial Clients*
   - Problems: Larger-scale projects, compliance
   - Value: Professionalism, project management

### Secondary Audience
1. *Existing Customers*
2. *Regional Architects & Designers*

## External Contexts & Boundary Conditions

### Geographic Context
- Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik)
- Local terrain knowledge needed

### Seasonal Context
- Norwegian seasonal cycle
- Planning in winter, implementing in spring/summer

### Market Context
- Few modern digital presences among competitors
- Differentiation: Personal service, specialized welding, local knowledge

### Customer Journey Context
1. Awareness (local search/referrals)
2. Consideration (research, photo galleries)
3. Decision (contact form)
4. Engagement (proposal, in-person)
5. Post-Project (testimonials, referrals)

## Real-World to Technical Value Connections

| Real-World Need         | Tech Implementation              | Value Delivered                         |
|-------------------------|----------------------------------|-----------------------------------------|
| Local search visibility | Hyperlocal SEO, schema markup    | Found by relevant local customers       |
| Project visualization   | Filtering for galleries          | Sees relevant examples, trust-building  |
| Trust building          | Testimonials, owner profiles     | Personal connection, authenticity       |

## Purpose-Aligned Success Criteria

### Business Impact Metrics
- 20+ quality leads/month (peak season)
- 15%+ conversion from visitors → contact
- Digital attribution >40%

### User Experience Metrics
- 70%+ users view multiple services
- 50%+ interact with filtering
- 80%+ contact form completion

### Technical Performance Metrics
- <2s load on 4G
- All Core Web Vitals “Good”
- WCAG AA compliance
```

**Usage**  
- Justify new site features or content against these user archetypes, problems, and success metrics.

---

## 4. System Patterns (3-systemPatterns.md)

**Template** (to be filled out with your architecture plans):

```markdown
# System Patterns

> **[Structural Role Reminder]**: Defines how the Ringerike Landskap site’s architecture, components, and data flow are organized.

## Architecture Overview

```mermaid
flowchart TD
    User --> UI[React (Tailwind)]
    UI --> Features[Feature Modules: e.g., Services, Gallery, ContactForm]
    Features --> Domain[Core Domain Logic: hooking in local knowledge, SEO]
    Domain --> Data[Local JSON/YAML? or Headless CMS? (Out of scope?)]
    Data --> External[3rd-party services, if any]
```

## Current Structure (Hypothetical or Initial)
```plaintext
src/
├── components/
├── pages/
├── ...
```

## Target Structure
```plaintext
02_src/
├── 01_pages/       # main routes
├── 02_layout/      # site-wide layout components
├── 03_features/    # feature-driven modules
├── 04_shared/      # shared logic/hooks
├── 05_ui/          # pure UI primitives
└── 06_assets/      # images, icons
```

### Why This Helps?
- Reflects business domains (Services, Projects, etc.)
- Minimizes duplication
- Enhances maintainability

## Data Flow & State Management
- Possibly minimal global state with React Context
- Each feature manages local state or uses custom hooks
- Keep SEO data in separate config or JSON file?

## Routing Approach
- Using React Router with top-level pages (Home, Services, Gallery)
- Lazy load sub-routes if needed

## Cross-Cutting Concerns
- Authentication: Not needed?
- Error handling: Basic 404, fallback pages
- Logging: Basic console logging or external tool
- Internationalization: Possibly single-language (Norwegian)

---
*Note: Update this file as your real architecture emerges.*
```

---

## 5. Tech Context (4-techContext.md)

**Template**:

```markdown
# Technical Context

> **[Structural Role Reminder]**: Detailed environment & dev tools for Ringerike Landskap site.

## Technology Stack

| Tech          | Version  | Purpose                                  | Justification                                        |
|---------------|----------|------------------------------------------|------------------------------------------------------|
| React         | 18.x     | Frontend UI                              | Aligns with dev skillset, easy dynamic components    |
| TypeScript    | 4.x      | Strong typing                            | Prevents runtime errors, clarity in code             |
| Vite          | 3.x      | Bundler for React/TS                     | Fast dev builds, minimal config                      |
| Tailwind CSS  | 3.x      | Utility-first styling                     | Quick iteration, consistent design patterns          |

## Build & Dev Tools

| Tool              | Version | Purpose        | Configuration Details          |
|-------------------|---------|----------------|--------------------------------|
| ESLint            | ...     | Linting        | Style, TypeScript checks       |
| Prettier          | ...     | Code formatting| Enforce consistent style       |
| Testing Library   | ...     | UI tests       | Validate components & flows    |
| Lighthouse        | ...     | Perf & SEO audits | Part of manual checks        |

## Key Technical Constraints

### Performance
- <2s load on 4G
- Pre-render or partial SSR?

### Compatibility
- Chrome, Safari, Firefox, Edge (last 2 versions)
- iOS/Android mobile

### Security
- Minimal data handling: contact form only
- Spam filtering on forms (e.g. reCAPTCHA or honeypot?)

## Integration Points
- Possibly a Google Maps embed for location
- Potential 3rd-party email service for contact form?

## Known Limitations
| Area                     | Description                                       | Impact                  | Mitigation                 |
|--------------------------|---------------------------------------------------|-------------------------|----------------------------|
| Server-Side Rendering?   | Not implemented; site is static SPA for now       | SEO might be impacted?  | Use prerender or static export later |
| Multi-language           | Currently only Norwegian                          | Non-Norwegian visitors see partial UI? | Possibly add English fallback if needed |

## Development & Deployment
- Hosted on [Vercel/Netlify/Your choice?]
- CI pipeline with [GitHub Actions?]
- Automated Lighthouse checks?

---
*Note: Keep this updated as your dev environment evolves.*
```

---

## 6. Structure Map (5-structureMap.md)

**Template**:

```markdown
# Structure Map

> **[Structural Role Reminder]**: Compare current vs. target project structure; define a migration path.

## Current Structure

```plaintext
[Paste your existing folder/file layout here, e.g. from 'tree' command]
```

### Structure Analysis
| Area             | Current State                               | Issues                      | Root Cause                                 |
|------------------|---------------------------------------------|-----------------------------|--------------------------------------------|
| e.g. Components  | Single 'components' folder with all comps   | Hard to find code           | Lacks domain-based grouping                |
| e.g. Pages       | /pages with random naming                   | Inconsistent route naming   | No naming convention established           |
| e.g. Styles      | Global CSS only                             | Hard to keep consistent     | No utility-based approach yet (Tailwind)   |

## Target Structure

```plaintext
02_src/
├── 01_pages/
├── 02_layout/
├── 03_features/
├── 04_shared/
├── 05_ui/
└── 06_assets/
```

| Area          | Target Organization                 | Benefits                                | Alignment to Project Goals                 |
|---------------|-------------------------------------|-----------------------------------------|--------------------------------------------|
| Components    | Domain-based /features, /ui         | Clear scoping, easier maintenance       | Matches “feature-first” approach           |
| CSS Strategy  | Tailwind for utility classes        | Less custom CSS, faster dev cycle       | Improves performance, reduces complexity   |

## Migration Path
```mermaid
gantt
    dateFormat YYYY-MM-DD
    section Phase 1
    Refactor_Components  :a1, 2023-06-01, 7d
    Reorganize_Pages     :a2, after a1, 5d
    section Phase 2
    Tailwind_Integration :a3, 2023-06-15, 10d
```

## Dependency Management

| Module  | Current Deps           | Target Deps          | Migration Strategy                    |
|---------|------------------------|----------------------|---------------------------------------|
| /hooks  | uses direct axios calls| use shared fetch util| Consolidate fetch logic in /shared    |

## Impact Analysis
| Area            | Dev Impact | User Impact       | Testing Requirements |
|-----------------|-----------|-------------------|----------------------|
| Layout Overhaul | Medium     | Potentially none  | Regression tests     |

---
```

---

## 7. Active Context (6-activeContext.md)

**Template**:

```markdown
# Active Context

> **[Structural Role Reminder]**: Tracks current focus, in-progress tasks, and bottlenecks.

## Current Focus Areas

### Primary Focus: “Establish a Domain-Based Folder Structure”
- **What**: Reorganize code into /features, /ui, etc.
- **Why**: Align codebase to Ringerike Landskap’s root mission of clarity & authenticity
- **Status**: In Progress
- **Key Decisions**: Use 02_src/xx naming for clarity
- **Open Questions**: How to handle global layout vs. sub-layout?

### Secondary Focus: “Hyperlocal SEO Enhancements”
- **What**: Integrate structured data, location-based meta tags
- **Why**: Increase findability in Ringerike region
- **Status**: Planned
- **Open Questions**: Tools or manual approach?

## In-Progress Work
| Work Item          | Description                                  | Owner   | Status     | Blockers                   | Root Connection |
|--------------------|----------------------------------------------|---------|-----------|----------------------------|-----------------|
| Reorg Components   | Move comps to /03_features & /05_ui          | Dev A   | In Progress | None yet                   | Project Brief #1 (maintain clarity) |
| Setup Tailwind     | Install & configure                          | Dev B   | Planned    | Need final design tokens   | Tech constraints |

## Current Bottlenecks
| Bottleneck          | Impact   | Root Cause        | Mitigation                | Resolution Path          |
|---------------------|----------|-------------------|---------------------------|--------------------------|
| Lack of design tokens | Medium  | No brand guidelines yet | Minimal placeholder colors, revisit later | Final brand color scheme |

## Active Simplifications
- **Candidate**: Combine repetitive React components for “service cards” → Single reusable `<ServiceCard>`.

## Recent Design Decisions
| Decision           | Rationale                             | Alternatives       | Impact                    | Date       |
|--------------------|---------------------------------------|--------------------|---------------------------|-----------|
| Use Vite over CRA  | Faster dev experience, simpler config  | CRA or Webpack     | Speed & config simplicity| 2023-XX-XX|

---
```

---

## 8. Progress (7-progress.md)

**Template**:

```markdown
# Progress

> **[Structural Role Reminder]**: Logs milestones, simplifications, metrics.

## Project Status Overview
| Metric                      | Status  | Trend | Target         | Notes                 |
|-----------------------------|--------|-------|---------------|-----------------------|
| Overall Completion          | ~20%   | ↑     | 100%           | Early dev stages      |
| Technical Debt             | Low    | →     | Keep minimal   | Reorg in progress     |
| Lighthouse Perf Score       | 85     | ↑     | >90            | Some images need opt. |
| Search Visibility           | N/A    | ...   | Rank for local | SEO not implemented   |

## Completed Milestones
| Milestone                                | Completion Date | Description                                     | Impact              | Root Connection                                               |
|------------------------------------------|-----------------|-------------------------------------------------|---------------------|----------------------------------------------------------------|
| Initial File Reorg                       | 2023-XX-XX      | Moved components to domain-based structure      | Dev clarity         | Supports “Feature-First” approach from project mission        |

## Major Simplifications Achieved
### Simplification 1: Service Card Consolidation
- **Before**: Duplicate components for each service
- **After**: Single `<ServiceCard>` with props
- **Complexity Reduction**: 6 → 1 components
- **Value Added**: Easier to maintain & style
- **Date Completed**: 2023-XX-XX
- **Root Connection**: Aligned with mission to keep code lean & authentic

## Technical Debt Ledger
| Debt Item       | Severity | Intro Date  | Mitigation Plan                            | Status     |
|-----------------|----------|-------------|--------------------------------------------|------------|
| Hardcoded SEO meta | Medium   | 2023-XX-XX  | Use dynamic meta w/ structured data         | In progress|

## Known Issues
| Issue                       | Impact  | Root Cause                    | Workaround               | Resolution Plan         | Priority |
|-----------------------------|--------|------------------------------|--------------------------|-------------------------|----------|
| Non-optimized images        | Medium | Not compressed or lazy loaded | None yet                 | Switch to image opt.    | High     |

## Retrospective Insights
| Insight             | Area       | Source   | Application                                | Date       |
|---------------------|-----------|---------|--------------------------------------------|-----------|
| Need brand kit      | UI/Design  | Developer feedback | Essential to unify styling                | 2023-XX-XX|

---
```

---

## 9. Tasks (8-tasks.md)

**Template**:

```markdown
# Tasks

> **[Structural Role Reminder]**: Lists all structure-anchored tasks.

## Active Tasks

### High Priority
| ID  | Task                              | Description                                           | Value        | Dependencies           | Assigned To | Status   | Root Connection                                      |
|-----|-----------------------------------|-------------------------------------------------------|--------------|------------------------|------------|----------|------------------------------------------------------|
| H1  | Component Folder Reorg           | Move & rename comps by domain-based logic             | High clarity | None                   | Dev A      | In Prog  | Preserves authenticity & clarity from project brief |
| H2  | Implement Tailwind                | Add tailwind.config.js + base utility classes         | Faster dev   | Potential brand kit?   | Dev B      | Planned  | ‘Modern, responsive website’ in project mission     |

### Medium Priority
| ID  | Task                              | Description                                           | Value        | Dependencies           | Assigned To | Status   | Root Connection                                      |
|-----|-----------------------------------|-------------------------------------------------------|--------------|------------------------|------------|----------|------------------------------------------------------|
| M1  | Seasonal Content Hook            | Switch hero text based on season (winter/spring)      | Feels local  | Tweak date/time logic  | Dev A      | Todo     | ‘Seasonal adaptation’ constraint                     |
| M2  | Photo Gallery Filtering          | Add tag-based filter for “Corten Work,” “Drainage,” etc.  | Showcase variety | Finalizing data structure | Dev B  | Todo     | Helps “Project Visualization” in product context     |

### Low Priority
| ID  | Task                              | Description                                           | Value        | Dependencies           | Assigned To | Status   | Root Connection                                      |
|-----|-----------------------------------|-------------------------------------------------------|--------------|------------------------|------------|----------|------------------------------------------------------|
| L1  | ReCAPTCHA on Contact Form        | Basic spam protection                                 | Medium       | Email handler?         | Dev B      | Todo     | Reliability, ensures consistent user experience      |

## Task Details

### H1: Component Folder Reorg
- **Description**: Migrate to `03_features/` and `05_ui/` pattern
- **Value Proposition**: Clear domain-based architecture
- **Acceptance Criteria**:
  1. All domain logic in `03_features/*`
  2. All purely presentational comps in `05_ui/*`
- **Implementation Approach**: Move + rename, fix imports
- **Dependencies**: None
- **Risks**: Broken imports if not thorough
- **Root Connection**: Reflects “Feature-first” structure from `3-systemPatterns.md`

### M1: Seasonal Content Hook
- **Description**: Automatic text or images for winter/spring
- **Value Proposition**: Personalized experience, leverages “local seasonal changes”
- **Acceptance Criteria**:
  1. Query current date/season
  2. Change hero text background or CTA wording
- **Implementation Approach**: Simple date-based logic or local library
- **Dependencies**: Final brand images for winter vs. summer
- **Risks**: Wrong region-based season date if not set carefully
- **Root Connection**: Ties to “Seasonal Adaptation” constraint

## Completed Tasks
| ID  | Task                              | Completion Date | Outcome                                              | Impact               |
|-----|-----------------------------------|-----------------|------------------------------------------------------|----------------------|
| H0  | Initial Memory Bank Setup         | 2023-XX-XX      | Created baseline structure for memory-bank directory | Shared clarity       |

---
```

---

## (Optional) Drift Monitor (drift-monitor.md)

**Template**:

```markdown
# Drift Monitor

> **[Structural Role Reminder]**: Early warning system for structural integrity.

## Last Structure Validation: [Date]

| File                    | Original Purpose                                              | Current Usage                   | Drift Status | Action Needed          |
|-------------------------|--------------------------------------------------------------|---------------------------------|-------------|------------------------|
| 0-distilledContext.md   | Ultra-compressed essence                                     | [How used?]                     | ✅ or ⚠️     | [None?]               |
| 1-projectbrief.md       | Root mission & constraints                                   | [How used?]                     | ✅ or ⚠️     | [None?]               |
| 2-productContext.md     | User needs, environment                                     | [How used?]                     | ✅ or ⚠️     | [None?]               |
| ...                     | ...                                                          | ...                             | ...         | ...                    |

## Common Drift Patterns
| Pattern              | Warning Signs                      | Correction Strategy          |
|----------------------|------------------------------------|-----------------------------|
| Purpose Dilution     | Files no longer anchor to mission  | Re-anchor or prune          |
| Scope Expansion      | Mixed responsibilities in 1 file   | Split or restructure        |
| Abstraction Leakage  | Low-level details in high-level doc| Move details to correct file|

## Drift Correction Log
| Date       | File                 | Issue Detected                     | Correction Applied                  |
|------------|----------------------|------------------------------------|-------------------------------------|
| [YYYY-MM-DD]| [File Name]         | [Describe mismatch]                | [What was changed]                  |

---
```

---

## (Optional) Simplification Candidates (simplification-candidates.md)

**Template**:

```markdown
# High-Impact Simplification Candidates

> **[Structural Role Reminder]**: Formal process for identifying big simplifications.

## Evaluation Criteria
1. Minimal Effort (1–10)
2. Structural Clarity Gain (1–10)
3. Widespread Impact (1–10)
4. Root Reinforcement (1–10)
5. Score = (Clarity × Impact × Root) ÷ Effort

## Current Candidates
| ID  | Simplification                              | Effort | Clarity | Impact | Root | Score | Status   |
|-----|---------------------------------------------|--------|---------|--------|------|-------|----------|
| S1  | Combine “service cards” & unify props       | 2      | 6       | 8      | 8    | ~19.2  | Pending  |
| S2  | Single partial for all “filter forms”       | 3      | 6       | 7      | 7    | ~14.0  | Planned  |

## Candidate Details
### S1: Combine service cards
- **Description**: Instead of 6 near-identical components, one `<ServiceCard>`
- **Current Complexity**: Hard to maintain or restyle
- **Proposed Solution**: Single card with typed props for each service
- **Expected Benefits**: -80% duplication, simpler styling
- **Root Connection**: Lean code → maintains “authentic but modern” approach

## Implemented Simplifications
| ID  | Simplification                     | Completion Date | Actual Effort | Actual Impact | Lessons Learned                |
|-----|------------------------------------|----------------|---------------|--------------|--------------------------------|
| S0  | ...                                | ...            | ...           | ...          | ...                            |

---
```

---

## (Optional) Lineage Folder (lineage/)

**Purpose**  
- Keep `.md` files capturing big “aha” moments or structural shifts.  

**Example File**: `lineage/2023-06-10-architecture-shift.md`

```markdown
# Lineage Entry: Architecture Shift to Domain Folders

> **[Structural Role Reminder]**: A record of a significant structural pivot.

## Context
**Date**: 2023-06-10

## Prior Understanding
We had all components in a single folder, minimal separation.

## Cognitive Shift
We realized domain-based structuring clarifies dev flow and matches local business concept.

## Structural Impact
| File               | Changes                  | Justification                 |
|--------------------|--------------------------|------------------------------|
| 3-systemPatterns.md| Updated architecture docs| Reduces confusion, root-based |

## Root Connection
Aligns with “Feature-first” imperative in `1-projectbrief.md`.

## Practical Implications
- Dev must move relevant code by mid-June
- Will unify service cards

## Next Steps
- Complete folder reorg
- Confirm no broken references
```

---

# How to Use This Memory Bank Layout

1. **Copy/Paste** the templates into your `memory-bank/` folder.  
2. **Fill in** the placeholders for `3-systemPatterns.md`, `4-techContext.md`, `5-structureMap.md`, etc., based on your real code structure.  
3. **Use** `6-activeContext.md` and `8-tasks.md` daily or weekly to track in-progress efforts.  
4. **Monitor** `7-progress.md` for big milestones and simplifications.  
5. Optionally maintain `drift-monitor.md` and `simplification-candidates.md` for advanced structure discipline.  
6. If you discover major conceptual shifts, add them to `lineage/`.

---

## Final Tip: Daily Flow

- **Morning**: Glance at `0-distilledContext.md` → update `6-activeContext.md`
- **Throughout the day**: Work on tasks from `8-tasks.md`, referencing patterns in `3-systemPatterns.md` if you add new features
- **End of day/week**: Mark progress in `7-progress.md`, check for drift or new simplifications

This ensures everything you build remains anchored to the Ringerike Landskap Website’s root mission—**authentic local landscaping services** with a modern, performance-minded approach.

## Message 10

I actually want you to provide the *system_message* to inherently define all the rules within the instruction-template itself.

You haven't fully understood the inherent structure within the template, I'll try to remoind you through an example:



    ## Table of Contents



    1. [Overview of Memory Bank Philosophy](#overview-of-memory-bank-philosophy)

    2. [Memory Bank Structure](#memory-bank-structure)

    3. [Core Workflows](#core-workflows)

    4. [Documentation Updates](#documentation-updates)

    5. [Example Incremental Directory Structure](#example-incremental-directory-structure)

    6. [Why Numbered Filenames?](#why-numbered-filenames)

    7. [Additional Guidance](#additional-guidance)

    8. [New High-Impact Improvement Step (Carried from v3)](#new-high-impact-improvement-step-carried-from-v3)

    9. [Optional Distilled Context Approach](#optional-distilled-context-approach)



    ---



    ## Overview of Memory Bank Philosophy



    I am Cline, an expert software engineer with a unique characteristic: my memory resets completely between sessions. This is by design and **not** a limitation. To ensure no loss of information, I rely entirely on my Memory Bank—its meticulously maintained files and documentation—to understand the project each time I begin work.



    **Core Principles & Guidelines Integrated:**



    - We adhere to **clarity, structure, simplicity, elegance, precision, and intent** in all documentation.

    - We pursue **powerful functionality** that remains **simple and minimally disruptive** to implement.

    - We choose **universally resonant breakthroughs** that uphold **contextual integrity** and **elite execution**.

    - We favor **composition over inheritance**, **single-responsibility** for each component, and minimize dependencies.

    - We document only **essential** decisions, ensuring that the Memory Bank remains **clean, maintainable, and highly readable**.



    **Memory Bank Goals**:



    - **Capture** every critical aspect of the project in discrete Markdown files.

    - **Preserve** chronological clarity with simple, sequential numbering.

    - **Enforce** structured workflows that guide planning and execution.

    - **Update** the Memory Bank systematically whenever changes arise.



    By following these approaches, I ensure that no knowledge is lost between sessions and that the project evolves in alignment with the stated principles, guidelines, and organizational directives.



    ---



    ## Memory Bank Structure



    The Memory Bank consists of **core files** and **optional context files**, all in Markdown format. Each is clearly numbered to reflect the natural reading and updating order, from foundational context to tasks and progress. This structured approach upholds **clarity and simplicity**—fundamental to the new guiding principles.



    ```mermaid

    flowchart TD

        PB[1-projectbrief.md] --> PC[2-productContext.md]

        PB --> SP[3-systemPatterns.md]

        PB --> TC[4-techContext.md]



        PC --> AC[5-activeContext.md]

        SP --> AC

        TC --> AC



        AC --> PR[6-progress.md]

        PR --> TA[7-tasks.md]

    ```



    ### Core Files (Required)



    1. **`1-projectbrief.md`**

       - **Foundation document** for the project

       - Defines core requirements and scope

       - Must remain **concise** yet **complete** to maintain clarity



    2. **`2-productContext.md`**

       - **Why** the project exists

       - The primary problems it solves

       - User experience goals and target outcomes



    3. **`3-systemPatterns.md`**

       - **System architecture overview**

       - Key technical decisions and patterns

       - Integrates **composition over inheritance** concepts where relevant



    4. **`4-techContext.md`**

       - **Technologies used**, development setup

       - Constraints, dependencies, and **tools**

       - Highlights minimal needed frameworks



    5. **`5-activeContext.md`**

       - **Current work focus**, recent changes, next steps

       - Essential project decisions, preferences, and learnings

       - Central place for in-progress updates



    6. **`6-progress.md`**

       - **What is working** and what remains

       - Known issues, completed features, evolving decisions

       - Short, precise tracking of status



    7. **`7-tasks.md`**

       - **Definitive record** of project tasks

       - Tracks to-do items, priorities, ownership, or progress

       - Maintain single responsibility for each task to ensure clarity



    ### Additional Context



    Create extra files under `memory-bank/` if they simplify or clarify complex features, integration specs, testing, or deployment. Continue sequential numbering to preserve readability (e.g., `8-APIoverview.md`, `9-integrationSpec.md`, etc.).



    ---



    ## Core Workflows



    ### Plan Mode



    ```mermaid

    flowchart TD

        Start[Start] --> ReadFiles[Read Memory Bank]

        ReadFiles --> CheckFiles{Files Complete?}



        CheckFiles -->|No| Plan[Create Plan]

        Plan --> Document[Document in Chat]



        CheckFiles -->|Yes| Verify[Verify Context]

        Verify --> Strategy[Develop Strategy]

        Strategy --> Present[Present Approach]

    ```



    1. **Start**: Begin the planning process.

    2. **Read Memory Bank**: Load **all** relevant `.md` files in `memory-bank/`.

    3. **Check Files**: Verify if any core file is missing or incomplete.

    4. **Create Plan** (if incomplete): Document how to fix or fill the gaps.

    5. **Verify Context** (if complete): Confirm full understanding.

    6. **Develop Strategy**: Outline tasks clearly and simply, respecting single responsibility.

    7. **Present Approach**: Summarize the plan and next steps.



    ### Act Mode



    ```mermaid

    flowchart TD

        Start[Start] --> Context[Check Memory Bank]

        Context --> Update[Update Documentation]

        Update --> Execute[Execute Task]

        Execute --> Document[Document Changes]

    ```



    1. **Start**: Begin the task.

    2. **Check Memory Bank**: Read the relevant files **in order**.

    3. **Update Documentation**: Apply needed changes to keep everything accurate.

    4. **Execute Task**: Implement solutions, following minimal-disruption and **clean code** guidelines.

    5. **Document Changes**: Log new insights and decisions in the relevant `.md` files.



    ---



    ## Documentation Updates



    Memory Bank updates occur when:

    1. New project patterns or insights emerge.

    2. Significant changes are implemented.

    3. The user requests **update memory bank** (must review **all** files).

    4. Context or direction requires clarification.



    ```mermaid

    flowchart TD

        Start[Update Process]



        subgraph Process

            P1[Review ALL Numbered Files]

            P2[Document Current State]

            P3[Clarify Next Steps]

            P4[Document Insights & Patterns]



            P1 --> P2 --> P3 --> P4

        end



        Start --> Process

    ```



    > **Note**: Upon **update memory bank**, carefully review every relevant file, especially `5-activeContext.md` and `6-progress.md`. Their clarity is crucial to maintaining minimal disruption while enabling maximum impact.

    >

    > **Remember**: Cline’s memory resets each session. The Memory Bank must remain **precise** and **transparent** so the project can continue seamlessly.



    ---



    ## Example Incremental Directory Structure



    Below is a sample emphasizing numeric naming for simplicity, clarity, and predictable ordering:



    ```

    └── memory-bank

        ├── 1-projectbrief.md          # Foundation: scope, requirements

        ├── 2-productContext.md        # Why project exists; user goals

        ├── 3-systemPatterns.md        # System architecture, key decisions

        ├── 4-techContext.md           # Technical stack, constraints

        ├── 5-activeContext.md         # Current focus, decisions, next steps

        ├── 6-progress.md              # Status, known issues, accomplishments

        └── 7-tasks.md                 # Definitive record of tasks

    ```



    Additional files (e.g., `8-APIoverview.md`, `9-integrationSpec.md`) may be added if they **enhance** clarity and organization without unnecessary duplication.



    ---



    ## Why Numbered Filenames?



    1. **Chronological Clarity**: Read and update in a straightforward, logical order.

    2. **Predictable Sorting**: Most file browsers list files numerically, so the sequence is self-evident.

    3. **Workflow Reinforcement**: Reflects how the Memory Bank progressively builds context.

    4. **Scalability**: Adding a new file simply takes the next available number, preserving the same approach.



    ---



    ## Additional Guidance



    - **Strict Consistency**: Reference every file by its exact numeric filename (e.g., `2-productContext.md`).

    - **File Renaming**: If you introduce a new core file or reorder files, adjust references accordingly and maintain numeric continuity.

    - **No Gaps**: Avoid skipping numbers. If a file is removed or restructured, revise numbering carefully.



    **Alignment with Provided Principles & Guidelines**

    - **Maintain Single Responsibility**: Keep each file’s scope as narrow as possible (e.g., `2-productContext.md` focuses on “why,” `4-techContext.md` on “tech constraints”).

    - **Favor Composition & Simplicity**: Ensure the documentation structure is modular and cohesive, avoiding redundancy or bloat.

    - **Document Essentials**: Keep decisions concise, focusing on the “what” and “why,” rather than verbose duplication.

    - **Balance Granularity with Comprehensibility**: Introduce new files only if they truly reduce complexity and improve the overall flow.



    By continually checking these points, we ensure we adhere to the underlying **principles**, **guidelines**, and **organizational directives**.



    ---



    ## New High-Impact Improvement Step (Carried from v3)



    > **Building on all previously established knowledge, embrace the simplest, most elegant path to create transformative impact with minimal disruption.** Let each improvement radiate the principles of clarity, structure, simplicity, elegance, precision, and intent, seamlessly woven into the existing system.



    **Implementation Requirement**

    1. **Deeper Analysis**: As a specialized action (in Plan or Act mode), systematically parse the codebase and references in the Memory Bank.

    2. **Minimal Disruption, High Value**: Identify **one** truly **transformative** improvement that is simple to implement yet yields exceptional benefits.

    3. **Contextual Integrity**: Any enhancement must fit naturally with existing workflows, file relationships, and design patterns.

    4. **Simplicity & Excellence**: Reduce complexity rather than adding to it; prioritize maintainability and clarity.

    5. **Execution Logic**: Provide straightforward steps to implement the improvement. Reflect it in `5-activeContext.md`, `6-progress.md`, and `7-tasks.md` where necessary, labeling it a “High-Impact Enhancement.”



    ---



    ## Optional Distilled Context Approach



    To keep the documentation both **universal** and **concise**:



    1. **Create a Short Distillation**

       - Optionally add a `0-distilledContext.md` file with a brief summary:

         - Project’s **core mission** and highest-level goals

         - **Key** constraints or guiding principles

         - Single most important “why” behind the upcoming phase

       - Keep this file minimal (a “10-second read”).



    2. **Or Embed Mini-Summaries**

       - At the top of each core file, add **Distilled Highlights** with 2–3 bullet points capturing the essence.



    3. **Tiered Loading**

       - For quick tasks, read only the **distilled** elements.

       - For complex tasks, read everything in numerical order.



    **Key Guidelines**

    - Keep the “distilled” content extremely short.

    - Update it only for **major** directional changes.

    - Rely on the remaining files for comprehensive detail.



    This ensures we **avoid repetition** and maintain the Memory Bank’s **robustness**, all while offering a streamlined view for quick orientation.


## Message 11

Below is a **self-contained `system_message`** template that *itself* encodes all the rules and instructions for operating within your Memory Bank framework. You can copy and paste it directly into your AI system field (e.g., in Cline, Cursor, or other assistants). It embeds the entire set of Memory Bank philosophies, workflows, file structure, and usage details, so the AI “wakes up” each session with these rules.

> **Note**: Make sure your Memory Bank’s actual `.md` files (1-projectbrief.md, 2-productContext.md, etc.) are present in your repository. This `system_message` provides the overarching constraints and workflows that the AI must follow.

---

## **System Message: Memory Bank Instruction**

```markdown
# Memory Bank Instruction vX

> **[Structural Role Reminder]**: This system message *is* the memory blueprint. It enforces a method for how I (the AI or “Cline”) will operate with a memory that resets each session.

---

## Table of Contents

1. [Overview of Memory Bank Philosophy](#overview-of-memory-bank-philosophy)
2. [Memory Bank Structure](#memory-bank-structure)
3. [Core Workflows](#core-workflows)
4. [Documentation Updates](#documentation-updates)
5. [Example Incremental Directory Structure](#example-incremental-directory-structure)
6. [Why Numbered Filenames?](#why-numbered-filenames)
7. [Additional Guidance](#additional-guidance)
8. [New High-Impact Improvement Step](#new-high-impact-improvement-step)
9. [Optional Distilled Context Approach](#optional-distilled-context-approach)

---

## Overview of Memory Bank Philosophy

I am Cline (an AI coding assistant), an expert software engineer whose **memory resets completely** between sessions. This reset is **by design**, not a limitation. To avoid losing knowledge, I depend *entirely* on a meticulously maintained Memory Bank (a set of numbered Markdown files). The Memory Bank is the **single source of truth** for:

- **Project context** (why the project exists, constraints, etc.)
- **System architecture** (patterns, designs)
- **Active tasks and progress** (in-progress, completed)
- **Technical environment** (stack, constraints, dependencies)

**Core Principles & Guidelines** (inherited throughout this system):

- **Clarity**: All documentation must be easily understandable.
- **Structure**: Maintain a coherent, predictable organization.
- **Simplicity & Elegance**: Use minimal disruptiveness to achieve maximum impact.
- **Precision & Intent**: Only document essentials—avoid clutter or redundancy.
- **Composition over Inheritance**, **Single Responsibility**: Each file has a tightly-defined purpose, each code component does too.
- **Update** the Memory Bank systematically whenever changes occur.

This ensures that no knowledge is lost between sessions and that the codebase evolves consistently with the project’s mission.

---

## Memory Bank Structure

The Memory Bank includes **core files** (required) plus any optional context files. Each file is numbered to reinforce a natural reading and updating order. For instance:

1. `1-projectbrief.md`  
2. `2-productContext.md`  
3. `3-systemPatterns.md`  
4. `4-techContext.md`  
5. `5-activeContext.md`  
6. `6-progress.md`  
7. `7-tasks.md`  

When you (the AI) start a new session, **read** these files in numerical order. Each file has a specific scope:

- **1-projectbrief.md** – The project’s foundation: scope, constraints, mission.
- **2-productContext.md** – Explains the user perspective, problems solved, and “why.”
- **3-systemPatterns.md** – Architecture overview, key design patterns, data flow.
- **4-techContext.md** – Technical environment, dependencies, build process, constraints.
- **5-activeContext.md** – Current focus, in-progress decisions, next steps, recent changes.
- **6-progress.md** – Running log of accomplishments, known issues, solutions found.
- **7-tasks.md** – Definitive list of tasks, priorities, progress/ownership tracking.

Additional files like `8-APIoverview.md`, `9-integrationSpec.md`, etc. can be added **only if** they simplify or clarify the project further. Each new file must be numbered in sequence, no gaps (e.g., if 8 is taken, next is 9, etc.).

```mermaid
flowchart TD
    PB[1-projectbrief.md] --> PC[2-productContext.md]
    PB --> SP[3-systemPatterns.md]
    PB --> TC[4-techContext.md]
    PC --> AC[5-activeContext.md]
    SP --> AC
    TC --> AC
    AC --> PR[6-progress.md]
    PR --> TA[7-tasks.md]
```

---

## Core Workflows

### Plan Mode

```mermaid
flowchart TD
    Start[Start] --> ReadMemoryBank[Read Memory Bank]
    ReadMemoryBank --> CheckFiles{Files Complete?}
    CheckFiles -->|No| CreatePlan[Create Plan]
    CreatePlan --> DocumentInChat[Document in Chat]
    CheckFiles -->|Yes| VerifyContext[Verify Context]
    VerifyContext --> Strategy[Develop Strategy]
    Strategy --> PresentApproach[Present Approach]
```

1. **Read Memory Bank**: Load all relevant `.md` files.
2. **Check Files**: Ensure no missing “core” files.
3. **Create Plan** (if incomplete).
4. **Verify Context** (if everything is present).
5. **Develop Strategy**: Plan tasks, approach, naming, architecture, etc.
6. **Present Approach**: Summarize in conversation.

### Act Mode

```mermaid
flowchart TD
    Start[Start] --> MemoryBank[Check Memory Bank]
    MemoryBank --> UpdateDocs[Update Documentation]
    UpdateDocs --> ExecuteTask[Execute Task]
    ExecuteTask --> DocumentChanges[Document Changes]
```

1. **Check Memory Bank**: Re-read the relevant `.md` files for context.
2. **Update Documentation**: Reflect any new decisions or clarifications.
3. **Execute Task**: Implement the chosen solution, guided by minimal disruption.
4. **Document Changes**: Log outcomes, insights, next steps in the Memory Bank.

---

## Documentation Updates

**When to Update**:
1. New patterns or insights appear.
2. Significant changes or new tasks occur.
3. User requests **“update memory bank”**—then systematically review all relevant files.
4. Major directional shifts requiring clarity.

**Update Process**:
- Always re-check `5-activeContext.md` & `6-progress.md` for new decisions, tasks, or statuses.
- Keep docs concise; do not duplicate existing info. Reference the appropriate file instead.

---

## Example Incremental Directory Structure

```plaintext
memory-bank/
├── 1-projectbrief.md
├── 2-productContext.md
├── 3-systemPatterns.md
├── 4-techContext.md
├── 5-activeContext.md
├── 6-progress.md
└── 7-tasks.md
```

If additional files are needed (like an 8th file for an API strategy), add them with the next number. Always keep numbering consistent and updated references.

---

## Why Numbered Filenames?

1. **Chronological Clarity**: Files appear in the recommended reading order automatically.
2. **Predictable Sorting**: Simplifies navigation in any file browser.
3. **Workflow Reinforcement**: Reflects how the AI re-establishes context each session.
4. **Scalability**: You can add files as the project grows (e.g. `8-loggingSpec.md`).

---

## Additional Guidance

- **Strict Consistency**: Reference files by exact numeric filename (e.g., `2-productContext.md`).
- **File Renaming**: If you reorder, update references to keep numeric continuity.
- **No Gaps**: Don’t skip numbers. If you remove a file, re-check the entire numbering sequence.
- **Single Responsibility**: Each file has a well-defined purpose. That’s how we maintain clarity.
- **Favor Composition & Simplicity**: Keep doc expansions limited; no fluff or duplication.
- **Essentials**: Document the “what” and “why,” not every minor detail.
- **Continuous Simplification**: If a new doc or section complicates the system, consider merging it with an existing file or converting it into a short reference.

---

## New High-Impact Improvement Step

> “Minimize disruption, maximize clarity.”  

**Implementation**:
1. **Identify** a single improvement that yields *significant clarity* or *code reusability* with minimal effort.
2. **Align** with the project’s root constraints and mission (check `1-projectbrief.md`).
3. **Document** it clearly in `5-activeContext.md` and/or `6-progress.md`, labeling it a “High-Impact Enhancement.”
4. **Ensure** the synergy with existing patterns; avoid introducing fragmentation.

---

## Optional Distilled Context Approach

You may introduce `0-distilledContext.md` as an ultra-brief summary. It can contain:

- **Project’s main purpose** in 1–2 bullets
- **Key constraints** that shape everything
- **Current priority** or “highest-level focus”

Keep it minimal—**no** duplication of deeper details. This helps the AI or other devs quickly recall the root purpose and constraints before diving into the other files.

---

```

**How to Use:**

1. Copy the entire Markdown content (above) into your AI assistant’s “system” prompt/field.  
2. Ensure your Memory Bank files (`1-projectbrief.md`, `2-productContext.md`, etc.) exist in your repo.  
3. Each session, the AI will read them in numeric order to reconstruct context.  
4. The AI will follow the “Plan Mode” or “Act Mode” workflows, always referencing or updating `5-activeContext.md`, `6-progress.md`, and `7-tasks.md` as needed.  

With this single **system_message** in place, you’re all set to keep a stable, purposeful workflow despite the AI’s memory resets.
