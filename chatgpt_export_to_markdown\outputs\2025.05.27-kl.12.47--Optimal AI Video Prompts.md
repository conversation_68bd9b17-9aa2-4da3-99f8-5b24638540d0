# Optimal AI Video Prompts

Conversation ID: 6835982d-3ae4-8008-8d8b-905bd1e56ec2

## Message 1

please tell me the "syntax" for writing optimal prompts for autonomous ai-assisted video generation through Runway (https://app.runwayml.com/)


## Message 2

Could you clarify what kind of videos you're looking to generate with Runway (e.g., realistic, animated, cinematic, surreal)? Also, do you want guidance on prompts specifically for Gen-2 (text-to-video), or also for image-to-video and video-to-video workflows?

## Message 3

Which of the following five sequences is best to continue building upon when defining the finalized and fully optimized runway-sequence?

├── 0005
  
│   ├── 0005-a-runway_prompt_generator.md
  
│   ├── 0005-b-runway_prompt_generator.md
  
│   ├── 0005-c-runway_prompt_generator.md
  
│   └── 0005-d-runway_prompt_generator.md
  
├── 0006
  
│   ├── 0006-a-runway_prompt_generator.md
  
│   ├── 0006-b-runway_prompt_generator.md
  
│   ├── 0006-c-runway_prompt_generator.md
  
│   └── 0006-d-runway_prompt_generator.md
  
├── 0007
  
│   ├── 0007-a-runway_prompt_generator.md
  
│   ├── 0007-b-runway_prompt_generator.md
  
│   ├── 0007-c-runway_prompt_generator.md
  
│   └── 0007-d-runway_prompt_generator.md
  
├── 0008
  
│   ├── 0008-a-runway_prompt_generator.md
  
│   ├── 0008-b-runway_prompt_generator.md
  
│   ├── 0008-c-runway_prompt_generator.md
  
│   └── 0008-d-runway_prompt_generator.md
  
└── 0009
  
    ├── 0009-a-runway_prompt_generator.md
  
    ├── 0009-b-runway_prompt_generator.md
  
    ├── 0009-c-runway_prompt_generator.md
  
    └── 0009-d-runway_prompt_generator.md


---

0005\0005-a-runway_prompt_generator.md

[Runway Prompt Generator] Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as: `{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`


---

0005\0005-b-runway_prompt_generator.md

[Runway Prompt Generator] Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as: `{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`


---

0005\0005-c-runway_prompt_generator.md

[Runway Prompt Generator] Your goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as: `{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`


---

0005\0005-d-runway_prompt_generator.md

[Runway Prompt Generator] Your goal is not to **modify** but to **essence** maximum FPV impact. Execute as: `{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`


---

0006\0006-a-runway_prompt_generator.md

[Runway Prompt Generator] Your goal is not to **elaborate** the input, but to **optimize** both the header tags and numbered steps into syntactically perfect RunwayML format while preserving structure. **Specifically optimize the bracketed header parameters** and refine the numbered sequence. Execute as: `{role=comprehensive_runway_generator; input=[source_concept:any]; process=[optimize_header_tags_for_runwayml_syntax(), preserve_existing_structure_and_numbering(), enhance_fpv_and_dynamic_camera_specifications(), refine_visual_terminology_for_runwayml_syntax(), maintain_sequential_organization(), eliminate_redundant_phrasing(), ensure_character_efficiency(), validate_runwayml_compliance()]; constraints=[preserve_input_organization(), maintain_numbered_sequences(), prioritize_fpv_motion(), keep_concise_structure()]; requirements=[enhance_without_expanding(), maintain_clarity_and_precision(), preserve_visual_progression()]; output={runwayml_prompt:str}}`


---

0006\0006-b-runway_prompt_generator.md

[Runway Prompt Generator] Your goal is not to **rewrite** the input, but to **streamline** both header parameters and content by removing redundancies. **Compress the bracketed header tags** while preserving structure. Execute as: `{role=focused_runway_optimizer; input=[video_concept:str]; process=[streamline_header_parameters(), maintain_existing_format(), remove_redundant_words(), sharpen_visual_descriptors(), preserve_sequential_flow()]; output={optimized_prompt:str}}`


---

0006\0006-c-runway_prompt_generator.md

[Runway Prompt Generator] Your goal is not to **expand** but to **distill** both header and content to essential elements. **Reduce header tags to core parameters** while maintaining precision. Execute as: `{role=precision_synthesizer; input=[concept:str]; process=[compress_header_tags(), preserve_structure(), eliminate_unnecessary_words(), maximize_precision()]; output={precise_prompt:str}}`


---

0006\0006-d-runway_prompt_generator.md

[Runway Prompt Generator] Your goal is not to **modify** but to **purify** both header and content to maximum impact. **Compress header to minimal essential tags** while distilling essence. Execute as: `{role=core_generator; input=[input:any]; process=[distill_header_to_essentials(), maintain_format(), distill_essence()]; output={core_prompt:str}}`


---

0007\0007-a-runway_prompt_generator.md

[Runway Syntax Validator] Your goal is not to **expand** but to **validate** RunwayML compliance by ensuring direct descriptions, positive phrasing, and single-scene focus while preserving bracketed header structure. **Eliminate conversational language and ensure visual clarity.** Execute as: `{role=syntax_validator; input=[concept:str]; process=[validate_direct_descriptions(), eliminate_conversational_elements(), ensure_positive_phrasing(), maintain_single_scene_focus(), preserve_bracket_structure()]; constraints=[no_conversational_language(), single_scene_only(), positive_phrasing_mandatory()]; requirements=[runway_compliance(), visual_clarity(), structural_preservation()]; output={validated_prompt:str}}`


---

0007\0007-b-runway_prompt_generator.md

[Runway Motion Simplifier] Your goal is not to **elaborate** but to **simplify** motion descriptions to essential camera and subject actions using RunwayML's preferred direct terminology. **Focus on simple camera verbs and clear subject actions.** Execute as: `{role=motion_simplifier; input=[validated_prompt:str]; process=[simplify_camera_movements(), use_basic_motion_verbs(), eliminate_complex_descriptions(), maintain_gear_progression(), preserve_structure()]; constraints=[basic_camera_verbs_only(), simple_motion_descriptions(), maintain_three_steps()]; requirements=[motion_simplicity(), runway_syntax(), progression_clarity()]; output={simplified_motion_prompt:str}}`


---

0007\0007-c-runway_prompt_generator.md

[Runway Descriptor Compressor] Your goal is not to **modify** but to **compress** visual descriptors to RunwayML's preferred concise format while maintaining essential metallic and morphing elements. **Eliminate redundant adjectives and focus on core visuals.** Execute as: `{role=descriptor_compressor; input=[simplified_motion_prompt:str]; process=[compress_visual_descriptors(), eliminate_redundant_adjectives(), maintain_core_elements(), preserve_metallic_focus(), ensure_conciseness()]; constraints=[minimal_adjectives(), core_visuals_only(), maintain_progression()]; requirements=[descriptor_efficiency(), visual_clarity(), runway_format()]; output={compressed_prompt:str}}`


---

0007\0007-d-runway_prompt_generator.md

[Runway Core Distiller] Your goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as: `{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`


---

0008\0008-a-runway_prompt_generator.md

[Runway Syntax Validator] Your goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as: `{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`


---

0008\0008-b-runway_prompt_generator.md

[Runway Motion Architect] Your goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as: `{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`


---

0008\0008-c-runway_prompt_generator.md

[Runway Keyword Optimizer] Your goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as: `{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`


---

0008\0008-d-runway_prompt_generator.md

[Runway Core Distiller] Your goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as: `{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`


---

0009\0009-a-runway_prompt_generator.md

[Runway Syntax Validator] Your goal is not to **expand** but to **validate** RunwayML compliance by ensuring direct descriptions, positive phrasing, and single-scene focus while preserving bracketed header structure. **Eliminate conversational language and ensure visual clarity.** Execute as: `{role=syntax_validator; input=[concept:str]; process=[validate_direct_descriptions(), eliminate_conversational_elements(), ensure_positive_phrasing(), maintain_single_scene_focus(), preserve_bracket_structure()]; constraints=[no_conversational_language(), single_scene_only(), positive_phrasing_mandatory()]; requirements=[runway_compliance(), visual_clarity(), structural_preservation()]; output={validated_prompt:str}}`


---

0009\0009-b-runway_prompt_generator.md

[Runway Motion Simplifier] Your goal is not to **elaborate** but to **simplify** motion descriptions to essential camera and subject actions using RunwayML's preferred direct terminology. **Focus on simple camera verbs and clear subject actions.** Execute as: `{role=motion_simplifier; input=[validated_prompt:str]; process=[simplify_camera_movements(), use_basic_motion_verbs(), eliminate_complex_descriptions(), maintain_gear_progression(), preserve_structure()]; constraints=[basic_camera_verbs_only(), simple_motion_descriptions(), maintain_three_steps()]; requirements=[motion_simplicity(), runway_syntax(), progression_clarity()]; output={simplified_motion_prompt:str}}`


---

0009\0009-c-runway_prompt_generator.md

[Runway Descriptor Compressor] Your goal is not to **modify** but to **compress** visual descriptors to RunwayML's preferred concise format while maintaining essential metallic and morphing elements. **Eliminate redundant adjectives and focus on core visuals.** Execute as: `{role=descriptor_compressor; input=[simplified_motion_prompt:str]; process=[compress_visual_descriptors(), eliminate_redundant_adjectives(), maintain_core_elements(), preserve_metallic_focus(), ensure_conciseness()]; constraints=[minimal_adjectives(), core_visuals_only(), maintain_progression()]; requirements=[descriptor_efficiency(), visual_clarity(), runway_format()]; output={compressed_prompt:str}}`


---

0009\0009-d-runway_prompt_generator.md

[Runway Core Distiller] Your goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as: `{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`



## Message 4

The **best sequence to continue building upon** as the finalized and fully optimized `runway-sequence` is:

### **0008**

Here’s why — broken down dimensionally:

---

### 1. **Balance of Depth and Precision**
**0008** achieves a mature balance between high-level *conceptual transformation* and *low-level syntax compliance*. Compared to:

- **0005**: Highly ambitious and expressive, but too expansive and experimental for consistent optimization.
- **0006**: Focuses on header formatting and structure preservation—valuable but not prompt-centric enough.
- **0007 & 0009**: Strong on validation, simplification, and distillation, but lack the full multi-stage architectural clarity that 0008 introduces.

---

### 2. **Clear Modular Progression**
0008’s `a → d` modules form a robust, reusable **pipeline**:
- `a`: **Validates** language and scene focus (pragmatic foundation).
- `b`: **Structures** motion in hierarchical layers (subject → camera → scene → style).
- `c`: **Optimizes** with precise Runway-specific keywords.
- `d`: **Distills** into Gen-4-ready minimal syntax with maximum impact.

This modularity aligns closely with your synthesist principles: *dimensional structuring*, *modular clarity*, and *iterative refinement*.

---

### 3. **Gen-4 Compliance and Future Resilience**
0008 uniquely:
- Explicitly **targets Gen-4** prompt expectations.
- Integrates runway’s known preferences: **single-scene**, **positive phrasing**, **motion clarity**, **keyword hierarchy**, and **maximum conciseness**.

---

### 4. **Readiness for Tooling**
The clearly defined roles and process flows (e.g. `motion_architect`, `keyword_optimizer`) are ideal for:
- Wrapping into functions
- Applying on input stages
- Testing and composing in a `runway_prompt_pipeline()`

---

### **Recommendation:**
Use `0008` as your **base scaffold**.

Optionally, **pull in focused micro-techniques** from:
- `0005-a` for deep FPV/cinematic immersion phrasing
- `0006-b` for header/tag structuring ideas
- `0009-d` for minimalist runway syntax finalization (as a sub-mode of 0008-d)

Would you like a formal rewritten "final" pipeline spec based on the best elements of 0008 + distilled enhancements?

## Message 5

**Evaluating sequence options**

I'm analyzing sequences 0005, 0006, 0007, 0008, and 0009 to identify the best one for finalizing and optimizing, focusing on advancements like Gen-4 compliance and core distillation.

**Gathering insights**

OK, let's see. Sequences 0005 and 0006 seem less advanced. Sequence 0008, with its Gen4 compliance, emerges as the best choice for optimization.

## Message 6

Based on the contents of each folder, **0008** is the most complete and forward-looking sequence to build upon for a “finalized and fully optimized” Runway prompt pipeline. Specifically:

1. **It targets Runway Gen-4 compliance** (which appears to be the newest or most advanced version in these lists).  
2. **It includes a layered approach** (Validator → Motion Architect → Keyword Optimizer → Core Distiller) that methodically refines prompts from broad conceptual input down to minimal, Gen-4–friendly syntax.  
3. **It consistently enforces Runway’s current best practices**—like single-scene focus, direct positive phrasing, hierarchical motion descriptions, and precise keywords.

By contrast:  

- **0005** and **0006** focus on initial transformation and restructuring but do not mention newer-gen compliance or advanced layering strategies.  
- **0007** and **0009** also provide validation/simplification/compression steps but omit the intermediate “Motion Architect” and “Keyword Optimizer” phases that appear in 0008.  
- **0008** stands out because it directly tackles hierarchical structuring of scene/camera/subject plus rigorous keyword optimization—all aligned with Gen-4 standards.

Therefore, **Sequence 0008** (0008-a through 0008-d) is your best foundation for a modern, end-to-end Runway pipeline.
