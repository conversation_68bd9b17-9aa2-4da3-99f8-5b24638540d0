# Good CursorRules Entries

Conversation ID: 67d6dc3c-d2c0-8008-823d-853c083c2367

## Message 1

Based on the gathered data provided below, what would be some fundamentally good entries to add to `cursorrules` ?



```

    ## How to Navigate the Codebase Safely Using These Tools



    | Step | Purpose                            | Commands                                            | Description                                                         |

    |------|------------------------------------|-----------------------------------------------------|---------------------------------------------------------------------|

    | 1    | Initial Orientation            | `npm run deps:all`                                  | Generates comprehensive visualizations of the entire codebase structure to understand component relationships and data flow. |

    | 2    | Component-Specific Understanding| `npm run deps:focus`                                | Visualizes dependencies for specific components or features.        |

    | 3    | Pre-Modification Safety Check  | `npm run deps:analyze:jsx`<br>`npm run verify-component-usage` | Checks component usage, including JSX usage patterns that aren't obvious from imports. |

    | 4    | Post-Modification Verification | `npm run deps:audit`                                | Audits the codebase for new issues like circular dependencies or broken relationships post-modification. |

    | 5    | Identifying Safe Cleanup Opportunities | `npm run deps:unused:jsx`<br>`npm run deps:cleanup:dry-run` | Identifies unused components considering JSX and simulates safe cleanup. |

    | 6    | Fixing Import Issues           | `npm run fix-hook-imports`<br>`npm run update-imports`       | Fixes common import-related issues, especially with React hooks and component relocations. |



    ## The Core Architecture: AST → JSON → Visualization Workflow



    | Step | Purpose                        | Commands / Files                                            | Description                                                                     |

    |------|--------------------------------|--------------------------------------------------------------|---------------------------------------------------------------------------------|

    | 1    | AST Extraction             | `.dependency-cruiser.cjs`<br>`depcruise-config.cjs`         | Dependency Cruiser parses TypeScript/React code into Abstract Syntax Trees (ASTs).|

    | 2    | JSON Transformation        | `dependency-data.json`                                      | ASTs are transformed into structured JSON and enriched with component metadata.  |

    | 3    | Visualization Generation   | `create-d3-graph.js`<br>`create-flow-diagram.js`           | Converts structured JSON data into interactive visual representations.           |



    ## Dependency Visualization Ecosystem



    | Step | Purpose                          | Commands                                                                                   | Description                                                                                                  |

    |------|----------------------------------|--------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------|

    | 1    | High-Level Architecture Visualization | `npm run deps:all`<br>`npm run depcruise:archi`                                            | Generates comprehensive visualizations of the entire system architecture, revealing interactions between features, pages, and components. |

    | 2    | Component Relationship Tools | `npm run deps:d3`<br>`npm run deps:flow`                                                   | Focuses on component interconnections with enhanced JSX detection, highlighting usage even without explicit imports. |

    | 3    | Safety Systems               | `npm run deps:analyze:jsx`<br>`npm run verify-component-usage`<br>`npm run deps:unused:jsx` | Verifies component usage and proactively identifies potential issues to ensure safe modifications.           |

    | 4    | Maintenance Tooling          | `npm run deps:cleanup:safe`<br>`npm run fix-hook-imports`                                  | Maintains codebase health by safely removing unused code and addressing common import-related issues.        |



    ## Mastering Safe Navigation Through the Codebase



    | Phase | Purpose                       | Commands                                        | Description                                                                                      |

    |-------|-------------------------------|-------------------------------------------------|--------------------------------------------------------------------------------------------------|

    | 1     | Orientation and Understanding | `npm run deps:all`                              | Generates comprehensive visualizations to understand overall architecture and component relationships, especially seasonal adaptations. |

    | 2     | Target Analysis           | `npm run deps:focus`                            | Focuses on specific components or features, revealing incoming and outgoing dependencies to ensure safe modifications. |

    | 3     | JSX Usage Verification    | `npm run deps:analyze:jsx`<br>`npm run verify-component-usage` | Verifies component usage, including both direct imports and implicit JSX usage patterns.         |

    | 4     | Post-Modification Verification | `npm run deps:audit`                            | Audits changes to detect issues like circular dependencies or broken component relationships.    |

    | 5     | Cleanup                   | `npm run deps:unused:jsx`<br>`npm run deps:cleanup:dry-run` | Identifies and safely simulates removal of truly unused components considering JSX usage.        |



    ## Leveraging Advanced Features for Complex Modifications



    | Step | Purpose                           | Commands                   | Description                                                                |

    |------|-----------------------------------|----------------------------|----------------------------------------------------------------------------|

    | 1    | Seasonal Feature Development  | `npm run deps:dates`       | Visualizes component changes based on seasonal variations.                 |

    | 2    | Component Relocation          | `npm run update-imports`   | Updates all imports automatically after restructuring component hierarchy. |

    | 3    | React Hook Modifications      | `npm run fix-hook-imports` | Ensures dependency arrays are correctly maintained when modifying hooks like `useData`. |

    | 4    | Performance Optimization      | `npm run deps:js`          | Identifies performance bottlenecks by visualizing JavaScript execution paths. |

```

## Message 2

Here are fundamentally good entries for `cursorrules`, structured modularly and hierarchically for clarity and safe navigation within your project:

### Foundational Rules (Core Safety & Sanity Checks)
- **Run comprehensive dependency visualization before structural changes.**
  ```
  npm run deps:all
  ```
- **Verify targeted component dependencies prior to modification.**
  ```
  npm run deps:focus
  ```
- **Audit for unintended circular dependencies post-modification.**
  ```
  npm run deps:audit
  ```
- **Ensure correct JSX dependency recognition (implicit and explicit).**
  ```
  npm run deps:analyze:jsx
  npm run verify-component-usage
  ```

### Maintenance & Cleanup Rules
- **Identify genuinely unused components before cleanup.**
  ```
  npm run deps:unused:jsx
  ```
- **Simulate safe component cleanup.**
  ```
  npm run deps:cleanup:dry-run
  ```
- **Perform safe component cleanup after simulation.**
  ```
  npm run deps:cleanup:safe
  ```

### Automated Fixes & Import Management
- **Automatically update component imports post-relocation or restructuring.**
  ```
  npm run update-imports
  ```
- **Automatically fix common React hook import issues.**
  ```
  npm run fix-hook-imports
  ```

### Advanced Analysis & Optimization
- **Visualize seasonal feature or variant-specific dependencies.**
  ```
  npm run deps:dates
  ```
- **Identify JavaScript performance bottlenecks.**
  ```
  npm run deps:js
  ```
- **Enhanced visualization of component relationships (D3 & Flow diagrams).**
  ```
  npm run deps:d3
  npm run deps:flow
  ```

These structured entries ensure safe navigation, provide clarity, and enable proactive maintenance and optimization across your project's lifecycle.
