# RigOfficeDownloader Overview

Conversation ID: 682ae828-5708-8008-8d1b-2bf523d9c7ee

## Message 1

# CONTEXT



RigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).



# SOLUTION



i've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__. you will understand exactly why i mean by this as you start understanding the fundamental concepts (especially with regards to the workflow itself) utilized in the utility, it's **extremely adaptable** (the workflow/concepts/methods for how it works is *meticiously crafted*; there's *a reason for everything*).



# CONSIDERATIONS



since our company use internal github repos for all development we do this utility will also be uploaded to github's company page (accessible for everyone within the company), i want to use this as an opportunity to impress (with unique and original simplicity and elegance). although 99.99% of developers in similar situations will choose to adhere to what they perceive as the concencus best practices and restructure (organize and refactor) the utility's codebase/project/filestructure based on these "rules", **i no longer blindly adhere to established concepts**, i *build* on them through *inherent understanding*. as an example, best practices are *great* for large and complex systems, and in some cases even **neccessary** - but such standardized practices does *not apply to small utilities the same way*, small utilities can often be transformed from a working utility within a single file of <500 lines, and after transforming it to "established best practices" will transform it into ~30 files and thousands of lines of code; __I WANT TO DO THE OPPOSITE OF THIS__. i want **balance** and **cognizeant and inherent understanding**, i want to leverage my own artistic vision onto the code. i find reduction of complexity through perpetually evaluation everything through different contexts through simulating (internally within my mind) different visual representations of components/sections/files in relation to each other.



# FILESTRUCTURE



this is the current filestructure:

```

RigOfficeDownloader

├── .cmd

│   ├── py_venv_pip_install.bat

│   ├── py_venv_run_script.bat

│   ├── py_venv_terminal.bat

│   ├── py_venv_upgrade_requirements.bat

│   └── py_venv_write_requirements.bat

├── memory-bank

│   ├── 01_foundation.md

│   ├── 02_context.md

│   ├── 03_patterns.md

│   ├── 04_tech.md

│   ├── 05_activity.md

│   ├── 06_progress.md

│   ├── 07_tasks.md

│   ├── 08_objective.md

│   └── memory-bank.md

├── outputs

│   ├── data

│   │   ├── R5385.010-a-docs.json

│   │   ├── R5385.010-a-docs.md

│   │   ├── R5385.010-b-files.json

│   │   └── R5385.010-b-files.md

│   └── downloads

│       └── R5385.dirtree.md

├── .gitignore

├── RigOfficeDownloader-v1.bat

├── RigOfficeDownloader-v1.py

├── RigOfficeDownloader-v2.bat

├── RigOfficeDownloader-v2.py

├── RigOfficeDownloader-v3.bat

├── RigOfficeDownloader-v3.py

├── RigOfficeDownloader-v4.bat

├── RigOfficeDownloader-v4.py

├── RigOfficeDownloader.sublime-project

├── notes.md

├── py_venv_init.bat

└── requirements.txt

```



# OBJECTIVE



start by writing a short `README.md` **IN LESS THAN <50 LINES OF CODE** based on all parameters inherently defined within this message, and based on the current memory-bank (provided below). the memory-bank is automatically generated and is therefore extremely (unneccessarily) verbose, so don't try to extract *everything* from it; instead transform subsets into (gradually more) consolidated (and condensed) ESSENTIALS.



## Memorybank



    ### RigOfficeDownloader



    #### Memorybank File Structure



    ```

    ├── 01_foundation.md

    ├── 02_context.md

    ├── 03_patterns.md

    ├── 04_tech.md

    ├── 05_activity.md

    ├── 06_progress.md

    ├── 07_tasks.md

    └── 08_objective.md

    ```



    ---



    ##### `01_foundation.md`



    ```markdown

        ## Distilled Highlights

        - RigOfficeDownloader automates document retrieval from NOV's RigDoc system

        - Three-step workflow: documents → files → downloads

        - Designed to save engineering time during document gathering phase

        - Primary purpose is to streamline 3D modeling preparation workflow



        # 01_foundation.md

        ## Core Mission



        RigOfficeDownloader exists to eliminate the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigOffice system. The mission is to free engineers from repetitive document retrieval tasks, allowing them to focus on their primary responsibility: high-quality 3D modeling.



        ## Primary Intent



        Engineering productivity is severely constrained by poorly functioning document retrieval systems that require excessive manual navigation and intervention. By automating this process, we can dramatically reduce the preparation phase for 3D modeling work, enabling faster project delivery without compromising quality.



        ## Project Identity



        This utility is characterized by:

        - **Pragmatic automation** that focuses on solving a single problem extremely well

        - **Progressive workflow** that divides complex tasks into manageable stages with human review points

        - **Preservation of context** by maintaining consistent naming and organizational patterns

        - **Prioritization of reliability** over speed, ensuring accurate document retrieval



        These guiding principles shape all design and implementation decisions in the project.

    ```



    ---



    ##### `02_context.md`



    ```markdown

        ## Distilled Highlights

        - Engineers waste significant time navigating poorly designed document retrieval systems

        - NOV's RigDoc system requires extensive manual interaction and repetitive tasks

        - Document retrieval is a prerequisite for the actual 3D modeling work

        - Target users are simulation engineers working on oil rig models



        # 02_context.md

        ## Problem Space



        3D simulation engineers working on oil rig visualizations face a significant challenge before they can begin their actual modeling work: gathering the necessary technical documentation. The NOV RigDoc system (https://rigdoc.nov.com) serves as the central repository for this documentation but presents several workflow challenges:



        1. **Fragmented Search Process**: Engineers must construct complex search queries to locate relevant documents

        2. **Manual Review Overhead**: Each search result requires manual review to determine relevance

        3. **Multi-Step Document Retrieval**: Accessing the actual files requires navigating through multiple interfaces

        4. **Inconsistent Naming Conventions**: Documents and files have inconsistent naming patterns

        5. **Inefficient Organization**: Downloaded files lack a clear organizational structure



        The time spent navigating these challenges can often exceed the time required for the actual 3D modeling work, creating a significant bottleneck in the overall workflow.



        ## Stakeholders



        - **Primary Users**: 3D simulation engineers responsible for creating accurate rig models

        - **Project Managers**: Team leaders who need to allocate resources efficiently

        - **NOV Documentation System**: The external service providing the technical documentation

        - **End Clients**: Those who will ultimately use the 3D simulations for training or analysis



        ## User Needs



        3D engineers require:

        1. **Efficient Document Discovery**: The ability to quickly locate relevant technical drawings

        2. **Selective Retrieval**: Options to choose specific documents rather than downloading everything

        3. **Contextual Organization**: Downloaded files organized in a way that preserves their relationships

        4. **Progress Visibility**: Clear feedback on lengthy operations

        5. **Workflow Control**: The ability to pause, review, and continue the process as needed



        ## Environmental Constraints



        - **Authentication Requirements**: Access to RigDoc requires proper credentials

        - **Session Timeouts**: Browser sessions expire after periods of inactivity

        - **Download Limitations**: The system may have rate limits or connection constraints

        - **File Format Variability**: Documents may be available in multiple formats (PDF, DWG, etc.)

        - **Network Dependencies**: The process requires stable internet connectivity



        ## Success Criteria



        The solution will be considered successful if it:

        1. **Reduces Documentation Gathering Time**: Cuts the time required by at least 75%

        2. **Minimizes Manual Intervention**: Automates the most repetitive aspects of the process

        3. **Maintains Engineer Control**: Allows for human judgment at key decision points

        4. **Organizes Results Logically**: Creates a clear structure for downloaded materials

        5. **Handles Edge Cases Gracefully**: Manages errors, timeouts, and unusual document formats

    ```



    ---



    ##### `03_patterns.md`



    ```markdown

        ## Distilled Highlights

        - Three-stage workflow: document retrieval → file metadata → selective download

        - User-editable Markdown interfaces between stages for review and selection

        - JSON storage with consistent structure and field ordering

        - Intelligent naming pattern with subfolder support for downloaded files



        # 03_patterns.md

        ## System Design



        RigOfficeDownloader follows a **linear multi-stage pipeline** architecture with human checkpoints between stages. This design prioritizes reliability, user control, and an incremental approach to data processing.



        ### Core Architectural Pattern



        The system operates through a three-stage workflow:



        ```mermaid

        flowchart TD

            A[Document Metadata Retrieval] --> B[Markdown Export]

            B --> C[User Selection]

            C --> D[Document Import]

            D --> E[File Metadata Retrieval]

            E --> F[Markdown Export]

            F --> G[User Selection]

            G --> H[File Import]

            H --> I[File Download]

            I --> J[Organized Storage]

        ```



        Each stage:

        1. **Collects necessary data** through web scraping

        2. **Transforms the data** into a human-readable format

        3. **Allows for user interaction** to make selection decisions

        4. **Processes the results** based on user choices



        This separation of concerns enables:

        - Independent testing and verification of each processing step

        - The ability to re-run specific stages without repeating the entire process

        - Clear progress tracking and resumption capabilities



        ## Data Representation



        The system employs a consistent data representation pattern across all stages:



        ### JSON as Internal Storage



        ```mermaid

        flowchart TD

            A[Web Scraping] --> B[JSON Documents]

            B --> C[JSON Files]

            C --> D[Downloaded Files]

        ```



        All metadata uses JSON with consistent field schemas:



        1. **Document Schema**:

           - `item_type`: Always "doc"

           - `item_title`: Document title

           - `item_drawing_type`: Drawing type classification

           - `item_include`: Boolean flag for inclusion in the file retrieval stage

           - `item_generated_name`: Constructed name for organization



        2. **File Schema**:

           - `item_type`: Always "file"

           - `item_download`: Boolean flag for download selection

           - `item_generated_name`: Inherits from document with additional identifiers

           - `item_file_ext`: File extension for type identification



        ### Markdown as User Interface



        The system converts JSON to Markdown tables for user editing, providing:

        - A familiar, readable format for reviewing data

        - Easy editing of inclusion/download flags

        - Consistent visualization of metadata

        - Preservation of all fields in a structured layout



        ## Naming and Organization Pattern



        A key pattern is the **hierarchical generated naming** system:



        ```

        <DocNumber>_REV<Revision>--<CaseNumber>-<DocTitle>.<FileIdentifier>.<FileTitle>

        ```



        This pattern:

        - Embeds critical metadata in the filename

        - Supports hierarchical organization through slash (/) characters

        - Maintains consistency between source documents and files

        - Automatically sanitizes invalid characters for filesystem compatibility



        ## Filter Chain Pattern



        The system implements a **sequential filter chain** for intelligent automation:



        ```mermaid

        flowchart LR

            A[Input Data] --> B[Filter 1]

            B --> C[Filter 2]

            C --> D[Filter 3]

            D --> E[Filter N]

            E --> F[Processed Data]

        ```



        Filters are:

        - **Sequentially applied** with later filters potentially overriding earlier ones

        - **Pattern-based** using glob-style syntax

        - **Type-specific** targeting either documents or files

        - **Field-targeted** applying to specific metadata fields

        - **Toggle-capable** with enable/disable functionality



        This approach balances automation with flexibility for special cases.



        ## Browser Interaction Pattern



        The system employs a consistent pattern for browser automation:



        1. **Session Context Management**: Using contextmanager for cleanup

        2. **Smart Waiting Strategy**: Progressive scrolling with dynamic waits

        3. **Deduplication**: Ensuring unique downloads and metadata

        4. **Progress Feedback**: Visibility for long-running operations

    ```



    ---



    ##### `04_tech.md`



    ```markdown

        ## Distilled Highlights

        - Python-based implementation with Selenium web automation

        - Modular architecture with clear separation of concerns

        - JSON and Markdown for data storage and user interaction

        - Browser automation with smart waiting strategies and error handling



        # 04_tech.md

        ## Technology Stack



        RigOfficeDownloader is built with a focused set of technologies to balance reliability, maintainability, and functionality:



        ### Core Technologies



        - **Python**: Primary implementation language

        - **Selenium**: Web automation framework

        - **BeautifulSoup**: HTML parsing and extraction

        - **ChromeDriver**: Browser control interface

        - **JSON**: Data storage format

        - **Markdown**: User interaction format



        ### Key Dependencies



        ```

        selenium         # Browser automation

        beautifulsoup4   # HTML parsing

        webdriver_manager # WebDriver installation automation

        colorama         # Terminal color support

        ansimarkup       # Enhanced terminal formatting

        dateutil         # Date parsing and formatting

        ```



        ## Implementation Architecture



        The system follows a modular implementation pattern with the following components:



        ### RigDocScraper Class



        The central class implementing the scraping workflow with methods for:

        - `fetch_docs()`: Document metadata retrieval

        - `fetch_files()`: File metadata retrieval

        - `download_files()`: File downloading with smart naming



        ### Browser Session Management



        ```python

        @contextmanager

        def browser_session(self, download_mode=False):

            # Setup browser with appropriate configuration

            try:

                # Initialize browser with options

                yield self.driver

            finally:

                # Clean up resources

                if self.driver:

                    self.driver.quit()

        ```



        This pattern ensures proper cleanup of browser resources regardless of execution path.



        ### Smart Waiting Strategy



        The system implements a progressive waiting approach:



        ```python

        class SleepCondition:

            # Callable class for WebDriverWait that sleeps for specified seconds then returns True

            def __init__(self, s):

                self.s = s

            def __call__(self, _):

                time.sleep(self.s)

                return True

        ```



        Combined with explicit conditions:



        ```python

        WebDriverWait(driver, max_wait).until(

            EC.presence_of_element_located((By.CSS_SELECTOR, "a.search-result-link"))

        )

        ```



        This ensures reliable extraction even with variable page load times.



        ### Data Transformation Pipeline



        Data flows through a consistent transformation pipeline:

        1. **Web Extraction**: Selenium + BeautifulSoup scraping

        2. **Data Structuring**: Consistent field mapping

        3. **Storage**: JSON persistence

        4. **Transformation**: JSON to Markdown conversion

        5. **User Interaction**: Markdown editing

        6. **Reversal**: Markdown to JSON conversion

        7. **Processing**: Filtered downloading based on flags



        ### File Organization System



        Downloads are managed through a structured file system:



        ```

        outputs/

        ├── data/

        │   ├── <rig>-a-docs.json   # Document metadata

        │   ├── <rig>-a-docs.md     # User-editable document selection

        │   ├── <rig>-b-files.json  # File metadata

        │   └── <rig>-b-files.md    # User-editable file selection

        └── downloads/

            └── <rig>/              # Downloaded files with subfolder organization

        ```



        ### Sanitization and Path Management



        File paths and names are sanitized with a robust approach:



        ```python

        def sanitize_filename(s):

            # Clean string for valid filename: replace invalid chars, preserve paths

            replacements = {

                "'": "", '"': "", '`': "",  # Remove quotes and backticks

                '\\': "_", '|': "_", '?': "_", '*': "_", '<': "_", '>': "_", ':': "_"  # Replace with underscore

            }



            # Apply replacements and clean up

            result = s

            for char, replacement in replacements.items():

                result = result.replace(char, replacement)



            result = ''.join(c for c in result if c.isprintable() or c == '/')  # Keep printable chars and path separators

            result = re.sub(r'\s+', ' ', result)  # Normalize spaces



            return result.strip()

        ```



        This allows for:

        - Safe filesystem operations

        - Preservation of subfolder structure

        - Consistent naming patterns



        ## Configuration System



        The system uses a structured configuration approach:



        ```python

        CONFIG = {

            "rig_number": f"{PROJECT_RIG_ID}.020",

            "search_urls": PROJECTINFO_GAD,

            "show_progress": True,

            "filters": [

                # Sequential filter definitions

            ]

        }

        ```



        This enables:

        - Easy modification of search parameters

        - Fine-tuning of filter chains

        - Toggle-based control of functionality

        - Project-specific customization



        ## Technical Constraints



        - **Browser Compatibility**: Requires Chrome browser

        - **Network Access**: Must have connectivity to rigdoc.nov.com

        - **Authentication**: User must handle authentication through browser session

        - **File System Access**: Requires write permissions for outputs directory

        - **Memory Consumption**: Can handle large document sets with incremental processing

    ```



    ---



    ##### `05_activity.md`



    ```markdown

        ## Distilled Highlights

        - Currently working on v4 of RigOfficeDownloader with enhanced file organization

        - Implementing subfolder support via path separators in generated names

        - Improved configuration system with filter chains

        - Working on progress visibility for lengthy operations



        # 05_activity.md

        ## Current Focus



        The development team is currently focused on enhancing RigOfficeDownloader with several key improvements:



        ### Version 4 Implementation



        Version 4 represents a significant maturation of the tool with emphasis on:



        1. **Enhanced File Organization**

           - Implementing subfolder support through path separator handling

           - Improving the item_generated_name pattern for more consistent file organization

           - Ensuring proper sanitization of filenames while preserving path structure



        2. **Smarter Configuration System**

           - Refining the filter chain mechanism for more precise document/file selection

           - Supporting pattern-based filtering with glob syntax

           - Enabling field-specific matching for greater flexibility



        3. **User Experience Improvements**

           - Enhancing progress visibility during long-running operations

           - Providing clearer feedback on the staging process

           - Making the interactive menu more intuitive



        ## Recent Decisions



        The team has made several key decisions that are shaping current development:



        1. **Single-File Approach**

           - Maintaining the utility as a single Python file for simplicity

           - Ensuring all functionality is self-contained for easy deployment

           - Using modular internal design for maintainability



        2. **Three-Stage Workflow**

           - Confirming the document → file → download workflow as optimal

           - Reinforcing user decision points between stages

           - Enhancing the JSON ↔ Markdown conversion for better user interaction



        3. **Progressive Enhancement**

           - Adding features incrementally without disrupting core functionality

           - Prioritizing stability and reliability over feature expansion

           - Focusing on refinements that enhance the engineer's workflow



        ## Technical Hypotheses



        The team is currently exploring several technical approaches:



        1. **Smart Waiting Strategy**

           - Hypothesis: Combining explicit waits with scrolling will improve scraping reliability

           - Implementation: Using WebDriverWait with both sleep conditions and element presence checks

           - Testing: Comparing success rates across different document types and quantities



        2. **Configurable Field Ordering**

           - Hypothesis: Customizable field order will improve user experience

           - Implementation: Separate field order definitions for documents and files

           - Testing: User feedback on Markdown table readability and editing experience



        3. **Deduplication Approach**

           - Hypothesis: Priority-based deduplication will handle conflicting data appropriately

           - Implementation: Using hashable key generation with exclusion lists

           - Testing: Verifying correct handling of duplicates with different attribute values



        ## Ongoing Discussions



        Current topics under active discussion include:



        1. **Session Management**

           - How to handle authentication when sessions expire

           - Options for persisting cookies between runs

           - Balancing security with convenience



        2. **Error Recovery**

           - Strategies for handling network interruptions

           - Approaches to resuming interrupted downloads

           - Error reporting format and detail level



        3. **Filter Chain Refinement**

           - Making filter creation more user-friendly

           - Potential for saving and loading filter configurations

           - Visualization of filter effects before application

    ```



    ---



    ##### `06_progress.md`



    ```markdown

        ## Distilled Highlights

        - Version 4 is the current active build with enhanced organization features

        - Subfolder support successfully implemented for downloaded files

        - Filter chain mechanism functioning with pattern-based selection

        - Interactive configuration menu implemented for runtime adjustments



        # 06_progress.md

        ## State of the Build



        The RigOfficeDownloader utility has evolved through several versions, with each iteration bringing focused improvements:



        ### Version History



        | Version | Status | Key Features |

        |---------|--------|-------------|

        | v1 | Complete | Basic document retrieval and download |

        | v2 | Complete | JSON/Markdown conversion and user selection |

        | v3 | Complete | Improved error handling and field organization |

        | v4 | Active | Subfolder support, filter chains, field ordering |



        ### Current Version Capabilities



        Version 4 represents the current stable build with several key capabilities:



        1. **Complete Document Retrieval Pipeline**

           - ‚úÖ Automated document metadata scraping from RigDoc

           - ‚úÖ Structured JSON storage with consistent field schemas

           - ‚úÖ Markdown export for user review and selection

           - ‚úÖ Re-import of user selections for processing



        2. **Enhanced File Organization**

           - ‚úÖ Hierarchical file naming with metadata embedding

           - ‚úÖ Subfolder support through path separator handling

           - ‚úÖ Filename sanitization with path preservation

           - ‚úÖ Collision detection and avoidance



        3. **Intelligent Selection System**

           - ‚úÖ Sequential filter chain with pattern-based matching

           - ‚úÖ Type-specific filters (documents vs. files)

           - ‚úÖ Field-targeted matching for precise filtering

           - ‚úÖ Enable/disable toggles for filter customization



        4. **User Experience Improvements**

           - ‚úÖ Interactive configuration menu for runtime adjustments

           - ‚úÖ Progress feedback during lengthy operations

           - ‚úÖ Color-coded terminal output for status clarity

           - ‚úÖ Modular workflow allowing partial process execution



        ## Recent Development Progress



        Recent development efforts have focused on several key areas:



        1. **Subfolder Organization**

           - Completed implementation of path separator handling in generated names

           - Validated correct directory creation and file placement

           - Ensured proper path sanitization while preserving structure

           - Tested with complex nested folder structures



        2. **Filter System Enhancement**

           - Implemented sequential filter chain with override capability

           - Added support for match field specification beyond default item_generated_name

           - Created interactive filter configuration interface

           - Built pattern-based filtering with glob syntax support



        3. **Browser Automation Improvements**

           - Enhanced waiting strategy with explicit condition checks

           - Implemented progressive scrolling for complete page loading

           - Added contextmanager for reliable resource cleanup

           - Improved download handling with timeout protection



        4. **Data Structure Refinement**

           - Standardized field ordering for consistent presentation

           - Enhanced deduplication with priority-based conflict resolution

           - Improved JSON and Markdown conversion fidelity

           - Added generated name consistency between documents and files



        ## Current Blockers



        Several challenges remain to be addressed in ongoing development:



        1. **Session Management**

           - ‚ö†Ô∏è Browser sessions can expire during lengthy operations

           - ‚ö†Ô∏è Authentication flow needs more robust handling

           - üîÑ Investigating options for session persistence



        2. **Progress Visibility**

           - ‚ö†Ô∏è Long-running operations lack detailed progress indicators

           - ‚ö†Ô∏è Users have limited insight into incremental completion

           - üîÑ Exploring approaches for finer-grained progress reporting



        3. **Download Verification**

           - ‚ö†Ô∏è No checksum validation for downloaded files

           - ‚ö†Ô∏è Incomplete downloads may not be detected

           - üîÑ Considering file integrity verification approaches



        ## Next Development Priorities



        The team has identified the following priorities for immediate focus:



        1. **Hash-based File Verification**

           - Implement checksum calculation for downloaded files

           - Add verification step to confirm download integrity

           - Provide retry mechanism for failed or corrupted downloads



        2. **Enhanced Progress Reporting**

           - Add percentage-based progress indicators

           - Implement estimated time remaining calculations

           - Create logging system for detailed operation tracking



        3. **Search Result Optimization**

           - Refine search URL templates for more precise results

           - Implement automatic deduplication across search queries

           - Improve handling of "VOID" documents and duplicates



        4. **Configuration Persistence**

           - Add ability to save and load filter configurations

           - Create presets for common search patterns

           - Implement project-specific configuration profiles

    ```



    ---



    ##### `07_tasks.md`



    ```markdown

        ## Distilled Highlights

        - Implement hash-based file verification to prevent redundant downloads

        - Add enhanced progress reporting with completion percentage and time estimates

        - Improve session management with automatic re-authentication

        - Create configuration persistence for filter chains and search templates



        # 07_tasks.md

        ## Current Task List



        The following tasks have been identified as priorities for near-term development:



        | Task ID | Priority | Complexity | Status | Description |

        |---------|----------|------------|--------|-------------|

        | T001 | High | Medium | Pending | Implement hash-based file verification |

        | T002 | High | Low | Pending | Enhance progress reporting for long operations |

        | T003 | Medium | High | Pending | Improve session management and re-authentication |

        | T004 | Medium | Medium | Pending | Add configuration persistence for filters |

        | T005 | Low | Medium | Pending | Refactor filter UI for improved usability |



        ## Task Details



        ### T001: Implement Hash-based File Verification



        **Description:**

        Implement a hash-based verification system to identify duplicate files and prevent redundant downloads. This will significantly improve efficiency for large document sets and enable smart resumption of interrupted processes.



        **Specific Requirements:**

        1. Calculate MD5 or SHA-256 hash for each downloaded file

        2. Store hashes in a persistent cache file

        3. Check existing files against stored hashes before downloading

        4. Implement a verification step after download to confirm integrity

        5. Add option to force re-download regardless of hash status



        **Justification:**

        This task directly addresses a current performance bottleneck and will eliminate wasted time downloading files that have already been retrieved in previous runs. This aligns with the project's goal of maximizing engineer productivity.



        **Dependencies:**

        - None



        ---



        ### T002: Enhance Progress Reporting



        **Description:**

        Implement more detailed progress reporting for long-running operations, particularly during document scraping, file metadata retrieval, and download phases.



        **Specific Requirements:**

        1. Add percentage-based completion indicators

        2. Display current item and total count during processing

        3. Implement estimated time remaining calculations

        4. Create a persistent log file of operations

        5. Add visual progress bar for terminal display



        **Justification:**

        Improved progress visibility will enhance user confidence during lengthy operations and provide better feedback on system status. This addresses a known pain point in the current implementation.



        **Dependencies:**

        - None



        ---



        ### T003: Improve Session Management



        **Description:**

        Enhance the browser session handling to better manage authentication, session timeouts, and recovery from interruptions.



        **Specific Requirements:**

        1. Detect session expiration conditions

        2. Implement graceful re-authentication flow

        3. Add session persistence between program runs (optional)

        4. Handle network interruptions with automatic retry

        5. Provide clear user feedback during authentication issues



        **Justification:**

        More robust session management will reduce failures during long-running operations and minimize the need for user intervention, furthering the automation goals of the project.



        **Dependencies:**

        - None



        ---



        ### T004: Add Configuration Persistence



        **Description:**

        Implement a system to save and load filter configurations, search templates, and other user preferences between program runs.



        **Specific Requirements:**

        1. Create a configuration file format (JSON) for storing settings

        2. Implement save/load functionality for filter chains

        3. Add support for named configuration profiles

        4. Create preset templates for common search patterns

        5. Maintain backward compatibility with direct configuration



        **Justification:**

        Configuration persistence will allow users to reuse successful search and filter strategies across projects, reducing setup time and improving consistency.



        **Dependencies:**

        - None



        ---



        ### T005: Refactor Filter UI



        **Description:**

        Improve the user interface for filter configuration to make it more intuitive and provide better visualization of filter effects.



        **Specific Requirements:**

        1. Redesign the interactive filter configuration menu

        2. Add preview functionality to show filter effects before applying

        3. Implement drag-and-drop reordering of filters

        4. Add grouping capability for related filters

        5. Provide filter templates for common use cases



        **Justification:**

        A more intuitive filter interface will reduce the learning curve for new users and improve efficiency for experienced users, supporting broader adoption of the tool.



        **Dependencies:**

        - T004: Add Configuration Persistence



        ## Task Assignments



        These tasks are currently unassigned and available for development. Engineers should select tasks based on:



        1. **Priority**: Higher priority items address more immediate needs

        2. **Dependencies**: Tasks with no dependencies can be started immediately

        3. **Expertise**: Match tasks to individual strengths and interests



        When picking up a task, document your ownership in the `05_activity.md` file and update status here as you progress.



        ## Success Criteria



        Each task should be considered complete when it meets these general criteria:



        1. Full implementation of all specific requirements

        2. Maintains compatibility with existing functionality

        3. Includes appropriate error handling

        4. Follows established code style and patterns

        5. Is documented in the appropriate memory bank files

    ```



    ---



    ##### `08_objective.md`



    ```markdown

        ## Distilled Highlights

        - Create a reliable automation tool that reduces documentation gathering time by 75%+

        - Maintain a three-stage pipeline with user control points for maximum flexibility

        - Ensure robust error handling and progress visibility during lengthy operations

        - Implement smart file organization with hash-based verification to prevent redundant work



        # 08_objective.md

        ## Current Definition of Success



        The RigOfficeDownloader project will be considered successful when it achieves these core objectives:



        ### Primary Objectives



        1. **Time Efficiency**

           - Reduce documentation gathering time by at least 75% compared to manual methods

           - Eliminate repetitive manual steps in the document retrieval workflow

           - Allow engineers to focus on 3D modeling rather than preparation tasks



        2. **Reliability and Robustness**

           - Handle session timeouts and network interruptions gracefully

           - Verify file integrity with hash-based validation

           - Provide clear error feedback and recovery options

           - Maintain data consistency across interrupted operations



        3. **User Control and Flexibility**

           - Preserve the three-stage workflow with human checkpoints

           - Support both automated and manual selection processes

           - Enable configuration of search parameters and filter chains

           - Allow execution of individual workflow stages as needed



        4. **Intelligent Organization**

           - Implement hierarchical file naming with metadata preservation

           - Support subfolder organization based on document relationships

           - Prevent redundant downloads through smart verification

           - Maintain consistent naming patterns for improved discoverability



        ## Success Metrics



        The project's success will be measured against these specific metrics:



        | Metric | Target | Current Status |

        |--------|--------|----------------|

        | Documentation gathering time | 75% reduction | ~60% reduction |

        | File organization consistency | 100% accuracy | 90% accuracy |

        | Error recovery capability | Automatic for common issues | Manual intervention required |

        | Configuration flexibility | Save/load support | Runtime only |

        | Progress visibility | Real-time with estimates | Basic progress indication |



        ## Immediate Focus



        To achieve our objectives, the immediate focus is on:



        1. **Hash-based Verification (Highest Priority)**

           - Implement file fingerprinting to identify duplicates

           - Store hash data for future reference

           - Verify file integrity after download

           - Enable smart resumption of interrupted processes



        2. **Enhanced Progress Reporting**

           - Provide detailed progress indicators for all operations

           - Implement estimated time remaining calculations

           - Create visual progress representation

           - Maintain log of completed operations



        3. **Session Management Improvements**

           - Detect and handle authentication timeouts

           - Implement automatic re-authentication

           - Provide clear user feedback during session issues

           - Add resilience to network interruptions



        ## Vision Alignment



        These objectives directly support the core mission of RigOfficeDownloader:



        - **Engineer Time Optimization**: By automating document retrieval, we free skilled engineers to focus on value-adding 3D modeling work.

        - **Quality Improvement**: Consistent file organization and verification improve the quality of inputs to the modeling process.

        - **Process Reliability**: Robust error handling and session management create a more dependable workflow.

        - **Knowledge Preservation**: Metadata-rich file naming and organization maintain important context about documents and their relationships.



        The utility will ultimately serve as a critical bridge between the NOV RigDoc system and the 3D modeling workflow, transforming a tedious, error-prone process into an efficient, reliable, and user-friendly experience.

    ```

## Message 2

**Crafting README structure**

Creating a concise `README.md` for `RigOfficeDownloader`, keeping it under 50 lines while highlighting key features, usage, and disclaimers.

**Verifying document clarity**

Checking that the README's sections—features, usage, roadmap, and disclaimer—fit the under 50 lines requirement, ensuring each area is concisely and effectively covered.

## Message 3

```markdown
# RigOfficeDownloader

RigOfficeDownloader automates engineering document retrieval from the RigOffice system, cutting tedious manual searches and downloads. It uses a three-stage pipeline (documents → files → downloads) to streamline your workflow while maintaining user control and clarity.

## Why Use It?
- **Time Saver**: Eliminates repetitive navigation steps
- **Consistent Organization**: Enforces clear, metadata-rich naming
- **Flexible Operation**: Supports both automated filters and manual selection
- **Reliable Data Flow**: Uses JSON for internal storage and Markdown for user edits

## Core Workflow
1. **Document Metadata**: Scrape and export to `*-docs.json` / `*-docs.md`  
2. **File Metadata**: Scrape and export to `*-files.json` / `*-files.md`  
3. **Downloads**: Fetch files and organize under `outputs/downloads/`

## Requirements
- **Python 3.x**  
- **Selenium** + browser driver  
- (Refer to `requirements.txt` for details)

## Usage
1. Adjust `RigOfficeDownloader-vX.py` settings or use default filters  
2. Run the corresponding `.bat` or `.py` script  
3. Edit generated `.md` files to include/exclude items as needed  
4. Rerun the script to download your final selection

For deeper insights, see the `memory-bank` folder. 
```

## Message 4

# CONTEXT



RigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).



# SOLUTION



i've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__. you will understand exactly why i mean by this as you start understanding the fundamental concepts (especially with regards to the workflow itself) utilized in the utility, it's **extremely adaptable** (the workflow/concepts/methods for how it works is *meticiously crafted*; there's *a reason for everything*). here's a short abstract breakdown:



    Interactive Menu

    - The utility provides an interactive menu where the user can choose which steps to execute

    - This allows for flexibility in the workflow, enabling the user to run specific steps as needed

    - The user can also update the rig number and search URLs through this menu



    Key Features

    - Automatic document and file metadata scraping

    - User-friendly Markdown editing interface

    - Customizable file naming with item_generated_name

    - Support for subfolder organization in downloads

    - Deduplication of documents and files

    - Configurable field ordering for JSON and Markdown exports



    Technical Implementation

    - Uses Selenium with Chrome WebDriver for web scraping

    - Implements smart waiting strategies for page loading

    - Handles browser sessions with proper cleanup

    - Provides progress feedback during operations

    - Sanitizes filenames for valid paths



## WORKFLOW



This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow follows these steps:



    1. Fetch Documents

    - The utility starts by scraping document metadata from predefined search URLs

    - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory

    - Each document entry includes metadata like title, document number, revision, etc.

    - All documents are initially marked with item_include=False

    - Each document gets an item_generated_name for better identification



    2. Export Documents to Markdown

    - The JSON data is exported to a Markdown table: <rig>-a-docs.md

    - This allows the user to easily review and edit which documents to include

    - The user is expected to edit the Markdown file and set item_include=true for desired documents



    3. Import Updated Document Data

    - After the user edits the Markdown file, the utility imports the changes back to the JSON file

    - This updates which documents are marked for file retrieval



    4. Fetch Files for Selected Documents

    - For each document with item_include=true, the utility scrapes file metadata

    - File data is saved to <rig>-b-files.json

    - Each file is initially marked with item_download=False

    - Files inherit the document's item_generated_name with additional identifiers



    5. Export Files to Markdown

    - The file data is exported to a Markdown table: <rig>-b-files.md

    - The user reviews and edits which files to download by setting item_download=true



    6. Import Updated File Data

    - After editing, the utility imports the changes back to the JSON file

    - This updates which files are marked for download



    7. Download Selected Files

    - Files with item_download=true are downloaded

    - Files are named according to their item_generated_name + extension

    - The utility supports creating subfolders based on '/' in the item_generated_name

    - Files are saved to the outputs/downloads/<rig> directory







# CONSIDERATIONS



since our company use internal github repos for all development we do this utility will also be uploaded to github's company page (accessible for everyone within the company), i want to use this as an opportunity to impress (with unique and original simplicity and elegance). although 99.99% of developers in similar situations will choose to adhere to what they perceive as the concencus best practices and restructure (organize and refactor) the utility's codebase/project/filestructure based on these "rules", **i no longer blindly adhere to established concepts**, i *build* on them through *inherent understanding*. as an example, best practices are *great* for large and complex systems, and in some cases even **neccessary** - but such standardized practices does *not apply to small utilities the same way*, small utilities can often be transformed from a working utility within a single file of <500 lines, and after transforming it to "established best practices" will transform it into ~30 files and thousands of lines of code; __I WANT TO DO THE OPPOSITE OF THIS__. i want **balance** and **cognizeant and inherent understanding**, i want to leverage my own artistic vision onto the code. i find reduction of complexity through perpetually evaluation everything through different contexts through simulating (internally within my mind) different visual representations of components/sections/files in relation to each other.



# FILESTRUCTURE



this is the current filestructure:

```

RigOfficeDownloader

├── .cmd

│   ├── py_venv_pip_install.bat

│   ├── py_venv_run_script.bat

│   ├── py_venv_terminal.bat

│   ├── py_venv_upgrade_requirements.bat

│   └── py_venv_write_requirements.bat

├── memory-bank

│   ├── 01_foundation.md

│   ├── 02_context.md

│   ├── 03_patterns.md

│   ├── 04_tech.md

│   ├── 05_activity.md

│   ├── 06_progress.md

│   ├── 07_tasks.md

│   ├── 08_objective.md

│   └── memory-bank.md

├── outputs

│   ├── data

│   │   ├── R5385.010-a-docs.json

│   │   ├── R5385.010-a-docs.md

│   │   ├── R5385.010-b-files.json

│   │   └── R5385.010-b-files.md

│   └── downloads

│       └── R5385.dirtree.md

├── .gitignore

├── RigOfficeDownloader-v1.bat

├── RigOfficeDownloader-v1.py

├── RigOfficeDownloader-v2.bat

├── RigOfficeDownloader-v2.py

├── RigOfficeDownloader-v3.bat

├── RigOfficeDownloader-v3.py

├── RigOfficeDownloader-v4.bat

├── RigOfficeDownloader-v4.py

├── RigOfficeDownloader.sublime-project

├── notes.md

├── py_venv_init.bat

└── requirements.txt

```



# OBJECTIVE



start by writing a short `README.md` **IN LESS THAN <50 LINES OF CODE** based on all parameters inherently defined within this message, and based on the current memory-bank (provided below). the memory-bank is automatically generated and is therefore extremely (unneccessarily) verbose, so don't try to extract *everything* from it; instead transform subsets into (gradually more) consolidated (and condensed) ESSENTIALS.



# MEMORYBANK



    ### RigOfficeDownloader



    #### Memorybank File Structure



    ```

    ├── 01_foundation.md

    ├── 02_context.md

    ├── 03_patterns.md

    ├── 04_tech.md

    ├── 05_activity.md

    ├── 06_progress.md

    ├── 07_tasks.md

    └── 08_objective.md

    ```



    ---



    ##### `01_foundation.md`



    ```markdown

        ## Distilled Highlights

        - RigOfficeDownloader automates document retrieval from NOV's RigDoc system

        - Three-step workflow: documents → files → downloads

        - Designed to save engineering time during document gathering phase

        - Primary purpose is to streamline 3D modeling preparation workflow



        # 01_foundation.md

        ## Core Mission



        RigOfficeDownloader exists to eliminate the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigOffice system. The mission is to free engineers from repetitive document retrieval tasks, allowing them to focus on their primary responsibility: high-quality 3D modeling.



        ## Primary Intent



        Engineering productivity is severely constrained by poorly functioning document retrieval systems that require excessive manual navigation and intervention. By automating this process, we can dramatically reduce the preparation phase for 3D modeling work, enabling faster project delivery without compromising quality.



        ## Project Identity



        This utility is characterized by:

        - **Pragmatic automation** that focuses on solving a single problem extremely well

        - **Progressive workflow** that divides complex tasks into manageable stages with human review points

        - **Preservation of context** by maintaining consistent naming and organizational patterns

        - **Prioritization of reliability** over speed, ensuring accurate document retrieval



        These guiding principles shape all design and implementation decisions in the project.

    ```



    ---



    ##### `02_context.md`



    ```markdown

        ## Distilled Highlights

        - Engineers waste significant time navigating poorly designed document retrieval systems

        - NOV's RigDoc system requires extensive manual interaction and repetitive tasks

        - Document retrieval is a prerequisite for the actual 3D modeling work

        - Target users are simulation engineers working on oil rig models



        # 02_context.md

        ## Problem Space



        3D simulation engineers working on oil rig visualizations face a significant challenge before they can begin their actual modeling work: gathering the necessary technical documentation. The NOV RigDoc system (https://rigdoc.nov.com) serves as the central repository for this documentation but presents several workflow challenges:



        1. **Fragmented Search Process**: Engineers must construct complex search queries to locate relevant documents

        2. **Manual Review Overhead**: Each search result requires manual review to determine relevance

        3. **Multi-Step Document Retrieval**: Accessing the actual files requires navigating through multiple interfaces

        4. **Inconsistent Naming Conventions**: Documents and files have inconsistent naming patterns

        5. **Inefficient Organization**: Downloaded files lack a clear organizational structure



        The time spent navigating these challenges can often exceed the time required for the actual 3D modeling work, creating a significant bottleneck in the overall workflow.



        ## Stakeholders



        - **Primary Users**: 3D simulation engineers responsible for creating accurate rig models

        - **Project Managers**: Team leaders who need to allocate resources efficiently

        - **NOV Documentation System**: The external service providing the technical documentation

        - **End Clients**: Those who will ultimately use the 3D simulations for training or analysis



        ## User Needs



        3D engineers require:

        1. **Efficient Document Discovery**: The ability to quickly locate relevant technical drawings

        2. **Selective Retrieval**: Options to choose specific documents rather than downloading everything

        3. **Contextual Organization**: Downloaded files organized in a way that preserves their relationships

        4. **Progress Visibility**: Clear feedback on lengthy operations

        5. **Workflow Control**: The ability to pause, review, and continue the process as needed



        ## Environmental Constraints



        - **Authentication Requirements**: Access to RigDoc requires proper credentials

        - **Session Timeouts**: Browser sessions expire after periods of inactivity

        - **Download Limitations**: The system may have rate limits or connection constraints

        - **File Format Variability**: Documents may be available in multiple formats (PDF, DWG, etc.)

        - **Network Dependencies**: The process requires stable internet connectivity



        ## Success Criteria



        The solution will be considered successful if it:

        1. **Reduces Documentation Gathering Time**: Cuts the time required by at least 75%

        2. **Minimizes Manual Intervention**: Automates the most repetitive aspects of the process

        3. **Maintains Engineer Control**: Allows for human judgment at key decision points

        4. **Organizes Results Logically**: Creates a clear structure for downloaded materials

        5. **Handles Edge Cases Gracefully**: Manages errors, timeouts, and unusual document formats

    ```



    ---



    ##### `03_patterns.md`



    ```markdown

        ## Distilled Highlights

        - Three-stage workflow: document retrieval → file metadata → selective download

        - User-editable Markdown interfaces between stages for review and selection

        - JSON storage with consistent structure and field ordering

        - Intelligent naming pattern with subfolder support for downloaded files



        # 03_patterns.md

        ## System Design



        RigOfficeDownloader follows a **linear multi-stage pipeline** architecture with human checkpoints between stages. This design prioritizes reliability, user control, and an incremental approach to data processing.



        ### Core Architectural Pattern



        The system operates through a three-stage workflow:



        ```mermaid

        flowchart TD

            A[Document Metadata Retrieval] --> B[Markdown Export]

            B --> C[User Selection]

            C --> D[Document Import]

            D --> E[File Metadata Retrieval]

            E --> F[Markdown Export]

            F --> G[User Selection]

            G --> H[File Import]

            H --> I[File Download]

            I --> J[Organized Storage]

        ```



        Each stage:

        1. **Collects necessary data** through web scraping

        2. **Transforms the data** into a human-readable format

        3. **Allows for user interaction** to make selection decisions

        4. **Processes the results** based on user choices



        This separation of concerns enables:

        - Independent testing and verification of each processing step

        - The ability to re-run specific stages without repeating the entire process

        - Clear progress tracking and resumption capabilities



        ## Data Representation



        The system employs a consistent data representation pattern across all stages:



        ### JSON as Internal Storage



        ```mermaid

        flowchart TD

            A[Web Scraping] --> B[JSON Documents]

            B --> C[JSON Files]

            C --> D[Downloaded Files]

        ```



        All metadata uses JSON with consistent field schemas:



        1. **Document Schema**:

           - `item_type`: Always "doc"

           - `item_title`: Document title

           - `item_drawing_type`: Drawing type classification

           - `item_include`: Boolean flag for inclusion in the file retrieval stage

           - `item_generated_name`: Constructed name for organization



        2. **File Schema**:

           - `item_type`: Always "file"

           - `item_download`: Boolean flag for download selection

           - `item_generated_name`: Inherits from document with additional identifiers

           - `item_file_ext`: File extension for type identification



        ### Markdown as User Interface



        The system converts JSON to Markdown tables for user editing, providing:

        - A familiar, readable format for reviewing data

        - Easy editing of inclusion/download flags

        - Consistent visualization of metadata

        - Preservation of all fields in a structured layout



        ## Naming and Organization Pattern



        A key pattern is the **hierarchical generated naming** system:



        ```

        <DocNumber>_REV<Revision>--<CaseNumber>-<DocTitle>.<FileIdentifier>.<FileTitle>

        ```



        This pattern:

        - Embeds critical metadata in the filename

        - Supports hierarchical organization through slash (/) characters

        - Maintains consistency between source documents and files

        - Automatically sanitizes invalid characters for filesystem compatibility



        ## Filter Chain Pattern



        The system implements a **sequential filter chain** for intelligent automation:



        ```mermaid

        flowchart LR

            A[Input Data] --> B[Filter 1]

            B --> C[Filter 2]

            C --> D[Filter 3]

            D --> E[Filter N]

            E --> F[Processed Data]

        ```



        Filters are:

        - **Sequentially applied** with later filters potentially overriding earlier ones

        - **Pattern-based** using glob-style syntax

        - **Type-specific** targeting either documents or files

        - **Field-targeted** applying to specific metadata fields

        - **Toggle-capable** with enable/disable functionality



        This approach balances automation with flexibility for special cases.



        ## Browser Interaction Pattern



        The system employs a consistent pattern for browser automation:



        1. **Session Context Management**: Using contextmanager for cleanup

        2. **Smart Waiting Strategy**: Progressive scrolling with dynamic waits

        3. **Deduplication**: Ensuring unique downloads and metadata

        4. **Progress Feedback**: Visibility for long-running operations

    ```



    ---



    ##### `04_tech.md`



    ```markdown

        ## Distilled Highlights

        - Python-based implementation with Selenium web automation

        - Modular architecture with clear separation of concerns

        - JSON and Markdown for data storage and user interaction

        - Browser automation with smart waiting strategies and error handling



        # 04_tech.md

        ## Technology Stack



        RigOfficeDownloader is built with a focused set of technologies to balance reliability, maintainability, and functionality:



        ### Core Technologies



        - **Python**: Primary implementation language

        - **Selenium**: Web automation framework

        - **BeautifulSoup**: HTML parsing and extraction

        - **ChromeDriver**: Browser control interface

        - **JSON**: Data storage format

        - **Markdown**: User interaction format



        ### Key Dependencies



        ```

        selenium         # Browser automation

        beautifulsoup4   # HTML parsing

        webdriver_manager # WebDriver installation automation

        colorama         # Terminal color support

        ansimarkup       # Enhanced terminal formatting

        dateutil         # Date parsing and formatting

        ```



        ## Implementation Architecture



        The system follows a modular implementation pattern with the following components:



        ### RigDocScraper Class



        The central class implementing the scraping workflow with methods for:

        - `fetch_docs()`: Document metadata retrieval

        - `fetch_files()`: File metadata retrieval

        - `download_files()`: File downloading with smart naming



        ### Browser Session Management



        ```python

        @contextmanager

        def browser_session(self, download_mode=False):

            # Setup browser with appropriate configuration

            try:

                # Initialize browser with options

                yield self.driver

            finally:

                # Clean up resources

                if self.driver:

                    self.driver.quit()

        ```



        This pattern ensures proper cleanup of browser resources regardless of execution path.



        ### Smart Waiting Strategy



        The system implements a progressive waiting approach:



        ```python

        class SleepCondition:

            # Callable class for WebDriverWait that sleeps for specified seconds then returns True

            def __init__(self, s):

                self.s = s

            def __call__(self, _):

                time.sleep(self.s)

                return True

        ```



        Combined with explicit conditions:



        ```python

        WebDriverWait(driver, max_wait).until(

            EC.presence_of_element_located((By.CSS_SELECTOR, "a.search-result-link"))

        )

        ```



        This ensures reliable extraction even with variable page load times.



        ### Data Transformation Pipeline



        Data flows through a consistent transformation pipeline:

        1. **Web Extraction**: Selenium + BeautifulSoup scraping

        2. **Data Structuring**: Consistent field mapping

        3. **Storage**: JSON persistence

        4. **Transformation**: JSON to Markdown conversion

        5. **User Interaction**: Markdown editing

        6. **Reversal**: Markdown to JSON conversion

        7. **Processing**: Filtered downloading based on flags



        ### File Organization System



        Downloads are managed through a structured file system:



        ```

        outputs/

        ├── data/

        │   ├── <rig>-a-docs.json   # Document metadata

        │   ├── <rig>-a-docs.md     # User-editable document selection

        │   ├── <rig>-b-files.json  # File metadata

        │   └── <rig>-b-files.md    # User-editable file selection

        └── downloads/

            └── <rig>/              # Downloaded files with subfolder organization

        ```



        ### Sanitization and Path Management



        File paths and names are sanitized with a robust approach:



        ```python

        def sanitize_filename(s):

            # Clean string for valid filename: replace invalid chars, preserve paths

            replacements = {

                "'": "", '"': "", '`': "",  # Remove quotes and backticks

                '\\': "_", '|': "_", '?': "_", '*': "_", '<': "_", '>': "_", ':': "_"  # Replace with underscore

            }



            # Apply replacements and clean up

            result = s

            for char, replacement in replacements.items():

                result = result.replace(char, replacement)



            result = ''.join(c for c in result if c.isprintable() or c == '/')  # Keep printable chars and path separators

            result = re.sub(r'\s+', ' ', result)  # Normalize spaces



            return result.strip()

        ```



        This allows for:

        - Safe filesystem operations

        - Preservation of subfolder structure

        - Consistent naming patterns



        ## Configuration System



        The system uses a structured configuration approach:



        ```python

        CONFIG = {

            "rig_number": f"{PROJECT_RIG_ID}.020",

            "search_urls": PROJECTINFO_GAD,

            "show_progress": True,

            "filters": [

                # Sequential filter definitions

            ]

        }

        ```



        This enables:

        - Easy modification of search parameters

        - Fine-tuning of filter chains

        - Toggle-based control of functionality

        - Project-specific customization



        ## Technical Constraints



        - **Browser Compatibility**: Requires Chrome browser

        - **Network Access**: Must have connectivity to rigdoc.nov.com

        - **Authentication**: User must handle authentication through browser session

        - **File System Access**: Requires write permissions for outputs directory

        - **Memory Consumption**: Can handle large document sets with incremental processing

    ```



    ---



    ##### `05_activity.md`



    ```markdown

        ## Distilled Highlights

        - Currently working on v4 of RigOfficeDownloader with enhanced file organization

        - Implementing subfolder support via path separators in generated names

        - Improved configuration system with filter chains

        - Working on progress visibility for lengthy operations



        # 05_activity.md

        ## Current Focus



        The development team is currently focused on enhancing RigOfficeDownloader with several key improvements:



        ### Version 4 Implementation



        Version 4 represents a significant maturation of the tool with emphasis on:



        1. **Enhanced File Organization**

           - Implementing subfolder support through path separator handling

           - Improving the item_generated_name pattern for more consistent file organization

           - Ensuring proper sanitization of filenames while preserving path structure



        2. **Smarter Configuration System**

           - Refining the filter chain mechanism for more precise document/file selection

           - Supporting pattern-based filtering with glob syntax

           - Enabling field-specific matching for greater flexibility



        3. **User Experience Improvements**

           - Enhancing progress visibility during long-running operations

           - Providing clearer feedback on the staging process

           - Making the interactive menu more intuitive



        ## Recent Decisions



        The team has made several key decisions that are shaping current development:



        1. **Single-File Approach**

           - Maintaining the utility as a single Python file for simplicity

           - Ensuring all functionality is self-contained for easy deployment

           - Using modular internal design for maintainability



        2. **Three-Stage Workflow**

           - Confirming the document → file → download workflow as optimal

           - Reinforcing user decision points between stages

           - Enhancing the JSON ↔ Markdown conversion for better user interaction



        3. **Progressive Enhancement**

           - Adding features incrementally without disrupting core functionality

           - Prioritizing stability and reliability over feature expansion

           - Focusing on refinements that enhance the engineer's workflow



        ## Technical Hypotheses



        The team is currently exploring several technical approaches:



        1. **Smart Waiting Strategy**

           - Hypothesis: Combining explicit waits with scrolling will improve scraping reliability

           - Implementation: Using WebDriverWait with both sleep conditions and element presence checks

           - Testing: Comparing success rates across different document types and quantities



        2. **Configurable Field Ordering**

           - Hypothesis: Customizable field order will improve user experience

           - Implementation: Separate field order definitions for documents and files

           - Testing: User feedback on Markdown table readability and editing experience



        3. **Deduplication Approach**

           - Hypothesis: Priority-based deduplication will handle conflicting data appropriately

           - Implementation: Using hashable key generation with exclusion lists

           - Testing: Verifying correct handling of duplicates with different attribute values



        ## Ongoing Discussions



        Current topics under active discussion include:



        1. **Session Management**

           - How to handle authentication when sessions expire

           - Options for persisting cookies between runs

           - Balancing security with convenience



        2. **Error Recovery**

           - Strategies for handling network interruptions

           - Approaches to resuming interrupted downloads

           - Error reporting format and detail level



        3. **Filter Chain Refinement**

           - Making filter creation more user-friendly

           - Potential for saving and loading filter configurations

           - Visualization of filter effects before application

    ```



    ---



    ##### `06_progress.md`



    ```markdown

        ## Distilled Highlights

        - Version 4 is the current active build with enhanced organization features

        - Subfolder support successfully implemented for downloaded files

        - Filter chain mechanism functioning with pattern-based selection

        - Interactive configuration menu implemented for runtime adjustments



        # 06_progress.md

        ## State of the Build



        The RigOfficeDownloader utility has evolved through several versions, with each iteration bringing focused improvements:



        ### Version History



        | Version | Status | Key Features |

        |---------|--------|-------------|

        | v1 | Complete | Basic document retrieval and download |

        | v2 | Complete | JSON/Markdown conversion and user selection |

        | v3 | Complete | Improved error handling and field organization |

        | v4 | Active | Subfolder support, filter chains, field ordering |



        ### Current Version Capabilities



        Version 4 represents the current stable build with several key capabilities:



        1. **Complete Document Retrieval Pipeline**

           - ‚úÖ Automated document metadata scraping from RigDoc

           - ‚úÖ Structured JSON storage with consistent field schemas

           - ‚úÖ Markdown export for user review and selection

           - ‚úÖ Re-import of user selections for processing



        2. **Enhanced File Organization**

           - ‚úÖ Hierarchical file naming with metadata embedding

           - ‚úÖ Subfolder support through path separator handling

           - ‚úÖ Filename sanitization with path preservation

           - ‚úÖ Collision detection and avoidance



        3. **Intelligent Selection System**

           - ‚úÖ Sequential filter chain with pattern-based matching

           - ‚úÖ Type-specific filters (documents vs. files)

           - ‚úÖ Field-targeted matching for precise filtering

           - ‚úÖ Enable/disable toggles for filter customization



        4. **User Experience Improvements**

           - ‚úÖ Interactive configuration menu for runtime adjustments

           - ‚úÖ Progress feedback during lengthy operations

           - ‚úÖ Color-coded terminal output for status clarity

           - ‚úÖ Modular workflow allowing partial process execution



        ## Recent Development Progress



        Recent development efforts have focused on several key areas:



        1. **Subfolder Organization**

           - Completed implementation of path separator handling in generated names

           - Validated correct directory creation and file placement

           - Ensured proper path sanitization while preserving structure

           - Tested with complex nested folder structures



        2. **Filter System Enhancement**

           - Implemented sequential filter chain with override capability

           - Added support for match field specification beyond default item_generated_name

           - Created interactive filter configuration interface

           - Built pattern-based filtering with glob syntax support



        3. **Browser Automation Improvements**

           - Enhanced waiting strategy with explicit condition checks

           - Implemented progressive scrolling for complete page loading

           - Added contextmanager for reliable resource cleanup

           - Improved download handling with timeout protection



        4. **Data Structure Refinement**

           - Standardized field ordering for consistent presentation

           - Enhanced deduplication with priority-based conflict resolution

           - Improved JSON and Markdown conversion fidelity

           - Added generated name consistency between documents and files



        ## Current Blockers



        Several challenges remain to be addressed in ongoing development:



        1. **Session Management**

           - ‚ö†Ô∏è Browser sessions can expire during lengthy operations

           - ‚ö†Ô∏è Authentication flow needs more robust handling

           - üîÑ Investigating options for session persistence



        2. **Progress Visibility**

           - ‚ö†Ô∏è Long-running operations lack detailed progress indicators

           - ‚ö†Ô∏è Users have limited insight into incremental completion

           - üîÑ Exploring approaches for finer-grained progress reporting



        3. **Download Verification**

           - ‚ö†Ô∏è No checksum validation for downloaded files

           - ‚ö†Ô∏è Incomplete downloads may not be detected

           - üîÑ Considering file integrity verification approaches



        ## Next Development Priorities



        The team has identified the following priorities for immediate focus:



        1. **Hash-based File Verification**

           - Implement checksum calculation for downloaded files

           - Add verification step to confirm download integrity

           - Provide retry mechanism for failed or corrupted downloads



        2. **Enhanced Progress Reporting**

           - Add percentage-based progress indicators

           - Implement estimated time remaining calculations

           - Create logging system for detailed operation tracking



        3. **Search Result Optimization**

           - Refine search URL templates for more precise results

           - Implement automatic deduplication across search queries

           - Improve handling of "VOID" documents and duplicates



        4. **Configuration Persistence**

           - Add ability to save and load filter configurations

           - Create presets for common search patterns

           - Implement project-specific configuration profiles

    ```



    ---



    ##### `07_tasks.md`



    ```markdown

        ## Distilled Highlights

        - Implement hash-based file verification to prevent redundant downloads

        - Add enhanced progress reporting with completion percentage and time estimates

        - Improve session management with automatic re-authentication

        - Create configuration persistence for filter chains and search templates



        # 07_tasks.md

        ## Current Task List



        The following tasks have been identified as priorities for near-term development:



        | Task ID | Priority | Complexity | Status | Description |

        |---------|----------|------------|--------|-------------|

        | T001 | High | Medium | Pending | Implement hash-based file verification |

        | T002 | High | Low | Pending | Enhance progress reporting for long operations |

        | T003 | Medium | High | Pending | Improve session management and re-authentication |

        | T004 | Medium | Medium | Pending | Add configuration persistence for filters |

        | T005 | Low | Medium | Pending | Refactor filter UI for improved usability |



        ## Task Details



        ### T001: Implement Hash-based File Verification



        **Description:**

        Implement a hash-based verification system to identify duplicate files and prevent redundant downloads. This will significantly improve efficiency for large document sets and enable smart resumption of interrupted processes.



        **Specific Requirements:**

        1. Calculate MD5 or SHA-256 hash for each downloaded file

        2. Store hashes in a persistent cache file

        3. Check existing files against stored hashes before downloading

        4. Implement a verification step after download to confirm integrity

        5. Add option to force re-download regardless of hash status



        **Justification:**

        This task directly addresses a current performance bottleneck and will eliminate wasted time downloading files that have already been retrieved in previous runs. This aligns with the project's goal of maximizing engineer productivity.



        **Dependencies:**

        - None



        ---



        ### T002: Enhance Progress Reporting



        **Description:**

        Implement more detailed progress reporting for long-running operations, particularly during document scraping, file metadata retrieval, and download phases.



        **Specific Requirements:**

        1. Add percentage-based completion indicators

        2. Display current item and total count during processing

        3. Implement estimated time remaining calculations

        4. Create a persistent log file of operations

        5. Add visual progress bar for terminal display



        **Justification:**

        Improved progress visibility will enhance user confidence during lengthy operations and provide better feedback on system status. This addresses a known pain point in the current implementation.



        **Dependencies:**

        - None



        ---



        ### T003: Improve Session Management



        **Description:**

        Enhance the browser session handling to better manage authentication, session timeouts, and recovery from interruptions.



        **Specific Requirements:**

        1. Detect session expiration conditions

        2. Implement graceful re-authentication flow

        3. Add session persistence between program runs (optional)

        4. Handle network interruptions with automatic retry

        5. Provide clear user feedback during authentication issues



        **Justification:**

        More robust session management will reduce failures during long-running operations and minimize the need for user intervention, furthering the automation goals of the project.



        **Dependencies:**

        - None



        ---



        ### T004: Add Configuration Persistence



        **Description:**

        Implement a system to save and load filter configurations, search templates, and other user preferences between program runs.



        **Specific Requirements:**

        1. Create a configuration file format (JSON) for storing settings

        2. Implement save/load functionality for filter chains

        3. Add support for named configuration profiles

        4. Create preset templates for common search patterns

        5. Maintain backward compatibility with direct configuration



        **Justification:**

        Configuration persistence will allow users to reuse successful search and filter strategies across projects, reducing setup time and improving consistency.



        **Dependencies:**

        - None



        ---



        ### T005: Refactor Filter UI



        **Description:**

        Improve the user interface for filter configuration to make it more intuitive and provide better visualization of filter effects.



        **Specific Requirements:**

        1. Redesign the interactive filter configuration menu

        2. Add preview functionality to show filter effects before applying

        3. Implement drag-and-drop reordering of filters

        4. Add grouping capability for related filters

        5. Provide filter templates for common use cases



        **Justification:**

        A more intuitive filter interface will reduce the learning curve for new users and improve efficiency for experienced users, supporting broader adoption of the tool.



        **Dependencies:**

        - T004: Add Configuration Persistence



        ## Task Assignments



        These tasks are currently unassigned and available for development. Engineers should select tasks based on:



        1. **Priority**: Higher priority items address more immediate needs

        2. **Dependencies**: Tasks with no dependencies can be started immediately

        3. **Expertise**: Match tasks to individual strengths and interests



        When picking up a task, document your ownership in the `05_activity.md` file and update status here as you progress.



        ## Success Criteria



        Each task should be considered complete when it meets these general criteria:



        1. Full implementation of all specific requirements

        2. Maintains compatibility with existing functionality

        3. Includes appropriate error handling

        4. Follows established code style and patterns

        5. Is documented in the appropriate memory bank files

    ```



    ---



    ##### `08_objective.md`



    ```markdown

        ## Distilled Highlights

        - Create a reliable automation tool that reduces documentation gathering time by 75%+

        - Maintain a three-stage pipeline with user control points for maximum flexibility

        - Ensure robust error handling and progress visibility during lengthy operations

        - Implement smart file organization with hash-based verification to prevent redundant work



        # 08_objective.md

        ## Current Definition of Success



        The RigOfficeDownloader project will be considered successful when it achieves these core objectives:



        ### Primary Objectives



        1. **Time Efficiency**

           - Reduce documentation gathering time by at least 75% compared to manual methods

           - Eliminate repetitive manual steps in the document retrieval workflow

           - Allow engineers to focus on 3D modeling rather than preparation tasks



        2. **Reliability and Robustness**

           - Handle session timeouts and network interruptions gracefully

           - Verify file integrity with hash-based validation

           - Provide clear error feedback and recovery options

           - Maintain data consistency across interrupted operations



        3. **User Control and Flexibility**

           - Preserve the three-stage workflow with human checkpoints

           - Support both automated and manual selection processes

           - Enable configuration of search parameters and filter chains

           - Allow execution of individual workflow stages as needed



        4. **Intelligent Organization**

           - Implement hierarchical file naming with metadata preservation

           - Support subfolder organization based on document relationships

           - Prevent redundant downloads through smart verification

           - Maintain consistent naming patterns for improved discoverability



        ## Success Metrics



        The project's success will be measured against these specific metrics:



        | Metric | Target | Current Status |

        |--------|--------|----------------|

        | Documentation gathering time | 75% reduction | ~60% reduction |

        | File organization consistency | 100% accuracy | 90% accuracy |

        | Error recovery capability | Automatic for common issues | Manual intervention required |

        | Configuration flexibility | Save/load support | Runtime only |

        | Progress visibility | Real-time with estimates | Basic progress indication |



        ## Immediate Focus



        To achieve our objectives, the immediate focus is on:



        1. **Hash-based Verification (Highest Priority)**

           - Implement file fingerprinting to identify duplicates

           - Store hash data for future reference

           - Verify file integrity after download

           - Enable smart resumption of interrupted processes



        2. **Enhanced Progress Reporting**

           - Provide detailed progress indicators for all operations

           - Implement estimated time remaining calculations

           - Create visual progress representation

           - Maintain log of completed operations



        3. **Session Management Improvements**

           - Detect and handle authentication timeouts

           - Implement automatic re-authentication

           - Provide clear user feedback during session issues

           - Add resilience to network interruptions



        ## Vision Alignment



        These objectives directly support the core mission of RigOfficeDownloader:



        - **Engineer Time Optimization**: By automating document retrieval, we free skilled engineers to focus on value-adding 3D modeling work.

        - **Quality Improvement**: Consistent file organization and verification improve the quality of inputs to the modeling process.

        - **Process Reliability**: Robust error handling and session management create a more dependable workflow.

        - **Knowledge Preservation**: Metadata-rich file naming and organization maintain important context about documents and their relationships.



        The utility will ultimately serve as a critical bridge between the NOV RigDoc system and the 3D modeling workflow, transforming a tedious, error-prone process into an efficient, reliable, and user-friendly experience.

    ```



# REQUIREMENTS



i want you to focus on the abstract and general concepts related to **functionality**, i don't want it to destroy the "canvas" with unusefull noise (unneccessary bloat)


## Message 5

**Charting functionalities**

I'm mapping out the readme for "RigOfficeDownloader," emphasizing tasks, key features, and usage steps.

## Message 6

```markdown
# RigOfficeDownloader

RigOfficeDownloader automates retrieval of engineering documents from RigOffice, cutting the repetitive chores of manual searches and downloads. It uses a flexible, step-by-step workflow that merges automated scraping with human review, ensuring clear organization and minimal guesswork.

## Core Concepts
- **Three-Stage Flow**  
  1. **Documents**: Fetch metadata → export/edit Markdown  
  2. **Files**: Fetch metadata → export/edit Markdown  
  3. **Downloads**: Save chosen files with subfolder support
- **Markdown Editing**: Users mark items for inclusion (documents) or download (files) by flipping flags in `.md` tables.
- **Consistent Naming**: Automatically generates structured filenames/path hierarchies.
- **Interactive Menu**: Lets you choose which step to run, update rig numbers, or tweak filters.

## Technical Highlights
- **Selenium Web Automation** for scraping and navigation
- **JSON + Markdown** for storing and editing metadata
- **Smart Waiting & Session Handling** to manage slow loads
- **Single-File Simplicity**: Core logic in one Python script

## Getting Started
1. **Install Dependencies**: `pip install -r requirements.txt`
2. **Run Script**: Launch `RigOfficeDownloader-v4.py` (or a desired version).
3. **Edit Markdown**: Include/exclude items as needed, then re-run to proceed.
4. **Downloads**: Final files land in `outputs/downloads/<rig>`.

See the `memory-bank` folder for deeper insights and design rationale.
```

## Message 7

actually, keep it above <70 lines instead. additionally, it's actually more correct to use the actual steps from the utility as source (provided below):



```python

#!/usr/bin/env python

"""

Single-run utility for rigdoc scraping, now with:

 + "item_generated_name" for both docs and files,

 + custom column ordering for JSON & Markdown,

 + interpretation of '/' as subfolders in download paths.



Workflow (in one run):

 1) Fetch docs => <rig>-a-docs.json (with item_include=False, item_generated_name set).

 2) Export docs => <rig>-a-docs.md; prompt user to edit 'item_include'=true for desired docs, press Enter.

 3) Import updated docs from Markdown.

 4) Fetch files for docs => <rig>-b-files.json (with item_download=False, item_generated_name set).

 5) Export files => <rig>-b-files.md; prompt user to edit 'item_download'=true for desired files, press Enter.

 6) Import updated files from Markdown.

 7) Download any files with item_download=True, naming them from "item_generated_name" + extension,

    and placing them into subfolders based on '/' in "item_generated_name".

"""



# ======================

# Templates

# - List of prebuilt search URLs for targeted, rapid retrieval of documents

# - Filtered by equipment type, function, and rig number

# - Quick, targeted search URLs for simulator-related project documents

# ======================

PROJECT_RIG_ID = "R0000"



# PROJECTINFO_GAD: Main equipment drawings

PROJECTINFO_GAD = [

    # > Main Equipment Drawings

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*G00*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*GAD-00*",

    # > Derrick/Drillfloor

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DRILL*FLOOR*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DERRICK*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EQUIPMENT*LAYOUT*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATION*VIEW*",

    # > Topdrive

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TOP*DRIVE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TRAVELLINGBLOCK*",

    # > Roughneck

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROUGHNECK*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRATONG*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MUDBUCKET*",

    # > Hydraracker

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRARACKER*",

    # > Fingerboard/Bellyboard/Rackingboard

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*BELLY*BOARD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*FINGER*BOARD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*BOARD*",

    # > Catwalk

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATWALK*",

    # > Mousehole

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MOUSE*HOLE*",

    # > Guidearm

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*GUIDE*ARM*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EBT*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATED*BACKUP*TONG*",

    # > Misc

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATHEAD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SPIDER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROTARY*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SLIPS*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*AHC*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CMC*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STANDBUILDING*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TUBULAR*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RTX*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*GUIDE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*LIFTING*CYLINDER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STAR*RACKER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*UTILITY*HANDLING*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*SYSTEM*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RAISED*SYSTEM*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DFMA*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MPMA*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MANUPILATOR*ARM*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*CHUTE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RISER*CHUTE*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SERVICE*BASKET*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MAINTENANCE*BASKET*",

]



# PROJECTINFO_ASM: Main assembly drawings

PROJECTINFO_ASM = [

    # > Equipment Main Drawings

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocNumber=*A000*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocNumber=*ASM-00*",

    # > Derrick/Drillfloor

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*DRILL*FLOOR*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*DERRICK*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*EQUIPMENT*LAYOUT*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*ELEVATION*VIEW*",

    # > Topdrive

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*TOP*DRIVE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*TRAVELLINGBLOCK*",

    # > Catwalk

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*CATWALK*",

    # > Roughneck

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*ROUGHNECK*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MUDBUCKET*",

    # > Hydraracker

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*HYDRARACKER*",

    # > Mousehole

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MOUSE*HOLE*",

    # > Guidearm

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*GUIDE*ARM*",

    # > Fingerboard/Bellyboard/Rackingboard

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*FINGER*BOARD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*BELLY*BOARD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RACKING*BOARD*",

    # > Misc

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*CATHEAD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*SPIDER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*ROTARY*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*SLIPS*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*AHC*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*CMC*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*EBT*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*ELEVATED*BACKUP*TONG*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*STANDBUILDING*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*TUBULAR*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RTX*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*PIPE*GUIDE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*LIFTING*CYLINDER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*UTILITY*HANDLING*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*STAR*RACKER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RACKING*SYSTEM*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RAISED*SYSTEM*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*DFMA*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MPMA*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MANUPILATOR*ARM*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*PIPE*CHUTE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RISER*CHUTE*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*SERVICE*BASKET*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MAINTENANCE*BASKET*",

]



# List of general inclusion patterns for Nov 3D Simulator Projects

FILTERS_SIMULATOR_PATTERNS_INCLUDE = [

    "*DRILL*FLOOR*",

    "*ELEVATION*VIEW*",

    "*EQUIPMENT*LAYOUT*",

    "*G000*",

    "*GAD-00*",

    "*A000*",

    "*ASM-00*",

]



# List of general exclusion patterns for Nov 3D Simulator Projects

FILTERS_SIMULATOR_PATTERNS_SKIP = [

    "*Agitator*",

    "*BOP *",

    "*Bulk*Systems*",

    "*Cabinet*",

    "*Centrifuge*",

    "*Compressor*",

    "*Crane*",

    "*Cuttings*",

    "*Cyberbase*",

    "*DCDA *",

    "*DCI*",

    "*Deadline*",

    "*Gantry*Crane*",

    "*HPU*",

    "*HVAC*",

    "*Knuckle*Crane*",

    "*LMRP*",

    "*Manrider*",

    "*Mixer*",

    "*Mixing*",

    "*Mud *",

    "*Pumps*",

    "*Sdi*",

    "*Standby*",

    "*Station*",

    "*Tank *",

    "*Valve*",

    "*VOID*",

    "*Winch*",

    "*X-tree*",

    "*Yoke*",

]



# ======================

# Configuration

# ======================

CONFIG = {

    "rig_number": f"{PROJECT_RIG_ID}.020",

    # "search_urls": PROJECTINFO_GAD,

    "search_urls": PROJECTINFO_GAD,

    "show_progress": True,

    # Sequential filter chain - applied in order, later filters can override earlier ones

    "filters": [

        # [+] Docs:item_generated_name

        {

            "type": "docs",

            "enabled": True,

            "pattern": FILTERS_SIMULATOR_PATTERNS_INCLUDE,

            "match_field": "item_generated_name",

            "field": "item_include",

            "value": True,

            "comment": "",

        },

        # [-] Docs:item_generated_name

        {

            "type": "docs",

            "enabled": True,

            "pattern": FILTERS_SIMULATOR_PATTERNS_SKIP,

            "match_field": "item_generated_name",

            "field": "item_include",

            "value": False,

            "comment": "",

        },

        # [-] Docs:item_case_description

        {

            "type": "docs",

            "enabled": True,

            "pattern": FILTERS_SIMULATOR_PATTERNS_SKIP,

            "match_field": "item_case_description",

            "field": "item_include",

            "value": False,

            "comment": "",

        },

        # [-] Docs:item_status

        {

            "type": "docs",

            "enabled": True,

            "pattern": ["(VOID)"],

            "match_field": "item_case_description",

            "field": "item_include",

            "value": False,

            "comment": "",

        },

        # [+] Files:item_file_ext

        {

            "type": "files",

            "enabled": True,

            "pattern": ["*.pdf"],

            "match_field": "item_file_ext",

            "field": "item_download",

            "value": True,

            "comment": "",

        },

        # [-] File:item_case_description

        {

            "type": "files",

            "enabled": True,

            "pattern": FILTERS_SIMULATOR_PATTERNS_SKIP,

            "match_field": "item_case_description",

            "field": "item_download",

            "value": False,

            "comment": "",

        },

        # [-] Files:item_generated_name

        {

            "type": "files",

            "enabled": True,

            "pattern": ["*Partslist*"],

            "match_field": "item_generated_name",

            "field": "item_download",

            "value": False,

            "comment": "",

        },

    ],

}



# ======================

# Imports

# ======================

# Standard library imports

import json

import os

import re

import shutil

import time

import timeit

from contextlib import contextmanager

from urllib.parse import urljoin



# Third-party library imports

import colorama

import dateutil.parser

from ansimarkup import ansiprint

from bs4 import BeautifulSoup

from selenium import webdriver

from selenium.webdriver.chrome.options import Options

from selenium.webdriver.chrome.service import Service

from selenium.webdriver.support.ui import WebDriverWait



# (ADDED imports for smarter wait)

from selenium.webdriver.common.by import By

from selenium.webdriver.support import expected_conditions as EC

from selenium.common.exceptions import TimeoutException



from webdriver_manager.chrome import ChromeDriverManager



colorama.init()



# ======================

# Folders

# ======================

BASE_OUTPUT = os.path.abspath(os.path.join(os.getcwd(), "outputs"))

DATA_DIR    = os.path.join(BASE_OUTPUT, "data")

DL_DIR      = os.path.join(BASE_OUTPUT, "downloads")

for d in (BASE_OUTPUT, DATA_DIR, DL_DIR):

    os.makedirs(d, exist_ok=True)



# ======================

# Field Orders

# ======================

DEFAULT_DOC_FIELD_ORDER = [

    "item_include",

    "item_generated_name",

    "item_type",

    "item_case_no",

    "item_drawing_type",

    "item_doc_no",

    "item_date",

    "item_title",

    "item_case_description",

    "item_status",

    "item_revision",

    "item_responsible",

    "item_url",

]



DEFAULT_FILE_FIELD_ORDER = [

    "item_type",

    "item_generated_name",

    "item_case_no",

    "item_drawing_type",

    "item_title",

    "item_doc_no",

    "item_file_ext",

    "item_download",

    "item_file_size",

    "item_file_id",

    "item_date",

    "item_revision",

    "item_case_description",

    "item_status",

    "item_responsible",

    "item_url",

]



# ======================

# Logging / Helper

# ======================

# Colored console output functions

def cprint(x, color='#FFFFFF'):

    ansiprint(f'<fg {color}>{x}')



print_err  = lambda x: cprint(x, '#C92B65')  # Error - red

print_warn = lambda x: cprint(x, '#E6992B')  # Warning - orange

print_ok   = lambda x: cprint(x, '#1FCE46')  # Success - green

print_inf  = lambda x: cprint(x, '#FFFFFF')  # Info - white

print_sub  = lambda x: cprint(x, '#74705D')  # Subdued - gray



class SleepCondition:

    # Callable class for WebDriverWait that sleeps for specified seconds then returns True

    def __init__(self, s):

        self.s = s

    def __call__(self, _):

        # Parameter is required by WebDriverWait but not used

        time.sleep(self.s)

        return True



def reformat_date(s):

    # Convert date string to YYYY.MM.DD format or return original if not a valid date

    try:

        dt = dateutil.parser.parse(s)

        return dt.strftime('%Y.%m.%d')

    except:

        return s



def match_pattern(text, pattern):

    # Match text against glob pattern(s) (*=any chars, ?=single char), case-insensitive

    # Pattern can be a single string or a list of patterns (match any)

    if isinstance(pattern, list):

        # If any pattern in the list matches, return True

        return any(match_pattern(text, p) for p in pattern)



    # Single pattern matching

    regex_pattern = pattern.replace(".", "\\.").replace("*", ".*").replace("?", ".")

    return bool(re.search(f"^{regex_pattern}$", text, re.IGNORECASE))



def sanitize_filename(s):

    # Clean string for valid filename: replace invalid chars, preserve paths, handle quotes

    replacements = {

        "'": "", '"': "", '`': "",  # Remove quotes and backticks

        '\\': "_", '|': "_", '?': "_", '*': "_", '<': "_", '>': "_", ':': "_"  # Replace with underscore

    }



    # Apply replacements and clean up

    result = s

    for char, replacement in replacements.items():

        result = result.replace(char, replacement)



    result = ''.join(c for c in result if c.isprintable() or c == '/')  # Keep printable chars and path separators

    result = re.sub(r'\s+', ' ', result)  # Normalize spaces



    return result.strip()



def reorder_dict_keys(data_dict, field_order):

    # Return dict with keys ordered according to field_order if they exist in data_dict

    reordered = {}

    for key in field_order:

        if key in data_dict:

            reordered[key] = data_dict[key]

    return reordered



def remove_duplicates(data, exclude_keys=None, priority_key='item_download', priority_vals=None):

    # Deduplicate items with priority-based conflict resolution

    if not exclude_keys:

        exclude_keys=[]

    if not priority_vals:

        priority_vals=[]

    exset = set(exclude_keys)

    pmap  = {v:i for i,v in enumerate(priority_vals)}

    out   = {}

    for d in data:

        # Build hashable key ignoring exclude_keys

        filt = {k:v for k,v in d.items() if k not in exset}

        hkey = frozenset(filt.items())

        if hkey not in out:

            out[hkey] = d

        else:

            # pick the item with better priority

            if priority_key and priority_vals:

                newv = d.get(priority_key)

                oldv = out[hkey].get(priority_key)

                if (newv in pmap) and (oldv in pmap) and pmap[newv] < pmap[oldv]:

                    out[hkey] = d

                elif newv in pmap and oldv not in pmap:

                    out[hkey] = d

    return list(out.values())



def dedupe_and_save(new_items, path, field_order=None):

    # Merge items with existing JSON, deduplicate, reorder fields, and save

    if os.path.exists(path):

        try:

            with open(path,'r',encoding='utf-8') as f:

                old = json.load(f)

        except:

            old = []

    else:

        old = []

    merged = old + new_items

    merged = remove_duplicates(

        merged,

        exclude_keys=['item_download','item_generated_path'],

        priority_key='item_download',

        priority_vals=[False,True]

    )



    # If field_order is provided, reorder each item

    if field_order:

        merged = [reorder_dict_keys(d, field_order) for d in merged]



    tmp = path + '.temp'

    with open(tmp,'w',encoding='utf-8') as f:

        json.dump(merged,f,indent=2)

    os.replace(tmp,path)



    # Get relative path for display

    rel_path = os.path.relpath(path, os.getcwd())

    print_inf(f"Saved {len(merged)} items => {rel_path}")



def append_items(new_items, path, field_order=None):

    # Convenience wrapper for dedupe_and_save that checks for empty items

    if new_items:

        dedupe_and_save(new_items, path, field_order=field_order)



# ======================

# JSON <-> Markdown

# ======================

def json_to_md_table(json_path, md_path, field_order=None, filter_chain=None):

    # Export JSON to MD table with optional field ordering and sequential filter chain

    if not os.path.exists(json_path):

        print_err(f"No JSON found: {json_path}")

        return

    with open(json_path,'r',encoding='utf-8') as f:

        data = json.load(f)

    if not data:

        print_warn(f"No data in {json_path}")

        return



    # Determine item type from filename

    item_type = "docs" if "-a-docs." in json_path else "files"



    # Apply sequential filter chain

    if filter_chain and isinstance(filter_chain, list):

        for filter_idx, filter_config in enumerate(filter_chain):

            # Skip if filter is disabled or doesn't match the current item type

            if not filter_config.get("enabled", False) or filter_config.get("type") != item_type:

                continue



            pattern = filter_config.get("pattern", "")

            field = filter_config.get("field", "")

            value = filter_config.get("value", True)

            comment = filter_config.get("comment", "")

            match_field = filter_config.get("match_field", "item_generated_name")



            if pattern and field:

                filtered_count = 0

                for d in data:

                    field_value = d.get(match_field, "")

                    if field_value and match_pattern(field_value, pattern):

                        if d.get(field) != value:  # Only update if value is different

                            d[field] = value

                            filtered_count += 1



                if filtered_count > 0:

                    action = "Set" if value else "Cleared"



                    # Format pattern for display

                    if isinstance(pattern, list):

                        pattern_str = f"[{', '.join(pattern)}]"

                    else:

                        pattern_str = f"'{pattern}'"



                    # Show match field if not the default

                    match_field_str = f" in {match_field}" if match_field != "item_generated_name" else ""



                    print_ok(f"Filter #{filter_idx+1}: {action} {field}={value} for {filtered_count} items matching {pattern_str}{match_field_str}")

                    if comment:

                        print_sub(f"  → {comment}")



    # Determine field order if not provided

    if not field_order:

        colset = set()

        for d in data:

            colset.update(d.keys())

        field_order = sorted(colset)



    # Build Markdown table

    lines = []

    lines.append("| " + " | ".join(field_order) + " |")

    lines.append("|" + "|".join(["---"]*len(field_order)) + "|")



    for d in data:

        row = []

        for c in field_order:

            val = d.get(c,"")

            if isinstance(val,bool):

                val = str(val).lower()

            val = str(val).replace("|","\\|")  # Escape pipe chars

            row.append(val)

        lines.append("| " + " | ".join(row) + " |")



    with open(md_path,'w',encoding='utf-8') as f:

        f.write("\n".join(lines))

    # Get relative path for display

    rel_md_path = os.path.relpath(md_path, os.getcwd())

    print_ok(f"Exported {len(data)} items => {rel_md_path}")



def md_table_to_json(md_path, json_path, field_order=None):

    # Import data from Markdown table to JSON with optional field reordering

    if not os.path.exists(md_path):

        print_err(f"No Markdown file found: {md_path}")

        return

    with open(md_path,'r',encoding='utf-8') as f:

        lines=f.read().splitlines()

    table_lines=[ln.strip() for ln in lines if ln.strip().startswith("|")]

    if len(table_lines)<2:

        print_err("No valid table rows found in the Markdown.")

        return



    header=table_lines[0]

    data_lines=table_lines[2:]

    cols=[x.strip() for x in header.strip('|').split('|')]



    new_data=[]

    for ln in data_lines:

        rowcols=[x.strip() for x in ln.strip('|').split('|')]

        if len(rowcols)!=len(cols):

            print_warn(f"Skipping mismatch line:\n{ln}")

            continue

        d={}

        for i,c in enumerate(cols):

            val=rowcols[i]

            if val.lower()=="true":

                val=True

            elif val.lower()=="false":

                val=False

            d[c]=val

        new_data.append(d)



    # Optionally reorder each dict according to field_order

    if field_order:

        new_data = [reorder_dict_keys(item, field_order) for item in new_data]



    with open(json_path,'w',encoding='utf-8') as f:

        json.dump(new_data,f,indent=2)

    # Get relative path for display

    rel_json_path = os.path.relpath(json_path, os.getcwd())

    print_ok(f"Imported {len(new_data)} items => {rel_json_path}")



# ======================

# RigDocScraper

# ======================

class RigDocScraper:

    def __init__(self, rig, show_progress=False):

        self.rig=rig

        self.show=show_progress

        self.driver=None

        self.docs_path = os.path.join(DATA_DIR, f'{rig}-a-docs.json')

        self.files_path= os.path.join(DATA_DIR, f'{rig}-b-files.json')



    @contextmanager

    def browser_session(self, download_mode=False):

        # Context manager for browser sessions with consistent configuration

        profdir = None

        try:

            # Setup browser options

            opts = Options()

            opts.add_argument("--log-level=3")



            # Configure for downloads if needed

            if download_mode:

                appdata = os.environ.get("APPDATA", os.getcwd())

                profdir = os.path.join(appdata, "chromedriver_profile")

                os.makedirs(profdir, exist_ok=True)

                opts.add_argument(f"--user-data-dir={profdir}")



                dl_dir = os.path.join(DL_DIR, self.rig)

                os.makedirs(dl_dir, exist_ok=True)



                prefs = {

                    "download.default_directory": dl_dir,

                    "download.prompt_for_download": False,

                    "download.directory_upgrade": True,

                    "safebrowsing.enabled": True

                }

                opts.add_experimental_option("prefs", prefs)



            # Initialize and configure browser

            svc = Service(ChromeDriverManager().install())

            self.driver = webdriver.Chrome(service=svc, options=opts)



            wait_time = 2 if download_mode else 5

            WebDriverWait(self.driver, wait_time).until(SleepCondition(wait_time))

            self.driver.implicitly_wait(5 if download_mode else 10)



            if self.show:

                print_sub(f"[Browser] Started {'download-enabled ' if download_mode else ''}session")



            yield self.driver



        finally:

            # Clean up resources

            if self.driver:

                if self.show:

                    print_sub("[Browser] Closing session")

                self.driver.quit()

                self.driver = None



                if download_mode and profdir:

                    try:

                        shutil.rmtree(profdir)

                    except Exception as e:

                        if self.show:

                            print_warn(f"[Browser] Failed to clean up profile: {e}")



    def fetch_docs(self, urls):

        # Scrape docs to JSON with item_include=False and item_generated_name

        if not os.path.exists(self.docs_path):

            with open(self.docs_path,'w',encoding='utf-8') as f:

                json.dump([],f)



        with self.browser_session() as driver:

            for url in urls:

                if self.show:

                    print_sub(f"[DocScrape] => {url}")



                driver.get(url)



                # Decide how long to wait (max) for initial results to load

                is_rig = ("rigsearch" in url.lower()) or ("advancedsearch" in url.lower())

                max_wait = 10 if is_rig else 5  # "upper bound" wait

                wait_scroll = 5 if is_rig else 2



                # Instead of blindly sleeping, explicitly wait for search results

                try:

                    WebDriverWait(driver, max_wait).until(

                        EC.presence_of_element_located((By.CSS_SELECTOR, "a.search-result-link"))

                    )

                except TimeoutException:

                    print_warn(f"Waited up to {max_wait}s, but no 'search-result-link' found. Proceeding anyway...")



                # Now we do the scroll loop, same as before:

                inc = 0

                prev = driver.execute_script("return document.body.scrollHeight")

                while inc < 150:

                    inc += 1

                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

                    time.sleep(wait_scroll)

                    newh = driver.execute_script("return document.body.scrollHeight")

                    if self.show:

                        print_sub(f" -> Scroll {inc}, h={newh}")

                    if newh == prev:

                        break

                    prev = newh



                # Parse the page

                soup = BeautifulSoup(driver.page_source, "html.parser")

                row_elems = soup.find_all("a", class_="search-result-link")

                rows = [(td.find_parent("tr")).find_all("td") for td in row_elems]

                if self.show:

                    print_inf(f"Found {len(rows)} doc rows.")



                new_docs = []

                for row_cells in rows:

                    doc_title   = re.sub(r'[\n\r]+',' ', row_cells[1].a.text.strip())  # doc_title

                    sub_type    = row_cells[2].get_text(strip=True)                    # doc_sub_type

                    doc_no      = row_cells[3].get_text(strip=True)                    # doc_doc_no

                    responsible = row_cells[4].get_text(strip=True)                    # doc_responsible

                    case_no     = row_cells[5].get_text(strip=True)                    # doc_case_no

                    case_desc   = row_cells[6].get_text(strip=True)                    # doc_case_description

                    revision    = row_cells[7].get_text(strip=True)                    # doc_revision

                    status      = row_cells[8].get_text(strip=True)                    # doc_status

                    dt_         = reformat_date(row_cells[9].get_text(strip=True))     # doc_date

                    doc_url     = urljoin("https://rigdoc.nov.com/", row_cells[1].a.get("href"))



                    # handle void

                    if "document-void" in (row_cells[1].a.get("class", [])) or "*VOID*" in doc_title.upper():

                        status = "(VOID)"



                    # build item_generated_name

                    doc_generated_name = (

                        f"{doc_no.upper()}_REV{revision.upper()}--{case_no.upper()}-{doc_title.title()}"

                    ).replace("  ", " ")

                    doc_generated_name_sanitized = sanitize_filename(doc_generated_name)



                    new_docs.append({

                        "item_type": "doc",

                        "item_title": doc_title,

                        "item_drawing_type": sub_type,

                        "item_doc_no": doc_no,

                        "item_responsible": responsible,

                        "item_case_no": case_no,

                        "item_case_description": case_desc,

                        "item_revision": revision,

                        "item_status": status,

                        "item_date": dt_,

                        "item_url": doc_url,

                        "item_include": False,

                        "item_generated_name": doc_generated_name_sanitized

                    })



                # Save new_docs, reorder using DEFAULT_DOC_FIELD_ORDER

                append_items(new_docs, self.docs_path, field_order=DEFAULT_DOC_FIELD_ORDER)



        # Get relative path for display

        rel_docs_path = os.path.relpath(self.docs_path, os.getcwd())

        print_ok(f"Docs => {rel_docs_path}")



    def fetch_files(self):

        # For docs with item_include=True, fetch files with item_download=False

        if not os.path.exists(self.docs_path):

            print_err(f"No docs found: {self.docs_path}")

            return

        with open(self.docs_path,'r',encoding='utf-8') as f:

            all_docs=json.load(f)



        docs_for_files=[d for d in all_docs if d.get("item_type")=="doc" and d.get("item_include")==True]

        if not docs_for_files:

            print_warn("No docs with item_include==True => No files to fetch.")

            return



        if not os.path.exists(self.files_path):

            with open(self.files_path,'w',encoding='utf-8') as f:

                json.dump([],f)



        with self.browser_session() as driver:

            for i,doc in enumerate(docs_for_files,1):

                if self.show:

                    print_sub(f"[FileScrape] doc {i}/{len(docs_for_files)} => {doc.get('item_doc_no')}")

                doc_url = doc.get("item_url","")

                if not doc_url:

                    continue

                wp_url = doc_url.replace("nov.com/documents/","nov.com/wp/documents/")

                if "nov.com/wp/documents/" not in wp_url:

                    continue



                driver.get(wp_url)

                WebDriverWait(driver,5).until(SleepCondition(5))



                soup=BeautifulSoup(driver.page_source,"html.parser")

                frows=soup.select("div.revision-panel-body div.file-detail-row")

                flinks=[]

                for row in frows:

                    lk=row.select_one("div.file-detail a")

                    if lk and lk.get("href"):

                        flinks.append(urljoin("https://rigdoc.nov.com/", lk["href"]))



                doc_generated_name = doc.get("item_generated_name","DOC_UNKNOWN")

                new_files=[]

                for idx,flink in enumerate(flinks,1):

                    fapi=flink.replace("/_download","")

                    driver.get(fapi)

                    WebDriverWait(driver,0.5).until(SleepCondition(0.5))

                    raw=BeautifulSoup(driver.page_source,"html.parser").get_text(strip=True)

                    info=json.loads(raw)



                    fext=info.get("extension","")

                    fsz =info.get("filesize",0)

                    fsmb=f"{(fsz/(1024*1024)):.2f} MB"

                    ftitle=info.get("title","").strip()



                    # build item_generated_name

                    file_generated_name = (f"{doc_generated_name}.id.{idx}-{len(flinks)}.{ftitle.title()}").replace("  "," ")

                    if fext and not file_generated_name.lower().endswith(f".{fext.lower()}"):

                        file_generated_name = f"{file_generated_name}{fext.lower()}"

                    file_generated_name_sanitized = sanitize_filename(file_generated_name)



                    new_files.append({

                        "item_type": "file",

                        "item_title": ftitle,

                        "item_file_id": f"{len(flinks)}->({idx}/{len(flinks)}) FID:{info.get('fileId','-')}",

                        "item_file_ext": f"{fext.lower()}" if fext else "",

                        "item_file_size": fsmb,

                        "item_date": reformat_date(info.get("insertedDate","")),

                        "item_revision": doc.get("item_revision",""),

                        "item_responsible": info.get("insertedBy",""),

                        "item_status": info.get("lastChangedDate") or "-",

                        "item_case_no": doc.get("item_case_no",""),

                        "item_doc_no": doc.get("item_doc_no",""),

                        "item_drawing_type": doc.get("item_drawing_type",""),

                        "item_case_description": doc.get("item_case_description",""),

                        "item_url": flink,

                        "item_download": False,

                        "item_generated_name": file_generated_name_sanitized

                    })

                # Save new_files, reorder with DEFAULT_FILE_FIELD_ORDER

                append_items(new_files, self.files_path, field_order=DEFAULT_FILE_FIELD_ORDER)



        # Get relative path for display

        rel_files_path = os.path.relpath(self.files_path, os.getcwd())

        print_ok(f"Files => {rel_files_path}")



    def download_files(self):

        # Download files with item_download=True using item_generated_name for paths

        if not os.path.exists(self.files_path):

            print_err(f"No files JSON found at {self.files_path}")

            return

        with open(self.files_path, 'r', encoding='utf-8') as f:

            data = json.load(f)



        to_download = [x for x in data if x.get("item_type") == "file" and x.get("item_download") == True]

        if not to_download:

            print_warn("No files with item_download==True.")

            return



        download_dir = os.path.join(DL_DIR, self.rig)



        print_inf(f"Will download {len(to_download)} files => {download_dir}")



        with self.browser_session(download_mode=True) as driver:

            for i, fitem in enumerate(to_download, 1):

                furl = fitem.get("item_url", "")

                if not furl:

                    continue



                # item_generated_name might contain subfolder paths

                base_name = fitem.get("item_generated_name", f"file_{i}")

                if not base_name:

                    base_name = f"file_{i}"



                # Sanitize the entire base_name while preserving path separators

                base_name = sanitize_filename(base_name)



                parts = base_name.split("/")



                # Handle filename and extension

                if parts:

                    last_part = parts[-1]

                    ext = fitem.get("item_file_ext", "").lower()



                    # Add extension if needed

                    if ext and not last_part.lower().endswith(f"{ext}"):

                        ext = f".{ext}" if not ext.startswith(".") else ext

                        final_filename = last_part + ext

                    else:

                        final_filename = last_part



                    subfolders = parts[:-1]

                else:

                    # Fallback for empty path

                    ext = fitem.get("item_file_ext", "")

                    ext = f".{ext}" if ext and not ext.startswith(".") else ext

                    final_filename = f"file_{i}{ext}"

                    subfolders = []



                # create subfolders

                final_subdir = os.path.join(download_dir, *subfolders)

                os.makedirs(final_subdir, exist_ok=True)

                final_destination = os.path.join(final_subdir, final_filename)



                if os.path.exists(final_destination):

                    print_ok(f'Skipped (already exists): "{final_destination}"')

                    continue



                if self.show:

                    print_sub("-" * 80)

                    print_inf(f'[Download {i}/{len(to_download)}] => "{final_filename}"')



                start = timeit.default_timer()

                existing_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f)))

                driver.get(furl)

                time.sleep(1)

                done = False

                while (timeit.default_timer() - start) < 30:

                    time.sleep(1)

                    new_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f))) - existing_files

                    if new_files:

                        candidate = list(new_files)[0]

                        ext2 = os.path.splitext(candidate)[1].lower()

                        # Wait for .tmp or .crdownload to finalize

                        if ext2 not in [".tmp", ".crdownload"]:

                            src = os.path.join(download_dir, candidate)

                            try:

                                os.rename(src, final_destination)

                                done = True

                                print_ok(f'-> Downloaded "{final_destination}"')

                                break

                            except Exception as e:

                                print_warn(f'Failed to rename: {e}')

                if not done:

                    print_warn(f'Timed out: "{final_destination}"')



        print_inf("All file downloads attempted.")



# ======================

# Main workflow

# ======================

def run_script(cfg):

    # Initialize from config

    rig = cfg.get("rig_number", "R9999")

    urls = cfg.get("search_urls", [])

    sp = cfg.get("show_progress", False)

    filters = cfg.get("filters", [])



    scraper = RigDocScraper(rig, show_progress=sp)



    while True:

        doc_json = os.path.join(DATA_DIR, f'{rig}-a-docs.json')

        doc_md = os.path.join(DATA_DIR, f'{rig}-a-docs.md')

        file_json = os.path.join(DATA_DIR, f'{rig}-b-files.json')

        file_md = os.path.join(DATA_DIR, f'{rig}-b-files.md')



        # Function to configure sequential filter chain

        def configure_filters():



            while True:

                # Show current filters

                print_inf("\nCurrent filter chain:")

                if not filters:

                    print_sub("  No filters configured")

                else:

                    for i, f in enumerate(filters):

                        status = "ENABLED" if f.get("enabled", False) else "DISABLED"

                        type_str = f.get("type", "unknown")

                        pattern = f.get("pattern", "")

                        field = f.get("field", "")

                        value = f.get("value", True)

                        comment = f.get("comment", "")



                        # Format pattern for display

                        if isinstance(pattern, list):

                            pattern_str = f"[{', '.join(pattern)}]"

                        else:

                            pattern_str = f"'{pattern}'"



                        # Get match field (defaults to item_generated_name if not specified)

                        match_field = f.get("match_field", "item_generated_name")

                        match_field_str = f" in {match_field}" if match_field != "item_generated_name" else ""



                        color_fn = print_ok if f.get("enabled", False) else print_sub

                        color_fn(f"  [{i+1}] {status} - {type_str}: {pattern_str}{match_field_str} → {field}={value}")

                        if comment:

                            print_sub(f"      {comment}")



                # Menu options

                print_inf("\nFilter options:")

                print_inf("  [a] Add new filter")

                print_inf("  [e] Edit filter")

                print_inf("  [d] Delete filter")

                print_inf("  [t] Toggle filter")

                print_inf("  [m] Move filter")

                print_inf("  [s] Save and return")



                choice = input("\nEnter option: ").strip().lower()



                if choice == 'a':

                    # Add new filter

                    print_inf("\nAdd new filter:")

                    filter_type = input("  Type (docs/files): ").strip().lower()

                    if filter_type not in ['docs', 'files']:

                        print_err("  Invalid type. Must be 'docs' or 'files'")

                        continue



                    pattern_input = input("  Pattern (e.g., *g0001*, *.pdf) or comma-separated list: ").strip()

                    if not pattern_input:

                        print_err("  Pattern cannot be empty")

                        continue



                    # Handle comma-separated list of patterns

                    if "," in pattern_input:

                        pattern = [p.strip() for p in pattern_input.split(",") if p.strip()]

                    else:

                        pattern = pattern_input



                    match_field = input(f"  Field to match against [item_generated_name]: ").strip()

                    if not match_field:

                        match_field = 'item_generated_name'



                    field = input(f"  Field to set ({'item_include' if filter_type == 'docs' else 'item_download'}): ").strip()

                    if not field:

                        field = 'item_include' if filter_type == 'docs' else 'item_download'



                    value_input = input(f"  Value to set (true/false) [true]: ").strip().lower()

                    value = False if value_input in ['false', 'f', 'no', 'n', '0'] else True



                    comment = input("  Comment (optional): ").strip()



                    new_filter = {

                        "type": filter_type,

                        "enabled": True,

                        "pattern": pattern,

                        "match_field": match_field,

                        "field": field,

                        "value": value,

                        "comment": comment

                    }



                    filters.append(new_filter)

                    print_ok("  Filter added")



                elif choice == 'e':

                    # Edit filter

                    if not filters:

                        print_err("  No filters to edit")

                        continue



                    idx_input = input(f"  Filter number to edit (1-{len(filters)}): ").strip()

                    try:

                        idx = int(idx_input) - 1

                        if idx < 0 or idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid filter number")

                        continue



                    f = filters[idx]

                    print_inf(f"\nEditing filter #{idx+1}:")



                    filter_type = input(f"  Type (docs/files) [{f.get('type', '')}]: ").strip().lower()

                    if filter_type and filter_type in ['docs', 'files']:

                        f['type'] = filter_type



                    # Format current pattern for display

                    current_pattern = f.get('pattern', '')

                    if isinstance(current_pattern, list):

                        current_pattern_str = ", ".join(current_pattern)

                    else:

                        current_pattern_str = current_pattern



                    pattern_input = input(f"  Pattern [{current_pattern_str}]: ").strip()

                    if pattern_input:

                        # Handle comma-separated list of patterns

                        if "," in pattern_input:

                            f['pattern'] = [p.strip() for p in pattern_input.split(",") if p.strip()]

                        else:

                            f['pattern'] = pattern_input



                    match_field = input(f"  Field to match against [{f.get('match_field', 'item_generated_name')}]: ").strip()

                    if match_field:

                        f['match_field'] = match_field

                    elif 'match_field' not in f:

                        f['match_field'] = 'item_generated_name'



                    field = input(f"  Field to set [{f.get('field', '')}]: ").strip()

                    if field:

                        f['field'] = field



                    value_input = input(f"  Value (true/false) [{f.get('value', True)}]: ").strip().lower()

                    if value_input:

                        f['value'] = False if value_input in ['false', 'f', 'no', 'n', '0'] else True



                    comment = input(f"  Comment [{f.get('comment', '')}]: ").strip()

                    if comment or comment == '':  # Allow clearing comment

                        f['comment'] = comment



                    print_ok("  Filter updated")



                elif choice == 'd':

                    # Delete filter

                    if not filters:

                        print_err("  No filters to delete")

                        continue



                    idx_input = input(f"  Filter number to delete (1-{len(filters)}): ").strip()

                    try:

                        idx = int(idx_input) - 1

                        if idx < 0 or idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid filter number")

                        continue



                    del filters[idx]

                    print_ok("  Filter deleted")



                elif choice == 't':

                    # Toggle filter

                    if not filters:

                        print_err("  No filters to toggle")

                        continue



                    idx_input = input(f"  Filter number to toggle (1-{len(filters)}): ").strip()

                    try:

                        idx = int(idx_input) - 1

                        if idx < 0 or idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid filter number")

                        continue



                    filters[idx]['enabled'] = not filters[idx].get('enabled', False)

                    status = "enabled" if filters[idx]['enabled'] else "disabled"

                    print_ok(f"  Filter {status}")



                elif choice == 'm':

                    # Move filter

                    if len(filters) < 2:

                        print_err("  Need at least 2 filters to move")

                        continue



                    from_idx_input = input(f"  Filter number to move (1-{len(filters)}): ").strip()

                    try:

                        from_idx = int(from_idx_input) - 1

                        if from_idx < 0 or from_idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid filter number")

                        continue



                    to_idx_input = input(f"  New position (1-{len(filters)}): ").strip()

                    try:

                        to_idx = int(to_idx_input) - 1

                        if to_idx < 0 or to_idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid position")

                        continue



                    if from_idx == to_idx:

                        print_sub("  No change needed")

                        continue



                    # Move the filter

                    filter_to_move = filters.pop(from_idx)

                    filters.insert(to_idx, filter_to_move)

                    print_ok(f"  Filter moved from position {from_idx+1} to {to_idx+1}")



                elif choice == 's':

                    # Save and return

                    break



                else:

                    print_err("  Invalid option")



            return True



        steps = [

            ("Change search parameters", None),

            ("Configure filter chain", configure_filters),

            ("Fetch docs (scrape initial data)", lambda: scraper.fetch_docs(urls)),

            ("Export docs (to Markdown for editing)", lambda: json_to_md_table(

                doc_json, doc_md, field_order=DEFAULT_DOC_FIELD_ORDER, filter_chain=filters

            )),

            ("Import updated doc data", lambda: md_table_to_json(doc_md, doc_json, field_order=DEFAULT_DOC_FIELD_ORDER)),

            ("Fetch files (prepare files for download)", scraper.fetch_files),

            ("Export files (to Markdown for editing)", lambda: json_to_md_table(

                file_json, file_md, field_order=DEFAULT_FILE_FIELD_ORDER, filter_chain=filters

            )),

            ("Import updated file data", lambda: md_table_to_json(file_md, file_json, field_order=DEFAULT_FILE_FIELD_ORDER)),

            ("Download files", scraper.download_files),

        ]



        print("\nChoose procedure(s) to run (space/comma-separated numbers, or q to quit):")

        for i, (desc, _) in enumerate(steps):

            print(f"  [{i}] {desc}")



        choice = input("Your choice: ").strip()



        if choice.lower() == 'q':

            print("Exiting.")

            break



        try:

            indices = [int(x.strip()) for x in choice.replace(',', ' ').split() if x.strip().isdigit()]

        except (ValueError, IndexError):

            print("Invalid selection. Please try again.")

            continue



        for i in indices:

            if i == 0:

                rig_input = input(f"  rig_number ({rig}): ").strip()

                if rig_input:

                    rig = rig_input



                urls_input = input(f"  search_urls ({urls}): ").strip()

                if urls_input:

                    urls = [u.strip() for u in urls_input.split(',') if u.strip()]



                # Create new scraper with updated configuration

                scraper = RigDocScraper(rig, show_progress=sp)

            elif 0 < i < len(steps):

                desc, action = steps[i]

                print_inf(f"\nRunning: {desc}")

                action()



        print_inf("\nSelected steps completed.\n")



if __name__=="__main__":

    run_script(CONFIG)

```


## Message 8

**Highlighting key features**  
RigOfficeDownloader streamlines documents retrieval from NOV RigDoc with a clear 7-step workflow, customizable filters, and single-run or step-based setup.

## Message 9

```markdown
# RigOfficeDownloader

RigOfficeDownloader automates retrieving engineering documents from RigOffice by following a clear seven-step flow in a single script. It supports both automated filtering and manual review (via Markdown edits), ensuring minimal clutter and maximum efficiency.

---

## Core Workflow (Single-Run or Step-by-Step)
1. **Fetch Docs**  
   Scrapes document metadata into `<rig>-a-docs.json` (all `item_include=False` by default).
2. **Export Docs**  
   Converts docs to `<rig>-a-docs.md` for easy editing of `item_include=true` where needed.
3. **Import Updated Docs**  
   Reads the edited Markdown back into JSON.
4. **Fetch Files**  
   For included docs, scrapes file metadata into `<rig>-b-files.json`.
5. **Export Files**  
   Outputs file info to `<rig>-b-files.md` so you can mark `item_download=true`.
6. **Import Updated Files**  
   Reads the Markdown changes into JSON again.
7. **Download Files**  
   Saves selected files by `item_generated_name`, preserving any subfolder paths (`/`).

---

## Interactive Menu
You can run each step individually or execute everything in one go. The menu also lets you:
- Update rig numbers and search URLs on the fly
- Configure a chain of filters to auto-include or skip items

---

## Highlights
- **Markdown Edits**: Human-friendly toggling of `item_include` / `item_download`
- **Subfolder Support**: Slash (`/`) in `item_generated_name` becomes nested folders
- **Single-File Simplicity**: All logic in a single Python script (~500 lines)
- **Smart Scraping**: Selenium with explicit waits, reducing timeouts and duplication
- **Custom Field Orders**: JSON/Markdown columns tailored to user needs

---

## Quick Start
1. **Install**: `pip install -r requirements.txt`
2. **Run**: `python RigOfficeDownloader-v4.py`
3. **Follow Prompts**: Choose steps, edit Markdown as needed
4. **Outputs**: Finds your data in `outputs/data` and your files in `outputs/downloads`

For advanced details, see the inline comments or the `memory-bank` folder. Enjoy clean, automated doc retrieval without unneeded complexity!
```

## Message 10

please convert this into a markdown format as README.md:

  "combined_result": {

    "README.md": {

      "Status": "Work in progress",

      "Concept_and_Process": {

        "Purpose": "Automate and streamline retrieval of project documentation from NOV's RigOffice for 3D engineers.",

        "Source_Spreadsheet": "\"R4511 - Internkontroll (*********-Rev.0).xlsx\"",

        "Directory_Structure": [

          "C:\\Vault\\Norway Simulation\\Rigs\\R4511-R5385-StenaIceMAX\\docs",

          "└── R4511-Internkontroll(*********-Rev.0).xlsx"

        ]

      },

      "Essential_Info_Table": [

        ["Case No", "Equipment", "GA drawing", "GA rev.", "Verified by", "Comment"],

        ["EQ-28209-104A", "Cylinder Hoisting Rig, 1250st, 48m", "********-GAD", "01", "", "29273-106"],

        ["EQ-28209-104A", "Sheave Cluster Cylinder Rig 1250", "********-GAD", "02", "", "29273-106"],

        ["EQ-28209-106A", "Top Drive, 1250t AC", "19140396-GAD", "01", "", "29273-106"],

        ["EQ-28209-120A", "Power Slip 1500 ton", "DD-10141101-605", "02", "", "29273-106"],

        ["V6056", "Iron Roughneck-Hydratong MPT-200", "V6056-D1100-G0001", "5", "", "29273-106"],

        ["V6051", "Tubular Chute, Main", "V6051-D1195-G0002", "2", "", "29273-107"],

        ["V6045", "Fingerboards", "V6045-D1202-G0001", "03A", "", "29273-107"],

        ["V6042", "Hydraracker IV, Main", "V6042-D1213-G0001", "0", "", "29273-107"],

        ["V6054", "Pipe guide, main under drillfloor", "V6054-D1194-G0002", "3", "", "29273-107"],

        ["EQ-28209-103A", "Elevated Backup Tong; EBT-150", "1906283-GAD", "03", "", "29273-107"]

      ],

      "Example_RigOffice_Search_URLs": {

        "Advanced": [

          "https://rigdoc.nov.com/search/advancedsearch?q=",

          "EQ-28209-104A → &CaseNo=EQ-28209-104A&DocTypeId=66"

        ],

        "Simple": [

          "https://rigdoc.nov.com/search?q=",

          "\"********-GAD\" → ********-GAD&CaseType=Equipment",

          "...",

          "\"29273-106\" → 29273-106&CaseType=Task / Deliverable"

        ]

      },

      "Intent": "Automate and streamline RigOffice documentation retrieval so 3D engineers focus on modeling, not manual collection.",

      "Key_Project_Aspects": {

        "Primary_Problem": "Engineers waste time with inefficient documentation gathering; manual prep is bottleneck.",

        "Solution": "Python utility scrapes, extracts, downloads, and organizes documentation based on user-selected criteria.",

        "Current_State": "Prototype with 3-step workflow, JSON intermediates, user intervention at key points, progress feedback.",

        "Critical_Next_Steps": [

          "Separate JSON outputs for search types",

          "Implement file hash checking (avoid redundant downloads)",

          "Improve progress feedback on scraping"

        ],

        "Technical_Pattern": "Single-script modular (Selenium for web automation, JSON, stepwise updates, incremental/controlled workflow).",

        "Success_Metrics": [

          "75%+ reduction in docs gathering time",

          "Reliable doc retrieval",

          "Organized output streamlining workflow",

          "Support broad and targeted searches"

        ]

      },

      "Utility_Workflow": [

        "1. Fetch Documents: Scrape metadata from search URLs; save to <rig>-a-docs.json (outputs/data); all item_include=False; generate unique item_generated_name.",

        "2. Export Docs: JSON → Markdown table; user reviews/sets item_include=true.",

        "3. Import Updates: Markdown → JSON; record inclusion/exclusion.",

        "4. Fetch Files: For item_include=true, scrape file metadata; save to <rig>-b-files.json; item_download=False.",

        "5. Export Files: JSON → Markdown; user reviews/sets item_download=true.",

        "6. Import File Updates: Markdown → JSON; finalize download selections.",

        "7. Download Files: Download item_download=true files; name using item_generated_name; directory: outputs/downloads/<rig>; auto subfolder creation (by '/').",

        "Interactive Menu: Steps can be run individually; user updates rig/searches as needed."

      ],

      "Key_Features": [

        "Automatic document/file scraping",

        "Review and edit via Markdown tables",

        "Structured file naming",

        "Subfolder organization",

        "Deduplication",

        "Configurable export fields"

      ],

      "Technical_Implementation": [

        "Selenium (Chrome WebDriver) automation",

        "Smart wait/page load strategies",

        "Managed browser sessions",

        "Progress logging/feedback",

        "Filename/path sanitization"

      ]

    }

  }

}

## Message 11

**Transposing and structuring**

I’m transforming JSON data into markdown for README.md. This involves segmenting it into areas like Status, Concept and Process, and Technical Implementation, ensuring readability and coherence.

**Structuring the README**

I'm setting up the markdown with main headings like README, Status, Concept and Process, and Essential Info Table. Each section will present details or structured data.

## Message 12

```markdown
# README

## Status
**Work in progress**

## Concept and Process
- **Purpose**: Automate and streamline retrieval of project documentation from NOV's RigOffice for 3D engineers.  
- **Source Spreadsheet**: `"R4511 - Internkontroll (*********-Rev.0).xlsx"`  
- **Directory Structure**:
  ```
  C:\Vault\Norway Simulation\Rigs\R4511-R5385-StenaIceMAX\docs
  └── R4511-Internkontroll(*********-Rev.0).xlsx
  ```

## Essential Info Table

| Case No         | Equipment                                 | GA drawing             | GA rev. | Verified by | Comment    |
|-----------------|-------------------------------------------|------------------------|---------|------------|------------|
| EQ-28209-104A   | Cylinder Hoisting Rig, 1250st, 48m        | ********-GAD           | 01      |            | 29273-106  |
| EQ-28209-104A   | Sheave Cluster Cylinder Rig 1250          | ********-GAD           | 02      |            | 29273-106  |
| EQ-28209-106A   | Top Drive, 1250t AC                       | 19140396-GAD           | 01      |            | 29273-106  |
| EQ-28209-120A   | Power Slip 1500 ton                       | DD-10141101-605        | 02      |            | 29273-106  |
| V6056           | Iron Roughneck-Hydratong MPT-200          | V6056-D1100-G0001      | 5       |            | 29273-106  |
| V6051           | Tubular Chute, Main                       | V6051-D1195-G0002      | 2       |            | 29273-107  |
| V6045           | Fingerboards                              | V6045-D1202-G0001      | 03A     |            | 29273-107  |
| V6042           | Hydraracker IV, Main                      | V6042-D1213-G0001      | 0       |            | 29273-107  |
| V6054           | Pipe guide, main under drillfloor         | V6054-D1194-G0002      | 3       |            | 29273-107  |
| EQ-28209-103A   | Elevated Backup Tong; EBT-150             | 1906283-GAD            | 03      |            | 29273-107  |

## Example RigOffice Search URLs

**Advanced:**
- `https://rigdoc.nov.com/search/advancedsearch?q=`
- `EQ-28209-104A → &CaseNo=EQ-28209-104A&DocTypeId=66`

**Simple:**
- `https://rigdoc.nov.com/search?q=`
- `"********-GAD" → ********-GAD&CaseType=Equipment`
- `"29273-106" → 29273-106&CaseType=Task / Deliverable`

## Intent
Automate and streamline RigOffice documentation retrieval so 3D engineers can focus on modeling, not manual collection.

## Key Project Aspects
- **Primary Problem**: Engineers waste time due to inefficient document gathering; manual prep bottlenecks the workflow.  
- **Solution**: A Python utility that scrapes, extracts, downloads, and organizes documentation based on user-selected criteria.  
- **Current State**: Prototype with a 3-step workflow, JSON intermediates, user intervention at key points, and progress feedback.  
- **Critical Next Steps**:
  1. Separate JSON outputs for different search types  
  2. Implement file hash checking to avoid redundant downloads  
  3. Improve progress feedback during scraping  
- **Technical Pattern**: Single-script modular approach using Selenium, JSON, stepwise updates, and an incremental/controlled workflow.  
- **Success Metrics**:
  - 75%+ reduction in documentation gathering time  
  - Reliable doc retrieval  
  - Organized output that streamlines the modeling workflow  
  - Broad and targeted search support  

## Utility Workflow

1. **Fetch Documents**:  
   Scrape metadata from search URLs into `<rig>-a-docs.json`, setting `item_include=False` initially and generating `item_generated_name`.
2. **Export Docs**:  
   Convert that JSON to `<rig>-a-docs.md` for easy human edits (flagging `item_include=true`).
3. **Import Updates**:  
   Bring the edited Markdown back into JSON, reflecting inclusion choices.
4. **Fetch Files**:  
   For docs marked `item_include=true`, scrape file metadata to `<rig>-b-files.json` (`item_download=false` initially).
5. **Export Files**:  
   Convert files JSON to `<rig>-b-files.md`; user sets `item_download=true` where needed.
6. **Import File Updates**:  
   Markdown → JSON once more, finalizing download choices.
7. **Download Files**:  
   Download selected files into `outputs/downloads/<rig>`, named from `item_generated_name` (with subfolder creation if a slash is present).

**Interactive Menu**: Steps can be run individually; rig/search parameters can be updated on the fly.

## Key Features
- Automatic document/file scraping  
- Review and edit via Markdown tables  
- Structured file naming (with subfolder support)  
- Deduplication logic  
- Configurable export fields  

## Technical Implementation
- Selenium (Chrome WebDriver) for web automation  
- Smart wait and page-load strategies  
- Managed browser sessions  
- Incremental progress logging  
- Filename/path sanitization  
```


## Message 13

please convert this into a markdown format as README.md:

{
  "combined_result": {
    "README.md": [
      "# RigDocScraper – Rig R5225",
      "",
      "## Overview",
      "Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) for NOV Rig R5225 from rigdoc.nov.com. Use case: curate, review, and batch-download rig-related documents and technical files.",
      "",
      "## Prerequisites",
      "- Python 3.8+",
      "- Required packages:",
      "  - selenium, beautifulsoup4, colorama, dateutil, ansimarkup, webdriver-manager",
      "- Chrome browser installed",
      "- ChromeDriver (managed via webdriver-manager)",
      "```bash",
      "pip install selenium beautifulsoup4 colorama python-dateutil ansimarkup webdriver-manager",
      "```",
      "",
      "## Directory Structure",
      "- `outputs/` (BASE_OUTPUT): All results stored here",
      "- `outputs/data/` (DATA_DIR): Document and file metadata (JSON/MD)",
      "- `outputs/downloads/` (DL_DIR): Downloaded PDF and file outputs",
      "",
      "## Configuration",
      "- Edit parameters in `CONFIG` at script start:",
      "  - `rig_number`: e.g., \"R5225.020\"",
      "  - `search_urls`: list of document search URLs (`PROJECTINFO_GAD`)",
      "  - `filters`: filter chain to include/exclude docs/files (see below)",
      "- Example patterns:",
      "  - `FILTERS_SIMULATOR_PATTERNS_INCLUDE`: doc types to include (e.g. \"*DRILL*FLOOR*\")",
      "  - `FILTERS_SIMULATOR_PATTERNS_SKIP`: exclusion patterns",
      "```python",
      "PROJECTINFO_GAD = [ ... ]  # List of URLs",
      "CONFIG = {",
      "    'rig_number': 'R5225.020',",
      "    'search_urls': PROJECTINFO_GAD,",
      "    'filters': [ ... ]",
      "}",
      "```",
      "",
      "## Pipeline Overview",
      "1. Change search parameters (rig number, URLs)",
      "2. Configure filter chain (add, edit, delete, toggle, reorder filters)",
      "3. Fetch docs (scrape data from rigdoc.nov.com)",
      "4. Export docs to Markdown (for selection/editing)",
      "5. Import docs from Markdown (sync edited selection)",
      "6. Fetch candidate files linked to selected docs",
      "7. Export file list to Markdown (for editing/selecting files for download)",
      "8. Import updated file list from Markdown",
      "9. Download marked files (PDFs only, via Chrome)",
      "",
      "## Filter System",
      "- Filters use `pattern` matching (wildcards) on specific fields (e.g., `item_generated_name`)",
      "- Types: `docs` (for document records), `files` (for downloadable files)",
      "- Inclusion example (set True):",
      "  - `pattern`: [\"*EQUIPMENT*LAYOUT*\"]",
      "- Exclusion example (set False):",
      "  - `pattern`: [\"*VOID*\"], field: `item_case_description`",
      "",
      "## Manual Editing",
      "- Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing",
      "- Set `item_include` (docs) and `item_download` (files) fields",
      "",
      "## Running the Tool",
      "```bash",
      "python rigdocscraper.py",
      "```",
      "- Interactive menu enables step selection (numbers/comma/space-separated)",
      "- Supports adjusting parameters, filter configuration, and reviewing batch steps",
      "- Prompts will guide through editing, import/export, and download procedures",
      "",
      "## Troubleshooting",
      "- Requires functioning Chrome installation; verify webdriver-manager compatibility",
      "- Common issues: browser launch failures, login/captcha requirements, file permissions",
      "- Output logs and warnings shown in terminal; inspect `outputs/data/` for progress",
      "",
      "## References",
      "- Main resource: https://rigdoc.nov.com",
      "- For documentation and filter patterns, see comments in `rigdocscraper.py`"
    ]
  }
}
{
  "summary": "The RigDocScraper for Rig R5225 is an automated Python tool designed to efficiently scrape, filter, and download technical documents (primarily PDFs) from rigdoc.nov.com, targeting NOV Rig R5225. Users must have Python 3.8+ and several packages (including selenium and beautifulsoup4), as well as Chrome and ChromeDriver (managed by webdriver-manager). The tool organizes outputs by separating metadata and downloaded files into distinct directories. Configuration is handled at the script's start, allowing users to specify rig numbers, search URLs, and customizable filter patterns for documents and files (using wildcards for inclusion/exclusion). The workflow is interactive and modular—users can review and edit selections (via Markdown files), sync changes, and perform stepwise batch operations including document fetching, filtering, exporting, and downloading. Troubleshooting tips cover browser setup and permissions, with references to help users understand filter patterns and script usage."
}

## Message 14

**Creating the JSON**

I’m assembling a JSON object featuring "combined_result" with an "README.md" array and a "summary" field, reflecting the user's format requirements.

**Confirming structure clarity**

OK, let me see. I’m thinking through presenting the "README.md" lines and the summary in a single, top-level JSON object, ensuring it aligns with the provided format.

## Message 15

```markdown
# RigDocScraper – Rig R5225

## Overview
Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) for NOV Rig R5225 from rigdoc.nov.com. Use case: curate, review, and batch-download rig-related documents and technical files.

## Prerequisites
- Python 3.8+
- Required packages:
  - selenium, beautifulsoup4, colorama, dateutil, ansimarkup, webdriver-manager
- Chrome browser installed
- ChromeDriver (managed via webdriver-manager)
```bash
pip install selenium beautifulsoup4 colorama python-dateutil ansimarkup webdriver-manager
```

## Directory Structure
- `outputs/` (BASE_OUTPUT): All results stored here  
- `outputs/data/` (DATA_DIR): Document and file metadata (JSON/MD)  
- `outputs/downloads/` (DL_DIR): Downloaded PDF and file outputs  

## Configuration
- Edit parameters in `CONFIG` at script start:
  - `rig_number`: e.g., "R5225.020"
  - `search_urls`: list of document search URLs (`PROJECTINFO_GAD`)
  - `filters`: filter chain to include/exclude docs/files (see below)
- Example patterns:
  - `FILTERS_SIMULATOR_PATTERNS_INCLUDE`: doc types to include (e.g. "*DRILL*FLOOR*")
  - `FILTERS_SIMULATOR_PATTERNS_SKIP`: exclusion patterns
```python
PROJECTINFO_GAD = [ ... ]  # List of URLs
CONFIG = {
    'rig_number': 'R5225.020',
    'search_urls': PROJECTINFO_GAD,
    'filters': [ ... ]
}
```

## Pipeline Overview
1. Change search parameters (rig number, URLs)  
2. Configure filter chain (add, edit, delete, toggle, reorder filters)  
3. Fetch docs (scrape data from rigdoc.nov.com)  
4. Export docs to Markdown (for selection/editing)  
5. Import docs from Markdown (sync edited selection)  
6. Fetch candidate files linked to selected docs  
7. Export file list to Markdown (for editing/selecting files for download)  
8. Import updated file list from Markdown  
9. Download marked files (PDFs only, via Chrome)  

## Filter System
- Filters use `pattern` matching (wildcards) on specific fields (e.g., `item_generated_name`)
- Types: `docs` (for document records), `files` (for downloadable files)
- Inclusion example (set True):
  - `pattern`: ["*EQUIPMENT*LAYOUT*"]
- Exclusion example (set False):
  - `pattern`: ["*VOID*"], field: `item_case_description`

## Manual Editing
- Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing
- Set `item_include` (docs) and `item_download` (files) fields

## Running the Tool
```bash
python rigdocscraper.py
```
- Interactive menu enables step selection (numbers/comma/space-separated)
- Supports adjusting parameters, filter configuration, and reviewing batch steps
- Prompts will guide through editing, import/export, and download procedures

## Troubleshooting
- Requires functioning Chrome installation; verify webdriver-manager compatibility
- Common issues: browser launch failures, login/captcha requirements, file permissions
- Output logs and warnings shown in terminal; inspect `outputs/data/` for progress

## References
- Main resource: [https://rigdoc.nov.com](https://rigdoc.nov.com)
- For documentation and filter patterns, see comments in `rigdocscraper.py`

---

## Summary
The RigDocScraper for Rig R5225 is an automated Python tool designed to efficiently scrape, filter, and download technical documents (primarily PDFs) from rigdoc.nov.com, targeting NOV Rig R5225. Users must have Python 3.8+ and several packages (including selenium and beautifulsoup4), as well as Chrome and ChromeDriver (managed by webdriver-manager). The tool organizes outputs by separating metadata and downloaded files into distinct directories. Configuration is handled at the script's start, allowing users to specify rig numbers, search URLs, and customizable filter patterns for documents and files (using wildcards for inclusion/exclusion). The workflow is interactive and modular—users can review and edit selections (via Markdown files), sync changes, and perform stepwise batch operations including document fetching, filtering, exporting, and downloading. Troubleshooting tips cover browser setup and permissions, with references to help users understand filter patterns and script usage.
```

## Message 16

Please create a single README.md based on taking the best from each part of these variations:



    # Dir `readme_variations`



    ### File Structure



    ```

    ├── README_v1.md

    ├── README_v2.md

    ├── README_v3.md

    ├── README_v4.md

    ├── README_v5.md

    ├── README_v6.md

    └── README_v7.md

    ```



    ---



    #### `README_v1.md`



    ```markdown

        # RigOfficeDownloader

        

        ## Overview

        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.

        

        ## Key Features

        - Three-stage workflow: Documents -> Files -> Downloads

        - Interactive menu for flexible execution

        - User control points via Markdown interfaces

        - Smart file organization with subfolder support

        - Configurable filter chains

        

        ## Setup & Usage

        1. Run `py_venv_init.bat` to create the Python environment

        2. Run `RigOfficeDownloader-v4.bat` to start the application

        3. Use the interactive menu to configure and execute workflow steps

        

        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Preserves document context and relationships

        

        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser (for Selenium automation)

    ```



    ---



    #### `README_v2.md`



    ```markdown

        # RigOfficeDownloader

        

        ## The Problem

        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:

        - Tedious and repetitive

        - Error-prone

        - A poor use of skilled engineering time

        

        ## The Solution

        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:

        1. **Document Retrieval**: Automatically scrapes document metadata

        2. **File Metadata**: Fetches file information for selected documents

        3. **Smart Downloads**: Downloads files with intelligent naming and organization

        

        ## How It Works

        - Uses Selenium to automate web interactions with RigDoc

        - Exports data to Markdown for user review and selection

        - Applies configurable filters to pre-select relevant documents

        - Organizes downloads with consistent naming patterns

        

        ## Getting Started

        1. Run `py_venv_init.bat` to set up the environment

        2. Run `RigOfficeDownloader-v4.bat` to launch the application

        3. Follow the interactive menu prompts

        

        ## Impact

        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.

    ```



    ---



    #### `README_v3.md`



    ```markdown

        # RigOfficeDownloader

        

        ## Technical Overview

        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.

        

        ## Architecture

        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown

        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads

        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support

        

        ## Workflow Steps

        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information

        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing

        3. **Selection Import**: `md_table_to_json()` imports user selections

        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents

        5. **File Selection**: Export/import cycle for user selection of files

        6. **Download Process**: `download_files()` retrieves selected files with smart naming

        

        ## Filter Chain System

        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.

        

        ## Setup Instructions

        1. Run `py_venv_init.bat` to initialize the Python environment

        2. Run `RigOfficeDownloader-v4.bat` to execute the application

        

        ## Version History

        - v1: Basic document retrieval and download

        - v2: JSON/Markdown conversion and user selection

        - v3: Improved error handling and field organization

        - v4: Subfolder support, filter chains, field ordering

    ```



    ---



    #### `README_v4.md`



    ```markdown

        # RigOfficeDownloader

        > Automate document retrieval from NOV's RigDoc system

        

        ## What This Tool Does

        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.

        

        ## Quick Start Guide

        1. **Setup**: Run `py_venv_init.bat` to create the Python environment

        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application

        3. **Configure**: Enter your rig number and search URLs when prompted

        4. **Run**: Follow the numbered menu to execute each step of the workflow

        

        ## Workflow Steps Explained

        0. **Change Parameters**: Update rig number and search URLs

        1. **Configure Filters**: Set up automatic document/file selection rules

        2. **Fetch Documents**: Retrieve document metadata from RigDoc

        3. **Review Documents**: Edit the Markdown file to select which documents to process

        4. **Import Selections**: Load your document selections

        5. **Fetch Files**: Get file metadata for selected documents

        6. **Review Files**: Edit the Markdown file to select which files to download

        7. **Import File Selections**: Load your file selections

        8. **Download Files**: Retrieve the selected files

        

        ## Tips for Success

        - Use filters to automatically pre-select relevant documents

        - Review the Markdown files carefully before proceeding to the next step

        - Files will be organized in subfolders based on '/' in their generated names

        

        ## Need Help?

        Check the source code comments for detailed information about each function and workflow step.

    ```



    ---



    #### `README_v5.md`



    ```markdown

        # RigOfficeDownloader

        

        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.

        

        ```

        Documents -> Files -> Downloads

        ```

        

        ## Features

        - Three-stage workflow with user control points

        - Interactive menu for flexible execution

        - Configurable filter chains for automatic selection

        - Smart file organization with subfolder support

        - Markdown interfaces for document/file review

        

        ## Quick Start

        ```

        1. Run py_venv_init.bat

        2. Run RigOfficeDownloader-v4.bat

        3. Follow the interactive menu

        ```

        

        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Handles errors gracefully

        

        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser

    ```



    ---



    #### `README_v6.md`



    ```markdown

        # RigOfficeDownloader

        

        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.

        

        ## Key Features

        - **Three-Stage Workflow**: Document selection → File selection → Download

        - **Metadata Preservation**: Structured naming with revision/case numbers

        - **Interactive Review**: Markdown tables for manual inclusion flags

        - **Smart Organization**: Automatic subfolder creation via naming patterns

        - **Filter System**: Pattern-based inclusion/exclusion rules

        - **Browser Automation**: Smart waiting strategies and session management

        

        ## Workflow Process

        ```python

        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json

        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true

        3. Import Selections     # Update JSON with user choices

        4. Fetch Files           # Get file listings → <rig>-b-files.json

        5. Export Files MD       # <rig>-b-files.md - Set item_download=true

        6. Import File Choices   # Update file selections

        7. Download Files        # Auto-organized with subfolder support

        ```

        

        ## Configuration (CONFIG Section)

        ```python

        {

            "rig_number": "R0000.020",  # Target rig ID

            "search_urls": [            # Predefined search templates

                "https://rigdoc.nov.com/search/rigsearch?q=...",

                "https://rigdoc.nov.com/search/rigsearch?q=..."

            ],

            "filters": [                # Sequential processing rules

                {

                    "type": "docs",     # Apply to documents/files

                    "pattern": "*DRILL*FLOOR*",  # Glob-style matching

                    "field": "item_include",     # Field to modify

                    "value": True       # Set True/False based on match

                }

            ]

        }

        ```

        

        ## Setup & Usage

        ```bash

        # Initialize environment

        py_venv_init.bat

        py_venv_pip_install.bat

        

        # Run modes

        RigOfficeDownloader-v4.py [rig_number] [mode]

        

        Modes:

        --auto         # Full automation

        --interactive  # Step-by-step control

        --config       # Modify search templates/filters

        ```

        

        ## Key Configuration Patterns

        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)

        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)

        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern

        

        ## Requirements

        - Chrome Browser + ChromeDriver

        - Active RigDoc credentials

        - Python 3.8+ with dependencies from requirements.txt

        - Network access to rigdoc.nov.com

        

        ## Advanced Features

        - **Path Sanitization**: Auto-clean special chars while preserving /

        - **Deduplication**: Hash-based conflict resolution

        - **Field Ordering**: Customizable JSON/Markdown columns

        - **Smart Scrolling**: Progressive page loading detection

        

        > Reduces documentation prep time by 60-75% compared to manual retrieval

        > Version 4.0 | Active development with subfolder support

    ```



    ---



    #### `README_v7.md`



    ```markdown

        

        ## Overview

        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.

        - Use case: curate, review, and batch-download rig-related documents and technical files.

        

        ## Directory Structure

        

        * `outputs/` (BASE\_OUTPUT): All results stored here

        * `outputs/data/` (DATA\_DIR): Document and file metadata (JSON/MD)

        * `outputs/downloads/` (DL\_DIR): Downloaded PDF and file outputs

        

        ## Pipeline Overview

        

        1. Change search parameters (rig number, URLs)

        2. Configure filter chain (add, edit, delete, toggle, reorder filters)

        3. Fetch docs (scrape data from rigdoc.nov.com)

        4. Export docs to Markdown (for selection/editing)

        5. Import docs from Markdown (sync edited selection)

        6. Fetch candidate files linked to selected docs

        7. Export file list to Markdown (for editing/selecting files for download)

        8. Import updated file list from Markdown

        9. Download marked files (PDFs only, via Chrome)

        

        ## Manual Editing

        

        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing

        * Set `item_include` (docs) and `item_download` (files) fields

        

        ## Running the Tool

        

        ```bash

        python rigdocscraper.py

        ```

        

        * Interactive menu enables step selection (numbers/comma/space-separated)

        * Supports adjusting parameters, filter configuration, and reviewing batch steps

        * Prompts will guide through editing, import/export, and download procedures

        

        ## Troubleshooting

        

        * Requires functioning Chrome installation; verify webdriver-manager compatibility

        * Common issues: browser launch failures, login/captcha requirements, file permissions

        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress

    ```




## Message 17

**Synthesizing variations**

Piecing together the best elements from seven readme versions, focusing on essential sections for a cohesive README.md file.

## Message 18

```markdown
# RigOfficeDownloader

> **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.

---

## 1. Overview

RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.

---

## 2. The Problem

- Engineers lose valuable hours **manually searching** and **downloading** technical documentation.  
- The repetitive process is **error-prone** and distracts from real engineering work.

---

## 3. The Solution

- **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.  
- Offers **three main stages**—document metadata retrieval, file metadata retrieval, and file downloads—each controlled by user-edited Markdown tables for fine-tuned selection.

---

## 4. Key Features

- **Three-Stage Workflow**  
  1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.  
  2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.  
  3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.

- **Interactive Menu**: Allows step-by-step or fully automated runs.  
- **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.  
- **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.  
- **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.  
- **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.

---

## 5. Workflow Steps

1. **Change Parameters**: (Optional) Update rig number, search URLs.  
2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.  
3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).  
4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.  
5. **Import Updated Docs**: Sync changes back from Markdown to JSON.  
6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).  
7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.  
8. **Import Updated Files**: Sync file selection from Markdown to JSON.  
9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.

---

## 6. Architecture & Technology

- **Core Stack**:  
  - **Python 3.6+**  
  - **Selenium** for browser automation  
  - **BeautifulSoup** for HTML parsing  
  - **JSON/Markdown** for data storage and user edits  
- **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.  
- **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.  
- **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.

---

## 7. Directory Structure

```
└── outputs/
    ├── data/            # JSON + Markdown for docs/files
    │   ├── <rig>-a-docs.json / <rig>-a-docs.md
    │   └── <rig>-b-files.json / <rig>-b-files.md
    └── downloads/       # Final downloaded files (with subfolders)
        └── <rig>/ 
```

---

## 8. Setup & Requirements

- **Windows OS**  
- **Chrome browser** installed  
- **ChromeDriver** managed by `webdriver-manager`  
- **Active RigDoc credentials**  
- Python dependencies (see `requirements.txt` or install individually):  
  ```bash
  pip install selenium beautifulsoup4 colorama python-dateutil ansimarkup webdriver-manager
  ```

### Environment Initialization
1. Run `py_venv_init.bat` to create the Python environment (optional step if you’re managing your own venv).  
2. Run `py_venv_pip_install.bat` or `pip install -r requirements.txt` to install dependencies.

---

## 9. Usage

1. **Launch**: 
   - `RigOfficeDownloader-v4.bat` or  
   - `python RigOfficeDownloader-v4.py`  
2. **Follow Prompts**:
   - Choose specific workflow steps (0-8) or do them all at once.  
   - Update rig number, search URLs, and configure filters if needed.  
3. **Edit Markdown**: 
   - Exported docs/files are in `.md` tables under `outputs/data/`.  
   - Toggle `item_include` or `item_download` as desired.  
4. **Re-run**: 
   - Import your changes, fetch new data, or proceed to downloads.

---

## 10. Filter Chain Examples

- **Inclusion** (set `item_include=true`):  
  ```json
  {
    "type": "docs",
    "pattern": "*DRILL*FLOOR*",
    "match_field": "item_generated_name",
    "field": "item_include",
    "value": true
  }
  ```
- **Exclusion** (set `item_include=false`):  
  ```json
  {
    "type": "docs",
    "pattern": "*VOID*",
    "match_field": "item_case_description",
    "field": "item_include",
    "value": false
  }
  ```
- **File Type Priority** (set `item_download=true`):  
  ```json
  {
    "type": "files",
    "pattern": "*.pdf",
    "match_field": "item_file_ext",
    "field": "item_download",
    "value": true
  }
  ```

---

## 11. Benefits & Impact

- **Efficiency**: Cuts documentation gathering time by 75%+  
- **User Control**: Interactive review points prevent unwanted downloads  
- **Consistency**: Uniform naming patterns and subfolder logic keep files organized  
- **Scalability**: Easily adapt filters and search URLs for different rigs or departments

---

## 12. Troubleshooting

- **Browser Launch Failures**: Ensure you have Chrome installed and up-to-date.  
- **Login/Captcha Issues**: Manually sign in or complete any corporate SSO steps.  
- **File Permission Errors**: Confirm write access in `outputs/`.  
- **Stalled Scraping**: Check the console for warnings about session timeouts or missing elements.  

---

## 13. Version History

- **v1**: Basic doc retrieval & download  
- **v2**: JSON/Markdown export + user selection  
- **v3**: Better error handling and field organization  
- **v4**: Subfolder support, filter chains, advanced field ordering (current release)

---

## 14. Need Help?

Check inline code comments or the `memory-bank` folder for deeper insights on each function and step. For immediate questions, refer to the **interactive menu** help prompts displayed in the console.

---
```

## Message 19

# CONTEXT



RigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).



# SOLUTION



i've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__. you will understand exactly why i mean by this as you start understanding the fundamental concepts (especially with regards to the workflow itself) utilized in the utility, it's **extremely adaptable** (the workflow/concepts/methods for how it works is *meticiously crafted*; there's *a reason for everything*).



## WORKFLOW



This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow follows these steps:



    Interactive Menu

    - The utility provides an interactive menu where the user can choose which steps to execute

    - This allows for flexibility in the workflow, enabling the user to run specific steps as needed

    - The user can also update the rig number and search URLs through this menu



    Key Features

    - Automatic document and file metadata scraping

    - User-friendly Markdown editing interface

    - Customizable file naming with item_generated_name

    - Support for subfolder organization in downloads

    - Deduplication of documents and files

    - Configurable field ordering for JSON and Markdown exports



    Technical Implementation

    - Uses Selenium with Chrome WebDriver for web scraping

    - Implements smart waiting strategies for page loading

    - Handles browser sessions with proper cleanup

    - Provides progress feedback during operations

    - Sanitizes filenames for valid paths



    1. Fetch Documents

    - The utility starts by scraping document metadata from predefined search URLs

    - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory

    - Each document entry includes metadata like title, document number, revision, etc.

    - All documents are initially marked with item_include=False

    - Each document gets an item_generated_name for better identification



    2. Export Documents to Markdown

    - The JSON data is exported to a Markdown table: <rig>-a-docs.md

    - This allows the user to easily review and edit which documents to include

    - The user is expected to edit the Markdown file and set item_include=true for desired documents



    3. Import Updated Document Data

    - After the user edits the Markdown file, the utility imports the changes back to the JSON file

    - This updates which documents are marked for file retrieval



    4. Fetch Files for Selected Documents

    - For each document with item_include=true, the utility scrapes file metadata

    - File data is saved to <rig>-b-files.json

    - Each file is initially marked with item_download=False

    - Files inherit the document's item_generated_name with additional identifiers



    5. Export Files to Markdown

    - The file data is exported to a Markdown table: <rig>-b-files.md

    - The user reviews and edits which files to download by setting item_download=true



    6. Import Updated File Data

    - After editing, the utility imports the changes back to the JSON file

    - This updates which files are marked for download



    7. Download Selected Files

    - Files with item_download=true are downloaded

    - Files are named according to their item_generated_name + extension

    - The utility supports creating subfolders based on '/' in the item_generated_name

    - Files are saved to the outputs/downloads/<rig> directory



# CONSIDERATIONS



since our company use internal github repos for all development we do this utility will also be uploaded to github's company page (accessible for everyone within the company), i want to use this as an opportunity to impress (with unique and original simplicity and elegance). although 99.99% of developers in similar situations will choose to adhere to what they perceive as the concencus best practices and restructure (organize and refactor) the utility's codebase/project/filestructure based on these "rules", **i no longer blindly adhere to established concepts**, i *build* on them through *inherent understanding*. as an example, best practices are *great* for large and complex systems, and in some cases even **neccessary** - but such standardized practices does *not apply to small utilities the same way*, small utilities can often be transformed from a working utility within a single file of <500 lines, and after transforming it to "established best practices" will transform it into ~30 files and thousands of lines of code; __I WANT TO DO THE OPPOSITE OF THIS__. i want **balance** and **cognizeant and inherent understanding**, i want to leverage my own artistic vision onto the code. i find reduction of complexity through perpetually evaluation everything through different contexts through simulating (internally within my mind) different visual representations of components/sections/files in relation to each other.



# FILESTRUCTURE



this is the current filestructure:

```

RigOfficeDownloader

└── RigOfficeDownloader_v4

    ├── outputs

    │   ├── data

    │   │   ├── R5385.010-a-docs.json

    │   │   ├── R5385.010-a-docs.md

    │   │   ├── R5385.010-b-files.json

    │   │   └── R5385.010-b-files.md

    │   └── downloads

    │       └── ...

    ├── readme_variations

    │   ├── README_v01.md

    │   ├── README_v02.md

    │   ├── README_v03.md

    │   ├── README_v04.md

    │   ├── README_v05.md

    │   ├── README_v06.md

    │   ├── README_v07.md

    │   ├── README_v08.md

    │   ├── README_v09.md

    │   ├── README_v10.md

    │   └── README_v11.md

    ├── src

    │   ├── .gitignore

    │   ├── RigOfficeDownloader-v4.bat

    │   ├── RigOfficeDownloader-v4.py

    │   └── code_guidelines.md

    ├── .gitignore

    ├── GOAL.md

    ├── README.md

    ├── RigOfficeDownloader-v4.sublime-project

    ├── py_venv_init.bat

    ├── requirements.txt

    └── understanding_the_environment.md

```



# OBJECTIVE



Please create a single README.md based on taking the best from each part of these variations:



    # Dir `readme_variations`



    ### File Structure



    ```

    ├── README_v01.md

    ├── README_v02.md

    ├── README_v03.md

    ├── README_v04.md

    ├── README_v05.md

    ├── README_v06.md

    ├── README_v07.md

    ├── README_v08.md

    ├── README_v09.md

    ├── README_v10.md

    ├── README_v11.md

    └── README_v12.md

    ```



    ---



    #### `README_v01.md`



    ```markdown

        # RigOfficeDownloader



        ## Overview

        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.



        ## Key Features

        - Three-stage workflow: Documents -> Files -> Downloads

        - Interactive menu for flexible execution

        - User control points via Markdown interfaces

        - Smart file organization with subfolder support

        - Configurable filter chains



        ## Setup & Usage

        1. Run `py_venv_init.bat` to create the Python environment

        2. Run `RigOfficeDownloader-v4.bat` to start the application

        3. Use the interactive menu to configure and execute workflow steps



        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Preserves document context and relationships



        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser (for Selenium automation)

    ```



    ---



    #### `README_v02.md`



    ```markdown

        # RigOfficeDownloader



        ## The Problem

        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:

        - Tedious and repetitive

        - Error-prone

        - A poor use of skilled engineering time



        ## The Solution

        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:

        1. **Document Retrieval**: Automatically scrapes document metadata

        2. **File Metadata**: Fetches file information for selected documents

        3. **Smart Downloads**: Downloads files with intelligent naming and organization



        ## How It Works

        - Uses Selenium to automate web interactions with RigDoc

        - Exports data to Markdown for user review and selection

        - Applies configurable filters to pre-select relevant documents

        - Organizes downloads with consistent naming patterns



        ## Getting Started

        1. Run `py_venv_init.bat` to set up the environment

        2. Run `RigOfficeDownloader-v4.bat` to launch the application

        3. Follow the interactive menu prompts



        ## Impact

        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.

    ```



    ---



    #### `README_v03.md`



    ```markdown

        # RigOfficeDownloader



        ## Technical Overview

        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.



        ## Architecture

        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown

        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads

        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support



        ## Workflow Steps

        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information

        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing

        3. **Selection Import**: `md_table_to_json()` imports user selections

        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents

        5. **File Selection**: Export/import cycle for user selection of files

        6. **Download Process**: `download_files()` retrieves selected files with smart naming



        ## Filter Chain System

        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.



        ## Setup Instructions

        1. Run `py_venv_init.bat` to initialize the Python environment

        2. Run `RigOfficeDownloader-v4.bat` to execute the application



        ## Version History

        - v1: Basic document retrieval and download

        - v2: JSON/Markdown conversion and user selection

        - v3: Improved error handling and field organization

        - v4: Subfolder support, filter chains, field ordering

    ```



    ---



    #### `README_v04.md`



    ```markdown

        # RigOfficeDownloader

        > Automate document retrieval from NOV's RigDoc system



        ## What This Tool Does

        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.



        ## Quick Start Guide

        1. **Setup**: Run `py_venv_init.bat` to create the Python environment

        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application

        3. **Configure**: Enter your rig number and search URLs when prompted

        4. **Run**: Follow the numbered menu to execute each step of the workflow



        ## Workflow Steps Explained

        0. **Change Parameters**: Update rig number and search URLs

        1. **Configure Filters**: Set up automatic document/file selection rules

        2. **Fetch Documents**: Retrieve document metadata from RigDoc

        3. **Review Documents**: Edit the Markdown file to select which documents to process

        4. **Import Selections**: Load your document selections

        5. **Fetch Files**: Get file metadata for selected documents

        6. **Review Files**: Edit the Markdown file to select which files to download

        7. **Import File Selections**: Load your file selections

        8. **Download Files**: Retrieve the selected files



        ## Tips for Success

        - Use filters to automatically pre-select relevant documents

        - Review the Markdown files carefully before proceeding to the next step

        - Files will be organized in subfolders based on '/' in their generated names



        ## Need Help?

        Check the source code comments for detailed information about each function and workflow step.

    ```



    ---



    #### `README_v05.md`



    ```markdown

        # RigOfficeDownloader



        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.



        ```

        Documents -> Files -> Downloads

        ```



        ## Features

        - Three-stage workflow with user control points

        - Interactive menu for flexible execution

        - Configurable filter chains for automatic selection

        - Smart file organization with subfolder support

        - Markdown interfaces for document/file review



        ## Quick Start

        ```

        1. Run py_venv_init.bat

        2. Run RigOfficeDownloader-v4.bat

        3. Follow the interactive menu

        ```



        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Handles errors gracefully



        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser

    ```



    ---



    #### `README_v06.md`



    ```markdown

        # RigOfficeDownloader



        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.



        ## Key Features

        - **Three-Stage Workflow**: Document selection → File selection → Download

        - **Metadata Preservation**: Structured naming with revision/case numbers

        - **Interactive Review**: Markdown tables for manual inclusion flags

        - **Smart Organization**: Automatic subfolder creation via naming patterns

        - **Filter System**: Pattern-based inclusion/exclusion rules

        - **Browser Automation**: Smart waiting strategies and session management



        ## Workflow Process

        ```python

        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json

        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true

        3. Import Selections     # Update JSON with user choices

        4. Fetch Files           # Get file listings → <rig>-b-files.json

        5. Export Files MD       # <rig>-b-files.md - Set item_download=true

        6. Import File Choices   # Update file selections

        7. Download Files        # Auto-organized with subfolder support

        ```



        ## Configuration (CONFIG Section)

        ```python

        {

            "rig_number": "R0000.020",  # Target rig ID

            "search_urls": [            # Predefined search templates

                "https://rigdoc.nov.com/search/rigsearch?q=...",

                "https://rigdoc.nov.com/search/rigsearch?q=..."

            ],

            "filters": [                # Sequential processing rules

                {

                    "type": "docs",     # Apply to documents/files

                    "pattern": "*DRILL*FLOOR*",  # Glob-style matching

                    "field": "item_include",     # Field to modify

                    "value": True       # Set True/False based on match

                }

            ]

        }

        ```



        ## Setup & Usage

        ```bash

        # Initialize environment

        py_venv_init.bat

        py_venv_pip_install.bat



        # Run modes

        RigOfficeDownloader-v4.py [rig_number] [mode]



        Modes:

        --auto         # Full automation

        --interactive  # Step-by-step control

        --config       # Modify search templates/filters

        ```



        ## Key Configuration Patterns

        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)

        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)

        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern



        ## Requirements

        - Chrome Browser + ChromeDriver

        - Active RigDoc credentials

        - Python 3.8+ with dependencies from requirements.txt

        - Network access to rigdoc.nov.com



        ## Advanced Features

        - **Path Sanitization**: Auto-clean special chars while preserving /

        - **Deduplication**: Hash-based conflict resolution

        - **Field Ordering**: Customizable JSON/Markdown columns

        - **Smart Scrolling**: Progressive page loading detection



        > Reduces documentation prep time by 60-75% compared to manual retrieval

        > Version 4.0 | Active development with subfolder support

    ```



    ---



    #### `README_v07.md`



    ```markdown



        ## Overview

        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.

        - Use case: curate, review, and batch-download rig-related documents and technical files.



        ## Directory Structure



        * `outputs/` (BASE\_OUTPUT): All results stored here

        * `outputs/data/` (DATA\_DIR): Document and file metadata (JSON/MD)

        * `outputs/downloads/` (DL\_DIR): Downloaded PDF and file outputs



        ## Pipeline Overview



        1. Change search parameters (rig number, URLs)

        2. Configure filter chain (add, edit, delete, toggle, reorder filters)

        3. Fetch docs (scrape data from rigdoc.nov.com)

        4. Export docs to Markdown (for selection/editing)

        5. Import docs from Markdown (sync edited selection)

        6. Fetch candidate files linked to selected docs

        7. Export file list to Markdown (for editing/selecting files for download)

        8. Import updated file list from Markdown

        9. Download marked files (PDFs only, via Chrome)



        ## Manual Editing



        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing

        * Set `item_include` (docs) and `item_download` (files) fields



        ## Running the Tool



        ```bash

        python rigdocscraper.py

        ```



        * Interactive menu enables step selection (numbers/comma/space-separated)

        * Supports adjusting parameters, filter configuration, and reviewing batch steps

        * Prompts will guide through editing, import/export, and download procedures



        ## Troubleshooting



        * Requires functioning Chrome installation; verify webdriver-manager compatibility

        * Common issues: browser launch failures, login/captcha requirements, file permissions

        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress

    ```



    ---



    #### `README_v08.md`



    ```markdown

        # RigOfficeDownloader



        > **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.



        ---



        ## 1. Overview



        RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.



        ---



        ## 2. The Problem



        - Engineers lose valuable hours **manually searching** and **downloading** technical documentation.

        - The repetitive process is **error-prone** and distracts from real engineering work.



        ---



        ## 3. The Solution



        - **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.

        - Offers **three main stages**—document metadata retrieval, file metadata retrieval, and file downloads—each controlled by user-edited Markdown tables for fine-tuned selection.



        ---



        ## 4. Key Features



        - **Three-Stage Workflow**

          1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.

          2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.

          3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.



        - **Interactive Menu**: Allows step-by-step or fully automated runs.

        - **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.

        - **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.

        - **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.

        - **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.



        ---



        ## 5. Workflow Steps



        1. **Change Parameters**: (Optional) Update rig number, search URLs.

        2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.

        3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).

        4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.

        5. **Import Updated Docs**: Sync changes back from Markdown to JSON.

        6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).

        7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.

        8. **Import Updated Files**: Sync file selection from Markdown to JSON.

        9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.



        ---



        ## 6. Architecture & Technology



        - **Core Stack**:

          - **Python 3.6+**

          - **Selenium** for browser automation

          - **BeautifulSoup** for HTML parsing

          - **JSON/Markdown** for data storage and user edits

        - **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.

        - **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.

        - **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.



        ---



        ## 7. Directory Structure



    ```



    ---



    #### `README_v09.md`



    ```markdown

        ## Intent

        To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.



        ## Key Project Aspects



        ### Primary Problem

        Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.



        ### Solution Approach

        A Python-based utility that:

        1. Automatically scrapes document metadata from RigOffice

        2. Extracts file information from those documents

        3. Downloads and organizes selected files based on user criteria



        ### Current State

        Functional working prototype that:

        - Uses a 3-step workflow (document metadata → file metadata → download)

        - Stores intermediate results in JSON format

        - Allows user intervention between steps

        - Provides progress feedback



        ### Critical Next Steps

        1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches

        2. **Implement file hash checking** to prevent redundant downloads

        3. **Improve progress visibility** during lengthy scraping operations



        ### Core Technical Pattern

        A single-file, modular approach using:

        - Selenium for browser automation

        - JSON for data storage

        - Three-stage processing with user control points

        - Incremental updates to avoid redundant work



        ### Key Success Metrics

        - Reduce documentation gathering time by 75%+

        - Ensure reliable retrieval of required documentation

        - Organize files in a way that streamlines workflow

        - Support both broad searches (by rig number) and targeted searches (by specific documents)

    ```



    ---



    #### `README_v10.md`



    ```markdown

        ### RigOfficeDownloader Utility Workflow



        This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:



        1. Fetch Documents

        - The utility starts by scraping document metadata from predefined search URLs

        - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory

        - Each document entry includes metadata like title, document number, revision, etc.

        - All documents are initially marked with item_include=False

        - Each document gets an item_generated_name for better identification



        2. Export Documents to Markdown

        - The JSON data is exported to a Markdown table: <rig>-a-docs.md

        - This allows the user to easily review and edit which documents to include

        - The user is expected to edit the Markdown file and set item_include=true for desired documents



        3. Import Updated Document Data

        - After the user edits the Markdown file, the utility imports the changes back to the JSON file

        - This updates which documents are marked for file retrieval



        4. Fetch Files for Selected Documents

        - For each document with item_include=true, the utility scrapes file metadata

        - File data is saved to <rig>-b-files.json

        - Each file is initially marked with item_download=False

        - Files inherit the document's item_generated_name with additional identifiers



        5. Export Files to Markdown

        - The file data is exported to a Markdown table: <rig>-b-files.md

        - The user reviews and edits which files to download by setting item_download=true



        6. Import Updated File Data

        - After editing, the utility imports the changes back to the JSON file

        - This updates which files are marked for download



        7. Download Selected Files

        - Files with item_download=true are downloaded

        - Files are named according to their item_generated_name + extension

        - The utility supports creating subfolders based on '/' in the item_generated_name

        - Files are saved to the outputs/downloads/<rig> directory



        Interactive Menu

        - The utility provides an interactive menu where the user can choose which steps to execute

        - This allows for flexibility in the workflow, enabling the user to run specific steps as needed

        - The user can also update the rig number and search URLs through this menu



        Key Features

        - Automatic document and file metadata scraping

        - User-friendly Markdown editing interface

        - Customizable file naming with item_generated_name

        - Support for subfolder organization in downloads

        - Deduplication of documents and files

        - Configurable field ordering for JSON and Markdown exports



        Technical Implementation

        - Uses Selenium with Chrome WebDriver for web scraping

        - Implements smart waiting strategies for page loading

        - Handles browser sessions with proper cleanup

        - Provides progress feedback during operations

        - Sanitizes filenames for valid paths

    ```



    ---



    #### `README_v11.md`



    ```markdown

        # RigOfficeDownloader



        ## Overview

        RigOfficeDownloader is an automation tool designed to streamline the process of retrieving and downloading technical documentation from NOV's RigDoc system. It eliminates the tedious, time-consuming process of manual document retrieval, allowing engineers to focus on their primary work.



        ## Key Features

        - **Three-Stage Workflow**: Documents → Files → Downloads

        - **Interactive Menu**: Choose which steps to execute

        - **User Control Points**: Review and select documents/files via Markdown interfaces

        - **Smart File Organization**: Subfolder support based on naming patterns

        - **Configurable Filters**: Apply filter chains to automatically select relevant documents



        ## Workflow

        1. **Document Retrieval**: Scrapes document metadata from RigDoc

        2. **Document Selection**: Exports to Markdown for user review and selection

        3. **File Metadata**: Fetches file information for selected documents

        4. **File Selection**: Exports to Markdown for user review and selection

        5. **Download**: Downloads selected files with intelligent naming and organization



        ## Setup

        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment

        2. The script will:

           - Find available Python installations

           - Create a virtual environment

           - Install required packages from requirements.txt



        ## Usage

        1. Run `RigOfficeDownloader-v4.bat` to start the application

        2. Use the interactive menu to:

           - Configure search parameters

           - Run specific workflow steps

           - Apply filter chains

           - Review and select documents/files



        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Handles errors gracefully

        - Preserves document context and relationships



        ## File Structure

        ```

        outputs/

        ├── data/           # JSON and Markdown files for documents and files

        └── downloads/      # Downloaded files organized by rig number

        ```



        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser (for Selenium automation)

    ```



    ---



    #### `README_v12.md`



    ```markdown

        # RigOfficeDownloader



        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.



        ```

        Documents -> Files -> Downloads

        ```



        ## Overview

        RigOfficeDownloader eliminates the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigDoc system. Engineers can focus on their primary work instead of navigating complex document repositories.



        ## Key Features

        - Three-stage workflow with user control points

        - Interactive menu for flexible execution

        - Configurable filter chains for automatic selection

        - Smart file organization with subfolder support

        - Markdown interfaces for document/file review



        ## Workflow

        1. **Document Retrieval**: Scrapes document metadata from RigDoc

        2. **Document Selection**: Exports to Markdown for user review and selection

        3. **File Metadata**: Fetches file information for selected documents

        4. **File Selection**: Exports to Markdown for user review and selection

        5. **Download**: Downloads selected files with intelligent naming and organization



        ## Setup

        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment

        2. Run `RigOfficeDownloader-v4.bat` to start the application



        ## Usage

        Choose from the interactive menu:

        ```

        [0] Change search parameters

        [1] Configure filter chain

        [2] Fetch docs (scrape initial data)

        [3] Export docs (to Markdown for editing)

        [4] Import updated doc data

        [5] Fetch files (prepare files for download)

        [6] Export files (to Markdown for editing)

        [7] Import updated file data

        [8] Download files

        ```



        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Handles errors gracefully



        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser (for Selenium automation)



        ## Version History

        - v1: Basic document retrieval and download

        - v2: JSON/Markdown conversion and user selection

        - v3: Improved error handling and field organization

        - v4: Subfolder support, filter chains, field ordering

    ```


## Message 20

**Highlighting key elements**  
I’m combining the most effective sections from various README documents to create a comprehensive, concise, and well-structured README for RigOfficeDownloader.

## Message 21

```markdown
# RigOfficeDownloader

> **Automate** document retrieval from NOV’s RigDoc system, **minimize** repetitive tasks, and **accelerate** your 3D modeling workflow.

---

## Overview

**RigOfficeDownloader** is a single-script Python utility that **scrapes**, **filters**, and **downloads** technical documentation from NOV’s RigDoc. Rather than wasting hours searching for and reviewing documents one by one, engineers can leverage an **interactive workflow** that guides them through each phase of document collection.

**Key Highlights**  
- **Three-Stage Workflow**: Documents → Files → Downloads  
- **Interactive Menu**: Run the entire workflow or individual steps on demand  
- **Markdown Edits**: Quickly select or deselect items by editing `.md` tables  
- **Subfolder Support**: Organize downloads with nested folder paths  
- **Filter Chains**: Automate selection based on glob-style patterns  
- **Single-File Simplicity**: All core logic in one script (~500 lines)

---

## The Problem

Engineering projects require diverse technical documents from RigDoc. This **manual** retrieval is:
- **Tedious & Repetitive**: Browsing multiple pages, copying metadata, creating folders  
- **Error-Prone**: Risk of missing crucial docs or misplacing files  
- **Time Consuming**: Delays the start of high-value engineering tasks

---

## The Solution

RigOfficeDownloader streamlines the entire process through an **automated workflow**:
1. **Fetch** document metadata (stores in `<rig>-a-docs.json`)  
2. **Export** docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`  
3. **Import** updated doc data back into JSON  
4. **Fetch** file metadata for included docs  
5. **Export** files to Markdown (`<rig>-b-files.md`): mark `item_download=true`  
6. **Import** updated file data  
7. **Download** files with subfolder paths derived from the generated names

---

## Workflow Steps in Detail

1. **Change / Configure**  
   - Input your **rig number** and **search URLs**.  
   - Set up or modify **filters** (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).  

2. **Fetch Documents**  
   - Scrape document metadata from RigDoc URLs.  
   - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.  

3. **Export & Review Docs**  
   - Creates `<rig>-a-docs.md`.  
   - Manually set `item_include=true` for relevant docs.  

4. **Import Updated Docs**  
   - Reads user changes back into JSON.  

5. **Fetch File Metadata**  
   - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).  
   - `item_download=false` by default.  

6. **Export & Review Files**  
   - Creates `<rig>-b-files.md`.  
   - Set `item_download=true` for desired files.  

7. **Import Updated Files**  
   - Sync file selections back into JSON.  

8. **Download Files**  
   - Retrieves selected files, applying `item_generated_name` for naming.  
   - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.  

---

## Directory Structure

```
RigOfficeDownloader_v4/
├── outputs/
│   ├── data/
│   │   ├── <rig>-a-docs.json
│   │   ├── <rig>-a-docs.md
│   │   ├── <rig>-b-files.json
│   │   └── <rig>-b-files.md
│   └── downloads/
│       └── ...
└── src/
    ├── RigOfficeDownloader-v4.py
    ├── RigOfficeDownloader-v4.bat
    └── ...
```

- **outputs/data/** stores JSON & Markdown for docs/files  
- **outputs/downloads/** houses your final files (organized in subfolders)

---

## Setup & Requirements

1. **OS**: Windows  
2. **Python**: 3.6+ (Recommended 3.8+)  
3. **Chrome Browser** (for Selenium)  
4. **Dependencies**:  
   ```bash
   pip install selenium beautifulsoup4 colorama python-dateutil ansimarkup webdriver-manager
   ```

### Virtual Environment (Optional)
1. Run `py_venv_init.bat` to create a local Python environment.  
2. `RigOfficeDownloader-v4.bat` (or `python RigOfficeDownloader-v4.py`) to start the application.

---

## Using RigOfficeDownloader

1. **Launch**:  
   ```bash
   python RigOfficeDownloader-v4.py
   ```
2. **Follow the Menu**:  
   - Step-by-step prompts let you fetch docs/files, export to Markdown, re-import edited data, and download.  
3. **Edit Markdown** (in `outputs/data/*.md`):  
   - Toggle `item_include` or `item_download`.  
4. **Re-run** to continue each stage or finalize the download.

---

## Filter System

- **Glob-Style Matching**: `*keyword*` or `*.pdf`  
- **Type**: `docs` or `files`  
- **Fields**: e.g., `item_generated_name`, `item_case_description`  
- **Value**: Set to `true` (include) or `false` (exclude)

Example:
```json
{
  "type": "docs",
  "enabled": true,
  "pattern": "*DRILL*FLOOR*",
  "match_field": "item_generated_name",
  "field": "item_include",
  "value": true
}
```

---

## Benefits & Impact

- **75%+ Reduction** in document-gathering time  
- **Flexible**: Use fully automated or manual interactive steps  
- **Consistent Organization**: Hierarchical naming prevents confusion  
- **Error Handling**: Graceful session management, progress feedback, robust deduplication

---

## Troubleshooting

- **Chrome Issues**: Ensure Chrome is up to date and webdriver-manager is compatible.  
- **Login/Credentials**: If your company uses SSO or captchas, you may need to sign in manually.  
- **Permissions**: Make sure you can create/write to `outputs/`.  
- **Interruptions**: If scraping stalls, check for network or session timeouts (restart from relevant step).

---

## Version History

- **v1**: Basic doc retrieval & download  
- **v2**: JSON/Markdown conversion, user selection  
- **v3**: Improved error handling & field organization  
- **v4**: Subfolder support, filter chains, custom field ordering (current)

---

## Need Help?

**Check** inline source code comments or the interactive menu’s help notes for each step. The memory bank (if present) contains deeper architectural reasoning.

Enjoy your streamlined RigDoc experience!
```
