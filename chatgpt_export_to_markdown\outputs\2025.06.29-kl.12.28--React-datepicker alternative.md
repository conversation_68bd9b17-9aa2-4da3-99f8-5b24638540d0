# React-datepicker alternative

Conversation ID: 68611531-5270-8008-9309-a4144f19bf69

## Message 1

what is the more popular alternative for     "react-datepicker": "^8.4.0" to create datefields that can be controlled using the keyboard rather than the datepicker automatically appearing when the datefield is active

## Message 2

A **more popular and flexible alternative** to `react-datepicker` that gives you **full control over keyboard interactions** and doesn't force the calendar popup is:

---

### ✅ **`react-day-picker`**

- **Repo:** [https://github.com/gpbl/react-day-picker](https://github.com/gpbl/react-day-picker)
- **Install:**  
  ```bash
  npm install react-day-picker
  ```

#### 🔹 Why it’s better for your use case:
- It **does not automatically attach** to an input field — instead, you control the **visibility** and **placement** of the calendar.
- You can **fully customize** keyboard behavior and accessibility.
- Plays nicely with **controlled inputs** and lets you decide **when to show/hide** the calendar.
- Built with **accessibility in mind** (WAI-ARIA compliant).

#### 🔹 Example (manual calendar toggle):
```tsx
import { format } from 'date-fns';
import { useState } from 'react';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';

function MyDateField() {
  const [selected, setSelected] = useState<Date | undefined>();
  const [showPicker, setShowPicker] = useState(false);

  return (
    <div>
      <input
        value={selected ? format(selected, 'yyyy-MM-dd') : ''}
        onFocus={() => setShowPicker(true)}
        onChange={() => {}} // optional, controlled input
      />
      {showPicker && (
        <DayPicker
          mode="single"
          selected={selected}
          onSelect={(date) => {
            setSelected(date);
            setShowPicker(false);
          }}
        />
      )}
    </div>
  );
}
```

---

### 🆚 Other alternatives (but less suited for full keyboard control):

| Package             | Notes                                                                 |
|---------------------|------------------------------------------------------------------------|
| `react-dates` (Airbnb) | Heavy and opinionated; difficult to customize keyboard behavior      |
| `mui/x-date-pickers`  | Good if you're using Material UI, but tightly coupled to MUI         |
| `react-calendar`      | Lightweight but also couples UI with logic like `react-datepicker`   |

---

### 🔚 Summary

If your goal is **keyboard-first input + full control** without automatic calendar popups, then:

> **Go with `react-day-picker`** — it's modular, flexible, and widely adopted.

Let me know if you want a custom wrapper component that mimics native input behavior but toggles the calendar manually.
