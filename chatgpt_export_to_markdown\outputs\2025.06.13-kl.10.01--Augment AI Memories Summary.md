# Augment AI Memories Summary

Conversation ID: 684bdae7-6b48-8008-8840-53b2a35a2038

## Message 1

# Context



When working with autonomous AI-assistants such as cursor and augment, the program builds an individual memory file for each project, the memories are expressed as short but precise and high-value "bulletpoints". The way it's used by the coding assistants is that the memories are used as internal "guardrails" to ensure not straying off topic and concistently as a fundamental source of truth to concistently work towards the intended direction/goal. As an example I'm currently using VSCode+Augment, the Augment-Memories feature in the Augment Code VS Code extension is an AI-powered context and memory system that automatically generates and updates as you use the tool. It records and summarizes your coding behavior and the current codespace context, persisting across conversations and sessions to improve code generation, task solving speed, and alignment with your coding style and patterns.



Here's an example to show how simple and effective these memories look:

```

# Core Philosophy & Design Principles



## Core Philosophy

- Value simplicity, elegance, and fundamental connections between components

- Root things together fundamentally for natural simplicity

- Information should be inherently interpretable and self-describing

- Apply systematic precautions against complexity spirals

- Visualize abstract patterns and inherent relationships when designing systems



## Fundamental Approach

- Simplicity & Elegance: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.

- Rooted Fundamentals: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.

- Meta-Information Principle: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.

- Systematic Precautions: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.

- Visual Abstraction: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.



## Focus & Priorities

- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.

- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.

- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.

- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.



## Design Methodology

- Inherent Structure: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.

- Natural Organization: Prefer file naming conventions that create natural and cohesive sequential order for better organization.

- Synergistic Efficiency: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.

- Directional Clarity: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.

- Value Extraction: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.

- Direction Setting: Focus on setting direction rather than planting flags when dealing with unknown complexity.



## Evaluation Criteria

- Comprehensive Progress: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.

- Inherent Direction: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).

- Sequential Composition: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.

- Gradual Reduction: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.

```



# Scenario



I have manually gone through these memories for of my projects and aggregated the most relevant memories (for my python projects) into a single file (`my-py-augment-memories.md`), my intent is to use these memories to build a "profile" for myself (based on all of these collected memories).



Here's the full `my-py-augment-memories.md`:

```

# Quick Reference Guide



## Most Essential Preferences

- Code Style: Self-explanatory code with minimal comments (<10% ratio)

- Structure: Clean, organized project structure with src directory

- Philosophy: Simplicity, elegance, and fundamental connections

- Refactoring: Aim for code size reduction while preserving functionality

- Documentation: Concise, brief, precise, accurate README.md reflecting current state

- Paths: Relative paths with forward slashes (/)

- Environment: PowerShell on Windows for all terminal operations



## Common Code Patterns

- Configuration: In-class config at the top of self-contained scripts

- Error Handling: Graceful handling without halting execution

- Docstrings: Concise single-line format only where needed



## Quick Decision Guide

> | When you need to... | Prefer this approach                             |

> |---------------------|--------------------------------------------------|

> | Add new feature     | Discover usage patterns first                    |

> | Refactor code       | Drastic consolidation with size reduction        |

> | Document code       | Minimal comments, clear structure                |

> | Organize files      | Clean src directory with intuitive structure     |

> | Handle errors       | Graceful handling without halting                |

> | Create templates    | Three-part format with progressive sequences     |

> | Test changes        | Autonomous verification with proper organization |

> | Display paths       | Relative with forward slashes                    |

> | Format CLI output   | Clean, minimal with 1-based indexing             |

> | Execute scripts     | Make paths relative to script location           |



## Context Triggers

- When starting new project: Establish clean src structure first

- When refactoring: Look for patterns across entire codebase

- When documenting: Focus on README.md, minimize in-code comments

- When creating templates: Follow three-part format and stage-based IDs

- When testing: Organize tests to mirror codebase structure

- When handling paths: Make relative to script location

- When designing CLI: Use 1-based indexing and minimal output

- When consolidating: Verify functionality before removing old code



---



# Core Philosophy & Design Principles



## Core Philosophy

- Value simplicity, elegance, and fundamental connections between components

- Root things together fundamentally for natural simplicity

- Information should be inherently interpretable and self-describing

- Apply systematic precautions against complexity spirals

- Visualize abstract patterns and inherent relationships when designing systems



## Fundamental Approach

- Simplicity & Elegance: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.

- Rooted Fundamentals: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.

- Meta-Information Principle: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.

- Systematic Precautions: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.

- Visual Abstraction: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.



## Focus & Priorities

- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.

- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.

- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.

- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.



## Design Methodology

- Inherent Structure: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.

- Natural Organization: Prefer file naming conventions that create natural and cohesive sequential order for better organization.

- Synergistic Efficiency: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.

- Directional Clarity: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.

- Value Extraction: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.

- Direction Setting: Focus on setting direction rather than planting flags when dealing with unknown complexity.



## Evaluation Criteria

- Comprehensive Progress: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.

- Inherent Direction: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).

- Sequential Composition: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.

- Gradual Reduction: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.



---



# Development Practices & Code Style



## Code Style & Structure

- Self-Explanatory Code: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.

- Minimal Comments: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.

- Concise Docstrings: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.

- Structural Clarity: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.

- Composition Over Inheritance: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.

- Self-Contained Scripts: Prefer self-contained scripts with configuration in a class at the top rather than external config files.

- Single File Preference: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.

- Clean Structure: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.

- Consistent Imports: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.

- Prefer self-explanatory code with minimal comments (<10% ratio) and concise single-line docstrings only where needed

- Maintain clean project structure with src directory and organized files

- Use composition over inheritance with single responsibility components

- Prefer self-contained scripts with in-class configuration at the top

- Make paths relative to script location with forward slashes (/)

- Use the existing venv directory to avoid module import errors



## Implementation Approaches

- Relative Paths: Make output directories relative to the script's location unless an absolute path is provided.

- Dynamic Resolution: Prefer dynamic path resolution in IDE configs instead of absolute paths.

- Forward Slashes: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\) in console output.

- Relative References: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.

- Timestamp Precision: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.



## Code Refactoring & Consolidation

- Systematic Safety: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.

- Verify Before Removing: Always check that consolidated files work properly before removing redundant/duplicate files.

- Centralize Values: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.

- Remove Unused: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.

- Verify Necessity: Verify if missing directories/files are actually used in the codebase before creating them.

- Actual Reduction: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.

- Drastic Consolidation: Prefer drastic code size reduction and significant consolidation rather than minor improvements.



## Error Handling & Logging

- Graceful Errors: Handle errors gracefully without halting the script.

- Terminal Persistence: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.

- Simplified Logging: Prefer simplified logging systems that overwrite rather than append to log files.

- Hierarchical Logging: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.

- Log Preservation: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.



## Testing & Validation

- Understand Before Changing: Before making changes to code, first use and understand how the utility works to avoid potential issues.

- Autonomous Verification: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.

- Proper Test Organization: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.

- Clean Output Structure: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.



## Documentation

- Update Documentation: Update relevant markdown documentation after making code improvements.

- README Accuracy: Ensure README.md documentation accurately reflects the current state of the codebase.

- Consolidated Documentation: Prefer removing redundant documentation files in favor of a consolidated README.md.

- Clean Documentation: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.



## User Interaction

- Rich Interfaces: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.

- Clean CLI: Prefer cleaner and less overly explicit CLI interfaces over unnecessary "bloat" in the output.

- 1-Based Indexing: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.

- Markdown Formatting: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.

- Console Output Formatting: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.

```



# Goal



Your goal is to go through my full list of aggregated memories and help me select *only the most abstract, generalized and HIGH-POTENTIAL memories* and present them similarly as they're normally written () into a new document


## Message 2

**Pulling together info**

I'm organizing memories, focusing on abstract ideas. Developing guidelines and incorporating reliable citations to enhance clarity.

**Synthesizing principles**

I’m weaving together summaries on high-level software design principles using a web tool to get the latest sources, ensuring each idea is well-cited and aligns with established guidelines.

**Crafting the final response**

I’m compiling a final response by summarizing key findings and adhering to style guidelines, including bulletpoints and citations for clarity and consistency.

## Message 3

In distilling your extensive memory bank, I focused on principles that are (1) abstract enough to remain relevant across projects and languages, (2) direction‑setting rather than prescriptive, and (3) capable of cascading leverage—i.e., following any one of them tends to pull lower‑level practices into alignment.  
Below is a **“high‑potential core profile”** expressed in the same bullet‑point idiom you already use, ready to drop into an Augment/ Cursor memory file.

---

# Core Philosophy & Design Principles  

- **Simplicity as a First Principle** – Default to the smallest coherent solution; every additional moving part must earn its place. K I S S keeps codebases stable, extensible, and more easily reasoned about. citeturn0search0turn0search8  
- **Inherent Self‑Description** – Strive for self‑documenting artifacts where names, structure, and flow explain the *what*; reserve comments for the *why*. citeturn0search9turn0news77  
- **Rooted Fundamentals** – Tie components together via their underlying conceptual relationships, not merely convenience APIs; this surfaces a system’s “truth table” at a glance and prevents accidental complexity.  
- **Systemic Guardrails > Ad‑hoc Fixes** – Embed precautions (tests, type‑checks, CI) that stop complexity spirals before they start; let tooling prove correctness continuously. citeturn0search5  

# Guiding Development Practices  

- **Composition over Inheritance** – Build behavior by wiring small, orthogonal pieces rather than deep class trees; flexibility and testability rise naturally. citeturn0search2turn0search10  
- **DRY, Centralized Truths** – Consolidate constants, algorithms, and knowledge into single sources; redundancy is technical debt with compound interest. citeturn0search7  
- **YAGNI / “Usage Before Features”** – Postpone optional functionality until a concrete need emerges, avoiding premature generalization and bloated APIs. citeturn0search6  
- **Impactful Refactoring** – Aim for code‑size reduction *and* stronger cohesion in the same sweep; refactorings that don’t simplify aren’t finished. citeturn0search4  
- **File‑Tree as Mental Model** – Let directory hierarchy mirror domain boundaries so a newcomer can infer architecture by reading the path bar. citeturn0search3turn0search11  

# Decision‑Making Heuristics  

| When faced with…                    | Prefer…                                                                            |
|-------------------------------------|------------------------------------------------------------------------------------|
| Feature pressure                    | Demonstrated usage > speculative capability (YAGNI) citeturn0search6 |
| Multiple design options             | The option with the fewest irreversible commitments (simplicity bias) citeturn0search0 |
| Overlapping functionality           | A single, DRY consolidation point citeturn0search7 |
| Large refactor                      | Proof‑of‑value spike + incremental rollout guarded by CI citeturn0search5 |

# Evaluation & Continuous Improvement  

- **Critical‑Value Targeting** – Identify the one leverage point whose improvement unlocks the most downstream benefit before touching code.  
- **Sequential Composition** – Design outputs so they can be chained without glue code; every artifact should be a clean API for the next.  
- **Gradual Reduction** – Apply progressive summarization: comprehensive → focused → essential → core, keeping fidelity while lightening cognitive load. citeturn1search2  

---

These bullets capture the *essence* of your larger list while remaining compact enough to serve as always‑on “guardrails” for any future AI assistant.
