# Code Overview Summary

Conversation ID: 67a62c73-d0f0-8008-8721-51b91623b4bf

## Message 1

i want to pose a challenge to you, but first i need you to familiarize yourself with the provided code:

#!/usr/bin/env python3

"""

template_runnet.py

A single-file script for managing templates, LLM interactions, and orchestrating prompt refinements.

Now updated to pass each template's 'enhanced_prompt' as input to the next template in the chain.

"""



import os

import sys

import re

import glob

import json

from typing import List, Dict, Union, Optional

from pathlib import Path



from dotenv import load_dotenv

from loguru import logger



# Provider SDKs

from openai import OpenAI

from anthropic import Anthropic



# -------------------------------------------------------

# 1. LowestLevelCommunicator

# -------------------------------------------------------

class LowestLevelCommunicator:

    """

    Captures raw input and output strings at the lowest level,

    preserving the entire stream before any filtering or transformation.

    """

    def __init__(self):

        self.raw_interactions = []



    def record_request(self, provider: str, model_name: str, messages: List[Dict[str, str]]):

        """

        Called just before sending the request to the LLM.

        """

        self.raw_interactions.append({

            "direction": "request",

            "provider": provider,

            "model_name": model_name,

            "messages": messages,  # Full list of role/content pairs

        })



    def record_response(self, provider: str, model_name: str, response_text: str):

        """

        Called immediately after receiving the raw text from the LLM.

        """

        self.raw_interactions.append({

            "direction": "response",

            "provider": provider,

            "model_name": model_name,

            "content": response_text,

        })



    def get_all_interactions(self) -> List[Dict]:

        """

        Return the raw record of all requests/responses.

        """

        return self.raw_interactions



    def get_formatted_output(self) -> str:

        """

        Pretty-print a summary of the captured interactions.

        """

        lines = []

        for entry in self.raw_interactions:

            direction = entry["direction"].upper()

            provider = entry["provider"]

            model_name = entry["model_name"]



            if direction == "REQUEST":

                lines.append(f"--- REQUEST to {provider} (model: {model_name}) ---")

                for i, msg in enumerate(entry["messages"], start=1):

                    role = msg.get("role", "").upper()

                    content = msg.get("content", "")

                    lines.append(f"{i}. {role}: {content}")

            else:

                lines.append(f"--- RESPONSE from {provider} (model: {model_name}) ---")

                lines.append(entry["content"])

            lines.append("")



        return "\n".join(lines).strip()



# -------------------------------------------------------

# 2. Global Configuration

# -------------------------------------------------------

class Config:

    """

    Global settings

    """



    PROVIDER_OPENAI = "openai"

    PROVIDER_DEEPSEEK = "deepseek"

    PROVIDER_ANTHROPIC = "anthropic"



    AVAILABLE_MODELS = {

        PROVIDER_OPENAI: {

            "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",

            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",

            "gpt-4": "Latest GPT-4 stable release",

            "gpt-4-0125-preview": "Preview GPT-4 Turbo",

            "gpt-4-0613": "June 2023 GPT-4 snapshot",

            "gpt-4-1106-preview": "Preview GPT-4 Turbo",

            "gpt-4-turbo": "Latest GPT-4 Turbo release",

            "gpt-4-turbo-2024-04-09": "GPT-4 Turbo w/ vision",

            "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",

            "gpt-4o": "Base GPT-4o model",

            "gpt-4o-mini": "Lightweight GPT-4o variant",

        },

        PROVIDER_DEEPSEEK: {

            "deepseek-chat": "DeepSeek Chat model",

            "deepseek-reasoner": "Specialized reasoning model",

        },

        PROVIDER_ANTHROPIC: {

            "claude-2": "Base Claude 2 model",

            "claude-2.0": "Enhanced Claude 2.0",

            "claude-2.1": "Latest Claude 2.1 release",

            "claude-3-opus-20240229": "Claude 3 Opus",

            "claude-3-sonnet-20240229": "Claude 3 Sonnet",

            "claude-3-haiku-20240307": "Claude 3 Haiku",

        },

    }



    DEFAULT_MODEL_PARAMS = {

        PROVIDER_OPENAI: {

            "model_name": "gpt-4-turbo-preview", # (5) used occasionally

            "model_name": "gpt-4o",              # (4) debugging

            "model_name": "gpt-4-turbo",         # (3) used often

            "model_name": "gpt-3.5-turbo",       # (3) most used

            "model_name": "gpt-3.5-turbo-1106",  # (1) most used

            "temperature": 0.7,

            "max_tokens": 800,

        },

        PROVIDER_DEEPSEEK: {

            "model_name": "deepseek-reasoner",

            "model_name": "deepseek-coder",

            "model_name": "deepseek-chat",

            "temperature": 0.7,

            "max_tokens": 800,

        },

        PROVIDER_ANTHROPIC: {

            "model_name": "claude-2.1",

            "model_name": "claude-3-opus-20240229",

            "temperature": 0.7,

            "max_tokens": 800,

        },

    }



    API_KEY_ENV_VARS = {

        PROVIDER_OPENAI: "OPENAI_API_KEY",

        PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",

        PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",

    }



    BASE_URLS = {

        PROVIDER_DEEPSEEK: "https://api.deepseek.com",

    }



    # Overriding allows for switching between providers by simply reordering the lines.

    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK

    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC

    DEFAULT_PROVIDER = PROVIDER_OPENAI



    def __init__(self):

        load_dotenv()

        self.configure_utf8_encoding()

        self.provider = self.DEFAULT_PROVIDER.lower()

        self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]

        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))

        self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()

        self.setup_logger()



    def configure_utf8_encoding(self):

        """

        Ensure UTF-8 encoding for standard output and error streams.

        """

        if hasattr(sys.stdout, "reconfigure"):

            sys.stdout.reconfigure(encoding="utf-8", errors="replace")

        if hasattr(sys.stderr, "reconfigure"):

            sys.stderr.reconfigure(encoding="utf-8", errors="replace")



    def setup_logger(self):

        """

        YAML logging via Loguru: clears logs, sets global context, and configures sinks

        """

        log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"

        log_filepath = os.path.join(self.log_dir, log_filename)

        open(log_filepath, "w").close()

        logger.remove()

        logger.configure(

            extra={

                "provider": self.provider,

                "model": self.model_params.get("model_name"),

            }

        )



        def yaml_sink(log_message):

            log_record = log_message.record

            formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")

            formatted_level = f"!{log_record['level'].name}"

            logger_name = log_record["name"]

            formatted_function_name = f"*{log_record['function']}"

            line_number = log_record["line"]

            extra_provider = log_record["extra"].get("provider")

            extra_model = log_record["extra"].get("model")

            log_message_content = log_record["message"]



            if "\n" in log_message_content:

                formatted_message = "|\n" + "\n".join(

                    f"  {line}" for line in log_message_content.splitlines()

                )

            else:

                formatted_message = (

                    f"'{log_message_content}'"

                    if ":" in log_message_content

                    else log_message_content

                )



            log_lines = [

                f"- time: {formatted_timestamp}",

                f"  level: {formatted_level}",

                f"  name: {logger_name}",

                f"  funcName: {formatted_function_name}",

                f"  lineno: {line_number}",

            ]

            if extra_provider is not None:

                log_lines.append(f"  provider: {extra_provider}")

            if extra_model is not None:

                log_lines.append(f"  model: {extra_model}")



            log_lines.append(f"  message: {formatted_message}")

            log_lines.append("")



            with open(log_filepath, "a", encoding="utf-8") as log_file:

                log_file.write("\n".join(log_lines) + "\n")



        logger.add(yaml_sink, level="DEBUG", enqueue=True, format="{message}")



# -------------------------------------------------------

# 3. LLM Interactions

# -------------------------------------------------------

class LLMInteractions:

    """

    Handles interactions with LLM APIs.

    """



    CLIENT_FACTORIES = {

        Config.PROVIDER_OPENAI: lambda key: OpenAI(api_key=key),

        Config.PROVIDER_DEEPSEEK: lambda key: OpenAI(api_key=key, base_url=Config.BASE_URLS[Config.PROVIDER_DEEPSEEK]),

        Config.PROVIDER_ANTHROPIC: lambda key: Anthropic(api_key=key),

    }



    def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None, provider=None):

        self.config = Config()

        self.provider = provider or self.config.provider

        defaults = self.config.DEFAULT_MODEL_PARAMS[self.provider]

        self.model_name = model_name or defaults["model_name"]

        self.temperature = temperature if temperature is not None else defaults["temperature"]

        self.max_tokens = max_tokens if max_tokens is not None else defaults["max_tokens"]



        # Our communicator capturing lowest-level I/O

        self.communicator = LowestLevelCommunicator()

        self.client = self._create_client(api_key)



    def _create_client(self, api_key=None):

        api_key_env = Config.API_KEY_ENV_VARS.get(self.provider)

        api_key_use = api_key or os.getenv(api_key_env)

        try:

            return self.CLIENT_FACTORIES[self.provider](api_key_use)

        except KeyError:

            raise ValueError(f"Unsupported LLM provider: {self.provider}")



    def _log_api_response(self, response):

        prompt_tokens = getattr(getattr(response, "usage", None), "prompt_tokens", "N/A")

        logger.bind(prompt_tokens=prompt_tokens).debug(response)



    def _log_api_error(self, exception, model_name, messages):

        logger.error(f"Error during (provider:{self.provider} | model:{model_name}) API call: {exception}")

        logger.debug(f"Exception type: {type(exception).__name__}")

        logger.debug(f"Detailed exception: {exception}")

        logger.debug(f"Input messages: {messages}")



    def _execute_api_call(self, call_fn, model_name, messages):

        try:

            response = call_fn()

            self._log_api_response(response)

            return response

        except Exception as e:

            self._log_api_error(e, model_name, messages)

            return None



    def _openai_call(self, messages, model_name, temperature, max_tokens):

        return self.client.chat.completions.create(

            model=model_name,

            temperature=temperature,

            max_tokens=max_tokens,

            messages=messages,

        )



    def _anthropic_call(self, messages, model_name, temperature, max_tokens):

        system_prompt = "\n".join(msg["content"] for msg in messages if msg["role"] == "system")

        user_msgs = [msg for msg in messages if msg["role"] == "user"]



        return self.client.messages.create(

            model=model_name,

            max_tokens=max_tokens,

            temperature=temperature,

            system=system_prompt.strip(),

            messages=user_msgs,

        )



    def _deepseek_call(self, messages, model_name, temperature, max_tokens):

        system_prompt = next((msg["content"] for msg in messages if msg["role"] == "system"), "")

        instructions_content = "\n".join(

            msg["content"]

            for msg in messages

            if msg["role"] == "system" and msg["content"] != system_prompt

        )

        user_prompt = next((msg["content"] for msg in messages if msg["role"] == "user"), "")

        combined_prompt = f"{system_prompt}\n{instructions_content}\n{user_prompt}"

        return self.client.chat.completions.create(

            model=model_name,

            temperature=temperature,

            max_tokens=max_tokens,

            messages=[{"role": "user", "content": combined_prompt}],

        )



    def _execute_llm_api_call(self, messages, model_name, temperature, max_tokens):

        # Record the raw request data

        self.communicator.record_request(self.provider, model_name, messages)



        provider_map = {

            Config.PROVIDER_OPENAI: lambda: self._openai_call(messages, model_name, temperature, max_tokens),

            Config.PROVIDER_DEEPSEEK: lambda: self._deepseek_call(messages, model_name, temperature, max_tokens),

            Config.PROVIDER_ANTHROPIC: lambda: self._anthropic_call(messages, model_name, temperature, max_tokens),

        }



        provider_api_request = provider_map.get(self.provider)

        if provider_api_request is None:

            raise ValueError(f"Unsupported LLM provider: {self.provider}")



        api_response = self._execute_api_call(provider_api_request, model_name, messages)

        if not api_response:

            return None



        # Parse out raw text

        if self.provider in [Config.PROVIDER_OPENAI, Config.PROVIDER_DEEPSEEK]:

            raw_text = (

                api_response.choices[0].message.content

                if hasattr(api_response, "choices") and api_response.choices

                else None

            )

        elif self.provider == Config.PROVIDER_ANTHROPIC:

            raw_text = (

                api_response.content[0].text

                if hasattr(api_response, "content") and api_response.content

                else None

            )

        else:

            raw_text = None



        # Record the raw response

        if raw_text is not None:

            self.communicator.record_response(self.provider, model_name, raw_text)



        return raw_text



    def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):

        used_model = model_name or self.model_name

        used_temp = temperature if temperature is not None else self.temperature

        used_tokens = max_tokens if max_tokens is not None else self.max_tokens

        return self._execute_llm_api_call(messages, used_model, used_temp, used_tokens)



# -------------------------------------------------------

# 4. Template File Manager

# -------------------------------------------------------

class TemplateFileManager:

    """

    Manages prompt templates, performing lazy loading, placeholder substitution,

    and recipe execution.

    """



    ALLOWED_FILE_EXTS = (".xml", ".py", ".json", ".md", ".txt")

    EXCLUDED_FILE_NAMES = ["ignore_me", "do_not_load", "temp_file"]

    EXCLUDED_FILE_PATHS = ["\\_md\\"]

    EXCLUDED_PATTERNS = [r".*\.\d{2}\.\d{2}.*", ]

    MAX_TEMPLATE_SIZE_KB = 100

    REQUIRED_CONTENT_MARKERS = ["<template>", "[INPUT_PROMPT]"]



    def __init__(self):

        self.template_dir = os.getcwd()

        self.template_cache = {}



    def template_qualifier(self, filepath):

        _, ext = os.path.splitext(filepath)

        filename = os.path.basename(filepath)

        basename, _ = os.path.splitext(filename)

        filepath_lower = filepath.lower()



        if ext.lower() not in self.ALLOWED_FILE_EXTS:

            return False

        if basename in self.EXCLUDED_FILE_NAMES:

            return False

        if any(excluded in filepath_lower for excluded in self.EXCLUDED_FILE_PATHS):

            return False

        if any(re.match(pattern, filename, re.IGNORECASE) for pattern in self.EXCLUDED_PATTERNS):

            return False

        try:

            filesize_kb = os.path.getsize(filepath) / 1024

            if filesize_kb > self.MAX_TEMPLATE_SIZE_KB:

                return False

            with open(filepath, "r", encoding="utf-8") as f:

                content = f.read()

            if not all(marker in content for marker in self.REQUIRED_CONTENT_MARKERS):

                return False

        except Exception:

            return False



        return True



    def reload_templates(self):

        self.template_cache.clear()

        pattern = os.path.join(self.template_dir, "**", "*.*")

        for filepath in glob.glob(pattern, recursive=True):

            name = os.path.splitext(os.path.basename(filepath))[0]

            if self.template_qualifier(filepath):

                self.template_cache[name] = filepath



    def prefetch_templates(self, template_name_list):

        for name in template_name_list:

            _ = self.get_template_path(name)



    def get_template_path(self, template_name):

        if template_name in self.template_cache:

            return self.template_cache[template_name]

        for ext in self.ALLOWED_FILE_EXTS:

            search_pattern = os.path.join(self.template_dir, "**", f"{template_name}{ext}")

            files = glob.glob(search_pattern, recursive=True)

            if files:

                self.template_cache[template_name] = files[0]

                return files[0]

        return None



    def _parse_template(self, template_path):

        try:

            with open(template_path, "r", encoding="utf-8") as f:

                content = f.read()



            placeholders = list(set(re.findall(r"\[(.*?)\]", content)))

            template_data = {

                "path": template_path,

                "content": content,

                "placeholders": placeholders,

            }

            return template_data



        except Exception as e:

            logger.error(f"Error parsing template file {template_path}: {e}")

            return {}



    def extract_placeholders(self, template_name):

        template_path = self.get_template_path(template_name)

        if not template_path:

            return []

        parsed_template = self._parse_template(template_path)

        if not parsed_template:

            return []

        return parsed_template.get("placeholders", [])



    def get_template_metadata(self, template_name):

        template_path = self.get_template_path(template_name)

        if not template_path:

            return {}

        parsed_template = self._parse_template(template_path)

        if not parsed_template:

            return {}



        content = parsed_template["content"]

        metadata = {

            "agent_name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),

            "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),

            "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>'),

            "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),

            "system_prompt": self._extract_value_from_content(content, r'<system_prompt value="([^"]*)"\s*/>')

        }

        return metadata



    def _extract_value_from_content(self, content, pattern):

        match = re.search(pattern, content)

        return match.group(1) if match else None



    def list_templates(

        self,

        exclude_paths=None,

        exclude_names=None,

        exclude_versions=None,

        exclude_statuses=None,

        exclude_none_versions=False,

        exclude_none_statuses=False

    ):

        search_pattern = os.path.join(self.template_dir, "**", "*.*")

        templates_info = {}



        for filepath in glob.glob(search_pattern, recursive=True):

            if not self.template_qualifier(filepath):

                continue

            template_name = os.path.splitext(os.path.basename(filepath))[0]

            parsed_template = self._parse_template(filepath)

            if not parsed_template:

                logger.warning(f"Skipping {filepath} due to parsing error.")

                continue

            content = parsed_template["content"]

            try:

                templates_info[template_name] = {

                    "path": filepath,

                    "name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),

                    "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),

                    "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),

                    "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>')

                }

            except Exception as e:

                logger.error(f"Error loading template from {filepath}: {e}")



        filtered_templates = {}

        for name, info in templates_info.items():

            if ((not exclude_paths or info["path"] not in exclude_paths) and

                (not exclude_names or info["name"] not in exclude_names) and

                (not exclude_versions or info["version"] not in exclude_versions) and

                (not exclude_statuses or info["status"] not in exclude_statuses) and

                (not exclude_none_versions or info["version"] is not None) and

                (not exclude_none_statuses or info["status"] is not None)):

                filtered_templates[name] = info



        return filtered_templates



    def load_template_from_file(self, template_filepath, initial_prompt=""):

        parsed_template = self._parse_template(template_filepath)

        if not parsed_template:

            return None



        content = parsed_template["content"]

        placeholders = {

            "[OUTPUT_FORMAT]": "plain_text",

            "[ORIGINAL_PROMPT_LENGTH]": str(len(initial_prompt)),

            "[RESPONSE_PROMPT_LENGTH]": str(int(len(initial_prompt) * 0.9)),

            "[INPUT_PROMPT]": initial_prompt,

            "[ADDITIONAL_CONSTRAINTS]": "",

            "[ADDITIONAL_PROCESS_STEPS]": "",

            "[ADDITIONAL_GUIDELINES]": "",

            "[ADDITIONAL_REQUIREMENTS]": "",

        }

        for placeholder, value in placeholders.items():

            value_str = str(value)

            content = content.replace(placeholder, value_str)



        return content



    def _extract_template_parts(self, raw_text):

        """

        Extracts relevant sections from the raw template text.

        E.g., <system_prompt ...>, <response_format>, etc.

        """

        metadata_match = re.search(r"<metadata>(.*?)</metadata>", raw_text, re.DOTALL)

        response_format_match = re.search(r"<response_format>(.*?)</response_format>", raw_text, re.DOTALL)



        template_match = re.search(r"<template>(.*?)</template>", raw_text, re.DOTALL)

        agent_match = re.search(r"<agent>(.*?)</agent>", raw_text, re.DOTALL)



        system_prompt_match = re.search(r'<system_prompt value="([^"]*)"\s*/>', raw_text)

        instructions_match = re.search(r"<instructions>(.*?)</instructions>", raw_text, re.DOTALL)



        system_prompt = system_prompt_match.group(1) if system_prompt_match else ""

        response_format = response_format_match.group(1) if response_format_match else ""

        template = template_match.group(1) if template_match else ""

        instructions = instructions_match.group(1) if instructions_match else ""



        return system_prompt, response_format, template



# -------------------------------------------------------

# 5. Prompt Refinement Orchestrator

# -------------------------------------------------------

class PromptRefinementOrchestrator:

    def __init__(self, template_manager: TemplateFileManager, agent: LLMInteractions):

        self.template_manager = template_manager

        self.agent = agent



    def _build_messages(self, system_prompt: str, instructions: str, user_prompt: str) -> List[Dict[str, str]]:

        """

        Combine system prompt + instructions, then user prompt, into messages.

        """

        combined_system_prompt = system_prompt.strip()

        if instructions.strip():

            combined_system_prompt += "\n" + instructions.strip()



        return [

            {"role": "system", "content": combined_system_prompt},

            {"role": "user", "content": user_prompt},

        ]



    def _format_multiline(self, text):

        """

        Nicely format the text for console output (esp. if it is JSON).

        """

        if isinstance(text, dict) or isinstance(text, list):

            return json.dumps(text, indent=4, ensure_ascii=False)

        elif isinstance(text, str):

            try:

                if text.startswith("```json"):

                    json_match = re.search(r"```json\n(.*?)\n```", text, re.DOTALL)

                    if json_match:

                        return json.dumps(json.loads(json_match.group(1)), indent=4, ensure_ascii=False)

                elif text.strip().startswith("{") and text.strip().endswith("}"):

                    return json.dumps(json.loads(text), indent=4, ensure_ascii=False)

            except json.JSONDecodeError:

                pass

        return text.replace("\\n", "\n")



    # -------------------------------------------------------

    # CHANGED: Now parse "enhanced_prompt" from the JSON output

    #          and pass it on to the next iteration.

    # -------------------------------------------------------

    def execute_prompt_refinement_chain_from_file(

        self,

        template_filepath,

        input_prompt,

        refinement_count=1,

        display_instructions=False,

        model_name=None,

        temperature=None,

        max_tokens=None

    ):

        """

        Executes multiple refinement iterations using one file-based template,

        passing 'enhanced_prompt' forward if present in the response JSON.

        """

        content = self.template_manager.load_template_from_file(template_filepath, input_prompt)

        if not content:

            return None



        if display_instructions:

            logger.info("=" * 60)

            logger.info(content)

            logger.info("=" * 60)



        agent_name = self.template_manager._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>')

        system_prompt, response_format, agent_instructions = self.template_manager._extract_template_parts(content)

        prompt = input_prompt

        results = []



        for _ in range(refinement_count):

            msgs = self._build_messages(system_prompt, agent_instructions, prompt)

            refined = self.agent.generate_response(

                msgs,

                model_name=model_name,

                temperature=temperature,

                max_tokens=max_tokens,

            )

            if refined:

                # Attempt to pretty-print if JSON

                refined_str = refined

                try:

                    data = json.loads(refined_str)

                    refined_str = json.dumps(data, indent=4)

                except json.JSONDecodeError:

                    pass



#                 print(f'''

# [{agent_name}]

# - Response:

# {self._format_multiline(refined_str)}

# ''')



                # Store the full raw response

                results.append(refined)



                # NEW: If the response is JSON and has "enhanced_prompt," pass that as the new input

                next_prompt = refined

                try:

                    data = json.loads(refined)

                    if isinstance(data, dict) and "enhanced_prompt" in data:

                        next_prompt = data["enhanced_prompt"]

                except (TypeError, json.JSONDecodeError):

                    pass



                prompt = next_prompt



        return results



    def _execute_single_template_refinement(self, template_name, initial_prompt, refinement_count, **kwargs):

        path = self.template_manager.get_template_path(template_name)

        if not path:

            logger.error(f"No template file found with name: {template_name}")

            return None

        return self.execute_prompt_refinement_chain_from_file(path, initial_prompt, refinement_count, **kwargs)



    def _execute_multiple_template_refinement(self, template_name_list, initial_prompt, refinement_levels, **kwargs):

        if not all(isinstance(template, str) for template in template_name_list):

            logger.error("All items in template_name_list must be strings.")

            return None

        if isinstance(refinement_levels, int):

            counts = [refinement_levels] * len(template_name_list)

        elif isinstance(refinement_levels, list) and len(refinement_levels) == len(template_name_list):

            counts = refinement_levels

        else:

            logger.error("refinement_levels must be int or a list matching template_name_list.")

            return None



        results = []

        current_prompt = initial_prompt

        for name, cnt in zip(template_name_list, counts):

            chain_result = self._execute_single_template_refinement(name, current_prompt, cnt, **kwargs)

            if chain_result:

                # The last returned string from that chain becomes the next prompt

                current_prompt = chain_result[-1]

                results.append({"agent_name": name, "iterations": cnt, "outputs": chain_result})

        return results



    def execute_prompt_refinement_by_name(

        self,

        template_name_or_list: Union[str, List[str]],

        initial_prompt: str,

        refinement_levels: Union[int, List[int]] = 1,

        display_instructions=False,

        model_name=None,

        temperature=None,

        max_tokens=None

    ):

        if isinstance(template_name_or_list, str):

            return self._execute_single_template_refinement(

                template_name_or_list,

                initial_prompt,

                refinement_levels,

                display_instructions=display_instructions,

                model_name=model_name,

                temperature=temperature,

                max_tokens=max_tokens,

            )

        elif isinstance(template_name_or_list, list):

            if not all(isinstance(x, str) for x in template_name_or_list):

                logger.error("All items in template_name_or_list must be strings.")

                return None

            return self._execute_multiple_template_refinement(

                template_name_or_list,

                initial_prompt,

                refinement_levels,

                display_instructions=display_instructions,

                model_name=model_name,

                temperature=temperature,

                max_tokens=max_tokens,

            )

        else:

            logger.error("template_name_or_list must be str or list[str].")

            return None



    def execute_recipe(self, recipe: List[Dict], initial_prompt: str) -> Dict[str, Union[str, List[Dict]]]:

        """

        Executes a multi-step prompt refinement "recipe."

        """

        current_input = initial_prompt

        refinement_history = []

        gathered_outputs = []



        for idx, step in enumerate(recipe, start=1):

            chain = step.get("chain")

            repeats = step.get("repeats", 1)

            gather = step.get("gather", False)

            aggregator = step.get("aggregator_chain")

            if not chain:

                logger.error(f"Recipe step {idx} missing 'chain' key.")

                continue



            step_gathered = []

            for rep in range(repeats):

                data = self.execute_prompt_refinement_by_name(chain, current_input, 1)

                if data:

                    refinement_history.append({"step": idx, "repeat": rep + 1, "chain": chain, "result": data})

                    final_str = data[-1]

                    step_gathered.append(final_str)

                    if not gather:

                        current_input = final_str



            if gather and step_gathered:

                gathered_outputs.extend(step_gathered)

                if aggregator:

                    aggregator_prompt = "\n\n".join([f"Option {i+1}:\n{v}" for i, v in enumerate(step_gathered)])

                    aggregator_data = self.execute_prompt_refinement_by_name(aggregator, aggregator_prompt, 1)

                    if aggregator_data:

                        refinement_history.append({

                            "step": idx,

                            "aggregator_chain": aggregator,

                            "aggregator_input": aggregator_prompt,

                            "aggregator_result": aggregator_data,

                        })

                        current_input = aggregator_data[-1]

                    else:

                        current_input = step_gathered[-1]

                else:

                    current_input = step_gathered[-1]



        return {

            "final_output": current_input,

            "refinement_history": refinement_history,

            "gathered_outputs": gathered_outputs,

        }



# -------------------------------------------------------

# 6. Main Execution

# -------------------------------------------------------

class Execution:

    def __init__(self, provider=None):

        self.config = Config()

        self.agent = LLMInteractions(provider=provider)

        self.template_manager = TemplateFileManager()

        self.refinement_engine = PromptRefinementOrchestrator(self.template_manager, self.agent)



    def log_usage_demo(self):

        self.template_manager.reload_templates()

        self.template_manager.prefetch_templates(["PromptOptimizerExpert", "MultiResponseSelector", "IntensityEnhancer"])



        placeholders = self.template_manager.extract_placeholders("IntensityEnhancer")

        logger.info(f"Placeholders in 'IntensityEnhancer': {placeholders}")



        metadata = self.template_manager.get_template_metadata("IntensityEnhancer")

        logger.info(f"Metadata for 'IntensityEnhancer': {metadata}")



        all_temps = self.template_manager.list_templates(exclude_none_statuses=True, exclude_none_versions=True)

        logger.info(f"Found a total of {len(all_temps)} templates.")

        logger.info("Template keys: " + ", ".join(all_temps.keys()))



    def run(self):

        self.log_usage_demo()

        self.template_manager.reload_templates()



        # Example recipe - two passes of PromptOptimizerExpert, gather results,

        # then feed to MultiResponseSelector aggregator.

        recipe_steps = [

            {

                "chain": ["PromptOptimizerExpert", "PromptOptimizerExpert"],

                "repeats": 1,

                "gather": True,

                "aggregator_chain": ["MultiResponseSelector"],

            },

        ]



        initial_prompt = (

            "You are a steadfast, detail-oriented assistant. Your critical task is to refine "

            "this prompt with absolute precision and laser-sharp clarity. Adhere strictly to all "

            "guidelines, ensuring the refined version fully captures and enhances the original's "

            "essence and intent."

        )



        recipe_result = self.refinement_engine.execute_recipe(

            recipe=recipe_steps, initial_prompt=initial_prompt

        )



        # Show final result

        print("\n=== Final Recipe Output ===")

        print(recipe_result["final_output"])



        # Show a formatted log of all raw I/O from the communicator

        print("\n=== Full Communicator Log (Raw I/O) ===")

        print(self.agent.communicator.get_formatted_output())





if __name__ == "__main__":

    provider_to_use = os.getenv("LLM_PROVIDER", Config.DEFAULT_PROVIDER).lower()

    execution = Execution(provider=provider_to_use)

    execution.run()


## Message 2

**Familiarizing with the code**

The user says they want to pose a challenge, but first, I need to familiarize myself with the provided Python code. The script includes various components like LLM interactions, a TemplateFileManager, and a PromptRefinementOrchestrator. It seems like these may be important for the challenge later, but the full challenge details aren't provided yet. So, I’ll make sure I understand the code and be ready for the challenge once the user is ready to give more details.

**Reviewing the code**

Alright, I've gone through the provided code. It’s a single-file script focused on managing prompt templates and orchestrating LLM interactions. The structure includes:

- **LowestLevelCommunicator**: Records raw interactions.
- **Config**: Handles global configuration and environment setup.
- **LLMInteractions**: Manages calls to LLM providers like OpenAI.
- **TemplateFileManager**: Manages prompt templates stored on disk.
- **PromptRefinementOrchestrator**: Coordinates prompt refinement through interactions.
- **Execution**: Demonstrates code usage, loading templates and executing recipes.

The code uses Loguru for logging and dotenv for environment variables, with a chain of refinement using agent responses.

## Message 3

I've reviewed the code thoroughly. Here’s a high-level summary of its structure and functionality:

1. **LowestLevelCommunicator**  
   - **Purpose:** Captures and logs all raw input/output interactions with the LLMs.  
   - **Key Methods:**  
     - `record_request`: Logs requests before they're sent.  
     - `record_response`: Logs the raw response from the LLM.  
     - `get_formatted_output`: Provides a human-readable summary of the logged interactions.

2. **Global Configuration (Config)**  
   - **Purpose:** Sets global settings such as default providers, model parameters, API key management, and logger configuration using Loguru.  
   - **Key Aspects:**  
     - Supports multiple LLM providers (OpenAI, DeepSeek, Anthropic).  
     - Configures UTF-8 encoding and logging sinks.

3. **LLMInteractions**  
   - **Purpose:** Manages API calls to different LLM providers.  
   - **Key Features:**  
     - Uses provider-specific methods (`_openai_call`, `_anthropic_call`, `_deepseek_call`) to format and send requests.  
     - Logs both API responses and errors, capturing details via the LowestLevelCommunicator.  
     - Implements a unified method (`generate_response`) to execute the API call.

4. **TemplateFileManager**  
   - **Purpose:** Handles prompt templates stored on disk, including loading, parsing, and placeholder substitution.  
   - **Key Features:**  
     - Filters templates based on file extension, file size, and content markers.  
     - Extracts metadata (agent name, version, description, etc.) from templates.  
     - Supports lazy loading and caching of templates.

5. **PromptRefinementOrchestrator**  
   - **Purpose:** Orchestrates multiple iterations (chains) of prompt refinements using loaded templates and LLM responses.  
   - **Key Features:**  
     - Builds message payloads by combining system prompts, instructions, and user inputs.  
     - Executes multiple iterations, passing along an "enhanced_prompt" extracted from JSON responses if available.  
     - Supports both single and multi-template refinement chains as well as recipe-based executions that can aggregate multiple responses.

6. **Execution**  
   - **Purpose:** Demonstrates the overall workflow by tying together template management, LLM interactions, and prompt refinement orchestration.  
   - **Example:**  
     - Reloads and prefetches templates.  
     - Logs usage and metadata.  
     - Executes a sample recipe that chains multiple refinement steps and aggregates the results, finally outputting both the final prompt and the detailed log of interactions.

I'm ready for the challenge you have in mind—please go ahead and let me know what you'd like to pose!

## Message 4

to further your understanding of this utility i've provided the output when executed exactly as-is:

 

=== Final Recipe Output ===

{'agent_name': 'MultiResponseSelector', 'iterations': 1, 'outputs': ['Refine the prompt to enhance precision and clarity while preserving the original essence and intent, strictly adhering to guidelines for a thorough and detailed refinement.']}



=== Full Communicator Log (Raw I/O) ===

--- REQUEST to openai (model: gpt-3.5-turbo-1106) ---

1. SYSTEM: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.

<metadata>

            <agent_name value="PromptOptimizerExpert" />

            <description value="The PromptOptimizerExpert enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />

            <version value="0" />

            <status value="wip"/>

        </metadata>



        <response_format>

            <type value="json" />

            <formatting value="false" />

            <line_breaks allowed="true" />

        </response_format>



        <agent>

            <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON." />



            <instructions>

                <role value="Prompt Optimizer" />

                <objective value="Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity." />



                <response_instructions><![CDATA[

                    Your response must be a JSON object:

                    ```json

                    {

                        "title": "Descriptive title",

                        "enhanced_prompt": "Optimized version of the prompt",

                        "context_layers": [

                            {"level": 1, "context": "Primary context layer"},

                            {"level": 2, "context": "Secondary contextual details"},

                            // Additional layers as needed

                        ]

                    }

                    ```]]>

                </response_instructions>



                <constants>

                    <item value="JSON output format: {'title', 'enhanced_prompt', 'context_layers'}." />

                </constants>



                <constraints>

                    <item value="Maintain logical, hierarchical organization." />

                    <item value="Avoid redundancy, ensure coherence." />

                    <item value="Limit length to double the original prompt." />

                </constraints>



                <process>

                    <item value="Analyze core message." />

                    <item value="Identify key themes." />

                    <item value="Generate concise title (max 50 chars)." />

                    <item value="Expand context layers meaningfully." />

                    <item value="Produce refined, concise prompt." />

                </process>



                <guidelines>

                    <item value="Use clear, structured language." />

                    <item value="Ensure relevancy of context layers." />

                    <item value="Prioritize more specific over generic, and actionable over vague instructions."/>

                    <item value="Maintain a logical flow and coherence within the combined instructions."/>

                </guidelines>



                <requirements>

                    <item value="Output must not exceed double the original length." />

                    <item value="Detailed enough for clarity and precision." />

                    <item value="JSON format containing: title, enhanced_prompt, and context_layers." />

                </requirements>



            </instructions>



        </agent>



        <prompt>

            <input value="You are a steadfast, detail-oriented assistant. Your critical task is to refine this prompt with absolute precision and laser-sharp clarity. Adhere strictly to all guidelines, ensuring the refined version fully captures and enhances the original's essence and intent." />

        </prompt>

2. USER: You are a steadfast, detail-oriented assistant. Your critical task is to refine this prompt with absolute precision and laser-sharp clarity. Adhere strictly to all guidelines, ensuring the refined version fully captures and enhances the original's essence and intent.



--- RESPONSE from openai (model: gpt-3.5-turbo-1106) ---

{

    "title": "Refining Prompt for Precision and Clarity",

    "enhanced_prompt": "Refine the prompt with precision and clarity while preserving the original essence and intent. Adhere strictly to the guidelines to ensure a thorough, detailed refinement.",

    "context_layers": [

        {"level": 1, "context": "The goal is to enhance the prompt's precision and clarity."},

        {"level": 2, "context": "Follow guidelines for a thorough and detailed refinement."},

        {"level": 2, "context": "Preserve the original essence and intent of the prompt."},

        {"level": 2, "context": "Ensure strict adherence to all guidelines for refining the prompt."}

    ]

}



--- REQUEST to openai (model: gpt-3.5-turbo-1106) ---

1. SYSTEM: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.

<metadata>

            <agent_name value="PromptOptimizerExpert" />

            <description value="The PromptOptimizerExpert enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />

            <version value="0" />

            <status value="wip"/>

        </metadata>



        <response_format>

            <type value="json" />

            <formatting value="false" />

            <line_breaks allowed="true" />

        </response_format>



        <agent>

            <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON." />



            <instructions>

                <role value="Prompt Optimizer" />

                <objective value="Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity." />



                <response_instructions><![CDATA[

                    Your response must be a JSON object:

                    ```json

                    {

                        "title": "Descriptive title",

                        "enhanced_prompt": "Optimized version of the prompt",

                        "context_layers": [

                            {"level": 1, "context": "Primary context layer"},

                            {"level": 2, "context": "Secondary contextual details"},

                            // Additional layers as needed

                        ]

                    }

                    ```]]>

                </response_instructions>



                <constants>

                    <item value="JSON output format: {'title', 'enhanced_prompt', 'context_layers'}." />

                </constants>



                <constraints>

                    <item value="Maintain logical, hierarchical organization." />

                    <item value="Avoid redundancy, ensure coherence." />

                    <item value="Limit length to double the original prompt." />

                </constraints>



                <process>

                    <item value="Analyze core message." />

                    <item value="Identify key themes." />

                    <item value="Generate concise title (max 50 chars)." />

                    <item value="Expand context layers meaningfully." />

                    <item value="Produce refined, concise prompt." />

                </process>



                <guidelines>

                    <item value="Use clear, structured language." />

                    <item value="Ensure relevancy of context layers." />

                    <item value="Prioritize more specific over generic, and actionable over vague instructions."/>

                    <item value="Maintain a logical flow and coherence within the combined instructions."/>

                </guidelines>



                <requirements>

                    <item value="Output must not exceed double the original length." />

                    <item value="Detailed enough for clarity and precision." />

                    <item value="JSON format containing: title, enhanced_prompt, and context_layers." />

                </requirements>



            </instructions>



        </agent>



        <prompt>

            <input value="{

    "title": "Refining Prompt for Precision and Clarity",

    "enhanced_prompt": "Refine the prompt with precision and clarity while preserving the original essence and intent. Adhere strictly to the guidelines to ensure a thorough, detailed refinement.",

    "context_layers": [

        {"level": 1, "context": "The goal is to enhance the prompt's precision and clarity."},

        {"level": 2, "context": "Follow guidelines for a thorough and detailed refinement."},

        {"level": 2, "context": "Preserve the original essence and intent of the prompt."},

        {"level": 2, "context": "Ensure strict adherence to all guidelines for refining the prompt."}

    ]

}" />

        </prompt>

2. USER: {

    "title": "Refining Prompt for Precision and Clarity",

    "enhanced_prompt": "Refine the prompt with precision and clarity while preserving the original essence and intent. Adhere strictly to the guidelines to ensure a thorough, detailed refinement.",

    "context_layers": [

        {"level": 1, "context": "The goal is to enhance the prompt's precision and clarity."},

        {"level": 2, "context": "Follow guidelines for a thorough and detailed refinement."},

        {"level": 2, "context": "Preserve the original essence and intent of the prompt."},

        {"level": 2, "context": "Ensure strict adherence to all guidelines for refining the prompt."}

    ]

}



--- RESPONSE from openai (model: gpt-3.5-turbo-1106) ---

{

    "title": "Refining Prompt for Precision and Clarity",

    "enhanced_prompt": "Refine the prompt with precision and clarity while preserving the original essence and intent. Adhere strictly to the guidelines to ensure a thorough, detailed refinement.",

    "context_layers": [

        {"level": 1, "context": "The goal is to enhance the prompt's precision and clarity."},

        {"level": 2, "context": "Follow guidelines for a thorough and detailed refinement."},

        {"level": 2, "context": "Preserve the original essence and intent of the prompt."},

        {"level": 2, "context": "Ensure strict adherence to all guidelines for refining the prompt."}

    ]

}



--- REQUEST to openai (model: gpt-3.5-turbo-1106) ---

1. SYSTEM: You are a highly skilled response synthesizer. Your task is to review a list of alternative responses to a prompt and produce a single, concise, and refined response. This new response should condense the most essential information from the alternatives, eliminate any redundancy or superfluous details, and present a clear and focused answer.

<metadata>

        <agent_name value="MultiResponseSelector" />

        <description value="The MultiResponseSelector agent is designed to analyze a list of alternative responses and synthesize them into a single, refined response that captures the most essential information while eliminating superfluous details." />

        <version value="a" />

        <status value="prototype" />

    </metadata>



    <response_format>

        <type value="plain_text" />

        <formatting value="false" />

        <line_breaks allowed="false" />

    </response_format>



    <agent>

        <system_prompt value="You are a highly skilled response synthesizer. Your task is to review a list of alternative responses to a prompt and produce a single, concise, and refined response. This new response should condense the most essential information from the alternatives, eliminate any redundancy or superfluous details, and present a clear and focused answer." />



        <instructions>

            <role value="Multi-Response Synthesizer and Selector" />

            <objective value="Condense a list of alternative responses into a single, rephrased response that captures the core essence and eliminates superfluity." />



            <constraints>

                <item value="Format: Single concise plain text line." />

                <item value="Input: A list of alternative responses (presented within the `<input>` tag)." />

                <item value="Output: A single rephrased response that synthesizes the essential information." />

                <item value="Focus on extracting and combining the most critical information from all alternatives." />

                <item value="Remove any redundant, repetitive, or unnecessary details present in the alternatives." />

                <item value="Ensure the rephrased response is clear, concise, and directly addresses the core of the original prompt." />

            </constraints>



            <process>

                <item value="Carefully read and analyze each alternative response in the provided list to understand its core message and key points." />

                <item value="Identify common themes, essential information, and any unique valuable insights across all alternatives." />

                <item value="Discard any redundant information, repetitions, or superfluous details that do not contribute to the core message." />

                <item value="Synthesize the identified essential information into a single, coherent, and concise response." />

                <item value="Rephrase and refine the synthesized response to ensure clarity, conciseness, and directness, removing any remaining superfluity." />

                <item value="Ensure the final response accurately reflects the combined essence of the input alternatives without adding new information or misrepresenting the original content." />

            </process>



            <guidelines>

                <item value="Prioritize the core meaning and essential information over verbatim inclusion of phrases from the alternatives." />

                <item value="Aim for maximum conciseness and clarity in the synthesized response." />

                <item value="Ensure the rephrased response is easily understandable and directly answers the implied or explicit question in the original prompt (even if not explicitly provided here)." />

                <item value="Focus on creating a response that is more effective and efficient than any of the individual alternatives by being synthesized and condensed." />

            </guidelines>



            <requirements>

                <item value="Output: A single, plain text response." />

                <item value="Content: Condense essential information from alternatives." />

                <item value="Conciseness: Remove all superfluous details and redundancy." />

                <item value="Clarity: Ensure the synthesized response is easily understood." />

            </requirements>



        </instructions>



    </agent>



    <prompt>

        <input value="Option 1:

{'agent_name': 'PromptOptimizerExpert', 'iterations': 1, 'outputs': ['{\n    "title": "Refining Prompt for Precision and Clarity",\n    "enhanced_prompt": "Refine the prompt with precision and clarity while preserving the original essence and intent. Adhere strictly to the guidelines to ensure a thorough, detailed refinement.",\n    "context_layers": [\n        {"level": 1, "context": "The goal is to enhance the prompt\'s precision and clarity."},\n        {"level": 2, "context": "Follow guidelines for a thorough and detailed refinement."},\n        {"level": 2, "context": "Preserve the original essence and intent of the prompt."},\n        {"level": 2, "context": "Ensure strict adherence to all guidelines for refining the prompt."}\n    ]\n}']}"/> <!-- In MultiResponseSelector, INPUT_PROMPT should be a list of alternative responses. -->

    </prompt>

2. USER: Option 1:

{'agent_name': 'PromptOptimizerExpert', 'iterations': 1, 'outputs': ['{\n    "title": "Refining Prompt for Precision and Clarity",\n    "enhanced_prompt": "Refine the prompt with precision and clarity while preserving the original essence and intent. Adhere strictly to the guidelines to ensure a thorough, detailed refinement.",\n    "context_layers": [\n        {"level": 1, "context": "The goal is to enhance the prompt\'s precision and clarity."},\n        {"level": 2, "context": "Follow guidelines for a thorough and detailed refinement."},\n        {"level": 2, "context": "Preserve the original essence and intent of the prompt."},\n        {"level": 2, "context": "Ensure strict adherence to all guidelines for refining the prompt."}\n    ]\n}']}



--- RESPONSE from openai (model: gpt-3.5-turbo-1106) ---

Refine the prompt to enhance precision and clarity while preserving the original essence and intent, strictly adhering to guidelines for a thorough and detailed refinement.

[Finished in 59.2s]

## Message 5

**Analyzing user's message**

The user provided a message with an output from executed Python code, which is related to a utility that refines prompts using a series of templates. The message doesn't contain a direct question but is intended to offer additional information for understanding the utility. Since it's not a question, I'll acknowledge the details they've shared, and I may ask if they need any clarifications or assistance! I should focus on how to continue the conversation or offer help as needed.

**Analyzing provided logs**

The user has shared the final recipe output and communicator log to help me understand the process. The final prompt refinement from the MultiResponseSelector agent gives the output: "Refine the prompt to enhance precision and clarity while preserving the original essence and intent, strictly adhering to guidelines." This marks the result after several iterations, starting with a request to the GPT model, followed by the same prompt being passed through. The third step aggregates it into a refined, clear output.

## Message 6

Thank you for sharing the complete output. Here's an organized analysis of what we’re seeing:

---

### 1. **Final Recipe Output**

- **Content:**  
  ```python
  {
      'agent_name': 'MultiResponseSelector', 
      'iterations': 1, 
      'outputs': [
          'Refine the prompt to enhance precision and clarity while preserving the original essence and intent, strictly adhering to guidelines for a thorough and detailed refinement.'
      ]
  }
  ```

- **Interpretation:**  
  - The final output is produced by the **MultiResponseSelector** agent.
  - It indicates that after a single iteration, the synthesized (or aggregated) response is the concise, plain-text refined prompt.
  - This final response captures the essence of the refinements made in previous steps.

---

### 2. **Detailed Communicator Log (Raw I/O)**

The log shows a step-by-step record of the interactions:

#### **First Interaction – Prompt Optimization (Iteration 1)**
- **Request:**  
  - **Provider & Model:** OpenAI using `gpt-3.5-turbo-1106`.
  - **System Message:**  
    - The instructions clearly describe the role of the **PromptOptimizerExpert** agent.  
    - The metadata, response format, and detailed instructions (including constraints, process, guidelines, and requirements) are provided.
  - **User Message:**  
    - Contains the original prompt:  
      > "You are a steadfast, detail-oriented assistant. Your critical task is to refine this prompt with absolute precision and laser-sharp clarity. Adhere strictly to all guidelines, ensuring the refined version fully captures and enhances the original's essence and intent."

- **Response:**  
  - The agent returns a JSON object with:  
    - A **title** ("Refining Prompt for Precision and Clarity").
    - An **enhanced_prompt** that is a refined version of the original prompt.
    - **Context layers** outlining hierarchical details that justify the refinement (e.g., focusing on clarity, adherence to guidelines, and preserving the original intent).

#### **Second Interaction – Reapplying the Prompt Optimizer**
- **Request:**  
  - The refined JSON from the previous step is fed back as the input.
- **Response:**  
  - The output remains consistent with the previous JSON response, indicating that the agent preserves its refinement when reprocessing the already refined prompt.

#### **Third Interaction – Aggregation via MultiResponseSelector**
- **Request:**  
  - A new system message is provided for the **MultiResponseSelector** agent.  
  - **Instructions:**  
    - Tasked with synthesizing a list of alternative responses.
    - It is designed to condense multiple inputs into a single, concise plain text response.
  - **Input:**  
    - The JSON response from the earlier iterations (listed as "Option 1") is provided as the input.
- **Response:**  
  - The MultiResponseSelector returns a single, plain text response:  
    > "Refine the prompt to enhance precision and clarity while preserving the original essence and intent, strictly adhering to guidelines for a thorough and detailed refinement."

---

### 3. **Overall Workflow Insight**

- **Chain Execution:**  
  - The utility successfully chains multiple prompt refinement iterations using file-based templates.
  - The first two interactions showcase the prompt being enhanced iteratively by the **PromptOptimizerExpert**.
  - The final step leverages the **MultiResponseSelector** to synthesize the responses, ensuring any redundant or extraneous details are eliminated.
  
- **Logging:**  
  - The **LowestLevelCommunicator** logs every request and response, which is invaluable for debugging and understanding the exact data flow between each LLM call.
  - The detailed log captures both the system and user inputs along with the full LLM outputs, making it easy to trace the transformation of the prompt.

- **Result Consistency:**  
  - The final output aligns well with the chain’s intended design—providing a refined, clear, and actionable prompt that remains within the constraints and guidelines defined in the templates.

---

### 4. **Conclusion**

The provided output confirms that:
- The prompt refinement chain operates as designed.
- The system effectively passes the `enhanced_prompt` between iterations.
- The aggregator (MultiResponseSelector) cleanly synthesizes the refinements into a single, concise final output.

This comprehensive log demonstrates the robustness of the utility in managing template-based prompt refinements and shows that each component— from low-level logging to multi-step synthesis—works in concert to produce the final refined prompt.

If you have further challenges or questions regarding the utility's behavior, feel free to let me know!

## Message 7

i want you to focus specifically in on how the output reflects the "history" of the llm interactions


## Message 8

**Exploring history in LLM output**

The user wants me to look at how the output reflects the "history" of LLM interactions. Specifically, I need to focus on how the final output connects to the sequence of interactions, which is captured in the Communicator Log. The output includes a JSON object showing things like agent_name, iterations, and refined prompts in the outputs list. I need to explain how each iteration and response is represented, paying attention to the log that documents the interaction history throughout the process.

**Explaining interaction history in output**

Each interaction in the process is logged, including the context, system prompt, user input, and final output—this history is not all reflected in the final output, but is captured in the communicator log. The final result comes from the refined, iterative process where each previous iteration’s output informs the next. The communicator log shows the chronological sequence of requests and responses, including agent and raw outputs, with each step building upon the last, such as how the PromptOptimizerExpert's output refined further inputs for the next step.

## Message 9

The output reflects the history of the LLM interactions in two key ways:

1. **Sequential Chaining of Interactions:**
   - **Iterative Refinement:**  
     The chain begins with the **PromptOptimizerExpert** processing the original prompt. Its JSON response (which includes a title, an enhanced prompt, and context layers) is then used as the input for the next iteration. This demonstrates how each LLM call builds on the result of the previous one.
   - **Aggregation Step:**  
     After refining the prompt through successive iterations, the **MultiResponseSelector** is invoked. This final step synthesizes the multiple responses (or the refined output) into a single, concise answer. The fact that the final recipe output comes from the MultiResponseSelector shows that the system not only processes a single iteration but can also aggregate a history of responses.

2. **Detailed Logging of Each Interaction:**
   - **Raw I/O Log:**  
     The printed communicator log captures every request and response in full detail. Each entry documents:
     - **The Request:**  
       The system message (defining the role and providing detailed instructions), along with the user’s input (either the original prompt or the refined JSON from a previous iteration).
     - **The Response:**  
       The LLM’s output, whether it’s a JSON object from the PromptOptimizerExpert or a plain text synthesis from the MultiResponseSelector.
   - **Chronological Record:**  
     Because each request and its corresponding response are logged sequentially, you can trace the entire evolution of the prompt—from the initial refinement to the final aggregated response. This “history” shows how the original input was transformed step by step, preserving context and ensuring consistency.

In summary, while the **Final Recipe Output** shows only the end result (the synthesized prompt), the **Full Communicator Log** provides a complete, chronological history of every LLM interaction. Together, they illustrate both the process (the iterative and aggregative steps) and the outcome (the refined prompt) of the prompt refinement chain.

## Message 10

to add further context, here's how the xml templates look. The following template shows `PromptOptimizerExpert`:



    ```xml

    <template>

        <metadata>

            <class_name value="PromptOptimizerExpert" />

            <description value="The PromptOptimizerExpert enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />

            <version value="0" />

            <status value="wip"/>

        </metadata>



        <response_format>

            <type value="json" />

            <formatting value="false" />

            <line_breaks allowed="true" />

        </response_format>



        <agent>

            <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON." />



            <instructions>

                <role value="Prompt Optimizer" />

                <objective value="Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity." />



                <response_instructions><![CDATA[

                    Your response must be a JSON object:

                    ```json

                    {

                        "title": "Descriptive title",

                        "enhanced_prompt": "Optimized version of the prompt",

                        "context_layers": [

                            {"level": 1, "context": "Primary context layer"},

                            {"level": 2, "context": "Secondary contextual details"},

                            // Additional layers as needed

                        ]

                    }

                    ```]]>

                </response_instructions>



                <constants>

                    <item value="JSON output format: {'title', 'enhanced_prompt', 'context_layers'}." />

                </constants>



                <constraints>

                    <item value="Maintain logical, hierarchical organization." />

                    <item value="Avoid redundancy, ensure coherence." />

                    <item value="Limit length to double the original prompt." />

                </constraints>



                <process>

                    <item value="Analyze core message." />

                    <item value="Identify key themes." />

                    <item value="Generate concise title (max 50 chars)." />

                    <item value="Expand context layers meaningfully." />

                    <item value="Produce refined, concise prompt." />

                </process>



                <guidelines>

                    <item value="Use clear, structured language." />

                    <item value="Ensure relevancy of context layers." />

                    <item value="Prioritize more specific over generic, and actionable over vague instructions."/>

                    <item value="Maintain a logical flow and coherence within the combined instructions."/>

                </guidelines>



                <requirements>

                    <item value="Output must not exceed double the original length." />

                    <item value="Detailed enough for clarity and precision." />

                    <item value="JSON format containing: title, enhanced_prompt, and context_layers." />

                </requirements>



            </instructions>



        </agent>



        <prompt>

            <input value="[INPUT_PROMPT]" />

        </prompt>



    </template>

    ```



The following template shows `MultiResponseSelector`:



    ```xml

    <template>



        <metadata>

            <class_name value="MultiResponseSelector" />

            <description value="The MultiResponseSelector agent is designed to analyze a list of alternative responses and synthesize them into a single, refined response that captures the most essential information while eliminating superfluous details." />

            <version value="a" />

            <status value="prototype" />

        </metadata>



        <response_format>

            <type value="plain_text" />

            <formatting value="false" />

            <line_breaks allowed="false" />

        </response_format>



        <agent>

            <system_prompt value="You are a highly skilled response synthesizer. Your task is to review a list of alternative responses to a prompt and produce a single, concise, and refined response. This new response should condense the most essential information from the alternatives, eliminate any redundancy or superfluous details, and present a clear and focused answer." />



            <instructions>

                <role value="Multi-Response Synthesizer and Selector" />

                <objective value="Condense a list of alternative responses into a single, rephrased response that captures the core essence and eliminates superfluity." />



                <constraints>

                    <item value="Format: Single concise plain text line." />

                    <item value="Input: A list of alternative responses (presented within the `<input>` tag)." />

                    <item value="Output: A single rephrased response that synthesizes the essential information." />

                    <item value="Focus on extracting and combining the most critical information from all alternatives." />

                    <item value="Remove any redundant, repetitive, or unnecessary details present in the alternatives." />

                    <item value="Ensure the rephrased response is clear, concise, and directly addresses the core of the original prompt." />

                </constraints>



                <process>

                    <item value="Carefully read and analyze each alternative response in the provided list to understand its core message and key points." />

                    <item value="Identify common themes, essential information, and any unique valuable insights across all alternatives." />

                    <item value="Discard any redundant information, repetitions, or superfluous details that do not contribute to the core message." />

                    <item value="Synthesize the identified essential information into a single, coherent, and concise response." />

                    <item value="Rephrase and refine the synthesized response to ensure clarity, conciseness, and directness, removing any remaining superfluity." />

                    <item value="Ensure the final response accurately reflects the combined essence of the input alternatives without adding new information or misrepresenting the original content." />

                </process>



                <guidelines>

                    <item value="Prioritize the core meaning and essential information over verbatim inclusion of phrases from the alternatives." />

                    <item value="Aim for maximum conciseness and clarity in the synthesized response." />

                    <item value="Ensure the rephrased response is easily understandable and directly answers the implied or explicit question in the original prompt (even if not explicitly provided here)." />

                    <item value="Focus on creating a response that is more effective and efficient than any of the individual alternatives by being synthesized and condensed." />

                </guidelines>



                <requirements>

                    <item value="Output: A single, plain text response." />

                    <item value="Content: Condense essential information from alternatives." />

                    <item value="Conciseness: Remove all superfluous details and redundancy." />

                    <item value="Clarity: Ensure the synthesized response is easily understood." />

                </requirements>



            </instructions>



        </agent>



        <prompt>

            <input value="[INPUT_PROMPT]"/> <!-- In MultiResponseSelector, INPUT_PROMPT should be a list of alternative responses. -->

        </prompt>



    </template>

    ```





    ```xml

    <template>

        <metadata>

            <agent_name value="PromptOptimizerExpert" />

            <description value="The PromptOptimizerExpert enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />

            <version value="0" />

            <status value="wip"/>

        </metadata>



        <response_format>

            <type value="json" />

            <formatting value="false" />

            <line_breaks allowed="true" />

        </response_format>



        <agent>

            <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON." />



            <instructions>

                <role value="Prompt Optimizer" />

                <objective value="Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity." />



                <response_instructions><![CDATA[

                    Your response must be a JSON object:

                    ```json

                    {

                        "title": "Descriptive title",

                        "enhanced_prompt": "Optimized version of the prompt",

                        "context_layers": [

                            {"level": 1, "context": "Primary context layer"},

                            {"level": 2, "context": "Secondary contextual details"},

                            // Additional layers as needed

                        ]

                    }

                    ```]]>

                </response_instructions>



                <constants>

                    <item value="JSON output format: {'title', 'enhanced_prompt', 'context_layers'}." />

                </constants>



                <constraints>

                    <item value="Maintain logical, hierarchical organization." />

                    <item value="Avoid redundancy, ensure coherence." />

                    <item value="Limit length to double the original prompt." />

                </constraints>



                <process>

                    <item value="Analyze core message." />

                    <item value="Identify key themes." />

                    <item value="Generate concise title (max 50 chars)." />

                    <item value="Expand context layers meaningfully." />

                    <item value="Produce refined, concise prompt." />

                </process>



                <guidelines>

                    <item value="Use clear, structured language." />

                    <item value="Ensure relevancy of context layers." />

                    <item value="Prioritize more specific over generic, and actionable over vague instructions."/>

                    <item value="Maintain a logical flow and coherence within the combined instructions."/>

                </guidelines>



                <requirements>

                    <item value="Output must not exceed double the original length." />

                    <item value="Detailed enough for clarity and precision." />

                    <item value="JSON format containing: title, enhanced_prompt, and context_layers." />

                </requirements>



            </instructions>



        </agent>



        <prompt>

            <input value="[INPUT_PROMPT]" />

        </prompt>



    </template>

    ```


## Message 11

**Synthesizing output with history**

The user is asking for a focus on how the output reflects the "history" of LLM interactions. The XML templates, like PromptOptimizerExpert and MultiResponseSelector, provide additional context regarding how the different agents interact. I'll need to integrate these templates into the discussion, ensuring the explanation ties together with the earlier points about chain-of-prompts and the final communicator log. The goal is to highlight how history is represented in the output, using the provided context from the templates.

**Explaining history of agent interactions**

The user wants clarity on how the output reflects the "history" of LLM interactions. The process involves multiple agents: PromptOptimizerExpert and MultiResponseSelector. 

First, the PromptOptimizerExpert generates a JSON with "title," "enhanced_prompt," and "context_layers." This output becomes part of the history. Next, MultiResponseSelector takes the previous responses and merges them into one final output. The communicator log reveals a clear sequence: from initial prompts to PromptOptimizerExpert, its iterations, and the final synthesis by MultiResponseSelector. Each log entry documents the interactions step by step.

**Final output reflecting history**

The conversation log captures the entire chain of LLM interactions. XML templates provide structure to each step. For example, PromptOptimizerExpert outputs JSON with context layers to refine the prompt, which is recorded in the log as the first step. MultiResponseSelector takes these refinements and synthesizes the final plain text output. Each step is logged, showing how the prompt evolved from the original to the final result. The log essentially preserves the history of all interactions, detailing each transformation.

## Message 12

Let's break down how the output encapsulates the history of the LLM interactions, using the provided XML templates as a lens:

---

### 1. **Structured Roles and Expectations via XML Templates**

- **PromptOptimizerExpert Template:**  
  - **Structure Defined:**  
    - The XML clearly stipulates that any response must be a JSON object with keys like `"title"`, `"enhanced_prompt"`, and `"context_layers"`.  
    - It lays out detailed instructions (including constraints, process steps, and guidelines) that determine how the prompt is to be refined.
  - **Impact on History:**  
    - When the agent processes the original prompt, it produces a JSON response that is rich in hierarchical details.  
    - These details (such as multiple context layers) serve as markers showing how the prompt was analyzed and transformed during that specific interaction.

- **MultiResponseSelector Template:**  
  - **Structure Defined:**  
    - This template specifies that the agent should synthesize a list of alternative responses (i.e., previous outputs) into one concise plain-text response.
  - **Impact on History:**  
    - It takes the JSON output from PromptOptimizerExpert (or potentially multiple alternative responses) and aggregates them.
    - The final output is a condensed version that reflects the cumulative refinement history, eliminating redundancies.

---

### 2. **Chained Interactions Reflecting Evolution**

- **Sequential Processing:**  
  - **Iteration 1:**  
    - The **PromptOptimizerExpert** is first invoked with the original prompt.  
    - Its JSON response not only refines the prompt but also embeds its internal “reasoning” in the form of context layers (e.g., a primary context layer and secondary details).  
    - This JSON output is a snapshot of that stage’s transformation.
  - **Iteration 2 / Aggregation:**  
    - The output from the first agent is then used as input for the next interaction (either a repeated refinement or, as in this case, passed to the **MultiResponseSelector**).
    - The **MultiResponseSelector** aggregates this information, synthesizing it into a final plain-text answer.
    - This step effectively “remembers” the previous refinement history by using it as the basis for further condensation.

---

### 3. **Detailed Logging as the Historical Record**

- **Communicator Log Entries:**  
  - **Full Traceability:**  
    - Each LLM interaction is recorded—every request and response is logged in full detail.
    - The log includes system prompts, user inputs, metadata, and the complete responses (including the JSON structure with context layers from PromptOptimizerExpert and the plain text from MultiResponseSelector).
  - **Chronological Record:**  
    - The log shows the first request (with detailed instructions from PromptOptimizerExpert) and its JSON response.
    - It then records subsequent requests (including the repeated or chained requests) and the final aggregation step by MultiResponseSelector.
    - This chronological log is the “history” that documents how the input evolved at each stage.

---

### 4. **Summarizing the Historical Reflection in the Output**

- **Final Recipe Output:**  
  - The final output (from MultiResponseSelector) is a plain text line that synthesizes the refined prompt.  
  - Although it is concise, it represents the culmination of all previous interactions.
- **Implicit History in the Output:**  
  - Even though the final output appears as a single line, its derivation is traceable through the detailed communicator log.  
  - Each step’s output (especially the JSON responses with hierarchical context layers) tells the story of how the prompt was incrementally enhanced.
  - In essence, the final product is not created in isolation—it’s the end result of an orchestrated, multi-step dialogue where each step builds on the last.

---

### **Conclusion**

The output reflects the history of the LLM interactions in two significant ways:

1. **Through the Chained, Iterative Refinement Process:**  
   - The XML templates enforce a structure that ensures each agent (PromptOptimizerExpert and MultiResponseSelector) contributes a specific kind of output.  
   - These outputs (detailed JSON objects and then a synthesized plain text) show how the original prompt was progressively refined and distilled.

2. **Via the Detailed, Chronological Logging:**  
   - The full communicator log records every interaction—from the first refinement to the final aggregation.  
   - This log serves as an audit trail, preserving the "history" of the transformations, and provides transparency into how each step influenced the final result.

Thus, even though the final recipe output is a single, concise response, its lineage is clearly documented through both the structure defined by the XML templates and the comprehensive log of interactions.

## Message 13

here's something you didn't consider: one of the most powerfull aspects of the utility is it's effectiveness and robustness in that it doesn't really **parse** the formats, instead we deal with strings. even though the datatype might represent a dictionary, we don't need to interpret it as such. as long as we deal with strings, we have **immense** flexibility with respect to the ease at which we can make draftic improvements by simple means. the reason why this matters, is that in your proposition you didn't account for the possibility to define the **representation** of how it's output.

## Message 14

**Considering flexibility in output**

The user provided an XML template and code output details, focusing on how the utility works with strings. The key point is that the utility doesn't parse the formats but handles the data as strings. This approach gives a lot of flexibility, allowing for simple yet impactful improvements. The user mentioned that, in our initial proposition, we didn’t fully account for how the output's representation could be defined, which is why this flexibility is so crucial.

**Highlighting string-based flexibility**

The user wants to emphasize that one of the utility's powerful aspects is its handling of data as strings, without parsing the output. This approach gives flexibility, allowing for simple formatting changes and easy modifications to the representation (whether it’s JSON, text, etc.). Since the utility doesn’t enforce a specific data structure, the flexibility of its string-based approach means it can adapt and iterate quickly. I'll incorporate this key point into the final answer for clarity.

## Message 15

That's an excellent observation—one of the utility's strengths lies in its string-based handling of outputs. Here’s how this design decision plays a crucial role in its effectiveness and flexibility:

1. **String-Centric Approach:**
   - **No Strict Parsing:**  
     The system treats all LLM outputs as plain strings, even if those strings represent structured data (like a JSON dictionary). This means that the outputs aren't forced into a rigid data structure during processing.
   - **Representation Agnosticism:**  
     Because there's no internal requirement to parse and validate against a specific schema, the utility can handle any string format. Whether it's JSON, plain text, or another format, the system simply passes the string along.

2. **Flexibility in Drafting and Iteration:**
   - **Ease of Draftic Improvements:**  
     Since the outputs remain as raw strings, making iterative changes (or "draftic" improvements) is straightforward. You can easily tweak formatting, adjust content, or experiment with different representations without the overhead of re-parsing or re-serializing structured data.
   - **Rapid Prototyping:**  
     This approach allows you to quickly test different output formats. For example, you might start with a JSON-like representation for clarity and then switch to a plain text summary—all without needing to modify the underlying logic that handles the data.

3. **Defining the Output Representation:**
   - **Customizable Output Format:**  
     Because the system doesn't enforce a strict parse into a dictionary or another data type, you have complete control over how the output is represented. This means you can decide on the best representation for your needs—be it human-readable plain text, a structured JSON for further processing, or even a hybrid format.
   - **Robustness to Format Changes:**  
     The utility's robustness is enhanced by this flexibility. Changes in output representation (for instance, modifying how context layers are shown or altering the format of the enhanced prompt) can be achieved through simple string operations rather than extensive refactoring of data parsing logic.

4. **Implications for the Interaction History:**
   - **Historical Record as Strings:**  
     The detailed communicator log records each interaction as a string. This log is not just a dump of structured data but a narrative of how the prompt evolved. Every system and user message, every instruction and response, remains in its original textual form.
   - **Traceable Transformation:**  
     Even if a string represents a dictionary, its literal content (including keys, values, and formatting) serves as an immutable record of that interaction. This means you can always trace how an input was transformed into an output, regardless of whether that output was conceptually a dictionary or a plain text message.

---

**In summary:**  
The utility’s decision to work with strings rather than parsed data structures provides immense flexibility. It allows for dynamic, iterative refinements and makes it easy to redefine or improve the output representation. This design choice ensures that the system remains robust and adaptable—capable of handling changes in output format without needing to overhaul the processing logic, and it enables a clear, traceable history of the LLM interactions.

## Message 16

with that in mind, there's a correction that needs to be made in the code; currently each processed prompt is based on the extracted data in `_extract_template_parts`, but this is completely unneccessary-because we're using placeholders to search/replace we can actually use the full string-representation of the template. the problem by extracting data from the templates is that if e.g. the template is different and the extraction doesn't work, it will basically corrupt the template. we still need to extract the system prompt, but there's no need to **extract** the agent_instructions in `execute_prompt_refinement_chain_from_file`.

## Message 17

**Proposing code correction**

It seems the issue here is with extracting data from templates in the `_extract_template_parts` method—it's unnecessary when we're using placeholders for search/replace. The problem is that if extraction fails, it corrupts the template. We still need to extract the system prompt, but we don't have to extract the `agent_instructions` in the `execute_prompt_refinement_chain_from_file` method. So, I'll need to suggest modifying this method to avoid extraction for the `agent_instructions`, keeping the template intact.

**Modifying code structure**

The user's goal is to avoid extracting `agent_instructions` from the template, while still extracting the `system_prompt`. I need to propose a change to the `execute_prompt_refinement_chain_from_file`. Instead of using `_extract_template_parts`, we’ll use `_extract_value_from_content` exclusively to extract the `system_prompt`. The `agent_instructions` could either be skipped or replaced with an empty string. We’ll then adjust the message-building function to either omit or pass an empty string for instructions. This will ensure proper functionality while respecting the user’s requirements.

**Proposing code changes**

The user's request is to modify the `execute_prompt_refinement_chain_from_file` function. Instead of extracting `agent_instructions` via `_extract_template_parts`, we should pass the full template string (with replaced placeholders) and only extract the `system_prompt` using `_extract_value_from_content`. The `agent_instructions` can be omitted by passing an empty string. Here's the revision:

```python
content = self.template_manager.load_template_from_file(template_filepath, input_prompt)
if not content:
    return None

system_prompt = self.template_manager._extract_value_from_content(content, r'<system_prompt value="([^"]*)"\s*/>')
for _ in range(refinement_count):
    msgs = self._build_messages(system_prompt, "", prompt)
    ...
```

This should ensure proper handling without erroneous template extraction issues.

## Message 18

Below is one way to adjust the code. The key idea is to stop using `_extract_template_parts` to pull out both the system prompt and the agent instructions. Instead, we extract only the system prompt and then let the full (placeholder‑processed) template—now in its complete string form—serve as the basis for the LLM call. This avoids the risk that a failure to match the expected format will inadvertently “corrupt” the template. In practice, you might change the code in the `execute_prompt_refinement_chain_from_file` method as follows:

---

**Before (Excerpt):**

```python
content = self.template_manager.load_template_from_file(template_filepath, input_prompt)
if not content:
    return None

if display_instructions:
    logger.info("=" * 60)
    logger.info(content)
    logger.info("=" * 60)

agent_name = self.template_manager._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>')
system_prompt, response_format, agent_instructions = self.template_manager._extract_template_parts(content)
prompt = input_prompt
results = []

for _ in range(refinement_count):
    msgs = self._build_messages(system_prompt, agent_instructions, prompt)
    refined = self.agent.generate_response(
        msgs,
        model_name=model_name,
        temperature=temperature,
        max_tokens=max_tokens,
    )
    # ... further processing ...
```

---

**After (Revised):**

```python
content = self.template_manager.load_template_from_file(template_filepath, input_prompt)
if not content:
    return None

if display_instructions:
    logger.info("=" * 60)
    logger.info(content)
    logger.info("=" * 60)

# Extract only the system prompt from the template.
system_prompt = self.template_manager._extract_value_from_content(
    content, r'<system_prompt value="([^"]*)"\s*/>'
)
# Optionally, extract the agent name if needed.
agent_name = self.template_manager._extract_value_from_content(
    content, r'<agent_name value="([^"]*)"\s*/>'
)

# We no longer extract agent_instructions; instead, use an empty string
# (or any default value) so that the full template string (with placeholders
# already replaced) remains intact.
agent_instructions = ""

prompt = input_prompt
results = []

for _ in range(refinement_count):
    # Build the message using only the system prompt and the user prompt.
    msgs = self._build_messages(system_prompt, agent_instructions, prompt)
    refined = self.agent.generate_response(
        msgs,
        model_name=model_name,
        temperature=temperature,
        max_tokens=max_tokens,
    )
    if refined:
        refined_str = refined
        try:
            data = json.loads(refined_str)
            refined_str = json.dumps(data, indent=4)
        except json.JSONDecodeError:
            pass

        results.append(refined)

        # Pass along the enhanced prompt if present
        next_prompt = refined
        try:
            data = json.loads(refined)
            if isinstance(data, dict) and "enhanced_prompt" in data:
                next_prompt = data["enhanced_prompt"]
        except (TypeError, json.JSONDecodeError):
            pass

        prompt = next_prompt

return results
```

---

### Explanation

1. **Extract Only the System Prompt:**  
   The revised code calls `_extract_value_from_content` with a regex for the system prompt. This is the only extraction we need—if this extraction fails, that would still be a problem, but it’s simpler to debug one extraction than multiple.  
   
2. **Avoid Agent Instruction Extraction:**  
   Instead of using `_extract_template_parts` (which returns system prompt, response format, and agent instructions), we now forgo extracting the agent instructions altogether. We set `agent_instructions` to an empty string (or a default if you prefer) so that our call to `_build_messages` remains valid.  
   
3. **Full Template as String:**  
   Since `load_template_from_file` already handles placeholder replacement, the full string (with its proper formatting) is preserved. This gives you the flexibility to modify the output representation later without worrying about structural extraction errors.

4. **Building the Message:**  
   The `_build_messages` method still concatenates the system prompt and instructions if available. By passing an empty string for instructions, you ensure that only the correctly extracted system prompt and the user’s prompt are used.  
   
This change removes an unnecessary dependency on rigid template parsing and fully leverages the string-based, flexible design of your utility.

## Message 19

please update `load_template_from_file` with a new placeholder for "[FILENAME]" which will get replaced by the filename of the file for the template:



    def load_template_from_file(self, template_filepath, initial_prompt=""):

        parsed_template = self._parse_template(template_filepath)

        if not parsed_template:

            return None



        content = parsed_template["content"]

        placeholders = {

            "[FILENAME]": this should be the filename of the template,

            "[OUTPUT_FORMAT]": "plain_text",

            "[ORIGINAL_PROMPT_LENGTH]": str(len(initial_prompt)),

            "[RESPONSE_PROMPT_LENGTH]": str(int(len(initial_prompt) * 0.9)),

            "[INPUT_PROMPT]": initial_prompt,

            "[ADDITIONAL_CONSTRAINTS]": "",

            "[ADDITIONAL_PROCESS_STEPS]": "",

            "[ADDITIONAL_GUIDELINES]": "",

            "[ADDITIONAL_REQUIREMENTS]": "",

        }

        for placeholder, value in placeholders.items():

            value_str = str(value)

            content = content.replace(placeholder, value_str)



        return content

## Message 20

**Updating template code**

Alright, I need to update the `load_template_from_file` method to include a new placeholder "[FILENAME]", which will be replaced by the template's filename.

I think the value of "[FILENAME]" should use `os.path.basename(template_filepath)` to extract the file name from the full file path.

So in the code, I need to replace the line:

`"[FILENAME]": this should be the filename of the template,`

with something like:

`"[FILENAME]": os.path.basename(template_filepath),`

## Message 21

Below is the updated `load_template_from_file` method. In this revision, the `[FILENAME]` placeholder is replaced by the actual filename (extracted via `os.path.basename`) of the template file:

```python
def load_template_from_file(self, template_filepath, initial_prompt=""):
    parsed_template = self._parse_template(template_filepath)
    if not parsed_template:
        return None

    content = parsed_template["content"]
    placeholders = {
        "[FILENAME]": os.path.basename(template_filepath),
        "[OUTPUT_FORMAT]": "plain_text",
        "[ORIGINAL_PROMPT_LENGTH]": str(len(initial_prompt)),
        "[RESPONSE_PROMPT_LENGTH]": str(int(len(initial_prompt) * 0.9)),
        "[INPUT_PROMPT]": initial_prompt,
        "[ADDITIONAL_CONSTRAINTS]": "",
        "[ADDITIONAL_PROCESS_STEPS]": "",
        "[ADDITIONAL_GUIDELINES]": "",
        "[ADDITIONAL_REQUIREMENTS]": "",
    }
    for placeholder, value in placeholders.items():
        value_str = str(value)
        content = content.replace(placeholder, value_str)

    return content
```

### Explanation

- **Extracting the Filename:**  
  The code uses `os.path.basename(template_filepath)` to obtain just the filename from the full path. This value replaces the `[FILENAME]` placeholder in the template.

- **Other Placeholders:**  
  The rest of the placeholders (such as `[OUTPUT_FORMAT]`, `[INPUT_PROMPT]`, etc.) are handled as before.

This change ensures that every time a template is loaded, the `[FILENAME]` placeholder is automatically replaced with the actual file name, providing a consistent and easily modifiable output representation.

## Message 22

what would be a more well-suited method name for `load_template_from_file`?

## Message 23

**Suggesting method name**

The original method name `load_template_from_file` doesn’t fully capture its functionality, which includes parsing, substituting placeholders, and returning processed content. It’s more than just loading the template! I’m thinking names like "render_template_from_file," "process_template_file," or "prepare_template." Since the function does placeholder substitution, "render_template_from_file" seems like a good option, especially given how common the term "render" is in template systems. Alternatively, "prepare_template_from_file" could also work.

## Message 24

A more descriptive name could be **`render_template_from_file`**. This name conveys that the method not only loads the template file but also processes (or "renders") it by replacing placeholders, aligning with common templating terminology.

## Message 25

what would be a more well-suited method name for `load_template_from_file` (e.g. something to convey that it does more than just loading, it kind of "prepares" the template)?

## Message 26

**Considering method name**

The user wants a method name that reflects both loading and preparing a template, including placeholder replacement. I’m noting possible names like “prepare_template_from_file,” “get_prepared_template,” “load_and_prepare_template,” and “prepare_template.” 

After thinking more, “prepare_template_from_file” seems best, since it clearly conveys both actions.

I’m also considering "preprocess_template_from_file," but it might be more specific. “Preload” doesn’t fit since placeholders are being replaced actively.

Ultimately, “prepare_template_from_file” seems like a good fit!

## Message 27

A more descriptive name that emphasizes both the loading and the preparation (i.e., placeholder substitution) might be:

**`render_template_from_file`**

This name suggests that the method does more than simply loading—it "renders" the template by processing its placeholders and preparing it for use. Other alternatives could include:

- `prepare_template_from_file`
- `load_and_render_template`

But **`render_template_from_file`** is concise and clearly conveys the dual purpose of the method.

## Message 28

choose something much more intuitive

## Message 29

**Deciding on method name**

The user wants a more intuitive method name. I'll consider names like "prepare_template" or "get_template_content," because they convey that the function loads the file, replaces placeholders, and gives the final content. "Assemble_template," "build_template," or "compile_template" also come to mind, but they could feel a bit generic or technical. "Render_template" is another option, but maybe that's less intuitive for this context. I think "prepare_template" is simple and clear, so that might be the best fit.

**Choosing method name**

The user wants something intuitive, and I’m considering a few options. "Prepare_template" stands out because it suggests the template is loaded and processed, which fits the description. "Render_template" is widely used, but it doesn't specify that it's coming from a file, and I’m unsure if it's intuitive enough for the user’s request. "Process_template_from_file" captures part of it, but it feels a bit vague. I’ll go with "prepare_template_from_file," since it’s clear and fits the function of loading and replacing placeholders.

## Message 30

A more intuitive name that clearly conveys both the loading and the placeholder substitution might be:

**`populate_template_from_file`**

This name implies that the method not only reads the template from disk but also fills in its placeholders with the appropriate values.

## Message 31

A more intuitive and natural name could be:

### **`populate_template`**  

#### **Why this name?**
- **"Populate"** conveys that the method not only loads the template but also **fills in placeholders** with actual values.
- It keeps the name simple, clear, and immediately understandable.
- It avoids unnecessary verbosity while still making it obvious that the method prepares the template for use.

---

### **Alternative Intuitive Names**
- **`fill_template`** → Simple, emphasizes placeholder replacement.
- **`prepare_template`** → General, but slightly vague compared to "populate".
- **`build_template`** → Implies some processing but might suggest structural changes.
- **`finalize_template`** → Sounds like it’s the last step before execution.

However, **`populate_template`** feels the most intuitive because it directly describes what the function is doing—taking a template and inserting the necessary values.

## Message 32

please fix [TEMPLATE_START] also:

    def prepare_template(self, template_filepath, input_prompt=""):
        parsed_template = self._parse_template(template_filepath)
        if not parsed_template:
            return None

        content = parsed_template["content"]
        placeholders = {
            "[TEMPLATE_START]": extension of the file,
            "[FILENAME]": os.path.basename(template_filepath),
            "[OUTPUT_FORMAT]": "plain_text",
            "[ORIGINAL_PROMPT_LENGTH]": str(len(input_prompt)),
            "[RESPONSE_PROMPT_LENGTH]": str(int(len(input_prompt) * 0.9)),
            "[INPUT_PROMPT]": input_prompt,
            "[ADDITIONAL_CONSTRAINTS]": "",
            "[ADDITIONAL_PROCESS_STEPS]": "",
            "[ADDITIONAL_GUIDELINES]": "",
            "[ADDITIONAL_REQUIREMENTS]": "",
        }
        for placeholder, value in placeholders.items():
            value_str = str(value)
            content = content.replace(placeholder, value_str)

        return content


## Message 33

Here’s the corrected `prepare_template` method with `[TEMPLATE_START]` properly replaced by the file extension:

```python
def prepare_template(self, template_filepath, input_prompt=""):
    parsed_template = self._parse_template(template_filepath)
    if not parsed_template:
        return None

    content = parsed_template["content"]
    placeholders = {
        "[TEMPLATE_START]": os.path.splitext(template_filepath)[1],  # Extracts the file extension
        "[FILENAME]": os.path.basename(template_filepath),
        "[OUTPUT_FORMAT]": "plain_text",
        "[ORIGINAL_PROMPT_LENGTH]": str(len(input_prompt)),
        "[RESPONSE_PROMPT_LENGTH]": str(int(len(input_prompt) * 0.9)),
        "[INPUT_PROMPT]": input_prompt,
        "[ADDITIONAL_CONSTRAINTS]": "",
        "[ADDITIONAL_PROCESS_STEPS]": "",
        "[ADDITIONAL_GUIDELINES]": "",
        "[ADDITIONAL_REQUIREMENTS]": "",
    }

    for placeholder, value in placeholders.items():
        value_str = str(value)
        content = content.replace(placeholder, value_str)

    return content
```

### Fixes & Improvements:
- **`[TEMPLATE_START]` now correctly maps to the file extension.**  
  - `os.path.splitext(template_filepath)[1]` extracts the file extension (e.g., `".xml"` for an XML file).
  - If needed, this can be stripped of the leading dot with `.lstrip(".")` (e.g., `"xml"` instead of `".xml"`).

This ensures that templates can dynamically reference their own file format if needed. 🚀

## Message 34

please update to this version

## Message 35

please update to this version:

# Project Files Documentation for `py_llm_framework`

### File Structure

```
├── PromptOptimizerExpert.xml
└── template_runner.py
```


#### `PromptOptimizerExpert.xml`

```xml
<!--
<metadata>
    <agent_name value="[FILENAME]" />
    <description value="The [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />
    <version value="0" />
    <status value="wip" />
</metadata>
-->

[TEMPLATE_START]
<template>
    <purpose value="[FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />
    <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON." />

    <agent>
        <name value="[FILENAME]" />
        <role value="Prompt Optimizer" />
        <objective value="Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity." />
        <instructions>
            <constants>
                <input value="JSON output format: {'title', 'enhanced_prompt', 'context_layers'}." />
            </constants>
            <constraints>
                <input value="Maintain logical, hierarchical organization." />
                <input value="Avoid redundancy, ensure coherence." />
                <input value="Limit length to double the original prompt." />
            </constraints>
            <process>
                <input value="Analyze core message." />
                <input value="Identify key themes." />
                <input value="Generate concise title (max 50 chars)." />
                <input value="Expand context layers meaningfully." />
                <input value="Produce refined, concise prompt." />
            </process>
            <guidelines>
                <input value="Use clear, structured language." />
                <input value="Ensure relevancy of context layers." />
                <input value="Prioritize more specific over generic, and actionable over vague instructions." />
                <input value="Maintain a logical flow and coherence within the combined instructions." />
            </guidelines>
            <requirements>
                <input value="Output must not exceed double the original length." />
                <input value="Detailed enough for clarity and precision." />
                <input value="JSON format containing: title, enhanced_prompt, and context_layers." />
            </requirements>
        </instructions>
    </agent>

[HEADER]
    <response_instructions>
        <![CDATA[
            Your response must be a JSON object:
            ```json
            {
                "title": "Descriptive title",
                "enhanced_prompt": "Optimized version of the prompt",
                "context_layers": [
                    {"level": 1, "context": "Primary context layer"},
                    {"level": 2, "context": "Secondary contextual details"},
                    // Additional layers as needed
                ]
            }
        ]]>
    </response_instructions>
[FOOTER]
</template>
<user_prompt>
    [INPUT_PROMPT]
</user_prompt>

[TEMPLATE_END]
```


#### `template_runner.py`

```python
#!/usr/bin/env python3
"""
template_runnet.py
A single-file script for managing templates, LLM interactions, and orchestrating prompt refinements.
Now updated to pass each template's 'enhanced_prompt' as input to the next template in the chain.
"""

import os
import sys
import re
import glob
import json
from typing import List, Dict, Union, Optional
from pathlib import Path

from dotenv import load_dotenv
from loguru import logger

# Provider SDKs
from openai import OpenAI
from anthropic import Anthropic

# -------------------------------------------------------
# 1. LowestLevelCommunicator
# -------------------------------------------------------
class LowestLevelCommunicator:
    """
    Captures raw input and output strings at the lowest level,
    preserving the entire stream before any filtering or transformation.
    """
    def __init__(self):
        self.raw_interactions = []

    def record_request(self, provider: str, model_name: str, messages: List[Dict[str, str]]):
        """
        Called just before sending the request to the LLM.
        """
        self.raw_interactions.append({
            "direction": "request",
            "provider": provider,
            "model_name": model_name,
            "messages": messages,
        })

    def record_response(self, provider: str, model_name: str, response_text: str):
        """
        Called immediately after receiving the raw text from the LLM.
        """
        self.raw_interactions.append({
            "direction": "response",
            "provider": provider,
            "model_name": model_name,
            "content": response_text,
        })

    def get_all_interactions(self) -> List[Dict]:
        """
        Return the raw record of all requests/responses.
        """
        return self.raw_interactions

    def get_formatted_output(self) -> str:
        """
        Pretty-print a summary of the captured interactions.
        """
        lines = []
        for entry in self.raw_interactions:
            direction = entry["direction"].upper()
            provider = entry["provider"]
            model_name = entry["model_name"]

            if direction == "REQUEST":
                lines.append(f"--- REQUEST to {provider} (model: {model_name}) ---")
                for i, msg in enumerate(entry["messages"], start=1):
                    role = msg.get("role", "").upper()
                    content = msg.get("content", "")
                    lines.append(f"{i}. {role}: {content}")
            else:
                lines.append(f"--- RESPONSE from {provider} (model: {model_name}) ---")
                lines.append(entry["content"])
            lines.append("")

        return "\n".join(lines).strip()

# -------------------------------------------------------
# 2. Global Configuration
# -------------------------------------------------------
class Config:
    """
    Global settings
    """

    PROVIDER_OPENAI = "openai"
    PROVIDER_DEEPSEEK = "deepseek"
    PROVIDER_ANTHROPIC = "anthropic"

    AVAILABLE_MODELS = {
        PROVIDER_OPENAI: {
            "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
            "gpt-4": "Latest GPT-4 stable release",
            "gpt-4-0125-preview": "Preview GPT-4 Turbo",
            "gpt-4-0613": "June 2023 GPT-4 snapshot",
            "gpt-4-1106-preview": "Preview GPT-4 Turbo",
            "gpt-4-turbo": "Latest GPT-4 Turbo release",
            "gpt-4-turbo-2024-04-09": "GPT-4 Turbo w/ vision",
            "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
            "gpt-4o": "Base GPT-4o model",
            "gpt-4o-mini": "Lightweight GPT-4o variant",
        },
        PROVIDER_DEEPSEEK: {
            "deepseek-chat": "DeepSeek Chat model",
            "deepseek-reasoner": "Specialized reasoning model",
        },
        PROVIDER_ANTHROPIC: {
            "claude-2": "Base Claude 2 model",
            "claude-2.0": "Enhanced Claude 2.0",
            "claude-2.1": "Latest Claude 2.1 release",
            "claude-3-opus-20240229": "Claude 3 Opus",
            "claude-3-sonnet-20240229": "Claude 3 Sonnet",
            "claude-3-haiku-20240307": "Claude 3 Haiku",
        },
    }

    DEFAULT_MODEL_PARAMS = {
        PROVIDER_OPENAI: {
            "model_name": "gpt-4-turbo-preview", # (5) used occasionally
            "model_name": "gpt-4o",              # (4) debugging
            "model_name": "gpt-4-turbo",         # (3) used often
            "model_name": "gpt-3.5-turbo",       # (3) most used
            "model_name": "gpt-3.5-turbo-1106",  # (1) most used
            "temperature": 0.7,
            "max_tokens": 800,
        },
        PROVIDER_DEEPSEEK: {
            "model_name": "deepseek-reasoner",
            "model_name": "deepseek-coder",
            "model_name": "deepseek-chat",
            "temperature": 0.7,
            "max_tokens": 800,
        },
        PROVIDER_ANTHROPIC: {
            "model_name": "claude-2.1",
            "model_name": "claude-3-opus-20240229",
            "temperature": 0.7,
            "max_tokens": 800,
        },
    }

    API_KEY_ENV_VARS = {
        PROVIDER_OPENAI: "OPENAI_API_KEY",
        PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",
        PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",
    }

    BASE_URLS = {
        PROVIDER_DEEPSEEK: "https://api.deepseek.com",
    }

    # Overriding allows for switching between providers by simply reordering the lines.
    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
    DEFAULT_PROVIDER = PROVIDER_OPENAI

    def __init__(self):
        load_dotenv()
        self.configure_utf8_encoding()
        self.provider = self.DEFAULT_PROVIDER.lower()
        self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]
        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()
        self.setup_logger()

    def configure_utf8_encoding(self):
        """
        Ensure UTF-8 encoding for standard output and error streams.
        """
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    def setup_logger(self):
        """
        YAML logging via Loguru: clears logs, sets global context, and configures sinks
        """
        log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"
        log_filepath = os.path.join(self.log_dir, log_filename)
        open(log_filepath, "w").close()
        logger.remove()
        logger.configure(
            extra={
                "provider": self.provider,
                "model": self.model_params.get("model_name"),
            }
        )

        def yaml_sink(log_message):
            log_record = log_message.record
            formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")
            formatted_level = f"!{log_record['level'].name}"
            logger_name = log_record["name"]
            formatted_function_name = f"*{log_record['function']}"
            line_number = log_record["line"]
            extra_provider = log_record["extra"].get("provider")
            extra_model = log_record["extra"].get("model")
            log_message_content = log_record["message"]

            if "\n" in log_message_content:
                formatted_message = "|\n" + "\n".join(
                    f"  {line}" for line in log_message_content.splitlines()
                )
            else:
                formatted_message = (
                    f"'{log_message_content}'"
                    if ":" in log_message_content
                    else log_message_content
                )

            log_lines = [
                f"- time: {formatted_timestamp}",
                f"  level: {formatted_level}",
                f"  name: {logger_name}",
                f"  funcName: {formatted_function_name}",
                f"  lineno: {line_number}",
            ]
            if extra_provider is not None:
                log_lines.append(f"  provider: {extra_provider}")
            if extra_model is not None:
                log_lines.append(f"  model: {extra_model}")

            log_lines.append(f"  message: {formatted_message}")
            log_lines.append("")

            with open(log_filepath, "a", encoding="utf-8") as log_file:
                log_file.write("\n".join(log_lines) + "\n")

        logger.add(yaml_sink, level="DEBUG", enqueue=True, format="{message}")

# -------------------------------------------------------
# 3. LLM Interactions
# -------------------------------------------------------
class LLMInteractions:
    """
    Handles interactions with LLM APIs.
    """

    CLIENT_FACTORIES = {
        Config.PROVIDER_OPENAI: lambda key: OpenAI(api_key=key),
        Config.PROVIDER_DEEPSEEK: lambda key: OpenAI(api_key=key, base_url=Config.BASE_URLS[Config.PROVIDER_DEEPSEEK]),
        Config.PROVIDER_ANTHROPIC: lambda key: Anthropic(api_key=key),
    }

    def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None, provider=None):
        self.config = Config()
        self.provider = provider or self.config.provider
        defaults = self.config.DEFAULT_MODEL_PARAMS[self.provider]
        self.model_name = model_name or defaults["model_name"]
        self.temperature = temperature if temperature is not None else defaults["temperature"]
        self.max_tokens = max_tokens if max_tokens is not None else defaults["max_tokens"]

        # Our communicator capturing lowest-level I/O
        self.communicator = LowestLevelCommunicator()
        self.client = self._create_client(api_key)

    def _create_client(self, api_key=None):
        api_key_env = Config.API_KEY_ENV_VARS.get(self.provider)
        api_key_use = api_key or os.getenv(api_key_env)
        try:
            return self.CLIENT_FACTORIES[self.provider](api_key_use)
        except KeyError:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")

    def _log_api_response(self, response):
        prompt_tokens = getattr(getattr(response, "usage", None), "prompt_tokens", "N/A")
        logger.bind(prompt_tokens=prompt_tokens).debug(response)

    def _log_api_error(self, exception, model_name, messages):
        logger.error(f"Error during (provider:{self.provider} | model:{model_name}) API call: {exception}")
        logger.debug(f"Exception type: {type(exception).__name__}")
        logger.debug(f"Detailed exception: {exception}")
        logger.debug(f"Input messages: {messages}")

    def _execute_api_call(self, call_fn, model_name, messages):
        try:
            response = call_fn()
            self._log_api_response(response)
            return response
        except Exception as e:
            self._log_api_error(e, model_name, messages)
            return None

    def _openai_call(self, messages, model_name, temperature, max_tokens):
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
        )

    def _anthropic_call(self, messages, model_name, temperature, max_tokens):
        system_prompt = "\n".join(msg["content"] for msg in messages if msg["role"] == "system")
        user_msgs = [msg for msg in messages if msg["role"] == "user"]

        return self.client.messages.create(
            model=model_name,
            max_tokens=max_tokens,
            temperature=temperature,
            system=system_prompt.strip(),
            messages=user_msgs,
        )

    def _deepseek_call(self, messages, model_name, temperature, max_tokens):
        system_prompt = next((msg["content"] for msg in messages if msg["role"] == "system"), "")
        instructions_content = "\n".join(
            msg["content"]
            for msg in messages
            if msg["role"] == "system" and msg["content"] != system_prompt
        )
        user_prompt = next((msg["content"] for msg in messages if msg["role"] == "user"), "")
        combined_prompt = f"{system_prompt}\n{instructions_content}\n{user_prompt}"
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=[{"role": "user", "content": combined_prompt}],
        )

    def _execute_llm_api_call(self, messages, model_name, temperature, max_tokens):
        # Record the raw request data
        self.communicator.record_request(self.provider, model_name, messages)

        provider_map = {
            Config.PROVIDER_OPENAI: lambda: self._openai_call(messages, model_name, temperature, max_tokens),
            Config.PROVIDER_DEEPSEEK: lambda: self._deepseek_call(messages, model_name, temperature, max_tokens),
            Config.PROVIDER_ANTHROPIC: lambda: self._anthropic_call(messages, model_name, temperature, max_tokens),
        }

        provider_api_request = provider_map.get(self.provider)
        if provider_api_request is None:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")

        api_response = self._execute_api_call(provider_api_request, model_name, messages)
        if not api_response:
            return None

        # Parse out raw text
        if self.provider in [Config.PROVIDER_OPENAI, Config.PROVIDER_DEEPSEEK]:
            raw_text = (
                api_response.choices[0].message.content
                if hasattr(api_response, "choices") and api_response.choices
                else None
            )
        elif self.provider == Config.PROVIDER_ANTHROPIC:
            raw_text = (
                api_response.content[0].text
                if hasattr(api_response, "content") and api_response.content
                else None
            )
        else:
            raw_text = None

        # Record the raw response
        if raw_text is not None:
            self.communicator.record_response(self.provider, model_name, raw_text)

        return raw_text

    def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
        used_model = model_name or self.model_name
        used_temp = temperature if temperature is not None else self.temperature
        used_tokens = max_tokens if max_tokens is not None else self.max_tokens
        return self._execute_llm_api_call(messages, used_model, used_temp, used_tokens)

# -------------------------------------------------------
# 4. Template File Manager
# -------------------------------------------------------
class TemplateFileManager:
    """
    Manages prompt templates, performing lazy loading, placeholder substitution,
    and recipe execution.
    """

    ALLOWED_FILE_EXTS = (".xml", ".py", ".json", ".md", ".txt")
    EXCLUDED_FILE_NAMES = ["ignore_me", "do_not_load", "temp_file"]
    EXCLUDED_FILE_PATHS = ["\\_md\\"]
    EXCLUDED_PATTERNS = [r".*\.\d{2}\.\d{2}.*", ]
    MAX_TEMPLATE_SIZE_KB = 100
    REQUIRED_CONTENT_MARKERS = ["<template>", "[INPUT_PROMPT]"]

    def __init__(self):
        self.template_dir = os.getcwd()
        self.template_cache = {}

    def template_qualifier(self, filepath):
        _, ext = os.path.splitext(filepath)
        filename = os.path.basename(filepath)
        basename, _ = os.path.splitext(filename)
        filepath_lower = filepath.lower()

        if ext.lower() not in self.ALLOWED_FILE_EXTS:
            return False
        if basename in self.EXCLUDED_FILE_NAMES:
            return False
        if any(excluded in filepath_lower for excluded in self.EXCLUDED_FILE_PATHS):
            return False
        if any(re.match(pattern, filename, re.IGNORECASE) for pattern in self.EXCLUDED_PATTERNS):
            return False
        try:
            filesize_kb = os.path.getsize(filepath) / 1024
            if filesize_kb > self.MAX_TEMPLATE_SIZE_KB:
                return False
            with open(filepath, "r", encoding="utf-8") as f:
                content = f.read()
            if not all(marker in content for marker in self.REQUIRED_CONTENT_MARKERS):
                return False
        except Exception:
            return False

        return True

    def reload_templates(self):
        self.template_cache.clear()
        pattern = os.path.join(self.template_dir, "**", "*.*")
        for filepath in glob.glob(pattern, recursive=True):
            name = os.path.splitext(os.path.basename(filepath))[0]
            if self.template_qualifier(filepath):
                self.template_cache[name] = filepath

    def prefetch_templates(self, template_name_list):
        for name in template_name_list:
            _ = self.get_template_path(name)

    def get_template_path(self, template_name):
        if template_name in self.template_cache:
            return self.template_cache[template_name]
        for ext in self.ALLOWED_FILE_EXTS:
            search_pattern = os.path.join(self.template_dir, "**", f"{template_name}{ext}")
            files = glob.glob(search_pattern, recursive=True)
            if files:
                self.template_cache[template_name] = files[0]
                return files[0]
        return None

    def _parse_template(self, template_path):
        try:
            with open(template_path, "r", encoding="utf-8") as f:
                content = f.read()

            placeholders = list(set(re.findall(r"\[(.*?)\]", content)))
            template_data = {
                "path": template_path,
                "content": content,
                "placeholders": placeholders,
            }
            return template_data

        except Exception as e:
            logger.error(f"Error parsing template file {template_path}: {e}")
            return {}

    def extract_placeholders(self, template_name):
        template_path = self.get_template_path(template_name)
        if not template_path:
            return []
        parsed_template = self._parse_template(template_path)
        if not parsed_template:
            return []
        return parsed_template.get("placeholders", [])

    def get_template_metadata(self, template_name):
        template_path = self.get_template_path(template_name)
        if not template_path:
            return {}
        parsed_template = self._parse_template(template_path)
        if not parsed_template:
            return {}

        content = parsed_template["content"]
        metadata = {
            "agent_name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),
            "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),
            "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>'),
            "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),
            "system_prompt": self._extract_value_from_content(content, r'<system_prompt value="([^"]*)"\s*/>')
        }
        return metadata

    def _extract_value_from_content(self, content, pattern):
        match = re.search(pattern, content)
        return match.group(1) if match else None

    def list_templates(
        self,
        exclude_paths=None,
        exclude_names=None,
        exclude_versions=None,
        exclude_statuses=None,
        exclude_none_versions=False,
        exclude_none_statuses=False
    ):
        search_pattern = os.path.join(self.template_dir, "**", "*.*")
        templates_info = {}

        for filepath in glob.glob(search_pattern, recursive=True):
            if not self.template_qualifier(filepath):
                continue
            template_name = os.path.splitext(os.path.basename(filepath))[0]
            parsed_template = self._parse_template(filepath)
            if not parsed_template:
                logger.warning(f"Skipping {filepath} due to parsing error.")
                continue
            content = parsed_template["content"]
            try:
                templates_info[template_name] = {
                    "path": filepath,
                    "name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),
                    "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),
                    "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),
                    "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>')
                }
            except Exception as e:
                logger.error(f"Error loading template from {filepath}: {e}")

        filtered_templates = {}
        for name, info in templates_info.items():
            if ((not exclude_paths or info["path"] not in exclude_paths) and
                (not exclude_names or info["name"] not in exclude_names) and
                (not exclude_versions or info["version"] not in exclude_versions) and
                (not exclude_statuses or info["status"] not in exclude_statuses) and
                (not exclude_none_versions or info["version"] is not None) and
                (not exclude_none_statuses or info["status"] is not None)):
                filtered_templates[name] = info

        return filtered_templates

    def prepare_template(self, template_filepath, input_prompt=""):
        parsed_template = self._parse_template(template_filepath)
        if not parsed_template:
            return None

        content = parsed_template["content"]
        placeholders = {
            "[HEADER]": f"```{os.path.splitext(template_filepath)[1]}",
            # "[TEMPLATE_START]": f"```{os.path.splitext(template_filepath)[1]}",
            "[FILENAME]": os.path.basename(template_filepath),
            "[OUTPUT_FORMAT]": "plain_text",
            "[ORIGINAL_PROMPT_LENGTH]": str(len(input_prompt)),
            "[RESPONSE_PROMPT_LENGTH]": str(int(len(input_prompt) * 0.9)),
            "[INPUT_PROMPT]": input_prompt,
            "[ADDITIONAL_CONSTRAINTS]": "",
            "[ADDITIONAL_PROCESS_STEPS]": "",
            "[ADDITIONAL_GUIDELINES]": "",
            "[ADDITIONAL_REQUIREMENTS]": "",
            # "[TEMPLATE_END]": f"```\n",
            "[FOOTER]": f"```",
        }

        for placeholder, value in placeholders.items():
            value_str = str(value)
            content = content.replace(placeholder, value_str)

        return content


    def _extract_template_parts(self, raw_text):
        """
        Extracts relevant sections from the raw template text.
        E.g., <system_prompt ...>, <response_format>, etc.
        """
        metadata_match = re.search(r"<metadata>(.*?)</metadata>", raw_text, re.DOTALL)
        response_format_match = re.search(r"<response_format>(.*?)</response_format>", raw_text, re.DOTALL)

        start_end_match = re.search(r"\[TEMPLATE_START\](.*?)\[TEMPLATE_END\]", raw_text, re.DOTALL)
        start_end = start_end_match.group(1) if start_end_match else ""

        # start_end_match = re.search(r"\=\=\=[START]\=\=\=(.*?)\=\=\=[END]\=\=\=", raw_text, re.DOTALL)

        template_match = re.search(r"<template>(.*?)</template>", raw_text, re.DOTALL)
        agent_match = re.search(r"<agent>(.*?)</agent>", raw_text, re.DOTALL)

        system_prompt_match = re.search(r'<system_prompt value="([^"]*)"\s*/>', raw_text)
        instructions_match = re.search(r"<instructions>(.*?)</instructions>", raw_text, re.DOTALL)

        system_prompt = system_prompt_match.group(1) if system_prompt_match else ""
        response_format = response_format_match.group(1) if response_format_match else ""
        template = template_match.group(1) if template_match else ""
        instructions = instructions_match.group(1) if instructions_match else ""

        return system_prompt, response_format, start_end

# -------------------------------------------------------
# 5. Prompt Refinement Orchestrator
# -------------------------------------------------------
class PromptRefinementOrchestrator:
    def __init__(self, template_manager: TemplateFileManager, agent: LLMInteractions):
        self.template_manager = template_manager
        self.agent = agent

    def _build_messages(self, system_prompt: str, agent_instructions: str) -> List[Dict[str, str]]:
        """
        Prepare message.
        """
        return [
            {"role": "system", "content": system_prompt.strip()},
            {"role": "user", "content": agent_instructions.strip()},
        ]

    def _format_multiline(self, text):
        """
        Nicely format the text for console output (esp. if it is JSON).
        """
        if isinstance(text, dict) or isinstance(text, list):
            return json.dumps(text, indent=4, ensure_ascii=False)
        elif isinstance(text, str):
            try:
                if text.startswith("```json"):
                    json_match = re.search(r"```json\n(.*?)\n```", text, re.DOTALL)
                    if json_match:
                        return json.dumps(json.loads(json_match.group(1)), indent=4, ensure_ascii=False)
                elif text.strip().startswith("{") and text.strip().endswith("}"):
                    return json.dumps(json.loads(text), indent=4, ensure_ascii=False)
            except json.JSONDecodeError:
                pass
        return text.replace("\\n", "\n")

    # -------------------------------------------------------
    # CHANGED: Now parse "enhanced_prompt" from the JSON output
    #          and pass it on to the next iteration.
    # -------------------------------------------------------
    def execute_prompt_refinement_chain_from_file(
        self,
        template_filepath,
        input_prompt,
        refinement_count=1,
        model_name=None,
        temperature=None,
        max_tokens=None
    ):
        """
        Executes refinement(s) using one file-based template,
        passing 'enhanced_prompt' forward if present in the response JSON.
        """
        content = self.template_manager.prepare_template(template_filepath, input_prompt)
        if not content:
            return None

        agent_name = self.template_manager._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>')
        system_prompt, response_format, agent_instructions = self.template_manager._extract_template_parts(content)
        prompt = input_prompt
        results = []

        for _ in range(refinement_count):
            msgs = self._build_messages(system_prompt.strip(), agent_instructions)
            print(len(msgs))
            # print(f'msgs: {msgs}')
            refined = self.agent.generate_response(
                msgs,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
            if refined:
                # Attempt to pretty-print if JSON
                refined_str = refined
                try:
                    data = json.loads(refined_str)
                    refined_str = json.dumps(data, indent=4)
                except json.JSONDecodeError:
                    pass

                # Store the full raw response
                results.append(refined)

                # If the response is JSON and has "enhanced_prompt," pass that as the new input
                next_prompt = refined
                try:
                    data = json.loads(refined)
                    if isinstance(data, dict) and "enhanced_prompt" in data:
                        next_prompt = data["enhanced_prompt"]
                except (TypeError, json.JSONDecodeError):
                    pass

                prompt = next_prompt

        return results

    def _execute_single_template_refinement(self, template_name, initial_prompt, refinement_count, **kwargs):
        path = self.template_manager.get_template_path(template_name)
        if not path:
            logger.error(f"No template file found with name: {template_name}")
            return None
        return self.execute_prompt_refinement_chain_from_file(path, initial_prompt, refinement_count, **kwargs)

    def _execute_multiple_template_refinement(self, template_name_list, initial_prompt, refinement_levels, **kwargs):
        if not all(isinstance(template, str) for template in template_name_list):
            logger.error("All items in template_name_list must be strings.")
            return None
        if isinstance(refinement_levels, int):
            counts = [refinement_levels] * len(template_name_list)
        elif isinstance(refinement_levels, list) and len(refinement_levels) == len(template_name_list):
            counts = refinement_levels
        else:
            logger.error("refinement_levels must be int or a list matching template_name_list.")
            return None

        results = []
        current_prompt = initial_prompt
        for name, cnt in zip(template_name_list, counts):
            chain_result = self._execute_single_template_refinement(name, current_prompt, cnt, **kwargs)
            if chain_result:
                # The last returned string from that chain becomes the next prompt
                current_prompt = chain_result[-1]
                results.append({"agent_name": name, "iterations": cnt, "outputs": chain_result})
        return results

    def execute_prompt_refinement_by_name(
        self,
        template_name_or_list: Union[str, List[str]],
        initial_prompt: str,
        refinement_levels: Union[int, List[int]] = 1,
        model_name=None,
        temperature=None,
        max_tokens=None
    ):
        if isinstance(template_name_or_list, str):
            return self._execute_single_template_refinement(
                template_name_or_list,
                initial_prompt,
                refinement_levels,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        elif isinstance(template_name_or_list, list):
            if not all(isinstance(x, str) for x in template_name_or_list):
                logger.error("All items in template_name_or_list must be strings.")
                return None
            return self._execute_multiple_template_refinement(
                template_name_or_list,
                initial_prompt,
                refinement_levels,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        else:
            logger.error("template_name_or_list must be str or list[str].")
            return None

    def execute_recipe(self, recipe: List[Dict], initial_prompt: str) -> Dict[str, Union[str, List[Dict]]]:
        """
        Executes a multi-step prompt refinement "recipe."
        """
        current_input = initial_prompt
        refinement_history = []
        gathered_outputs = []

        for idx, step in enumerate(recipe, start=1):
            chain = step.get("chain")
            repeats = step.get("repeats", 1)
            gather = step.get("gather", False)
            aggregator = step.get("aggregator_chain")
            if not chain:
                logger.error(f"Recipe step {idx} missing 'chain' key.")
                continue

            step_gathered = []
            for rep in range(repeats):
                data = self.execute_prompt_refinement_by_name(chain, current_input, 1)
                if data:
                    refinement_history.append({"step": idx, "repeat": rep + 1, "chain": chain, "result": data})
                    final_str = data[-1]
                    step_gathered.append(final_str)
                    if not gather:
                        current_input = final_str

            if gather and step_gathered:
                gathered_outputs.extend(step_gathered)
                if aggregator:
                    aggregator_prompt = "\n\n".join([f"Option {i+1}:\n{v}" for i, v in enumerate(step_gathered)])
                    aggregator_data = self.execute_prompt_refinement_by_name(aggregator, aggregator_prompt, 1)
                    if aggregator_data:
                        refinement_history.append({
                            "step": idx,
                            "aggregator_chain": aggregator,
                            "aggregator_input": aggregator_prompt,
                            "aggregator_result": aggregator_data,
                        })
                        current_input = aggregator_data[-1]
                    else:
                        current_input = step_gathered[-1]
                else:
                    current_input = step_gathered[-1]

        return {
            "final_output": current_input,
            "refinement_history": refinement_history,
            "gathered_outputs": gathered_outputs,
        }

# -------------------------------------------------------
# 6. Main Execution
# -------------------------------------------------------
class Execution:
    def __init__(self, provider=None):
        self.config = Config()
        self.agent = LLMInteractions(provider=provider)
        self.template_manager = TemplateFileManager()
        self.refinement_engine = PromptRefinementOrchestrator(self.template_manager, self.agent)

    def log_usage_demo(self):
        self.template_manager.reload_templates()
        self.template_manager.prefetch_templates(["PromptOptimizerExpert", "MultiResponseSelector", "IntensityEnhancer"])

        placeholders = self.template_manager.extract_placeholders("IntensityEnhancer")
        logger.info(f"Placeholders in 'IntensityEnhancer': {placeholders}")

        metadata = self.template_manager.get_template_metadata("IntensityEnhancer")
        logger.info(f"Metadata for 'IntensityEnhancer': {metadata}")

        all_temps = self.template_manager.list_templates(exclude_none_statuses=True, exclude_none_versions=True)
        logger.info(f"Found a total of {len(all_temps)} templates.")
        logger.info("Template keys: " + ", ".join(all_temps.keys()))

    def run(self):
        self.log_usage_demo()
        self.template_manager.reload_templates()

        # Example recipe - two passes of PromptOptimizerExpert, gather results,
        # then feed to MultiResponseSelector aggregator.
        # recipe_steps = [
        #     {
        #         "chain": ["PromptOptimizerExpert", "PromptOptimizerExpert"],
        #         "repeats": 1,
        #         "gather": True,
        #         "aggregator_chain": ["MultiResponseSelector"],
        #     },
        # ]

        recipe_steps = [
            {
                "chain": ["PromptOptimizerExpert"],
                "repeats": 2,
                "gather": False,
                "aggregator_chain": ["MultiResponseSelector"],
            },
            # {
            #     "chain": "RunwayPromptBuilder",
            #     "repeats": 1,
            # },
            # {
            #     "chain": ["CinematicSceneDescriptor", "MultiResponseSelector"],
            #     "repeats": 1,
            # },
            # {"chain": "CodeCommentCondenser", "repeats": 1},
            # {"chain": "MultiResponseSelector", "repeats": 1},
        ]


        initial_prompt = (
            "You are a steadfast, detail-oriented assistant. Your critical task is to refine "
            "this prompt with absolute precision and laser-sharp clarity. Adhere strictly to all "
            "guidelines, ensuring the refined version fully captures and enhances the original's "
            "essence and intent."
        )

        initial_prompt = "Ignite a scenario so genuine and commanding it stirs unshakable empathy, leaving no heart unmoved. Demonstrate precisely how to shape and structure this video’s narrative, ensuring it resonates with staggering impact and unforgettable power. A scene that conveys profound loss."
        recipe_result = self.refinement_engine.execute_recipe(
            recipe=recipe_steps, initial_prompt=initial_prompt
        )

        # Show a formatted log of all raw I/O from the communicator
        print("\n=== Full Communicator Log (Raw I/O) ===")
        print(self.agent.communicator.get_formatted_output())




if __name__ == "__main__":
    provider_to_use = os.getenv("LLM_PROVIDER", Config.DEFAULT_PROVIDER).lower()
    execution = Execution(provider=provider_to_use)
    execution.run()
```


## Message 36

**Updating project structure**

The user asked for an update to the project structure, including two files: "PromptOptimizerExpert.xml" and "template_runner.py." The request seems to ask for an updated version of the code snippet, likely with some modifications to the `prepare_template` method. It's not immediately clear if the user wants an explanation of the changes or just the updated code snippet itself. My best guess is they want the updated version of the code, so I'll make sure to present it as a full code block.

**Updating project files**

The user has given an updated version of the "PromptOptimizerExpert.xml" and "template_runner.py" files, with modifications to placeholders and the `prepare_template` method. These changes include new placeholders like `[TEMPLATE_START]`, `[HEADER]`, `[FOOTER]`, and `[TEMPLATE_END]`. I'll produce the final answer by presenting the full, updated code in two sections: one for "PromptOptimizerExpert.xml" and one for "template_runner.py." The code will be wrapped in triple backticks to make it clear and easy to follow.

## Message 37

Below is the updated version of the project files, incorporating the new XML template and the revised code. You’ll notice that the XML now uses placeholders such as `[TEMPLATE_START]`, `[HEADER]`, `[FOOTER]`, and `[TEMPLATE_END]` to help control the output representation. In the Python code (in `template_runner.py`), the `prepare_template` method has been updated so that it replaces `[HEADER]` and `[FOOTER]` accordingly, and the overall structure reflects the new file‐format. 

---

### Project Files Documentation for `py_llm_framework`

#### File Structure

```
├── PromptOptimizerExpert.xml
└── template_runner.py
```

---

#### `PromptOptimizerExpert.xml`

```xml
<!--
<metadata>
    <agent_name value="[FILENAME]" />
    <description value="The [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />
    <version value="0" />
    <status value="wip" />
</metadata>
-->

[TEMPLATE_START]
<template>
    <purpose value="[FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights." />
    <system_prompt value="You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON." />

    <agent>
        <name value="[FILENAME]" />
        <role value="Prompt Optimizer" />
        <objective value="Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity." />
        <instructions>
            <constants>
                <input value="JSON output format: {'title', 'enhanced_prompt', 'context_layers'}." />
            </constants>
            <constraints>
                <input value="Maintain logical, hierarchical organization." />
                <input value="Avoid redundancy, ensure coherence." />
                <input value="Limit length to double the original prompt." />
            </constraints>
            <process>
                <input value="Analyze core message." />
                <input value="Identify key themes." />
                <input value="Generate concise title (max 50 chars)." />
                <input value="Expand context layers meaningfully." />
                <input value="Produce refined, concise prompt." />
            </process>
            <guidelines>
                <input value="Use clear, structured language." />
                <input value="Ensure relevancy of context layers." />
                <input value="Prioritize more specific over generic, and actionable over vague instructions." />
                <input value="Maintain a logical flow and coherence within the combined instructions." />
            </guidelines>
            <requirements>
                <input value="Output must not exceed double the original length." />
                <input value="Detailed enough for clarity and precision." />
                <input value="JSON format containing: title, enhanced_prompt, and context_layers." />
            </requirements>
        </instructions>
    </agent>

[HEADER]
    <response_instructions>
        <![CDATA[
            Your response must be a JSON object:
            ```json
            {
                "title": "Descriptive title",
                "enhanced_prompt": "Optimized version of the prompt",
                "context_layers": [
                    {"level": 1, "context": "Primary context layer"},
                    {"level": 2, "context": "Secondary contextual details"},
                    // Additional layers as needed
                ]
            }
        ]]>
    </response_instructions>
[FOOTER]
</template>
<user_prompt>
    [INPUT_PROMPT]
</user_prompt>

[TEMPLATE_END]
```

---

#### `template_runner.py`

```python
#!/usr/bin/env python3
"""
template_runnet.py
A single-file script for managing templates, LLM interactions, and orchestrating prompt refinements.
Now updated to pass each template's 'enhanced_prompt' as input to the next template in the chain.
"""

import os
import sys
import re
import glob
import json
from typing import List, Dict, Union, Optional
from pathlib import Path

from dotenv import load_dotenv
from loguru import logger

# Provider SDKs
from openai import OpenAI
from anthropic import Anthropic

# -------------------------------------------------------
# 1. LowestLevelCommunicator
# -------------------------------------------------------
class LowestLevelCommunicator:
    """
    Captures raw input and output strings at the lowest level,
    preserving the entire stream before any filtering or transformation.
    """
    def __init__(self):
        self.raw_interactions = []

    def record_request(self, provider: str, model_name: str, messages: List[Dict[str, str]]):
        """
        Called just before sending the request to the LLM.
        """
        self.raw_interactions.append({
            "direction": "request",
            "provider": provider,
            "model_name": model_name,
            "messages": messages,
        })

    def record_response(self, provider: str, model_name: str, response_text: str):
        """
        Called immediately after receiving the raw text from the LLM.
        """
        self.raw_interactions.append({
            "direction": "response",
            "provider": provider,
            "model_name": model_name,
            "content": response_text,
        })

    def get_all_interactions(self) -> List[Dict]:
        """
        Return the raw record of all requests/responses.
        """
        return self.raw_interactions

    def get_formatted_output(self) -> str:
        """
        Pretty-print a summary of the captured interactions.
        """
        lines = []
        for entry in self.raw_interactions:
            direction = entry["direction"].upper()
            provider = entry["provider"]
            model_name = entry["model_name"]

            if direction == "REQUEST":
                lines.append(f"--- REQUEST to {provider} (model: {model_name}) ---")
                for i, msg in enumerate(entry["messages"], start=1):
                    role = msg.get("role", "").upper()
                    content = msg.get("content", "")
                    lines.append(f"{i}. {role}: {content}")
            else:
                lines.append(f"--- RESPONSE from {provider} (model: {model_name}) ---")
                lines.append(entry["content"])
            lines.append("")

        return "\n".join(lines).strip()

# -------------------------------------------------------
# 2. Global Configuration
# -------------------------------------------------------
class Config:
    """
    Global settings
    """

    PROVIDER_OPENAI = "openai"
    PROVIDER_DEEPSEEK = "deepseek"
    PROVIDER_ANTHROPIC = "anthropic"

    AVAILABLE_MODELS = {
        PROVIDER_OPENAI: {
            "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",
            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",
            "gpt-4": "Latest GPT-4 stable release",
            "gpt-4-0125-preview": "Preview GPT-4 Turbo",
            "gpt-4-0613": "June 2023 GPT-4 snapshot",
            "gpt-4-1106-preview": "Preview GPT-4 Turbo",
            "gpt-4-turbo": "Latest GPT-4 Turbo release",
            "gpt-4-turbo-2024-04-09": "GPT-4 Turbo w/ vision",
            "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",
            "gpt-4o": "Base GPT-4o model",
            "gpt-4o-mini": "Lightweight GPT-4o variant",
        },
        PROVIDER_DEEPSEEK: {
            "deepseek-chat": "DeepSeek Chat model",
            "deepseek-reasoner": "Specialized reasoning model",
        },
        PROVIDER_ANTHROPIC: {
            "claude-2": "Base Claude 2 model",
            "claude-2.0": "Enhanced Claude 2.0",
            "claude-2.1": "Latest Claude 2.1 release",
            "claude-3-opus-20240229": "Claude 3 Opus",
            "claude-3-sonnet-20240229": "Claude 3 Sonnet",
            "claude-3-haiku-20240307": "Claude 3 Haiku",
        },
    }

    DEFAULT_MODEL_PARAMS = {
        PROVIDER_OPENAI: {
            "model_name": "gpt-4-turbo-preview", # (5) used occasionally
            "model_name": "gpt-4o",              # (4) debugging
            "model_name": "gpt-4-turbo",         # (3) used often
            "model_name": "gpt-3.5-turbo",       # (3) most used
            "model_name": "gpt-3.5-turbo-1106",  # (1) most used
            "temperature": 0.7,
            "max_tokens": 800,
        },
        PROVIDER_DEEPSEEK: {
            "model_name": "deepseek-reasoner",
            "model_name": "deepseek-coder",
            "model_name": "deepseek-chat",
            "temperature": 0.7,
            "max_tokens": 800,
        },
        PROVIDER_ANTHROPIC: {
            "model_name": "claude-2.1",
            "model_name": "claude-3-opus-20240229",
            "temperature": 0.7,
            "max_tokens": 800,
        },
    }

    API_KEY_ENV_VARS = {
        PROVIDER_OPENAI: "OPENAI_API_KEY",
        PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",
        PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",
    }

    BASE_URLS = {
        PROVIDER_DEEPSEEK: "https://api.deepseek.com",
    }

    # Overriding allows for switching between providers by simply reordering the lines.
    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
    DEFAULT_PROVIDER = PROVIDER_OPENAI

    def __init__(self):
        load_dotenv()
        self.configure_utf8_encoding()
        self.provider = self.DEFAULT_PROVIDER.lower()
        self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]
        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()
        self.setup_logger()

    def configure_utf8_encoding(self):
        """
        Ensure UTF-8 encoding for standard output and error streams.
        """
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    def setup_logger(self):
        """
        YAML logging via Loguru: clears logs, sets global context, and configures sinks
        """
        log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"
        log_filepath = os.path.join(self.log_dir, log_filename)
        open(log_filepath, "w").close()
        logger.remove()
        logger.configure(
            extra={
                "provider": self.provider,
                "model": self.model_params.get("model_name"),
            }
        )

        def yaml_sink(log_message):
            log_record = log_message.record
            formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")
            formatted_level = f"!{log_record['level'].name}"
            logger_name = log_record["name"]
            formatted_function_name = f"*{log_record['function']}"
            line_number = log_record["line"]
            extra_provider = log_record["extra"].get("provider")
            extra_model = log_record["extra"].get("model")
            log_message_content = log_record["message"]

            if "\n" in log_message_content:
                formatted_message = "|\n" + "\n".join(
                    f"  {line}" for line in log_message_content.splitlines()
                )
            else:
                formatted_message = (
                    f"'{log_message_content}'"
                    if ":" in log_message_content
                    else log_message_content
                )

            log_lines = [
                f"- time: {formatted_timestamp}",
                f"  level: {formatted_level}",
                f"  name: {logger_name}",
                f"  funcName: {formatted_function_name}",
                f"  lineno: {line_number}",
            ]
            if extra_provider is not None:
                log_lines.append(f"  provider: {extra_provider}")
            if extra_model is not None:
                log_lines.append(f"  model: {extra_model}")

            log_lines.append(f"  message: {formatted_message}")
            log_lines.append("")

            with open(log_filepath, "a", encoding="utf-8") as log_file:
                log_file.write("\n".join(log_lines) + "\n")

        logger.add(yaml_sink, level="DEBUG", enqueue=True, format="{message}")

# -------------------------------------------------------
# 3. LLM Interactions
# -------------------------------------------------------
class LLMInteractions:
    """
    Handles interactions with LLM APIs.
    """

    CLIENT_FACTORIES = {
        Config.PROVIDER_OPENAI: lambda key: OpenAI(api_key=key),
        Config.PROVIDER_DEEPSEEK: lambda key: OpenAI(api_key=key, base_url=Config.BASE_URLS[Config.PROVIDER_DEEPSEEK]),
        Config.PROVIDER_ANTHROPIC: lambda key: Anthropic(api_key=key),
    }

    def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None, provider=None):
        self.config = Config()
        self.provider = provider or self.config.provider
        defaults = self.config.DEFAULT_MODEL_PARAMS[self.provider]
        self.model_name = model_name or defaults["model_name"]
        self.temperature = temperature if temperature is not None else defaults["temperature"]
        self.max_tokens = max_tokens if max_tokens is not None else defaults["max_tokens"]

        # Our communicator capturing lowest-level I/O
        self.communicator = LowestLevelCommunicator()
        self.client = self._create_client(api_key)

    def _create_client(self, api_key=None):
        api_key_env = Config.API_KEY_ENV_VARS.get(self.provider)
        api_key_use = api_key or os.getenv(api_key_env)
        try:
            return self.CLIENT_FACTORIES[self.provider](api_key_use)
        except KeyError:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")

    def _log_api_response(self, response):
        prompt_tokens = getattr(getattr(response, "usage", None), "prompt_tokens", "N/A")
        logger.bind(prompt_tokens=prompt_tokens).debug(response)

    def _log_api_error(self, exception, model_name, messages):
        logger.error(f"Error during (provider:{self.provider} | model:{model_name}) API call: {exception}")
        logger.debug(f"Exception type: {type(exception).__name__}")
        logger.debug(f"Detailed exception: {exception}")
        logger.debug(f"Input messages: {messages}")

    def _execute_api_call(self, call_fn, model_name, messages):
        try:
            response = call_fn()
            self._log_api_response(response)
            return response
        except Exception as e:
            self._log_api_error(e, model_name, messages)
            return None

    def _openai_call(self, messages, model_name, temperature, max_tokens):
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
        )

    def _anthropic_call(self, messages, model_name, temperature, max_tokens):
        system_prompt = "\n".join(msg["content"] for msg in messages if msg["role"] == "system")
        user_msgs = [msg for msg in messages if msg["role"] == "user"]

        return self.client.messages.create(
            model=model_name,
            max_tokens=max_tokens,
            temperature=temperature,
            system=system_prompt.strip(),
            messages=user_msgs,
        )

    def _deepseek_call(self, messages, model_name, temperature, max_tokens):
        system_prompt = next((msg["content"] for msg in messages if msg["role"] == "system"), "")
        instructions_content = "\n".join(
            msg["content"]
            for msg in messages
            if msg["role"] == "system" and msg["content"] != system_prompt
        )
        user_prompt = next((msg["content"] for msg in messages if msg["role"] == "user"), "")
        combined_prompt = f"{system_prompt}\n{instructions_content}\n{user_prompt}"
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=[{"role": "user", "content": combined_prompt}],
        )

    def _execute_llm_api_call(self, messages, model_name, temperature, max_tokens):
        # Record the raw request data
        self.communicator.record_request(self.provider, model_name, messages)

        provider_map = {
            Config.PROVIDER_OPENAI: lambda: self._openai_call(messages, model_name, temperature, max_tokens),
            Config.PROVIDER_DEEPSEEK: lambda: self._deepseek_call(messages, model_name, temperature, max_tokens),
            Config.PROVIDER_ANTHROPIC: lambda: self._anthropic_call(messages, model_name, temperature, max_tokens),
        }

        provider_api_request = provider_map.get(self.provider)
        if provider_api_request is None:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")

        api_response = self._execute_api_call(provider_api_request, model_name, messages)
        if not api_response:
            return None

        # Parse out raw text
        if self.provider in [Config.PROVIDER_OPENAI, Config.PROVIDER_DEEPSEEK]:
            raw_text = (
                api_response.choices[0].message.content
                if hasattr(api_response, "choices") and api_response.choices
                else None
            )
        elif self.provider == Config.PROVIDER_ANTHROPIC:
            raw_text = (
                api_response.content[0].text
                if hasattr(api_response, "content") and api_response.content
                else None
            )
        else:
            raw_text = None

        # Record the raw response
        if raw_text is not None:
            self.communicator.record_response(self.provider, model_name, raw_text)

        return raw_text

    def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):
        used_model = model_name or self.model_name
        used_temp = temperature if temperature is not None else self.temperature
        used_tokens = max_tokens if max_tokens is not None else self.max_tokens
        return self._execute_llm_api_call(messages, used_model, used_temp, used_tokens)

# -------------------------------------------------------
# 4. Template File Manager
# -------------------------------------------------------
class TemplateFileManager:
    """
    Manages prompt templates, performing lazy loading, placeholder substitution,
    and recipe execution.
    """

    ALLOWED_FILE_EXTS = (".xml", ".py", ".json", ".md", ".txt")
    EXCLUDED_FILE_NAMES = ["ignore_me", "do_not_load", "temp_file"]
    EXCLUDED_FILE_PATHS = ["\\_md\\"]
    EXCLUDED_PATTERNS = [r".*\.\d{2}\.\d{2}.*", ]
    MAX_TEMPLATE_SIZE_KB = 100
    REQUIRED_CONTENT_MARKERS = ["<template>", "[INPUT_PROMPT]"]

    def __init__(self):
        self.template_dir = os.getcwd()
        self.template_cache = {}

    def template_qualifier(self, filepath):
        _, ext = os.path.splitext(filepath)
        filename = os.path.basename(filepath)
        basename, _ = os.path.splitext(filename)
        filepath_lower = filepath.lower()

        if ext.lower() not in self.ALLOWED_FILE_EXTS:
            return False
        if basename in self.EXCLUDED_FILE_NAMES:
            return False
        if any(excluded in filepath_lower for excluded in self.EXCLUDED_FILE_PATHS):
            return False
        if any(re.match(pattern, filename, re.IGNORECASE) for pattern in self.EXCLUDED_PATTERNS):
            return False
        try:
            filesize_kb = os.path.getsize(filepath) / 1024
            if filesize_kb > self.MAX_TEMPLATE_SIZE_KB:
                return False
            with open(filepath, "r", encoding="utf-8") as f:
                content = f.read()
            if not all(marker in content for marker in self.REQUIRED_CONTENT_MARKERS):
                return False
        except Exception:
            return False

        return True

    def reload_templates(self):
        self.template_cache.clear()
        pattern = os.path.join(self.template_dir, "**", "*.*")
        for filepath in glob.glob(pattern, recursive=True):
            name = os.path.splitext(os.path.basename(filepath))[0]
            if self.template_qualifier(filepath):
                self.template_cache[name] = filepath

    def prefetch_templates(self, template_name_list):
        for name in template_name_list:
            _ = self.get_template_path(name)

    def get_template_path(self, template_name):
        if template_name in self.template_cache:
            return self.template_cache[template_name]
        for ext in self.ALLOWED_FILE_EXTS:
            search_pattern = os.path.join(self.template_dir, "**", f"{template_name}{ext}")
            files = glob.glob(search_pattern, recursive=True)
            if files:
                self.template_cache[template_name] = files[0]
                return files[0]
        return None

    def _parse_template(self, template_path):
        try:
            with open(template_path, "r", encoding="utf-8") as f:
                content = f.read()

            placeholders = list(set(re.findall(r"\[(.*?)\]", content)))
            template_data = {
                "path": template_path,
                "content": content,
                "placeholders": placeholders,
            }
            return template_data

        except Exception as e:
            logger.error(f"Error parsing template file {template_path}: {e}")
            return {}

    def extract_placeholders(self, template_name):
        template_path = self.get_template_path(template_name)
        if not template_path:
            return []
        parsed_template = self._parse_template(template_path)
        if not parsed_template:
            return []
        return parsed_template.get("placeholders", [])

    def get_template_metadata(self, template_name):
        template_path = self.get_template_path(template_name)
        if not template_path:
            return {}
        parsed_template = self._parse_template(template_path)
        if not parsed_template:
            return {}

        content = parsed_template["content"]
        metadata = {
            "agent_name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),
            "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),
            "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>'),
            "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),
            "system_prompt": self._extract_value_from_content(content, r'<system_prompt value="([^"]*)"\s*/>')
        }
        return metadata

    def _extract_value_from_content(self, content, pattern):
        match = re.search(pattern, content)
        return match.group(1) if match else None

    def list_templates(
        self,
        exclude_paths=None,
        exclude_names=None,
        exclude_versions=None,
        exclude_statuses=None,
        exclude_none_versions=False,
        exclude_none_statuses=False
    ):
        search_pattern = os.path.join(self.template_dir, "**", "*.*")
        templates_info = {}

        for filepath in glob.glob(search_pattern, recursive=True):
            if not self.template_qualifier(filepath):
                continue
            template_name = os.path.splitext(os.path.basename(filepath))[0]
            parsed_template = self._parse_template(filepath)
            if not parsed_template:
                logger.warning(f"Skipping {filepath} due to parsing error.")
                continue
            content = parsed_template["content"]
            try:
                templates_info[template_name] = {
                    "path": filepath,
                    "name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),
                    "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),
                    "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),
                    "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>')
                }
            except Exception as e:
                logger.error(f"Error loading template from {filepath}: {e}")

        filtered_templates = {}
        for name, info in templates_info.items():
            if ((not exclude_paths or info["path"] not in exclude_paths) and
                (not exclude_names or info["name"] not in exclude_names) and
                (not exclude_versions or info["version"] not in exclude_versions) and
                (not exclude_statuses or info["status"] not in exclude_statuses) and
                (not exclude_none_versions or info["version"] is not None) and
                (not exclude_none_statuses or info["status"] is not None)):
                filtered_templates[name] = info

        return filtered_templates

    def prepare_template(self, template_filepath, input_prompt=""):
        parsed_template = self._parse_template(template_filepath)
        if not parsed_template:
            return None

        content = parsed_template["content"]
        placeholders = {
            "[HEADER]": f"```{os.path.splitext(template_filepath)[1]}",
            # "[TEMPLATE_START]": f"```{os.path.splitext(template_filepath)[1]}",  # commented out per new design
            "[FILENAME]": os.path.basename(template_filepath),
            "[OUTPUT_FORMAT]": "plain_text",
            "[ORIGINAL_PROMPT_LENGTH]": str(len(input_prompt)),
            "[RESPONSE_PROMPT_LENGTH]": str(int(len(input_prompt) * 0.9)),
            "[INPUT_PROMPT]": input_prompt,
            "[ADDITIONAL_CONSTRAINTS]": "",
            "[ADDITIONAL_PROCESS_STEPS]": "",
            "[ADDITIONAL_GUIDELINES]": "",
            "[ADDITIONAL_REQUIREMENTS]": "",
            # "[TEMPLATE_END]": f"```\n",  # commented out per new design
            "[FOOTER]": f"```",
        }

        for placeholder, value in placeholders.items():
            value_str = str(value)
            content = content.replace(placeholder, value_str)

        return content


    def _extract_template_parts(self, raw_text):
        """
        Extracts relevant sections from the raw template text.
        E.g., <system_prompt ...>, <response_format>, etc.
        """
        metadata_match = re.search(r"<metadata>(.*?)</metadata>", raw_text, re.DOTALL)
        response_format_match = re.search(r"<response_format>(.*?)</response_format>", raw_text, re.DOTALL)

        start_end_match = re.search(r"\[TEMPLATE_START\](.*?)\[TEMPLATE_END\]", raw_text, re.DOTALL)
        start_end = start_end_match.group(1) if start_end_match else ""

        template_match = re.search(r"<template>(.*?)</template>", raw_text, re.DOTALL)
        agent_match = re.search(r"<agent>(.*?)</agent>", raw_text, re.DOTALL)

        system_prompt_match = re.search(r'<system_prompt value="([^"]*)"\s*/>', raw_text)
        instructions_match = re.search(r"<instructions>(.*?)</instructions>", raw_text, re.DOTALL)

        system_prompt = system_prompt_match.group(1) if system_prompt_match else ""
        response_format = response_format_match.group(1) if response_format_match else ""
        template = template_match.group(1) if template_match else ""
        instructions = instructions_match.group(1) if instructions_match else ""

        return system_prompt, response_format, start_end

# -------------------------------------------------------
# 5. Prompt Refinement Orchestrator
# -------------------------------------------------------
class PromptRefinementOrchestrator:
    def __init__(self, template_manager: TemplateFileManager, agent: LLMInteractions):
        self.template_manager = template_manager
        self.agent = agent

    def _build_messages(self, system_prompt: str, agent_instructions: str) -> List[Dict[str, str]]:
        """
        Prepare message.
        """
        return [
            {"role": "system", "content": system_prompt.strip()},
            {"role": "user", "content": agent_instructions.strip()},
        ]

    def _format_multiline(self, text):
        """
        Nicely format the text for console output (esp. if it is JSON).
        """
        if isinstance(text, dict) or isinstance(text, list):
            return json.dumps(text, indent=4, ensure_ascii=False)
        elif isinstance(text, str):
            try:
                if text.startswith("```json"):
                    json_match = re.search(r"```json\n(.*?)\n```", text, re.DOTALL)
                    if json_match:
                        return json.dumps(json.loads(json_match.group(1)), indent=4, ensure_ascii=False)
                elif text.strip().startswith("{") and text.strip().endswith("}"):
                    return json.dumps(json.loads(text), indent=4, ensure_ascii=False)
            except json.JSONDecodeError:
                pass
        return text.replace("\\n", "\n")

    # -------------------------------------------------------
    # CHANGED: Now parse "enhanced_prompt" from the JSON output
    #          and pass it on to the next iteration.
    # -------------------------------------------------------
    def execute_prompt_refinement_chain_from_file(
        self,
        template_filepath,
        input_prompt,
        refinement_count=1,
        model_name=None,
        temperature=None,
        max_tokens=None
    ):
        """
        Executes refinement(s) using one file-based template,
        passing 'enhanced_prompt' forward if present in the response JSON.
        """
        content = self.template_manager.prepare_template(template_filepath, input_prompt)
        if not content:
            return None

        agent_name = self.template_manager._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>')
        system_prompt, response_format, agent_instructions = self.template_manager._extract_template_parts(content)
        prompt = input_prompt
        results = []

        for _ in range(refinement_count):
            msgs = self._build_messages(system_prompt.strip(), agent_instructions)
            refined = self.agent.generate_response(
                msgs,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
            if refined:
                # Attempt to pretty-print if JSON
                refined_str = refined
                try:
                    data = json.loads(refined_str)
                    refined_str = json.dumps(data, indent=4)
                except json.JSONDecodeError:
                    pass

                # Store the full raw response
                results.append(refined)

                # If the response is JSON and has "enhanced_prompt," pass that as the new input
                next_prompt = refined
                try:
                    data = json.loads(refined)
                    if isinstance(data, dict) and "enhanced_prompt" in data:
                        next_prompt = data["enhanced_prompt"]
                except (TypeError, json.JSONDecodeError):
                    pass

                prompt = next_prompt

        return results

    def _execute_single_template_refinement(self, template_name, initial_prompt, refinement_count, **kwargs):
        path = self.template_manager.get_template_path(template_name)
        if not path:
            logger.error(f"No template file found with name: {template_name}")
            return None
        return self.execute_prompt_refinement_chain_from_file(path, initial_prompt, refinement_count, **kwargs)

    def _execute_multiple_template_refinement(self, template_name_list, initial_prompt, refinement_levels, **kwargs):
        if not all(isinstance(template, str) for template in template_name_list):
            logger.error("All items in template_name_list must be strings.")
            return None
        if isinstance(refinement_levels, int):
            counts = [refinement_levels] * len(template_name_list)
        elif isinstance(refinement_levels, list) and len(refinement_levels) == len(template_name_list):
            counts = refinement_levels
        else:
            logger.error("refinement_levels must be int or a list matching template_name_list.")
            return None

        results = []
        current_prompt = initial_prompt
        for name, cnt in zip(template_name_list, counts):
            chain_result = self._execute_single_template_refinement(name, current_prompt, cnt, **kwargs)
            if chain_result:
                # The last returned string from that chain becomes the next prompt
                current_prompt = chain_result[-1]
                results.append({"agent_name": name, "iterations": cnt, "outputs": chain_result})
        return results

    def execute_prompt_refinement_by_name(
        self,
        template_name_or_list: Union[str, List[str]],
        initial_prompt: str,
        refinement_levels: Union[int, List[int]] = 1,
        model_name=None,
        temperature=None,
        max_tokens=None
    ):
        if isinstance(template_name_or_list, str):
            return self._execute_single_template_refinement(
                template_name_or_list,
                initial_prompt,
                refinement_levels,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        elif isinstance(template_name_or_list, list):
            if not all(isinstance(x, str) for x in template_name_or_list):
                logger.error("All items in template_name_or_list must be strings.")
                return None
            return self._execute_multiple_template_refinement(
                template_name_or_list,
                initial_prompt,
                refinement_levels,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )
        else:
            logger.error("template_name_or_list must be str or list[str].")
            return None

    def execute_recipe(self, recipe: List[Dict], initial_prompt: str) -> Dict[str, Union[str, List[Dict]]]:
        """
        Executes a multi-step prompt refinement "recipe."
        """
        current_input = initial_prompt
        refinement_history = []
        gathered_outputs = []

        for idx, step in enumerate(recipe, start=1):
            chain = step.get("chain")
            repeats = step.get("repeats", 1)
            gather = step.get("gather", False)
            aggregator = step.get("aggregator_chain")
            if not chain:
                logger.error(f"Recipe step {idx} missing 'chain' key.")
                continue

            step_gathered = []
            for rep in range(repeats):
                data = self.execute_prompt_refinement_by_name(chain, current_input, 1)
                if data:
                    refinement_history.append({"step": idx, "repeat": rep + 1, "chain": chain, "result": data})
                    final_str = data[-1]
                    step_gathered.append(final_str)
                    if not gather:
                        current_input = final_str

            if gather and step_gathered:
                gathered_outputs.extend(step_gathered)
                if aggregator:
                    aggregator_prompt = "\n\n".join([f"Option {i+1}:\n{v}" for i, v in enumerate(step_gathered)])
                    aggregator_data = self.execute_prompt_refinement_by_name(aggregator, aggregator_prompt, 1)
                    if aggregator_data:
                        refinement_history.append({
                            "step": idx,
                            "aggregator_chain": aggregator,
                            "aggregator_input": aggregator_prompt,
                            "aggregator_result": aggregator_data,
                        })
                        current_input = aggregator_data[-1]
                    else:
                        current_input = step_gathered[-1]
                else:
                    current_input = step_gathered[-1]

        return {
            "final_output": current_input,
            "refinement_history": refinement_history,
            "gathered_outputs": gathered_outputs,
        }

# -------------------------------------------------------
# 6. Main Execution
# -------------------------------------------------------
class Execution:
    def __init__(self, provider=None):
        self.config = Config()
        self.agent = LLMInteractions(provider=provider)
        self.template_manager = TemplateFileManager()
        self.refinement_engine = PromptRefinementOrchestrator(self.template_manager, self.agent)

    def log_usage_demo(self):
        self.template_manager.reload_templates()
        self.template_manager.prefetch_templates(["PromptOptimizerExpert", "MultiResponseSelector", "IntensityEnhancer"])

        placeholders = self.template_manager.extract_placeholders("IntensityEnhancer")
        logger.info(f"Placeholders in 'IntensityEnhancer': {placeholders}")

        metadata = self.template_manager.get_template_metadata("IntensityEnhancer")
        logger.info(f"Metadata for 'IntensityEnhancer': {metadata}")

        all_temps = self.template_manager.list_templates(exclude_none_statuses=True, exclude_none_versions=True)
        logger.info(f"Found a total of {len(all_temps)} templates.")
        logger.info("Template keys: " + ", ".join(all_temps.keys()))

    def run(self):
        self.log_usage_demo()
        self.template_manager.reload_templates()

        recipe_steps = [
            {
                "chain": ["PromptOptimizerExpert"],
                "repeats": 2,
                "gather": False,
                "aggregator_chain": ["MultiResponseSelector"],
            },
        ]

        initial_prompt = (
            "You are a steadfast, detail-oriented assistant. Your critical task is to refine "
            "this prompt with absolute precision and laser-sharp clarity. Adhere strictly to all "
            "guidelines, ensuring the refined version fully captures and enhances the original's "
            "essence and intent."
        )

        # An alternative initial prompt for testing:
        initial_prompt = ("Ignite a scenario so genuine and commanding it stirs unshakable empathy, leaving no heart unmoved. "
                          "Demonstrate precisely how to shape and structure this video’s narrative, ensuring it resonates with "
                          "staggering impact and unforgettable power. A scene that conveys profound loss.")
        recipe_result = self.refinement_engine.execute_recipe(
            recipe=recipe_steps, initial_prompt=initial_prompt
        )

        # Show a formatted log of all raw I/O from the communicator
        print("\n=== Full Communicator Log (Raw I/O) ===")
        print(self.agent.communicator.get_formatted_output())

if __name__ == "__main__":
    provider_to_use = os.getenv("LLM_PROVIDER", Config.DEFAULT_PROVIDER).lower()
    execution = Execution(provider=provider_to_use)
    execution.run()
```

---

### Summary

- The **XML template** now uses placeholders such as `[TEMPLATE_START]`, `[HEADER]`, `[FOOTER]`, and `[TEMPLATE_END]` so that the file’s metadata (like the filename and file extension) can be automatically injected.
- In the **Python code**:
  - The `prepare_template` method in the `TemplateFileManager` has been updated to replace `[HEADER]` and `[FOOTER]` (and the other placeholders) with the appropriate dynamic values.
  - The overall project structure and orchestration (LLM interactions, logging, and recipe execution) remain the same, now adapted to work with the new template representation.

This complete update should reflect the enhanced flexibility and robustness of your framework, leveraging string-based handling and easy draftic improvements without over-parsing the template format.

## Message 38

some of openai's models doesn't support the `max_token` and will trigger the following error (such as the o1 and o3 models): 


- time: 2025.02.07 22:48:33

  level: !ERROR

  name: __main__

  funcName: *_log_api_error

  lineno: 280

  provider: openai

  model: o3-mini

  message: 'Error during (provider:openai | model:o3-mini) API call: Error code: 400 - {'error': {'message': "Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.", 'type': 'invalid_request_error', 'param': 'max_tokens', 'code': 'unsupported_parameter'}}'

what would the most seamless, simple and effective way to allow for such dynamic handling?

## Message 39

**Proposing dynamic handling for parameter errors**

The user is asking how to handle the error regarding `max_tokens` with models that don't support it. One solution could be to catch the error and, if the error points to the unsupported `max_tokens`, switch to `max_completion_tokens` instead. Another approach might be to check the model’s name against a list of supported models. The goal is to pick a dynamic solution, which means reading the error message and adjusting the parameter based on the model.

**Evaluating error handling for dynamic API calls**

The user wants a seamless, simple solution for handling errors when models don’t support `max_tokens`. An option could be to implement a parameter conversion in the code. For models that need `max_completion_tokens`, we could check if the model requires the parameter and adjust accordingly. Another option is using try/except in `_execute_api_call`, catching errors related to `max_tokens`, and then modifying the call to use `max_completion_tokens`. This way, we ensure the call adapts dynamically.

## Message 40

One of the simplest and most dynamic solutions is to implement a fallback mechanism right where you make the API call. In other words, wrap the call in a try/except block and—if the error indicates that the parameter is unsupported—reissue the API call using the alternative parameter (i.e. replacing `max_tokens` with `max_completion_tokens`). This way, you don’t need to hard-code a list of models; you simply react to the error message dynamically.

For example, you could modify the `_openai_call` method as follows:

```python
def _openai_call(self, messages, model_name, temperature, max_tokens):
    try:
        # First attempt using 'max_tokens'
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
        )
    except Exception as e:
        error_str = str(e)
        # Check if the error message indicates that max_tokens is unsupported.
        if "Unsupported parameter: 'max_tokens'" in error_str:
            # Retry with 'max_completion_tokens' instead.
            return self.client.chat.completions.create(
                model=model_name,
                temperature=temperature,
                max_completion_tokens=max_tokens,  # Use the same value here.
                messages=messages,
            )
        # Otherwise, re-raise the error.
        raise
```

### How This Helps

- **Dynamic Handling:**  
  The code doesn't need to know in advance which models require which parameter—it simply reacts to the error returned by the API.
  
- **Seamless Fallback:**  
  The user of your framework doesn't have to worry about changing configurations; the fallback happens transparently.

- **Simplicity:**  
  A try/except block is straightforward to understand and maintain. You’re catching a very specific error (based on its message) and then retrying with an alternative parameter name.

This approach is both simple and effective for dynamic parameter handling across different OpenAI models.

## Message 41



- time: 2025.02.07 22:50:37

  level: !ERROR

  name: __main__

  funcName: *_log_api_error

  lineno: 280

  provider: openai

  model: o3-mini

  message: 'Error during (provider:openai | model:o3-mini) API call: Error code: 400 - {'error': {'message': "Unsupported parameter: 'temperature' is not supported with this model.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_parameter'}}'



- time: 2025.02.07 22:50:37

  level: !DEBUG

  name: __main__

  funcName: *_log_api_error

  lineno: 281

  provider: openai

  model: o3-mini

  message: 'Exception type: BadRequestError'



- time: 2025.02.07 22:50:37

  level: !DEBUG

  name: __main__

  funcName: *_log_api_error

  lineno: 282

  provider: openai

  model: o3-mini

  message: 'Detailed exception: Error code: 400 - {'error': {'message': "Unsupported parameter: 'temperature' is not supported with this model.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_parameter'}}'


## Message 42

**Handling unsupported parameters**

The user encountered an error with the OpenAI model "o3-mini" regarding an unsupported 'temperature' parameter. This could also be an issue with 'max_tokens' in the past. To handle this dynamically, we could consider removing or renaming the unsupported parameters before the API call. I think a good approach is to check for parameter issues when calling the API. If 'temperature' is unsupported, we could attempt calling the API without it, since temperature is important but causes an error here.

**Handling dynamic errors in API**

To handle unsupported parameters like 'temperature' dynamically, we can create a wrapper function that checks the error message for "Unsupported parameter: 'temperature'." If it appears, the function would remove the temperature parameter and retry the API call. If it still fails, we’d re-raise the error. Temperature is generally optional for many models, but "o3-mini" doesn’t support it, so we might need to handle it differently, using either a default or removing it entirely for that particular call.

## Message 43

One straightforward solution is to catch the exception and inspect its message to determine which parameter is unsupported. Then you can retry the API call—either by renaming the parameter (as we did for max_tokens) or by simply omitting it (in the case of temperature).

Here's an updated version of the `_openai_call` method that dynamically handles both unsupported `max_tokens` and `temperature` parameters:

```python
def _openai_call(self, messages, model_name, temperature, max_tokens):
    try:
        # First try using both parameters as usual.
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
        )
    except Exception as e:
        error_str = str(e)
        # Check if max_tokens is unsupported.
        if "Unsupported parameter: 'max_tokens'" in error_str:
            try:
                return self.client.chat.completions.create(
                    model=model_name,
                    temperature=temperature,
                    max_completion_tokens=max_tokens,  # Use alternative parameter name.
                    messages=messages,
                )
            except Exception as inner_e:
                raise inner_e

        # Check if temperature is unsupported.
        elif "Unsupported parameter: 'temperature'" in error_str:
            try:
                return self.client.chat.completions.create(
                    model=model_name,
                    max_tokens=max_tokens,
                    messages=messages,
                )
            except Exception as inner_e:
                raise inner_e

        # Otherwise, re-raise the original error.
        raise e
```

### Explanation

- **Try/Except Block:**  
  The API call is wrapped in a try/except block to catch errors.

- **Dynamic Error Checking:**  
  The error message is inspected to see if it mentions that `max_tokens` or `temperature` is unsupported.

- **Fallback Logic:**  
  - If **max_tokens** is unsupported, the code retries the API call using `max_completion_tokens` instead.
  - If **temperature** is unsupported, the call is retried without including the temperature parameter at all (assuming the model will use a default value).

- **Seamless Handling:**  
  This approach requires no pre-configuration of which models support which parameters. It simply reacts to the error messages returned by the API call, ensuring that your framework can handle these dynamic differences in a seamless and effective manner.

This solution is simple, flexible, and requires minimal changes while ensuring robust dynamic handling of model-specific parameter support.

## Message 44



- time: 2025.02.07 22:51:47

  level: !ERROR

  name: __main__

  funcName: *_log_api_error

  lineno: 280

  provider: openai

  model: o3-mini

  message: 'Error during (provider:openai | model:o3-mini) API call: Error code: 400 - {'error': {'message': "Unsupported parameter: 'temperature' is not supported with this model.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_parameter'}}'



- time: 2025.02.07 22:51:47

  level: !DEBUG

  name: __main__

  funcName: *_log_api_error

  lineno: 281

  provider: openai

  model: o3-mini

  message: 'Exception type: BadRequestError'



- time: 2025.02.07 22:51:47

  level: !DEBUG

  name: __main__

  funcName: *_log_api_error

  lineno: 282

  provider: openai

  model: o3-mini

  message: 'Detailed exception: Error code: 400 - {'error': {'message': "Unsupported parameter: 'temperature' is not supported with this model.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_parameter'}}'



here's the full code:
#!/usr/bin/env python3

"""

template_runnet.py

A single-file script for managing templates, LLM interactions, and orchestrating prompt refinements.

Now updated to pass each template's 'enhanced_prompt' as input to the next template in the chain.

"""



import os

import sys

import re

import glob

import json

from typing import List, Dict, Union, Optional

from pathlib import Path



from dotenv import load_dotenv

from loguru import logger



# Provider SDKs

from openai import OpenAI

from anthropic import Anthropic



# -------------------------------------------------------

# 1. LowestLevelCommunicator

# -------------------------------------------------------

class LowestLevelCommunicator:

    """

    Captures raw input and output strings at the lowest level,

    preserving the entire stream before any filtering or transformation.

    """

    def __init__(self):

        self.raw_interactions = []



    def record_request(self, provider: str, model_name: str, messages: List[Dict[str, str]]):

        """

        Called just before sending the request to the LLM.

        """

        self.raw_interactions.append({

            "direction": "request",

            "provider": provider,

            "model_name": model_name,

            "messages": messages,

        })



    def record_response(self, provider: str, model_name: str, response_text: str):

        """

        Called immediately after receiving the raw text from the LLM.

        """

        self.raw_interactions.append({

            "direction": "response",

            "provider": provider,

            "model_name": model_name,

            "content": response_text,

        })



    def get_all_interactions(self) -> List[Dict]:

        """

        Return the raw record of all requests/responses.

        """

        return self.raw_interactions



    def get_formatted_output(self) -> str:

        """

        Pretty-print a summary of the captured interactions.

        """

        lines = []

        for entry in self.raw_interactions:

            direction = entry["direction"].upper()

            provider = entry["provider"]

            model_name = entry["model_name"]



            if direction == "REQUEST":

                lines.append(f"--- REQUEST to {provider} (model: {model_name}) ---")

                for i, msg in enumerate(entry["messages"], start=1):

                    role = msg.get("role", "").upper()

                    content = msg.get("content", "")

                    lines.append(f"{i}. {role}: {content}")

            else:

                lines.append(f"--- RESPONSE from {provider} (model: {model_name}) ---")

                lines.append(entry["content"])

            lines.append("")



        return "\n".join(lines).strip()



# -------------------------------------------------------

# 2. Global Configuration

# -------------------------------------------------------

class Config:

    """

    Global settings

    """



    PROVIDER_OPENAI = "openai"

    PROVIDER_DEEPSEEK = "deepseek"

    PROVIDER_ANTHROPIC = "anthropic"



    AVAILABLE_MODELS = {

        PROVIDER_OPENAI: {

            "gpt-3.5-turbo": "Base GPT-3.5 Turbo model",

            "gpt-3.5-turbo-1106": "Enhanced GPT-3.5 Turbo",

            "gpt-4": "Latest GPT-4 stable release",

            "gpt-4-0125-preview": "Preview GPT-4 Turbo",

            "gpt-4-0613": "June 2023 GPT-4 snapshot",

            "gpt-4-1106-preview": "Preview GPT-4 Turbo",

            "gpt-4-turbo": "Latest GPT-4 Turbo release",

            "gpt-4-turbo-2024-04-09": "GPT-4 Turbo w/ vision",

            "gpt-4-turbo-preview": "Latest preview GPT-4 Turbo",

            "gpt-4o": "Base GPT-4o model",

            "gpt-4o-mini": "Lightweight GPT-4o variant",

        },

        PROVIDER_DEEPSEEK: {

            "deepseek-chat": "DeepSeek Chat model",

            "deepseek-reasoner": "Specialized reasoning model",

        },

        PROVIDER_ANTHROPIC: {

            "claude-2": "Base Claude 2 model",

            "claude-2.0": "Enhanced Claude 2.0",

            "claude-2.1": "Latest Claude 2.1 release",

            "claude-3-opus-20240229": "Claude 3 Opus",

            "claude-3-sonnet-20240229": "Claude 3 Sonnet",

            "claude-3-haiku-20240307": "Claude 3 Haiku",

        },

    }



    DEFAULT_MODEL_PARAMS = {

        PROVIDER_OPENAI: {

            "model_name": "gpt-4-turbo-preview", # (5) used occasionally

            "model_name": "gpt-4o",              # (4) debugging

            "model_name": "gpt-4-turbo",         # (3) used often

            "model_name": "gpt-3.5-turbo",       # (3) most used

            "model_name": "gpt-3.5-turbo-1106",  # (1) most used

            "model_name": "o3-mini",  # (1) most used

            "temperature": 0.7,

            "max_tokens": 800,

        },

        PROVIDER_DEEPSEEK: {

            "model_name": "deepseek-reasoner",

            "model_name": "deepseek-coder",

            "model_name": "deepseek-chat",

            "temperature": 0.7,

            "max_tokens": 800,

        },

        PROVIDER_ANTHROPIC: {

            "model_name": "claude-2.1",

            "model_name": "claude-3-opus-20240229",

            "temperature": 0.7,

            "max_tokens": 800,

        },

    }



    API_KEY_ENV_VARS = {

        PROVIDER_OPENAI: "OPENAI_API_KEY",

        PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",

        PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",

    }



    BASE_URLS = {

        PROVIDER_DEEPSEEK: "https://api.deepseek.com",

    }



    # Overriding allows for switching between providers by simply reordering the lines.

    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK

    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC

    DEFAULT_PROVIDER = PROVIDER_OPENAI



    def __init__(self):

        load_dotenv()

        self.configure_utf8_encoding()

        self.provider = self.DEFAULT_PROVIDER.lower()

        self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]

        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))

        self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()

        self.setup_logger()



    def configure_utf8_encoding(self):

        """

        Ensure UTF-8 encoding for standard output and error streams.

        """

        if hasattr(sys.stdout, "reconfigure"):

            sys.stdout.reconfigure(encoding="utf-8", errors="replace")

        if hasattr(sys.stderr, "reconfigure"):

            sys.stderr.reconfigure(encoding="utf-8", errors="replace")



    def setup_logger(self):

        """

        YAML logging via Loguru: clears logs, sets global context, and configures sinks

        """

        log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"

        log_filepath = os.path.join(self.log_dir, log_filename)

        open(log_filepath, "w").close()

        logger.remove()

        logger.configure(

            extra={

                "provider": self.provider,

                "model": self.model_params.get("model_name"),

            }

        )



        def yaml_sink(log_message):

            log_record = log_message.record

            formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")

            formatted_level = f"!{log_record['level'].name}"

            logger_name = log_record["name"]

            formatted_function_name = f"*{log_record['function']}"

            line_number = log_record["line"]

            extra_provider = log_record["extra"].get("provider")

            extra_model = log_record["extra"].get("model")

            log_message_content = log_record["message"]



            if "\n" in log_message_content:

                formatted_message = "|\n" + "\n".join(

                    f"  {line}" for line in log_message_content.splitlines()

                )

            else:

                formatted_message = (

                    f"'{log_message_content}'"

                    if ":" in log_message_content

                    else log_message_content

                )



            log_lines = [

                f"- time: {formatted_timestamp}",

                f"  level: {formatted_level}",

                f"  name: {logger_name}",

                f"  funcName: {formatted_function_name}",

                f"  lineno: {line_number}",

            ]

            if extra_provider is not None:

                log_lines.append(f"  provider: {extra_provider}")

            if extra_model is not None:

                log_lines.append(f"  model: {extra_model}")



            log_lines.append(f"  message: {formatted_message}")

            log_lines.append("")



            with open(log_filepath, "a", encoding="utf-8") as log_file:

                log_file.write("\n".join(log_lines) + "\n")



        logger.add(yaml_sink, level="DEBUG", enqueue=True, format="{message}")



# -------------------------------------------------------

# 3. LLM Interactions

# -------------------------------------------------------

class LLMInteractions:

    """

    Handles interactions with LLM APIs.

    """



    CLIENT_FACTORIES = {

        Config.PROVIDER_OPENAI: lambda key: OpenAI(api_key=key),

        Config.PROVIDER_DEEPSEEK: lambda key: OpenAI(api_key=key, base_url=Config.BASE_URLS[Config.PROVIDER_DEEPSEEK]),

        Config.PROVIDER_ANTHROPIC: lambda key: Anthropic(api_key=key),

    }



    def __init__(self, api_key=None, model_name=None, temperature=None, max_tokens=None, provider=None):

        self.config = Config()

        self.provider = provider or self.config.provider

        defaults = self.config.DEFAULT_MODEL_PARAMS[self.provider]

        self.model_name = model_name or defaults["model_name"]

        self.temperature = temperature if temperature is not None else defaults["temperature"]

        self.max_tokens = max_tokens if max_tokens is not None else defaults["max_tokens"]



        # Our communicator capturing lowest-level I/O

        self.communicator = LowestLevelCommunicator()

        self.client = self._create_client(api_key)



    def _create_client(self, api_key=None):

        api_key_env = Config.API_KEY_ENV_VARS.get(self.provider)

        api_key_use = api_key or os.getenv(api_key_env)

        try:

            return self.CLIENT_FACTORIES[self.provider](api_key_use)

        except KeyError:

            raise ValueError(f"Unsupported LLM provider: {self.provider}")



    def _log_api_response(self, response):

        prompt_tokens = getattr(getattr(response, "usage", None), "prompt_tokens", "N/A")

        logger.bind(prompt_tokens=prompt_tokens).debug(response)



    def _log_api_error(self, exception, model_name, messages):

        logger.error(f"Error during (provider:{self.provider} | model:{model_name}) API call: {exception}")

        logger.debug(f"Exception type: {type(exception).__name__}")

        logger.debug(f"Detailed exception: {exception}")

        logger.debug(f"Input messages: {messages}")



    def _execute_api_call(self, call_fn, model_name, messages):

        try:

            response = call_fn()

            self._log_api_response(response)

            return response

        except Exception as e:

            self._log_api_error(e, model_name, messages)

            return None



    def _openai_call(self, messages, model_name, temperature, max_tokens):

        try:

            # First try using both parameters as usual.

            return self.client.chat.completions.create(

                model=model_name,

                temperature=temperature,

                max_tokens=max_tokens,

                messages=messages,

            )

        except Exception as e:

            error_str = str(e)

            # Check if max_tokens is unsupported.

            if "Unsupported parameter: 'max_tokens'" in error_str:

                try:

                    return self.client.chat.completions.create(

                        model=model_name,

                        temperature=temperature,

                        max_completion_tokens=max_tokens,  # Use alternative parameter name.

                        messages=messages,

                    )

                except Exception as inner_e:

                    raise inner_e



            # Check if temperature is unsupported.

            elif "Unsupported parameter: 'temperature'" in error_str:

                try:

                    return self.client.chat.completions.create(

                        model=model_name,

                        max_tokens=max_tokens,

                        messages=messages,

                    )

                except Exception as inner_e:

                    raise inner_e



            # Otherwise, re-raise the original error.

            raise e



    def _anthropic_call(self, messages, model_name, temperature, max_tokens):

        system_prompt = "\n".join(msg["content"] for msg in messages if msg["role"] == "system")

        user_msgs = [msg for msg in messages if msg["role"] == "user"]



        return self.client.messages.create(

            model=model_name,

            max_tokens=max_tokens,

            temperature=temperature,

            system=system_prompt.strip(),

            messages=user_msgs,

        )



    def _deepseek_call(self, messages, model_name, temperature, max_tokens):

        system_prompt = next((msg["content"] for msg in messages if msg["role"] == "system"), "")

        instructions_content = "\n".join(

            msg["content"]

            for msg in messages

            if msg["role"] == "system" and msg["content"] != system_prompt

        )

        user_prompt = next((msg["content"] for msg in messages if msg["role"] == "user"), "")

        combined_prompt = f"{system_prompt}\n{instructions_content}\n{user_prompt}"

        return self.client.chat.completions.create(

            model=model_name,

            temperature=temperature,

            max_tokens=max_tokens,

            messages=[{"role": "user", "content": combined_prompt}],

        )



    def _execute_llm_api_call(self, messages, model_name, temperature, max_tokens):

        # Record the raw request data

        self.communicator.record_request(self.provider, model_name, messages)



        provider_map = {

            Config.PROVIDER_OPENAI: lambda: self._openai_call(messages, model_name, temperature, max_tokens),

            Config.PROVIDER_DEEPSEEK: lambda: self._deepseek_call(messages, model_name, temperature, max_tokens),

            Config.PROVIDER_ANTHROPIC: lambda: self._anthropic_call(messages, model_name, temperature, max_tokens),

        }



        provider_api_request = provider_map.get(self.provider)

        if provider_api_request is None:

            raise ValueError(f"Unsupported LLM provider: {self.provider}")



        api_response = self._execute_api_call(provider_api_request, model_name, messages)

        if not api_response:

            return None



        # Parse out raw text

        if self.provider in [Config.PROVIDER_OPENAI, Config.PROVIDER_DEEPSEEK]:

            raw_text = (

                api_response.choices[0].message.content

                if hasattr(api_response, "choices") and api_response.choices

                else None

            )

        elif self.provider == Config.PROVIDER_ANTHROPIC:

            raw_text = (

                api_response.content[0].text

                if hasattr(api_response, "content") and api_response.content

                else None

            )

        else:

            raw_text = None



        # Record the raw response

        if raw_text is not None:

            self.communicator.record_response(self.provider, model_name, raw_text)



        return raw_text



    def generate_response(self, messages, model_name=None, temperature=None, max_tokens=None):

        used_model = model_name or self.model_name

        used_temp = temperature if temperature is not None else self.temperature

        used_tokens = max_tokens if max_tokens is not None else self.max_tokens

        return self._execute_llm_api_call(messages, used_model, used_temp, used_tokens)



# -------------------------------------------------------

# 4. Template File Manager

# -------------------------------------------------------

class TemplateFileManager:

    """

    Manages prompt templates, performing lazy loading, placeholder substitution,

    and recipe execution.

    """



    ALLOWED_FILE_EXTS = (".xml", ".py", ".json", ".md", ".txt")

    EXCLUDED_FILE_NAMES = ["ignore_me", "do_not_load", "temp_file"]

    EXCLUDED_FILE_PATHS = ["\\_md\\"]

    EXCLUDED_PATTERNS = [r".*\.\d{2}\.\d{2}.*", ]

    MAX_TEMPLATE_SIZE_KB = 100

    REQUIRED_CONTENT_MARKERS = ["<template>", "[INPUT_PROMPT]"]



    def __init__(self):

        self.template_dir = os.getcwd()

        self.template_cache = {}



    def template_qualifier(self, filepath):

        _, ext = os.path.splitext(filepath)

        filename = os.path.basename(filepath)

        basename, _ = os.path.splitext(filename)

        filepath_lower = filepath.lower()



        if ext.lower() not in self.ALLOWED_FILE_EXTS:

            return False

        if basename in self.EXCLUDED_FILE_NAMES:

            return False

        if any(excluded in filepath_lower for excluded in self.EXCLUDED_FILE_PATHS):

            return False

        if any(re.match(pattern, filename, re.IGNORECASE) for pattern in self.EXCLUDED_PATTERNS):

            return False

        try:

            filesize_kb = os.path.getsize(filepath) / 1024

            if filesize_kb > self.MAX_TEMPLATE_SIZE_KB:

                return False

            with open(filepath, "r", encoding="utf-8") as f:

                content = f.read()

            if not all(marker in content for marker in self.REQUIRED_CONTENT_MARKERS):

                return False

        except Exception:

            return False



        return True



    def reload_templates(self):

        self.template_cache.clear()

        pattern = os.path.join(self.template_dir, "**", "*.*")

        for filepath in glob.glob(pattern, recursive=True):

            name = os.path.splitext(os.path.basename(filepath))[0]

            if self.template_qualifier(filepath):

                self.template_cache[name] = filepath



    def prefetch_templates(self, template_name_list):

        for name in template_name_list:

            _ = self.get_template_path(name)



    def get_template_path(self, template_name):

        if template_name in self.template_cache:

            return self.template_cache[template_name]

        for ext in self.ALLOWED_FILE_EXTS:

            search_pattern = os.path.join(self.template_dir, "**", f"{template_name}{ext}")

            files = glob.glob(search_pattern, recursive=True)

            if files:

                self.template_cache[template_name] = files[0]

                return files[0]

        return None



    def _parse_template(self, template_path):

        try:

            with open(template_path, "r", encoding="utf-8") as f:

                content = f.read()



            placeholders = list(set(re.findall(r"\[(.*?)\]", content)))

            template_data = {

                "path": template_path,

                "content": content,

                "placeholders": placeholders,

            }

            return template_data



        except Exception as e:

            logger.error(f"Error parsing template file {template_path}: {e}")

            return {}



    def extract_placeholders(self, template_name):

        template_path = self.get_template_path(template_name)

        if not template_path:

            return []

        parsed_template = self._parse_template(template_path)

        if not parsed_template:

            return []

        return parsed_template.get("placeholders", [])



    def get_template_metadata(self, template_name):

        template_path = self.get_template_path(template_name)

        if not template_path:

            return {}

        parsed_template = self._parse_template(template_path)

        if not parsed_template:

            return {}



        content = parsed_template["content"]

        metadata = {

            "agent_name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),

            "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),

            "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>'),

            "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),

            "system_prompt": self._extract_value_from_content(content, r'<system_prompt value="([^"]*)"\s*/>')

        }

        return metadata



    def _extract_value_from_content(self, content, pattern):

        match = re.search(pattern, content)

        return match.group(1) if match else None



    def list_templates(

        self,

        exclude_paths=None,

        exclude_names=None,

        exclude_versions=None,

        exclude_statuses=None,

        exclude_none_versions=False,

        exclude_none_statuses=False

    ):

        search_pattern = os.path.join(self.template_dir, "**", "*.*")

        templates_info = {}



        for filepath in glob.glob(search_pattern, recursive=True):

            if not self.template_qualifier(filepath):

                continue

            template_name = os.path.splitext(os.path.basename(filepath))[0]

            parsed_template = self._parse_template(filepath)

            if not parsed_template:

                logger.warning(f"Skipping {filepath} due to parsing error.")

                continue

            content = parsed_template["content"]

            try:

                templates_info[template_name] = {

                    "path": filepath,

                    "name": self._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),

                    "description": self._extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),

                    "version": self._extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),

                    "status": self._extract_value_from_content(content, r'<status value="([^"]*)"\s*/>')

                }

            except Exception as e:

                logger.error(f"Error loading template from {filepath}: {e}")



        filtered_templates = {}

        for name, info in templates_info.items():

            if ((not exclude_paths or info["path"] not in exclude_paths) and

                (not exclude_names or info["name"] not in exclude_names) and

                (not exclude_versions or info["version"] not in exclude_versions) and

                (not exclude_statuses or info["status"] not in exclude_statuses) and

                (not exclude_none_versions or info["version"] is not None) and

                (not exclude_none_statuses or info["status"] is not None)):

                filtered_templates[name] = info



        return filtered_templates



    def prepare_template(self, template_filepath, input_prompt=""):

        parsed_template = self._parse_template(template_filepath)

        if not parsed_template:

            return None



        content = parsed_template["content"]

        placeholders = {

            "[HEADER]": f"```{os.path.splitext(template_filepath)[1]}",

            "[FILENAME]": os.path.basename(template_filepath),

            "[OUTPUT_FORMAT]": "plain_text",

            "[ORIGINAL_PROMPT_LENGTH]": str(len(input_prompt)),

            "[RESPONSE_PROMPT_LENGTH]": str(int(len(input_prompt) * 0.9)),

            "[INPUT_PROMPT]": input_prompt,

            "[ADDITIONAL_CONSTRAINTS]": "",

            "[ADDITIONAL_PROCESS_STEPS]": "",

            "[ADDITIONAL_GUIDELINES]": "",

            "[ADDITIONAL_REQUIREMENTS]": "",

            "[FOOTER]": f"```",

        }



        for placeholder, value in placeholders.items():

            value_str = str(value)

            content = content.replace(placeholder, value_str)



        return content





    def _extract_template_parts(self, raw_text):

        """

        Extracts relevant sections from the raw template text.

        E.g., <system_prompt ...>, <response_format>, etc.

        """

        metadata_match = re.search(r"<metadata>(.*?)</metadata>", raw_text, re.DOTALL)

        response_format_match = re.search(r"<response_format>(.*?)</response_format>", raw_text, re.DOTALL)



        start_end_match = re.search(r"\[TEMPLATE_START\](.*?)\[TEMPLATE_END\]", raw_text, re.DOTALL)

        start_end = start_end_match.group(1) if start_end_match else ""



        template_match = re.search(r"<template>(.*?)</template>", raw_text, re.DOTALL)

        agent_match = re.search(r"<agent>(.*?)</agent>", raw_text, re.DOTALL)



        system_prompt_match = re.search(r'<system_prompt value="([^"]*)"\s*/>', raw_text)

        instructions_match = re.search(r"<instructions>(.*?)</instructions>", raw_text, re.DOTALL)



        system_prompt = system_prompt_match.group(1) if system_prompt_match else ""

        response_format = response_format_match.group(1) if response_format_match else ""

        template = template_match.group(1) if template_match else ""

        instructions = instructions_match.group(1) if instructions_match else ""



        return system_prompt, response_format, start_end



# -------------------------------------------------------

# 5. Prompt Refinement Orchestrator

# -------------------------------------------------------

class PromptRefinementOrchestrator:

    def __init__(self, template_manager: TemplateFileManager, agent: LLMInteractions):

        self.template_manager = template_manager

        self.agent = agent



    def _build_messages(self, system_prompt: str, agent_instructions: str) -> List[Dict[str, str]]:

        """

        Prepare message.

        """

        return [

            {"role": "system", "content": system_prompt.strip()},

            {"role": "user", "content": agent_instructions.strip()},

        ]



    def _format_multiline(self, text):

        """

        Nicely format the text for console output (esp. if it is JSON).

        """

        if isinstance(text, dict) or isinstance(text, list):

            return json.dumps(text, indent=4, ensure_ascii=False)

        elif isinstance(text, str):

            try:

                if text.startswith("```json"):

                    json_match = re.search(r"```json\n(.*?)\n```", text, re.DOTALL)

                    if json_match:

                        return json.dumps(json.loads(json_match.group(1)), indent=4, ensure_ascii=False)

                elif text.strip().startswith("{") and text.strip().endswith("}"):

                    return json.dumps(json.loads(text), indent=4, ensure_ascii=False)

            except json.JSONDecodeError:

                pass

        return text.replace("\\n", "\n")



    # -------------------------------------------------------

    # CHANGED: Now parse "enhanced_prompt" from the JSON output

    #          and pass it on to the next iteration.

    # -------------------------------------------------------

    def execute_prompt_refinement_chain_from_file(

        self,

        template_filepath,

        input_prompt,

        refinement_count=1,

        model_name=None,

        temperature=None,

        max_tokens=None

    ):

        """

        Executes refinement(s) using one file-based template,

        passing 'enhanced_prompt' forward if present in the response JSON.

        """

        content = self.template_manager.prepare_template(template_filepath, input_prompt)

        if not content:

            return None



        agent_name = self.template_manager._extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>')

        system_prompt, response_format, agent_instructions = self.template_manager._extract_template_parts(content)

        prompt = input_prompt

        results = []

        print(f'agent_instructions: {agent_instructions}')



        for _ in range(refinement_count):

            msgs = self._build_messages(system_prompt.strip(), agent_instructions)

            # print(f'msgs: {msgs}')

            refined = self.agent.generate_response(

                msgs,

                model_name=model_name,

                temperature=temperature,

                max_tokens=max_tokens,

            )

            if refined:

                # Attempt to pretty-print if JSON

                refined_str = refined

                try:

                    data = json.loads(refined_str)

                    refined_str = json.dumps(data, indent=4)

                except json.JSONDecodeError:

                    pass



                # Store the full raw response

                results.append(refined)



                # If the response is JSON and has "enhanced_prompt," pass that as the new input

                next_prompt = refined

                try:

                    data = json.loads(refined)

                    if isinstance(data, dict) and "enhanced_prompt" in data:

                        next_prompt = data["enhanced_prompt"]

                except (TypeError, json.JSONDecodeError):

                    pass



                prompt = next_prompt



        return results



    def _execute_single_template_refinement(self, template_name, initial_prompt, refinement_count, **kwargs):

        path = self.template_manager.get_template_path(template_name)

        if not path:

            logger.error(f"No template file found with name: {template_name}")

            return None

        return self.execute_prompt_refinement_chain_from_file(path, initial_prompt, refinement_count, **kwargs)



    def _execute_multiple_template_refinement(self, template_name_list, initial_prompt, refinement_levels, **kwargs):

        if not all(isinstance(template, str) for template in template_name_list):

            logger.error("All items in template_name_list must be strings.")

            return None

        if isinstance(refinement_levels, int):

            counts = [refinement_levels] * len(template_name_list)

        elif isinstance(refinement_levels, list) and len(refinement_levels) == len(template_name_list):

            counts = refinement_levels

        else:

            logger.error("refinement_levels must be int or a list matching template_name_list.")

            return None



        results = []

        current_prompt = initial_prompt

        for name, cnt in zip(template_name_list, counts):

            chain_result = self._execute_single_template_refinement(name, current_prompt, cnt, **kwargs)

            if chain_result:

                # The last returned string from that chain becomes the next prompt

                current_prompt = chain_result[-1]

                results.append({"agent_name": name, "iterations": cnt, "outputs": chain_result})

        return results



    def execute_prompt_refinement_by_name(

        self,

        template_name_or_list: Union[str, List[str]],

        initial_prompt: str,

        refinement_levels: Union[int, List[int]] = 1,

        model_name=None,

        temperature=None,

        max_tokens=None

    ):

        if isinstance(template_name_or_list, str):

            return self._execute_single_template_refinement(

                template_name_or_list,

                initial_prompt,

                refinement_levels,

                model_name=model_name,

                temperature=temperature,

                max_tokens=max_tokens,

            )

        elif isinstance(template_name_or_list, list):

            if not all(isinstance(x, str) for x in template_name_or_list):

                logger.error("All items in template_name_or_list must be strings.")

                return None

            return self._execute_multiple_template_refinement(

                template_name_or_list,

                initial_prompt,

                refinement_levels,

                model_name=model_name,

                temperature=temperature,

                max_tokens=max_tokens,

            )

        else:

            logger.error("template_name_or_list must be str or list[str].")

            return None



    def execute_recipe(self, recipe: List[Dict], initial_prompt: str) -> Dict[str, Union[str, List[Dict]]]:

        """

        Executes a multi-step prompt refinement "recipe."

        """

        current_input = initial_prompt

        refinement_history = []

        gathered_outputs = []



        for idx, step in enumerate(recipe, start=1):

            chain = step.get("chain")

            repeats = step.get("repeats", 1)

            gather = step.get("gather", False)

            aggregator = step.get("aggregator_chain")

            if not chain:

                logger.error(f"Recipe step {idx} missing 'chain' key.")

                continue



            step_gathered = []

            for rep in range(repeats):

                data = self.execute_prompt_refinement_by_name(chain, current_input, 1)

                if data:

                    refinement_history.append({"step": idx, "repeat": rep + 1, "chain": chain, "result": data})

                    final_str = data[-1]

                    step_gathered.append(final_str)

                    if not gather:

                        current_input = final_str



            if gather and step_gathered:

                gathered_outputs.extend(step_gathered)

                if aggregator:

                    aggregator_prompt = "\n\n".join([f"Option {i+1}:\n{v}" for i, v in enumerate(step_gathered)])

                    aggregator_data = self.execute_prompt_refinement_by_name(aggregator, aggregator_prompt, 1)

                    if aggregator_data:

                        refinement_history.append({

                            "step": idx,

                            "aggregator_chain": aggregator,

                            "aggregator_input": aggregator_prompt,

                            "aggregator_result": aggregator_data,

                        })

                        current_input = aggregator_data[-1]

                    else:

                        current_input = step_gathered[-1]

                else:

                    current_input = step_gathered[-1]



        return {

            "final_output": current_input,

            "refinement_history": refinement_history,

            "gathered_outputs": gathered_outputs,

        }



# -------------------------------------------------------

# 6. Main Execution

# -------------------------------------------------------

class Execution:

    def __init__(self, provider=None):

        self.config = Config()

        self.agent = LLMInteractions(provider=provider)

        self.template_manager = TemplateFileManager()

        self.refinement_engine = PromptRefinementOrchestrator(self.template_manager, self.agent)



    def log_usage_demo(self):

        self.template_manager.reload_templates()

        self.template_manager.prefetch_templates(["PromptOptimizerExpert", "MultiResponseSelector", "IntensityEnhancer"])



        placeholders = self.template_manager.extract_placeholders("IntensityEnhancer")

        logger.info(f"Placeholders in 'IntensityEnhancer': {placeholders}")



        metadata = self.template_manager.get_template_metadata("IntensityEnhancer")

        logger.info(f"Metadata for 'IntensityEnhancer': {metadata}")



        all_temps = self.template_manager.list_templates(exclude_none_statuses=True, exclude_none_versions=True)

        logger.info(f"Found a total of {len(all_temps)} templates.")

        logger.info("Template keys: " + ", ".join(all_temps.keys()))



    def run(self):

        self.log_usage_demo()

        self.template_manager.reload_templates()



        # Example recipe - two passes of PromptOptimizerExpert, gather results,

        # then feed to MultiResponseSelector aggregator.

        # recipe_steps = [

        #     {

        #         "chain": ["PromptOptimizerExpert", "PromptOptimizerExpert"],

        #         "repeats": 1,

        #         "gather": True,

        #         "aggregator_chain": ["MultiResponseSelector"],

        #     },

        # ]



        recipe_steps = [

            {

                # "chain": ["PromptOptimizerExpert"],

                # "chain": ["EmphasisEnhancer", "PromptOptimizerExpert"],

                "chain": ["IntensityEnhancer"],

                "repeats": 1,

                # "gather": True,

                # "aggregator_chain": ["MultiResponseSelector"],

            },

            # {

            #     "chain": "RunwayPromptBuilder",

            #     "repeats": 1,

            # },

            # {

            #     "chain": ["CinematicSceneDescriptor", "MultiResponseSelector"],

            #     "repeats": 1,

            # },

            # {"chain": "CodeCommentCondenser", "repeats": 1},

            # {"chain": "MultiResponseSelector", "repeats": 1},

        ]





        initial_prompt = (

            "You are a steadfast, detail-oriented assistant. Your critical task is to refine "

            "this prompt with absolute precision and laser-sharp clarity. Adhere strictly to all "

            "guidelines, ensuring the refined version fully captures and enhances the original's "

            "essence and intent."

        )



        initial_prompt = "Ignite a scenario so genuine and commanding it stirs unshakable empathy, leaving no heart unmoved. Demonstrate precisely how to shape and structure this video’s narrative, ensuring it resonates with staggering impact and unforgettable power. A scene that conveys profound loss."

        initial_prompt = "Develop a response entirely in a single, continuous line without using any line breaks or formatting elements. You will get a prompt that you need to refine. Follow the agent's instructions and refine prompt for absolute clarity and emphasis. Ensure every word and phrase carries unmistakable importance and significance. The message must be clear and powerful, leaving no room for ambiguity. Emphasize the core message forcefully.. Rewrite the input with absolute and unmistakable emphasis. Every word and phrase must resonate with heightened importance and significance. Make it undeniably clear what the core message is through the sheer force of your emphatic language. There should be no ambiguity—the emphasis must be palpable. Provide your response in a single unformatted line without linebreaks. Prioritize clarity, conciseness, and grammatical accuracy. Use strong, precise language to convey impactful meaning. Preserve the essence of the core message without dilution. Favor brevity while maintaining high-value content. Strive for words that deliver maximum depth and meaning. Develop a response entirely in a single, continuous line without using any line breaks or formatting elements."

        initial_prompt = "Develop a response entirely in a single, continuous line without using any line breaks or formatting elements. Emphasizing the core message with forceful language and palpable significance. Preserve the essence of the core message without dilution. Provide your response in a single unformatted line without linebreaks."

        initial_prompt = """

- Prioritize clarity, conciseness, and grammatical accuracy.

- Use strong, precise language to convey impactful meaning.

- Preserve the essence of the core message without dilution.

- Favor brevity while maintaining high-value content.

- Strive for words that deliver maximum depth and meaning.

- Focus on delivering messages with heightened impact.

- Identify and emphasize the core message with clarity.

- Use strategic wording to intensify the message's significance.

- Ensure every word contributes to the message's potency.

- Avoid exaggeration; ensure emphasis is deliberate and effective.

- Preserve the original intent while amplifying its importance.

- Choose impactful brevity when it enhances the message.

- Ensure the message resonates with absolute certainty.

- Brevity: Express the core message with minimal words.

- Impact:  Maximize the message's power and resonance.

- Clarity: Ensure the refined message is crystal clear.

- Message: Preserving the essence of the core message without dilution.



"""

        initial_prompt = """enhance prompt:

* **Efficiency:** Its quote-finding strategy is likely more efficient, especially for large documents.

* **Accuracy:** The explicit handling of nested quotes prevents errors that could occur in Version 1.

* **Maintainability:** The code is better organized and easier to modify or extend in the future."""

        initial_prompt = """enhance prompt: Enhance intensity while preserving intent and clarity"""

        recipe_result = self.refinement_engine.execute_recipe(

            recipe=recipe_steps, initial_prompt=initial_prompt

        )



        # Show a formatted log of all raw I/O from the communicator

        print("\n=== Full Communicator Log (Raw I/O) ===")

        print(self.agent.communicator.get_formatted_output())









if __name__ == "__main__":

    provider_to_use = os.getenv("LLM_PROVIDER", Config.DEFAULT_PROVIDER).lower()

    execution = Execution(provider=provider_to_use)

    execution.run()


## Message 45

**Proposing code modifications**

The user is asking for a way to dynamically handle errors related to unsupported parameters, specifically for the `temperature` parameter in the `_openai_call` method. 

I need to update the code to check for this error and remove the `temperature` parameter if it's unsupported. I'll write the modification, being sure to avoid unnecessary complexity while making it effective. The final answer will include the updated code snippet and a clear explanation of how it works.

**Proposing dynamic handling for parameters**

The user wants a dynamic way to handle unsupported parameters (like temperature or max_tokens). Based on the existing method, we’ve already caught the “Unsupported parameter” error for temperature. But maybe I can enhance it by using a dictionary to map parameter names to values and then filter out unsupported ones based on the error messages, providing a more flexible approach.

I’ll propose the update by modifying `_openai_call` to use a dynamic check, which is a nice balance between simplicity and effectiveness. Time to finalize the code!

## Message 46

Below is one clean and dynamic solution. The idea is to wrap the API call in a try/except block that first attempts the call with both parameters. If an error indicates that one of them is unsupported, we retry the call without that parameter (or with its alternative name). For example:

```python
def _openai_call(self, messages, model_name, temperature, max_tokens):
    try:
        # First try using both parameters as usual.
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
        )
    except Exception as e:
        error_str = str(e)
        # If the error indicates that max_tokens is unsupported, try using max_completion_tokens instead.
        if "Unsupported parameter: 'max_tokens'" in error_str:
            try:
                return self.client.chat.completions.create(
                    model=model_name,
                    temperature=temperature,
                    max_completion_tokens=max_tokens,
                    messages=messages,
                )
            except Exception as inner_e:
                raise inner_e

        # If the error indicates that temperature is unsupported, retry without it.
        elif "Unsupported parameter: 'temperature'" in error_str:
            try:
                return self.client.chat.completions.create(
                    model=model_name,
                    max_tokens=max_tokens,
                    messages=messages,
                )
            except Exception as inner_e:
                raise inner_e

        # Otherwise, re-raise the original error.
        raise e
```

### How This Works

- **Initial Attempt:**  
  The method first tries the standard call with both `temperature` and `max_tokens`.

- **Dynamic Fallback for `max_tokens`:**  
  If the error message indicates that `"max_tokens"` is unsupported (as with some models), it retries using the alternative parameter name `"max_completion_tokens"`.

- **Dynamic Fallback for `temperature`:**  
  Similarly, if the error message shows that `"temperature"` is unsupported, the method retries the API call without including the temperature parameter—letting the model use its default setting.

- **Simplicity & Flexibility:**  
  This approach avoids hard-coding a list of models or pre-checking capabilities. Instead, it reacts dynamically to the error messages, making it both simple and effective.

This solution seamlessly handles the dynamic differences in parameter support among OpenAI models without complicating the overall code.

## Message 47



- time: 2025.02.07 22:52:46

  level: !ERROR

  name: __main__

  funcName: *_log_api_error

  lineno: 280

  provider: openai

  model: o3-mini

  message: 'Error during (provider:openai | model:o3-mini) API call: Error code: 400 - {'error': {'message': "Unsupported parameter: 'temperature' is not supported with this model.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_parameter'}}'



- time: 2025.02.07 22:52:46

  level: !DEBUG

  name: __main__

  funcName: *_log_api_error

  lineno: 281

  provider: openai

  model: o3-mini

  message: 'Exception type: BadRequestError'



- time: 2025.02.07 22:52:46

  level: !DEBUG

  name: __main__

  funcName: *_log_api_error

  lineno: 282

  provider: openai

  model: o3-mini

  message: 'Detailed exception: Error code: 400 - {'error': {'message': "Unsupported parameter: 'temperature' is not supported with this model.", 'type': 'invalid_request_error', 'param': 'temperature', 'code': 'unsupported_parameter'}}'


## Message 48

**Fixing unsupported temperature parameter**

The user is encountering an error related to the "temperature" parameter, specifically with the "o3-mini" model. The error message indicates that the model doesn't support this parameter. There's already some fallback code to try removing it, but it seems that solution didn't work. I need to update the code to dynamically check for this specific error and re-call the function without the "temperature" parameter when it appears in the error message.

**Fixing error handling for unsupported temperature parameter**

It looks like the provided code snippet didn't fully resolve the issue, as you’re still seeing the error about the unsupported "temperature" parameter. The code is meant to handle it, but maybe it's not catching variations of the error message. I’m thinking we could try a case-insensitive search for "Unsupported parameter: 'temperature'”. Maybe a regular expression can help: `re.search(r"Unsupported parameter: 'temperature'", error_str, re.IGNORECASE)`. If that error is triggered, we can then remove the temperature parameter and retry the call. I'll still modify the `_openai_call` method to implement this.

## Message 49

One clean solution is to make the error‐detection case‑insensitive and then remove the offending parameter from the call. For example, you can update your `_openai_call` method as follows:

```python
def _openai_call(self, messages, model_name, temperature, max_tokens):
    try:
        # First try using both parameters as usual.
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
        )
    except Exception as e:
        error_str = str(e).lower()  # convert to lowercase for case-insensitive matching

        if "unsupported parameter" in error_str:
            if "max_tokens" in error_str:
                # Retry using the alternative parameter name for max tokens.
                try:
                    return self.client.chat.completions.create(
                        model=model_name,
                        temperature=temperature,
                        max_completion_tokens=max_tokens,  # alternative name
                        messages=messages,
                    )
                except Exception as inner_e:
                    raise inner_e

            if "temperature" in error_str:
                # Retry without sending the temperature parameter.
                try:
                    return self.client.chat.completions.create(
                        model=model_name,
                        max_tokens=max_tokens,
                        messages=messages,
                    )
                except Exception as inner_e:
                    raise inner_e

        # If none of these conditions are met, re-raise the original error.
        raise e
```

### How This Works

1. **Case‑Insensitive Error Checking:**  
   By converting the error string to lowercase (`error_str = str(e).lower()`), we make sure that our checks (e.g. `"unsupported parameter"` or `"temperature"`) work regardless of the original casing.

2. **Dynamic Fallback for `max_tokens`:**  
   If the error mentions that `"max_tokens"` is unsupported, the code retries using the alternative parameter name `max_completion_tokens`.

3. **Dynamic Fallback for `temperature`:**  
   If the error indicates that `"temperature"` is unsupported, the code retries without including the temperature parameter at all. This lets the model fall back to its default value.

This approach is simple, dynamic, and requires minimal changes—allowing your framework to seamlessly handle models (like the o3-mini) that do not support certain parameters.

## Message 50

Below is a concise, dynamic fallback solution in the `_openai_call` method. It gracefully retries with adjusted parameters if the API complains about `temperature` or `max_tokens` being unsupported. Otherwise, it re-raises the original exception.

```python
def _openai_call(self, messages, model_name, temperature, max_tokens):
    """
    Attempts an OpenAI API call using temperature and max_tokens.
    If the model doesn't support one of these parameters, it falls back
    by adjusting or removing that parameter and retries the call.
    """
    try:
        # First try using both parameters as usual.
        return self.client.chat.completions.create(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            messages=messages,
        )
    except Exception as e:
        error_str = str(e)

        # Fallback for unsupported max_tokens:
        if "Unsupported parameter: 'max_tokens'" in error_str:
            try:
                return self.client.chat.completions.create(
                    model=model_name,
                    temperature=temperature,
                    max_completion_tokens=max_tokens,  # Use alternative param name
                    messages=messages,
                )
            except Exception as inner_e:
                raise inner_e

        # Fallback for unsupported temperature:
        elif "Unsupported parameter: 'temperature'" in error_str:
            try:
                return self.client.chat.completions.create(
                    model=model_name,
                    max_tokens=max_tokens,
                    messages=messages,
                )
            except Exception as inner_e:
                raise inner_e

        # If it's neither of those specific issues, re-raise the original.
        raise e
```

### Why This Works

1. **Initial Call:**  
   A normal call is attempted with both `temperature` and `max_tokens`.

2. **Error Inspection:**  
   If the response indicates `max_tokens` is unsupported, the method retries using `max_completion_tokens` (OpenAI's alternative parameter).  
   If `temperature` is the problem, it retries without the `temperature` parameter (allowing the model’s default).

3. **Keeps Code Simple:**  
   No need for pre-knowledge of which models support which parameters. It simply reacts to the error messages coming back from the API.

4. **Easy to Extend:**  
   Should you need to handle additional unsupported parameters in the future (e.g., `top_p`, `n`, etc.), you can follow the same pattern of retrying upon detecting a specific error message.
