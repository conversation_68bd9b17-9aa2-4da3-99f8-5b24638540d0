# RigOfficeDownloader Markdown Guide

Conversation ID: 682b0177-f1d4-8008-9ab0-f4c06fd7613a

## Message 1

please convert this into markdown (`README.md`):



{

  "combined_result": {

    "README.md": {

      "title": "RigOfficeDownloader",

      "purpose": "Automate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and enabling rapid, organized, and auditable access to engineering documentation.",

      "features": [

        "Three-stage sequential pipeline: Documents → Files → Downloads",

        "Automated and interactive (menu-driven) run modes",

        "Editable Markdown checkpoints for document/file selection",

        "Configurable pattern-based filter chains (auto-inclusion/exclusion)",

        "Metadata-driven file naming and hierarchical subfolder organization",

        "Robust Selenium-based browser automation, smart waiting, deduplication, and error handling",

        "Strict field ordering in JSON and Markdown outputs",

        "Comprehensive traceability and auditability via exported JSON and Markdown records"

      ],

      "workflow": [

        "1. Configure rig number and search URLs (filter/templates setup)",

        "2. Fetch document metadata from RigDoc to <rig>-a-docs.json",

        "3. Export docs to Markdown (<rig>-a-docs.md); review and set item_include as needed",

        "4. Import updated selections from Markdown → JSON",

        "5. Fetch file metadata for selected docs → <rig>-b-files.json",

        "6. Export files to Markdown (<rig>-b-files.md); set item_download as needed",

        "7. Import updated file selections from Markdown",

        "8. Download marked files to outputs/downloads/<rig>, auto-organized via generated subfolder paths"

      ],

      "directory_structure": [

        "outputs/",

        "├── data/         # All metadata/selection (JSON + Markdown)",

        "│   ├── <rig>-a-docs.json",

        "│   ├── <rig>-a-docs.md",

        "│   ├── <rig>-b-files.json",

        "│   └── <rig>-b-files.md",

        "└── downloads/    # All downloaded files, by rig/subfolder structure",

        "    └── <rig>/",

        "        ├── <subfolder>/",

        "        │   └── <item_generated_name>.<ext>",

        "        └── ..."

      ],

      "setup_prerequisites": [

        "Windows OS",

        "Python 3.11+",

        "Chrome browser (with valid Chromedriver)",

        "Valid NOV RigDoc credentials",

        "Install dependencies: pip install -r requirements.txt"

      ],

      "execution_run_modes": [

        "RigOfficeDownloader-v4.bat --auto           # Full pipeline, minimal user intervention",

        "RigOfficeDownloader-v4.bat --interactive    # Menu-driven, step-by-step workflow",

        "RigOfficeDownloader-v4.bat --config         # Edit search templates and filters"

      ],

      "configuration_and_filtering": [

        "Edit filter chains (glob patterns: *G000*, *VOID*, etc.) for auto-inclusion/exclusion",

        "Specify rig number and search URLs in configuration",

        "Modify which metadata fields control selection"

      ],

      "benefits": [

        "≥75% reduction in document preparation time",

        "Consistent, metadata-driven file organization",

        "Centralized user review and selection via Markdown checkpoints",

        "Robust error handling, deduplication, and smart automation",

        "Scalable, cohesive, and audit-ready architecture for broader adoption"

      ],

      "comparison_table": {

        "Time per project": {

          "Manual": "4–6 hours",

          "Automated": "<1 hour"

        },

        "File organization": {

          "Manual": "Error-prone, ad hoc",

          "Automated": "Metadata-driven, standardized"

        },

        "User review": {

          "Manual": "Tedious and repetitive",

          "Automated": "Centralized via Markdown checkpoints"

        },

        "Auditability": {

          "Manual": "Weak or absent",

          "Automated": "Full JSON/Markdown traceability"

        },

        "Expandability": {

          "Manual": "N/A",

          "Automated": "Configurable, reusable templates"

        }

      },

      "extensibility": [

        "Built-in support for subfolders, pattern-based filter chains, customizable field ordering, advanced configuration",

        "Easily adaptable for new departments or expanded document organization/selection policies"

      ],

      "philosophy": "\"Complexity is the enemy of execution — Optimized for engineers, by engineers.\""

    }

  }

}


## Message 2

**Creating a structure**

I’m mapping out the markdown document, focusing on sections like title, purpose, features, and workflow. The comparison table will be organized in markdown format for clarity.

## Message 3

```markdown
# RigOfficeDownloader

## Purpose
Automate and streamline technical document retrieval from NOV RigDoc, eliminating tedious manual effort and enabling rapid, organized, and auditable access to engineering documentation.

## Features
- Three-stage sequential pipeline: **Documents → Files → Downloads**
- Automated and interactive (menu-driven) run modes
- Editable Markdown checkpoints for document/file selection
- Configurable pattern-based filter chains (auto-inclusion/exclusion)
- Metadata-driven file naming and hierarchical subfolder organization
- Robust Selenium-based browser automation, smart waiting, deduplication, and error handling
- Strict field ordering in JSON and Markdown outputs
- Comprehensive traceability and auditability via exported JSON and Markdown records

## Workflow
1. Configure rig number and search URLs (filter/templates setup)  
2. Fetch document metadata from RigDoc to `<rig>-a-docs.json`  
3. Export docs to Markdown (`<rig>-a-docs.md`); review and set `item_include` as needed  
4. Import updated selections from Markdown → JSON  
5. Fetch file metadata for selected docs → `<rig>-b-files.json`  
6. Export files to Markdown (`<rig>-b-files.md`); set `item_download` as needed  
7. Import updated file selections from Markdown  
8. Download marked files to `outputs/downloads/<rig>`, auto-organized via generated subfolder paths

## Directory Structure
```plaintext
outputs/
├── data/         # All metadata/selection (JSON + Markdown)
│   ├── <rig>-a-docs.json
│   ├── <rig>-a-docs.md
│   ├── <rig>-b-files.json
│   └── <rig>-b-files.md
└── downloads/    # All downloaded files, by rig/subfolder structure
    └── <rig>/
        ├── <subfolder>/
        │   └── <item_generated_name>.<ext>
        └── ...
```

## Setup Prerequisites
- Windows OS  
- Python 3.11+  
- Chrome browser (with valid Chromedriver)  
- Valid NOV RigDoc credentials  
- Install dependencies: `pip install -r requirements.txt`

## Execution Run Modes
- `RigOfficeDownloader-v4.bat --auto`  
  *Full pipeline, minimal user intervention*  
- `RigOfficeDownloader-v4.bat --interactive`  
  *Menu-driven, step-by-step workflow*  
- `RigOfficeDownloader-v4.bat --config`  
  *Edit search templates and filters*

## Configuration and Filtering
- Edit filter chains (glob patterns: `*G000*`, `*VOID*`, etc.) for auto-inclusion/exclusion  
- Specify rig number and search URLs in configuration  
- Modify which metadata fields control selection  

## Benefits
- ≥75% reduction in document preparation time  
- Consistent, metadata-driven file organization  
- Centralized user review and selection via Markdown checkpoints  
- Robust error handling, deduplication, and smart automation  
- Scalable, cohesive, and audit-ready architecture for broader adoption  

## Comparison Table

|                 | Manual                 | Automated                             |
|-----------------|------------------------|---------------------------------------|
| **Time per project**    | 4–6 hours              | <1 hour                                |
| **File organization**   | Error-prone, ad hoc    | Metadata-driven, standardized          |
| **User review**         | Tedious and repetitive | Centralized via Markdown checkpoints   |
| **Auditability**        | Weak or absent         | Full JSON/Markdown traceability        |
| **Expandability**       | N/A                    | Configurable, reusable templates       |

## Extensibility
- Built-in support for subfolders, pattern-based filter chains, customizable field ordering, advanced configuration  
- Easily adaptable for new departments or expanded document organization/selection policies

## Philosophy
> "Complexity is the enemy of execution — Optimized for engineers, by engineers."
```
