# Blender Gitignore Setup

Conversation ID: 681ef9a5-5844-8008-b16f-802ba873f8e0

## Message 1

**Crafting the .gitignore**

Piecing together which files and directories to ignore in Blender 4.1's portable ZIP distribution to ensure only non-settings or non-preference files are excluded.

**Ignoring non-settings files**

I’m crafting a .gitignore rule for Blender 4.1 on a portable ZIP, ensuring exclusions of system files, binaries, and libraries, while retaining Python scripts for version control.

**Refining exclusions**

I'm ensuring the .gitignore file for Blender 4.1 excludes system data, while keeping "scripts" and "python" directories intact to track user preferences and modifications.

## Message 2

Below is an example `.gitignore` geared towards **keeping any potential user modifications** (scripts, presets, configuration files) **while ignoring** the bulk of Blender's core executables, libraries, and other files that **never** store user‐level preferences.

> **Note**: This focuses on the common case where Blender’s **`scripts`** folder (and optionally certain config files like `config.ocio`) is where user or team customizations might live. Everything else is typically unmodified “system content” that can be safely ignored.  
> Feel free to adjust which subfolders you choose to keep under version control if you actually do tweak e.g. color‐management files or some of the assets in `datafiles`.

<details>
<summary><strong>.gitignore (example)</strong></summary>

```gitignore
###############################################################################
# 1) Ignore everything by default.
###############################################################################
*

###############################################################################
# 2) Never ignore our .gitignore file itself.
###############################################################################
!.gitignore

###############################################################################
# 3) Keep any top-level text docs or license info if you want them versioned.
#    (Uncomment to keep these, or remove if not needed.)
###############################################################################
#!readme.html
#!copyright.txt
#!license/
#!license/**

###############################################################################
# 4) Keep the main 'scripts' folder and everything in it,
#    since user addons and user scripts/presets can live here.
###############################################################################
!4.1/scripts/
!4.1/scripts/**

###############################################################################
# 5) OPTIONAL: If you tweak color management or colormanagement config:
#    Uncomment these lines to keep them in version control.
###############################################################################
#!4.1/datafiles/colormanagement/
#!4.1/datafiles/colormanagement/config.ocio
#!4.1/datafiles/colormanagement/luts/
#!4.1/datafiles/colormanagement/filmic/

###############################################################################
# 6) OPTIONAL: If you or your team repackage new “studiolights” or “fonts,”
#    unignore them here. By default these are standard system files.
###############################################################################
# !4.1/datafiles/studiolights/
# !4.1/datafiles/fonts/

###############################################################################
# 7) Everything else is ignored: executables, libs, standard Python site-packages,
#    locale translations, etc.
###############################################################################

```
</details>

### How This Works

1. **`*`** at the top ignores everything by default.  
2. **`!.gitignore`** ensures the `.gitignore` file itself is always tracked.  
3. **`!4.1/scripts/`** and `!4.1/scripts/**` un-ignore the entire `scripts` folder (including any subfolder). This is usually where user add-ons, startup scripts, or custom operators may live.  
4. Further lines (commented out as “OPTIONAL”) show how you might keep color management or other `datafiles` content if you actually tweak them. Just remove the `#` if needed.  

In other words, you’re free to tune which system folders are kept under version control depending on where you actually store “settings” or “preferences.” Everything else in the portable Blender build (executables, `.dll`s, standard library files, translations, etc.) is safely ignored.

## Message 3

The following contains all of the files in the portable extracted zip of Blender 4.1, your goal is to write a .gitignore that can be used within this directory but that only ignores files/dirs that won't contain any settings or preferences:



```

├── 4.1

│   ├── datafiles

│   │   ├── assets

│   │   │   ├── geometry_nodes

│   │   │   │   ├── procedural_hair_node_assets.blend

│   │   │   │   └── smooth_by_angle.blend

│   │   │   ├── LICENSE

│   │   │   └── blender_assets.cats.txt

│   │   ├── colormanagement

│   │   │   ├── filmic

│   │   │   │   ├── filmic_desat_33.cube

│   │   │   │   ├── filmic_to_0-35_1-30.spi1d

│   │   │   │   ├── filmic_to_0-48_1-09.spi1d

│   │   │   │   ├── filmic_to_0-60_1-04.spi1d

│   │   │   │   ├── filmic_to_0-70_1-03.spi1d

│   │   │   │   ├── filmic_to_0-85_1-011.spi1d

│   │   │   │   ├── filmic_to_0.99_1-0075.spi1d

│   │   │   │   └── filmic_to_1.20_1-00.spi1d

│   │   │   ├── luts

│   │   │   │   ├── AgX_Base_P3.cube

│   │   │   │   ├── AgX_Base_Rec2020.cube

│   │   │   │   ├── AgX_Base_sRGB.cube

│   │   │   │   ├── AgX_False_Color.spi1d

│   │   │   │   ├── Guard_Rail_Shaper_EOTF.spi1d

│   │   │   │   ├── Inverse_AgX_Base_Rec2020.cube

│   │   │   │   ├── luminance_compensation_bt2020.cube

│   │   │   │   ├── luminance_compensation_p3.cube

│   │   │   │   ├── luminance_compensation_srgb.cube

│   │   │   │   └── xyz_E_to_D65.spimtx

│   │   │   └── config.ocio

│   │   ├── fonts

│   │   │   ├── DejaVuSansMono.woff2

│   │   │   ├── Inter.woff2

│   │   │   ├── Noto Sans CJK Regular.woff2

│   │   │   ├── NotoEmoji-VariableFont_wght.woff2

│   │   │   ├── NotoSansArabic-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansArmenian-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansBengali-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansDevanagari-Regular.woff2

│   │   │   ├── NotoSansEthiopic-Regular.woff2

│   │   │   ├── NotoSansGeorgian-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansGujarati-Regular.woff2

│   │   │   ├── NotoSansGurmukhi-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansHebrew-Regular.woff2

│   │   │   ├── NotoSansJavanese-Regular.woff2

│   │   │   ├── NotoSansKannada-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansKhmer-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansMalayalam-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansMath-Regular.woff2

│   │   │   ├── NotoSansMyanmar-Regular.woff2

│   │   │   ├── NotoSansSymbols-VariableFont_wght.woff2

│   │   │   ├── NotoSansSymbols2-Regular.woff2

│   │   │   ├── NotoSansTamil-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansTelugu-VariableFont_wdth,wght.woff2

│   │   │   ├── NotoSansThai-VariableFont_wdth,wght.woff2

│   │   │   └── lastresort.woff2

│   │   ├── icons

│   │   │   ├── brush.gpencil_draw.draw.dat

│   │   │   ├── brush.gpencil_draw.erase.dat

│   │   │   ├── brush.gpencil_draw.fill.dat

│   │   │   ├── brush.gpencil_draw.tint.dat

│   │   │   ├── brush.paint_texture.airbrush.dat

│   │   │   ├── brush.paint_texture.clone.dat

│   │   │   ├── brush.paint_texture.draw.dat

│   │   │   ├── brush.paint_texture.fill.dat

│   │   │   ├── brush.paint_texture.mask.dat

│   │   │   ├── brush.paint_texture.masklort.dat

│   │   │   ├── brush.paint_texture.multiply.dat

│   │   │   ├── brush.paint_texture.smear.dat

│   │   │   ├── brush.paint_texture.soften.dat

│   │   │   ├── brush.paint_vertex.alpha.dat

│   │   │   ├── brush.paint_vertex.average.dat

│   │   │   ├── brush.paint_vertex.blur.dat

│   │   │   ├── brush.paint_vertex.draw.dat

│   │   │   ├── brush.paint_vertex.replace.dat

│   │   │   ├── brush.paint_vertex.smear.dat

│   │   │   ├── brush.paint_weight.average.dat

│   │   │   ├── brush.paint_weight.blur.dat

│   │   │   ├── brush.paint_weight.draw.dat

│   │   │   ├── brush.paint_weight.mix.dat

│   │   │   ├── brush.paint_weight.smear.dat

│   │   │   ├── brush.particle.add.dat

│   │   │   ├── brush.particle.comb.dat

│   │   │   ├── brush.particle.cut.dat

│   │   │   ├── brush.particle.length.dat

│   │   │   ├── brush.particle.puff.dat

│   │   │   ├── brush.particle.smooth.dat

│   │   │   ├── brush.particle.weight.dat

│   │   │   ├── brush.sculpt.blob.dat

│   │   │   ├── brush.sculpt.boundary.dat

│   │   │   ├── brush.sculpt.clay.dat

│   │   │   ├── brush.sculpt.clay_strips.dat

│   │   │   ├── brush.sculpt.clay_thumb.dat

│   │   │   ├── brush.sculpt.cloth.dat

│   │   │   ├── brush.sculpt.crease.dat

│   │   │   ├── brush.sculpt.displacement_eraser.dat

│   │   │   ├── brush.sculpt.displacement_smear.dat

│   │   │   ├── brush.sculpt.draw.dat

│   │   │   ├── brush.sculpt.draw_face_sets.dat

│   │   │   ├── brush.sculpt.draw_sharp.dat

│   │   │   ├── brush.sculpt.elastic_deform.dat

│   │   │   ├── brush.sculpt.fill.dat

│   │   │   ├── brush.sculpt.flatten.dat

│   │   │   ├── brush.sculpt.grab.dat

│   │   │   ├── brush.sculpt.inflate.dat

│   │   │   ├── brush.sculpt.layer.dat

│   │   │   ├── brush.sculpt.mask.dat

│   │   │   ├── brush.sculpt.multiplane_scrape.dat

│   │   │   ├── brush.sculpt.nudge.dat

│   │   │   ├── brush.sculpt.paint.dat

│   │   │   ├── brush.sculpt.pinch.dat

│   │   │   ├── brush.sculpt.pose.dat

│   │   │   ├── brush.sculpt.rotate.dat

│   │   │   ├── brush.sculpt.scrape.dat

│   │   │   ├── brush.sculpt.simplify.dat

│   │   │   ├── brush.sculpt.smear.dat

│   │   │   ├── brush.sculpt.smooth.dat

│   │   │   ├── brush.sculpt.snake_hook.dat

│   │   │   ├── brush.sculpt.thumb.dat

│   │   │   ├── brush.sculpt.topology.dat

│   │   │   ├── brush.uv_sculpt.grab.dat

│   │   │   ├── brush.uv_sculpt.pinch.dat

│   │   │   ├── brush.uv_sculpt.relax.dat

│   │   │   ├── none.dat

│   │   │   ├── ops.armature.bone.roll.dat

│   │   │   ├── ops.armature.extrude_cursor.dat

│   │   │   ├── ops.armature.extrude_move.dat

│   │   │   ├── ops.curve.draw.dat

│   │   │   ├── ops.curve.extrude_cursor.dat

│   │   │   ├── ops.curve.extrude_move.dat

│   │   │   ├── ops.curve.pen.dat

│   │   │   ├── ops.curve.radius.dat

│   │   │   ├── ops.curve.vertex_random.dat

│   │   │   ├── ops.curves.sculpt_add.dat

│   │   │   ├── ops.curves.sculpt_comb.dat

│   │   │   ├── ops.curves.sculpt_cut.dat

│   │   │   ├── ops.curves.sculpt_delete.dat

│   │   │   ├── ops.curves.sculpt_density.dat

│   │   │   ├── ops.curves.sculpt_grow_shrink.dat

│   │   │   ├── ops.curves.sculpt_pinch.dat

│   │   │   ├── ops.curves.sculpt_puff.dat

│   │   │   ├── ops.curves.sculpt_slide.dat

│   │   │   ├── ops.curves.sculpt_smooth.dat

│   │   │   ├── ops.curves.sculpt_snake_hook.dat

│   │   │   ├── ops.generic.cursor.dat

│   │   │   ├── ops.generic.select.dat

│   │   │   ├── ops.generic.select_box.dat

│   │   │   ├── ops.generic.select_circle.dat

│   │   │   ├── ops.generic.select_lasso.dat

│   │   │   ├── ops.generic.select_paint.dat

│   │   │   ├── ops.gpencil.draw.dat

│   │   │   ├── ops.gpencil.draw.eraser.dat

│   │   │   ├── ops.gpencil.draw.line.dat

│   │   │   ├── ops.gpencil.draw.poly.dat

│   │   │   ├── ops.gpencil.edit_bend.dat

│   │   │   ├── ops.gpencil.edit_mirror.dat

│   │   │   ├── ops.gpencil.edit_shear.dat

│   │   │   ├── ops.gpencil.edit_to_sphere.dat

│   │   │   ├── ops.gpencil.extrude_move.dat

│   │   │   ├── ops.gpencil.primitive_arc.dat

│   │   │   ├── ops.gpencil.primitive_box.dat

│   │   │   ├── ops.gpencil.primitive_circle.dat

│   │   │   ├── ops.gpencil.primitive_curve.dat

│   │   │   ├── ops.gpencil.primitive_line.dat

│   │   │   ├── ops.gpencil.primitive_polyline.dat

│   │   │   ├── ops.gpencil.radius.dat

│   │   │   ├── ops.gpencil.sculpt_average.dat

│   │   │   ├── ops.gpencil.sculpt_blur.dat

│   │   │   ├── ops.gpencil.sculpt_clone.dat

│   │   │   ├── ops.gpencil.sculpt_grab.dat

│   │   │   ├── ops.gpencil.sculpt_pinch.dat

│   │   │   ├── ops.gpencil.sculpt_push.dat

│   │   │   ├── ops.gpencil.sculpt_randomize.dat

│   │   │   ├── ops.gpencil.sculpt_smear.dat

│   │   │   ├── ops.gpencil.sculpt_smooth.dat

│   │   │   ├── ops.gpencil.sculpt_strength.dat

│   │   │   ├── ops.gpencil.sculpt_thickness.dat

│   │   │   ├── ops.gpencil.sculpt_twist.dat

│   │   │   ├── ops.gpencil.sculpt_weight.dat

│   │   │   ├── ops.gpencil.stroke_cutter.dat

│   │   │   ├── ops.gpencil.transform_fill.dat

│   │   │   ├── ops.mesh.bevel.dat

│   │   │   ├── ops.mesh.bisect.dat

│   │   │   ├── ops.mesh.dupli_extrude_cursor.dat

│   │   │   ├── ops.mesh.extrude_faces_move.dat

│   │   │   ├── ops.mesh.extrude_manifold.dat

│   │   │   ├── ops.mesh.extrude_region_move.dat

│   │   │   ├── ops.mesh.extrude_region_shrink_fatten.dat

│   │   │   ├── ops.mesh.inset.dat

│   │   │   ├── ops.mesh.knife_tool.dat

│   │   │   ├── ops.mesh.loopcut_slide.dat

│   │   │   ├── ops.mesh.offset_edge_loops_slide.dat

│   │   │   ├── ops.mesh.polybuild_hover.dat

│   │   │   ├── ops.mesh.primitive_cone_add_gizmo.dat

│   │   │   ├── ops.mesh.primitive_cube_add_gizmo.dat

│   │   │   ├── ops.mesh.primitive_cylinder_add_gizmo.dat

│   │   │   ├── ops.mesh.primitive_grid_add_gizmo.dat

│   │   │   ├── ops.mesh.primitive_sphere_add_gizmo.dat

│   │   │   ├── ops.mesh.primitive_torus_add_gizmo.dat

│   │   │   ├── ops.mesh.rip.dat

│   │   │   ├── ops.mesh.rip_edge.dat

│   │   │   ├── ops.mesh.spin.dat

│   │   │   ├── ops.mesh.vertices_smooth.dat

│   │   │   ├── ops.node.links_cut.dat

│   │   │   ├── ops.paint.eyedropper_add.dat

│   │   │   ├── ops.paint.vertex_color_fill.dat

│   │   │   ├── ops.paint.weight_fill.dat

│   │   │   ├── ops.paint.weight_gradient.dat

│   │   │   ├── ops.paint.weight_sample.dat

│   │   │   ├── ops.paint.weight_sample_group.dat

│   │   │   ├── ops.pose.breakdowner.dat

│   │   │   ├── ops.pose.push.dat

│   │   │   ├── ops.pose.relax.dat

│   │   │   ├── ops.sculpt.border_face_set.dat

│   │   │   ├── ops.sculpt.border_hide.dat

│   │   │   ├── ops.sculpt.border_mask.dat

│   │   │   ├── ops.sculpt.box_trim.dat

│   │   │   ├── ops.sculpt.cloth_filter.dat

│   │   │   ├── ops.sculpt.color_filter.dat

│   │   │   ├── ops.sculpt.face_set_edit.dat

│   │   │   ├── ops.sculpt.lasso_face_set.dat

│   │   │   ├── ops.sculpt.lasso_mask.dat

│   │   │   ├── ops.sculpt.lasso_trim.dat

│   │   │   ├── ops.sculpt.line_mask.dat

│   │   │   ├── ops.sculpt.line_project.dat

│   │   │   ├── ops.sculpt.mask_by_color.dat

│   │   │   ├── ops.sculpt.mesh_filter.dat

│   │   │   ├── ops.sequencer.blade.dat

│   │   │   ├── ops.sequencer.retime.dat

│   │   │   ├── ops.transform.bone_envelope.dat

│   │   │   ├── ops.transform.bone_size.dat

│   │   │   ├── ops.transform.edge_slide.dat

│   │   │   ├── ops.transform.push_pull.dat

│   │   │   ├── ops.transform.resize.cage.dat

│   │   │   ├── ops.transform.resize.dat

│   │   │   ├── ops.transform.rotate.dat

│   │   │   ├── ops.transform.shear.dat

│   │   │   ├── ops.transform.shrink_fatten.dat

│   │   │   ├── ops.transform.tilt.dat

│   │   │   ├── ops.transform.tosphere.dat

│   │   │   ├── ops.transform.transform.dat

│   │   │   ├── ops.transform.translate.dat

│   │   │   ├── ops.transform.vert_slide.dat

│   │   │   ├── ops.transform.vertex_random.dat

│   │   │   └── ops.view3d.ruler.dat

│   │   ├── locale

│   │   │   ├── ab

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ar

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── be

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── bg

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ca

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── cs

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── da

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── de

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── el

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── eo

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── es

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── eu

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── fa

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── fi

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── fr

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ha

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── he

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── hi

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── hr

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── hu

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── id

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── it

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ja

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ka

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── km

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ko

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ky

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ne

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── nl

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── pl

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── pt

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── pt_BR

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ru

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── sk

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── sr

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── sr@latin

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── sv

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── sw

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── ta

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── th

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── tr

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── uk

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── vi

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── zh_HANS

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   ├── zh_HANT

│   │   │   │   └── LC_MESSAGES

│   │   │   │       └── blender.mo

│   │   │   └── languages

│   │   └── studiolights

│   │       ├── matcap

│   │       │   ├── basic_1.exr

│   │       │   ├── basic_2.exr

│   │       │   ├── basic_dark.exr

│   │       │   ├── basic_side.exr

│   │       │   ├── ceramic_dark.exr

│   │       │   ├── ceramic_lightbulb.exr

│   │       │   ├── check_normal+y.exr

│   │       │   ├── check_rim_dark.exr

│   │       │   ├── check_rim_light.exr

│   │       │   ├── clay_brown.exr

│   │       │   ├── clay_muddy.exr

│   │       │   ├── clay_studio.exr

│   │       │   ├── jade.exr

│   │       │   ├── license.txt

│   │       │   ├── metal_anisotropic.exr

│   │       │   ├── metal_carpaint.exr

│   │       │   ├── metal_lead.exr

│   │       │   ├── metal_shiny.exr

│   │       │   ├── pearl.exr

│   │       │   ├── reflection_check_horizontal.exr

│   │       │   ├── reflection_check_vertical.exr

│   │       │   ├── resin.exr

│   │       │   ├── skin.exr

│   │       │   └── toon.exr

│   │       ├── studio

│   │       │   ├── basic.sl

│   │       │   ├── outdoor.sl

│   │       │   ├── paint.sl

│   │       │   ├── rim.sl

│   │       │   └── studio.sl

│   │       └── world

│   │           ├── city.exr

│   │           ├── courtyard.exr

│   │           ├── forest.exr

│   │           ├── interior.exr

│   │           ├── license.txt

│   │           ├── night.exr

│   │           ├── studio.exr

│   │           ├── sunrise.exr

│   │           └── sunset.exr

│   ├── python

│   │   ├── DLLs

│   │   │   ├── _asyncio.pyd

│   │   │   ├── _bz2.pyd

│   │   │   ├── _ctypes.pyd

│   │   │   ├── _decimal.pyd

│   │   │   ├── _elementtree.pyd

│   │   │   ├── _hashlib.pyd

│   │   │   ├── _lzma.pyd

│   │   │   ├── _msi.pyd

│   │   │   ├── _multiprocessing.pyd

│   │   │   ├── _overlapped.pyd

│   │   │   ├── _queue.pyd

│   │   │   ├── _socket.pyd

│   │   │   ├── _sqlite3.pyd

│   │   │   ├── _ssl.pyd

│   │   │   ├── _uuid.pyd

│   │   │   ├── _zoneinfo.pyd

│   │   │   ├── libcrypto-3.dll

│   │   │   ├── libffi-8.dll

│   │   │   ├── libssl-3.dll

│   │   │   ├── pyexpat.pyd

│   │   │   ├── select.pyd

│   │   │   ├── sqlite3.dll

│   │   │   ├── unicodedata.pyd

│   │   │   └── winsound.pyd

│   │   ├── bin

│   │   │   ├── python.exe

│   │   │   └── python311.dll

│   │   └── lib

│   │       ├── __phello__

│   │       │   ├── ham

│   │       │   │   ├── __init__.py

│   │       │   │   └── eggs.py

│   │       │   ├── __init__.py

│   │       │   └── spam.py

│   │       ├── asyncio

│   │       │   ├── __init__.py

│   │       │   ├── __main__.py

│   │       │   ├── base_events.py

│   │       │   ├── base_futures.py

│   │       │   ├── base_subprocess.py

│   │       │   ├── base_tasks.py

│   │       │   ├── constants.py

│   │       │   ├── coroutines.py

│   │       │   ├── events.py

│   │       │   ├── exceptions.py

│   │       │   ├── format_helpers.py

│   │       │   ├── futures.py

│   │       │   ├── locks.py

│   │       │   ├── log.py

│   │       │   ├── mixins.py

│   │       │   ├── proactor_events.py

│   │       │   ├── protocols.py

│   │       │   ├── queues.py

│   │       │   ├── runners.py

│   │       │   ├── selector_events.py

│   │       │   ├── sslproto.py

│   │       │   ├── staggered.py

│   │       │   ├── streams.py

│   │       │   ├── subprocess.py

│   │       │   ├── taskgroups.py

│   │       │   ├── tasks.py

│   │       │   ├── threads.py

│   │       │   ├── timeouts.py

│   │       │   ├── transports.py

│   │       │   ├── trsock.py

│   │       │   ├── unix_events.py

│   │       │   ├── windows_events.py

│   │       │   └── windows_utils.py

│   │       ├── collections

│   │       │   ├── __init__.py

│   │       │   └── abc.py

│   │       ├── concurrent

│   │       │   ├── futures

│   │       │   │   ├── __init__.py

│   │       │   │   ├── _base.py

│   │       │   │   ├── process.py

│   │       │   │   └── thread.py

│   │       │   └── __init__.py

│   │       ├── ctypes

│   │       │   ├── macholib

│   │       │   │   ├── README.ctypes

│   │       │   │   ├── __init__.py

│   │       │   │   ├── dyld.py

│   │       │   │   ├── dylib.py

│   │       │   │   ├── fetch_macholib

│   │       │   │   ├── fetch_macholib.bat

│   │       │   │   └── framework.py

│   │       │   ├── __init__.py

│   │       │   ├── _aix.py

│   │       │   ├── _endian.py

│   │       │   ├── util.py

│   │       │   └── wintypes.py

│   │       ├── curses

│   │       │   ├── __init__.py

│   │       │   ├── ascii.py

│   │       │   ├── has_key.py

│   │       │   ├── panel.py

│   │       │   └── textpad.py

│   │       ├── dbm

│   │       │   ├── __init__.py

│   │       │   ├── dumb.py

│   │       │   ├── gnu.py

│   │       │   └── ndbm.py

│   │       ├── distutils

│   │       │   ├── command

│   │       │   │   ├── __init__.py

│   │       │   │   ├── bdist.py

│   │       │   │   ├── bdist_dumb.py

│   │       │   │   ├── bdist_rpm.py

│   │       │   │   ├── build.py

│   │       │   │   ├── build_clib.py

│   │       │   │   ├── build_ext.py

│   │       │   │   ├── build_py.py

│   │       │   │   ├── build_scripts.py

│   │       │   │   ├── check.py

│   │       │   │   ├── clean.py

│   │       │   │   ├── command_template

│   │       │   │   ├── config.py

│   │       │   │   ├── install.py

│   │       │   │   ├── install_data.py

│   │       │   │   ├── install_egg_info.py

│   │       │   │   ├── install_headers.py

│   │       │   │   ├── install_lib.py

│   │       │   │   ├── install_scripts.py

│   │       │   │   ├── register.py

│   │       │   │   ├── sdist.py

│   │       │   │   └── upload.py

│   │       │   ├── README

│   │       │   ├── __init__.py

│   │       │   ├── _msvccompiler.py

│   │       │   ├── archive_util.py

│   │       │   ├── bcppcompiler.py

│   │       │   ├── ccompiler.py

│   │       │   ├── cmd.py

│   │       │   ├── config.py

│   │       │   ├── core.py

│   │       │   ├── cygwinccompiler.py

│   │       │   ├── debug.py

│   │       │   ├── dep_util.py

│   │       │   ├── dir_util.py

│   │       │   ├── dist.py

│   │       │   ├── errors.py

│   │       │   ├── extension.py

│   │       │   ├── fancy_getopt.py

│   │       │   ├── file_util.py

│   │       │   ├── filelist.py

│   │       │   ├── log.py

│   │       │   ├── msvc9compiler.py

│   │       │   ├── msvccompiler.py

│   │       │   ├── spawn.py

│   │       │   ├── sysconfig.py

│   │       │   ├── text_file.py

│   │       │   ├── unixccompiler.py

│   │       │   ├── util.py

│   │       │   ├── version.py

│   │       │   └── versionpredicate.py

│   │       ├── email

│   │       │   ├── mime

│   │       │   │   ├── __init__.py

│   │       │   │   ├── application.py

│   │       │   │   ├── audio.py

│   │       │   │   ├── base.py

│   │       │   │   ├── image.py

│   │       │   │   ├── message.py

│   │       │   │   ├── multipart.py

│   │       │   │   ├── nonmultipart.py

│   │       │   │   └── text.py

│   │       │   ├── __init__.py

│   │       │   ├── _encoded_words.py

│   │       │   ├── _header_value_parser.py

│   │       │   ├── _parseaddr.py

│   │       │   ├── _policybase.py

│   │       │   ├── architecture.rst

│   │       │   ├── base64mime.py

│   │       │   ├── charset.py

│   │       │   ├── contentmanager.py

│   │       │   ├── encoders.py

│   │       │   ├── errors.py

│   │       │   ├── feedparser.py

│   │       │   ├── generator.py

│   │       │   ├── header.py

│   │       │   ├── headerregistry.py

│   │       │   ├── iterators.py

│   │       │   ├── message.py

│   │       │   ├── parser.py

│   │       │   ├── policy.py

│   │       │   ├── quoprimime.py

│   │       │   └── utils.py

│   │       ├── encodings

│   │       │   ├── __init__.py

│   │       │   ├── aliases.py

│   │       │   ├── ascii.py

│   │       │   ├── base64_codec.py

│   │       │   ├── big5.py

│   │       │   ├── big5hkscs.py

│   │       │   ├── bz2_codec.py

│   │       │   ├── charmap.py

│   │       │   ├── cp037.py

│   │       │   ├── cp1006.py

│   │       │   ├── cp1026.py

│   │       │   ├── cp1125.py

│   │       │   ├── cp1140.py

│   │       │   ├── cp1250.py

│   │       │   ├── cp1251.py

│   │       │   ├── cp1252.py

│   │       │   ├── cp1253.py

│   │       │   ├── cp1254.py

│   │       │   ├── cp1255.py

│   │       │   ├── cp1256.py

│   │       │   ├── cp1257.py

│   │       │   ├── cp1258.py

│   │       │   ├── cp273.py

│   │       │   ├── cp424.py

│   │       │   ├── cp437.py

│   │       │   ├── cp500.py

│   │       │   ├── cp720.py

│   │       │   ├── cp737.py

│   │       │   ├── cp775.py

│   │       │   ├── cp850.py

│   │       │   ├── cp852.py

│   │       │   ├── cp855.py

│   │       │   ├── cp856.py

│   │       │   ├── cp857.py

│   │       │   ├── cp858.py

│   │       │   ├── cp860.py

│   │       │   ├── cp861.py

│   │       │   ├── cp862.py

│   │       │   ├── cp863.py

│   │       │   ├── cp864.py

│   │       │   ├── cp865.py

│   │       │   ├── cp866.py

│   │       │   ├── cp869.py

│   │       │   ├── cp874.py

│   │       │   ├── cp875.py

│   │       │   ├── cp932.py

│   │       │   ├── cp949.py

│   │       │   ├── cp950.py

│   │       │   ├── euc_jis_2004.py

│   │       │   ├── euc_jisx0213.py

│   │       │   ├── euc_jp.py

│   │       │   ├── euc_kr.py

│   │       │   ├── gb18030.py

│   │       │   ├── gb2312.py

│   │       │   ├── gbk.py

│   │       │   ├── hex_codec.py

│   │       │   ├── hp_roman8.py

│   │       │   ├── hz.py

│   │       │   ├── idna.py

│   │       │   ├── iso2022_jp.py

│   │       │   ├── iso2022_jp_1.py

│   │       │   ├── iso2022_jp_2.py

│   │       │   ├── iso2022_jp_2004.py

│   │       │   ├── iso2022_jp_3.py

│   │       │   ├── iso2022_jp_ext.py

│   │       │   ├── iso2022_kr.py

│   │       │   ├── iso8859_1.py

│   │       │   ├── iso8859_10.py

│   │       │   ├── iso8859_11.py

│   │       │   ├── iso8859_13.py

│   │       │   ├── iso8859_14.py

│   │       │   ├── iso8859_15.py

│   │       │   ├── iso8859_16.py

│   │       │   ├── iso8859_2.py

│   │       │   ├── iso8859_3.py

│   │       │   ├── iso8859_4.py

│   │       │   ├── iso8859_5.py

│   │       │   ├── iso8859_6.py

│   │       │   ├── iso8859_7.py

│   │       │   ├── iso8859_8.py

│   │       │   ├── iso8859_9.py

│   │       │   ├── johab.py

│   │       │   ├── koi8_r.py

│   │       │   ├── koi8_t.py

│   │       │   ├── koi8_u.py

│   │       │   ├── kz1048.py

│   │       │   ├── latin_1.py

│   │       │   ├── mac_arabic.py

│   │       │   ├── mac_croatian.py

│   │       │   ├── mac_cyrillic.py

│   │       │   ├── mac_farsi.py

│   │       │   ├── mac_greek.py

│   │       │   ├── mac_iceland.py

│   │       │   ├── mac_latin2.py

│   │       │   ├── mac_roman.py

│   │       │   ├── mac_romanian.py

│   │       │   ├── mac_turkish.py

│   │       │   ├── mbcs.py

│   │       │   ├── oem.py

│   │       │   ├── palmos.py

│   │       │   ├── ptcp154.py

│   │       │   ├── punycode.py

│   │       │   ├── quopri_codec.py

│   │       │   ├── raw_unicode_escape.py

│   │       │   ├── rot_13.py

│   │       │   ├── shift_jis.py

│   │       │   ├── shift_jis_2004.py

│   │       │   ├── shift_jisx0213.py

│   │       │   ├── tis_620.py

│   │       │   ├── undefined.py

│   │       │   ├── unicode_escape.py

│   │       │   ├── utf_16.py

│   │       │   ├── utf_16_be.py

│   │       │   ├── utf_16_le.py

│   │       │   ├── utf_32.py

│   │       │   ├── utf_32_be.py

│   │       │   ├── utf_32_le.py

│   │       │   ├── utf_7.py

│   │       │   ├── utf_8.py

│   │       │   ├── utf_8_sig.py

│   │       │   ├── uu_codec.py

│   │       │   └── zlib_codec.py

│   │       ├── ensurepip

│   │       │   ├── _bundled

│   │       │   │   ├── pip-23.2.1-py3-none-any.whl

│   │       │   │   └── setuptools-65.5.0-py3-none-any.whl

│   │       │   ├── __init__.py

│   │       │   ├── __main__.py

│   │       │   └── _uninstall.py

│   │       ├── html

│   │       │   ├── __init__.py

│   │       │   ├── entities.py

│   │       │   └── parser.py

│   │       ├── http

│   │       │   ├── __init__.py

│   │       │   ├── client.py

│   │       │   ├── cookiejar.py

│   │       │   ├── cookies.py

│   │       │   └── server.py

│   │       ├── importlib

│   │       │   ├── metadata

│   │       │   │   ├── __init__.py

│   │       │   │   ├── _adapters.py

│   │       │   │   ├── _collections.py

│   │       │   │   ├── _functools.py

│   │       │   │   ├── _itertools.py

│   │       │   │   ├── _meta.py

│   │       │   │   └── _text.py

│   │       │   ├── resources

│   │       │   │   ├── __init__.py

│   │       │   │   ├── _adapters.py

│   │       │   │   ├── _common.py

│   │       │   │   ├── _itertools.py

│   │       │   │   ├── _legacy.py

│   │       │   │   ├── abc.py

│   │       │   │   ├── readers.py

│   │       │   │   └── simple.py

│   │       │   ├── __init__.py

│   │       │   ├── _abc.py

│   │       │   ├── _bootstrap.py

│   │       │   ├── _bootstrap_external.py

│   │       │   ├── abc.py

│   │       │   ├── machinery.py

│   │       │   ├── readers.py

│   │       │   ├── simple.py

│   │       │   └── util.py

│   │       ├── json

│   │       │   ├── __init__.py

│   │       │   ├── decoder.py

│   │       │   ├── encoder.py

│   │       │   ├── scanner.py

│   │       │   └── tool.py

│   │       ├── lib2to3

│   │       │   ├── fixes

│   │       │   │   ├── __init__.py

│   │       │   │   ├── fix_apply.py

│   │       │   │   ├── fix_asserts.py

│   │       │   │   ├── fix_basestring.py

│   │       │   │   ├── fix_buffer.py

│   │       │   │   ├── fix_dict.py

│   │       │   │   ├── fix_except.py

│   │       │   │   ├── fix_exec.py

│   │       │   │   ├── fix_execfile.py

│   │       │   │   ├── fix_exitfunc.py

│   │       │   │   ├── fix_filter.py

│   │       │   │   ├── fix_funcattrs.py

│   │       │   │   ├── fix_future.py

│   │       │   │   ├── fix_getcwdu.py

│   │       │   │   ├── fix_has_key.py

│   │       │   │   ├── fix_idioms.py

│   │       │   │   ├── fix_import.py

│   │       │   │   ├── fix_imports.py

│   │       │   │   ├── fix_imports2.py

│   │       │   │   ├── fix_input.py

│   │       │   │   ├── fix_intern.py

│   │       │   │   ├── fix_isinstance.py

│   │       │   │   ├── fix_itertools.py

│   │       │   │   ├── fix_itertools_imports.py

│   │       │   │   ├── fix_long.py

│   │       │   │   ├── fix_map.py

│   │       │   │   ├── fix_metaclass.py

│   │       │   │   ├── fix_methodattrs.py

│   │       │   │   ├── fix_ne.py

│   │       │   │   ├── fix_next.py

│   │       │   │   ├── fix_nonzero.py

│   │       │   │   ├── fix_numliterals.py

│   │       │   │   ├── fix_operator.py

│   │       │   │   ├── fix_paren.py

│   │       │   │   ├── fix_print.py

│   │       │   │   ├── fix_raise.py

│   │       │   │   ├── fix_raw_input.py

│   │       │   │   ├── fix_reduce.py

│   │       │   │   ├── fix_reload.py

│   │       │   │   ├── fix_renames.py

│   │       │   │   ├── fix_repr.py

│   │       │   │   ├── fix_set_literal.py

│   │       │   │   ├── fix_standarderror.py

│   │       │   │   ├── fix_sys_exc.py

│   │       │   │   ├── fix_throw.py

│   │       │   │   ├── fix_tuple_params.py

│   │       │   │   ├── fix_types.py

│   │       │   │   ├── fix_unicode.py

│   │       │   │   ├── fix_urllib.py

│   │       │   │   ├── fix_ws_comma.py

│   │       │   │   ├── fix_xrange.py

│   │       │   │   ├── fix_xreadlines.py

│   │       │   │   └── fix_zip.py

│   │       │   ├── pgen2

│   │       │   │   ├── __init__.py

│   │       │   │   ├── conv.py

│   │       │   │   ├── driver.py

│   │       │   │   ├── grammar.py

│   │       │   │   ├── literals.py

│   │       │   │   ├── parse.py

│   │       │   │   ├── pgen.py

│   │       │   │   ├── token.py

│   │       │   │   └── tokenize.py

│   │       │   ├── Grammar.txt

│   │       │   ├── PatternGrammar.txt

│   │       │   ├── __init__.py

│   │       │   ├── __main__.py

│   │       │   ├── btm_matcher.py

│   │       │   ├── btm_utils.py

│   │       │   ├── fixer_base.py

│   │       │   ├── fixer_util.py

│   │       │   ├── main.py

│   │       │   ├── patcomp.py

│   │       │   ├── pygram.py

│   │       │   ├── pytree.py

│   │       │   └── refactor.py

│   │       ├── logging

│   │       │   ├── __init__.py

│   │       │   ├── config.py

│   │       │   └── handlers.py

│   │       ├── msilib

│   │       │   ├── __init__.py

│   │       │   ├── schema.py

│   │       │   ├── sequence.py

│   │       │   └── text.py

│   │       ├── multiprocessing

│   │       │   ├── dummy

│   │       │   │   ├── __init__.py

│   │       │   │   └── connection.py

│   │       │   ├── __init__.py

│   │       │   ├── connection.py

│   │       │   ├── context.py

│   │       │   ├── forkserver.py

│   │       │   ├── heap.py

│   │       │   ├── managers.py

│   │       │   ├── pool.py

│   │       │   ├── popen_fork.py

│   │       │   ├── popen_forkserver.py

│   │       │   ├── popen_spawn_posix.py

│   │       │   ├── popen_spawn_win32.py

│   │       │   ├── process.py

│   │       │   ├── queues.py

│   │       │   ├── reduction.py

│   │       │   ├── resource_sharer.py

│   │       │   ├── resource_tracker.py

│   │       │   ├── shared_memory.py

│   │       │   ├── sharedctypes.py

│   │       │   ├── spawn.py

│   │       │   ├── synchronize.py

│   │       │   └── util.py

│   │       ├── pydoc_data

│   │       │   ├── __init__.py

│   │       │   ├── _pydoc.css

│   │       │   └── topics.py

│   │       ├── re

│   │       │   ├── __init__.py

│   │       │   ├── _casefix.py

│   │       │   ├── _compiler.py

│   │       │   ├── _constants.py

│   │       │   └── _parser.py

│   │       ├── site-packages

│   │       │   ├── Cython

│   │       │   │   ├── Build

│   │       │   │   │   ├── Tests

│   │       │   │   │   │   ├── TestCyCache.py

│   │       │   │   │   │   ├── TestInline.py

│   │       │   │   │   │   ├── TestIpythonMagic.py

│   │       │   │   │   │   ├── TestStripLiterals.py

│   │       │   │   │   │   └── __init__.py

│   │       │   │   │   ├── BuildExecutable.py

│   │       │   │   │   ├── Cythonize.py

│   │       │   │   │   ├── Dependencies.py

│   │       │   │   │   ├── Distutils.py

│   │       │   │   │   ├── Inline.py

│   │       │   │   │   ├── IpythonMagic.py

│   │       │   │   │   └── __init__.py

│   │       │   │   ├── Compiler

│   │       │   │   │   ├── Tests

│   │       │   │   │   │   ├── TestBuffer.py

│   │       │   │   │   │   ├── TestCmdLine.py

│   │       │   │   │   │   ├── TestFlowControl.py

│   │       │   │   │   │   ├── TestGrammar.py

│   │       │   │   │   │   ├── TestMemView.py

│   │       │   │   │   │   ├── TestParseTreeTransforms.py

│   │       │   │   │   │   ├── TestSignatureMatching.py

│   │       │   │   │   │   ├── TestStringEncoding.py

│   │       │   │   │   │   ├── TestTreeFragment.py

│   │       │   │   │   │   ├── TestTreePath.py

│   │       │   │   │   │   ├── TestTypes.py

│   │       │   │   │   │   ├── TestUtilityLoad.py

│   │       │   │   │   │   ├── TestVisitor.py

│   │       │   │   │   │   └── __init__.py

│   │       │   │   │   ├── AnalysedTreeTransforms.py

│   │       │   │   │   ├── Annotate.py

│   │       │   │   │   ├── AutoDocTransforms.py

│   │       │   │   │   ├── Buffer.py

│   │       │   │   │   ├── Builtin.py

│   │       │   │   │   ├── CmdLine.py

│   │       │   │   │   ├── Code.pxd

│   │       │   │   │   ├── Code.py

│   │       │   │   │   ├── CodeGeneration.py

│   │       │   │   │   ├── CythonScope.py

│   │       │   │   │   ├── DebugFlags.py

│   │       │   │   │   ├── Errors.py

│   │       │   │   │   ├── ExprNodes.py

│   │       │   │   │   ├── FlowControl.cp311-win_amd64.pyd

│   │       │   │   │   ├── FlowControl.pxd

│   │       │   │   │   ├── FlowControl.py

│   │       │   │   │   ├── FusedNode.cp311-win_amd64.pyd

│   │       │   │   │   ├── FusedNode.py

│   │       │   │   │   ├── Future.py

│   │       │   │   │   ├── Interpreter.py

│   │       │   │   │   ├── Lexicon.py

│   │       │   │   │   ├── Main.py

│   │       │   │   │   ├── MemoryView.py

│   │       │   │   │   ├── ModuleNode.py

│   │       │   │   │   ├── Naming.py

│   │       │   │   │   ├── Nodes.py

│   │       │   │   │   ├── Optimize.py

│   │       │   │   │   ├── Options.py

│   │       │   │   │   ├── ParseTreeTransforms.pxd

│   │       │   │   │   ├── ParseTreeTransforms.py

│   │       │   │   │   ├── Parsing.pxd

│   │       │   │   │   ├── Parsing.py

│   │       │   │   │   ├── Pipeline.py

│   │       │   │   │   ├── PyrexTypes.py

│   │       │   │   │   ├── Pythran.py

│   │       │   │   │   ├── Scanning.cp311-win_amd64.pyd

│   │       │   │   │   ├── Scanning.pxd

│   │       │   │   │   ├── Scanning.py

│   │       │   │   │   ├── StringEncoding.py

│   │       │   │   │   ├── Symtab.py

│   │       │   │   │   ├── TreeFragment.py

│   │       │   │   │   ├── TreePath.py

│   │       │   │   │   ├── TypeInference.py

│   │       │   │   │   ├── TypeSlots.py

│   │       │   │   │   ├── UtilNodes.py

│   │       │   │   │   ├── UtilityCode.py

│   │       │   │   │   ├── Version.py

│   │       │   │   │   ├── Visitor.cp311-win_amd64.pyd

│   │       │   │   │   ├── Visitor.pxd

│   │       │   │   │   ├── Visitor.py

│   │       │   │   │   └── __init__.py

│   │       │   │   ├── Debugger

│   │       │   │   │   ├── Tests

│   │       │   │   │   │   ├── TestLibCython.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── cfuncs.c

│   │       │   │   │   │   ├── codefile

│   │       │   │   │   │   ├── test_libcython_in_gdb.py

│   │       │   │   │   │   └── test_libpython_in_gdb.py

│   │       │   │   │   ├── Cygdb.py

│   │       │   │   │   ├── DebugWriter.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── libcython.py

│   │       │   │   │   └── libpython.py

│   │       │   │   ├── Distutils

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── build_ext.py

│   │       │   │   │   ├── extension.py

│   │       │   │   │   └── old_build_ext.py

│   │       │   │   ├── Includes

│   │       │   │   │   ├── Deprecated

│   │       │   │   │   │   ├── python.pxd

│   │       │   │   │   │   ├── python_bool.pxd

│   │       │   │   │   │   ├── python_buffer.pxd

│   │       │   │   │   │   ├── python_bytes.pxd

│   │       │   │   │   │   ├── python_cobject.pxd

│   │       │   │   │   │   ├── python_complex.pxd

│   │       │   │   │   │   ├── python_dict.pxd

│   │       │   │   │   │   ├── python_exc.pxd

│   │       │   │   │   │   ├── python_float.pxd

│   │       │   │   │   │   ├── python_function.pxd

│   │       │   │   │   │   ├── python_getargs.pxd

│   │       │   │   │   │   ├── python_instance.pxd

│   │       │   │   │   │   ├── python_int.pxd

│   │       │   │   │   │   ├── python_iterator.pxd

│   │       │   │   │   │   ├── python_list.pxd

│   │       │   │   │   │   ├── python_long.pxd

│   │       │   │   │   │   ├── python_mapping.pxd

│   │       │   │   │   │   ├── python_mem.pxd

│   │       │   │   │   │   ├── python_method.pxd

│   │       │   │   │   │   ├── python_module.pxd

│   │       │   │   │   │   ├── python_number.pxd

│   │       │   │   │   │   ├── python_object.pxd

│   │       │   │   │   │   ├── python_oldbuffer.pxd

│   │       │   │   │   │   ├── python_pycapsule.pxd

│   │       │   │   │   │   ├── python_ref.pxd

│   │       │   │   │   │   ├── python_sequence.pxd

│   │       │   │   │   │   ├── python_set.pxd

│   │       │   │   │   │   ├── python_string.pxd

│   │       │   │   │   │   ├── python_tuple.pxd

│   │       │   │   │   │   ├── python_type.pxd

│   │       │   │   │   │   ├── python_unicode.pxd

│   │       │   │   │   │   ├── python_version.pxd

│   │       │   │   │   │   ├── python_weakref.pxd

│   │       │   │   │   │   ├── stdio.pxd

│   │       │   │   │   │   ├── stdlib.pxd

│   │       │   │   │   │   └── stl.pxd

│   │       │   │   │   ├── cpython

│   │       │   │   │   │   ├── __init__.pxd

│   │       │   │   │   │   ├── array.pxd

│   │       │   │   │   │   ├── bool.pxd

│   │       │   │   │   │   ├── buffer.pxd

│   │       │   │   │   │   ├── bytearray.pxd

│   │       │   │   │   │   ├── bytes.pxd

│   │       │   │   │   │   ├── cellobject.pxd

│   │       │   │   │   │   ├── ceval.pxd

│   │       │   │   │   │   ├── cobject.pxd

│   │       │   │   │   │   ├── codecs.pxd

│   │       │   │   │   │   ├── complex.pxd

│   │       │   │   │   │   ├── conversion.pxd

│   │       │   │   │   │   ├── datetime.pxd

│   │       │   │   │   │   ├── dict.pxd

│   │       │   │   │   │   ├── exc.pxd

│   │       │   │   │   │   ├── float.pxd

│   │       │   │   │   │   ├── function.pxd

│   │       │   │   │   │   ├── genobject.pxd

│   │       │   │   │   │   ├── getargs.pxd

│   │       │   │   │   │   ├── instance.pxd

│   │       │   │   │   │   ├── int.pxd

│   │       │   │   │   │   ├── iterator.pxd

│   │       │   │   │   │   ├── iterobject.pxd

│   │       │   │   │   │   ├── list.pxd

│   │       │   │   │   │   ├── long.pxd

│   │       │   │   │   │   ├── longintrepr.pxd

│   │       │   │   │   │   ├── mapping.pxd

│   │       │   │   │   │   ├── mem.pxd

│   │       │   │   │   │   ├── memoryview.pxd

│   │       │   │   │   │   ├── method.pxd

│   │       │   │   │   │   ├── module.pxd

│   │       │   │   │   │   ├── number.pxd

│   │       │   │   │   │   ├── object.pxd

│   │       │   │   │   │   ├── oldbuffer.pxd

│   │       │   │   │   │   ├── pycapsule.pxd

│   │       │   │   │   │   ├── pylifecycle.pxd

│   │       │   │   │   │   ├── pystate.pxd

│   │       │   │   │   │   ├── pythread.pxd

│   │       │   │   │   │   ├── ref.pxd

│   │       │   │   │   │   ├── sequence.pxd

│   │       │   │   │   │   ├── set.pxd

│   │       │   │   │   │   ├── slice.pxd

│   │       │   │   │   │   ├── string.pxd

│   │       │   │   │   │   ├── tuple.pxd

│   │       │   │   │   │   ├── type.pxd

│   │       │   │   │   │   ├── unicode.pxd

│   │       │   │   │   │   ├── version.pxd

│   │       │   │   │   │   └── weakref.pxd

│   │       │   │   │   ├── libc

│   │       │   │   │   │   ├── __init__.pxd

│   │       │   │   │   │   ├── errno.pxd

│   │       │   │   │   │   ├── float.pxd

│   │       │   │   │   │   ├── limits.pxd

│   │       │   │   │   │   ├── locale.pxd

│   │       │   │   │   │   ├── math.pxd

│   │       │   │   │   │   ├── setjmp.pxd

│   │       │   │   │   │   ├── signal.pxd

│   │       │   │   │   │   ├── stddef.pxd

│   │       │   │   │   │   ├── stdint.pxd

│   │       │   │   │   │   ├── stdio.pxd

│   │       │   │   │   │   ├── stdlib.pxd

│   │       │   │   │   │   ├── string.pxd

│   │       │   │   │   │   └── time.pxd

│   │       │   │   │   ├── libcpp

│   │       │   │   │   │   ├── __init__.pxd

│   │       │   │   │   │   ├── algorithm.pxd

│   │       │   │   │   │   ├── cast.pxd

│   │       │   │   │   │   ├── complex.pxd

│   │       │   │   │   │   ├── deque.pxd

│   │       │   │   │   │   ├── forward_list.pxd

│   │       │   │   │   │   ├── functional.pxd

│   │       │   │   │   │   ├── iterator.pxd

│   │       │   │   │   │   ├── limits.pxd

│   │       │   │   │   │   ├── list.pxd

│   │       │   │   │   │   ├── map.pxd

│   │       │   │   │   │   ├── memory.pxd

│   │       │   │   │   │   ├── pair.pxd

│   │       │   │   │   │   ├── queue.pxd

│   │       │   │   │   │   ├── set.pxd

│   │       │   │   │   │   ├── stack.pxd

│   │       │   │   │   │   ├── string.pxd

│   │       │   │   │   │   ├── typeindex.pxd

│   │       │   │   │   │   ├── typeinfo.pxd

│   │       │   │   │   │   ├── unordered_map.pxd

│   │       │   │   │   │   ├── unordered_set.pxd

│   │       │   │   │   │   ├── utility.pxd

│   │       │   │   │   │   └── vector.pxd

│   │       │   │   │   ├── numpy

│   │       │   │   │   │   ├── __init__.pxd

│   │       │   │   │   │   └── math.pxd

│   │       │   │   │   ├── posix

│   │       │   │   │   │   ├── __init__.pxd

│   │       │   │   │   │   ├── dlfcn.pxd

│   │       │   │   │   │   ├── fcntl.pxd

│   │       │   │   │   │   ├── ioctl.pxd

│   │       │   │   │   │   ├── mman.pxd

│   │       │   │   │   │   ├── resource.pxd

│   │       │   │   │   │   ├── select.pxd

│   │       │   │   │   │   ├── signal.pxd

│   │       │   │   │   │   ├── stat.pxd

│   │       │   │   │   │   ├── stdio.pxd

│   │       │   │   │   │   ├── stdlib.pxd

│   │       │   │   │   │   ├── strings.pxd

│   │       │   │   │   │   ├── time.pxd

│   │       │   │   │   │   ├── types.pxd

│   │       │   │   │   │   ├── unistd.pxd

│   │       │   │   │   │   └── wait.pxd

│   │       │   │   │   └── openmp.pxd

│   │       │   │   ├── Plex

│   │       │   │   │   ├── Actions.cp311-win_amd64.pyd

│   │       │   │   │   ├── Actions.pxd

│   │       │   │   │   ├── Actions.py

│   │       │   │   │   ├── DFA.py

│   │       │   │   │   ├── Errors.py

│   │       │   │   │   ├── Lexicons.py

│   │       │   │   │   ├── Machines.py

│   │       │   │   │   ├── Regexps.py

│   │       │   │   │   ├── Scanners.cp311-win_amd64.pyd

│   │       │   │   │   ├── Scanners.pxd

│   │       │   │   │   ├── Scanners.py

│   │       │   │   │   ├── Timing.py

│   │       │   │   │   ├── Traditional.py

│   │       │   │   │   ├── Transitions.py

│   │       │   │   │   └── __init__.py

│   │       │   │   ├── Runtime

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── refnanny.cp311-win_amd64.pyd

│   │       │   │   │   └── refnanny.pyx

│   │       │   │   ├── Tempita

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _looper.py

│   │       │   │   │   ├── _tempita.cp311-win_amd64.pyd

│   │       │   │   │   ├── _tempita.py

│   │       │   │   │   └── compat3.py

│   │       │   │   ├── Tests

│   │       │   │   │   ├── TestCodeWriter.py

│   │       │   │   │   ├── TestCythonUtils.py

│   │       │   │   │   ├── TestJediTyper.py

│   │       │   │   │   ├── TestStringIOTree.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── xmlrunner.py

│   │       │   │   ├── Utility

│   │       │   │   │   ├── AsyncGen.c

│   │       │   │   │   ├── Buffer.c

│   │       │   │   │   ├── Builtins.c

│   │       │   │   │   ├── CConvert.pyx

│   │       │   │   │   ├── CMath.c

│   │       │   │   │   ├── Capsule.c

│   │       │   │   │   ├── CommonStructures.c

│   │       │   │   │   ├── Complex.c

│   │       │   │   │   ├── Coroutine.c

│   │       │   │   │   ├── CpdefEnums.pyx

│   │       │   │   │   ├── CppConvert.pyx

│   │       │   │   │   ├── CppSupport.cpp

│   │       │   │   │   ├── CythonFunction.c

│   │       │   │   │   ├── Embed.c

│   │       │   │   │   ├── Exceptions.c

│   │       │   │   │   ├── ExtensionTypes.c

│   │       │   │   │   ├── FunctionArguments.c

│   │       │   │   │   ├── ImportExport.c

│   │       │   │   │   ├── MemoryView.pyx

│   │       │   │   │   ├── MemoryView_C.c

│   │       │   │   │   ├── ModuleSetupCode.c

│   │       │   │   │   ├── ObjectHandling.c

│   │       │   │   │   ├── Optimize.c

│   │       │   │   │   ├── Overflow.c

│   │       │   │   │   ├── Printing.c

│   │       │   │   │   ├── Profile.c

│   │       │   │   │   ├── StringTools.c

│   │       │   │   │   ├── TestCyUtilityLoader.pyx

│   │       │   │   │   ├── TestCythonScope.pyx

│   │       │   │   │   ├── TestUtilityLoader.c

│   │       │   │   │   ├── TypeConversion.c

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── arrayarray.h

│   │       │   │   ├── CodeWriter.py

│   │       │   │   ├── Coverage.py

│   │       │   │   ├── Debugging.py

│   │       │   │   ├── Shadow.py

│   │       │   │   ├── StringIOTree.py

│   │       │   │   ├── TestUtils.py

│   │       │   │   ├── Utils.py

│   │       │   │   └── __init__.py

│   │       │   ├── Cython-0.29.30.dist-info

│   │       │   │   ├── COPYING.txt

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE.txt

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   ├── entry_points.txt

│   │       │   │   └── top_level.txt

│   │       │   ├── MaterialX

│   │       │   │   ├── _scripts

│   │       │   │   │   ├── README.md

│   │       │   │   │   └── __init__.py

│   │       │   │   ├── libraries

│   │       │   │   │   ├── bxdf

│   │       │   │   │   │   ├── lama

│   │       │   │   │   │   │   ├── lama_add.mtlx

│   │       │   │   │   │   │   ├── lama_conductor.mtlx

│   │       │   │   │   │   │   ├── lama_dielectric.mtlx

│   │       │   │   │   │   │   ├── lama_diffuse.mtlx

│   │       │   │   │   │   │   ├── lama_emission.mtlx

│   │       │   │   │   │   │   ├── lama_layer.mtlx

│   │       │   │   │   │   │   ├── lama_mix.mtlx

│   │       │   │   │   │   │   ├── lama_sheen.mtlx

│   │       │   │   │   │   │   ├── lama_sss.mtlx

│   │       │   │   │   │   │   └── lama_translucent.mtlx

│   │       │   │   │   │   ├── translation

│   │       │   │   │   │   │   ├── standard_surface_to_gltf_pbr.mtlx

│   │       │   │   │   │   │   └── standard_surface_to_usd.mtlx

│   │       │   │   │   │   ├── disney_brdf_2012.mtlx

│   │       │   │   │   │   ├── disney_brdf_2015.mtlx

│   │       │   │   │   │   ├── gltf_pbr.mtlx

│   │       │   │   │   │   ├── standard_surface.mtlx

│   │       │   │   │   │   └── usd_preview_surface.mtlx

│   │       │   │   │   ├── cmlib

│   │       │   │   │   │   ├── cmlib_defs.mtlx

│   │       │   │   │   │   └── cmlib_ng.mtlx

│   │       │   │   │   ├── lights

│   │       │   │   │   │   ├── genglsl

│   │       │   │   │   │   │   ├── lights_genglsl_impl.mtlx

│   │       │   │   │   │   │   ├── mx_directional_light.glsl

│   │       │   │   │   │   │   ├── mx_point_light.glsl

│   │       │   │   │   │   │   └── mx_spot_light.glsl

│   │       │   │   │   │   ├── genmsl

│   │       │   │   │   │   │   ├── lights_genmsl_impl.mtlx

│   │       │   │   │   │   │   ├── mx_directional_light.metal

│   │       │   │   │   │   │   ├── mx_point_light.metal

│   │       │   │   │   │   │   └── mx_spot_light.metal

│   │       │   │   │   │   └── lights_defs.mtlx

│   │       │   │   │   ├── pbrlib

│   │       │   │   │   │   ├── genglsl

│   │       │   │   │   │   │   ├── lib

│   │       │   │   │   │   │   │   ├── mx_environment_fis.glsl

│   │       │   │   │   │   │   │   ├── mx_environment_none.glsl

│   │       │   │   │   │   │   │   ├── mx_environment_prefilter.glsl

│   │       │   │   │   │   │   │   ├── mx_microfacet.glsl

│   │       │   │   │   │   │   │   ├── mx_microfacet_diffuse.glsl

│   │       │   │   │   │   │   │   ├── mx_microfacet_sheen.glsl

│   │       │   │   │   │   │   │   ├── mx_microfacet_specular.glsl

│   │       │   │   │   │   │   │   ├── mx_shadow.glsl

│   │       │   │   │   │   │   │   ├── mx_table.glsl

│   │       │   │   │   │   │   │   ├── mx_transmission_opacity.glsl

│   │       │   │   │   │   │   │   └── mx_transmission_refract.glsl

│   │       │   │   │   │   │   ├── mx_add_edf.glsl

│   │       │   │   │   │   │   ├── mx_anisotropic_vdf.glsl

│   │       │   │   │   │   │   ├── mx_artistic_ior.glsl

│   │       │   │   │   │   │   ├── mx_blackbody.glsl

│   │       │   │   │   │   │   ├── mx_burley_diffuse_bsdf.glsl

│   │       │   │   │   │   │   ├── mx_conductor_bsdf.glsl

│   │       │   │   │   │   │   ├── mx_dielectric_bsdf.glsl

│   │       │   │   │   │   │   ├── mx_displacement_float.glsl

│   │       │   │   │   │   │   ├── mx_displacement_vector3.glsl

│   │       │   │   │   │   │   ├── mx_generalized_schlick_bsdf.glsl

│   │       │   │   │   │   │   ├── mx_generalized_schlick_edf.glsl

│   │       │   │   │   │   │   ├── mx_oren_nayar_diffuse_bsdf.glsl

│   │       │   │   │   │   │   ├── mx_roughness_anisotropy.glsl

│   │       │   │   │   │   │   ├── mx_roughness_dual.glsl

│   │       │   │   │   │   │   ├── mx_sheen_bsdf.glsl

│   │       │   │   │   │   │   ├── mx_subsurface_bsdf.glsl

│   │       │   │   │   │   │   ├── mx_translucent_bsdf.glsl

│   │       │   │   │   │   │   ├── mx_uniform_edf.glsl

│   │       │   │   │   │   │   └── pbrlib_genglsl_impl.mtlx

│   │       │   │   │   │   ├── genmdl

│   │       │   │   │   │   │   └── pbrlib_genmdl_impl.mtlx

│   │       │   │   │   │   ├── genmsl

│   │       │   │   │   │   │   └── pbrlib_genmsl_impl.mtlx

│   │       │   │   │   │   ├── genosl

│   │       │   │   │   │   │   ├── legacy

│   │       │   │   │   │   │   │   ├── mx_anisotropic_vdf.osl

│   │       │   │   │   │   │   │   ├── mx_burley_diffuse_bsdf.osl

│   │       │   │   │   │   │   │   ├── mx_conductor_bsdf.osl

│   │       │   │   │   │   │   │   ├── mx_dielectric_bsdf.osl

│   │       │   │   │   │   │   │   ├── mx_generalized_schlick_bsdf.osl

│   │       │   │   │   │   │   │   ├── mx_oren_nayar_diffuse_bsdf.osl

│   │       │   │   │   │   │   │   ├── mx_sheen_bsdf.osl

│   │       │   │   │   │   │   │   ├── mx_subsurface_bsdf.osl

│   │       │   │   │   │   │   │   ├── mx_surface.osl

│   │       │   │   │   │   │   │   └── mx_translucent_bsdf.osl

│   │       │   │   │   │   │   ├── lib

│   │       │   │   │   │   │   │   ├── mx_microfacet.osl

│   │       │   │   │   │   │   │   ├── mx_microfacet_sheen.osl

│   │       │   │   │   │   │   │   └── mx_microfacet_specular.osl

│   │       │   │   │   │   │   ├── mx_anisotropic_vdf.osl

│   │       │   │   │   │   │   ├── mx_artistic_ior.osl

│   │       │   │   │   │   │   ├── mx_blackbody.osl

│   │       │   │   │   │   │   ├── mx_dielectric_bsdf.osl

│   │       │   │   │   │   │   ├── mx_displacement_float.osl

│   │       │   │   │   │   │   ├── mx_displacement_vector3.osl

│   │       │   │   │   │   │   ├── mx_generalized_schlick_bsdf.osl

│   │       │   │   │   │   │   ├── mx_generalized_schlick_edf.osl

│   │       │   │   │   │   │   ├── mx_roughness_anisotropy.osl

│   │       │   │   │   │   │   ├── mx_roughness_dual.osl

│   │       │   │   │   │   │   ├── mx_subsurface_bsdf.osl

│   │       │   │   │   │   │   ├── mx_surface.osl

│   │       │   │   │   │   │   └── pbrlib_genosl_impl.mtlx

│   │       │   │   │   │   ├── pbrlib_defs.mtlx

│   │       │   │   │   │   └── pbrlib_ng.mtlx

│   │       │   │   │   ├── stdlib

│   │       │   │   │   │   ├── genglsl

│   │       │   │   │   │   │   ├── lib

│   │       │   │   │   │   │   │   ├── mx_hsv.glsl

│   │       │   │   │   │   │   │   ├── mx_math.glsl

│   │       │   │   │   │   │   │   ├── mx_noise.glsl

│   │       │   │   │   │   │   │   ├── mx_sampling.glsl

│   │       │   │   │   │   │   │   ├── mx_transform_uv.glsl

│   │       │   │   │   │   │   │   └── mx_transform_uv_vflip.glsl

│   │       │   │   │   │   │   ├── mx_aastep.glsl

│   │       │   │   │   │   │   ├── mx_burn_color3.glsl

│   │       │   │   │   │   │   ├── mx_burn_color4.glsl

│   │       │   │   │   │   │   ├── mx_burn_float.glsl

│   │       │   │   │   │   │   ├── mx_cellnoise2d_float.glsl

│   │       │   │   │   │   │   ├── mx_cellnoise3d_float.glsl

│   │       │   │   │   │   │   ├── mx_disjointover_color4.glsl

│   │       │   │   │   │   │   ├── mx_dodge_color3.glsl

│   │       │   │   │   │   │   ├── mx_dodge_color4.glsl

│   │       │   │   │   │   │   ├── mx_dodge_float.glsl

│   │       │   │   │   │   │   ├── mx_fractal3d_fa_vector2.glsl

│   │       │   │   │   │   │   ├── mx_fractal3d_fa_vector3.glsl

│   │       │   │   │   │   │   ├── mx_fractal3d_fa_vector4.glsl

│   │       │   │   │   │   │   ├── mx_fractal3d_float.glsl

│   │       │   │   │   │   │   ├── mx_fractal3d_vector2.glsl

│   │       │   │   │   │   │   ├── mx_fractal3d_vector3.glsl

│   │       │   │   │   │   │   ├── mx_fractal3d_vector4.glsl

│   │       │   │   │   │   │   ├── mx_hsvtorgb_color3.glsl

│   │       │   │   │   │   │   ├── mx_hsvtorgb_color4.glsl

│   │       │   │   │   │   │   ├── mx_image_color3.glsl

│   │       │   │   │   │   │   ├── mx_image_color4.glsl

│   │       │   │   │   │   │   ├── mx_image_float.glsl

│   │       │   │   │   │   │   ├── mx_image_vector2.glsl

│   │       │   │   │   │   │   ├── mx_image_vector3.glsl

│   │       │   │   │   │   │   ├── mx_image_vector4.glsl

│   │       │   │   │   │   │   ├── mx_luminance_color3.glsl

│   │       │   │   │   │   │   ├── mx_luminance_color4.glsl

│   │       │   │   │   │   │   ├── mx_mix_surfaceshader.glsl

│   │       │   │   │   │   │   ├── mx_noise2d_fa_vector2.glsl

│   │       │   │   │   │   │   ├── mx_noise2d_fa_vector3.glsl

│   │       │   │   │   │   │   ├── mx_noise2d_fa_vector4.glsl

│   │       │   │   │   │   │   ├── mx_noise2d_float.glsl

│   │       │   │   │   │   │   ├── mx_noise2d_vector2.glsl

│   │       │   │   │   │   │   ├── mx_noise2d_vector3.glsl

│   │       │   │   │   │   │   ├── mx_noise2d_vector4.glsl

│   │       │   │   │   │   │   ├── mx_noise3d_fa_vector2.glsl

│   │       │   │   │   │   │   ├── mx_noise3d_fa_vector3.glsl

│   │       │   │   │   │   │   ├── mx_noise3d_fa_vector4.glsl

│   │       │   │   │   │   │   ├── mx_noise3d_float.glsl

│   │       │   │   │   │   │   ├── mx_noise3d_vector2.glsl

│   │       │   │   │   │   │   ├── mx_noise3d_vector3.glsl

│   │       │   │   │   │   │   ├── mx_noise3d_vector4.glsl

│   │       │   │   │   │   │   ├── mx_normalmap.glsl

│   │       │   │   │   │   │   ├── mx_overlay.glsl

│   │       │   │   │   │   │   ├── mx_overlay_color3.glsl

│   │       │   │   │   │   │   ├── mx_overlay_color4.glsl

│   │       │   │   │   │   │   ├── mx_premult_color4.glsl

│   │       │   │   │   │   │   ├── mx_ramplr_float.glsl

│   │       │   │   │   │   │   ├── mx_ramplr_vector2.glsl

│   │       │   │   │   │   │   ├── mx_ramplr_vector3.glsl

│   │       │   │   │   │   │   ├── mx_ramplr_vector4.glsl

│   │       │   │   │   │   │   ├── mx_ramptb_float.glsl

│   │       │   │   │   │   │   ├── mx_ramptb_vector2.glsl

│   │       │   │   │   │   │   ├── mx_ramptb_vector3.glsl

│   │       │   │   │   │   │   ├── mx_ramptb_vector4.glsl

│   │       │   │   │   │   │   ├── mx_rgbtohsv_color3.glsl

│   │       │   │   │   │   │   ├── mx_rgbtohsv_color4.glsl

│   │       │   │   │   │   │   ├── mx_rotate_vector2.glsl

│   │       │   │   │   │   │   ├── mx_rotate_vector3.glsl

│   │       │   │   │   │   │   ├── mx_smoothstep_float.glsl

│   │       │   │   │   │   │   ├── mx_smoothstep_vec2.glsl

│   │       │   │   │   │   │   ├── mx_smoothstep_vec2FA.glsl

│   │       │   │   │   │   │   ├── mx_smoothstep_vec3.glsl

│   │       │   │   │   │   │   ├── mx_smoothstep_vec3FA.glsl

│   │       │   │   │   │   │   ├── mx_smoothstep_vec4.glsl

│   │       │   │   │   │   │   ├── mx_smoothstep_vec4FA.glsl

│   │       │   │   │   │   │   ├── mx_splitlr_float.glsl

│   │       │   │   │   │   │   ├── mx_splitlr_vector2.glsl

│   │       │   │   │   │   │   ├── mx_splitlr_vector3.glsl

│   │       │   │   │   │   │   ├── mx_splitlr_vector4.glsl

│   │       │   │   │   │   │   ├── mx_splittb_float.glsl

│   │       │   │   │   │   │   ├── mx_splittb_vector2.glsl

│   │       │   │   │   │   │   ├── mx_splittb_vector3.glsl

│   │       │   │   │   │   │   ├── mx_splittb_vector4.glsl

│   │       │   │   │   │   │   ├── mx_transformmatrix_vector2M3.glsl

│   │       │   │   │   │   │   ├── mx_transformmatrix_vector3M4.glsl

│   │       │   │   │   │   │   ├── mx_unpremult_color4.glsl

│   │       │   │   │   │   │   ├── mx_worleynoise2d_float.glsl

│   │       │   │   │   │   │   ├── mx_worleynoise2d_vector2.glsl

│   │       │   │   │   │   │   ├── mx_worleynoise2d_vector3.glsl

│   │       │   │   │   │   │   ├── mx_worleynoise3d_float.glsl

│   │       │   │   │   │   │   ├── mx_worleynoise3d_vector2.glsl

│   │       │   │   │   │   │   ├── mx_worleynoise3d_vector3.glsl

│   │       │   │   │   │   │   └── stdlib_genglsl_impl.mtlx

│   │       │   │   │   │   ├── genmdl

│   │       │   │   │   │   │   └── stdlib_genmdl_impl.mtlx

│   │       │   │   │   │   ├── genmsl

│   │       │   │   │   │   │   ├── lib

│   │       │   │   │   │   │   │   ├── mx_math.metal

│   │       │   │   │   │   │   │   ├── mx_matscalaroperators.metal

│   │       │   │   │   │   │   │   ├── mx_sampling.metal

│   │       │   │   │   │   │   │   └── mx_texture.metal

│   │       │   │   │   │   │   ├── mx_burn_color3.metal

│   │       │   │   │   │   │   ├── mx_burn_color4.metal

│   │       │   │   │   │   │   ├── mx_burn_float.metal

│   │       │   │   │   │   │   ├── mx_dodge_color3.metal

│   │       │   │   │   │   │   ├── mx_dodge_color4.metal

│   │       │   │   │   │   │   ├── mx_dodge_float.metal

│   │       │   │   │   │   │   ├── mx_normalmap.metal

│   │       │   │   │   │   │   ├── mx_smoothstep_float.metal

│   │       │   │   │   │   │   ├── mx_smoothstep_vec2.metal

│   │       │   │   │   │   │   ├── mx_smoothstep_vec2FA.metal

│   │       │   │   │   │   │   ├── mx_smoothstep_vec3.metal

│   │       │   │   │   │   │   ├── mx_smoothstep_vec3FA.metal

│   │       │   │   │   │   │   ├── mx_smoothstep_vec4.metal

│   │       │   │   │   │   │   ├── mx_smoothstep_vec4FA.metal

│   │       │   │   │   │   │   └── stdlib_genmsl_impl.mtlx

│   │       │   │   │   │   ├── genosl

│   │       │   │   │   │   │   ├── include

│   │       │   │   │   │   │   │   ├── color4.h

│   │       │   │   │   │   │   │   ├── matrix33.h

│   │       │   │   │   │   │   │   ├── mx_funcs.h

│   │       │   │   │   │   │   │   ├── vector2.h

│   │       │   │   │   │   │   │   └── vector4.h

│   │       │   │   │   │   │   ├── lib

│   │       │   │   │   │   │   │   ├── mx_sampling.osl

│   │       │   │   │   │   │   │   ├── mx_transform_uv.osl

│   │       │   │   │   │   │   │   └── mx_transform_uv_vflip.osl

│   │       │   │   │   │   │   ├── mx_ambientocclusion_float.osl

│   │       │   │   │   │   │   ├── mx_burn_color3.osl

│   │       │   │   │   │   │   ├── mx_burn_color4.osl

│   │       │   │   │   │   │   ├── mx_burn_float.osl

│   │       │   │   │   │   │   ├── mx_cellnoise2d_float.osl

│   │       │   │   │   │   │   ├── mx_cellnoise3d_float.osl

│   │       │   │   │   │   │   ├── mx_disjointover_color4.osl

│   │       │   │   │   │   │   ├── mx_dodge_color3.osl

│   │       │   │   │   │   │   ├── mx_dodge_color4.osl

│   │       │   │   │   │   │   ├── mx_dodge_float.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_color3.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_color4.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_fa_color3.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_fa_color4.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_fa_vector2.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_fa_vector3.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_fa_vector4.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_float.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_vector2.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_vector3.osl

│   │       │   │   │   │   │   ├── mx_fractal3d_vector4.osl

│   │       │   │   │   │   │   ├── mx_frame_float.osl

│   │       │   │   │   │   │   ├── mx_geomcolor_color3.osl

│   │       │   │   │   │   │   ├── mx_geomcolor_color4.osl

│   │       │   │   │   │   │   ├── mx_geomcolor_float.osl

│   │       │   │   │   │   │   ├── mx_geompropvalue_boolean.osl

│   │       │   │   │   │   │   ├── mx_geompropvalue_color3.osl

│   │       │   │   │   │   │   ├── mx_geompropvalue_color4.osl

│   │       │   │   │   │   │   ├── mx_geompropvalue_float.osl

│   │       │   │   │   │   │   ├── mx_geompropvalue_integer.osl

│   │       │   │   │   │   │   ├── mx_geompropvalue_string.osl

│   │       │   │   │   │   │   ├── mx_geompropvalue_vector2.osl

│   │       │   │   │   │   │   ├── mx_geompropvalue_vector3.osl

│   │       │   │   │   │   │   ├── mx_geompropvalue_vector4.osl

│   │       │   │   │   │   │   ├── mx_heighttonormal_vector3.osl

│   │       │   │   │   │   │   ├── mx_hsvtorgb_color3.osl

│   │       │   │   │   │   │   ├── mx_hsvtorgb_color4.osl

│   │       │   │   │   │   │   ├── mx_image_color3.osl

│   │       │   │   │   │   │   ├── mx_image_color4.osl

│   │       │   │   │   │   │   ├── mx_image_float.osl

│   │       │   │   │   │   │   ├── mx_image_vector2.osl

│   │       │   │   │   │   │   ├── mx_image_vector3.osl

│   │       │   │   │   │   │   ├── mx_image_vector4.osl

│   │       │   │   │   │   │   ├── mx_luminance_color3.osl

│   │       │   │   │   │   │   ├── mx_luminance_color4.osl

│   │       │   │   │   │   │   ├── mx_mix_surfaceshader.osl

│   │       │   │   │   │   │   ├── mx_noise2d_color3.osl

│   │       │   │   │   │   │   ├── mx_noise2d_color4.osl

│   │       │   │   │   │   │   ├── mx_noise2d_fa_color3.osl

│   │       │   │   │   │   │   ├── mx_noise2d_fa_color4.osl

│   │       │   │   │   │   │   ├── mx_noise2d_fa_vector2.osl

│   │       │   │   │   │   │   ├── mx_noise2d_fa_vector3.osl

│   │       │   │   │   │   │   ├── mx_noise2d_fa_vector4.osl

│   │       │   │   │   │   │   ├── mx_noise2d_float.osl

│   │       │   │   │   │   │   ├── mx_noise2d_vector2.osl

│   │       │   │   │   │   │   ├── mx_noise2d_vector3.osl

│   │       │   │   │   │   │   ├── mx_noise2d_vector4.osl

│   │       │   │   │   │   │   ├── mx_noise3d_color3.osl

│   │       │   │   │   │   │   ├── mx_noise3d_color4.osl

│   │       │   │   │   │   │   ├── mx_noise3d_fa_color3.osl

│   │       │   │   │   │   │   ├── mx_noise3d_fa_color4.osl

│   │       │   │   │   │   │   ├── mx_noise3d_fa_vector2.osl

│   │       │   │   │   │   │   ├── mx_noise3d_fa_vector3.osl

│   │       │   │   │   │   │   ├── mx_noise3d_fa_vector4.osl

│   │       │   │   │   │   │   ├── mx_noise3d_float.osl

│   │       │   │   │   │   │   ├── mx_noise3d_vector2.osl

│   │       │   │   │   │   │   ├── mx_noise3d_vector3.osl

│   │       │   │   │   │   │   ├── mx_noise3d_vector4.osl

│   │       │   │   │   │   │   ├── mx_normalmap.osl

│   │       │   │   │   │   │   ├── mx_overlay_color3.osl

│   │       │   │   │   │   │   ├── mx_overlay_color4.osl

│   │       │   │   │   │   │   ├── mx_premult_color4.osl

│   │       │   │   │   │   │   ├── mx_rgbtohsv_color3.osl

│   │       │   │   │   │   │   ├── mx_rgbtohsv_color4.osl

│   │       │   │   │   │   │   ├── mx_rotate_vector2.osl

│   │       │   │   │   │   │   ├── mx_rotate_vector3.osl

│   │       │   │   │   │   │   ├── mx_surface_unlit.osl

│   │       │   │   │   │   │   ├── mx_time_float.osl

│   │       │   │   │   │   │   ├── mx_transformmatrix_vector2M3.osl

│   │       │   │   │   │   │   ├── mx_unpremult_color4.osl

│   │       │   │   │   │   │   ├── mx_worleynoise2d_float.osl

│   │       │   │   │   │   │   ├── mx_worleynoise2d_vector2.osl

│   │       │   │   │   │   │   ├── mx_worleynoise2d_vector3.osl

│   │       │   │   │   │   │   ├── mx_worleynoise3d_float.osl

│   │       │   │   │   │   │   ├── mx_worleynoise3d_vector2.osl

│   │       │   │   │   │   │   ├── mx_worleynoise3d_vector3.osl

│   │       │   │   │   │   │   └── stdlib_genosl_impl.mtlx

│   │       │   │   │   │   ├── stdlib_defs.mtlx

│   │       │   │   │   │   └── stdlib_ng.mtlx

│   │       │   │   │   ├── targets

│   │       │   │   │   │   ├── essl.mtlx

│   │       │   │   │   │   ├── genglsl.mtlx

│   │       │   │   │   │   ├── genmdl.mtlx

│   │       │   │   │   │   ├── genmsl.mtlx

│   │       │   │   │   │   └── genosl.mtlx

│   │       │   │   │   └── README.md

│   │       │   │   ├── MANIFEST.in

│   │       │   │   ├── PyMaterialXCore.cp311-win_amd64.pyd

│   │       │   │   ├── PyMaterialXFormat.cp311-win_amd64.pyd

│   │       │   │   ├── PyMaterialXGenGlsl.cp311-win_amd64.pyd

│   │       │   │   ├── PyMaterialXGenMdl.cp311-win_amd64.pyd

│   │       │   │   ├── PyMaterialXGenMsl.cp311-win_amd64.pyd

│   │       │   │   ├── PyMaterialXGenOsl.cp311-win_amd64.pyd

│   │       │   │   ├── PyMaterialXGenShader.cp311-win_amd64.pyd

│   │       │   │   ├── PyMaterialXRender.cp311-win_amd64.pyd

│   │       │   │   ├── PyMaterialXRenderGlsl.cp311-win_amd64.pyd

│   │       │   │   ├── PyMaterialXRenderOsl.cp311-win_amd64.pyd

│   │       │   │   ├── __init__.py

│   │       │   │   ├── colorspace.py

│   │       │   │   ├── datatype.py

│   │       │   │   └── main.py

│   │       │   ├── OpenImageIO

│   │       │   │   ├── OpenImageIO.cp311-win_amd64.pyd

│   │       │   │   └── __init__.py

│   │       │   ├── PyOpenColorIO

│   │       │   │   ├── PyOpenColorIO.pyd

│   │       │   │   └── __init__.py

│   │       │   ├── _distutils_hack

│   │       │   │   ├── __init__.py

│   │       │   │   └── override.py

│   │       │   ├── autopep8-1.6.0.dist-info

│   │       │   │   ├── AUTHORS.rst

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   ├── entry_points.txt

│   │       │   │   └── top_level.txt

│   │       │   ├── certifi

│   │       │   │   ├── __init__.py

│   │       │   │   ├── __main__.py

│   │       │   │   ├── cacert.pem

│   │       │   │   └── core.py

│   │       │   ├── certifi-2021.10.8.dist-info

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   └── top_level.txt

│   │       │   ├── charset_normalizer

│   │       │   │   ├── assets

│   │       │   │   │   └── __init__.py

│   │       │   │   ├── cli

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── normalizer.py

│   │       │   │   ├── __init__.py

│   │       │   │   ├── api.py

│   │       │   │   ├── cd.py

│   │       │   │   ├── constant.py

│   │       │   │   ├── legacy.py

│   │       │   │   ├── md.py

│   │       │   │   ├── models.py

│   │       │   │   ├── py.typed

│   │       │   │   ├── utils.py

│   │       │   │   └── version.py

│   │       │   ├── charset_normalizer-2.0.10.dist-info

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   ├── entry_points.txt

│   │       │   │   └── top_level.txt

│   │       │   ├── idna

│   │       │   │   ├── __init__.py

│   │       │   │   ├── codec.py

│   │       │   │   ├── compat.py

│   │       │   │   ├── core.py

│   │       │   │   ├── idnadata.py

│   │       │   │   ├── intranges.py

│   │       │   │   ├── package_data.py

│   │       │   │   ├── py.typed

│   │       │   │   └── uts46data.py

│   │       │   ├── idna-3.3.dist-info

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE.md

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   └── top_level.txt

│   │       │   ├── meson-0.63.0.dist-info

│   │       │   │   ├── COPYING

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   ├── entry_points.txt

│   │       │   │   └── top_level.txt

│   │       │   ├── mesonbuild

│   │       │   │   ├── ast

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── interpreter.py

│   │       │   │   │   ├── introspection.py

│   │       │   │   │   ├── postprocess.py

│   │       │   │   │   ├── printer.py

│   │       │   │   │   └── visitor.py

│   │       │   │   ├── backend

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── backends.py

│   │       │   │   │   ├── ninjabackend.py

│   │       │   │   │   ├── vs2010backend.py

│   │       │   │   │   ├── vs2012backend.py

│   │       │   │   │   ├── vs2013backend.py

│   │       │   │   │   ├── vs2015backend.py

│   │       │   │   │   ├── vs2017backend.py

│   │       │   │   │   ├── vs2019backend.py

│   │       │   │   │   ├── vs2022backend.py

│   │       │   │   │   └── xcodebackend.py

│   │       │   │   ├── cmake

│   │       │   │   │   ├── data

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   └── preload.cmake

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── common.py

│   │       │   │   │   ├── executor.py

│   │       │   │   │   ├── fileapi.py

│   │       │   │   │   ├── generator.py

│   │       │   │   │   ├── interpreter.py

│   │       │   │   │   ├── toolchain.py

│   │       │   │   │   ├── traceparser.py

│   │       │   │   │   └── tracetargets.py

│   │       │   │   ├── compilers

│   │       │   │   │   ├── mixins

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── arm.py

│   │       │   │   │   │   ├── ccrx.py

│   │       │   │   │   │   ├── clang.py

│   │       │   │   │   │   ├── clike.py

│   │       │   │   │   │   ├── compcert.py

│   │       │   │   │   │   ├── elbrus.py

│   │       │   │   │   │   ├── emscripten.py

│   │       │   │   │   │   ├── gnu.py

│   │       │   │   │   │   ├── intel.py

│   │       │   │   │   │   ├── islinker.py

│   │       │   │   │   │   ├── pgi.py

│   │       │   │   │   │   ├── ti.py

│   │       │   │   │   │   ├── visualstudio.py

│   │       │   │   │   │   └── xc16.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── c.py

│   │       │   │   │   ├── c_function_attributes.py

│   │       │   │   │   ├── compilers.py

│   │       │   │   │   ├── cpp.py

│   │       │   │   │   ├── cs.py

│   │       │   │   │   ├── cuda.py

│   │       │   │   │   ├── cython.py

│   │       │   │   │   ├── d.py

│   │       │   │   │   ├── detect.py

│   │       │   │   │   ├── fortran.py

│   │       │   │   │   ├── java.py

│   │       │   │   │   ├── objc.py

│   │       │   │   │   ├── objcpp.py

│   │       │   │   │   ├── rust.py

│   │       │   │   │   ├── swift.py

│   │       │   │   │   └── vala.py

│   │       │   │   ├── dependencies

│   │       │   │   │   ├── data

│   │       │   │   │   │   ├── CMakeLists.txt

│   │       │   │   │   │   ├── CMakeListsLLVM.txt

│   │       │   │   │   │   ├── CMakePathInfo.txt

│   │       │   │   │   │   └── __init__.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── base.py

│   │       │   │   │   ├── boost.py

│   │       │   │   │   ├── cmake.py

│   │       │   │   │   ├── coarrays.py

│   │       │   │   │   ├── configtool.py

│   │       │   │   │   ├── cuda.py

│   │       │   │   │   ├── detect.py

│   │       │   │   │   ├── dev.py

│   │       │   │   │   ├── dub.py

│   │       │   │   │   ├── factory.py

│   │       │   │   │   ├── framework.py

│   │       │   │   │   ├── hdf5.py

│   │       │   │   │   ├── misc.py

│   │       │   │   │   ├── mpi.py

│   │       │   │   │   ├── pkgconfig.py

│   │       │   │   │   ├── platform.py

│   │       │   │   │   ├── qt.py

│   │       │   │   │   ├── scalapack.py

│   │       │   │   │   └── ui.py

│   │       │   │   ├── interpreter

│   │       │   │   │   ├── primitives

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── array.py

│   │       │   │   │   │   ├── boolean.py

│   │       │   │   │   │   ├── dict.py

│   │       │   │   │   │   ├── integer.py

│   │       │   │   │   │   ├── range.py

│   │       │   │   │   │   └── string.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── compiler.py

│   │       │   │   │   ├── dependencyfallbacks.py

│   │       │   │   │   ├── interpreter.py

│   │       │   │   │   ├── interpreterobjects.py

│   │       │   │   │   ├── kwargs.py

│   │       │   │   │   ├── mesonmain.py

│   │       │   │   │   └── type_checking.py

│   │       │   │   ├── interpreterbase

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _unholder.py

│   │       │   │   │   ├── baseobjects.py

│   │       │   │   │   ├── decorators.py

│   │       │   │   │   ├── disabler.py

│   │       │   │   │   ├── exceptions.py

│   │       │   │   │   ├── helpers.py

│   │       │   │   │   ├── interpreterbase.py

│   │       │   │   │   └── operator.py

│   │       │   │   ├── linkers

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── detect.py

│   │       │   │   │   └── linkers.py

│   │       │   │   ├── mesonlib

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── platform.py

│   │       │   │   │   ├── posix.py

│   │       │   │   │   ├── universal.py

│   │       │   │   │   ├── vsenv.py

│   │       │   │   │   └── win32.py

│   │       │   │   ├── modules

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── cmake.py

│   │       │   │   │   ├── dlang.py

│   │       │   │   │   ├── fs.py

│   │       │   │   │   ├── gnome.py

│   │       │   │   │   ├── hotdoc.py

│   │       │   │   │   ├── i18n.py

│   │       │   │   │   ├── java.py

│   │       │   │   │   ├── keyval.py

│   │       │   │   │   ├── modtest.py

│   │       │   │   │   ├── pkgconfig.py

│   │       │   │   │   ├── python.py

│   │       │   │   │   ├── python3.py

│   │       │   │   │   ├── qt.py

│   │       │   │   │   ├── qt4.py

│   │       │   │   │   ├── qt5.py

│   │       │   │   │   ├── qt6.py

│   │       │   │   │   ├── sourceset.py

│   │       │   │   │   ├── unstable_cuda.py

│   │       │   │   │   ├── unstable_external_project.py

│   │       │   │   │   ├── unstable_icestorm.py

│   │       │   │   │   ├── unstable_rust.py

│   │       │   │   │   ├── unstable_simd.py

│   │       │   │   │   ├── unstable_wayland.py

│   │       │   │   │   └── windows.py

│   │       │   │   ├── scripts

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── clangformat.py

│   │       │   │   │   ├── clangtidy.py

│   │       │   │   │   ├── cleantrees.py

│   │       │   │   │   ├── cmake_run_ctgt.py

│   │       │   │   │   ├── cmd_or_ps.ps1

│   │       │   │   │   ├── copy.py

│   │       │   │   │   ├── coverage.py

│   │       │   │   │   ├── delwithsuffix.py

│   │       │   │   │   ├── depfixer.py

│   │       │   │   │   ├── depscan.py

│   │       │   │   │   ├── dirchanger.py

│   │       │   │   │   ├── env2mfile.py

│   │       │   │   │   ├── externalproject.py

│   │       │   │   │   ├── gettext.py

│   │       │   │   │   ├── gtkdochelper.py

│   │       │   │   │   ├── hotdochelper.py

│   │       │   │   │   ├── itstool.py

│   │       │   │   │   ├── meson_exe.py

│   │       │   │   │   ├── msgfmthelper.py

│   │       │   │   │   ├── regen_checker.py

│   │       │   │   │   ├── run_tool.py

│   │       │   │   │   ├── scanbuild.py

│   │       │   │   │   ├── symbolextractor.py

│   │       │   │   │   ├── tags.py

│   │       │   │   │   ├── uninstall.py

│   │       │   │   │   └── vcstagger.py

│   │       │   │   ├── templates

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── cpptemplates.py

│   │       │   │   │   ├── cstemplates.py

│   │       │   │   │   ├── ctemplates.py

│   │       │   │   │   ├── cudatemplates.py

│   │       │   │   │   ├── dlangtemplates.py

│   │       │   │   │   ├── fortrantemplates.py

│   │       │   │   │   ├── javatemplates.py

│   │       │   │   │   ├── mesontemplates.py

│   │       │   │   │   ├── objcpptemplates.py

│   │       │   │   │   ├── objctemplates.py

│   │       │   │   │   ├── rusttemplates.py

│   │       │   │   │   ├── samplefactory.py

│   │       │   │   │   ├── sampleimpl.py

│   │       │   │   │   └── valatemplates.py

│   │       │   │   ├── wrap

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── wrap.py

│   │       │   │   │   └── wraptool.py

│   │       │   │   ├── __init__.py

│   │       │   │   ├── _pathlib.py

│   │       │   │   ├── _typing.py

│   │       │   │   ├── arglist.py

│   │       │   │   ├── build.py

│   │       │   │   ├── coredata.py

│   │       │   │   ├── depfile.py

│   │       │   │   ├── envconfig.py

│   │       │   │   ├── environment.py

│   │       │   │   ├── mcompile.py

│   │       │   │   ├── mconf.py

│   │       │   │   ├── mdevenv.py

│   │       │   │   ├── mdist.py

│   │       │   │   ├── mesondata.py

│   │       │   │   ├── mesonmain.py

│   │       │   │   ├── minit.py

│   │       │   │   ├── minstall.py

│   │       │   │   ├── mintro.py

│   │       │   │   ├── mlog.py

│   │       │   │   ├── mparser.py

│   │       │   │   ├── msetup.py

│   │       │   │   ├── msubprojects.py

│   │       │   │   ├── mtest.py

│   │       │   │   ├── munstable_coredata.py

│   │       │   │   ├── optinterpreter.py

│   │       │   │   ├── programs.py

│   │       │   │   └── rewriter.py

│   │       │   ├── numpy

│   │       │   │   ├── _pyinstaller

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── hook-numpy.py

│   │       │   │   │   ├── pyinstaller-smoke.py

│   │       │   │   │   └── test_pyinstaller.py

│   │       │   │   ├── _typing

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _add_docstring.py

│   │       │   │   │   ├── _array_like.py

│   │       │   │   │   ├── _callable.pyi

│   │       │   │   │   ├── _char_codes.py

│   │       │   │   │   ├── _dtype_like.py

│   │       │   │   │   ├── _extended_precision.py

│   │       │   │   │   ├── _generic_alias.py

│   │       │   │   │   ├── _nbit.py

│   │       │   │   │   ├── _nested_sequence.py

│   │       │   │   │   ├── _scalars.py

│   │       │   │   │   ├── _shape.py

│   │       │   │   │   ├── _ufunc.pyi

│   │       │   │   │   └── setup.py

│   │       │   │   ├── array_api

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_array_object.py

│   │       │   │   │   │   ├── test_creation_functions.py

│   │       │   │   │   │   ├── test_data_type_functions.py

│   │       │   │   │   │   ├── test_elementwise_functions.py

│   │       │   │   │   │   ├── test_set_functions.py

│   │       │   │   │   │   ├── test_sorting_functions.py

│   │       │   │   │   │   └── test_validation.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _array_object.py

│   │       │   │   │   ├── _constants.py

│   │       │   │   │   ├── _creation_functions.py

│   │       │   │   │   ├── _data_type_functions.py

│   │       │   │   │   ├── _dtypes.py

│   │       │   │   │   ├── _elementwise_functions.py

│   │       │   │   │   ├── _manipulation_functions.py

│   │       │   │   │   ├── _searching_functions.py

│   │       │   │   │   ├── _set_functions.py

│   │       │   │   │   ├── _sorting_functions.py

│   │       │   │   │   ├── _statistical_functions.py

│   │       │   │   │   ├── _typing.py

│   │       │   │   │   ├── _utility_functions.py

│   │       │   │   │   ├── linalg.py

│   │       │   │   │   └── setup.py

│   │       │   │   ├── compat

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   └── test_compat.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _inspect.py

│   │       │   │   │   ├── _pep440.py

│   │       │   │   │   ├── py3k.py

│   │       │   │   │   └── setup.py

│   │       │   │   ├── core

│   │       │   │   │   ├── include

│   │       │   │   │   │   └── numpy

│   │       │   │   │   │       ├── libdivide

│   │       │   │   │   │       │   ├── LICENSE.txt

│   │       │   │   │   │       │   └── libdivide.h

│   │       │   │   │   │       ├── random

│   │       │   │   │   │       │   ├── bitgen.h

│   │       │   │   │   │       │   └── distributions.h

│   │       │   │   │   │       ├── .doxyfile

│   │       │   │   │   │       ├── __multiarray_api.h

│   │       │   │   │   │       ├── __ufunc_api.h

│   │       │   │   │   │       ├── _neighborhood_iterator_imp.h

│   │       │   │   │   │       ├── _numpyconfig.h

│   │       │   │   │   │       ├── arrayobject.h

│   │       │   │   │   │       ├── arrayscalars.h

│   │       │   │   │   │       ├── experimental_dtype_api.h

│   │       │   │   │   │       ├── halffloat.h

│   │       │   │   │   │       ├── multiarray_api.txt

│   │       │   │   │   │       ├── ndarrayobject.h

│   │       │   │   │   │       ├── ndarraytypes.h

│   │       │   │   │   │       ├── noprefix.h

│   │       │   │   │   │       ├── npy_1_7_deprecated_api.h

│   │       │   │   │   │       ├── npy_3kcompat.h

│   │       │   │   │   │       ├── npy_common.h

│   │       │   │   │   │       ├── npy_cpu.h

│   │       │   │   │   │       ├── npy_endian.h

│   │       │   │   │   │       ├── npy_interrupt.h

│   │       │   │   │   │       ├── npy_math.h

│   │       │   │   │   │       ├── npy_no_deprecated_api.h

│   │       │   │   │   │       ├── npy_os.h

│   │       │   │   │   │       ├── numpyconfig.h

│   │       │   │   │   │       ├── old_defines.h

│   │       │   │   │   │       ├── oldnumeric.h

│   │       │   │   │   │       ├── ufunc_api.txt

│   │       │   │   │   │       ├── ufuncobject.h

│   │       │   │   │   │       └── utils.h

│   │       │   │   │   ├── lib

│   │       │   │   │   │   ├── npy-pkg-config

│   │       │   │   │   │   │   ├── mlib.ini

│   │       │   │   │   │   │   └── npymath.ini

│   │       │   │   │   │   └── npymath.lib

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── data

│   │       │   │   │   │   │   ├── astype_copy.pkl

│   │       │   │   │   │   │   ├── generate_umath_validation_data.cpp

│   │       │   │   │   │   │   ├── recarray_from_file.fits

│   │       │   │   │   │   │   ├── umath-validation-set-README.txt

│   │       │   │   │   │   │   ├── umath-validation-set-arccos.csv

│   │       │   │   │   │   │   ├── umath-validation-set-arccosh.csv

│   │       │   │   │   │   │   ├── umath-validation-set-arcsin.csv

│   │       │   │   │   │   │   ├── umath-validation-set-arcsinh.csv

│   │       │   │   │   │   │   ├── umath-validation-set-arctan.csv

│   │       │   │   │   │   │   ├── umath-validation-set-arctanh.csv

│   │       │   │   │   │   │   ├── umath-validation-set-cbrt.csv

│   │       │   │   │   │   │   ├── umath-validation-set-cos.csv

│   │       │   │   │   │   │   ├── umath-validation-set-cosh.csv

│   │       │   │   │   │   │   ├── umath-validation-set-exp.csv

│   │       │   │   │   │   │   ├── umath-validation-set-exp2.csv

│   │       │   │   │   │   │   ├── umath-validation-set-expm1.csv

│   │       │   │   │   │   │   ├── umath-validation-set-log.csv

│   │       │   │   │   │   │   ├── umath-validation-set-log10.csv

│   │       │   │   │   │   │   ├── umath-validation-set-log1p.csv

│   │       │   │   │   │   │   ├── umath-validation-set-log2.csv

│   │       │   │   │   │   │   ├── umath-validation-set-sin.csv

│   │       │   │   │   │   │   ├── umath-validation-set-sinh.csv

│   │       │   │   │   │   │   ├── umath-validation-set-tan.csv

│   │       │   │   │   │   │   └── umath-validation-set-tanh.csv

│   │       │   │   │   │   ├── examples

│   │       │   │   │   │   │   ├── cython

│   │       │   │   │   │   │   │   ├── checks.pyx

│   │       │   │   │   │   │   │   └── setup.py

│   │       │   │   │   │   │   └── limited_api

│   │       │   │   │   │   │       ├── limited_api.c

│   │       │   │   │   │   │       └── setup.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _locales.py

│   │       │   │   │   │   ├── test__exceptions.py

│   │       │   │   │   │   ├── test_abc.py

│   │       │   │   │   │   ├── test_api.py

│   │       │   │   │   │   ├── test_argparse.py

│   │       │   │   │   │   ├── test_array_coercion.py

│   │       │   │   │   │   ├── test_array_interface.py

│   │       │   │   │   │   ├── test_arraymethod.py

│   │       │   │   │   │   ├── test_arrayprint.py

│   │       │   │   │   │   ├── test_casting_floatingpoint_errors.py

│   │       │   │   │   │   ├── test_casting_unittests.py

│   │       │   │   │   │   ├── test_conversion_utils.py

│   │       │   │   │   │   ├── test_cpu_dispatcher.py

│   │       │   │   │   │   ├── test_cpu_features.py

│   │       │   │   │   │   ├── test_custom_dtypes.py

│   │       │   │   │   │   ├── test_cython.py

│   │       │   │   │   │   ├── test_datetime.py

│   │       │   │   │   │   ├── test_defchararray.py

│   │       │   │   │   │   ├── test_deprecations.py

│   │       │   │   │   │   ├── test_dlpack.py

│   │       │   │   │   │   ├── test_dtype.py

│   │       │   │   │   │   ├── test_einsum.py

│   │       │   │   │   │   ├── test_errstate.py

│   │       │   │   │   │   ├── test_extint128.py

│   │       │   │   │   │   ├── test_function_base.py

│   │       │   │   │   │   ├── test_getlimits.py

│   │       │   │   │   │   ├── test_half.py

│   │       │   │   │   │   ├── test_hashtable.py

│   │       │   │   │   │   ├── test_indexerrors.py

│   │       │   │   │   │   ├── test_indexing.py

│   │       │   │   │   │   ├── test_item_selection.py

│   │       │   │   │   │   ├── test_limited_api.py

│   │       │   │   │   │   ├── test_longdouble.py

│   │       │   │   │   │   ├── test_machar.py

│   │       │   │   │   │   ├── test_mem_overlap.py

│   │       │   │   │   │   ├── test_mem_policy.py

│   │       │   │   │   │   ├── test_memmap.py

│   │       │   │   │   │   ├── test_multiarray.py

│   │       │   │   │   │   ├── test_nditer.py

│   │       │   │   │   │   ├── test_nep50_promotions.py

│   │       │   │   │   │   ├── test_numeric.py

│   │       │   │   │   │   ├── test_numerictypes.py

│   │       │   │   │   │   ├── test_overrides.py

│   │       │   │   │   │   ├── test_print.py

│   │       │   │   │   │   ├── test_protocols.py

│   │       │   │   │   │   ├── test_records.py

│   │       │   │   │   │   ├── test_regression.py

│   │       │   │   │   │   ├── test_scalar_ctors.py

│   │       │   │   │   │   ├── test_scalar_methods.py

│   │       │   │   │   │   ├── test_scalarbuffer.py

│   │       │   │   │   │   ├── test_scalarinherit.py

│   │       │   │   │   │   ├── test_scalarmath.py

│   │       │   │   │   │   ├── test_scalarprint.py

│   │       │   │   │   │   ├── test_shape_base.py

│   │       │   │   │   │   ├── test_simd.py

│   │       │   │   │   │   ├── test_simd_module.py

│   │       │   │   │   │   ├── test_strings.py

│   │       │   │   │   │   ├── test_ufunc.py

│   │       │   │   │   │   ├── test_umath.py

│   │       │   │   │   │   ├── test_umath_accuracy.py

│   │       │   │   │   │   ├── test_umath_complex.py

│   │       │   │   │   │   └── test_unicode.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── _add_newdocs.py

│   │       │   │   │   ├── _add_newdocs_scalars.py

│   │       │   │   │   ├── _asarray.py

│   │       │   │   │   ├── _asarray.pyi

│   │       │   │   │   ├── _dtype.py

│   │       │   │   │   ├── _dtype_ctypes.py

│   │       │   │   │   ├── _exceptions.py

│   │       │   │   │   ├── _internal.py

│   │       │   │   │   ├── _internal.pyi

│   │       │   │   │   ├── _machar.py

│   │       │   │   │   ├── _methods.py

│   │       │   │   │   ├── _multiarray_tests.cp311-win_amd64.pyd

│   │       │   │   │   ├── _multiarray_umath.cp311-win_amd64.pyd

│   │       │   │   │   ├── _operand_flag_tests.cp311-win_amd64.pyd

│   │       │   │   │   ├── _rational_tests.cp311-win_amd64.pyd

│   │       │   │   │   ├── _simd.cp311-win_amd64.pyd

│   │       │   │   │   ├── _string_helpers.py

│   │       │   │   │   ├── _struct_ufunc_tests.cp311-win_amd64.pyd

│   │       │   │   │   ├── _type_aliases.py

│   │       │   │   │   ├── _type_aliases.pyi

│   │       │   │   │   ├── _ufunc_config.py

│   │       │   │   │   ├── _ufunc_config.pyi

│   │       │   │   │   ├── _umath_tests.cp311-win_amd64.pyd

│   │       │   │   │   ├── arrayprint.py

│   │       │   │   │   ├── arrayprint.pyi

│   │       │   │   │   ├── cversions.py

│   │       │   │   │   ├── defchararray.py

│   │       │   │   │   ├── defchararray.pyi

│   │       │   │   │   ├── einsumfunc.py

│   │       │   │   │   ├── einsumfunc.pyi

│   │       │   │   │   ├── fromnumeric.py

│   │       │   │   │   ├── fromnumeric.pyi

│   │       │   │   │   ├── function_base.py

│   │       │   │   │   ├── function_base.pyi

│   │       │   │   │   ├── generate_numpy_api.py

│   │       │   │   │   ├── getlimits.py

│   │       │   │   │   ├── getlimits.pyi

│   │       │   │   │   ├── memmap.py

│   │       │   │   │   ├── memmap.pyi

│   │       │   │   │   ├── multiarray.py

│   │       │   │   │   ├── multiarray.pyi

│   │       │   │   │   ├── numeric.py

│   │       │   │   │   ├── numeric.pyi

│   │       │   │   │   ├── numerictypes.py

│   │       │   │   │   ├── numerictypes.pyi

│   │       │   │   │   ├── overrides.py

│   │       │   │   │   ├── records.py

│   │       │   │   │   ├── records.pyi

│   │       │   │   │   ├── setup.py

│   │       │   │   │   ├── setup_common.py

│   │       │   │   │   ├── shape_base.py

│   │       │   │   │   ├── shape_base.pyi

│   │       │   │   │   ├── umath.py

│   │       │   │   │   └── umath_tests.py

│   │       │   │   ├── distutils

│   │       │   │   │   ├── checks

│   │       │   │   │   │   ├── cpu_asimd.c

│   │       │   │   │   │   ├── cpu_asimddp.c

│   │       │   │   │   │   ├── cpu_asimdfhm.c

│   │       │   │   │   │   ├── cpu_asimdhp.c

│   │       │   │   │   │   ├── cpu_avx.c

│   │       │   │   │   │   ├── cpu_avx2.c

│   │       │   │   │   │   ├── cpu_avx512_clx.c

│   │       │   │   │   │   ├── cpu_avx512_cnl.c

│   │       │   │   │   │   ├── cpu_avx512_icl.c

│   │       │   │   │   │   ├── cpu_avx512_knl.c

│   │       │   │   │   │   ├── cpu_avx512_knm.c

│   │       │   │   │   │   ├── cpu_avx512_skx.c

│   │       │   │   │   │   ├── cpu_avx512cd.c

│   │       │   │   │   │   ├── cpu_avx512f.c

│   │       │   │   │   │   ├── cpu_f16c.c

│   │       │   │   │   │   ├── cpu_fma3.c

│   │       │   │   │   │   ├── cpu_fma4.c

│   │       │   │   │   │   ├── cpu_neon.c

│   │       │   │   │   │   ├── cpu_neon_fp16.c

│   │       │   │   │   │   ├── cpu_neon_vfpv4.c

│   │       │   │   │   │   ├── cpu_popcnt.c

│   │       │   │   │   │   ├── cpu_sse.c

│   │       │   │   │   │   ├── cpu_sse2.c

│   │       │   │   │   │   ├── cpu_sse3.c

│   │       │   │   │   │   ├── cpu_sse41.c

│   │       │   │   │   │   ├── cpu_sse42.c

│   │       │   │   │   │   ├── cpu_ssse3.c

│   │       │   │   │   │   ├── cpu_vsx.c

│   │       │   │   │   │   ├── cpu_vsx2.c

│   │       │   │   │   │   ├── cpu_vsx3.c

│   │       │   │   │   │   ├── cpu_vsx4.c

│   │       │   │   │   │   ├── cpu_vx.c

│   │       │   │   │   │   ├── cpu_vxe.c

│   │       │   │   │   │   ├── cpu_vxe2.c

│   │       │   │   │   │   ├── cpu_xop.c

│   │       │   │   │   │   ├── extra_avx512bw_mask.c

│   │       │   │   │   │   ├── extra_avx512dq_mask.c

│   │       │   │   │   │   ├── extra_avx512f_reduce.c

│   │       │   │   │   │   ├── extra_vsx4_mma.c

│   │       │   │   │   │   ├── extra_vsx_asm.c

│   │       │   │   │   │   └── test_flags.c

│   │       │   │   │   ├── command

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── autodist.py

│   │       │   │   │   │   ├── bdist_rpm.py

│   │       │   │   │   │   ├── build.py

│   │       │   │   │   │   ├── build_clib.py

│   │       │   │   │   │   ├── build_ext.py

│   │       │   │   │   │   ├── build_py.py

│   │       │   │   │   │   ├── build_scripts.py

│   │       │   │   │   │   ├── build_src.py

│   │       │   │   │   │   ├── config.py

│   │       │   │   │   │   ├── config_compiler.py

│   │       │   │   │   │   ├── develop.py

│   │       │   │   │   │   ├── egg_info.py

│   │       │   │   │   │   ├── install.py

│   │       │   │   │   │   ├── install_clib.py

│   │       │   │   │   │   ├── install_data.py

│   │       │   │   │   │   ├── install_headers.py

│   │       │   │   │   │   └── sdist.py

│   │       │   │   │   ├── fcompiler

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── absoft.py

│   │       │   │   │   │   ├── arm.py

│   │       │   │   │   │   ├── compaq.py

│   │       │   │   │   │   ├── environment.py

│   │       │   │   │   │   ├── fujitsu.py

│   │       │   │   │   │   ├── g95.py

│   │       │   │   │   │   ├── gnu.py

│   │       │   │   │   │   ├── hpux.py

│   │       │   │   │   │   ├── ibm.py

│   │       │   │   │   │   ├── intel.py

│   │       │   │   │   │   ├── lahey.py

│   │       │   │   │   │   ├── mips.py

│   │       │   │   │   │   ├── nag.py

│   │       │   │   │   │   ├── none.py

│   │       │   │   │   │   ├── nv.py

│   │       │   │   │   │   ├── pathf95.py

│   │       │   │   │   │   ├── pg.py

│   │       │   │   │   │   ├── sun.py

│   │       │   │   │   │   └── vast.py

│   │       │   │   │   ├── mingw

│   │       │   │   │   │   └── gfortran_vs2003_hack.c

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_build_ext.py

│   │       │   │   │   │   ├── test_ccompiler_opt.py

│   │       │   │   │   │   ├── test_ccompiler_opt_conf.py

│   │       │   │   │   │   ├── test_exec_command.py

│   │       │   │   │   │   ├── test_fcompiler.py

│   │       │   │   │   │   ├── test_fcompiler_gnu.py

│   │       │   │   │   │   ├── test_fcompiler_intel.py

│   │       │   │   │   │   ├── test_fcompiler_nagfor.py

│   │       │   │   │   │   ├── test_from_template.py

│   │       │   │   │   │   ├── test_log.py

│   │       │   │   │   │   ├── test_mingw32ccompiler.py

│   │       │   │   │   │   ├── test_misc_util.py

│   │       │   │   │   │   ├── test_npy_pkg_config.py

│   │       │   │   │   │   ├── test_shell_utils.py

│   │       │   │   │   │   └── test_system_info.py

│   │       │   │   │   ├── __config__.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── _shell_utils.py

│   │       │   │   │   ├── armccompiler.py

│   │       │   │   │   ├── ccompiler.py

│   │       │   │   │   ├── ccompiler_opt.py

│   │       │   │   │   ├── conv_template.py

│   │       │   │   │   ├── core.py

│   │       │   │   │   ├── cpuinfo.py

│   │       │   │   │   ├── exec_command.py

│   │       │   │   │   ├── extension.py

│   │       │   │   │   ├── from_template.py

│   │       │   │   │   ├── intelccompiler.py

│   │       │   │   │   ├── lib2def.py

│   │       │   │   │   ├── line_endings.py

│   │       │   │   │   ├── log.py

│   │       │   │   │   ├── mingw32ccompiler.py

│   │       │   │   │   ├── misc_util.py

│   │       │   │   │   ├── msvc9compiler.py

│   │       │   │   │   ├── msvccompiler.py

│   │       │   │   │   ├── npy_pkg_config.py

│   │       │   │   │   ├── numpy_distribution.py

│   │       │   │   │   ├── pathccompiler.py

│   │       │   │   │   ├── setup.py

│   │       │   │   │   ├── system_info.py

│   │       │   │   │   └── unixccompiler.py

│   │       │   │   ├── doc

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── constants.py

│   │       │   │   │   └── ufuncs.py

│   │       │   │   ├── f2py

│   │       │   │   │   ├── src

│   │       │   │   │   │   ├── fortranobject.c

│   │       │   │   │   │   └── fortranobject.h

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── src

│   │       │   │   │   │   │   ├── abstract_interface

│   │       │   │   │   │   │   │   ├── foo.f90

│   │       │   │   │   │   │   │   └── gh18403_mod.f90

│   │       │   │   │   │   │   ├── array_from_pyobj

│   │       │   │   │   │   │   │   └── wrapmodule.c

│   │       │   │   │   │   │   ├── assumed_shape

│   │       │   │   │   │   │   │   ├── .f2py_f2cmap

│   │       │   │   │   │   │   │   ├── foo_free.f90

│   │       │   │   │   │   │   │   ├── foo_mod.f90

│   │       │   │   │   │   │   │   ├── foo_use.f90

│   │       │   │   │   │   │   │   └── precision.f90

│   │       │   │   │   │   │   ├── block_docstring

│   │       │   │   │   │   │   │   └── foo.f

│   │       │   │   │   │   │   ├── callback

│   │       │   │   │   │   │   │   ├── foo.f

│   │       │   │   │   │   │   │   ├── gh17797.f90

│   │       │   │   │   │   │   │   └── gh18335.f90

│   │       │   │   │   │   │   ├── cli

│   │       │   │   │   │   │   │   ├── hi77.f

│   │       │   │   │   │   │   │   └── hiworld.f90

│   │       │   │   │   │   │   ├── common

│   │       │   │   │   │   │   │   └── block.f

│   │       │   │   │   │   │   ├── crackfortran

│   │       │   │   │   │   │   │   ├── accesstype.f90

│   │       │   │   │   │   │   │   ├── foo_deps.f90

│   │       │   │   │   │   │   │   ├── gh15035.f

│   │       │   │   │   │   │   │   ├── gh17859.f

│   │       │   │   │   │   │   │   ├── gh2848.f90

│   │       │   │   │   │   │   │   ├── operators.f90

│   │       │   │   │   │   │   │   ├── privatemod.f90

│   │       │   │   │   │   │   │   ├── publicmod.f90

│   │       │   │   │   │   │   │   ├── pubprivmod.f90

│   │       │   │   │   │   │   │   └── unicode_comment.f90

│   │       │   │   │   │   │   ├── f2cmap

│   │       │   │   │   │   │   │   ├── .f2py_f2cmap

│   │       │   │   │   │   │   │   └── isoFortranEnvMap.f90

│   │       │   │   │   │   │   ├── kind

│   │       │   │   │   │   │   │   └── foo.f90

│   │       │   │   │   │   │   ├── mixed

│   │       │   │   │   │   │   │   ├── foo.f

│   │       │   │   │   │   │   │   ├── foo_fixed.f90

│   │       │   │   │   │   │   │   └── foo_free.f90

│   │       │   │   │   │   │   ├── module_data

│   │       │   │   │   │   │   │   ├── mod.mod

│   │       │   │   │   │   │   │   └── module_data_docstring.f90

│   │       │   │   │   │   │   ├── negative_bounds

│   │       │   │   │   │   │   │   └── issue_20853.f90

│   │       │   │   │   │   │   ├── parameter

│   │       │   │   │   │   │   │   ├── constant_both.f90

│   │       │   │   │   │   │   │   ├── constant_compound.f90

│   │       │   │   │   │   │   │   ├── constant_integer.f90

│   │       │   │   │   │   │   │   ├── constant_non_compound.f90

│   │       │   │   │   │   │   │   └── constant_real.f90

│   │       │   │   │   │   │   ├── quoted_character

│   │       │   │   │   │   │   │   └── foo.f

│   │       │   │   │   │   │   ├── regression

│   │       │   │   │   │   │   │   └── inout.f90

│   │       │   │   │   │   │   ├── return_character

│   │       │   │   │   │   │   │   ├── foo77.f

│   │       │   │   │   │   │   │   └── foo90.f90

│   │       │   │   │   │   │   ├── return_complex

│   │       │   │   │   │   │   │   ├── foo77.f

│   │       │   │   │   │   │   │   └── foo90.f90

│   │       │   │   │   │   │   ├── return_integer

│   │       │   │   │   │   │   │   ├── foo77.f

│   │       │   │   │   │   │   │   └── foo90.f90

│   │       │   │   │   │   │   ├── return_logical

│   │       │   │   │   │   │   │   ├── foo77.f

│   │       │   │   │   │   │   │   └── foo90.f90

│   │       │   │   │   │   │   ├── return_real

│   │       │   │   │   │   │   │   ├── foo77.f

│   │       │   │   │   │   │   │   └── foo90.f90

│   │       │   │   │   │   │   ├── size

│   │       │   │   │   │   │   │   └── foo.f90

│   │       │   │   │   │   │   ├── string

│   │       │   │   │   │   │   │   ├── char.f90

│   │       │   │   │   │   │   │   ├── fixed_string.f90

│   │       │   │   │   │   │   │   ├── scalar_string.f90

│   │       │   │   │   │   │   │   └── string.f

│   │       │   │   │   │   │   └── value_attrspec

│   │       │   │   │   │   │       └── gh21665.f90

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_abstract_interface.py

│   │       │   │   │   │   ├── test_array_from_pyobj.py

│   │       │   │   │   │   ├── test_assumed_shape.py

│   │       │   │   │   │   ├── test_block_docstring.py

│   │       │   │   │   │   ├── test_callback.py

│   │       │   │   │   │   ├── test_character.py

│   │       │   │   │   │   ├── test_common.py

│   │       │   │   │   │   ├── test_compile_function.py

│   │       │   │   │   │   ├── test_crackfortran.py

│   │       │   │   │   │   ├── test_docs.py

│   │       │   │   │   │   ├── test_f2cmap.py

│   │       │   │   │   │   ├── test_f2py2e.py

│   │       │   │   │   │   ├── test_kind.py

│   │       │   │   │   │   ├── test_mixed.py

│   │       │   │   │   │   ├── test_module_doc.py

│   │       │   │   │   │   ├── test_parameter.py

│   │       │   │   │   │   ├── test_quoted_character.py

│   │       │   │   │   │   ├── test_regression.py

│   │       │   │   │   │   ├── test_return_character.py

│   │       │   │   │   │   ├── test_return_complex.py

│   │       │   │   │   │   ├── test_return_integer.py

│   │       │   │   │   │   ├── test_return_logical.py

│   │       │   │   │   │   ├── test_return_real.py

│   │       │   │   │   │   ├── test_semicolon_split.py

│   │       │   │   │   │   ├── test_size.py

│   │       │   │   │   │   ├── test_string.py

│   │       │   │   │   │   ├── test_symbolic.py

│   │       │   │   │   │   ├── test_value_attrspec.py

│   │       │   │   │   │   └── util.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── __main__.py

│   │       │   │   │   ├── __version__.py

│   │       │   │   │   ├── auxfuncs.py

│   │       │   │   │   ├── capi_maps.py

│   │       │   │   │   ├── cb_rules.py

│   │       │   │   │   ├── cfuncs.py

│   │       │   │   │   ├── common_rules.py

│   │       │   │   │   ├── crackfortran.py

│   │       │   │   │   ├── diagnose.py

│   │       │   │   │   ├── f2py2e.py

│   │       │   │   │   ├── f90mod_rules.py

│   │       │   │   │   ├── func2subr.py

│   │       │   │   │   ├── rules.py

│   │       │   │   │   ├── setup.py

│   │       │   │   │   ├── symbolic.py

│   │       │   │   │   └── use_rules.py

│   │       │   │   ├── fft

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_helper.py

│   │       │   │   │   │   └── test_pocketfft.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── _pocketfft.py

│   │       │   │   │   ├── _pocketfft.pyi

│   │       │   │   │   ├── _pocketfft_internal.cp311-win_amd64.pyd

│   │       │   │   │   ├── helper.py

│   │       │   │   │   ├── helper.pyi

│   │       │   │   │   └── setup.py

│   │       │   │   ├── lib

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── data

│   │       │   │   │   │   │   ├── py2-objarr.npy

│   │       │   │   │   │   │   ├── py2-objarr.npz

│   │       │   │   │   │   │   ├── py3-objarr.npy

│   │       │   │   │   │   │   ├── py3-objarr.npz

│   │       │   │   │   │   │   ├── python3.npy

│   │       │   │   │   │   │   └── win64python2.npy

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test__datasource.py

│   │       │   │   │   │   ├── test__iotools.py

│   │       │   │   │   │   ├── test__version.py

│   │       │   │   │   │   ├── test_arraypad.py

│   │       │   │   │   │   ├── test_arraysetops.py

│   │       │   │   │   │   ├── test_arrayterator.py

│   │       │   │   │   │   ├── test_financial_expired.py

│   │       │   │   │   │   ├── test_format.py

│   │       │   │   │   │   ├── test_function_base.py

│   │       │   │   │   │   ├── test_histograms.py

│   │       │   │   │   │   ├── test_index_tricks.py

│   │       │   │   │   │   ├── test_io.py

│   │       │   │   │   │   ├── test_loadtxt.py

│   │       │   │   │   │   ├── test_mixins.py

│   │       │   │   │   │   ├── test_nanfunctions.py

│   │       │   │   │   │   ├── test_packbits.py

│   │       │   │   │   │   ├── test_polynomial.py

│   │       │   │   │   │   ├── test_recfunctions.py

│   │       │   │   │   │   ├── test_regression.py

│   │       │   │   │   │   ├── test_shape_base.py

│   │       │   │   │   │   ├── test_stride_tricks.py

│   │       │   │   │   │   ├── test_twodim_base.py

│   │       │   │   │   │   ├── test_type_check.py

│   │       │   │   │   │   ├── test_ufunclike.py

│   │       │   │   │   │   └── test_utils.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── _datasource.py

│   │       │   │   │   ├── _iotools.py

│   │       │   │   │   ├── _version.py

│   │       │   │   │   ├── _version.pyi

│   │       │   │   │   ├── arraypad.py

│   │       │   │   │   ├── arraypad.pyi

│   │       │   │   │   ├── arraysetops.py

│   │       │   │   │   ├── arraysetops.pyi

│   │       │   │   │   ├── arrayterator.py

│   │       │   │   │   ├── arrayterator.pyi

│   │       │   │   │   ├── format.py

│   │       │   │   │   ├── format.pyi

│   │       │   │   │   ├── function_base.py

│   │       │   │   │   ├── function_base.pyi

│   │       │   │   │   ├── histograms.py

│   │       │   │   │   ├── histograms.pyi

│   │       │   │   │   ├── index_tricks.py

│   │       │   │   │   ├── index_tricks.pyi

│   │       │   │   │   ├── mixins.py

│   │       │   │   │   ├── mixins.pyi

│   │       │   │   │   ├── nanfunctions.py

│   │       │   │   │   ├── nanfunctions.pyi

│   │       │   │   │   ├── npyio.py

│   │       │   │   │   ├── npyio.pyi

│   │       │   │   │   ├── polynomial.py

│   │       │   │   │   ├── polynomial.pyi

│   │       │   │   │   ├── recfunctions.py

│   │       │   │   │   ├── scimath.py

│   │       │   │   │   ├── scimath.pyi

│   │       │   │   │   ├── setup.py

│   │       │   │   │   ├── shape_base.py

│   │       │   │   │   ├── shape_base.pyi

│   │       │   │   │   ├── stride_tricks.py

│   │       │   │   │   ├── stride_tricks.pyi

│   │       │   │   │   ├── twodim_base.py

│   │       │   │   │   ├── twodim_base.pyi

│   │       │   │   │   ├── type_check.py

│   │       │   │   │   ├── type_check.pyi

│   │       │   │   │   ├── ufunclike.py

│   │       │   │   │   ├── ufunclike.pyi

│   │       │   │   │   ├── user_array.py

│   │       │   │   │   ├── utils.py

│   │       │   │   │   └── utils.pyi

│   │       │   │   ├── linalg

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_deprecations.py

│   │       │   │   │   │   ├── test_linalg.py

│   │       │   │   │   │   └── test_regression.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── _umath_linalg.cp311-win_amd64.pyd

│   │       │   │   │   ├── lapack_lite.cp311-win_amd64.pyd

│   │       │   │   │   ├── linalg.py

│   │       │   │   │   ├── linalg.pyi

│   │       │   │   │   └── setup.py

│   │       │   │   ├── ma

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_core.py

│   │       │   │   │   │   ├── test_deprecations.py

│   │       │   │   │   │   ├── test_extras.py

│   │       │   │   │   │   ├── test_mrecords.py

│   │       │   │   │   │   ├── test_old_ma.py

│   │       │   │   │   │   ├── test_regression.py

│   │       │   │   │   │   └── test_subclassing.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── bench.py

│   │       │   │   │   ├── core.py

│   │       │   │   │   ├── core.pyi

│   │       │   │   │   ├── extras.py

│   │       │   │   │   ├── extras.pyi

│   │       │   │   │   ├── mrecords.py

│   │       │   │   │   ├── mrecords.pyi

│   │       │   │   │   ├── setup.py

│   │       │   │   │   ├── testutils.py

│   │       │   │   │   └── timer_comparison.py

│   │       │   │   ├── matrixlib

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_defmatrix.py

│   │       │   │   │   │   ├── test_interaction.py

│   │       │   │   │   │   ├── test_masked_matrix.py

│   │       │   │   │   │   ├── test_matrix_linalg.py

│   │       │   │   │   │   ├── test_multiarray.py

│   │       │   │   │   │   ├── test_numeric.py

│   │       │   │   │   │   └── test_regression.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── defmatrix.py

│   │       │   │   │   ├── defmatrix.pyi

│   │       │   │   │   └── setup.py

│   │       │   │   ├── polynomial

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_chebyshev.py

│   │       │   │   │   │   ├── test_classes.py

│   │       │   │   │   │   ├── test_hermite.py

│   │       │   │   │   │   ├── test_hermite_e.py

│   │       │   │   │   │   ├── test_laguerre.py

│   │       │   │   │   │   ├── test_legendre.py

│   │       │   │   │   │   ├── test_polynomial.py

│   │       │   │   │   │   ├── test_polyutils.py

│   │       │   │   │   │   ├── test_printing.py

│   │       │   │   │   │   └── test_symbol.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── _polybase.py

│   │       │   │   │   ├── _polybase.pyi

│   │       │   │   │   ├── chebyshev.py

│   │       │   │   │   ├── chebyshev.pyi

│   │       │   │   │   ├── hermite.py

│   │       │   │   │   ├── hermite.pyi

│   │       │   │   │   ├── hermite_e.py

│   │       │   │   │   ├── hermite_e.pyi

│   │       │   │   │   ├── laguerre.py

│   │       │   │   │   ├── laguerre.pyi

│   │       │   │   │   ├── legendre.py

│   │       │   │   │   ├── legendre.pyi

│   │       │   │   │   ├── polynomial.py

│   │       │   │   │   ├── polynomial.pyi

│   │       │   │   │   ├── polyutils.py

│   │       │   │   │   ├── polyutils.pyi

│   │       │   │   │   └── setup.py

│   │       │   │   ├── random

│   │       │   │   │   ├── _examples

│   │       │   │   │   │   ├── cffi

│   │       │   │   │   │   │   ├── extending.py

│   │       │   │   │   │   │   └── parse.py

│   │       │   │   │   │   ├── cython

│   │       │   │   │   │   │   ├── extending.pyx

│   │       │   │   │   │   │   ├── extending_distributions.pyx

│   │       │   │   │   │   │   └── setup.py

│   │       │   │   │   │   └── numba

│   │       │   │   │   │       ├── extending.py

│   │       │   │   │   │       └── extending_distributions.py

│   │       │   │   │   ├── lib

│   │       │   │   │   │   └── npyrandom.lib

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── data

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── mt19937-testset-1.csv

│   │       │   │   │   │   │   ├── mt19937-testset-2.csv

│   │       │   │   │   │   │   ├── pcg64-testset-1.csv

│   │       │   │   │   │   │   ├── pcg64-testset-2.csv

│   │       │   │   │   │   │   ├── pcg64dxsm-testset-1.csv

│   │       │   │   │   │   │   ├── pcg64dxsm-testset-2.csv

│   │       │   │   │   │   │   ├── philox-testset-1.csv

│   │       │   │   │   │   │   ├── philox-testset-2.csv

│   │       │   │   │   │   │   ├── sfc64-testset-1.csv

│   │       │   │   │   │   │   └── sfc64-testset-2.csv

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_direct.py

│   │       │   │   │   │   ├── test_extending.py

│   │       │   │   │   │   ├── test_generator_mt19937.py

│   │       │   │   │   │   ├── test_generator_mt19937_regressions.py

│   │       │   │   │   │   ├── test_random.py

│   │       │   │   │   │   ├── test_randomstate.py

│   │       │   │   │   │   ├── test_randomstate_regression.py

│   │       │   │   │   │   ├── test_regression.py

│   │       │   │   │   │   ├── test_seed_sequence.py

│   │       │   │   │   │   └── test_smoke.py

│   │       │   │   │   ├── __init__.pxd

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── _bounded_integers.cp311-win_amd64.pyd

│   │       │   │   │   ├── _bounded_integers.pxd

│   │       │   │   │   ├── _common.cp311-win_amd64.pyd

│   │       │   │   │   ├── _common.pxd

│   │       │   │   │   ├── _generator.cp311-win_amd64.pyd

│   │       │   │   │   ├── _generator.pyi

│   │       │   │   │   ├── _mt19937.cp311-win_amd64.pyd

│   │       │   │   │   ├── _mt19937.pyi

│   │       │   │   │   ├── _pcg64.cp311-win_amd64.pyd

│   │       │   │   │   ├── _pcg64.pyi

│   │       │   │   │   ├── _philox.cp311-win_amd64.pyd

│   │       │   │   │   ├── _philox.pyi

│   │       │   │   │   ├── _pickle.py

│   │       │   │   │   ├── _sfc64.cp311-win_amd64.pyd

│   │       │   │   │   ├── _sfc64.pyi

│   │       │   │   │   ├── bit_generator.cp311-win_amd64.pyd

│   │       │   │   │   ├── bit_generator.pxd

│   │       │   │   │   ├── bit_generator.pyi

│   │       │   │   │   ├── c_distributions.pxd

│   │       │   │   │   ├── mtrand.cp311-win_amd64.pyd

│   │       │   │   │   ├── mtrand.pyi

│   │       │   │   │   └── setup.py

│   │       │   │   ├── testing

│   │       │   │   │   ├── _private

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── decorators.py

│   │       │   │   │   │   ├── extbuild.py

│   │       │   │   │   │   ├── noseclasses.py

│   │       │   │   │   │   ├── nosetester.py

│   │       │   │   │   │   ├── parameterized.py

│   │       │   │   │   │   ├── utils.py

│   │       │   │   │   │   └── utils.pyi

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_doctesting.py

│   │       │   │   │   │   └── test_utils.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __init__.pyi

│   │       │   │   │   ├── print_coercion_tables.py

│   │       │   │   │   ├── setup.py

│   │       │   │   │   └── utils.py

│   │       │   │   ├── tests

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── test__all__.py

│   │       │   │   │   ├── test_ctypeslib.py

│   │       │   │   │   ├── test_lazyloading.py

│   │       │   │   │   ├── test_matlib.py

│   │       │   │   │   ├── test_numpy_version.py

│   │       │   │   │   ├── test_public_api.py

│   │       │   │   │   ├── test_reloading.py

│   │       │   │   │   ├── test_scripts.py

│   │       │   │   │   └── test_warnings.py

│   │       │   │   ├── typing

│   │       │   │   │   ├── tests

│   │       │   │   │   │   ├── data

│   │       │   │   │   │   │   ├── fail

│   │       │   │   │   │   │   │   ├── arithmetic.pyi

│   │       │   │   │   │   │   │   ├── array_constructors.pyi

│   │       │   │   │   │   │   │   ├── array_like.pyi

│   │       │   │   │   │   │   │   ├── array_pad.pyi

│   │       │   │   │   │   │   │   ├── arrayprint.pyi

│   │       │   │   │   │   │   │   ├── arrayterator.pyi

│   │       │   │   │   │   │   │   ├── bitwise_ops.pyi

│   │       │   │   │   │   │   │   ├── char.pyi

│   │       │   │   │   │   │   │   ├── chararray.pyi

│   │       │   │   │   │   │   │   ├── comparisons.pyi

│   │       │   │   │   │   │   │   ├── constants.pyi

│   │       │   │   │   │   │   │   ├── datasource.pyi

│   │       │   │   │   │   │   │   ├── dtype.pyi

│   │       │   │   │   │   │   │   ├── einsumfunc.pyi

│   │       │   │   │   │   │   │   ├── false_positives.pyi

│   │       │   │   │   │   │   │   ├── flatiter.pyi

│   │       │   │   │   │   │   │   ├── fromnumeric.pyi

│   │       │   │   │   │   │   │   ├── histograms.pyi

│   │       │   │   │   │   │   │   ├── index_tricks.pyi

│   │       │   │   │   │   │   │   ├── lib_function_base.pyi

│   │       │   │   │   │   │   │   ├── lib_polynomial.pyi

│   │       │   │   │   │   │   │   ├── lib_utils.pyi

│   │       │   │   │   │   │   │   ├── lib_version.pyi

│   │       │   │   │   │   │   │   ├── linalg.pyi

│   │       │   │   │   │   │   │   ├── memmap.pyi

│   │       │   │   │   │   │   │   ├── modules.pyi

│   │       │   │   │   │   │   │   ├── multiarray.pyi

│   │       │   │   │   │   │   │   ├── ndarray.pyi

│   │       │   │   │   │   │   │   ├── ndarray_misc.pyi

│   │       │   │   │   │   │   │   ├── nditer.pyi

│   │       │   │   │   │   │   │   ├── nested_sequence.pyi

│   │       │   │   │   │   │   │   ├── npyio.pyi

│   │       │   │   │   │   │   │   ├── numerictypes.pyi

│   │       │   │   │   │   │   │   ├── random.pyi

│   │       │   │   │   │   │   │   ├── rec.pyi

│   │       │   │   │   │   │   │   ├── scalars.pyi

│   │       │   │   │   │   │   │   ├── shape_base.pyi

│   │       │   │   │   │   │   │   ├── stride_tricks.pyi

│   │       │   │   │   │   │   │   ├── testing.pyi

│   │       │   │   │   │   │   │   ├── twodim_base.pyi

│   │       │   │   │   │   │   │   ├── type_check.pyi

│   │       │   │   │   │   │   │   ├── ufunc_config.pyi

│   │       │   │   │   │   │   │   ├── ufunclike.pyi

│   │       │   │   │   │   │   │   ├── ufuncs.pyi

│   │       │   │   │   │   │   │   └── warnings_and_errors.pyi

│   │       │   │   │   │   │   ├── misc

│   │       │   │   │   │   │   │   └── extended_precision.pyi

│   │       │   │   │   │   │   ├── pass

│   │       │   │   │   │   │   │   ├── arithmetic.py

│   │       │   │   │   │   │   │   ├── array_constructors.py

│   │       │   │   │   │   │   │   ├── array_like.py

│   │       │   │   │   │   │   │   ├── arrayprint.py

│   │       │   │   │   │   │   │   ├── arrayterator.py

│   │       │   │   │   │   │   │   ├── bitwise_ops.py

│   │       │   │   │   │   │   │   ├── comparisons.py

│   │       │   │   │   │   │   │   ├── dtype.py

│   │       │   │   │   │   │   │   ├── einsumfunc.py

│   │       │   │   │   │   │   │   ├── flatiter.py

│   │       │   │   │   │   │   │   ├── fromnumeric.py

│   │       │   │   │   │   │   │   ├── index_tricks.py

│   │       │   │   │   │   │   │   ├── lib_utils.py

│   │       │   │   │   │   │   │   ├── lib_version.py

│   │       │   │   │   │   │   │   ├── literal.py

│   │       │   │   │   │   │   │   ├── mod.py

│   │       │   │   │   │   │   │   ├── modules.py

│   │       │   │   │   │   │   │   ├── multiarray.py

│   │       │   │   │   │   │   │   ├── ndarray_conversion.py

│   │       │   │   │   │   │   │   ├── ndarray_misc.py

│   │       │   │   │   │   │   │   ├── ndarray_shape_manipulation.py

│   │       │   │   │   │   │   │   ├── numeric.py

│   │       │   │   │   │   │   │   ├── numerictypes.py

│   │       │   │   │   │   │   │   ├── random.py

│   │       │   │   │   │   │   │   ├── scalars.py

│   │       │   │   │   │   │   │   ├── simple.py

│   │       │   │   │   │   │   │   ├── simple_py3.py

│   │       │   │   │   │   │   │   ├── ufunc_config.py

│   │       │   │   │   │   │   │   ├── ufunclike.py

│   │       │   │   │   │   │   │   ├── ufuncs.py

│   │       │   │   │   │   │   │   └── warnings_and_errors.py

│   │       │   │   │   │   │   ├── reveal

│   │       │   │   │   │   │   │   ├── arithmetic.pyi

│   │       │   │   │   │   │   │   ├── array_constructors.pyi

│   │       │   │   │   │   │   │   ├── arraypad.pyi

│   │       │   │   │   │   │   │   ├── arrayprint.pyi

│   │       │   │   │   │   │   │   ├── arraysetops.pyi

│   │       │   │   │   │   │   │   ├── arrayterator.pyi

│   │       │   │   │   │   │   │   ├── bitwise_ops.pyi

│   │       │   │   │   │   │   │   ├── char.pyi

│   │       │   │   │   │   │   │   ├── chararray.pyi

│   │       │   │   │   │   │   │   ├── comparisons.pyi

│   │       │   │   │   │   │   │   ├── constants.pyi

│   │       │   │   │   │   │   │   ├── ctypeslib.pyi

│   │       │   │   │   │   │   │   ├── datasource.pyi

│   │       │   │   │   │   │   │   ├── dtype.pyi

│   │       │   │   │   │   │   │   ├── einsumfunc.pyi

│   │       │   │   │   │   │   │   ├── emath.pyi

│   │       │   │   │   │   │   │   ├── false_positives.pyi

│   │       │   │   │   │   │   │   ├── fft.pyi

│   │       │   │   │   │   │   │   ├── flatiter.pyi

│   │       │   │   │   │   │   │   ├── fromnumeric.pyi

│   │       │   │   │   │   │   │   ├── getlimits.pyi

│   │       │   │   │   │   │   │   ├── histograms.pyi

│   │       │   │   │   │   │   │   ├── index_tricks.pyi

│   │       │   │   │   │   │   │   ├── lib_function_base.pyi

│   │       │   │   │   │   │   │   ├── lib_polynomial.pyi

│   │       │   │   │   │   │   │   ├── lib_utils.pyi

│   │       │   │   │   │   │   │   ├── lib_version.pyi

│   │       │   │   │   │   │   │   ├── linalg.pyi

│   │       │   │   │   │   │   │   ├── matrix.pyi

│   │       │   │   │   │   │   │   ├── memmap.pyi

│   │       │   │   │   │   │   │   ├── mod.pyi

│   │       │   │   │   │   │   │   ├── modules.pyi

│   │       │   │   │   │   │   │   ├── multiarray.pyi

│   │       │   │   │   │   │   │   ├── nbit_base_example.pyi

│   │       │   │   │   │   │   │   ├── ndarray_conversion.pyi

│   │       │   │   │   │   │   │   ├── ndarray_misc.pyi

│   │       │   │   │   │   │   │   ├── ndarray_shape_manipulation.pyi

│   │       │   │   │   │   │   │   ├── nditer.pyi

│   │       │   │   │   │   │   │   ├── nested_sequence.pyi

│   │       │   │   │   │   │   │   ├── npyio.pyi

│   │       │   │   │   │   │   │   ├── numeric.pyi

│   │       │   │   │   │   │   │   ├── numerictypes.pyi

│   │       │   │   │   │   │   │   ├── random.pyi

│   │       │   │   │   │   │   │   ├── rec.pyi

│   │       │   │   │   │   │   │   ├── scalars.pyi

│   │       │   │   │   │   │   │   ├── shape_base.pyi

│   │       │   │   │   │   │   │   ├── stride_tricks.pyi

│   │       │   │   │   │   │   │   ├── testing.pyi

│   │       │   │   │   │   │   │   ├── twodim_base.pyi

│   │       │   │   │   │   │   │   ├── type_check.pyi

│   │       │   │   │   │   │   │   ├── ufunc_config.pyi

│   │       │   │   │   │   │   │   ├── ufunclike.pyi

│   │       │   │   │   │   │   │   ├── ufuncs.pyi

│   │       │   │   │   │   │   │   ├── version.pyi

│   │       │   │   │   │   │   │   └── warnings_and_errors.pyi

│   │       │   │   │   │   │   └── mypy.ini

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── test_generic_alias.py

│   │       │   │   │   │   ├── test_isfile.py

│   │       │   │   │   │   ├── test_runtime.py

│   │       │   │   │   │   └── test_typing.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── mypy_plugin.py

│   │       │   │   │   └── setup.py

│   │       │   │   ├── LICENSE.txt

│   │       │   │   ├── __config__.py

│   │       │   │   ├── __init__.cython-30.pxd

│   │       │   │   ├── __init__.pxd

│   │       │   │   ├── __init__.py

│   │       │   │   ├── __init__.pyi

│   │       │   │   ├── _distributor_init.py

│   │       │   │   ├── _globals.py

│   │       │   │   ├── _pytesttester.py

│   │       │   │   ├── _pytesttester.pyi

│   │       │   │   ├── _version.py

│   │       │   │   ├── conftest.py

│   │       │   │   ├── ctypeslib.py

│   │       │   │   ├── ctypeslib.pyi

│   │       │   │   ├── dual.py

│   │       │   │   ├── matlib.py

│   │       │   │   ├── py.typed

│   │       │   │   ├── setup.py

│   │       │   │   └── version.py

│   │       │   ├── numpy-1.24.3-py3.11.egg-info

│   │       │   │   ├── PKG-INFO

│   │       │   │   ├── SOURCES.txt

│   │       │   │   ├── dependency_links.txt

│   │       │   │   ├── entry_points.txt

│   │       │   │   ├── not-zip-safe

│   │       │   │   └── top_level.txt

│   │       │   ├── pip

│   │       │   │   ├── _internal

│   │       │   │   │   ├── cli

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── autocompletion.py

│   │       │   │   │   │   ├── base_command.py

│   │       │   │   │   │   ├── cmdoptions.py

│   │       │   │   │   │   ├── command_context.py

│   │       │   │   │   │   ├── main.py

│   │       │   │   │   │   ├── main_parser.py

│   │       │   │   │   │   ├── parser.py

│   │       │   │   │   │   ├── progress_bars.py

│   │       │   │   │   │   ├── req_command.py

│   │       │   │   │   │   ├── spinners.py

│   │       │   │   │   │   └── status_codes.py

│   │       │   │   │   ├── commands

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── cache.py

│   │       │   │   │   │   ├── check.py

│   │       │   │   │   │   ├── completion.py

│   │       │   │   │   │   ├── configuration.py

│   │       │   │   │   │   ├── debug.py

│   │       │   │   │   │   ├── download.py

│   │       │   │   │   │   ├── freeze.py

│   │       │   │   │   │   ├── hash.py

│   │       │   │   │   │   ├── help.py

│   │       │   │   │   │   ├── index.py

│   │       │   │   │   │   ├── inspect.py

│   │       │   │   │   │   ├── install.py

│   │       │   │   │   │   ├── list.py

│   │       │   │   │   │   ├── search.py

│   │       │   │   │   │   ├── show.py

│   │       │   │   │   │   ├── uninstall.py

│   │       │   │   │   │   └── wheel.py

│   │       │   │   │   ├── distributions

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── base.py

│   │       │   │   │   │   ├── installed.py

│   │       │   │   │   │   ├── sdist.py

│   │       │   │   │   │   └── wheel.py

│   │       │   │   │   ├── index

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── collector.py

│   │       │   │   │   │   ├── package_finder.py

│   │       │   │   │   │   └── sources.py

│   │       │   │   │   ├── locations

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _distutils.py

│   │       │   │   │   │   ├── _sysconfig.py

│   │       │   │   │   │   └── base.py

│   │       │   │   │   ├── metadata

│   │       │   │   │   │   ├── importlib

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── _compat.py

│   │       │   │   │   │   │   ├── _dists.py

│   │       │   │   │   │   │   └── _envs.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _json.py

│   │       │   │   │   │   ├── base.py

│   │       │   │   │   │   └── pkg_resources.py

│   │       │   │   │   ├── models

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── candidate.py

│   │       │   │   │   │   ├── direct_url.py

│   │       │   │   │   │   ├── format_control.py

│   │       │   │   │   │   ├── index.py

│   │       │   │   │   │   ├── installation_report.py

│   │       │   │   │   │   ├── link.py

│   │       │   │   │   │   ├── scheme.py

│   │       │   │   │   │   ├── search_scope.py

│   │       │   │   │   │   ├── selection_prefs.py

│   │       │   │   │   │   ├── target_python.py

│   │       │   │   │   │   └── wheel.py

│   │       │   │   │   ├── network

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── auth.py

│   │       │   │   │   │   ├── cache.py

│   │       │   │   │   │   ├── download.py

│   │       │   │   │   │   ├── lazy_wheel.py

│   │       │   │   │   │   ├── session.py

│   │       │   │   │   │   ├── utils.py

│   │       │   │   │   │   └── xmlrpc.py

│   │       │   │   │   ├── operations

│   │       │   │   │   │   ├── build

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── build_tracker.py

│   │       │   │   │   │   │   ├── metadata.py

│   │       │   │   │   │   │   ├── metadata_editable.py

│   │       │   │   │   │   │   ├── metadata_legacy.py

│   │       │   │   │   │   │   ├── wheel.py

│   │       │   │   │   │   │   ├── wheel_editable.py

│   │       │   │   │   │   │   └── wheel_legacy.py

│   │       │   │   │   │   ├── install

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── editable_legacy.py

│   │       │   │   │   │   │   └── wheel.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── check.py

│   │       │   │   │   │   ├── freeze.py

│   │       │   │   │   │   └── prepare.py

│   │       │   │   │   ├── req

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── constructors.py

│   │       │   │   │   │   ├── req_file.py

│   │       │   │   │   │   ├── req_install.py

│   │       │   │   │   │   ├── req_set.py

│   │       │   │   │   │   └── req_uninstall.py

│   │       │   │   │   ├── resolution

│   │       │   │   │   │   ├── legacy

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   └── resolver.py

│   │       │   │   │   │   ├── resolvelib

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── base.py

│   │       │   │   │   │   │   ├── candidates.py

│   │       │   │   │   │   │   ├── factory.py

│   │       │   │   │   │   │   ├── found_candidates.py

│   │       │   │   │   │   │   ├── provider.py

│   │       │   │   │   │   │   ├── reporter.py

│   │       │   │   │   │   │   ├── requirements.py

│   │       │   │   │   │   │   └── resolver.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   └── base.py

│   │       │   │   │   ├── utils

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _jaraco_text.py

│   │       │   │   │   │   ├── _log.py

│   │       │   │   │   │   ├── appdirs.py

│   │       │   │   │   │   ├── compat.py

│   │       │   │   │   │   ├── compatibility_tags.py

│   │       │   │   │   │   ├── datetime.py

│   │       │   │   │   │   ├── deprecation.py

│   │       │   │   │   │   ├── direct_url_helpers.py

│   │       │   │   │   │   ├── egg_link.py

│   │       │   │   │   │   ├── encoding.py

│   │       │   │   │   │   ├── entrypoints.py

│   │       │   │   │   │   ├── filesystem.py

│   │       │   │   │   │   ├── filetypes.py

│   │       │   │   │   │   ├── glibc.py

│   │       │   │   │   │   ├── hashes.py

│   │       │   │   │   │   ├── inject_securetransport.py

│   │       │   │   │   │   ├── logging.py

│   │       │   │   │   │   ├── misc.py

│   │       │   │   │   │   ├── models.py

│   │       │   │   │   │   ├── packaging.py

│   │       │   │   │   │   ├── setuptools_build.py

│   │       │   │   │   │   ├── subprocess.py

│   │       │   │   │   │   ├── temp_dir.py

│   │       │   │   │   │   ├── unpacking.py

│   │       │   │   │   │   ├── urls.py

│   │       │   │   │   │   ├── virtualenv.py

│   │       │   │   │   │   └── wheel.py

│   │       │   │   │   ├── vcs

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── bazaar.py

│   │       │   │   │   │   ├── git.py

│   │       │   │   │   │   ├── mercurial.py

│   │       │   │   │   │   ├── subversion.py

│   │       │   │   │   │   └── versioncontrol.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── build_env.py

│   │       │   │   │   ├── cache.py

│   │       │   │   │   ├── configuration.py

│   │       │   │   │   ├── exceptions.py

│   │       │   │   │   ├── main.py

│   │       │   │   │   ├── pyproject.py

│   │       │   │   │   ├── self_outdated_check.py

│   │       │   │   │   └── wheel_builder.py

│   │       │   │   ├── _vendor

│   │       │   │   │   ├── cachecontrol

│   │       │   │   │   │   ├── caches

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── file_cache.py

│   │       │   │   │   │   │   └── redis_cache.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _cmd.py

│   │       │   │   │   │   ├── adapter.py

│   │       │   │   │   │   ├── cache.py

│   │       │   │   │   │   ├── compat.py

│   │       │   │   │   │   ├── controller.py

│   │       │   │   │   │   ├── filewrapper.py

│   │       │   │   │   │   ├── heuristics.py

│   │       │   │   │   │   ├── serialize.py

│   │       │   │   │   │   └── wrapper.py

│   │       │   │   │   ├── certifi

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── __main__.py

│   │       │   │   │   │   ├── cacert.pem

│   │       │   │   │   │   └── core.py

│   │       │   │   │   ├── chardet

│   │       │   │   │   │   ├── cli

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   └── chardetect.py

│   │       │   │   │   │   ├── metadata

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   └── languages.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── big5freq.py

│   │       │   │   │   │   ├── big5prober.py

│   │       │   │   │   │   ├── chardistribution.py

│   │       │   │   │   │   ├── charsetgroupprober.py

│   │       │   │   │   │   ├── charsetprober.py

│   │       │   │   │   │   ├── codingstatemachine.py

│   │       │   │   │   │   ├── codingstatemachinedict.py

│   │       │   │   │   │   ├── cp949prober.py

│   │       │   │   │   │   ├── enums.py

│   │       │   │   │   │   ├── escprober.py

│   │       │   │   │   │   ├── escsm.py

│   │       │   │   │   │   ├── eucjpprober.py

│   │       │   │   │   │   ├── euckrfreq.py

│   │       │   │   │   │   ├── euckrprober.py

│   │       │   │   │   │   ├── euctwfreq.py

│   │       │   │   │   │   ├── euctwprober.py

│   │       │   │   │   │   ├── gb2312freq.py

│   │       │   │   │   │   ├── gb2312prober.py

│   │       │   │   │   │   ├── hebrewprober.py

│   │       │   │   │   │   ├── jisfreq.py

│   │       │   │   │   │   ├── johabfreq.py

│   │       │   │   │   │   ├── johabprober.py

│   │       │   │   │   │   ├── jpcntx.py

│   │       │   │   │   │   ├── langbulgarianmodel.py

│   │       │   │   │   │   ├── langgreekmodel.py

│   │       │   │   │   │   ├── langhebrewmodel.py

│   │       │   │   │   │   ├── langhungarianmodel.py

│   │       │   │   │   │   ├── langrussianmodel.py

│   │       │   │   │   │   ├── langthaimodel.py

│   │       │   │   │   │   ├── langturkishmodel.py

│   │       │   │   │   │   ├── latin1prober.py

│   │       │   │   │   │   ├── macromanprober.py

│   │       │   │   │   │   ├── mbcharsetprober.py

│   │       │   │   │   │   ├── mbcsgroupprober.py

│   │       │   │   │   │   ├── mbcssm.py

│   │       │   │   │   │   ├── resultdict.py

│   │       │   │   │   │   ├── sbcharsetprober.py

│   │       │   │   │   │   ├── sbcsgroupprober.py

│   │       │   │   │   │   ├── sjisprober.py

│   │       │   │   │   │   ├── universaldetector.py

│   │       │   │   │   │   ├── utf1632prober.py

│   │       │   │   │   │   ├── utf8prober.py

│   │       │   │   │   │   └── version.py

│   │       │   │   │   ├── colorama

│   │       │   │   │   │   ├── tests

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── ansi_test.py

│   │       │   │   │   │   │   ├── ansitowin32_test.py

│   │       │   │   │   │   │   ├── initialise_test.py

│   │       │   │   │   │   │   ├── isatty_test.py

│   │       │   │   │   │   │   ├── utils.py

│   │       │   │   │   │   │   └── winterm_test.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── ansi.py

│   │       │   │   │   │   ├── ansitowin32.py

│   │       │   │   │   │   ├── initialise.py

│   │       │   │   │   │   ├── win32.py

│   │       │   │   │   │   └── winterm.py

│   │       │   │   │   ├── distlib

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── compat.py

│   │       │   │   │   │   ├── database.py

│   │       │   │   │   │   ├── index.py

│   │       │   │   │   │   ├── locators.py

│   │       │   │   │   │   ├── manifest.py

│   │       │   │   │   │   ├── markers.py

│   │       │   │   │   │   ├── metadata.py

│   │       │   │   │   │   ├── resources.py

│   │       │   │   │   │   ├── scripts.py

│   │       │   │   │   │   ├── t32.exe

│   │       │   │   │   │   ├── t64-arm.exe

│   │       │   │   │   │   ├── t64.exe

│   │       │   │   │   │   ├── util.py

│   │       │   │   │   │   ├── version.py

│   │       │   │   │   │   ├── w32.exe

│   │       │   │   │   │   ├── w64-arm.exe

│   │       │   │   │   │   ├── w64.exe

│   │       │   │   │   │   └── wheel.py

│   │       │   │   │   ├── distro

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── __main__.py

│   │       │   │   │   │   └── distro.py

│   │       │   │   │   ├── idna

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── codec.py

│   │       │   │   │   │   ├── compat.py

│   │       │   │   │   │   ├── core.py

│   │       │   │   │   │   ├── idnadata.py

│   │       │   │   │   │   ├── intranges.py

│   │       │   │   │   │   ├── package_data.py

│   │       │   │   │   │   └── uts46data.py

│   │       │   │   │   ├── msgpack

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── exceptions.py

│   │       │   │   │   │   ├── ext.py

│   │       │   │   │   │   └── fallback.py

│   │       │   │   │   ├── packaging

│   │       │   │   │   │   ├── __about__.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _manylinux.py

│   │       │   │   │   │   ├── _musllinux.py

│   │       │   │   │   │   ├── _structures.py

│   │       │   │   │   │   ├── markers.py

│   │       │   │   │   │   ├── requirements.py

│   │       │   │   │   │   ├── specifiers.py

│   │       │   │   │   │   ├── tags.py

│   │       │   │   │   │   ├── utils.py

│   │       │   │   │   │   └── version.py

│   │       │   │   │   ├── pkg_resources

│   │       │   │   │   │   └── __init__.py

│   │       │   │   │   ├── platformdirs

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── __main__.py

│   │       │   │   │   │   ├── android.py

│   │       │   │   │   │   ├── api.py

│   │       │   │   │   │   ├── macos.py

│   │       │   │   │   │   ├── unix.py

│   │       │   │   │   │   ├── version.py

│   │       │   │   │   │   └── windows.py

│   │       │   │   │   ├── pygments

│   │       │   │   │   │   ├── filters

│   │       │   │   │   │   │   └── __init__.py

│   │       │   │   │   │   ├── formatters

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── _mapping.py

│   │       │   │   │   │   │   ├── bbcode.py

│   │       │   │   │   │   │   ├── groff.py

│   │       │   │   │   │   │   ├── html.py

│   │       │   │   │   │   │   ├── img.py

│   │       │   │   │   │   │   ├── irc.py

│   │       │   │   │   │   │   ├── latex.py

│   │       │   │   │   │   │   ├── other.py

│   │       │   │   │   │   │   ├── pangomarkup.py

│   │       │   │   │   │   │   ├── rtf.py

│   │       │   │   │   │   │   ├── svg.py

│   │       │   │   │   │   │   ├── terminal.py

│   │       │   │   │   │   │   └── terminal256.py

│   │       │   │   │   │   ├── lexers

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── _mapping.py

│   │       │   │   │   │   │   └── python.py

│   │       │   │   │   │   ├── styles

│   │       │   │   │   │   │   └── __init__.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── __main__.py

│   │       │   │   │   │   ├── cmdline.py

│   │       │   │   │   │   ├── console.py

│   │       │   │   │   │   ├── filter.py

│   │       │   │   │   │   ├── formatter.py

│   │       │   │   │   │   ├── lexer.py

│   │       │   │   │   │   ├── modeline.py

│   │       │   │   │   │   ├── plugin.py

│   │       │   │   │   │   ├── regexopt.py

│   │       │   │   │   │   ├── scanner.py

│   │       │   │   │   │   ├── sphinxext.py

│   │       │   │   │   │   ├── style.py

│   │       │   │   │   │   ├── token.py

│   │       │   │   │   │   ├── unistring.py

│   │       │   │   │   │   └── util.py

│   │       │   │   │   ├── pyparsing

│   │       │   │   │   │   ├── diagram

│   │       │   │   │   │   │   └── __init__.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── actions.py

│   │       │   │   │   │   ├── common.py

│   │       │   │   │   │   ├── core.py

│   │       │   │   │   │   ├── exceptions.py

│   │       │   │   │   │   ├── helpers.py

│   │       │   │   │   │   ├── results.py

│   │       │   │   │   │   ├── testing.py

│   │       │   │   │   │   ├── unicode.py

│   │       │   │   │   │   └── util.py

│   │       │   │   │   ├── pyproject_hooks

│   │       │   │   │   │   ├── _in_process

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   └── _in_process.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _compat.py

│   │       │   │   │   │   └── _impl.py

│   │       │   │   │   ├── requests

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── __version__.py

│   │       │   │   │   │   ├── _internal_utils.py

│   │       │   │   │   │   ├── adapters.py

│   │       │   │   │   │   ├── api.py

│   │       │   │   │   │   ├── auth.py

│   │       │   │   │   │   ├── certs.py

│   │       │   │   │   │   ├── compat.py

│   │       │   │   │   │   ├── cookies.py

│   │       │   │   │   │   ├── exceptions.py

│   │       │   │   │   │   ├── help.py

│   │       │   │   │   │   ├── hooks.py

│   │       │   │   │   │   ├── models.py

│   │       │   │   │   │   ├── packages.py

│   │       │   │   │   │   ├── sessions.py

│   │       │   │   │   │   ├── status_codes.py

│   │       │   │   │   │   ├── structures.py

│   │       │   │   │   │   └── utils.py

│   │       │   │   │   ├── resolvelib

│   │       │   │   │   │   ├── compat

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   └── collections_abc.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── providers.py

│   │       │   │   │   │   ├── reporters.py

│   │       │   │   │   │   ├── resolvers.py

│   │       │   │   │   │   └── structs.py

│   │       │   │   │   ├── rich

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── __main__.py

│   │       │   │   │   │   ├── _cell_widths.py

│   │       │   │   │   │   ├── _emoji_codes.py

│   │       │   │   │   │   ├── _emoji_replace.py

│   │       │   │   │   │   ├── _export_format.py

│   │       │   │   │   │   ├── _extension.py

│   │       │   │   │   │   ├── _fileno.py

│   │       │   │   │   │   ├── _inspect.py

│   │       │   │   │   │   ├── _log_render.py

│   │       │   │   │   │   ├── _loop.py

│   │       │   │   │   │   ├── _null_file.py

│   │       │   │   │   │   ├── _palettes.py

│   │       │   │   │   │   ├── _pick.py

│   │       │   │   │   │   ├── _ratio.py

│   │       │   │   │   │   ├── _spinners.py

│   │       │   │   │   │   ├── _stack.py

│   │       │   │   │   │   ├── _timer.py

│   │       │   │   │   │   ├── _win32_console.py

│   │       │   │   │   │   ├── _windows.py

│   │       │   │   │   │   ├── _windows_renderer.py

│   │       │   │   │   │   ├── _wrap.py

│   │       │   │   │   │   ├── abc.py

│   │       │   │   │   │   ├── align.py

│   │       │   │   │   │   ├── ansi.py

│   │       │   │   │   │   ├── bar.py

│   │       │   │   │   │   ├── box.py

│   │       │   │   │   │   ├── cells.py

│   │       │   │   │   │   ├── color.py

│   │       │   │   │   │   ├── color_triplet.py

│   │       │   │   │   │   ├── columns.py

│   │       │   │   │   │   ├── console.py

│   │       │   │   │   │   ├── constrain.py

│   │       │   │   │   │   ├── containers.py

│   │       │   │   │   │   ├── control.py

│   │       │   │   │   │   ├── default_styles.py

│   │       │   │   │   │   ├── diagnose.py

│   │       │   │   │   │   ├── emoji.py

│   │       │   │   │   │   ├── errors.py

│   │       │   │   │   │   ├── file_proxy.py

│   │       │   │   │   │   ├── filesize.py

│   │       │   │   │   │   ├── highlighter.py

│   │       │   │   │   │   ├── json.py

│   │       │   │   │   │   ├── jupyter.py

│   │       │   │   │   │   ├── layout.py

│   │       │   │   │   │   ├── live.py

│   │       │   │   │   │   ├── live_render.py

│   │       │   │   │   │   ├── logging.py

│   │       │   │   │   │   ├── markup.py

│   │       │   │   │   │   ├── measure.py

│   │       │   │   │   │   ├── padding.py

│   │       │   │   │   │   ├── pager.py

│   │       │   │   │   │   ├── palette.py

│   │       │   │   │   │   ├── panel.py

│   │       │   │   │   │   ├── pretty.py

│   │       │   │   │   │   ├── progress.py

│   │       │   │   │   │   ├── progress_bar.py

│   │       │   │   │   │   ├── prompt.py

│   │       │   │   │   │   ├── protocol.py

│   │       │   │   │   │   ├── region.py

│   │       │   │   │   │   ├── repr.py

│   │       │   │   │   │   ├── rule.py

│   │       │   │   │   │   ├── scope.py

│   │       │   │   │   │   ├── screen.py

│   │       │   │   │   │   ├── segment.py

│   │       │   │   │   │   ├── spinner.py

│   │       │   │   │   │   ├── status.py

│   │       │   │   │   │   ├── style.py

│   │       │   │   │   │   ├── styled.py

│   │       │   │   │   │   ├── syntax.py

│   │       │   │   │   │   ├── table.py

│   │       │   │   │   │   ├── terminal_theme.py

│   │       │   │   │   │   ├── text.py

│   │       │   │   │   │   ├── theme.py

│   │       │   │   │   │   ├── themes.py

│   │       │   │   │   │   ├── traceback.py

│   │       │   │   │   │   └── tree.py

│   │       │   │   │   ├── tenacity

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _asyncio.py

│   │       │   │   │   │   ├── _utils.py

│   │       │   │   │   │   ├── after.py

│   │       │   │   │   │   ├── before.py

│   │       │   │   │   │   ├── before_sleep.py

│   │       │   │   │   │   ├── nap.py

│   │       │   │   │   │   ├── retry.py

│   │       │   │   │   │   ├── stop.py

│   │       │   │   │   │   ├── tornadoweb.py

│   │       │   │   │   │   └── wait.py

│   │       │   │   │   ├── tomli

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _parser.py

│   │       │   │   │   │   ├── _re.py

│   │       │   │   │   │   └── _types.py

│   │       │   │   │   ├── urllib3

│   │       │   │   │   │   ├── contrib

│   │       │   │   │   │   │   ├── _securetransport

│   │       │   │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   │   ├── bindings.py

│   │       │   │   │   │   │   │   └── low_level.py

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── _appengine_environ.py

│   │       │   │   │   │   │   ├── appengine.py

│   │       │   │   │   │   │   ├── ntlmpool.py

│   │       │   │   │   │   │   ├── pyopenssl.py

│   │       │   │   │   │   │   ├── securetransport.py

│   │       │   │   │   │   │   └── socks.py

│   │       │   │   │   │   ├── packages

│   │       │   │   │   │   │   ├── backports

│   │       │   │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   │   ├── makefile.py

│   │       │   │   │   │   │   │   └── weakref_finalize.py

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   └── six.py

│   │       │   │   │   │   ├── util

│   │       │   │   │   │   │   ├── __init__.py

│   │       │   │   │   │   │   ├── connection.py

│   │       │   │   │   │   │   ├── proxy.py

│   │       │   │   │   │   │   ├── queue.py

│   │       │   │   │   │   │   ├── request.py

│   │       │   │   │   │   │   ├── response.py

│   │       │   │   │   │   │   ├── retry.py

│   │       │   │   │   │   │   ├── ssl_.py

│   │       │   │   │   │   │   ├── ssl_match_hostname.py

│   │       │   │   │   │   │   ├── ssltransport.py

│   │       │   │   │   │   │   ├── timeout.py

│   │       │   │   │   │   │   ├── url.py

│   │       │   │   │   │   │   └── wait.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _collections.py

│   │       │   │   │   │   ├── _version.py

│   │       │   │   │   │   ├── connection.py

│   │       │   │   │   │   ├── connectionpool.py

│   │       │   │   │   │   ├── exceptions.py

│   │       │   │   │   │   ├── fields.py

│   │       │   │   │   │   ├── filepost.py

│   │       │   │   │   │   ├── poolmanager.py

│   │       │   │   │   │   ├── request.py

│   │       │   │   │   │   └── response.py

│   │       │   │   │   ├── webencodings

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── labels.py

│   │       │   │   │   │   ├── mklabels.py

│   │       │   │   │   │   ├── tests.py

│   │       │   │   │   │   └── x_user_defined.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── six.py

│   │       │   │   │   ├── typing_extensions.py

│   │       │   │   │   └── vendor.txt

│   │       │   │   ├── __init__.py

│   │       │   │   ├── __main__.py

│   │       │   │   ├── __pip-runner__.py

│   │       │   │   └── py.typed

│   │       │   ├── pip-23.2.1.dist-info

│   │       │   │   ├── AUTHORS.txt

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE.txt

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   ├── entry_points.txt

│   │       │   │   └── top_level.txt

│   │       │   ├── pkg_resources

│   │       │   │   ├── _vendor

│   │       │   │   │   ├── importlib_resources

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _adapters.py

│   │       │   │   │   │   ├── _common.py

│   │       │   │   │   │   ├── _compat.py

│   │       │   │   │   │   ├── _itertools.py

│   │       │   │   │   │   ├── _legacy.py

│   │       │   │   │   │   ├── abc.py

│   │       │   │   │   │   ├── readers.py

│   │       │   │   │   │   └── simple.py

│   │       │   │   │   ├── jaraco

│   │       │   │   │   │   ├── text

│   │       │   │   │   │   │   └── __init__.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── context.py

│   │       │   │   │   │   └── functools.py

│   │       │   │   │   ├── more_itertools

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── more.py

│   │       │   │   │   │   └── recipes.py

│   │       │   │   │   ├── packaging

│   │       │   │   │   │   ├── __about__.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _manylinux.py

│   │       │   │   │   │   ├── _musllinux.py

│   │       │   │   │   │   ├── _structures.py

│   │       │   │   │   │   ├── markers.py

│   │       │   │   │   │   ├── requirements.py

│   │       │   │   │   │   ├── specifiers.py

│   │       │   │   │   │   ├── tags.py

│   │       │   │   │   │   ├── utils.py

│   │       │   │   │   │   └── version.py

│   │       │   │   │   ├── pyparsing

│   │       │   │   │   │   ├── diagram

│   │       │   │   │   │   │   └── __init__.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── actions.py

│   │       │   │   │   │   ├── common.py

│   │       │   │   │   │   ├── core.py

│   │       │   │   │   │   ├── exceptions.py

│   │       │   │   │   │   ├── helpers.py

│   │       │   │   │   │   ├── results.py

│   │       │   │   │   │   ├── testing.py

│   │       │   │   │   │   ├── unicode.py

│   │       │   │   │   │   └── util.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── appdirs.py

│   │       │   │   │   └── zipp.py

│   │       │   │   ├── extern

│   │       │   │   │   └── __init__.py

│   │       │   │   └── __init__.py

│   │       │   ├── pxr

│   │       │   │   ├── Ar

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _ar.pyd

│   │       │   │   ├── CameraUtil

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _cameraUtil.pyd

│   │       │   │   ├── Garch

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _garch.pyd

│   │       │   │   ├── GeomUtil

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _geomUtil.pyd

│   │       │   │   ├── Gf

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _gf.pyd

│   │       │   │   ├── Glf

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _glf.pyd

│   │       │   │   ├── Kind

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _kind.pyd

│   │       │   │   ├── Ndr

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _ndr.pyd

│   │       │   │   ├── Pcp

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _pcp.pyd

│   │       │   │   ├── Plug

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _plug.pyd

│   │       │   │   ├── PxOsd

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _pxOsd.pyd

│   │       │   │   ├── Sdf

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _sdf.pyd

│   │       │   │   ├── Sdr

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _sdr.pyd

│   │       │   │   │   └── shaderParserTestUtils.py

│   │       │   │   ├── SdrGlslfx

│   │       │   │   │   └── __init__.py

│   │       │   │   ├── Tf

│   │       │   │   │   ├── testenv

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── testTfPyInvoke_callees.py

│   │       │   │   │   │   ├── testTfScriptModuleLoader_AAA_RaisesError.py

│   │       │   │   │   │   ├── testTfScriptModuleLoader_DepLoadsAll.py

│   │       │   │   │   │   ├── testTfScriptModuleLoader_LoadsAll.py

│   │       │   │   │   │   ├── testTfScriptModuleLoader_LoadsUnknown.py

│   │       │   │   │   │   ├── testTfScriptModuleLoader_Other.py

│   │       │   │   │   │   ├── testTfScriptModuleLoader_Test.py

│   │       │   │   │   │   └── testTfScriptModuleLoader_Unknown.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _tf.pyd

│   │       │   │   ├── Trace

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── __main__.py

│   │       │   │   │   └── _trace.pyd

│   │       │   │   ├── Usd

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usd.pyd

│   │       │   │   ├── UsdAppUtils

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _usdAppUtils.pyd

│   │       │   │   │   ├── cameraArgs.py

│   │       │   │   │   ├── colorArgs.py

│   │       │   │   │   ├── complexityArgs.py

│   │       │   │   │   ├── framesArgs.py

│   │       │   │   │   └── rendererArgs.py

│   │       │   │   ├── UsdGeom

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdGeom.pyd

│   │       │   │   ├── UsdHydra

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdHydra.pyd

│   │       │   │   ├── UsdImagingGL

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdImagingGL.pyd

│   │       │   │   ├── UsdLux

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdLux.pyd

│   │       │   │   ├── UsdMedia

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdMedia.pyd

│   │       │   │   ├── UsdMtlx

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdMtlx.pyd

│   │       │   │   ├── UsdPhysics

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdPhysics.pyd

│   │       │   │   ├── UsdProc

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdProc.pyd

│   │       │   │   ├── UsdRender

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdRender.pyd

│   │       │   │   ├── UsdRi

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdRi.pyd

│   │       │   │   ├── UsdShade

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdShade.pyd

│   │       │   │   ├── UsdShaders

│   │       │   │   │   └── __init__.py

│   │       │   │   ├── UsdSkel

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdSkel.pyd

│   │       │   │   ├── UsdUI

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdUI.pyd

│   │       │   │   ├── UsdUtils

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _usdUtils.pyd

│   │       │   │   │   ├── complianceChecker.py

│   │       │   │   │   ├── constantsGroup.py

│   │       │   │   │   ├── fixBrokenPixarSchemas.py

│   │       │   │   │   ├── toolPaths.py

│   │       │   │   │   ├── updateSchemaWithSdrNode.py

│   │       │   │   │   └── usdzUtils.py

│   │       │   │   ├── UsdVol

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _usdVol.pyd

│   │       │   │   ├── Vt

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _vt.pyd

│   │       │   │   ├── Work

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── _work.pyd

│   │       │   │   └── __init__.py

│   │       │   ├── pycodestyle-2.8.0.dist-info

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   ├── entry_points.txt

│   │       │   │   ├── namespace_packages.txt

│   │       │   │   └── top_level.txt

│   │       │   ├── pyximport

│   │       │   │   ├── __init__.py

│   │       │   │   ├── pyxbuild.py

│   │       │   │   └── pyximport.py

│   │       │   ├── requests

│   │       │   │   ├── __init__.py

│   │       │   │   ├── __version__.py

│   │       │   │   ├── _internal_utils.py

│   │       │   │   ├── adapters.py

│   │       │   │   ├── api.py

│   │       │   │   ├── auth.py

│   │       │   │   ├── certs.py

│   │       │   │   ├── compat.py

│   │       │   │   ├── cookies.py

│   │       │   │   ├── exceptions.py

│   │       │   │   ├── help.py

│   │       │   │   ├── hooks.py

│   │       │   │   ├── models.py

│   │       │   │   ├── packages.py

│   │       │   │   ├── sessions.py

│   │       │   │   ├── status_codes.py

│   │       │   │   ├── structures.py

│   │       │   │   └── utils.py

│   │       │   ├── requests-2.27.1.dist-info

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   └── top_level.txt

│   │       │   ├── setuptools

│   │       │   │   ├── _distutils

│   │       │   │   │   ├── command

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _framework_compat.py

│   │       │   │   │   │   ├── bdist.py

│   │       │   │   │   │   ├── bdist_dumb.py

│   │       │   │   │   │   ├── bdist_msi.py

│   │       │   │   │   │   ├── bdist_rpm.py

│   │       │   │   │   │   ├── bdist_wininst.py

│   │       │   │   │   │   ├── build.py

│   │       │   │   │   │   ├── build_clib.py

│   │       │   │   │   │   ├── build_ext.py

│   │       │   │   │   │   ├── build_py.py

│   │       │   │   │   │   ├── build_scripts.py

│   │       │   │   │   │   ├── check.py

│   │       │   │   │   │   ├── clean.py

│   │       │   │   │   │   ├── config.py

│   │       │   │   │   │   ├── install.py

│   │       │   │   │   │   ├── install_data.py

│   │       │   │   │   │   ├── install_egg_info.py

│   │       │   │   │   │   ├── install_headers.py

│   │       │   │   │   │   ├── install_lib.py

│   │       │   │   │   │   ├── install_scripts.py

│   │       │   │   │   │   ├── py37compat.py

│   │       │   │   │   │   ├── register.py

│   │       │   │   │   │   ├── sdist.py

│   │       │   │   │   │   └── upload.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _collections.py

│   │       │   │   │   ├── _functools.py

│   │       │   │   │   ├── _macos_compat.py

│   │       │   │   │   ├── _msvccompiler.py

│   │       │   │   │   ├── archive_util.py

│   │       │   │   │   ├── bcppcompiler.py

│   │       │   │   │   ├── ccompiler.py

│   │       │   │   │   ├── cmd.py

│   │       │   │   │   ├── config.py

│   │       │   │   │   ├── core.py

│   │       │   │   │   ├── cygwinccompiler.py

│   │       │   │   │   ├── debug.py

│   │       │   │   │   ├── dep_util.py

│   │       │   │   │   ├── dir_util.py

│   │       │   │   │   ├── dist.py

│   │       │   │   │   ├── errors.py

│   │       │   │   │   ├── extension.py

│   │       │   │   │   ├── fancy_getopt.py

│   │       │   │   │   ├── file_util.py

│   │       │   │   │   ├── filelist.py

│   │       │   │   │   ├── log.py

│   │       │   │   │   ├── msvc9compiler.py

│   │       │   │   │   ├── msvccompiler.py

│   │       │   │   │   ├── py38compat.py

│   │       │   │   │   ├── py39compat.py

│   │       │   │   │   ├── spawn.py

│   │       │   │   │   ├── sysconfig.py

│   │       │   │   │   ├── text_file.py

│   │       │   │   │   ├── unixccompiler.py

│   │       │   │   │   ├── util.py

│   │       │   │   │   ├── version.py

│   │       │   │   │   └── versionpredicate.py

│   │       │   │   ├── _vendor

│   │       │   │   │   ├── importlib_metadata

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _adapters.py

│   │       │   │   │   │   ├── _collections.py

│   │       │   │   │   │   ├── _compat.py

│   │       │   │   │   │   ├── _functools.py

│   │       │   │   │   │   ├── _itertools.py

│   │       │   │   │   │   ├── _meta.py

│   │       │   │   │   │   └── _text.py

│   │       │   │   │   ├── importlib_resources

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _adapters.py

│   │       │   │   │   │   ├── _common.py

│   │       │   │   │   │   ├── _compat.py

│   │       │   │   │   │   ├── _itertools.py

│   │       │   │   │   │   ├── _legacy.py

│   │       │   │   │   │   ├── abc.py

│   │       │   │   │   │   ├── readers.py

│   │       │   │   │   │   └── simple.py

│   │       │   │   │   ├── jaraco

│   │       │   │   │   │   ├── text

│   │       │   │   │   │   │   └── __init__.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── context.py

│   │       │   │   │   │   └── functools.py

│   │       │   │   │   ├── more_itertools

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── more.py

│   │       │   │   │   │   └── recipes.py

│   │       │   │   │   ├── packaging

│   │       │   │   │   │   ├── __about__.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _manylinux.py

│   │       │   │   │   │   ├── _musllinux.py

│   │       │   │   │   │   ├── _structures.py

│   │       │   │   │   │   ├── markers.py

│   │       │   │   │   │   ├── requirements.py

│   │       │   │   │   │   ├── specifiers.py

│   │       │   │   │   │   ├── tags.py

│   │       │   │   │   │   ├── utils.py

│   │       │   │   │   │   └── version.py

│   │       │   │   │   ├── pyparsing

│   │       │   │   │   │   ├── diagram

│   │       │   │   │   │   │   └── __init__.py

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── actions.py

│   │       │   │   │   │   ├── common.py

│   │       │   │   │   │   ├── core.py

│   │       │   │   │   │   ├── exceptions.py

│   │       │   │   │   │   ├── helpers.py

│   │       │   │   │   │   ├── results.py

│   │       │   │   │   │   ├── testing.py

│   │       │   │   │   │   ├── unicode.py

│   │       │   │   │   │   └── util.py

│   │       │   │   │   ├── tomli

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── _parser.py

│   │       │   │   │   │   ├── _re.py

│   │       │   │   │   │   └── _types.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── ordered_set.py

│   │       │   │   │   ├── typing_extensions.py

│   │       │   │   │   └── zipp.py

│   │       │   │   ├── command

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── alias.py

│   │       │   │   │   ├── bdist_egg.py

│   │       │   │   │   ├── bdist_rpm.py

│   │       │   │   │   ├── build.py

│   │       │   │   │   ├── build_clib.py

│   │       │   │   │   ├── build_ext.py

│   │       │   │   │   ├── build_py.py

│   │       │   │   │   ├── develop.py

│   │       │   │   │   ├── dist_info.py

│   │       │   │   │   ├── easy_install.py

│   │       │   │   │   ├── egg_info.py

│   │       │   │   │   ├── install.py

│   │       │   │   │   ├── install_egg_info.py

│   │       │   │   │   ├── install_lib.py

│   │       │   │   │   ├── install_scripts.py

│   │       │   │   │   ├── launcher manifest.xml

│   │       │   │   │   ├── py36compat.py

│   │       │   │   │   ├── register.py

│   │       │   │   │   ├── rotate.py

│   │       │   │   │   ├── saveopts.py

│   │       │   │   │   ├── sdist.py

│   │       │   │   │   ├── setopt.py

│   │       │   │   │   ├── test.py

│   │       │   │   │   ├── upload.py

│   │       │   │   │   └── upload_docs.py

│   │       │   │   ├── config

│   │       │   │   │   ├── _validate_pyproject

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── error_reporting.py

│   │       │   │   │   │   ├── extra_validations.py

│   │       │   │   │   │   ├── fastjsonschema_exceptions.py

│   │       │   │   │   │   ├── fastjsonschema_validations.py

│   │       │   │   │   │   └── formats.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _apply_pyprojecttoml.py

│   │       │   │   │   ├── expand.py

│   │       │   │   │   ├── pyprojecttoml.py

│   │       │   │   │   └── setupcfg.py

│   │       │   │   ├── extern

│   │       │   │   │   └── __init__.py

│   │       │   │   ├── __init__.py

│   │       │   │   ├── _deprecation_warning.py

│   │       │   │   ├── _entry_points.py

│   │       │   │   ├── _imp.py

│   │       │   │   ├── _importlib.py

│   │       │   │   ├── _itertools.py

│   │       │   │   ├── _path.py

│   │       │   │   ├── _reqs.py

│   │       │   │   ├── archive_util.py

│   │       │   │   ├── build_meta.py

│   │       │   │   ├── cli-32.exe

│   │       │   │   ├── cli-64.exe

│   │       │   │   ├── cli-arm64.exe

│   │       │   │   ├── cli.exe

│   │       │   │   ├── dep_util.py

│   │       │   │   ├── depends.py

│   │       │   │   ├── discovery.py

│   │       │   │   ├── dist.py

│   │       │   │   ├── errors.py

│   │       │   │   ├── extension.py

│   │       │   │   ├── glob.py

│   │       │   │   ├── gui-32.exe

│   │       │   │   ├── gui-64.exe

│   │       │   │   ├── gui-arm64.exe

│   │       │   │   ├── gui.exe

│   │       │   │   ├── installer.py

│   │       │   │   ├── launch.py

│   │       │   │   ├── logging.py

│   │       │   │   ├── monkey.py

│   │       │   │   ├── msvc.py

│   │       │   │   ├── namespaces.py

│   │       │   │   ├── package_index.py

│   │       │   │   ├── py34compat.py

│   │       │   │   ├── sandbox.py

│   │       │   │   ├── script (dev).tmpl

│   │       │   │   ├── script.tmpl

│   │       │   │   ├── unicode_utils.py

│   │       │   │   ├── version.py

│   │       │   │   ├── wheel.py

│   │       │   │   └── windows_support.py

│   │       │   ├── setuptools-63.2.0.dist-info

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   ├── entry_points.txt

│   │       │   │   └── top_level.txt

│   │       │   ├── toml

│   │       │   │   ├── __init__.py

│   │       │   │   ├── __init__.pyi

│   │       │   │   ├── decoder.py

│   │       │   │   ├── decoder.pyi

│   │       │   │   ├── encoder.py

│   │       │   │   ├── encoder.pyi

│   │       │   │   ├── ordered.py

│   │       │   │   ├── ordered.pyi

│   │       │   │   ├── tz.py

│   │       │   │   └── tz.pyi

│   │       │   ├── toml-0.10.2.dist-info

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   └── top_level.txt

│   │       │   ├── urllib3

│   │       │   │   ├── contrib

│   │       │   │   │   ├── _securetransport

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   ├── bindings.py

│   │       │   │   │   │   └── low_level.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── _appengine_environ.py

│   │       │   │   │   ├── appengine.py

│   │       │   │   │   ├── ntlmpool.py

│   │       │   │   │   ├── pyopenssl.py

│   │       │   │   │   ├── securetransport.py

│   │       │   │   │   └── socks.py

│   │       │   │   ├── packages

│   │       │   │   │   ├── backports

│   │       │   │   │   │   ├── __init__.py

│   │       │   │   │   │   └── makefile.py

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   └── six.py

│   │       │   │   ├── util

│   │       │   │   │   ├── __init__.py

│   │       │   │   │   ├── connection.py

│   │       │   │   │   ├── proxy.py

│   │       │   │   │   ├── queue.py

│   │       │   │   │   ├── request.py

│   │       │   │   │   ├── response.py

│   │       │   │   │   ├── retry.py

│   │       │   │   │   ├── ssl_.py

│   │       │   │   │   ├── ssl_match_hostname.py

│   │       │   │   │   ├── ssltransport.py

│   │       │   │   │   ├── timeout.py

│   │       │   │   │   ├── url.py

│   │       │   │   │   └── wait.py

│   │       │   │   ├── __init__.py

│   │       │   │   ├── _collections.py

│   │       │   │   ├── _version.py

│   │       │   │   ├── connection.py

│   │       │   │   ├── connectionpool.py

│   │       │   │   ├── exceptions.py

│   │       │   │   ├── fields.py

│   │       │   │   ├── filepost.py

│   │       │   │   ├── poolmanager.py

│   │       │   │   ├── request.py

│   │       │   │   └── response.py

│   │       │   ├── urllib3-1.26.8.dist-info

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE.txt

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   └── top_level.txt

│   │       │   ├── zstandard

│   │       │   │   ├── __init__.py

│   │       │   │   ├── __init__.pyi

│   │       │   │   ├── backend_c.cp311-win_amd64.pyd

│   │       │   │   ├── backend_cffi.py

│   │       │   │   └── py.typed

│   │       │   ├── zstandard-0.16.0.dist-info

│   │       │   │   ├── INSTALLER

│   │       │   │   ├── LICENSE

│   │       │   │   ├── METADATA

│   │       │   │   ├── RECORD

│   │       │   │   ├── REQUESTED

│   │       │   │   ├── WHEEL

│   │       │   │   └── top_level.txt

│   │       │   ├── README.txt

│   │       │   ├── autopep8.py

│   │       │   ├── cython.py

│   │       │   ├── distutils-precedence.pth

│   │       │   ├── extern_draco.dll

│   │       │   ├── pycodestyle.py

│   │       │   ├── pyopenvdb.cp311-win_amd64.pyd

│   │       │   └── sitecustomize.py

│   │       ├── sqlite3

│   │       │   ├── __init__.py

│   │       │   ├── dbapi2.py

│   │       │   └── dump.py

│   │       ├── tomllib

│   │       │   ├── __init__.py

│   │       │   ├── _parser.py

│   │       │   ├── _re.py

│   │       │   └── _types.py

│   │       ├── unittest

│   │       │   ├── __init__.py

│   │       │   ├── __main__.py

│   │       │   ├── _log.py

│   │       │   ├── async_case.py

│   │       │   ├── case.py

│   │       │   ├── loader.py

│   │       │   ├── main.py

│   │       │   ├── mock.py

│   │       │   ├── result.py

│   │       │   ├── runner.py

│   │       │   ├── signals.py

│   │       │   ├── suite.py

│   │       │   └── util.py

│   │       ├── urllib

│   │       │   ├── __init__.py

│   │       │   ├── error.py

│   │       │   ├── parse.py

│   │       │   ├── request.py

│   │       │   ├── response.py

│   │       │   └── robotparser.py

│   │       ├── wsgiref

│   │       │   ├── __init__.py

│   │       │   ├── handlers.py

│   │       │   ├── headers.py

│   │       │   ├── simple_server.py

│   │       │   ├── types.py

│   │       │   ├── util.py

│   │       │   └── validate.py

│   │       ├── xml

│   │       │   ├── dom

│   │       │   │   ├── NodeFilter.py

│   │       │   │   ├── __init__.py

│   │       │   │   ├── domreg.py

│   │       │   │   ├── expatbuilder.py

│   │       │   │   ├── minicompat.py

│   │       │   │   ├── minidom.py

│   │       │   │   ├── pulldom.py

│   │       │   │   └── xmlbuilder.py

│   │       │   ├── etree

│   │       │   │   ├── ElementInclude.py

│   │       │   │   ├── ElementPath.py

│   │       │   │   ├── ElementTree.py

│   │       │   │   ├── __init__.py

│   │       │   │   └── cElementTree.py

│   │       │   ├── parsers

│   │       │   │   ├── __init__.py

│   │       │   │   └── expat.py

│   │       │   ├── sax

│   │       │   │   ├── __init__.py

│   │       │   │   ├── _exceptions.py

│   │       │   │   ├── expatreader.py

│   │       │   │   ├── handler.py

│   │       │   │   ├── saxutils.py

│   │       │   │   └── xmlreader.py

│   │       │   └── __init__.py

│   │       ├── xmlrpc

│   │       │   ├── __init__.py

│   │       │   ├── client.py

│   │       │   └── server.py

│   │       ├── zoneinfo

│   │       │   ├── __init__.py

│   │       │   ├── _common.py

│   │       │   ├── _tzpath.py

│   │       │   └── _zoneinfo.py

│   │       ├── __future__.py

│   │       ├── __hello__.py

│   │       ├── _aix_support.py

│   │       ├── _bootsubprocess.py

│   │       ├── _collections_abc.py

│   │       ├── _compat_pickle.py

│   │       ├── _compression.py

│   │       ├── _markupbase.py

│   │       ├── _osx_support.py

│   │       ├── _py_abc.py

│   │       ├── _pydecimal.py

│   │       ├── _pyio.py

│   │       ├── _sitebuiltins.py

│   │       ├── _strptime.py

│   │       ├── _threading_local.py

│   │       ├── _weakrefset.py

│   │       ├── abc.py

│   │       ├── aifc.py

│   │       ├── antigravity.py

│   │       ├── argparse.py

│   │       ├── ast.py

│   │       ├── asynchat.py

│   │       ├── asyncore.py

│   │       ├── base64.py

│   │       ├── bdb.py

│   │       ├── bisect.py

│   │       ├── bz2.py

│   │       ├── cProfile.py

│   │       ├── calendar.py

│   │       ├── cgi.py

│   │       ├── cgitb.py

│   │       ├── chunk.py

│   │       ├── cmd.py

│   │       ├── code.py

│   │       ├── codecs.py

│   │       ├── codeop.py

│   │       ├── colorsys.py

│   │       ├── compileall.py

│   │       ├── configparser.py

│   │       ├── contextlib.py

│   │       ├── contextvars.py

│   │       ├── copy.py

│   │       ├── copyreg.py

│   │       ├── crypt.py

│   │       ├── csv.py

│   │       ├── dataclasses.py

│   │       ├── datetime.py

│   │       ├── decimal.py

│   │       ├── difflib.py

│   │       ├── dis.py

│   │       ├── doctest.py

│   │       ├── enum.py

│   │       ├── filecmp.py

│   │       ├── fileinput.py

│   │       ├── fnmatch.py

│   │       ├── fractions.py

│   │       ├── ftplib.py

│   │       ├── functools.py

│   │       ├── genericpath.py

│   │       ├── getopt.py

│   │       ├── getpass.py

│   │       ├── gettext.py

│   │       ├── glob.py

│   │       ├── graphlib.py

│   │       ├── gzip.py

│   │       ├── hashlib.py

│   │       ├── heapq.py

│   │       ├── hmac.py

│   │       ├── imaplib.py

│   │       ├── imghdr.py

│   │       ├── imp.py

│   │       ├── inspect.py

│   │       ├── io.py

│   │       ├── ipaddress.py

│   │       ├── keyword.py

│   │       ├── linecache.py

│   │       ├── locale.py

│   │       ├── lzma.py

│   │       ├── mailbox.py

│   │       ├── mailcap.py

│   │       ├── mimetypes.py

│   │       ├── modulefinder.py

│   │       ├── netrc.py

│   │       ├── nntplib.py

│   │       ├── ntpath.py

│   │       ├── nturl2path.py

│   │       ├── numbers.py

│   │       ├── opcode.py

│   │       ├── operator.py

│   │       ├── optparse.py

│   │       ├── os.py

│   │       ├── pathlib.py

│   │       ├── pdb.py

│   │       ├── pickle.py

│   │       ├── pickletools.py

│   │       ├── pipes.py

│   │       ├── pkgutil.py

│   │       ├── platform.py

│   │       ├── plistlib.py

│   │       ├── poplib.py

│   │       ├── posixpath.py

│   │       ├── pprint.py

│   │       ├── profile.py

│   │       ├── pstats.py

│   │       ├── pty.py

│   │       ├── py_compile.py

│   │       ├── pyclbr.py

│   │       ├── pydoc.py

│   │       ├── queue.py

│   │       ├── quopri.py

│   │       ├── random.py

│   │       ├── reprlib.py

│   │       ├── rlcompleter.py

│   │       ├── runpy.py

│   │       ├── sched.py

│   │       ├── secrets.py

│   │       ├── selectors.py

│   │       ├── shelve.py

│   │       ├── shlex.py

│   │       ├── shutil.py

│   │       ├── signal.py

│   │       ├── site.py

│   │       ├── smtpd.py

│   │       ├── smtplib.py

│   │       ├── sndhdr.py

│   │       ├── socket.py

│   │       ├── socketserver.py

│   │       ├── sre_compile.py

│   │       ├── sre_constants.py

│   │       ├── sre_parse.py

│   │       ├── ssl.py

│   │       ├── stat.py

│   │       ├── statistics.py

│   │       ├── string.py

│   │       ├── stringprep.py

│   │       ├── struct.py

│   │       ├── subprocess.py

│   │       ├── sunau.py

│   │       ├── symtable.py

│   │       ├── sysconfig.py

│   │       ├── tabnanny.py

│   │       ├── tarfile.py

│   │       ├── telnetlib.py

│   │       ├── tempfile.py

│   │       ├── textwrap.py

│   │       ├── this.py

│   │       ├── threading.py

│   │       ├── timeit.py

│   │       ├── token.py

│   │       ├── tokenize.py

│   │       ├── trace.py

│   │       ├── traceback.py

│   │       ├── tracemalloc.py

│   │       ├── tty.py

│   │       ├── types.py

│   │       ├── typing.py

│   │       ├── uu.py

│   │       ├── uuid.py

│   │       ├── warnings.py

│   │       ├── wave.py

│   │       ├── weakref.py

│   │       ├── webbrowser.py

│   │       ├── xdrlib.py

│   │       ├── zipapp.py

│   │       ├── zipfile.py

│   │       └── zipimport.py

│   └── scripts

│       ├── addons

│       │   ├── add_camera_rigs

│       │   │   ├── __init__.py

│       │   │   ├── build_rigs.py

│       │   │   ├── composition_guides_menu.py

│       │   │   ├── create_widgets.py

│       │   │   ├── operators.py

│       │   │   ├── prefs.py

│       │   │   └── ui_panels.py

│       │   ├── add_curve_extra_objects

│       │   │   ├── __init__.py

│       │   │   ├── add_curve_aceous_galore.py

│       │   │   ├── add_curve_braid.py

│       │   │   ├── add_curve_celtic_links.py

│       │   │   ├── add_curve_curly.py

│       │   │   ├── add_curve_simple.py

│       │   │   ├── add_curve_spirals.py

│       │   │   ├── add_curve_spirofit_bouncespline.py

│       │   │   ├── add_curve_torus_knots.py

│       │   │   ├── add_surface_plane_cone.py

│       │   │   └── beveltaper_curve.py

│       │   ├── add_curve_sapling

│       │   │   ├── presets

│       │   │   │   ├── callistemon.py

│       │   │   │   ├── douglas_fir.py

│       │   │   │   ├── japanese_maple.py

│       │   │   │   ├── quaking_aspen.py

│       │   │   │   ├── small_maple.py

│       │   │   │   ├── small_pine.py

│       │   │   │   ├── weeping_willow.py

│       │   │   │   ├── white_birch.py

│       │   │   │   └── willow.py

│       │   │   ├── __init__.py

│       │   │   └── utils.py

│       │   ├── add_mesh_BoltFactory

│       │   │   ├── Boltfactory.py

│       │   │   ├── __init__.py

│       │   │   └── createMesh.py

│       │   ├── add_mesh_discombobulator

│       │   │   ├── __init__.py

│       │   │   └── mesh_discombobulator.py

│       │   ├── add_mesh_extra_objects

│       │   │   ├── add_mesh_rocks

│       │   │   │   ├── __init__.py

│       │   │   │   ├── add_mesh_rocks.xml

│       │   │   │   ├── factory.xml

│       │   │   │   ├── randomize_texture.py

│       │   │   │   ├── rockgen.py

│       │   │   │   ├── settings.py

│       │   │   │   └── utils.py

│       │   │   ├── Blocks.py

│       │   │   ├── Wallfactory.py

│       │   │   ├── __init__.py

│       │   │   ├── add_empty_as_parent.py

│       │   │   ├── add_mesh_3d_function_surface.py

│       │   │   ├── add_mesh_beam_builder.py

│       │   │   ├── add_mesh_gears.py

│       │   │   ├── add_mesh_gemstones.py

│       │   │   ├── add_mesh_honeycomb.py

│       │   │   ├── add_mesh_menger_sponge.py

│       │   │   ├── add_mesh_pipe_joint.py

│       │   │   ├── add_mesh_pyramid.py

│       │   │   ├── add_mesh_round_brilliant.py

│       │   │   ├── add_mesh_round_cube.py

│       │   │   ├── add_mesh_solid.py

│       │   │   ├── add_mesh_star.py

│       │   │   ├── add_mesh_supertoroid.py

│       │   │   ├── add_mesh_teapot.py

│       │   │   ├── add_mesh_torusknot.py

│       │   │   ├── add_mesh_triangles.py

│       │   │   ├── add_mesh_twisted_torus.py

│       │   │   └── add_mesh_vertex.py

│       │   ├── add_mesh_geodesic_domes

│       │   │   ├── __init__.py

│       │   │   ├── add_shape_geodesic.py

│       │   │   ├── forms_271.py

│       │   │   ├── geodesic_classes_271.py

│       │   │   ├── third_domes_panel_271.py

│       │   │   └── vefm_271.py

│       │   ├── amaranth

│       │   │   ├── animation

│       │   │   │   ├── __init__.py

│       │   │   │   ├── frame_current.py

│       │   │   │   ├── jump_frames.py

│       │   │   │   ├── motion_paths.py

│       │   │   │   └── time_extra_info.py

│       │   │   ├── misc

│       │   │   │   ├── __init__.py

│       │   │   │   ├── color_management.py

│       │   │   │   ├── dupli_group_id.py

│       │   │   │   ├── sequencer_extra_info.py

│       │   │   │   └── toggle_wire.py

│       │   │   ├── modeling

│       │   │   │   ├── __init__.py

│       │   │   │   └── symmetry_tools.py

│       │   │   ├── node_editor

│       │   │   │   ├── templates

│       │   │   │   │   ├── __init__.py

│       │   │   │   │   ├── vectorblur.py

│       │   │   │   │   └── vignette.py

│       │   │   │   ├── __init__.py

│       │   │   │   ├── display_image.py

│       │   │   │   ├── id_panel.py

│       │   │   │   ├── node_stats.py

│       │   │   │   ├── normal_node.py

│       │   │   │   └── simplify_nodes.py

│       │   │   ├── render

│       │   │   │   ├── __init__.py

│       │   │   │   ├── border_camera.py

│       │   │   │   ├── final_resolution.py

│       │   │   │   ├── meshlight_add.py

│       │   │   │   ├── meshlight_select.py

│       │   │   │   ├── passepartout.py

│       │   │   │   └── samples_scene.py

│       │   │   ├── scene

│       │   │   │   ├── __init__.py

│       │   │   │   ├── current_blend.py

│       │   │   │   ├── debug.py

│       │   │   │   ├── goto_library.py

│       │   │   │   ├── material_remove_unassigned.py

│       │   │   │   ├── refresh.py

│       │   │   │   ├── save_reload.py

│       │   │   │   └── stats.py

│       │   │   ├── __init__.py

│       │   │   ├── prefs.py

│       │   │   └── utils.py

│       │   ├── animation_animall

│       │   │   ├── __init__.py

│       │   │   └── translations.py

│       │   ├── ant_landscape

│       │   │   ├── ErosionR.txt

│       │   │   ├── __init__.py

│       │   │   ├── add_mesh_ant_landscape.py

│       │   │   ├── ant_functions.py

│       │   │   ├── ant_noise.py

│       │   │   ├── eroder.py

│       │   │   ├── mesh_ant_displace.py

│       │   │   ├── stats.py

│       │   │   ├── test.py

│       │   │   └── utils.py

│       │   ├── archimesh

│       │   │   ├── images

│       │   │   │   └── fabric_diffuse.png

│       │   │   ├── __init__.py

│       │   │   ├── achm_books_maker.py

│       │   │   ├── achm_column_maker.py

│       │   │   ├── achm_curtain_maker.py

│       │   │   ├── achm_door_maker.py

│       │   │   ├── achm_gltools.py

│       │   │   ├── achm_kitchen_maker.py

│       │   │   ├── achm_lamp_maker.py

│       │   │   ├── achm_main_panel.py

│       │   │   ├── achm_roof_maker.py

│       │   │   ├── achm_room_maker.py

│       │   │   ├── achm_shelves_maker.py

│       │   │   ├── achm_stairs_maker.py

│       │   │   ├── achm_tools.py

│       │   │   ├── achm_venetian_maker.py

│       │   │   ├── achm_window_maker.py

│       │   │   └── achm_window_panel.py

│       │   ├── blender_id

│       │   │   ├── CHANGELOG.md

│       │   │   ├── README.md

│       │   │   ├── __init__.py

│       │   │   ├── communication.py

│       │   │   └── profiles.py

│       │   ├── btrace

│       │   │   ├── __init__.py

│       │   │   ├── bTrace.py

│       │   │   ├── bTrace_panel.py

│       │   │   └── bTrace_props.py

│       │   ├── curve_tools

│       │   │   ├── __init__.py

│       │   │   ├── auto_loft.py

│       │   │   ├── cad.py

│       │   │   ├── curves.py

│       │   │   ├── exports.py

│       │   │   ├── fillet.py

│       │   │   ├── internal.py

│       │   │   ├── intersections.py

│       │   │   ├── mathematics.py

│       │   │   ├── operators.py

│       │   │   ├── outline.py

│       │   │   ├── path_finder.py

│       │   │   ├── properties.py

│       │   │   ├── remove_doubles.py

│       │   │   ├── show_resolution.py

│       │   │   ├── splines_sequence.py

│       │   │   ├── surfaces.py

│       │   │   ├── toolpath.py

│       │   │   └── util.py

│       │   ├── cycles

│       │   │   ├── lib

│       │   │   │   ├── kernel_compute_75.ptx

│       │   │   │   ├── kernel_gfx1010.fatbin

│       │   │   │   ├── kernel_gfx1011.fatbin

│       │   │   │   ├── kernel_gfx1012.fatbin

│       │   │   │   ├── kernel_gfx1030.fatbin

│       │   │   │   ├── kernel_gfx1031.fatbin

│       │   │   │   ├── kernel_gfx1032.fatbin

│       │   │   │   ├── kernel_gfx1034.fatbin

│       │   │   │   ├── kernel_gfx1035.fatbin

│       │   │   │   ├── kernel_gfx1036.fatbin

│       │   │   │   ├── kernel_gfx1100.fatbin

│       │   │   │   ├── kernel_gfx1101.fatbin

│       │   │   │   ├── kernel_gfx1102.fatbin

│       │   │   │   ├── kernel_gfx1103.fatbin

│       │   │   │   ├── kernel_gfx900.fatbin

│       │   │   │   ├── kernel_gfx902.fatbin

│       │   │   │   ├── kernel_gfx90c.fatbin

│       │   │   │   ├── kernel_optix.ptx

│       │   │   │   ├── kernel_optix_osl.ptx

│       │   │   │   ├── kernel_optix_osl_services.ptx

│       │   │   │   ├── kernel_optix_shader_raytrace.ptx

│       │   │   │   ├── kernel_rt_gfx.hipfb

│       │   │   │   ├── kernel_sm_30.cubin

│       │   │   │   ├── kernel_sm_35.cubin

│       │   │   │   ├── kernel_sm_37.cubin

│       │   │   │   ├── kernel_sm_50.cubin

│       │   │   │   ├── kernel_sm_52.cubin

│       │   │   │   ├── kernel_sm_60.cubin

│       │   │   │   ├── kernel_sm_61.cubin

│       │   │   │   ├── kernel_sm_70.cubin

│       │   │   │   ├── kernel_sm_75.cubin

│       │   │   │   ├── kernel_sm_86.cubin

│       │   │   │   └── kernel_sm_89.cubin

│       │   │   ├── license

│       │   │   │   ├── Apache2-license.txt

│       │   │   │   ├── BSD-3-Clause-license.txt

│       │   │   │   ├── MIT-license.txt

│       │   │   │   ├── SPDX-license-identifiers.txt

│       │   │   │   ├── Zlib-license.txt

│       │   │   │   └── readme.txt

│       │   │   ├── shader

│       │   │   │   ├── color2.h

│       │   │   │   ├── color4.h

│       │   │   │   ├── matrix33.h

│       │   │   │   ├── node_absorption_volume.oso

│       │   │   │   ├── node_add_closure.oso

│       │   │   │   ├── node_ambient_occlusion.oso

│       │   │   │   ├── node_attribute.oso

│       │   │   │   ├── node_background.oso

│       │   │   │   ├── node_bevel.oso

│       │   │   │   ├── node_blackbody.oso

│       │   │   │   ├── node_brick_texture.oso

│       │   │   │   ├── node_brightness.oso

│       │   │   │   ├── node_bump.oso

│       │   │   │   ├── node_camera.oso

│       │   │   │   ├── node_checker_texture.oso

│       │   │   │   ├── node_clamp.oso

│       │   │   │   ├── node_color.h

│       │   │   │   ├── node_color_blend.h

│       │   │   │   ├── node_combine_color.oso

│       │   │   │   ├── node_combine_hsv.oso

│       │   │   │   ├── node_combine_rgb.oso

│       │   │   │   ├── node_combine_xyz.oso

│       │   │   │   ├── node_convert_from_color.oso

│       │   │   │   ├── node_convert_from_float.oso

│       │   │   │   ├── node_convert_from_int.oso

│       │   │   │   ├── node_convert_from_normal.oso

│       │   │   │   ├── node_convert_from_point.oso

│       │   │   │   ├── node_convert_from_vector.oso

│       │   │   │   ├── node_diffuse_bsdf.oso

│       │   │   │   ├── node_displacement.oso

│       │   │   │   ├── node_emission.oso

│       │   │   │   ├── node_environment_texture.oso

│       │   │   │   ├── node_float_curve.oso

│       │   │   │   ├── node_fractal_voronoi.h

│       │   │   │   ├── node_fresnel.h

│       │   │   │   ├── node_fresnel.oso

│       │   │   │   ├── node_gamma.oso

│       │   │   │   ├── node_geometry.oso

│       │   │   │   ├── node_glass_bsdf.oso

│       │   │   │   ├── node_glossy_bsdf.oso

│       │   │   │   ├── node_gradient_texture.oso

│       │   │   │   ├── node_hair_bsdf.oso

│       │   │   │   ├── node_hair_info.oso

│       │   │   │   ├── node_hash.h

│       │   │   │   ├── node_holdout.oso

│       │   │   │   ├── node_hsv.oso

│       │   │   │   ├── node_ies_light.oso

│       │   │   │   ├── node_image_texture.oso

│       │   │   │   ├── node_invert.oso

│       │   │   │   ├── node_layer_weight.oso

│       │   │   │   ├── node_light_falloff.oso

│       │   │   │   ├── node_light_path.oso

│       │   │   │   ├── node_magic_texture.oso

│       │   │   │   ├── node_map_range.oso

│       │   │   │   ├── node_mapping.oso

│       │   │   │   ├── node_math.h

│       │   │   │   ├── node_math.oso

│       │   │   │   ├── node_mix.oso

│       │   │   │   ├── node_mix_closure.oso

│       │   │   │   ├── node_mix_color.oso

│       │   │   │   ├── node_mix_float.oso

│       │   │   │   ├── node_mix_vector.oso

│       │   │   │   ├── node_mix_vector_non_uniform.oso

│       │   │   │   ├── node_noise.h

│       │   │   │   ├── node_noise_texture.oso

│       │   │   │   ├── node_normal.oso

│       │   │   │   ├── node_normal_map.oso

│       │   │   │   ├── node_object_info.oso

│       │   │   │   ├── node_output_displacement.oso

│       │   │   │   ├── node_output_surface.oso

│       │   │   │   ├── node_output_volume.oso

│       │   │   │   ├── node_particle_info.oso

│       │   │   │   ├── node_point_info.oso

│       │   │   │   ├── node_principled_bsdf.oso

│       │   │   │   ├── node_principled_hair_bsdf.oso

│       │   │   │   ├── node_principled_volume.oso

│       │   │   │   ├── node_ramp_util.h

│       │   │   │   ├── node_refraction_bsdf.oso

│       │   │   │   ├── node_rgb_curves.oso

│       │   │   │   ├── node_rgb_ramp.oso

│       │   │   │   ├── node_rgb_to_bw.oso

│       │   │   │   ├── node_scatter_volume.oso

│       │   │   │   ├── node_separate_color.oso

│       │   │   │   ├── node_separate_hsv.oso

│       │   │   │   ├── node_separate_rgb.oso

│       │   │   │   ├── node_separate_xyz.oso

│       │   │   │   ├── node_set_normal.oso

│       │   │   │   ├── node_sheen_bsdf.oso

│       │   │   │   ├── node_sky_texture.oso

│       │   │   │   ├── node_subsurface_scattering.oso

│       │   │   │   ├── node_tangent.oso

│       │   │   │   ├── node_texture_coordinate.oso

│       │   │   │   ├── node_toon_bsdf.oso

│       │   │   │   ├── node_translucent_bsdf.oso

│       │   │   │   ├── node_transparent_bsdf.oso

│       │   │   │   ├── node_uv_map.oso

│       │   │   │   ├── node_value.oso

│       │   │   │   ├── node_vector_curves.oso

│       │   │   │   ├── node_vector_displacement.oso

│       │   │   │   ├── node_vector_map_range.oso

│       │   │   │   ├── node_vector_math.oso

│       │   │   │   ├── node_vector_rotate.oso

│       │   │   │   ├── node_vector_transform.oso

│       │   │   │   ├── node_vertex_color.oso

│       │   │   │   ├── node_voronoi.h

│       │   │   │   ├── node_voronoi_texture.oso

│       │   │   │   ├── node_voxel_texture.oso

│       │   │   │   ├── node_wave_texture.oso

│       │   │   │   ├── node_wavelength.oso

│       │   │   │   ├── node_white_noise_texture.oso

│       │   │   │   ├── node_wireframe.oso

│       │   │   │   ├── oslutil.h

│       │   │   │   ├── stdcycles.h

│       │   │   │   ├── stdosl.h

│       │   │   │   ├── vector2.h

│       │   │   │   └── vector4.h

│       │   │   ├── source

│       │   │   │   ├── kernel

│       │   │   │   │   ├── bake

│       │   │   │   │   │   └── bake.h

│       │   │   │   │   ├── bvh

│       │   │   │   │   │   ├── bvh.h

│       │   │   │   │   │   ├── local.h

│       │   │   │   │   │   ├── nodes.h

│       │   │   │   │   │   ├── shadow_all.h

│       │   │   │   │   │   ├── traversal.h

│       │   │   │   │   │   ├── types.h

│       │   │   │   │   │   ├── util.h

│       │   │   │   │   │   ├── volume.h

│       │   │   │   │   │   └── volume_all.h

│       │   │   │   │   ├── camera

│       │   │   │   │   │   ├── camera.h

│       │   │   │   │   │   └── projection.h

│       │   │   │   │   ├── closure

│       │   │   │   │   │   ├── alloc.h

│       │   │   │   │   │   ├── bsdf.h

│       │   │   │   │   │   ├── bsdf_ashikhmin_shirley.h

│       │   │   │   │   │   ├── bsdf_ashikhmin_velvet.h

│       │   │   │   │   │   ├── bsdf_diffuse.h

│       │   │   │   │   │   ├── bsdf_diffuse_ramp.h

│       │   │   │   │   │   ├── bsdf_hair.h

│       │   │   │   │   │   ├── bsdf_microfacet.h

│       │   │   │   │   │   ├── bsdf_oren_nayar.h

│       │   │   │   │   │   ├── bsdf_phong_ramp.h

│       │   │   │   │   │   ├── bsdf_principled_hair_chiang.h

│       │   │   │   │   │   ├── bsdf_principled_hair_huang.h

│       │   │   │   │   │   ├── bsdf_sheen.h

│       │   │   │   │   │   ├── bsdf_toon.h

│       │   │   │   │   │   ├── bsdf_transparent.h

│       │   │   │   │   │   ├── bsdf_util.h

│       │   │   │   │   │   ├── bssrdf.h

│       │   │   │   │   │   ├── emissive.h

│       │   │   │   │   │   └── volume.h

│       │   │   │   │   ├── device

│       │   │   │   │   │   ├── cuda

│       │   │   │   │   │   │   ├── compat.h

│       │   │   │   │   │   │   ├── config.h

│       │   │   │   │   │   │   ├── globals.h

│       │   │   │   │   │   │   └── kernel.cu

│       │   │   │   │   │   ├── gpu

│       │   │   │   │   │   │   ├── image.h

│       │   │   │   │   │   │   ├── kernel.h

│       │   │   │   │   │   │   ├── parallel_active_index.h

│       │   │   │   │   │   │   ├── parallel_prefix_sum.h

│       │   │   │   │   │   │   ├── parallel_sorted_index.h

│       │   │   │   │   │   │   └── work_stealing.h

│       │   │   │   │   │   ├── hip

│       │   │   │   │   │   │   ├── compat.h

│       │   │   │   │   │   │   ├── config.h

│       │   │   │   │   │   │   ├── globals.h

│       │   │   │   │   │   │   └── kernel.cpp

│       │   │   │   │   │   ├── hiprt

│       │   │   │   │   │   │   ├── bvh.h

│       │   │   │   │   │   │   ├── common.h

│       │   │   │   │   │   │   ├── globals.h

│       │   │   │   │   │   │   ├── hiprt_kernels.h

│       │   │   │   │   │   │   └── kernel.cpp

│       │   │   │   │   │   ├── metal

│       │   │   │   │   │   │   ├── bvh.h

│       │   │   │   │   │   │   ├── compat.h

│       │   │   │   │   │   │   ├── context_begin.h

│       │   │   │   │   │   │   ├── context_end.h

│       │   │   │   │   │   │   ├── function_constants.h

│       │   │   │   │   │   │   ├── globals.h

│       │   │   │   │   │   │   └── kernel.metal

│       │   │   │   │   │   └── optix

│       │   │   │   │   │       ├── bvh.h

│       │   │   │   │   │       ├── compat.h

│       │   │   │   │   │       ├── globals.h

│       │   │   │   │   │       ├── kernel.cu

│       │   │   │   │   │       ├── kernel_osl.cu

│       │   │   │   │   │       ├── kernel_shader_raytrace.cu

│       │   │   │   │   │       └── services_optix.cu

│       │   │   │   │   ├── film

│       │   │   │   │   │   ├── adaptive_sampling.h

│       │   │   │   │   │   ├── aov_passes.h

│       │   │   │   │   │   ├── cryptomatte_passes.h

│       │   │   │   │   │   ├── data_passes.h

│       │   │   │   │   │   ├── denoising_passes.h

│       │   │   │   │   │   ├── light_passes.h

│       │   │   │   │   │   ├── read.h

│       │   │   │   │   │   └── write.h

│       │   │   │   │   ├── geom

│       │   │   │   │   │   ├── attribute.h

│       │   │   │   │   │   ├── curve.h

│       │   │   │   │   │   ├── curve_intersect.h

│       │   │   │   │   │   ├── geom.h

│       │   │   │   │   │   ├── motion_curve.h

│       │   │   │   │   │   ├── motion_point.h

│       │   │   │   │   │   ├── motion_triangle.h

│       │   │   │   │   │   ├── motion_triangle_intersect.h

│       │   │   │   │   │   ├── motion_triangle_shader.h

│       │   │   │   │   │   ├── object.h

│       │   │   │   │   │   ├── patch.h

│       │   │   │   │   │   ├── point.h

│       │   │   │   │   │   ├── point_intersect.h

│       │   │   │   │   │   ├── primitive.h

│       │   │   │   │   │   ├── shader_data.h

│       │   │   │   │   │   ├── subd_triangle.h

│       │   │   │   │   │   ├── triangle.h

│       │   │   │   │   │   ├── triangle_intersect.h

│       │   │   │   │   │   └── volume.h

│       │   │   │   │   ├── integrator

│       │   │   │   │   │   ├── displacement_shader.h

│       │   │   │   │   │   ├── guiding.h

│       │   │   │   │   │   ├── init_from_bake.h

│       │   │   │   │   │   ├── init_from_camera.h

│       │   │   │   │   │   ├── intersect_closest.h

│       │   │   │   │   │   ├── intersect_dedicated_light.h

│       │   │   │   │   │   ├── intersect_shadow.h

│       │   │   │   │   │   ├── intersect_subsurface.h

│       │   │   │   │   │   ├── intersect_volume_stack.h

│       │   │   │   │   │   ├── megakernel.h

│       │   │   │   │   │   ├── mnee.h

│       │   │   │   │   │   ├── path_state.h

│       │   │   │   │   │   ├── shade_background.h

│       │   │   │   │   │   ├── shade_dedicated_light.h

│       │   │   │   │   │   ├── shade_light.h

│       │   │   │   │   │   ├── shade_shadow.h

│       │   │   │   │   │   ├── shade_surface.h

│       │   │   │   │   │   ├── shade_volume.h

│       │   │   │   │   │   ├── shadow_catcher.h

│       │   │   │   │   │   ├── shadow_linking.h

│       │   │   │   │   │   ├── shadow_state_template.h

│       │   │   │   │   │   ├── state.h

│       │   │   │   │   │   ├── state_flow.h

│       │   │   │   │   │   ├── state_template.h

│       │   │   │   │   │   ├── state_util.h

│       │   │   │   │   │   ├── subsurface.h

│       │   │   │   │   │   ├── subsurface_disk.h

│       │   │   │   │   │   ├── subsurface_random_walk.h

│       │   │   │   │   │   ├── surface_shader.h

│       │   │   │   │   │   ├── volume_shader.h

│       │   │   │   │   │   └── volume_stack.h

│       │   │   │   │   ├── light

│       │   │   │   │   │   ├── area.h

│       │   │   │   │   │   ├── background.h

│       │   │   │   │   │   ├── common.h

│       │   │   │   │   │   ├── distant.h

│       │   │   │   │   │   ├── distribution.h

│       │   │   │   │   │   ├── light.h

│       │   │   │   │   │   ├── point.h

│       │   │   │   │   │   ├── sample.h

│       │   │   │   │   │   ├── spot.h

│       │   │   │   │   │   ├── tree.h

│       │   │   │   │   │   └── triangle.h

│       │   │   │   │   ├── osl

│       │   │   │   │   │   ├── closures_setup.h

│       │   │   │   │   │   ├── closures_template.h

│       │   │   │   │   │   ├── osl.h

│       │   │   │   │   │   ├── services_gpu.h

│       │   │   │   │   │   └── types.h

│       │   │   │   │   ├── sample

│       │   │   │   │   │   ├── lcg.h

│       │   │   │   │   │   ├── mapping.h

│       │   │   │   │   │   ├── mis.h

│       │   │   │   │   │   ├── pattern.h

│       │   │   │   │   │   ├── sobol_burley.h

│       │   │   │   │   │   ├── tabulated_sobol.h

│       │   │   │   │   │   └── util.h

│       │   │   │   │   ├── svm

│       │   │   │   │   │   ├── ao.h

│       │   │   │   │   │   ├── aov.h

│       │   │   │   │   │   ├── attribute.h

│       │   │   │   │   │   ├── bevel.h

│       │   │   │   │   │   ├── blackbody.h

│       │   │   │   │   │   ├── brick.h

│       │   │   │   │   │   ├── brightness.h

│       │   │   │   │   │   ├── bump.h

│       │   │   │   │   │   ├── camera.h

│       │   │   │   │   │   ├── checker.h

│       │   │   │   │   │   ├── clamp.h

│       │   │   │   │   │   ├── closure.h

│       │   │   │   │   │   ├── color_util.h

│       │   │   │   │   │   ├── convert.h

│       │   │   │   │   │   ├── displace.h

│       │   │   │   │   │   ├── fractal_noise.h

│       │   │   │   │   │   ├── fresnel.h

│       │   │   │   │   │   ├── gamma.h

│       │   │   │   │   │   ├── geometry.h

│       │   │   │   │   │   ├── gradient.h

│       │   │   │   │   │   ├── hsv.h

│       │   │   │   │   │   ├── ies.h

│       │   │   │   │   │   ├── image.h

│       │   │   │   │   │   ├── invert.h

│       │   │   │   │   │   ├── light_path.h

│       │   │   │   │   │   ├── magic.h

│       │   │   │   │   │   ├── map_range.h

│       │   │   │   │   │   ├── mapping.h

│       │   │   │   │   │   ├── mapping_util.h

│       │   │   │   │   │   ├── math.h

│       │   │   │   │   │   ├── math_util.h

│       │   │   │   │   │   ├── mix.h

│       │   │   │   │   │   ├── node_types_template.h

│       │   │   │   │   │   ├── noise.h

│       │   │   │   │   │   ├── noisetex.h

│       │   │   │   │   │   ├── normal.h

│       │   │   │   │   │   ├── ramp.h

│       │   │   │   │   │   ├── ramp_util.h

│       │   │   │   │   │   ├── sepcomb_color.h

│       │   │   │   │   │   ├── sepcomb_hsv.h

│       │   │   │   │   │   ├── sepcomb_vector.h

│       │   │   │   │   │   ├── sky.h

│       │   │   │   │   │   ├── svm.h

│       │   │   │   │   │   ├── tex_coord.h

│       │   │   │   │   │   ├── types.h

│       │   │   │   │   │   ├── value.h

│       │   │   │   │   │   ├── vector_rotate.h

│       │   │   │   │   │   ├── vector_transform.h

│       │   │   │   │   │   ├── vertex_color.h

│       │   │   │   │   │   ├── voronoi.h

│       │   │   │   │   │   ├── voxel.h

│       │   │   │   │   │   ├── wave.h

│       │   │   │   │   │   ├── wavelength.h

│       │   │   │   │   │   ├── white_noise.h

│       │   │   │   │   │   └── wireframe.h

│       │   │   │   │   ├── util

│       │   │   │   │   │   ├── color.h

│       │   │   │   │   │   ├── differential.h

│       │   │   │   │   │   ├── lookup_table.h

│       │   │   │   │   │   ├── nanovdb.h

│       │   │   │   │   │   └── profiling.h

│       │   │   │   │   ├── data_arrays.h

│       │   │   │   │   ├── data_template.h

│       │   │   │   │   ├── tables.h

│       │   │   │   │   └── types.h

│       │   │   │   └── util

│       │   │   │       ├── atomic.h

│       │   │   │       ├── color.h

│       │   │   │       ├── defines.h

│       │   │   │       ├── half.h

│       │   │   │       ├── hash.h

│       │   │   │       ├── math.h

│       │   │   │       ├── math_fast.h

│       │   │   │       ├── math_float2.h

│       │   │   │       ├── math_float3.h

│       │   │   │       ├── math_float4.h

│       │   │   │       ├── math_float8.h

│       │   │   │       ├── math_int2.h

│       │   │   │       ├── math_int3.h

│       │   │   │       ├── math_int4.h

│       │   │   │       ├── math_int8.h

│       │   │   │       ├── math_intersect.h

│       │   │   │       ├── math_matrix.h

│       │   │   │       ├── projection.h

│       │   │   │       ├── rect.h

│       │   │   │       ├── static_assert.h

│       │   │   │       ├── texture.h

│       │   │   │       ├── transform.h

│       │   │   │       ├── transform_inverse.h

│       │   │   │       ├── types.h

│       │   │   │       ├── types_float2.h

│       │   │   │       ├── types_float2_impl.h

│       │   │   │       ├── types_float3.h

│       │   │   │       ├── types_float3_impl.h

│       │   │   │       ├── types_float4.h

│       │   │   │       ├── types_float4_impl.h

│       │   │   │       ├── types_float8.h

│       │   │   │       ├── types_float8_impl.h

│       │   │   │       ├── types_int2.h

│       │   │   │       ├── types_int2_impl.h

│       │   │   │       ├── types_int3.h

│       │   │   │       ├── types_int3_impl.h

│       │   │   │       ├── types_int4.h

│       │   │   │       ├── types_int4_impl.h

│       │   │   │       ├── types_int8.h

│       │   │   │       ├── types_int8_impl.h

│       │   │   │       ├── types_spectrum.h

│       │   │   │       ├── types_uchar2.h

│       │   │   │       ├── types_uchar2_impl.h

│       │   │   │       ├── types_uchar3.h

│       │   │   │       ├── types_uchar3_impl.h

│       │   │   │       ├── types_uchar4.h

│       │   │   │       ├── types_uchar4_impl.h

│       │   │   │       ├── types_uint2.h

│       │   │   │       ├── types_uint2_impl.h

│       │   │   │       ├── types_uint3.h

│       │   │   │       ├── types_uint3_impl.h

│       │   │   │       ├── types_uint4.h

│       │   │   │       ├── types_uint4_impl.h

│       │   │   │       └── types_ushort4.h

│       │   │   ├── __init__.py

│       │   │   ├── camera.py

│       │   │   ├── engine.py

│       │   │   ├── operators.py

│       │   │   ├── osl.py

│       │   │   ├── presets.py

│       │   │   ├── properties.py

│       │   │   ├── ui.py

│       │   │   └── version_update.py

│       │   ├── greasepencil_tools

│       │   │   ├── icons

│       │   │   │   ├── hide_off.png

│       │   │   │   ├── hide_on.png

│       │   │   │   ├── locked.png

│       │   │   │   └── unlocked.png

│       │   │   ├── __init__.py

│       │   │   ├── box_deform.py

│       │   │   ├── draw_tools.py

│       │   │   ├── import_brush_pack.py

│       │   │   ├── layer_navigator.py

│       │   │   ├── line_reshape.py

│       │   │   ├── prefs.py

│       │   │   ├── rotate_canvas.py

│       │   │   ├── timeline_scrub.py

│       │   │   └── ui_panels.py

│       │   ├── hydra_storm

│       │   │   ├── __init__.py

│       │   │   ├── engine.py

│       │   │   ├── properties.py

│       │   │   └── ui.py

│       │   ├── io_anim_bvh

│       │   │   ├── __init__.py

│       │   │   ├── export_bvh.py

│       │   │   └── import_bvh.py

│       │   ├── io_anim_nuke_chan

│       │   │   ├── __init__.py

│       │   │   ├── export_nuke_chan.py

│       │   │   └── import_nuke_chan.py

│       │   ├── io_coat3D

│       │   │   ├── __init__.py

│       │   │   ├── data.json

│       │   │   ├── folders.py

│       │   │   ├── tex.py

│       │   │   ├── texVR.py

│       │   │   └── updateimage.py

│       │   ├── io_curve_svg

│       │   │   ├── __init__.py

│       │   │   ├── import_svg.py

│       │   │   ├── svg_colors.py

│       │   │   ├── svg_util.py

│       │   │   └── svg_util_test.py

│       │   ├── io_export_dxf

│       │   │   ├── draw_blenders

│       │   │   │   └── __init__.py

│       │   │   ├── model

│       │   │   │   ├── __init__.py

│       │   │   │   ├── dxfLibrary.py

│       │   │   │   ├── migiusModel.py

│       │   │   │   └── model.py

│       │   │   ├── primitive_exporters

│       │   │   │   ├── __init__.py

│       │   │   │   ├── base_exporter.py

│       │   │   │   ├── camera_exporter.py

│       │   │   │   ├── curve_exporter.py

│       │   │   │   ├── empty_exporter.py

│       │   │   │   ├── insert_exporter.py

│       │   │   │   ├── lamp_exporter.py

│       │   │   │   ├── mesh_exporter.py

│       │   │   │   ├── text_exporter.py

│       │   │   │   └── viewborder_exporter.py

│       │   │   ├── __init__.py

│       │   │   ├── export_dxf.py

│       │   │   ├── operator.py

│       │   │   └── test2.txt

│       │   ├── io_import_dxf

│       │   │   ├── dxfgrabber

│       │   │   │   ├── __init__.py

│       │   │   │   ├── acdsdata.py

│       │   │   │   ├── blockssection.py

│       │   │   │   ├── codepage.py

│       │   │   │   ├── color.py

│       │   │   │   ├── const.py

│       │   │   │   ├── decode.py

│       │   │   │   ├── defaultchunk.py

│       │   │   │   ├── drawing.py

│       │   │   │   ├── dxfentities.py

│       │   │   │   ├── dxfobjects.py

│       │   │   │   ├── entitysection.py

│       │   │   │   ├── headersection.py

│       │   │   │   ├── juliandate.py

│       │   │   │   ├── layers.py

│       │   │   │   ├── linetypes.py

│       │   │   │   ├── sections.py

│       │   │   │   ├── styles.py

│       │   │   │   ├── tablessection.py

│       │   │   │   └── tags.py

│       │   │   ├── dxfimport

│       │   │   │   ├── __init__.py

│       │   │   │   ├── convert.py

│       │   │   │   ├── do.py

│       │   │   │   ├── fake_entities.py

│       │   │   │   ├── groupsort.py

│       │   │   │   ├── is_.py

│       │   │   │   └── line_merger.py

│       │   │   ├── __init__.py

│       │   │   └── transverse_mercator.py

│       │   ├── io_import_palette

│       │   │   ├── __init__.py

│       │   │   ├── import_ase.py

│       │   │   └── import_krita.py

│       │   ├── io_mesh_atomic

│       │   │   ├── __init__.py

│       │   │   ├── atom_info.dat

│       │   │   ├── pdb_export.py

│       │   │   ├── pdb_gui.py

│       │   │   ├── pdb_import.py

│       │   │   ├── utility_gui.py

│       │   │   ├── utility_panel.py

│       │   │   ├── xyz_export.py

│       │   │   ├── xyz_gui.py

│       │   │   └── xyz_import.py

│       │   ├── io_mesh_stl

│       │   │   ├── __init__.py

│       │   │   ├── blender_utils.py

│       │   │   └── stl_utils.py

│       │   ├── io_mesh_uv_layout

│       │   │   ├── __init__.py

│       │   │   ├── export_uv_eps.py

│       │   │   ├── export_uv_png.py

│       │   │   └── export_uv_svg.py

│       │   ├── io_scene_3ds

│       │   │   ├── __init__.py

│       │   │   ├── export_3ds.py

│       │   │   └── import_3ds.py

│       │   ├── io_scene_fbx

│       │   │   ├── __init__.py

│       │   │   ├── data_types.py

│       │   │   ├── encode_bin.py

│       │   │   ├── export_fbx_bin.py

│       │   │   ├── fbx2json.py

│       │   │   ├── fbx_utils.py

│       │   │   ├── fbx_utils_threading.py

│       │   │   ├── import_fbx.py

│       │   │   ├── json2fbx.py

│       │   │   └── parse_fbx.py

│       │   ├── io_scene_gltf2

│       │   │   ├── blender

│       │   │   │   ├── com

│       │   │   │   │   ├── gltf2_blender_conversion.py

│       │   │   │   │   ├── gltf2_blender_data_path.py

│       │   │   │   │   ├── gltf2_blender_default.py

│       │   │   │   │   ├── gltf2_blender_extras.py

│       │   │   │   │   ├── gltf2_blender_json.py

│       │   │   │   │   ├── gltf2_blender_material_helpers.py

│       │   │   │   │   ├── gltf2_blender_math.py

│       │   │   │   │   ├── gltf2_blender_ui.py

│       │   │   │   │   └── gltf2_blender_utils.py

│       │   │   │   ├── exp

│       │   │   │   │   ├── animation

│       │   │   │   │   │   ├── fcurves

│       │   │   │   │   │   │   ├── gltf2_blender_gather_fcurves_animation.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_fcurves_channel_target.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_fcurves_channels.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_fcurves_keyframes.py

│       │   │   │   │   │   │   └── gltf2_blender_gather_fcurves_sampler.py

│       │   │   │   │   │   ├── sampled

│       │   │   │   │   │   │   ├── armature

│       │   │   │   │   │   │   │   ├── armature_action_sampled.py

│       │   │   │   │   │   │   │   ├── armature_channel_target.py

│       │   │   │   │   │   │   │   ├── armature_channels.py

│       │   │   │   │   │   │   │   ├── armature_keyframes.py

│       │   │   │   │   │   │   │   └── armature_sampler.py

│       │   │   │   │   │   │   ├── object

│       │   │   │   │   │   │   │   ├── gltf2_blender_gather_object_action_sampled.py

│       │   │   │   │   │   │   │   ├── gltf2_blender_gather_object_channel_target.py

│       │   │   │   │   │   │   │   ├── gltf2_blender_gather_object_channels.py

│       │   │   │   │   │   │   │   ├── gltf2_blender_gather_object_keyframes.py

│       │   │   │   │   │   │   │   └── gltf2_blender_gather_object_sampler.py

│       │   │   │   │   │   │   ├── shapekeys

│       │   │   │   │   │   │   │   ├── gltf2_blender_gather_sk_action_sampled.py

│       │   │   │   │   │   │   │   ├── gltf2_blender_gather_sk_channel_target.py

│       │   │   │   │   │   │   │   ├── gltf2_blender_gather_sk_channels.py

│       │   │   │   │   │   │   │   ├── gltf2_blender_gather_sk_keyframes.py

│       │   │   │   │   │   │   │   └── gltf2_blender_gather_sk_sampler.py

│       │   │   │   │   │   │   └── gltf2_blender_gather_animation_sampling_cache.py

│       │   │   │   │   │   ├── gltf2_blender_gather_action.py

│       │   │   │   │   │   ├── gltf2_blender_gather_animation_utils.py

│       │   │   │   │   │   ├── gltf2_blender_gather_animations.py

│       │   │   │   │   │   ├── gltf2_blender_gather_drivers.py

│       │   │   │   │   │   ├── gltf2_blender_gather_keyframes.py

│       │   │   │   │   │   ├── gltf2_blender_gather_scene_animation.py

│       │   │   │   │   │   └── gltf2_blender_gather_tracks.py

│       │   │   │   │   ├── material

│       │   │   │   │   │   ├── extensions

│       │   │   │   │   │   │   ├── gltf2_blender_gather_materials_anisotropy.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_materials_clearcoat.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_materials_emission.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_materials_ior.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_materials_sheen.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_materials_specular.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_materials_transmission.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_materials_variants.py

│       │   │   │   │   │   │   ├── gltf2_blender_gather_materials_volume.py

│       │   │   │   │   │   │   └── gltf2_blender_image.py

│       │   │   │   │   │   ├── gltf2_blender_gather_image.py

│       │   │   │   │   │   ├── gltf2_blender_gather_materials.py

│       │   │   │   │   │   ├── gltf2_blender_gather_materials_pbr_metallic_roughness.py

│       │   │   │   │   │   ├── gltf2_blender_gather_materials_unlit.py

│       │   │   │   │   │   ├── gltf2_blender_gather_texture.py

│       │   │   │   │   │   ├── gltf2_blender_gather_texture_info.py

│       │   │   │   │   │   └── gltf2_blender_search_node_tree.py

│       │   │   │   │   ├── gltf2_blender_export.py

│       │   │   │   │   ├── gltf2_blender_gather.py

│       │   │   │   │   ├── gltf2_blender_gather_accessors.py

│       │   │   │   │   ├── gltf2_blender_gather_cache.py

│       │   │   │   │   ├── gltf2_blender_gather_cameras.py

│       │   │   │   │   ├── gltf2_blender_gather_joints.py

│       │   │   │   │   ├── gltf2_blender_gather_light_spots.py

│       │   │   │   │   ├── gltf2_blender_gather_lights.py

│       │   │   │   │   ├── gltf2_blender_gather_mesh.py

│       │   │   │   │   ├── gltf2_blender_gather_nodes.py

│       │   │   │   │   ├── gltf2_blender_gather_primitive_attributes.py

│       │   │   │   │   ├── gltf2_blender_gather_primitives.py

│       │   │   │   │   ├── gltf2_blender_gather_primitives_extract.py

│       │   │   │   │   ├── gltf2_blender_gather_sampler.py

│       │   │   │   │   ├── gltf2_blender_gather_skins.py

│       │   │   │   │   ├── gltf2_blender_gather_tree.py

│       │   │   │   │   ├── gltf2_blender_get.py

│       │   │   │   │   └── gltf2_blender_gltf2_exporter.py

│       │   │   │   ├── imp

│       │   │   │   │   ├── gltf2_blender_KHR_materials_anisotropy.py

│       │   │   │   │   ├── gltf2_blender_KHR_materials_pbrSpecularGlossiness.py

│       │   │   │   │   ├── gltf2_blender_KHR_materials_unlit.py

│       │   │   │   │   ├── gltf2_blender_animation.py

│       │   │   │   │   ├── gltf2_blender_animation_node.py

│       │   │   │   │   ├── gltf2_blender_animation_utils.py

│       │   │   │   │   ├── gltf2_blender_animation_weight.py

│       │   │   │   │   ├── gltf2_blender_camera.py

│       │   │   │   │   ├── gltf2_blender_gltf.py

│       │   │   │   │   ├── gltf2_blender_image.py

│       │   │   │   │   ├── gltf2_blender_light.py

│       │   │   │   │   ├── gltf2_blender_material.py

│       │   │   │   │   ├── gltf2_blender_material_utils.py

│       │   │   │   │   ├── gltf2_blender_mesh.py

│       │   │   │   │   ├── gltf2_blender_node.py

│       │   │   │   │   ├── gltf2_blender_pbrMetallicRoughness.py

│       │   │   │   │   ├── gltf2_blender_scene.py

│       │   │   │   │   ├── gltf2_blender_texture.py

│       │   │   │   │   ├── gltf2_blender_vnode.py

│       │   │   │   │   └── gltf2_io_draco_compression_extension.py

│       │   │   │   └── __init__.py

│       │   │   ├── io

│       │   │   │   ├── com

│       │   │   │   │   ├── gltf2_io.py

│       │   │   │   │   ├── gltf2_io_constants.py

│       │   │   │   │   ├── gltf2_io_debug.py

│       │   │   │   │   ├── gltf2_io_draco_compression_extension.py

│       │   │   │   │   ├── gltf2_io_extensions.py

│       │   │   │   │   ├── gltf2_io_lights_punctual.py

│       │   │   │   │   ├── gltf2_io_path.py

│       │   │   │   │   └── gltf2_io_variants.py

│       │   │   │   ├── exp

│       │   │   │   │   ├── gltf2_io_binary_data.py

│       │   │   │   │   ├── gltf2_io_buffer.py

│       │   │   │   │   ├── gltf2_io_draco_compression_extension.py

│       │   │   │   │   ├── gltf2_io_export.py

│       │   │   │   │   ├── gltf2_io_image_data.py

│       │   │   │   │   └── gltf2_io_user_extensions.py

│       │   │   │   ├── imp

│       │   │   │   │   ├── __init__.py

│       │   │   │   │   ├── gltf2_io_binary.py

│       │   │   │   │   ├── gltf2_io_gltf.py

│       │   │   │   │   └── gltf2_io_user_extensions.py

│       │   │   │   └── __init__.py

│       │   │   └── __init__.py

│       │   ├── io_scene_x3d

│       │   │   ├── __init__.py

│       │   │   ├── export_x3d.py

│       │   │   └── import_x3d.py

│       │   ├── io_shape_mdd

│       │   │   ├── __init__.py

│       │   │   ├── export_mdd.py

│       │   │   └── import_mdd.py

│       │   ├── magic_uv

│       │   │   ├── op

│       │   │   │   ├── __init__.py

│       │   │   │   ├── align_uv.py

│       │   │   │   ├── align_uv_cursor.py

│       │   │   │   ├── clip_uv.py

│       │   │   │   ├── copy_paste_uv.py

│       │   │   │   ├── copy_paste_uv_object.py

│       │   │   │   ├── copy_paste_uv_uvedit.py

│       │   │   │   ├── flip_rotate_uv.py

│       │   │   │   ├── mirror_uv.py

│       │   │   │   ├── move_uv.py

│       │   │   │   ├── pack_uv.py

│       │   │   │   ├── preserve_uv_aspect.py

│       │   │   │   ├── select_uv.py

│       │   │   │   ├── smooth_uv.py

│       │   │   │   ├── texture_lock.py

│       │   │   │   ├── texture_projection.py

│       │   │   │   ├── texture_wrap.py

│       │   │   │   ├── transfer_uv.py

│       │   │   │   ├── unwrap_constraint.py

│       │   │   │   ├── uv_bounding_box.py

│       │   │   │   ├── uv_inspection.py

│       │   │   │   ├── uv_sculpt.py

│       │   │   │   ├── uvw.py

│       │   │   │   └── world_scale_uv.py

│       │   │   ├── ui

│       │   │   │   ├── IMAGE_MT_uvs.py

│       │   │   │   ├── VIEW3D_MT_object.py

│       │   │   │   ├── VIEW3D_MT_uv_map.py

│       │   │   │   ├── __init__.py

│       │   │   │   ├── uvedit_copy_paste_uv.py

│       │   │   │   ├── uvedit_editor_enhancement.py

│       │   │   │   ├── uvedit_uv_manipulation.py

│       │   │   │   ├── view3d_copy_paste_uv_editmode.py

│       │   │   │   ├── view3d_copy_paste_uv_objectmode.py

│       │   │   │   ├── view3d_uv_manipulation.py

│       │   │   │   └── view3d_uv_mapping.py

│       │   │   ├── utils

│       │   │   │   ├── __init__.py

│       │   │   │   ├── bl_class_registry.py

│       │   │   │   ├── compatibility.py

│       │   │   │   ├── graph.py

│       │   │   │   └── property_class_registry.py

│       │   │   ├── __init__.py

│       │   │   ├── common.py

│       │   │   ├── preferences.py

│       │   │   └── properties.py

│       │   ├── materials_library_vx

│       │   │   ├── __init__.py

│       │   │   ├── categories.txt

│       │   │   ├── sample_materials.blend

│       │   │   └── templates.blend

│       │   ├── materials_utils

│       │   │   ├── __init__.py

│       │   │   ├── enum_values.py

│       │   │   ├── functions.py

│       │   │   ├── menus.py

│       │   │   ├── operators.py

│       │   │   └── preferences.py

│       │   ├── measureit

│       │   │   ├── __init__.py

│       │   │   ├── measureit_geometry.py

│       │   │   ├── measureit_main.py

│       │   │   └── measureit_render.py

│       │   ├── mesh_inset

│       │   │   ├── __init__.py

│       │   │   ├── geom.py

│       │   │   ├── model.py

│       │   │   ├── offset.py

│       │   │   └── triquad.py

│       │   ├── mesh_snap_utilities_line

│       │   │   ├── icons

│       │   │   │   └── ops.mesh.snap_utilities_line.dat

│       │   │   ├── snap_context_l

│       │   │   │   ├── __init__.py

│       │   │   │   ├── mesh_drawing.py

│       │   │   │   └── utils_projection.py

│       │   │   ├── __init__.py

│       │   │   ├── common_classes.py

│       │   │   ├── common_utilities.py

│       │   │   ├── drawing_utilities.py

│       │   │   ├── keys.py

│       │   │   ├── navigation_ops.py

│       │   │   ├── op_line.py

│       │   │   ├── preferences.py

│       │   │   └── widgets.py

│       │   ├── mesh_tiny_cad

│       │   │   ├── icons

│       │   │   │   ├── BIX.png

│       │   │   │   ├── CCEN.png

│       │   │   │   ├── E2F.png

│       │   │   │   ├── V2X.png

│       │   │   │   ├── VTX.png

│       │   │   │   └── XALL.png

│       │   │   ├── BIX.py

│       │   │   ├── CCEN.py

│       │   │   ├── CFG.py

│       │   │   ├── E2F.py

│       │   │   ├── README.md

│       │   │   ├── V2X.py

│       │   │   ├── VTX.py

│       │   │   ├── XALL.py

│       │   │   ├── __init__.py

│       │   │   └── cad_module.py

│       │   ├── mesh_tissue

│       │   │   ├── README.md

│       │   │   ├── __init__.py

│       │   │   ├── config.py

│       │   │   ├── contour_curves.py

│       │   │   ├── curves_tools.py

│       │   │   ├── dual_mesh.py

│       │   │   ├── lattice.py

│       │   │   ├── material_tools.py

│       │   │   ├── numba_functions.py

│       │   │   ├── polyhedra.py

│       │   │   ├── tessellate_numpy.py

│       │   │   ├── texture_reaction_diffusion.py

│       │   │   ├── tissue_properties.py

│       │   │   ├── utils.py

│       │   │   ├── utils_pip.py

│       │   │   ├── uv_to_mesh.py

│       │   │   ├── weight_reaction_diffusion.py

│       │   │   └── weight_tools.py

│       │   ├── mesh_tools

│       │   │   ├── __init__.py

│       │   │   ├── face_inset_fillet.py

│       │   │   ├── mesh_cut_faces.py

│       │   │   ├── mesh_edge_roundifier.py

│       │   │   ├── mesh_edges_floor_plan.py

│       │   │   ├── mesh_edges_length.py

│       │   │   ├── mesh_edgetools.py

│       │   │   ├── mesh_extrude_and_reshape.py

│       │   │   ├── mesh_filletplus.py

│       │   │   ├── mesh_mextrude_plus.py

│       │   │   ├── mesh_offset_edges.py

│       │   │   ├── mesh_relax.py

│       │   │   ├── mesh_vertex_chamfer.py

│       │   │   ├── pkhg_faces.py

│       │   │   ├── random_vertices.py

│       │   │   ├── split_solidify.py

│       │   │   └── vertex_align.py

│       │   ├── node_wrangler

│       │   │   ├── utils

│       │   │   │   ├── constants.py

│       │   │   │   ├── draw.py

│       │   │   │   ├── nodes.py

│       │   │   │   ├── paths.py

│       │   │   │   └── paths_test.py

│       │   │   ├── README.md

│       │   │   ├── __init__.py

│       │   │   ├── interface.py

│       │   │   ├── operators.py

│       │   │   └── preferences.py

│       │   ├── object_carver

│       │   │   ├── __init__.py

│       │   │   ├── carver_draw.py

│       │   │   ├── carver_operator.py

│       │   │   ├── carver_preferences.py

│       │   │   ├── carver_profils.py

│       │   │   └── carver_utils.py

│       │   ├── object_collection_manager

│       │   │   ├── icons

│       │   │   │   └── minus.png

│       │   │   ├── __init__.py

│       │   │   ├── cm_init.py

│       │   │   ├── internals.py

│       │   │   ├── operator_utils.py

│       │   │   ├── operators.py

│       │   │   ├── persistent_data.py

│       │   │   ├── preferences.py

│       │   │   ├── qcd_init.py

│       │   │   ├── qcd_move_widget.py

│       │   │   ├── qcd_operators.py

│       │   │   └── ui.py

│       │   ├── object_fracture_cell

│       │   │   ├── __init__.py

│       │   │   ├── fracture_cell_calc.py

│       │   │   └── fracture_cell_setup.py

│       │   ├── object_print3d_utils

│       │   │   ├── __init__.py

│       │   │   ├── export.py

│       │   │   ├── mesh_helpers.py

│       │   │   ├── operators.py

│       │   │   ├── report.py

│       │   │   ├── todo.rst

│       │   │   └── ui.py

│       │   ├── object_scatter

│       │   │   ├── __init__.py

│       │   │   ├── operator.py

│       │   │   └── ui.py

│       │   ├── pose_library

│       │   │   ├── __init__.py

│       │   │   ├── asset_browser.py

│       │   │   ├── conversion.py

│       │   │   ├── functions.py

│       │   │   ├── gui.py

│       │   │   ├── keymaps.py

│       │   │   ├── operators.py

│       │   │   ├── pose_creation.py

│       │   │   └── pose_usage.py

│       │   ├── power_sequencer

│       │   │   ├── operators

│       │   │   │   ├── render_presets

│       │   │   │   │   ├── twitter_720p.py

│       │   │   │   │   └── youtube_1080.py

│       │   │   │   ├── utils

│       │   │   │   │   ├── __init__.py

│       │   │   │   │   ├── doc.py

│       │   │   │   │   ├── draw.py

│       │   │   │   │   ├── functions.py

│       │   │   │   │   ├── global_settings.py

│       │   │   │   │   └── info_progress_bar.py

│       │   │   │   ├── __init__.py

│       │   │   │   ├── channel_offset.py

│       │   │   │   ├── concatenate_strips.py

│       │   │   │   ├── copy_selected_sequences.py

│       │   │   │   ├── crossfade_add.py

│       │   │   │   ├── crossfade_edit.py

│       │   │   │   ├── cut_strips_under_cursor.py

│       │   │   │   ├── delete_direct.py

│       │   │   │   ├── deselect_all_left_or_right.py

│       │   │   │   ├── deselect_handles_and_grab.py

│       │   │   │   ├── duplicate_move.py

│       │   │   │   ├── expand_to_surrounding_cuts.py

│       │   │   │   ├── fade_add.py

│       │   │   │   ├── fade_clear.py

│       │   │   │   ├── gap_remove.py

│       │   │   │   ├── grab.py

│       │   │   │   ├── grab_closest_handle_or_cut.py

│       │   │   │   ├── grab_sequence_handles.py

│       │   │   │   ├── import_local_footage.py

│       │   │   │   ├── jump_time_offset.py

│       │   │   │   ├── jump_to_cut.py

│       │   │   │   ├── make_hold_frame.py

│       │   │   │   ├── marker_delete_closest.py

│       │   │   │   ├── marker_delete_direct.py

│       │   │   │   ├── marker_snap_to_cursor.py

│       │   │   │   ├── markers_as_timecodes.py

│       │   │   │   ├── markers_create_from_selected.py

│       │   │   │   ├── markers_set_preview_in_between.py

│       │   │   │   ├── markers_snap_matching_strips.py

│       │   │   │   ├── meta_resize_to_content.py

│       │   │   │   ├── meta_trim_content_to_bounds.py

│       │   │   │   ├── meta_ungroup_and_trim.py

│       │   │   │   ├── mouse_toggle_mute.py

│       │   │   │   ├── mouse_trim_instantly.py

│       │   │   │   ├── mouse_trim_modal.py

│       │   │   │   ├── open_project_directory.py

│       │   │   │   ├── playback_speed_set.py

│       │   │   │   ├── preview_closest_cut.py

│       │   │   │   ├── preview_to_selection.py

│       │   │   │   ├── render_apply_preset.py

│       │   │   │   ├── ripple_delete.py

│       │   │   │   ├── save_direct.py

│       │   │   │   ├── scene_create_from_selection.py

│       │   │   │   ├── scene_cycle.py

│       │   │   │   ├── scene_merge_from.py

│       │   │   │   ├── scene_open_from_strip.py

│       │   │   │   ├── scene_rename_with_strip.py

│       │   │   │   ├── select_all_left_or_right.py

│       │   │   │   ├── select_closest_to_mouse.py

│       │   │   │   ├── select_linked_effect.py

│       │   │   │   ├── select_linked_strips.py

│       │   │   │   ├── select_related_strips.py

│       │   │   │   ├── select_strips_under_cursor.py

│       │   │   │   ├── set_timeline_range.py

│       │   │   │   ├── snap.py

│       │   │   │   ├── snap_selection.py

│       │   │   │   ├── space_sequences.py

│       │   │   │   ├── speed_remove_effect.py

│       │   │   │   ├── speed_up_movie_strip.py

│       │   │   │   ├── swap_strips.py

│       │   │   │   ├── toggle_selected_mute.py

│       │   │   │   ├── toggle_waveforms.py

│       │   │   │   ├── transitions_remove.py

│       │   │   │   ├── trim_left_or_right_handles.py

│       │   │   │   ├── trim_three_point_edit.py

│       │   │   │   ├── trim_to_surrounding_cuts.py

│       │   │   │   └── value_offset.py

│       │   │   ├── tools

│       │   │   │   ├── __init__.py

│       │   │   │   └── trim.py

│       │   │   ├── ui

│       │   │   │   ├── __init__.py

│       │   │   │   ├── menu_contextual.py

│       │   │   │   └── menu_toolbar.py

│       │   │   ├── utils

│       │   │   │   ├── addon_auto_imports.py

│       │   │   │   └── register_shortcuts.py

│       │   │   ├── __init__.py

│       │   │   ├── addon_preferences.py

│       │   │   ├── addon_properties.py

│       │   │   └── handlers.py

│       │   ├── precision_drawing_tools

│       │   │   ├── __init__.py

│       │   │   ├── pdt_bix.py

│       │   │   ├── pdt_cad_module.py

│       │   │   ├── pdt_command.py

│       │   │   ├── pdt_command_functions.py

│       │   │   ├── pdt_design.py

│       │   │   ├── pdt_etof.py

│       │   │   ├── pdt_exception.py

│       │   │   ├── pdt_functions.py

│       │   │   ├── pdt_library.py

│       │   │   ├── pdt_menus.py

│       │   │   ├── pdt_msg_strings.py

│       │   │   ├── pdt_pivot_point.py

│       │   │   ├── pdt_tangent.py

│       │   │   ├── pdt_trig_waves.py

│       │   │   ├── pdt_view.py

│       │   │   └── pdt_xall.py

│       │   ├── presets

│       │   │   ├── interface_theme

│       │   │   │   ├── Deep_Grey.xml

│       │   │   │   ├── Maya.xml

│       │   │   │   ├── Minimal_Dark.xml

│       │   │   │   ├── Modo.xml

│       │   │   │   ├── Print_Friendly.xml

│       │   │   │   ├── White.xml

│       │   │   │   └── XSI.xml

│       │   │   ├── operator

│       │   │   │   ├── curve.torus_knot_plus

│       │   │   │   │   ├── 13x8_wicker_globe.py

│       │   │   │   │   ├── 7x6.py

│       │   │   │   │   ├── 9x9_color.py

│       │   │   │   │   ├── braided_coil.py

│       │   │   │   │   ├── flower_mesh_(2d).py

│       │   │   │   │   ├── slinky_knot.py

│       │   │   │   │   ├── snowflake_(2d).py

│       │   │   │   │   ├── sun_cross_(2d).py

│       │   │   │   │   ├── tripple_dna.py

│       │   │   │   │   └── wicker_basket.py

│       │   │   │   ├── mesh.bolt_add

│       │   │   │   │   ├── _default.py

│       │   │   │   │   ├── _m3.py

│       │   │   │   │   ├── _m4.py

│       │   │   │   │   ├── _m5.py

│       │   │   │   │   ├── _m6.py

│       │   │   │   │   ├── _m8.py

│       │   │   │   │   ├── m10.py

│       │   │   │   │   └── m12.py

│       │   │   │   ├── mesh.eroder

│       │   │   │   │   ├── default.py

│       │   │   │   │   ├── light_erosion.py

│       │   │   │   │   ├── medium_erosion.py

│       │   │   │   │   ├── strong_erosion.py

│       │   │   │   │   └── thermal_diffusion.py

│       │   │   │   ├── mesh.landscape_add

│       │   │   │   │   ├── abstract.py

│       │   │   │   │   ├── another_noise.py

│       │   │   │   │   ├── billow.py

│       │   │   │   │   ├── canyon.py

│       │   │   │   │   ├── canyons.py

│       │   │   │   │   ├── cauliflower_hills.py

│       │   │   │   │   ├── cliff.py

│       │   │   │   │   ├── crystalline.py

│       │   │   │   │   ├── default.py

│       │   │   │   │   ├── default_large.py

│       │   │   │   │   ├── dunes.py

│       │   │   │   │   ├── flatstones.py

│       │   │   │   │   ├── gully.py

│       │   │   │   │   ├── lakes_1.py

│       │   │   │   │   ├── lakes_2.py

│       │   │   │   │   ├── large_terrain.py

│       │   │   │   │   ├── mesa.py

│       │   │   │   │   ├── mounds.py

│       │   │   │   │   ├── mountain_1.py

│       │   │   │   │   ├── mountain_2.py

│       │   │   │   │   ├── planet.py

│       │   │   │   │   ├── planet_noise.py

│       │   │   │   │   ├── ridged.py

│       │   │   │   │   ├── river.py

│       │   │   │   │   ├── rock.py

│       │   │   │   │   ├── slick_rock.py

│       │   │   │   │   ├── tech_effect.py

│       │   │   │   │   ├── techno_cell.py

│       │   │   │   │   ├── vlnoise_turbulence.py

│       │   │   │   │   ├── volcano.py

│       │   │   │   │   ├── voronoi_hills.py

│       │   │   │   │   └── yin_yang.py

│       │   │   │   ├── mesh.primitive_round_cube_add

│       │   │   │   │   ├── Capsule.py

│       │   │   │   │   ├── Clay_Bar.py

│       │   │   │   │   ├── Cube.py

│       │   │   │   │   ├── Grid_3D.py

│       │   │   │   │   ├── Octahedron.py

│       │   │   │   │   ├── Quadsphere.py

│       │   │   │   │   └── Rounded_Cube.py

│       │   │   │   └── mesh.primitive_xyz_function_surface

│       │   │   │       ├── bonbon.py

│       │   │   │       ├── boy.py

│       │   │   │       ├── catalan.py

│       │   │   │       ├── catenoid.py

│       │   │   │       ├── clifford_torus.py

│       │   │   │       ├── cochlea.py

│       │   │   │       ├── cosinus.py

│       │   │   │       ├── dini.py

│       │   │   │       ├── enneper.py

│       │   │   │       ├── helicoidal.py

│       │   │   │       ├── helix.py

│       │   │   │       ├── hexahedron.py

│       │   │   │       ├── hyperhelicoidal.py

│       │   │   │       ├── klein.py

│       │   │   │       ├── moebius.py

│       │   │   │       ├── pseudo_catenoid.py

│       │   │   │       ├── pseudosphere.py

│       │   │   │       ├── ridged_torus.py

│       │   │   │       ├── shell.py

│       │   │   │       ├── sine.py

│       │   │   │       ├── snake.py

│       │   │   │       ├── sterosphere.py

│       │   │   │       ├── torus.py

│       │   │   │       └── twisted_torus.py

│       │   │   └── pov

│       │   │       ├── light

│       │   │       │   ├── 01_(4800K)_Direct_Sun.py

│       │   │       │   ├── 02_(5400K)_High_Noon_Sun.py

│       │   │       │   ├── 03_(6000K)_Daylight_Window.py

│       │   │       │   ├── 04_(6000K)_2500W_HMI_(Halogen_Metal_Iodide).py

│       │   │       │   ├── 05_(4000K)_100W_Metal_Halide.py

│       │   │       │   ├── 06_(3200K)_100W_Quartz_Halogen.py

│       │   │       │   ├── 07_(2850K)_100w_Tungsten.py

│       │   │       │   ├── 08_(2600K)_40w_Tungsten.py

│       │   │       │   ├── 09_(5000K)_75W_Full_Spectrum_Fluorescent_T12.py

│       │   │       │   ├── 10_(4300K)_40W_Vintage_Fluorescent_T12.py

│       │   │       │   ├── 11_(5000K)_18W_Standard_Fluorescent_T8.py

│       │   │       │   ├── 12_(4200K)_18W_Cool_White_Fluorescent_T8.py

│       │   │       │   ├── 13_(3000K)_18W_Warm_Fluorescent_T8.py

│       │   │       │   ├── 14_(6500K)_54W_Grow_Light_Fluorescent_T5-HO.py

│       │   │       │   ├── 15_(3200K)_40W_Induction_Fluorescent.py

│       │   │       │   ├── 16_(2100K)_150W_High_Pressure_Sodium.py

│       │   │       │   ├── 17_(1700K)_135W_Low_Pressure_Sodium.py

│       │   │       │   ├── 18_(6800K)_175W_Mercury_Vapor.py

│       │   │       │   ├── 19_(5200K)_700W_Carbon_Arc.py

│       │   │       │   ├── 20_(6500K)_15W_LED_Spot.py

│       │   │       │   ├── 21_(2700K)_7W_OLED_Panel.py

│       │   │       │   ├── 22_(30000K)_40W_Black_Light_Fluorescent.py

│       │   │       │   ├── 23_(30000K)_40W_Black_Light_Bulb.py

│       │   │       │   └── 24_(1850K)_Candle.py

│       │   │       ├── material

│       │   │       │   └── sss

│       │   │       │       ├── apple.py

│       │   │       │       ├── chicken.py

│       │   │       │       ├── cream.py

│       │   │       │       ├── ketchup.py

│       │   │       │       ├── marble.py

│       │   │       │       ├── potato.py

│       │   │       │       ├── skim_milk.py

│       │   │       │       ├── skin1.py

│       │   │       │       ├── skin2.py

│       │   │       │       └── whole_milk.py

│       │   │       ├── radiosity

│       │   │       │   ├── 01_Debug.py

│       │   │       │   ├── 02_Fast.py

│       │   │       │   ├── 03_Normal.py

│       │   │       │   ├── 04_Two_Bounces.py

│       │   │       │   ├── 05_Final.py

│       │   │       │   ├── 06_Outdoor_Low_Quality.py

│       │   │       │   ├── 07_Outdoor_High_Quality.py

│       │   │       │   ├── 08_Outdoor_(Sun)Light.py

│       │   │       │   ├── 09_Indoor_Low_Quality.py

│       │   │       │   └── 10_Indoor_High_Quality.py

│       │   │       └── world

│       │   │           ├── 1_Clear_Blue_Sky.py

│       │   │           ├── 2_Partly_Hazy_Sky.py

│       │   │           ├── 3_Overcast_Sky.py

│       │   │           ├── 4_Cartoony_Sky.py

│       │   │           └── 5_Under_Water.py

│       │   ├── render_copy_settings

│       │   │   ├── __init__.py

│       │   │   ├── data.py

│       │   │   ├── operator.py

│       │   │   ├── panel.py

│       │   │   ├── presets.py

│       │   │   └── translations.py

│       │   ├── render_povray

│       │   │   ├── icons

│       │   │   │   ├── pov.add.blobcapsule.dat

│       │   │   │   ├── pov.add.blobcube.dat

│       │   │   │   ├── pov.add.blobellipsoid.dat

│       │   │   │   ├── pov.add.blobplane.dat

│       │   │   │   ├── pov.add.blobsphere.dat

│       │   │   │   ├── pov.add.box.dat

│       │   │   │   ├── pov.add.cone.dat

│       │   │   │   ├── pov.add.cylinder.dat

│       │   │   │   ├── pov.add.heightfield.dat

│       │   │   │   ├── pov.add.infinite_plane.dat

│       │   │   │   ├── pov.add.isosurface.dat

│       │   │   │   ├── pov.add.isosurfacebox.dat

│       │   │   │   ├── pov.add.isosurfacesphere.dat

│       │   │   │   ├── pov.add.isosurfacesupertorus.dat

│       │   │   │   ├── pov.add.lathe.dat

│       │   │   │   ├── pov.add.loft.dat

│       │   │   │   ├── pov.add.parametric.dat

│       │   │   │   ├── pov.add.polygontocircle.dat

│       │   │   │   ├── pov.add.prism.dat

│       │   │   │   ├── pov.add.rainbow.dat

│       │   │   │   ├── pov.add.sphere.dat

│       │   │   │   ├── pov.add.spheresweep.dat

│       │   │   │   ├── pov.add.superellipsoid.dat

│       │   │   │   └── pov.add.torus.dat

│       │   │   ├── templates_pov

│       │   │   │   ├── abyss.pov

│       │   │   │   ├── biscuit.pov

│       │   │   │   ├── bsp_Tango.pov

│       │   │   │   ├── chess2.pov

│       │   │   │   ├── cornell.pov

│       │   │   │   ├── diffract.pov

│       │   │   │   ├── diffuse_back.pov

│       │   │   │   ├── float5.pov

│       │   │   │   ├── gamma_showcase.pov

│       │   │   │   ├── grenadine.pov

│       │   │   │   ├── isocacti.pov

│       │   │   │   ├── mediasky.pov

│       │   │   │   ├── patio-radio.pov

│       │   │   │   ├── subsurface.pov

│       │   │   │   └── wallstucco.pov

│       │   │   ├── __init__.py

│       │   │   ├── model_all.py

│       │   │   ├── model_curve_topology.py

│       │   │   ├── model_gui.py

│       │   │   ├── model_meta_topology.py

│       │   │   ├── model_poly_topology.py

│       │   │   ├── model_primitives.py

│       │   │   ├── model_primitives_topology.py

│       │   │   ├── model_properties.py

│       │   │   ├── nodes.py

│       │   │   ├── nodes_fn.py

│       │   │   ├── nodes_gui.py

│       │   │   ├── nodes_properties.py

│       │   │   ├── particles.py

│       │   │   ├── particles_properties.py

│       │   │   ├── render.py

│       │   │   ├── render_core.py

│       │   │   ├── render_gui.py

│       │   │   ├── render_properties.py

│       │   │   ├── scenography.py

│       │   │   ├── scenography_gui.py

│       │   │   ├── scenography_properties.py

│       │   │   ├── scripting.py

│       │   │   ├── scripting_gui.py

│       │   │   ├── scripting_properties.py

│       │   │   ├── shading.py

│       │   │   ├── shading_gui.py

│       │   │   ├── shading_properties.py

│       │   │   ├── shading_ray_properties.py

│       │   │   ├── texturing.py

│       │   │   ├── texturing_gui.py

│       │   │   ├── texturing_procedural.py

│       │   │   ├── texturing_properties.py

│       │   │   ├── ui_core.py

│       │   │   ├── update_files.py

│       │   │   └── voxel_lib.py

│       │   ├── rigify

│       │   │   ├── feature_sets

│       │   │   │   └── __init__.py

│       │   │   ├── metarigs

│       │   │   │   ├── Animals

│       │   │   │   │   ├── __init__.py

│       │   │   │   │   ├── bird.py

│       │   │   │   │   ├── cat.py

│       │   │   │   │   ├── horse.py

│       │   │   │   │   ├── shark.py

│       │   │   │   │   └── wolf.py

│       │   │   │   ├── Basic

│       │   │   │   │   ├── basic_human.py

│       │   │   │   │   └── basic_quadruped.py

│       │   │   │   ├── __init__.py

│       │   │   │   └── human.py

│       │   │   ├── operators

│       │   │   │   ├── __init__.py

│       │   │   │   ├── action_layers.py

│       │   │   │   ├── copy_mirror_parameters.py

│       │   │   │   ├── generic_ui_list.py

│       │   │   │   └── upgrade_face.py

│       │   │   ├── rigs

│       │   │   │   ├── basic

│       │   │   │   │   ├── __init__.py

│       │   │   │   │   ├── copy_chain.py

│       │   │   │   │   ├── pivot.py

│       │   │   │   │   ├── raw_copy.py

│       │   │   │   │   └── super_copy.py

│       │   │   │   ├── experimental

│       │   │   │   │   ├── __init__.py

│       │   │   │   │   └── super_chain.py

│       │   │   │   ├── face

│       │   │   │   │   ├── basic_tongue.py

│       │   │   │   │   ├── skin_eye.py

│       │   │   │   │   └── skin_jaw.py

│       │   │   │   ├── faces

│       │   │   │   │   ├── __init__.py

│       │   │   │   │   └── super_face.py

│       │   │   │   ├── limbs

│       │   │   │   │   ├── __init__.py

│       │   │   │   │   ├── arm.py

│       │   │   │   │   ├── front_paw.py

│       │   │   │   │   ├── leg.py

│       │   │   │   │   ├── limb_rigs.py

│       │   │   │   │   ├── limb_utils.py

│       │   │   │   │   ├── paw.py

│       │   │   │   │   ├── rear_paw.py

│       │   │   │   │   ├── simple_tentacle.py

│       │   │   │   │   ├── spline_tentacle.py

│       │   │   │   │   ├── super_finger.py

│       │   │   │   │   ├── super_limb.py

│       │   │   │   │   └── super_palm.py

│       │   │   │   ├── skin

│       │   │   │   │   ├── transform

│       │   │   │   │   │   └── basic.py

│       │   │   │   │   ├── anchor.py

│       │   │   │   │   ├── basic_chain.py

│       │   │   │   │   ├── glue.py

│       │   │   │   │   ├── skin_nodes.py

│       │   │   │   │   ├── skin_parents.py

│       │   │   │   │   ├── skin_rigs.py

│       │   │   │   │   └── stretchy_chain.py

│       │   │   │   ├── spines

│       │   │   │   │   ├── __init__.py

│       │   │   │   │   ├── basic_spine.py

│       │   │   │   │   ├── basic_tail.py

│       │   │   │   │   ├── spine_rigs.py

│       │   │   │   │   ├── super_head.py

│       │   │   │   │   └── super_spine.py

│       │   │   │   ├── __init__.py

│       │   │   │   ├── chain_rigs.py

│       │   │   │   ├── utils.py

│       │   │   │   └── widgets.py

│       │   │   ├── utils

│       │   │   │   ├── __init__.py

│       │   │   │   ├── action_layers.py

│       │   │   │   ├── animation.py

│       │   │   │   ├── bones.py

│       │   │   │   ├── collections.py

│       │   │   │   ├── components.py

│       │   │   │   ├── errors.py

│       │   │   │   ├── layers.py

│       │   │   │   ├── mechanism.py

│       │   │   │   ├── metaclass.py

│       │   │   │   ├── misc.py

│       │   │   │   ├── naming.py

│       │   │   │   ├── node_merger.py

│       │   │   │   ├── objects.py

│       │   │   │   ├── rig.py

│       │   │   │   ├── switch_parent.py

│       │   │   │   ├── widgets.py

│       │   │   │   ├── widgets_basic.py

│       │   │   │   └── widgets_special.py

│       │   │   ├── .pep8

│       │   │   ├── __init__.py

│       │   │   ├── base_generate.py

│       │   │   ├── base_rig.py

│       │   │   ├── feature_set_list.py

│       │   │   ├── generate.py

│       │   │   ├── metarig_menu.py

│       │   │   ├── rig_lists.py

│       │   │   ├── rig_ui_template.py

│       │   │   ├── rot_mode.py

│       │   │   └── ui.py

│       │   ├── space_view3d_brush_menus

│       │   │   ├── __init__.py

│       │   │   ├── brush_menu.py

│       │   │   ├── brushes.py

│       │   │   ├── curve_menu.py

│       │   │   ├── dyntopo_menu.py

│       │   │   ├── stroke_menu.py

│       │   │   ├── symmetry_menu.py

│       │   │   ├── texture_menu.py

│       │   │   └── utils_core.py

│       │   ├── space_view3d_math_vis

│       │   │   ├── __init__.py

│       │   │   ├── draw.py

│       │   │   └── utils.py

│       │   ├── space_view3d_pie_menus

│       │   │   ├── __init__.py

│       │   │   ├── pie_align_menu.py

│       │   │   ├── pie_animation_menu.py

│       │   │   ├── pie_apply_transform_menu.py

│       │   │   ├── pie_defaults_menu.py

│       │   │   ├── pie_delete_menu.py

│       │   │   ├── pie_editor_switch_menu.py

│       │   │   ├── pie_manipulator_menu.py

│       │   │   ├── pie_modes_menu.py

│       │   │   ├── pie_origin.py

│       │   │   ├── pie_proportional_menu.py

│       │   │   ├── pie_save_open_menu.py

│       │   │   ├── pie_sculpt_menu.py

│       │   │   ├── pie_select_menu.py

│       │   │   ├── pie_shading_menu.py

│       │   │   └── pie_views_numpad_menu.py

│       │   ├── space_view3d_spacebar_menu

│       │   │   ├── __init__.py

│       │   │   ├── animation_menus.py

│       │   │   ├── armature_menus.py

│       │   │   ├── curve_menus.py

│       │   │   ├── edit_mesh.py

│       │   │   ├── object_menus.py

│       │   │   ├── snap_origin_cursor.py

│       │   │   ├── transform_menus.py

│       │   │   └── view_menus.py

│       │   ├── space_view3d_stored_views

│       │   │   ├── __init__.py

│       │   │   ├── core.py

│       │   │   ├── io.py

│       │   │   ├── operators.py

│       │   │   ├── properties.py

│       │   │   ├── stored_views_test.py

│       │   │   └── ui.py

│       │   ├── storypencil

│       │   │   ├── __init__.py

│       │   │   ├── dopesheet_overlay.py

│       │   │   ├── render.py

│       │   │   ├── scene_tools.py

│       │   │   ├── sound.py

│       │   │   ├── synchro.py

│       │   │   ├── ui.py

│       │   │   └── utils.py

│       │   ├── sun_position

│       │   │   ├── __init__.py

│       │   │   ├── draw.py

│       │   │   ├── geo.py

│       │   │   ├── hdr.py

│       │   │   ├── properties.py

│       │   │   ├── sun_calc.py

│       │   │   ├── translations.py

│       │   │   └── ui_sun.py

│       │   ├── system_demo_mode

│       │   │   ├── __init__.py

│       │   │   ├── config.py

│       │   │   └── demo_mode.py

│       │   ├── ui_translate

│       │   │   ├── __init__.py

│       │   │   ├── edit_translation.py

│       │   │   ├── settings.py

│       │   │   ├── update_addon.py

│       │   │   ├── update_repo.py

│       │   │   └── update_ui.py

│       │   ├── vdm_brush_baker

│       │   │   ├── __init__.py

│       │   │   └── bakematerial.py

│       │   ├── viewport_vr_preview

│       │   │   ├── configs

│       │   │   │   └── default.py

│       │   │   ├── __init__.py

│       │   │   ├── action_map.py

│       │   │   ├── action_map_io.py

│       │   │   ├── defaults.py

│       │   │   ├── gui.py

│       │   │   ├── operators.py

│       │   │   ├── properties.py

│       │   │   └── versioning.py

│       │   ├── add_curve_ivygen.py

│       │   ├── animation_add_corrective_shape_key.py

│       │   ├── bone_selection_sets.py

│       │   ├── camera_turnaround.py

│       │   ├── copy_global_transform.py

│       │   ├── curve_assign_shapekey.py

│       │   ├── curve_simplify.py

│       │   ├── depsgraph_debug.py

│       │   ├── development_edit_operator.py

│       │   ├── development_icon_get.py

│       │   ├── development_iskeyfree.py

│       │   ├── io_anim_camera.py

│       │   ├── io_export_paper_model.py

│       │   ├── io_export_pc2.py

│       │   ├── io_import_BrushSet.py

│       │   ├── io_import_images_as_planes.py

│       │   ├── lighting_dynamic_sky.py

│       │   ├── lighting_tri_lights.py

│       │   ├── mesh_auto_mirror.py

│       │   ├── mesh_bsurfaces.py

│       │   ├── mesh_f2.py

│       │   ├── mesh_looptools.py

│       │   ├── node_arrange.py

│       │   ├── node_presets.py

│       │   ├── object_boolean_tools.py

│       │   ├── object_color_rules.py

│       │   ├── object_edit_linked.py

│       │   ├── object_skinify.py

│       │   ├── paint_palette.py

│       │   ├── real_snow.py

│       │   ├── render_freestyle_svg.py

│       │   ├── render_ui_animation_render.py

│       │   ├── space_clip_editor_refine_solution.py

│       │   ├── space_view3d_3d_navigation.py

│       │   ├── space_view3d_align_tools.py

│       │   ├── space_view3d_copy_attributes.py

│       │   ├── space_view3d_modifier_tools.py

│       │   ├── system_blend_info.py

│       │   └── system_property_chart.py

│       ├── addons_contrib

│       ├── freestyle

│       │   ├── modules

│       │   │   ├── freestyle

│       │   │   │   ├── __init__.py

│       │   │   │   ├── chainingiterators.py

│       │   │   │   ├── functions.py

│       │   │   │   ├── predicates.py

│       │   │   │   ├── shaders.py

│       │   │   │   ├── types.py

│       │   │   │   └── utils.py

│       │   │   └── parameter_editor.py

│       │   └── styles

│       │       ├── anisotropic_diffusion.py

│       │       ├── apriori_and_causal_density.py

│       │       ├── apriori_density.py

│       │       ├── backbone_stretcher.py

│       │       ├── blueprint_circles.py

│       │       ├── blueprint_ellipses.py

│       │       ├── blueprint_squares.py

│       │       ├── cartoon.py

│       │       ├── contour.py

│       │       ├── curvature2d.py

│       │       ├── external_contour.py

│       │       ├── external_contour_sketchy.py

│       │       ├── external_contour_smooth.py

│       │       ├── haloing.py

│       │       ├── ignore_small_occlusions.py

│       │       ├── invisible_lines.py

│       │       ├── japanese_bigbrush.py

│       │       ├── long_anisotropically_dense.py

│       │       ├── multiple_parameterization.py

│       │       ├── nature.py

│       │       ├── near_lines.py

│       │       ├── occluded_by_specific_object.py

│       │       ├── polygonalize.py

│       │       ├── qi0.py

│       │       ├── qi0_not_external_contour.py

│       │       ├── qi1.py

│       │       ├── qi2.py

│       │       ├── sequentialsplit_sketchy.py

│       │       ├── sketchy_multiple_parameterization.py

│       │       ├── sketchy_topology_broken.py

│       │       ├── sketchy_topology_preserved.py

│       │       ├── split_at_highest_2d_curvatures.py

│       │       ├── split_at_tvertices.py

│       │       ├── suggestive.py

│       │       ├── thickness_fof_depth_discontinuity.py

│       │       ├── tipremover.py

│       │       ├── tvertex_remover.py

│       │       └── uniformpruning_zsort.py

│       ├── modules

│       │   ├── bl_app_override

│       │   │   ├── __init__.py

│       │   │   └── helpers.py

│       │   ├── bl_console_utils

│       │   │   ├── autocomplete

│       │   │   │   ├── __init__.py

│       │   │   │   ├── complete_calltip.py

│       │   │   │   ├── complete_import.py

│       │   │   │   ├── complete_namespace.py

│       │   │   │   └── intellisense.py

│       │   │   └── __init__.py

│       │   ├── bl_i18n_utils

│       │   │   ├── __init__.py

│       │   │   ├── bl_extract_messages.py

│       │   │   ├── merge_po.py

│       │   │   ├── settings.py

│       │   │   ├── settings_user.py

│       │   │   ├── utils.py

│       │   │   ├── utils_cli.py

│       │   │   ├── utils_languages_menu.py

│       │   │   ├── utils_rtl.py

│       │   │   └── utils_spell_check.py

│       │   ├── bl_keymap_utils

│       │   │   ├── __init__.py

│       │   │   ├── io.py

│       │   │   ├── keymap_from_toolbar.py

│       │   │   ├── keymap_hierarchy.py

│       │   │   ├── platform_helpers.py

│       │   │   └── versioning.py

│       │   ├── bl_previews_utils

│       │   │   └── bl_previews_render.py

│       │   ├── bl_rna_utils

│       │   │   ├── __init__.py

│       │   │   └── data_path.py

│       │   ├── bl_text_utils

│       │   │   ├── __init__.py

│       │   │   └── external_editor.py

│       │   ├── bl_ui_utils

│       │   │   ├── __init__.py

│       │   │   ├── bug_report_url.py

│       │   │   └── layout.py

│       │   ├── bpy

│       │   │   ├── utils

│       │   │   │   ├── __init__.py

│       │   │   │   ├── previews.py

│       │   │   │   └── toolsystem.py

│       │   │   ├── __init__.py

│       │   │   ├── ops.py

│       │   │   └── path.py

│       │   ├── bpy_extras

│       │   │   ├── extensions

│       │   │   │   └── junction_module.py

│       │   │   ├── wm_utils

│       │   │   │   └── progress_report.py

│       │   │   ├── __init__.py

│       │   │   ├── anim_utils.py

│       │   │   ├── asset_utils.py

│       │   │   ├── bmesh_utils.py

│       │   │   ├── id_map_utils.py

│       │   │   ├── image_utils.py

│       │   │   ├── io_utils.py

│       │   │   ├── keyconfig_utils.py

│       │   │   ├── mesh_utils.py

│       │   │   ├── node_shader_utils.py

│       │   │   ├── node_utils.py

│       │   │   ├── object_utils.py

│       │   │   └── view3d_utils.py

│       │   ├── gpu_extras

│       │   │   ├── __init__.py

│       │   │   ├── batch.py

│       │   │   └── presets.py

│       │   ├── addon_utils.py

│       │   ├── animsys_refactor.py

│       │   ├── bl_app_template_utils.py

│       │   ├── blend_render_info.py

│       │   ├── bpy_restrict_state.py

│       │   ├── bpy_types.py

│       │   ├── console_python.py

│       │   ├── console_shell.py

│       │   ├── graphviz_export.py

│       │   ├── keyingsets_utils.py

│       │   ├── nodeitems_utils.py

│       │   ├── rna_info.py

│       │   ├── rna_keymap_ui.py

│       │   ├── rna_manual_reference.py

│       │   ├── rna_prop_ui.py

│       │   ├── rna_xml.py

│       │   └── sys_info.py

│       ├── presets

│       │   ├── camera

│       │   │   ├── 1_inch.py

│       │   │   ├── 1_slash_1.8_inch.py

│       │   │   ├── 1_slash_2.3_inch.py

│       │   │   ├── 1_slash_2.5_inch.py

│       │   │   ├── 1_slash_2.7_inch.py

│       │   │   ├── 1_slash_3.2_inch.py

│       │   │   ├── 2_slash_3_inch.py

│       │   │   ├── APS-C.py

│       │   │   ├── APS-C_(Canon).py

│       │   │   ├── APS-H_(Canon).py

│       │   │   ├── Analog_16mm.py

│       │   │   ├── Analog_35mm.py

│       │   │   ├── Analog_65mm.py

│       │   │   ├── Analog_IMAX.py

│       │   │   ├── Analog_Super_16.py

│       │   │   ├── Analog_Super_35.py

│       │   │   ├── Arri_Alexa_65.py

│       │   │   ├── Arri_Alexa_LF.py

│       │   │   ├── Arri_Alexa_Mini_&_SXT.py

│       │   │   ├── Blackmagic_Pocket_&_Studio.py

│       │   │   ├── Blackmagic_Pocket_4K.py

│       │   │   ├── Blackmagic_Pocket_6k.py

│       │   │   ├── Blackmagic_URSA_4.6K.py

│       │   │   ├── Foveon_(Sigma).py

│       │   │   ├── Fullframe.py

│       │   │   ├── MFT.py

│       │   │   ├── Medium-format_(Hasselblad).py

│       │   │   ├── RED_Dragon_5K.py

│       │   │   ├── RED_Dragon_6K.py

│       │   │   ├── RED_Helium_8K.py

│       │   │   └── RED_Monstro_8K.py

│       │   ├── cloth

│       │   │   ├── Cotton.py

│       │   │   ├── Denim.py

│       │   │   ├── Leather.py

│       │   │   ├── Rubber.py

│       │   │   └── Silk.py

│       │   ├── cycles

│       │   │   ├── integrator

│       │   │   │   ├── Default.py

│       │   │   │   ├── Direct_Light.py

│       │   │   │   ├── Fast_Global_Illumination.py

│       │   │   │   ├── Full_Global_Illumination.py

│       │   │   │   └── Limited_Global_Illumination.py

│       │   │   ├── performance

│       │   │   │   ├── Default.py

│       │   │   │   ├── Faster_Render.py

│       │   │   │   └── Lower_Memory.py

│       │   │   ├── sampling

│       │   │   │   ├── Final.py

│       │   │   │   └── Preview.py

│       │   │   └── viewport_sampling

│       │   │       ├── Final.py

│       │   │       └── Preview.py

│       │   ├── eevee

│       │   │   └── raytracing

│       │   │       └── Default.py

│       │   ├── ffmpeg

│       │   │   ├── DVD_(note_colon__this_changes_render_resolution).py

│       │   │   ├── H264_in_MP4.py

│       │   │   ├── H264_in_Matroska.py

│       │   │   ├── H264_in_Matroska_for_scrubbing.py

│       │   │   ├── Ogg_Theora.py

│       │   │   ├── WebM_(VP9+Opus).py

│       │   │   └── Xvid.py

│       │   ├── fluid

│       │   │   ├── Honey.py

│       │   │   ├── Oil.py

│       │   │   └── Water.py

│       │   ├── framerate

│       │   │   ├── 120.py

│       │   │   ├── 23.98.py

│       │   │   ├── 24.py

│       │   │   ├── 240.py

│       │   │   ├── 25.py

│       │   │   ├── 29.97.py

│       │   │   ├── 30.py

│       │   │   ├── 50.py

│       │   │   ├── 59.94.py

│       │   │   ├── 60.py

│       │   │   └── Custom.py

│       │   ├── gpencil_material

│       │   │   ├── Fill_Only.py

│       │   │   ├── Stroke_Only.py

│       │   │   └── Stroke_and_Fill.py

│       │   ├── hair_dynamics

│       │   │   └── Default.py

│       │   ├── interface_theme

│       │   │   ├── Blender_Dark.xml

│       │   │   └── Blender_Light.xml

│       │   ├── keyconfig

│       │   │   ├── keymap_data

│       │   │   │   ├── blender_default.py

│       │   │   │   └── industry_compatible_data.py

│       │   │   ├── Blender.py

│       │   │   ├── Blender_27x.py

│       │   │   └── Industry_Compatible.py

│       │   ├── operator

│       │   │   └── wm.collada_export

│       │   │       ├── sl_plus_open_sim_rigged.py

│       │   │       └── sl_plus_open_sim_static.py

│       │   ├── render

│       │   │   ├── 4K_DCI_2160p.py

│       │   │   ├── 4K_UHDTV_2160p.py

│       │   │   ├── 4K_UW_1600p.py

│       │   │   ├── DVCPRO_HD_1080p.py

│       │   │   ├── DVCPRO_HD_720p.py

│       │   │   ├── HDTV_1080p.py

│       │   │   ├── HDTV_720p.py

│       │   │   ├── HDV_1080p.py

│       │   │   ├── HDV_NTSC_1080p.py

│       │   │   ├── HDV_PAL_1080p.py

│       │   │   ├── TV_NTSC_16_colon_9.py

│       │   │   ├── TV_NTSC_4_colon_3.py

│       │   │   ├── TV_PAL_16_colon_9.py

│       │   │   └── TV_PAL_4_colon_3.py

│       │   ├── safe_areas

│       │   │   ├── 14_colon_9_in_16_colon_9.py

│       │   │   ├── 16_colon_9.py

│       │   │   └── 4_colon_3_in_16_colon_9.py

│       │   ├── text_editor

│       │   │   ├── Internal.py

│       │   │   └── Visual_Studio_Code.py

│       │   ├── tracking_camera

│       │   │   ├── 1_inch.py

│       │   │   ├── 1_slash_1.8_inch.py

│       │   │   ├── 1_slash_2.3_inch.py

│       │   │   ├── 1_slash_2.5_inch.py

│       │   │   ├── 1_slash_2.7_inch.py

│       │   │   ├── 1_slash_3.2_inch.py

│       │   │   ├── 2_slash_3_inch.py

│       │   │   ├── APS-C.py

│       │   │   ├── APS-C_(Canon).py

│       │   │   ├── APS-H_(Canon).py

│       │   │   ├── Analog_16mm.py

│       │   │   ├── Analog_35mm.py

│       │   │   ├── Analog_65mm.py

│       │   │   ├── Analog_IMAX.py

│       │   │   ├── Analog_Super_16.py

│       │   │   ├── Analog_Super_35.py

│       │   │   ├── Arri_Alexa_65.py

│       │   │   ├── Arri_Alexa_LF.py

│       │   │   ├── Arri_Alexa_Mini_&_SXT.py

│       │   │   ├── Blackmagic_Pocket_&_Studio.py

│       │   │   ├── Blackmagic_Pocket_4K.py

│       │   │   ├── Blackmagic_Pocket_6k.py

│       │   │   ├── Blackmagic_URSA_4.6K.py

│       │   │   ├── Foveon_(Sigma).py

│       │   │   ├── Fullframe.py

│       │   │   ├── MFT.py

│       │   │   ├── Medium-format_(Hasselblad).py

│       │   │   ├── RED_Dragon_5K.py

│       │   │   ├── RED_Dragon_6K.py

│       │   │   ├── RED_Helium_8K.py

│       │   │   └── RED_Monstro_8K.py

│       │   ├── tracking_settings

│       │   │   ├── Blurry_Footage.py

│       │   │   ├── Default.py

│       │   │   ├── Fast_Motion.py

│       │   │   └── Planar.py

│       │   └── tracking_track_color

│       │       ├── Default.py

│       │       ├── Far_Plane.py

│       │       ├── Near_Plane.py

│       │       └── Object.py

│       ├── startup

│       │   ├── bl_app_templates_system

│       │   │   ├── 2D_Animation

│       │   │   │   ├── __init__.py

│       │   │   │   └── startup.blend

│       │   │   ├── Sculpting

│       │   │   │   ├── __init__.py

│       │   │   │   └── startup.blend

│       │   │   ├── VFX

│       │   │   │   └── startup.blend

│       │   │   └── Video_Editing

│       │   │       ├── __init__.py

│       │   │       └── startup.blend

│       │   ├── bl_operators

│       │   │   ├── bmesh

│       │   │   │   └── find_adjacent.py

│       │   │   ├── __init__.py

│       │   │   ├── add_mesh_torus.py

│       │   │   ├── anim.py

│       │   │   ├── assets.py

│       │   │   ├── clip.py

│       │   │   ├── console.py

│       │   │   ├── constraint.py

│       │   │   ├── file.py

│       │   │   ├── freestyle.py

│       │   │   ├── geometry_nodes.py

│       │   │   ├── image.py

│       │   │   ├── mesh.py

│       │   │   ├── node.py

│       │   │   ├── object.py

│       │   │   ├── object_align.py

│       │   │   ├── object_quick_effects.py

│       │   │   ├── object_randomize_transform.py

│       │   │   ├── presets.py

│       │   │   ├── rigidbody.py

│       │   │   ├── screen_play_rendered_anim.py

│       │   │   ├── sequencer.py

│       │   │   ├── spreadsheet.py

│       │   │   ├── userpref.py

│       │   │   ├── uvcalc_follow_active.py

│       │   │   ├── uvcalc_lightmap.py

│       │   │   ├── uvcalc_transform.py

│       │   │   ├── vertexpaint_dirt.py

│       │   │   ├── view3d.py

│       │   │   └── wm.py

│       │   ├── bl_ui

│       │   │   ├── __init__.py

│       │   │   ├── anim.py

│       │   │   ├── asset_shelf.py

│       │   │   ├── generic_ui_list.py

│       │   │   ├── node_add_menu.py

│       │   │   ├── node_add_menu_compositor.py

│       │   │   ├── node_add_menu_geometry.py

│       │   │   ├── node_add_menu_shader.py

│       │   │   ├── node_add_menu_texture.py

│       │   │   ├── properties_animviz.py

│       │   │   ├── properties_collection.py

│       │   │   ├── properties_constraint.py

│       │   │   ├── properties_data_armature.py

│       │   │   ├── properties_data_bone.py

│       │   │   ├── properties_data_camera.py

│       │   │   ├── properties_data_curve.py

│       │   │   ├── properties_data_curves.py

│       │   │   ├── properties_data_empty.py

│       │   │   ├── properties_data_gpencil.py

│       │   │   ├── properties_data_grease_pencil.py

│       │   │   ├── properties_data_lattice.py

│       │   │   ├── properties_data_light.py

│       │   │   ├── properties_data_lightprobe.py

│       │   │   ├── properties_data_mesh.py

│       │   │   ├── properties_data_metaball.py

│       │   │   ├── properties_data_modifier.py

│       │   │   ├── properties_data_pointcloud.py

│       │   │   ├── properties_data_shaderfx.py

│       │   │   ├── properties_data_speaker.py

│       │   │   ├── properties_data_volume.py

│       │   │   ├── properties_freestyle.py

│       │   │   ├── properties_grease_pencil_common.py

│       │   │   ├── properties_mask_common.py

│       │   │   ├── properties_material.py

│       │   │   ├── properties_material_gpencil.py

│       │   │   ├── properties_object.py

│       │   │   ├── properties_output.py

│       │   │   ├── properties_paint_common.py

│       │   │   ├── properties_particle.py

│       │   │   ├── properties_physics_cloth.py

│       │   │   ├── properties_physics_common.py

│       │   │   ├── properties_physics_dynamicpaint.py

│       │   │   ├── properties_physics_field.py

│       │   │   ├── properties_physics_fluid.py

│       │   │   ├── properties_physics_geometry_nodes.py

│       │   │   ├── properties_physics_rigidbody.py

│       │   │   ├── properties_physics_rigidbody_constraint.py

│       │   │   ├── properties_physics_softbody.py

│       │   │   ├── properties_render.py

│       │   │   ├── properties_scene.py

│       │   │   ├── properties_texture.py

│       │   │   ├── properties_view_layer.py

│       │   │   ├── properties_workspace.py

│       │   │   ├── properties_world.py

│       │   │   ├── space_clip.py

│       │   │   ├── space_console.py

│       │   │   ├── space_dopesheet.py

│       │   │   ├── space_filebrowser.py

│       │   │   ├── space_graph.py

│       │   │   ├── space_image.py

│       │   │   ├── space_info.py

│       │   │   ├── space_nla.py

│       │   │   ├── space_node.py

│       │   │   ├── space_outliner.py

│       │   │   ├── space_properties.py

│       │   │   ├── space_sequencer.py

│       │   │   ├── space_spreadsheet.py

│       │   │   ├── space_statusbar.py

│       │   │   ├── space_text.py

│       │   │   ├── space_time.py

│       │   │   ├── space_toolsystem_common.py

│       │   │   ├── space_toolsystem_toolbar.py

│       │   │   ├── space_topbar.py

│       │   │   ├── space_userpref.py

│       │   │   ├── space_view3d.py

│       │   │   ├── space_view3d_toolbar.py

│       │   │   └── utils.py

│       │   ├── keyingsets_builtins.py

│       │   └── nodeitems_builtins.py

│       ├── templates_osl

│       │   ├── basic_shader.osl

│       │   ├── empty_shader.osl

│       │   ├── gabor_noise.osl

│       │   ├── lyapunov_texture.osl

│       │   ├── noise.osl

│       │   └── ramp_closure.osl

│       └── templates_py

│           ├── addon_add_object.py

│           ├── background_job.py

│           ├── batch_export.py

│           ├── bmesh_simple.py

│           ├── bmesh_simple_editmode.py

│           ├── builtin_keyingset.py

│           ├── custom_nodes.py

│           ├── driver_functions.py

│           ├── external_script_stub.py

│           ├── gizmo_custom_geometry.py

│           ├── gizmo_operator.py

│           ├── gizmo_operator_target.py

│           ├── gizmo_simple.py

│           ├── image_processing.py

│           ├── operator_file_export.py

│           ├── operator_file_import.py

│           ├── operator_mesh_add.py

│           ├── operator_mesh_uv.py

│           ├── operator_modal.py

│           ├── operator_modal_draw.py

│           ├── operator_modal_timer.py

│           ├── operator_modal_view3d.py

│           ├── operator_modal_view3d_raycast.py

│           ├── operator_node.py

│           ├── operator_simple.py

│           ├── ui_asset_shelf.py

│           ├── ui_list.py

│           ├── ui_list_generic.py

│           ├── ui_list_simple.py

│           ├── ui_menu.py

│           ├── ui_menu_simple.py

│           ├── ui_panel.py

│           ├── ui_panel_simple.py

│           ├── ui_pie_menu.py

│           ├── ui_previews_custom_icon.py

│           ├── ui_previews_dynamic_enum.py

│           └── ui_tool_simple.py

├── blender.crt

│   ├── api-ms-win-core-console-l1-1-0.dll

│   ├── api-ms-win-core-console-l1-2-0.dll

│   ├── api-ms-win-core-datetime-l1-1-0.dll

│   ├── api-ms-win-core-debug-l1-1-0.dll

│   ├── api-ms-win-core-errorhandling-l1-1-0.dll

│   ├── api-ms-win-core-file-l1-1-0.dll

│   ├── api-ms-win-core-file-l1-2-0.dll

│   ├── api-ms-win-core-file-l2-1-0.dll

│   ├── api-ms-win-core-handle-l1-1-0.dll

│   ├── api-ms-win-core-heap-l1-1-0.dll

│   ├── api-ms-win-core-interlocked-l1-1-0.dll

│   ├── api-ms-win-core-libraryloader-l1-1-0.dll

│   ├── api-ms-win-core-localization-l1-2-0.dll

│   ├── api-ms-win-core-memory-l1-1-0.dll

│   ├── api-ms-win-core-namedpipe-l1-1-0.dll

│   ├── api-ms-win-core-processenvironment-l1-1-0.dll

│   ├── api-ms-win-core-processthreads-l1-1-0.dll

│   ├── api-ms-win-core-processthreads-l1-1-1.dll

│   ├── api-ms-win-core-profile-l1-1-0.dll

│   ├── api-ms-win-core-rtlsupport-l1-1-0.dll

│   ├── api-ms-win-core-string-l1-1-0.dll

│   ├── api-ms-win-core-synch-l1-1-0.dll

│   ├── api-ms-win-core-synch-l1-2-0.dll

│   ├── api-ms-win-core-sysinfo-l1-1-0.dll

│   ├── api-ms-win-core-timezone-l1-1-0.dll

│   ├── api-ms-win-core-util-l1-1-0.dll

│   ├── api-ms-win-crt-conio-l1-1-0.dll

│   ├── api-ms-win-crt-convert-l1-1-0.dll

│   ├── api-ms-win-crt-environment-l1-1-0.dll

│   ├── api-ms-win-crt-filesystem-l1-1-0.dll

│   ├── api-ms-win-crt-heap-l1-1-0.dll

│   ├── api-ms-win-crt-locale-l1-1-0.dll

│   ├── api-ms-win-crt-math-l1-1-0.dll

│   ├── api-ms-win-crt-multibyte-l1-1-0.dll

│   ├── api-ms-win-crt-private-l1-1-0.dll

│   ├── api-ms-win-crt-process-l1-1-0.dll

│   ├── api-ms-win-crt-runtime-l1-1-0.dll

│   ├── api-ms-win-crt-stdio-l1-1-0.dll

│   ├── api-ms-win-crt-string-l1-1-0.dll

│   ├── api-ms-win-crt-time-l1-1-0.dll

│   ├── api-ms-win-crt-utility-l1-1-0.dll

│   ├── blender.crt.manifest

│   ├── concrt140.dll

│   ├── msvcp140.dll

│   ├── msvcp140_1.dll

│   ├── msvcp140_2.dll

│   ├── msvcp140_atomic_wait.dll

│   ├── msvcp140_codecvt_ids.dll

│   ├── vcomp140.dll

│   ├── vcruntime140.dll

│   └── vcruntime140_1.dll

├── blender.shared

│   ├── materialx

│   │   └── libraries

│   │       ├── bxdf

│   │       │   ├── lama

│   │       │   │   ├── lama_add.mtlx

│   │       │   │   ├── lama_conductor.mtlx

│   │       │   │   ├── lama_dielectric.mtlx

│   │       │   │   ├── lama_diffuse.mtlx

│   │       │   │   ├── lama_emission.mtlx

│   │       │   │   ├── lama_layer.mtlx

│   │       │   │   ├── lama_mix.mtlx

│   │       │   │   ├── lama_sheen.mtlx

│   │       │   │   ├── lama_sss.mtlx

│   │       │   │   └── lama_translucent.mtlx

│   │       │   ├── translation

│   │       │   │   ├── standard_surface_to_gltf_pbr.mtlx

│   │       │   │   └── standard_surface_to_usd.mtlx

│   │       │   ├── disney_brdf_2012.mtlx

│   │       │   ├── disney_brdf_2015.mtlx

│   │       │   ├── gltf_pbr.mtlx

│   │       │   ├── standard_surface.mtlx

│   │       │   └── usd_preview_surface.mtlx

│   │       ├── cmlib

│   │       │   ├── cmlib_defs.mtlx

│   │       │   └── cmlib_ng.mtlx

│   │       ├── lights

│   │       │   ├── genglsl

│   │       │   │   ├── lights_genglsl_impl.mtlx

│   │       │   │   ├── mx_directional_light.glsl

│   │       │   │   ├── mx_point_light.glsl

│   │       │   │   └── mx_spot_light.glsl

│   │       │   ├── genmsl

│   │       │   │   ├── lights_genmsl_impl.mtlx

│   │       │   │   ├── mx_directional_light.metal

│   │       │   │   ├── mx_point_light.metal

│   │       │   │   └── mx_spot_light.metal

│   │       │   └── lights_defs.mtlx

│   │       ├── mdl

│   │       │   └── materialx

│   │       │       ├── core.mdl

│   │       │       ├── hsv.mdl

│   │       │       ├── noise.mdl

│   │       │       ├── pbrlib.mdl

│   │       │       ├── sampling.mdl

│   │       │       ├── stdlib.mdl

│   │       │       └── swizzle.mdl

│   │       ├── pbrlib

│   │       │   ├── genglsl

│   │       │   │   ├── lib

│   │       │   │   │   ├── mx_environment_fis.glsl

│   │       │   │   │   ├── mx_environment_none.glsl

│   │       │   │   │   ├── mx_environment_prefilter.glsl

│   │       │   │   │   ├── mx_microfacet.glsl

│   │       │   │   │   ├── mx_microfacet_diffuse.glsl

│   │       │   │   │   ├── mx_microfacet_sheen.glsl

│   │       │   │   │   ├── mx_microfacet_specular.glsl

│   │       │   │   │   ├── mx_shadow.glsl

│   │       │   │   │   ├── mx_table.glsl

│   │       │   │   │   ├── mx_transmission_opacity.glsl

│   │       │   │   │   └── mx_transmission_refract.glsl

│   │       │   │   ├── mx_add_edf.glsl

│   │       │   │   ├── mx_anisotropic_vdf.glsl

│   │       │   │   ├── mx_artistic_ior.glsl

│   │       │   │   ├── mx_blackbody.glsl

│   │       │   │   ├── mx_burley_diffuse_bsdf.glsl

│   │       │   │   ├── mx_conductor_bsdf.glsl

│   │       │   │   ├── mx_dielectric_bsdf.glsl

│   │       │   │   ├── mx_displacement_float.glsl

│   │       │   │   ├── mx_displacement_vector3.glsl

│   │       │   │   ├── mx_generalized_schlick_bsdf.glsl

│   │       │   │   ├── mx_generalized_schlick_edf.glsl

│   │       │   │   ├── mx_oren_nayar_diffuse_bsdf.glsl

│   │       │   │   ├── mx_roughness_anisotropy.glsl

│   │       │   │   ├── mx_roughness_dual.glsl

│   │       │   │   ├── mx_sheen_bsdf.glsl

│   │       │   │   ├── mx_subsurface_bsdf.glsl

│   │       │   │   ├── mx_translucent_bsdf.glsl

│   │       │   │   ├── mx_uniform_edf.glsl

│   │       │   │   └── pbrlib_genglsl_impl.mtlx

│   │       │   ├── genmdl

│   │       │   │   └── pbrlib_genmdl_impl.mtlx

│   │       │   ├── genmsl

│   │       │   │   └── pbrlib_genmsl_impl.mtlx

│   │       │   ├── genosl

│   │       │   │   ├── legacy

│   │       │   │   │   ├── mx_anisotropic_vdf.osl

│   │       │   │   │   ├── mx_burley_diffuse_bsdf.osl

│   │       │   │   │   ├── mx_conductor_bsdf.osl

│   │       │   │   │   ├── mx_dielectric_bsdf.osl

│   │       │   │   │   ├── mx_generalized_schlick_bsdf.osl

│   │       │   │   │   ├── mx_oren_nayar_diffuse_bsdf.osl

│   │       │   │   │   ├── mx_sheen_bsdf.osl

│   │       │   │   │   ├── mx_subsurface_bsdf.osl

│   │       │   │   │   ├── mx_surface.osl

│   │       │   │   │   └── mx_translucent_bsdf.osl

│   │       │   │   ├── lib

│   │       │   │   │   ├── mx_microfacet.osl

│   │       │   │   │   ├── mx_microfacet_sheen.osl

│   │       │   │   │   └── mx_microfacet_specular.osl

│   │       │   │   ├── mx_anisotropic_vdf.osl

│   │       │   │   ├── mx_artistic_ior.osl

│   │       │   │   ├── mx_blackbody.osl

│   │       │   │   ├── mx_dielectric_bsdf.osl

│   │       │   │   ├── mx_displacement_float.osl

│   │       │   │   ├── mx_displacement_vector3.osl

│   │       │   │   ├── mx_generalized_schlick_bsdf.osl

│   │       │   │   ├── mx_generalized_schlick_edf.osl

│   │       │   │   ├── mx_roughness_anisotropy.osl

│   │       │   │   ├── mx_roughness_dual.osl

│   │       │   │   ├── mx_subsurface_bsdf.osl

│   │       │   │   ├── mx_surface.osl

│   │       │   │   └── pbrlib_genosl_impl.mtlx

│   │       │   ├── pbrlib_defs.mtlx

│   │       │   └── pbrlib_ng.mtlx

│   │       ├── stdlib

│   │       │   ├── genglsl

│   │       │   │   ├── lib

│   │       │   │   │   ├── mx_hsv.glsl

│   │       │   │   │   ├── mx_math.glsl

│   │       │   │   │   ├── mx_noise.glsl

│   │       │   │   │   ├── mx_sampling.glsl

│   │       │   │   │   ├── mx_transform_uv.glsl

│   │       │   │   │   └── mx_transform_uv_vflip.glsl

│   │       │   │   ├── mx_aastep.glsl

│   │       │   │   ├── mx_burn_color3.glsl

│   │       │   │   ├── mx_burn_color4.glsl

│   │       │   │   ├── mx_burn_float.glsl

│   │       │   │   ├── mx_cellnoise2d_float.glsl

│   │       │   │   ├── mx_cellnoise3d_float.glsl

│   │       │   │   ├── mx_disjointover_color4.glsl

│   │       │   │   ├── mx_dodge_color3.glsl

│   │       │   │   ├── mx_dodge_color4.glsl

│   │       │   │   ├── mx_dodge_float.glsl

│   │       │   │   ├── mx_fractal3d_fa_vector2.glsl

│   │       │   │   ├── mx_fractal3d_fa_vector3.glsl

│   │       │   │   ├── mx_fractal3d_fa_vector4.glsl

│   │       │   │   ├── mx_fractal3d_float.glsl

│   │       │   │   ├── mx_fractal3d_vector2.glsl

│   │       │   │   ├── mx_fractal3d_vector3.glsl

│   │       │   │   ├── mx_fractal3d_vector4.glsl

│   │       │   │   ├── mx_hsvtorgb_color3.glsl

│   │       │   │   ├── mx_hsvtorgb_color4.glsl

│   │       │   │   ├── mx_image_color3.glsl

│   │       │   │   ├── mx_image_color4.glsl

│   │       │   │   ├── mx_image_float.glsl

│   │       │   │   ├── mx_image_vector2.glsl

│   │       │   │   ├── mx_image_vector3.glsl

│   │       │   │   ├── mx_image_vector4.glsl

│   │       │   │   ├── mx_luminance_color3.glsl

│   │       │   │   ├── mx_luminance_color4.glsl

│   │       │   │   ├── mx_mix_surfaceshader.glsl

│   │       │   │   ├── mx_noise2d_fa_vector2.glsl

│   │       │   │   ├── mx_noise2d_fa_vector3.glsl

│   │       │   │   ├── mx_noise2d_fa_vector4.glsl

│   │       │   │   ├── mx_noise2d_float.glsl

│   │       │   │   ├── mx_noise2d_vector2.glsl

│   │       │   │   ├── mx_noise2d_vector3.glsl

│   │       │   │   ├── mx_noise2d_vector4.glsl

│   │       │   │   ├── mx_noise3d_fa_vector2.glsl

│   │       │   │   ├── mx_noise3d_fa_vector3.glsl

│   │       │   │   ├── mx_noise3d_fa_vector4.glsl

│   │       │   │   ├── mx_noise3d_float.glsl

│   │       │   │   ├── mx_noise3d_vector2.glsl

│   │       │   │   ├── mx_noise3d_vector3.glsl

│   │       │   │   ├── mx_noise3d_vector4.glsl

│   │       │   │   ├── mx_normalmap.glsl

│   │       │   │   ├── mx_overlay.glsl

│   │       │   │   ├── mx_overlay_color3.glsl

│   │       │   │   ├── mx_overlay_color4.glsl

│   │       │   │   ├── mx_premult_color4.glsl

│   │       │   │   ├── mx_ramplr_float.glsl

│   │       │   │   ├── mx_ramplr_vector2.glsl

│   │       │   │   ├── mx_ramplr_vector3.glsl

│   │       │   │   ├── mx_ramplr_vector4.glsl

│   │       │   │   ├── mx_ramptb_float.glsl

│   │       │   │   ├── mx_ramptb_vector2.glsl

│   │       │   │   ├── mx_ramptb_vector3.glsl

│   │       │   │   ├── mx_ramptb_vector4.glsl

│   │       │   │   ├── mx_rgbtohsv_color3.glsl

│   │       │   │   ├── mx_rgbtohsv_color4.glsl

│   │       │   │   ├── mx_rotate_vector2.glsl

│   │       │   │   ├── mx_rotate_vector3.glsl

│   │       │   │   ├── mx_smoothstep_float.glsl

│   │       │   │   ├── mx_smoothstep_vec2.glsl

│   │       │   │   ├── mx_smoothstep_vec2FA.glsl

│   │       │   │   ├── mx_smoothstep_vec3.glsl

│   │       │   │   ├── mx_smoothstep_vec3FA.glsl

│   │       │   │   ├── mx_smoothstep_vec4.glsl

│   │       │   │   ├── mx_smoothstep_vec4FA.glsl

│   │       │   │   ├── mx_splitlr_float.glsl

│   │       │   │   ├── mx_splitlr_vector2.glsl

│   │       │   │   ├── mx_splitlr_vector3.glsl

│   │       │   │   ├── mx_splitlr_vector4.glsl

│   │       │   │   ├── mx_splittb_float.glsl

│   │       │   │   ├── mx_splittb_vector2.glsl

│   │       │   │   ├── mx_splittb_vector3.glsl

│   │       │   │   ├── mx_splittb_vector4.glsl

│   │       │   │   ├── mx_transformmatrix_vector2M3.glsl

│   │       │   │   ├── mx_transformmatrix_vector3M4.glsl

│   │       │   │   ├── mx_unpremult_color4.glsl

│   │       │   │   ├── mx_worleynoise2d_float.glsl

│   │       │   │   ├── mx_worleynoise2d_vector2.glsl

│   │       │   │   ├── mx_worleynoise2d_vector3.glsl

│   │       │   │   ├── mx_worleynoise3d_float.glsl

│   │       │   │   ├── mx_worleynoise3d_vector2.glsl

│   │       │   │   ├── mx_worleynoise3d_vector3.glsl

│   │       │   │   └── stdlib_genglsl_impl.mtlx

│   │       │   ├── genmdl

│   │       │   │   └── stdlib_genmdl_impl.mtlx

│   │       │   ├── genmsl

│   │       │   │   ├── lib

│   │       │   │   │   ├── mx_math.metal

│   │       │   │   │   ├── mx_matscalaroperators.metal

│   │       │   │   │   ├── mx_sampling.metal

│   │       │   │   │   └── mx_texture.metal

│   │       │   │   ├── mx_burn_color3.metal

│   │       │   │   ├── mx_burn_color4.metal

│   │       │   │   ├── mx_burn_float.metal

│   │       │   │   ├── mx_dodge_color3.metal

│   │       │   │   ├── mx_dodge_color4.metal

│   │       │   │   ├── mx_dodge_float.metal

│   │       │   │   ├── mx_normalmap.metal

│   │       │   │   ├── mx_smoothstep_float.metal

│   │       │   │   ├── mx_smoothstep_vec2.metal

│   │       │   │   ├── mx_smoothstep_vec2FA.metal

│   │       │   │   ├── mx_smoothstep_vec3.metal

│   │       │   │   ├── mx_smoothstep_vec3FA.metal

│   │       │   │   ├── mx_smoothstep_vec4.metal

│   │       │   │   ├── mx_smoothstep_vec4FA.metal

│   │       │   │   └── stdlib_genmsl_impl.mtlx

│   │       │   ├── genosl

│   │       │   │   ├── include

│   │       │   │   │   ├── color4.h

│   │       │   │   │   ├── matrix33.h

│   │       │   │   │   ├── mx_funcs.h

│   │       │   │   │   ├── vector2.h

│   │       │   │   │   └── vector4.h

│   │       │   │   ├── lib

│   │       │   │   │   ├── mx_sampling.osl

│   │       │   │   │   ├── mx_transform_uv.osl

│   │       │   │   │   └── mx_transform_uv_vflip.osl

│   │       │   │   ├── mx_ambientocclusion_float.osl

│   │       │   │   ├── mx_burn_color3.osl

│   │       │   │   ├── mx_burn_color4.osl

│   │       │   │   ├── mx_burn_float.osl

│   │       │   │   ├── mx_cellnoise2d_float.osl

│   │       │   │   ├── mx_cellnoise3d_float.osl

│   │       │   │   ├── mx_disjointover_color4.osl

│   │       │   │   ├── mx_dodge_color3.osl

│   │       │   │   ├── mx_dodge_color4.osl

│   │       │   │   ├── mx_dodge_float.osl

│   │       │   │   ├── mx_fractal3d_color3.osl

│   │       │   │   ├── mx_fractal3d_color4.osl

│   │       │   │   ├── mx_fractal3d_fa_color3.osl

│   │       │   │   ├── mx_fractal3d_fa_color4.osl

│   │       │   │   ├── mx_fractal3d_fa_vector2.osl

│   │       │   │   ├── mx_fractal3d_fa_vector3.osl

│   │       │   │   ├── mx_fractal3d_fa_vector4.osl

│   │       │   │   ├── mx_fractal3d_float.osl

│   │       │   │   ├── mx_fractal3d_vector2.osl

│   │       │   │   ├── mx_fractal3d_vector3.osl

│   │       │   │   ├── mx_fractal3d_vector4.osl

│   │       │   │   ├── mx_frame_float.osl

│   │       │   │   ├── mx_geomcolor_color3.osl

│   │       │   │   ├── mx_geomcolor_color4.osl

│   │       │   │   ├── mx_geomcolor_float.osl

│   │       │   │   ├── mx_geompropvalue_boolean.osl

│   │       │   │   ├── mx_geompropvalue_color3.osl

│   │       │   │   ├── mx_geompropvalue_color4.osl

│   │       │   │   ├── mx_geompropvalue_float.osl

│   │       │   │   ├── mx_geompropvalue_integer.osl

│   │       │   │   ├── mx_geompropvalue_string.osl

│   │       │   │   ├── mx_geompropvalue_vector2.osl

│   │       │   │   ├── mx_geompropvalue_vector3.osl

│   │       │   │   ├── mx_geompropvalue_vector4.osl

│   │       │   │   ├── mx_heighttonormal_vector3.osl

│   │       │   │   ├── mx_hsvtorgb_color3.osl

│   │       │   │   ├── mx_hsvtorgb_color4.osl

│   │       │   │   ├── mx_image_color3.osl

│   │       │   │   ├── mx_image_color4.osl

│   │       │   │   ├── mx_image_float.osl

│   │       │   │   ├── mx_image_vector2.osl

│   │       │   │   ├── mx_image_vector3.osl

│   │       │   │   ├── mx_image_vector4.osl

│   │       │   │   ├── mx_luminance_color3.osl

│   │       │   │   ├── mx_luminance_color4.osl

│   │       │   │   ├── mx_mix_surfaceshader.osl

│   │       │   │   ├── mx_noise2d_color3.osl

│   │       │   │   ├── mx_noise2d_color4.osl

│   │       │   │   ├── mx_noise2d_fa_color3.osl

│   │       │   │   ├── mx_noise2d_fa_color4.osl

│   │       │   │   ├── mx_noise2d_fa_vector2.osl

│   │       │   │   ├── mx_noise2d_fa_vector3.osl

│   │       │   │   ├── mx_noise2d_fa_vector4.osl

│   │       │   │   ├── mx_noise2d_float.osl

│   │       │   │   ├── mx_noise2d_vector2.osl

│   │       │   │   ├── mx_noise2d_vector3.osl

│   │       │   │   ├── mx_noise2d_vector4.osl

│   │       │   │   ├── mx_noise3d_color3.osl

│   │       │   │   ├── mx_noise3d_color4.osl

│   │       │   │   ├── mx_noise3d_fa_color3.osl

│   │       │   │   ├── mx_noise3d_fa_color4.osl

│   │       │   │   ├── mx_noise3d_fa_vector2.osl

│   │       │   │   ├── mx_noise3d_fa_vector3.osl

│   │       │   │   ├── mx_noise3d_fa_vector4.osl

│   │       │   │   ├── mx_noise3d_float.osl

│   │       │   │   ├── mx_noise3d_vector2.osl

│   │       │   │   ├── mx_noise3d_vector3.osl

│   │       │   │   ├── mx_noise3d_vector4.osl

│   │       │   │   ├── mx_normalmap.osl

│   │       │   │   ├── mx_overlay_color3.osl

│   │       │   │   ├── mx_overlay_color4.osl

│   │       │   │   ├── mx_premult_color4.osl

│   │       │   │   ├── mx_rgbtohsv_color3.osl

│   │       │   │   ├── mx_rgbtohsv_color4.osl

│   │       │   │   ├── mx_rotate_vector2.osl

│   │       │   │   ├── mx_rotate_vector3.osl

│   │       │   │   ├── mx_surface_unlit.osl

│   │       │   │   ├── mx_time_float.osl

│   │       │   │   ├── mx_transformmatrix_vector2M3.osl

│   │       │   │   ├── mx_unpremult_color4.osl

│   │       │   │   ├── mx_worleynoise2d_float.osl

│   │       │   │   ├── mx_worleynoise2d_vector2.osl

│   │       │   │   ├── mx_worleynoise2d_vector3.osl

│   │       │   │   ├── mx_worleynoise3d_float.osl

│   │       │   │   ├── mx_worleynoise3d_vector2.osl

│   │       │   │   ├── mx_worleynoise3d_vector3.osl

│   │       │   │   └── stdlib_genosl_impl.mtlx

│   │       │   ├── stdlib_defs.mtlx

│   │       │   └── stdlib_ng.mtlx

│   │       ├── targets

│   │       │   ├── essl.mtlx

│   │       │   ├── genglsl.mtlx

│   │       │   ├── genmdl.mtlx

│   │       │   ├── genmsl.mtlx

│   │       │   └── genosl.mtlx

│   │       └── README.md

│   ├── usd

│   │   ├── ar

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── glf

│   │   │   └── resources

│   │   │       ├── shaders

│   │   │       │   ├── pcfShader.glslfx

│   │   │       │   └── simpleLighting.glslfx

│   │   │       └── plugInfo.json

│   │   ├── hd

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── hdGp

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── hdSt

│   │   │   └── resources

│   │   │       ├── shaders

│   │   │       │   ├── basisCurves.glslfx

│   │   │       │   ├── compute.glslfx

│   │   │       │   ├── domeLight.glslfx

│   │   │       │   ├── edgeId.glslfx

│   │   │       │   ├── fallbackLighting.glslfx

│   │   │       │   ├── fallbackLightingShader.glslfx

│   │   │       │   ├── fallbackMaterialNetwork.glslfx

│   │   │       │   ├── fallbackVolume.glslfx

│   │   │       │   ├── frustumCull.glslfx

│   │   │       │   ├── imageShader.glslfx

│   │   │       │   ├── instancing.glslfx

│   │   │       │   ├── mesh.glslfx

│   │   │       │   ├── meshFaceCull.glslfx

│   │   │       │   ├── meshNormal.glslfx

│   │   │       │   ├── meshWire.glslfx

│   │   │       │   ├── pointId.glslfx

│   │   │       │   ├── points.glslfx

│   │   │       │   ├── ptexTexture.glslfx

│   │   │       │   ├── renderPass.glslfx

│   │   │       │   ├── renderPassShader.glslfx

│   │   │       │   ├── simpleLightingShader.glslfx

│   │   │       │   ├── terminals.glslfx

│   │   │       │   ├── visibility.glslfx

│   │   │       │   └── volume.glslfx

│   │   │       ├── textures

│   │   │       │   └── fallbackBlackDomeLight.png

│   │   │       └── plugInfo.json

│   │   ├── hdStorm

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── hdx

│   │   │   └── resources

│   │   │       ├── shaders

│   │   │       │   ├── boundingBox.glslfx

│   │   │       │   ├── colorChannel.glslfx

│   │   │       │   ├── colorCorrection.glslfx

│   │   │       │   ├── fullscreen.glslfx

│   │   │       │   ├── oitResolveImageShader.glslfx

│   │   │       │   ├── outline.glslfx

│   │   │       │   ├── renderPass.glslfx

│   │   │       │   ├── renderPassColorAndSelectionShader.glslfx

│   │   │       │   ├── renderPassColorShader.glslfx

│   │   │       │   ├── renderPassColorWithOccludedSelectionShader.glslfx

│   │   │       │   ├── renderPassIdShader.glslfx

│   │   │       │   ├── renderPassOitOpaqueShader.glslfx

│   │   │       │   ├── renderPassOitShader.glslfx

│   │   │       │   ├── renderPassOitVolumeShader.glslfx

│   │   │       │   ├── renderPassPickingShader.glslfx

│   │   │       │   ├── renderPassShadowShader.glslfx

│   │   │       │   ├── selection.glslfx

│   │   │       │   ├── skydome.glslfx

│   │   │       │   └── visualize.glslfx

│   │   │       ├── textures

│   │   │       │   ├── StinsonBeach.hdr

│   │   │       │   └── StinsonBeach.tex

│   │   │       └── plugInfo.json

│   │   ├── hgiGL

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── hio

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── hioOiio

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── hioOpenVDB

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── ndr

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── sdf

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── usd

│   │   │   └── resources

│   │   │       ├── codegenTemplates

│   │   │       │   ├── api.h

│   │   │       │   ├── plugInfo.json

│   │   │       │   ├── schemaClass.cpp

│   │   │       │   ├── schemaClass.h

│   │   │       │   ├── tokens.cpp

│   │   │       │   ├── tokens.h

│   │   │       │   ├── wrapSchemaClass.cpp

│   │   │       │   └── wrapTokens.cpp

│   │   │       ├── usd

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdGeom

│   │   │   └── resources

│   │   │       ├── usdGeom

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdHydra

│   │   │   └── resources

│   │   │       ├── shaders

│   │   │       │   ├── empty.glslfx

│   │   │       │   └── shaderDefs.usda

│   │   │       ├── usdHydra

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdImaging

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── usdImagingGL

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── usdLux

│   │   │   └── resources

│   │   │       ├── usdLux

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdMedia

│   │   │   └── resources

│   │   │       ├── usdMedia

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdMtlx

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── usdPhysics

│   │   │   └── resources

│   │   │       ├── usdPhysics

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdProc

│   │   │   └── resources

│   │   │       ├── usdProc

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdProcImaging

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── usdRender

│   │   │   └── resources

│   │   │       ├── usdRender

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdRi

│   │   │   └── resources

│   │   │       ├── usdRi

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdRiPxrImaging

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   ├── usdShade

│   │   │   └── resources

│   │   │       ├── usdShade

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdShaders

│   │   │   └── resources

│   │   │       ├── shaders

│   │   │       │   ├── previewSurface.glslfx

│   │   │       │   ├── primvarReader.glslfx

│   │   │       │   ├── shaderDefs.usda

│   │   │       │   ├── transform2d.glslfx

│   │   │       │   └── uvTexture.glslfx

│   │   │       └── plugInfo.json

│   │   ├── usdSkel

│   │   │   └── resources

│   │   │       ├── usdSkel

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdSkelImaging

│   │   │   └── resources

│   │   │       ├── shaders

│   │   │       │   └── skinning.glslfx

│   │   │       └── plugInfo.json

│   │   ├── usdUI

│   │   │   └── resources

│   │   │       ├── usdUI

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdVol

│   │   │   └── resources

│   │   │       ├── usdVol

│   │   │       │   └── schema.usda

│   │   │       ├── generatedSchema.usda

│   │   │       └── plugInfo.json

│   │   ├── usdVolImaging

│   │   │   └── resources

│   │   │       └── plugInfo.json

│   │   └── plugInfo.json

│   ├── Iex.dll

│   ├── IlmThread.dll

│   ├── MaterialXCore.dll

│   ├── MaterialXFormat.dll

│   ├── MaterialXGenGlsl.dll

│   ├── MaterialXGenMdl.dll

│   ├── MaterialXGenMsl.dll

│   ├── MaterialXGenOsl.dll

│   ├── MaterialXGenShader.dll

│   ├── MaterialXRender.dll

│   ├── MaterialXRenderGlsl.dll

│   ├── MaterialXRenderHw.dll

│   ├── MaterialXRenderOsl.dll

│   ├── OpenAL32.dll

│   ├── OpenEXR.dll

│   ├── OpenEXRCore.dll

│   ├── OpenEXRUtil.dll

│   ├── OpenImageDenoise.dll

│   ├── OpenImageDenoise_core.dll

│   ├── OpenImageDenoise_device_cpu.dll

│   ├── OpenImageDenoise_device_cuda.dll

│   ├── OpenImageDenoise_device_hip.dll

│   ├── OpenImageDenoise_device_sycl.dll

│   ├── SDL2.dll

│   ├── avcodec-60.dll

│   ├── avdevice-60.dll

│   ├── avformat-60.dll

│   ├── avutil-58.dll

│   ├── blender.shared.manifest

│   ├── boost_atomic-vc142-mt-x64-1_82.dll

│   ├── boost_chrono-vc142-mt-x64-1_82.dll

│   ├── boost_date_time-vc142-mt-x64-1_82.dll

│   ├── boost_filesystem-vc142-mt-x64-1_82.dll

│   ├── boost_iostreams-vc142-mt-x64-1_82.dll

│   ├── boost_locale-vc142-mt-x64-1_82.dll

│   ├── boost_numpy311-vc142-mt-x64-1_82.dll

│   ├── boost_program_options-vc142-mt-x64-1_82.dll

│   ├── boost_python311-vc142-mt-x64-1_82.dll

│   ├── boost_regex-vc142-mt-x64-1_82.dll

│   ├── boost_serialization-vc142-mt-x64-1_82.dll

│   ├── boost_system-vc142-mt-x64-1_82.dll

│   ├── boost_thread-vc142-mt-x64-1_82.dll

│   ├── boost_wave-vc142-mt-x64-1_82.dll

│   ├── boost_wserialization-vc142-mt-x64-1_82.dll

│   ├── embree4.dll

│   ├── epoxy-0.dll

│   ├── fftw3.dll

│   ├── fftw3f.dll

│   ├── gmp-10.dll

│   ├── imath.dll

│   ├── libgmpxx.dll

│   ├── opencolorio_2_3.dll

│   ├── openimageio.dll

│   ├── openimageio_util.dll

│   ├── openvdb.dll

│   ├── oslcomp.dll

│   ├── oslexec.dll

│   ├── oslnoise.dll

│   ├── oslquery.dll

│   ├── pi_level_zero.dll

│   ├── shaderc_shared.dll

│   ├── sndfile.dll

│   ├── swresample-4.dll

│   ├── swscale-7.dll

│   ├── sycl6.dll

│   ├── tbb.dll

│   ├── tbbmalloc.dll

│   ├── tbbmalloc_proxy.dll

│   └── usd_ms.dll

├── license

│   ├── Apache-2.0.txt

│   ├── BSD-3-Clause.txt

│   ├── BSL-1.0.txt

│   ├── FTL.txt

│   ├── GPL-3.0.txt

│   ├── GPL-license.txt

│   ├── MIT.txt

│   ├── MPL-2.0.txt

│   ├── OpenColorIO.txt

│   ├── OpenSSL.txt

│   ├── Python.txt

│   ├── THIRD-PARTY-LICENSES.txt

│   ├── bfont.ttf.txt

│   ├── bmonofont-i18n.ttf.txt

│   ├── droidsans.ttf.txt

│   └── jemalloc.txt

├── .gitignore

├── BlendThumb.dll

├── BlendThumb.lib

├── blender-launcher.exe

├── blender.exe

├── blender.pdb

├── blender_debug_gpu.cmd

├── blender_debug_gpu_glitchworkaround.cmd

├── blender_debug_log.cmd

├── blender_factory_startup.cmd

├── blender_oculus.cmd

├── copyright.txt

├── cycles_kernel_oneapi_aot.dll

├── oculus.json

├── python3.dll

├── python311.dll

├── readme.html

└── ucrtbase.dll

```


## Message 4

**Evaluating approach**

Considering a new strategy to ignore specific directories and files, weighing its simplicity and potential limitations. Careful analysis is essential.

## Message 5

Whether your shorter snippet is **“better”** depends on what you want to achieve. Your snippet certainly works if:

- You only want to **ignore all the main runtime files** (.dll, .exe, .lib, etc.).  
- You only care to **keep** everything that’s *not* explicitly in those ignored extensions/folders.  

However, there are a few considerations:

1. **Are you ignoring potential custom config or modifications?**  
   - For instance, your snippet ignores everything inside `**/python/lib/`. That will effectively ignore *all* Python stdlib modules (which is usually correct) but could *also* ignore any site‐packages that *you* installed or might want versioned.  
   - Same goes for `**/4.1/datafiles/icons/` and `fonts/` or `locale/`: If you *ever* want to store user‐altered icons, fonts, or translated message catalogs, you’d need to unignore those subfolders or files.

2. **Which “scripts” or “addons” do you want to keep?**  
   - You’re only ignoring `**/scripts/addons/cycles/lib/`. That’s good if you’d like to *keep* the rest of `scripts/addons/` in version control (for example, your custom addons).  
   - But if you ever want to also ignore *all* official or third‐party addons except for your own, you might do:
     ```gitignore
     **/scripts/addons/
     !**/scripts/addons/my_team_addon/
     ```
     …and so on.

3. **Extensions alone don’t catch everything**  
   - You’re ignoring `*.dll, *.exe, *.pdb, *.pyc…` which is great for typical Windows binaries. But if a future update ships e.g. `.pyd` modules or specialized dynamic libraries, you might need to expand that.  

In practice, your snippet is a **solid start** for ignoring typical Blender portable “runtime” content. Just verify you **aren’t ignoring** anything that *could store user changes*, e.g. custom color management configs or special datafiles. If that’s all you need, your snippet is simpler—and indeed can be “better” for the typical case of ignoring official builds while capturing only custom scripts or addons.

## Message 6

**Tracking and organizing**

I'm working through the issue by considering typical methods, like using a separate directory for custom site-packages and managing them via .gitignore.

## Message 7

**Short Answer**  
Most Blender users avoid placing custom Python code inside `python/lib/site-packages` when working with a portable build; instead, they either:

1. Put their custom modules/packages in **`scripts/modules/`** or **`scripts/addons/`** (which Blender’s Python automatically imports from), **or**  
2. Keep a **separate** folder of custom site‐packages that they add to Blender’s Python `sys.path` via a small script or environment variable—so that the main `python/lib/` remains “untouched” by version control.

If you do actually need to store *some* packages in `python/lib/site-packages`, then you can use **selective unignore** patterns (example below) to allow only your custom package folder while ignoring everything else.

---

## Details & Approaches

### 1) Put custom code in `scripts/modules/` or `scripts/addons/`
Blender automatically appends `<blender_root>/4.1/scripts/modules` (and also `addons`) to Python’s `sys.path`. When you place your own `.py` files or add-on packages there, they are:
- **Easy to version-control** (since you typically keep the rest of `python/lib/` ignored).
- Automatically discoverable by Blender’s Python (no extra config needed).

For example, you could do this in `.gitignore`:

```gitignore
# Ignore everything in scripts, EXCEPT our "modules" or "addons" subfolder
4.1/scripts/*
!4.1/scripts/modules/
!4.1/scripts/modules/**
!4.1/scripts/addons/
!4.1/scripts/addons/**
```

Then you can place your custom Python modules or add-ons in those subdirectories, and they’ll be picked up by Blender as well as versioned by Git.

### 2) Use a separate folder + add to `sys.path`
Alternatively, you can keep your custom Python code in **any** folder (e.g. `my_python_stuff/`) at the same level as Blender, and add a snippet that modifies `sys.path`. For instance:

```python
# In some startup script or user Setup script
import sys
import os

here = os.path.dirname(os.path.realpath(__file__))
my_packages_dir = os.path.join(here, "my_python_stuff")

if my_packages_dir not in sys.path:
    sys.path.append(my_packages_dir)
```

This way, you store `my_python_stuff/` in Git, while ignoring the main `python/` environment that Blender ships with.

### 3) Partially unignore site-packages
If you truly want certain site-packages living inside `python/lib/site-packages`, but still ignore the rest, you can use a **negative ignore** (unignore) pattern in `.gitignore`:

```gitignore
# Ignore everything in python/lib/...
4.1/python/lib/*
4.1/python/lib/**

# ...But unignore your specific custom package
!4.1/python/lib/site-packages/my_custom_pkg/**
```

This approach keeps the “stock” Python environment untracked, yet checks in the subfolder(s) you specify.

---

## Summary
- A typical best practice with **portable Blender** is **not** to put custom site-packages in `python/lib/`.  
- Instead, you rely on **Blender’s `scripts/modules/` or `scripts/addons/`** for anything you’d like to version and modify.  
- If you *must* add something to the real `site-packages/`, you can unignore *only that package folder* with the appropriate negative ignore rules.

## Message 8

wouldnt something like this be better?

# exts

*.dll

*.exe

*.hipfb

*.lib

*.pyc

*.pdb



# dirs

**/4.1/datafiles/icons/

**/4.1/datafiles/fonts/

**/4.1/datafiles/locale/

#

**/blender.crt/

**/blender.shared/

**/python/bin/

**/python/DLLs/

**/python/lib/

**/scripts/addons/cycles/lib/


## Message 9

You wrote "For instance, your snippet ignores everything inside **/python/lib/. That will effectively ignore all Python stdlib modules (which is usually correct) but could also ignore any site‐packages that you installed or might want versioned.", what is the intended way to cirumvent this issue for portable blender installations?
