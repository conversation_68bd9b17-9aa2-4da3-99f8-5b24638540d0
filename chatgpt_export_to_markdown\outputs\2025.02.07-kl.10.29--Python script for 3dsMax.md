# Python script for 3dsMax

Conversation ID: 67a5d293-c964-8008-b42c-3b669bbdf07d

## Message 1

are you capable of writing python script for 3dsmax that will execute correctly in 3dsmax 2025?

## Message 2

**Confirming Python script capability**

The user asked if I'm capable of writing a Python script for 3dsMax 2025. The answer is yes, 3dsMax 2025 does support Python integration, so it can be scripted.

However, I’m not sure about the user's specific needs for the script. I'll say yes, but I'd like to know more about what type of script they need.

**Confirming Python script capability**

Yes, I can write Python scripts for 3dsMax 2025. 3dsMax has supported Python integration since version 2021, so it's definitely workable in 2025.

However, since I don't have the ability to test directly in 3dsMax, my answer is based on theoretical understanding. I’d be happy to help, but I’ll need more details from the user about what kind of script they need specifically. Let me know, and we can work through it together!

## Message 3

Yes, I can help you write a Python script for 3ds Max 2025. Could you provide more details about what you’d like the script to do? That way, I can tailor the solution to your specific requirements and ensure it integrates properly with 3ds Max’s Python API.

## Message 4

please explain exactly what this is and what it's for:

import sys

import os

import subprocess

import json

import fnmatch

from PySide6 import QtCore, QtGui, QtWidgets

from PySide6.QtCore import Qt, QDateTime, QSize

from PySide6.QtGui import QAction, QKeySequence

from pymxs import runtime as rt

import qtmax



PRINT_DEBUG_INFO = False



class Config:

    STYLESHEET_TEMPLATE = """

        QTreeWidget {{

            background-color: {tree_bg};

            color: {tree_fg};

            border: none;

            padding: {pad}px;

        }}

        QTreeWidget::item {{

            padding: {pad}px;

            font-size: {item_font_size}px;

        }}

        QTreeWidget::item:hover {{

            background-color: {hover_bg};

        }}

        QTreeWidget::item:selected {{

            background-color: {sel_bg};

        }}

        QTreeWidget::item:pressed {{

            background-color: {press_bg};

        }}

        QHeaderView::section {{

            background-color: {hdr_bg};

            color: {hdr_fg};

            padding: {pad}px;

            font-size: {hdr_font_size}px;

            border: 1px solid {hdr_border};

        }}

    """



    class UI:

        MIN_DOCK_WIDTH = 300

        MIN_DOCK_HEIGHT = 400

        SPLITTER_RATIO = [1, 2]

        ICON_SIZE = QSize(20, 20)

        TREE_INDENT = 20

        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}

        SEARCH_DEBOUNCE_MS = 150

        SEARCH_MIN_CHARS = 2

        AUTO_EXPAND_RESULTS = True

        PRESERVE_EXPANSION = False

        FOLDERS_EXPANDABLE = True

        DOUBLE_CLICK_ACTION = "run"

        BUTTON_PADDING_RATIO = 0.002

        HEADER_FONT_RATIO = 0.007

        ITEM_FONT_RATIO = 0.005



    class Style:

        FAVORITES_BG = "#1a1a1a"

        TREE_BG = "#2b2b2b"

        FAVORITES_FG = "#f0f0f0"

        TREE_FG = "#f0f0f0"

        PIN_COLOR = "#79ff1c"

        HDR_BG = "#2b2b2b"

        HDR_FG = "#f0f0f0"

        HDR_BORDER = "#3c3c3c"

        HOVER_BG = "#3c3c3c"

        SEL_BG = "#1e90ff"

        PRESS_BG = "#104e8b"

        TINT_RULES = [

            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},

            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},

            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},

            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},

        ]



    class Paths:

        SUPPORTED = ('.ms', '.mse', '.json')

        MAX_DEPTH = 8



        @classmethod

        def favorites_file(cls):

            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")



        @classmethod

        def run_counts_file(cls):

            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")



    class Menu:

        # These keybindings appear in the context menus.

        CONTEXT = {

            'file': [

                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},

                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},

                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},

                {'text': '-'},

                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},

                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}

            ],

            'folder': [

                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},

                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},

                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},

                {'text': '-'},

                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}

            ],

            'favorites': [

                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},

                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},

                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},

                {'text': '-'},

                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}

            ]

        }



    def stylesheet(self):

        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()

        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)

        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))

        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))

        return self.STYLESHEET_TEMPLATE.format(

            tree_bg=Config.Style.TREE_BG,

            tree_fg=Config.Style.TREE_FG,

            hdr_bg=Config.Style.HDR_BG,

            hdr_fg=Config.Style.HDR_FG,

            hdr_border=Config.Style.HDR_BORDER,

            hover_bg=Config.Style.HOVER_BG,

            sel_bg=Config.Style.SEL_BG,

            press_bg=Config.Style.PRESS_BG,

            pad=pad,

            hdr_font_size=hdr_fs,

            item_font_size=item_fs

        )



def create_menu(menu_items, parent):

    m = QtWidgets.QMenu(parent)

    for it in menu_items:

        if it['text'] == '-':

            m.addSeparator()

            continue

        a = QAction(it['text'], parent)

        if 'shortcut' in it:

            a.setShortcut(QKeySequence(it['shortcut']))

        if 'icon' in it:

            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))

            a.setIcon(icon)

        m.addAction(a)

    return m



def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):

    if depth > max_depth:

        return {}

    structure = {}

    try:

        with os.scandir(path) as it:

            dirs, files = [], []

            for e in it:

                if e.is_dir():

                    dirs.append(e)

                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):

                    files.append(e)

            dirs.sort(key=lambda x: x.name.lower())

            files.sort(key=lambda x: x.name.lower())

            for d in dirs:

                sub = scan_dir(d.path, max_depth, depth+1)

                if sub:

                    structure[d.name] = sub

            for f in files:

                structure[f.name] = f.path

    except PermissionError:

        pass

    return structure



def blend(base_hex, overlay_hex, alpha=0.3):

    base = QtGui.QColor(base_hex)

    over = QtGui.QColor(overlay_hex)

    r = int(base.red()*(1-alpha) + over.red()*alpha)

    g = int(base.green()*(1-alpha) + over.green()*alpha)

    b = int(base.blue()*(1-alpha) + over.blue()*alpha)

    c = QtGui.QColor(r, g, b).name()

    if PRINT_DEBUG_INFO:

        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")

    return c



class ScriptItem(QtWidgets.QTreeWidgetItem):

    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):

        super().__init__([name, "" if folder else str(count)])

        self.original_name = name

        self.script_path = path

        self.is_folder = folder

        self.is_pinned = pinned

        self.run_count = count

        self.setData(0, Qt.UserRole, path)

        self.setData(0, Qt.UserRole+1, folder)

        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)

        self.set_icon()

        self.defer_tooltip()

        if pinned:

            self.setData(0, Qt.UserRole+2, True)

            self.setData(1, Qt.UserRole+2, True)

            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)

            self.setForeground(0, QtGui.QBrush(pcolor))

            self.setForeground(1, QtGui.QBrush(pcolor))

        if not folder:

            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)

        self.tint_background(parents)



    def set_icon(self):

        style = QtWidgets.QApplication.style()

        if self.is_pinned:

            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)

        elif self.is_folder:

            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)

        else:

            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)

        self.setIcon(0, ic)



    def defer_tooltip(self):

        if not self.is_folder and self.script_path:

            self.setToolTip(0, "Loading...")

            QtCore.QTimer.singleShot(100, self.load_tooltip)



    def load_tooltip(self):

        if self.script_path and os.path.exists(self.script_path):

            try:

                st = os.stat(self.script_path)

                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)

                sz = st.st_size

                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")

            except Exception as e:

                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")



    def update_run_count(self, n):

        self.run_count = n

        if not self.is_folder:

            self.setText(1, str(n))



    def tint_background(self, parents):

        tinted = False

        if parents:

            for rule in Config.Style.TINT_RULES:

                pat, c, a = rule['pattern'], rule['color'], rule['alpha']

                for f in parents:

                    if fnmatch.fnmatch(f.lower(), pat):

                        blended = blend(Config.Style.TREE_BG, c, a)

                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))

                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))

                        tinted = True

                        break

                if tinted:

                    break

        if not tinted:

            bg = QtGui.QColor(Config.Style.TREE_BG)

            self.setBackground(0, QtGui.QBrush(bg))

            self.setBackground(1, QtGui.QBrush(bg))



class FavoriteItem(QtWidgets.QListWidgetItem):

    def __init__(self, name, path, count=0):

        super().__init__(name)

        self.script_path = path

        self.run_count = count

        self.is_pinned = True

        self.is_folder = False

        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})

        self.setToolTip(f"Path: {path}\nRun Count: {count}")



    def update_run_count(self, n):

        self.run_count = n

        d = self.data(Qt.UserRole)

        d['run_count'] = n

        self.setData(Qt.UserRole, d)

        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")



class Dock(QtWidgets.QDockWidget):

    def __init__(self, parent=None, scripts_dir=None):

        super().__init__(parent)

        self.setWindowTitle("Jorn Tools: Widget")

        self.setAllowedAreas(Qt.AllDockWidgetAreas)

        self.setObjectName("jornWidget_treeView")

        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)



        self.scripts_directory = scripts_dir

        self.favorites = {}

        self.run_counts = {}

        self._search_index = {}

        self._last_search = ""



        self.init_ui()

        self.load_run_counts()

        self.load_favorites()

        if self.scripts_directory:

            self.build_tree()

        self.init_file_watcher()

        self.init_shortcuts()



    def init_ui(self):

        c = QtWidgets.QWidget()

        self.setWidget(c)

        lay = QtWidgets.QVBoxLayout(c)

        split = QtWidgets.QSplitter(Qt.Vertical)

        lay.addWidget(split)



        fav_cont = QtWidgets.QWidget()

        fav_lay = QtWidgets.QVBoxLayout(fav_cont)

        fav_lay.setContentsMargins(0, 0, 0, 0)

        fav_label = QtWidgets.QLabel("Favorites")

        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")

        fav_lay.addWidget(fav_label)



        self.favorites_widget = QtWidgets.QListWidget()

        # Set the Favorites widget to receive focus

        self.favorites_widget.setFocusPolicy(Qt.StrongFocus)

        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)

        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)

        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)

        self.favorites_widget.setDropIndicatorShown(True)

        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)

        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)

        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)

        fav_lay.addWidget(self.favorites_widget)

        split.addWidget(fav_cont)



        h = QtWidgets.QApplication.primaryScreen().geometry().height()

        p = int(h * Config.UI.BUTTON_PADDING_RATIO)

        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))

        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))

        style = Config().stylesheet()

        self.favorites_widget.setStyleSheet(style)

        self.favorites_widget.setIconSize(QSize(20, 20))

        ff = self.favorites_widget.font()

        ff.setPointSize(it_fs)

        self.favorites_widget.setFont(ff)



        main_cont = QtWidgets.QWidget()

        main_lay = QtWidgets.QVBoxLayout(main_cont)

        main_lay.setContentsMargins(0, 0, 0, 0)

        main_label = QtWidgets.QLabel("Scripts")

        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")

        main_lay.addWidget(main_label)



        self.tree_widget = QtWidgets.QTreeWidget()

        self.tree_widget.setHeaderLabels(["Name", "Run Count"])

        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)

        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)

        self.tree_widget.setColumnWidth(1, 80)

        self.tree_widget.setHeaderHidden(False)

        hi = self.tree_widget.headerItem()

        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)

        self.tree_widget.setStyleSheet(style)

        self.tree_widget.setIconSize(QSize(20, 20))

        self.tree_widget.itemDoubleClicked.connect(self.run_script)

        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)

        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)

        self.tree_widget.setDragEnabled(True)

        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)

        main_lay.addWidget(self.tree_widget)



        self.search_bar = QtWidgets.QLineEdit()

        self.search_bar.setPlaceholderText("Search...")

        self.search_bar.setFocusPolicy(Qt.ClickFocus)

        self.search_bar.textChanged.connect(self.debounced_filter)

        main_lay.addWidget(self.search_bar)

        split.addWidget(main_cont)



        split.setStretchFactor(0, 0)

        split.setStretchFactor(1, 1)

        split.setSizes([300, 600])



        tf = self.tree_widget.font()

        tf.setPointSize(it_fs)

        self.tree_widget.setFont(tf)



    def init_shortcuts(self):

        # Global shortcuts for the Dock:

        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)

        pin_sc.activated.connect(self.pin_selected)

        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)

        unpin_sc.activated.connect(self.unpin_selected)

        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)

        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)

        enter_sc.activated.connect(self.run_selected_if_focused)

        # Shortcuts for the Scripts (tree) widget:

        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)

        explorer_sc.activated.connect(self.handle_explorer_shortcut)

        file_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.tree_widget)

        file_open_sc.activated.connect(self.open_selected_script)

        expand_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+E"), self.tree_widget)

        expand_sc.activated.connect(self.expand_selected)

        collapse_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+C"), self.tree_widget)

        collapse_sc.activated.connect(self.collapse_selected)

        # Instead of attaching directly to the Favorites widget,

        # attach favorites shortcuts to the Dock and check if favorites_widget is focused.

        fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self)

        fav_run_sc.setContext(Qt.WidgetWithChildrenShortcut)

        fav_run_sc.activated.connect(self.handle_fav_run)

        fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self)

        fav_open_sc.setContext(Qt.WidgetWithChildrenShortcut)

        fav_open_sc.activated.connect(self.handle_fav_open)

        fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self)

        fav_explorer_sc.setContext(Qt.WidgetWithChildrenShortcut)

        fav_explorer_sc.activated.connect(self.handle_fav_explorer)

        fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)

        fav_unpin_sc.setContext(Qt.WidgetWithChildrenShortcut)

        fav_unpin_sc.activated.connect(self.handle_fav_unpin)



    # --- Helper methods for the Scripts widget ---

    def handle_explorer_shortcut(self):

        if self.tree_widget.hasFocus():

            sel = self.tree_widget.selectedItems()

            if not sel:

                return

            for item in sel:

                if item.script_path:

                    if item.is_folder:

                        self.open_in_explorer(item.script_path)

                    else:

                        folder = os.path.dirname(item.script_path)

                        self.open_in_explorer(folder)

                    break



    def open_selected_script(self):

        sel = self.tree_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No script selected.", 5000)

            return

        for it in sel:

            if not it.is_folder and it.script_path:

                self.open_script_file(it.script_path)



    def expand_selected(self):

        sel = self.tree_widget.selectedItems()

        if not sel:

            return

        for item in sel:

            if item.is_folder:

                self.expand_all(item)



    def collapse_selected(self):

        sel = self.tree_widget.selectedItems()

        if not sel:

            return

        for item in sel:

            if item.is_folder:

                self.collapse_all(item)



    # --- Helper methods for the Favorites widget (via the Dock) ---

    def handle_fav_run(self):

        # Only trigger if the favorites widget is focused.

        if self.favorites_widget.hasFocus():

            self.run_selected_favorites()



    def handle_fav_open(self):

        if self.favorites_widget.hasFocus():

            self.open_selected_favorite_script()



    def handle_fav_explorer(self):

        if self.favorites_widget.hasFocus():

            self.explorer_selected_favorites()



    def handle_fav_unpin(self):

        if self.favorites_widget.hasFocus():

            self.unpin_selected()



    def run_selected_favorites(self):

        sel = self.favorites_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No favorite selected.", 5000)

            return

        for item in sel:

            self.run_script_from_favorites(item)



    def open_selected_favorite_script(self):

        sel = self.favorites_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No favorite selected.", 5000)

            return

        for item in sel:

            d = item.data(Qt.UserRole)

            p = d.get("script_path", None)

            if p:

                self.open_script_file(p)



    def explorer_selected_favorites(self):

        sel = self.favorites_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No favorite selected.", 5000)

            return

        for item in sel:

            d = item.data(Qt.UserRole)

            p = d.get("script_path", None)

            if p:

                folder = os.path.dirname(p)

                self.open_in_explorer(folder)

                break



    def init_file_watcher(self):

        self.fs_watcher = QtCore.QFileSystemWatcher()

        if self.scripts_directory:

            self.fs_watcher.addPath(self.scripts_directory)

            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)



    def load_run_counts(self):

        rp = Config.Paths.run_counts_file()

        if os.path.exists(rp):

            try:

                with open(rp, 'r') as f:

                    self.run_counts = json.load(f)

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")

                self.run_counts = {}

        else:

            self.save_run_counts()



    def save_run_counts(self):

        try:

            with open(Config.Paths.run_counts_file(), 'w') as f:

                json.dump(self.run_counts, f, indent=4)

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")



    def load_favorites(self):

        fp = Config.Paths.favorites_file()

        if os.path.exists(fp):

            try:

                with open(fp, 'r') as f:

                    self.favorites = json.load(f)

                for n, pth in self.favorites.items():

                    if os.path.exists(pth):

                        c = self.run_counts.get(pth, 0)

                        it = FavoriteItem(n, pth, c)

                        self.favorites_widget.addItem(it)

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")

                self.favorites = {}

        else:

            self.save_favorites()



    def save_favorites(self):

        try:

            with open(Config.Paths.favorites_file(), 'w') as f:

                json.dump(self.favorites, f, indent=4)

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")



    def build_tree(self):

        self.tree_widget.setUpdatesEnabled(False)

        self._search_index.clear()

        self.tree_widget.clear()

        self.folder_structure = scan_dir(self.scripts_directory)

        self.populate_tree(self.tree_widget, self.folder_structure, [])

        self.tree_widget.setUpdatesEnabled(True)



    def rebuild_tree(self, _path):

        self.build_tree()



    def populate_tree(self, parent, structure, parents):

        for name, val in structure.items():

            if isinstance(val, dict):

                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))

                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)

                if isinstance(parent, QtWidgets.QTreeWidget):

                    parent.addTopLevelItem(it)

                else:

                    parent.addChild(it)

                self.index_item(it)

                self.populate_tree(it, val, parents + [name])

            else:

                it = ScriptItem(name, path=val, parents=parents)

                if isinstance(parent, QtWidgets.QTreeWidget):

                    parent.addTopLevelItem(it)

                else:

                    parent.addChild(it)

                self.index_item(it)



    def refresh_item(self, it):

        if getattr(it, 'is_folder', False):

            fpath = it.script_path

            if not fpath or not os.path.isdir(fpath):

                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")

                return

            pf = self.parent_names(it)

            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)

            it.takeChildren()

            self.populate_tree(it, st, pf + [it.text(0)])

            it.setExpanded(False)

        else:

            p = getattr(it, 'script_path', None)

            if p and os.path.exists(p):

                c = self.run_counts.get(p, 0)

                it.update_run_count(c)

                it.defer_tooltip()

            else:

                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")



    def debounced_filter(self, text):

        if hasattr(self, '_filter_timer'):

            self._filter_timer.stop()

        self._filter_timer = QtCore.QTimer(singleShot=True)

        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))

        self._filter_timer.start(150)



    def apply_filter(self, t):

        self.tree_widget.setUpdatesEnabled(False)

        t = t.strip().lower()

        if not t:

            self.show_all(collapse=True)

        else:

            self.hide_all()

            self.show_matches(t)

        self.tree_widget.setUpdatesEnabled(True)



    def show_all(self, collapse=False):

        for items in self._search_index.values():

            for it in items:

                it.setHidden(False)

                p = it.parent()

                while p and p != self.tree_widget.invisibleRootItem():

                    p.setHidden(False)

                    if collapse:

                        p.setExpanded(False)

                    p = p.parent()



    def hide_all(self):

        for items in self._search_index.values():

            for it in items:

                it.setHidden(True)



    def show_matches(self, txt):

        found = set()

        for n, items in self._search_index.items():

            if txt in n:

                for it in items:

                    found.add(it)

                    p = it.parent()

                    while p and p != self.tree_widget.invisibleRootItem():

                        found.add(p)

                        p = p.parent()

        for it in found:

            it.setHidden(False)

            if it.childCount() > 0:

                it.setExpanded(True)



    def display_context_menu(self, pos):

        w = self.sender()

        it = w.itemAt(pos)

        if not it:

            return

        if w == self.favorites_widget:

            menu_items = Config.Menu.CONTEXT['favorites']

        else:

            is_folder = getattr(it, 'is_folder', False)

            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']

        menu = create_menu(menu_items, w)

        chosen = menu.exec(w.viewport().mapToGlobal(pos))

        if chosen:

            self.execute_menu_action(chosen.text(), it)



    def execute_menu_action(self, text, it):

        sp = getattr(it, 'script_path', None)

        from_fav = (self.sender() == self.favorites_widget)

        acts = {

            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,

            'Open Script':        lambda: self.open_script_file(sp) if sp else None,

            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,

            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,

            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,

            'Refresh':            lambda: self.refresh_item(it),

            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,

            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,

            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None

        }

        a = acts.get(text)

        if a:

            a()



    def pin_item(self, it):

        n, p = it.original_name, it.script_path

        if not p:

            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")

            return

        if any(existing == p for existing in self.favorites.values()):

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)

            return

        self.favorites[n] = p

        self.save_favorites()

        c = self.run_counts.get(p, 0)

        fav = FavoriteItem(n, p, c)

        self.favorites_widget.addItem(fav)

        it.is_pinned = True

        it.set_icon()



    def unpin_item(self, item, from_favorites=False):

        if hasattr(item, 'is_pinned') and item.is_pinned:

            if from_favorites:

                d = item.data(Qt.UserRole)

                n, p = d['name'], d['script_path']

            else:

                n, p = item.original_name, item.script_path

            if n in self.favorites and self.favorites[n] == p:

                del self.favorites[n]

                self.save_favorites()

                if from_favorites:

                    r = self.favorites_widget.row(item)

                    if r != -1:

                        self.favorites_widget.takeItem(r)

                mw = self.parent()

                if isinstance(mw, QtWidgets.QMainWindow):

                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)



    def pin_selected(self):

        sel = self.tree_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No script to pin.", 5000)

            return

        for it in sel:

            if not it.is_folder:

                self.pin_item(it)



    def unpin_selected(self):

        sel = self.favorites_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No favorite to unpin.", 5000)

            return

        for it in sel:

            self.unpin_item(it, True)



    def run_script(self, it, col):

        p = it.script_path

        if p and not it.is_folder:

            try:

                rt.filein(p)

                mw = self.parent()

                if isinstance(mw, QtWidgets.QMainWindow):

                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)

                c = self.run_counts.get(p, 0) + 1

                self.run_counts[p] = c

                self.save_run_counts()

                it.update_run_count(c)

                if it.is_pinned:

                    fi = self.find_fav_by_path(p)

                    if fi:

                        fi.update_run_count(c)

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")



    def run_script_by_path(self, p):

        if p and os.path.exists(p):

            try:

                rt.filein(p)

                mw = self.parent()

                if isinstance(mw, QtWidgets.QMainWindow):

                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)

                c = self.run_counts.get(p, 0) + 1

                self.run_counts[p] = c

                self.save_run_counts()

                return c

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

        else:

            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")

        return None



    def run_script_from_favorites(self, it):

        d = it.data(Qt.UserRole)

        p = d['script_path']

        if p and os.path.exists(p):

            self.run_script_by_path(p)

            c = self.run_counts.get(p, 0)

            it.update_run_count(c)



    def run_selected_if_focused(self):

        if self.tree_widget.hasFocus():

            self.run_selected()

        elif self.favorites_widget.hasFocus():

            self.run_selected_favorites()



    def run_selected(self):

        sel = self.tree_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No scripts selected.", 5000)

            return

        for it in sel:

            if not it.is_folder and it.script_path:

                self.run_script(it, 0)



    def open_in_explorer(self, p):

        try:

            if os.name == 'nt':

                subprocess.Popen(['explorer', '/select,', p])

            elif os.name == 'posix':

                subprocess.Popen(['open', '-R', p])

            else:

                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")



    def open_script_file(self, p):

        try:

            if os.name == 'nt':

                os.startfile(p)

            elif os.name == 'posix':

                subprocess.Popen(['open', p])

            else:

                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")



    def dragEnterEvent(self, ev):

        if ev.mimeData().hasUrls():

            ev.acceptProposedAction()

        else:

            ev.ignore()



    def dropEvent(self, ev):

        if ev.mimeData().hasUrls():

            for url in ev.mimeData().urls():

                p = url.toLocalFile()

                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):

                    n = os.path.basename(p)

                    it = self.find_by_path(self.tree_widget, p)

                    if it and not it.is_pinned:

                        self.pin_item(it)

            ev.acceptProposedAction()

        else:

            ev.ignore()



    def find_by_path(self, parent, path):

        for i in range(parent.childCount()):

            c = parent.child(i)

            if c.script_path == path:

                return c

            if c.is_folder:

                r = self.find_by_path(c, path)

                if r:

                    return r

        return None



    def find_fav_by_path(self, path):

        for i in range(self.favorites_widget.count()):

            fav = self.favorites_widget.item(i)

            d = fav.data(Qt.UserRole)

            if d and d['script_path'] == path:

                return fav

        return None



    def parent_names(self, it):

        names = []

        p = it.parent()

        while p:

            names.insert(0, p.text(0))

            p = p.parent()

        return names



    def index_item(self, it):

        n = it.original_name.lower()

        if n not in self._search_index:

            self._search_index[n] = []

        self._search_index[n].append(it)



    def expand_all(self, it):

        it.setExpanded(True)

        for i in range(it.childCount()):

            self.expand_all(it.child(i))



    def collapse_all(self, it):

        it.setExpanded(False)

        for i in range(it.childCount()):

            self.collapse_all(it.child(i))



    def mouseDoubleClickEvent(self, ev):

        super().mouseDoubleClickEvent(ev)



def create_dock_widget(scripts_directory):

    mw = qtmax.GetQMaxMainWindow()

    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")

    if existing:

        mw.removeDockWidget(existing)

        existing.deleteLater()

    dw = Dock(parent=mw, scripts_dir=scripts_directory)

    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)

    dw.setFloating(False)

    dw.show()



def main():

    scripts_dir = ""

    try:

        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):

            scripts_dir = rt.novMaxscriptsPath

    except AttributeError:

        scripts_dir = rt.getDir(rt.Name('userMacros'))

    mw = qtmax.GetQMaxMainWindow()

    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")

    if existing:

        mw.removeDockWidget(existing)

        existing.deleteLater()

    create_dock_widget(scripts_dir)



if __name__ == "__main__":

    main()


## Message 5

**Explaining code snippet**

The user provided a Python code snippet, asking for a detailed explanation. It's clear this script is designed for 3ds Max, using PySide6 and pymxs, along with qtmax. The script seems to add a dockable widget to 3ds Max's UI, displaying a tree structure of available scripts and managing favorites.

First, I see imports like `sys`, `os`, `subprocess`, `json`, and `fnmatch`. These are standard Python modules for handling system operations, file paths, processes, JSON data, and pattern matching, respectively. I’ll dive deeper into the rest of the code now!

**Breaking down the code structure**

The script begins by importing necessary modules—`QtCore`, `QtGui`, `QtWidgets`, and `pymxs`, along with `qtmax` for integrating with 3ds Max.

There’s a `Config` class housing various settings such as `UI`, `Style`, `Paths`, and `Menu`. These help configure the widget’s behavior and appearance, like UI properties, colors, file extensions, and directory paths.

The `stylesheet()` function generates a style template, adjusting padding and font sizes based on screen geometry. A `create_menu()` function follows, building menus from dictionary-based items with menu text, shortcuts, and icons.

**Breaking down script functionalities**

The script supports directory scanning (`scan_dir`), which recursively finds files up to a set depth, matching supported file extensions. It includes a method to blend two hex colors (`blend`).

Classes like `ScriptItem`, `FavoriteItem`, and `Dock` manage tree items and the dock widget in 3ds Max's interface, enabling functionality like file handling, favorites, run count tracking, and UI interactions. The `Dock` class offers key methods like file watching, context menus, and script execution.

Finally, `create_dock_widget` and `main()` set up the widget to run within 3ds Max's UI—this tool organizes and simplifies script management.

## Message 6

This script is a complete tool that integrates with 3ds Max (using its Python API) to provide a dockable user interface (UI) for browsing, running, and managing script files. Here’s a detailed breakdown of what it is and what it does:

---

### 1. **Overall Purpose**

- **Script Management in 3ds Max:**  
  The tool creates a dockable widget inside 3ds Max that lets you browse a directory of script files (e.g., MaxScript files with extensions like `.ms` or `.mse`, and even `.json` files), execute them, and keep track of how often each script is run.

- **Favorites & Run Counts:**  
  It allows you to “pin” frequently used scripts to a favorites list and automatically tracks and displays the number of times each script has been executed.

- **Enhanced User Interaction:**  
  With features like context menus, drag-and-drop, keyboard shortcuts, and a search bar, the script enhances the usability of managing and executing scripts directly from within 3ds Max.

---

### 2. **Key Libraries and Their Roles**

- **Standard Libraries (`sys`, `os`, `subprocess`, `json`, `fnmatch`):**  
  Used for system operations, file handling, launching external processes (like opening Windows Explorer), JSON file management (for storing favorites and run counts), and pattern matching.

- **PySide6 (Qt for Python):**  
  Provides the GUI components (widgets, menus, icons, etc.). The script uses various PySide6 modules to build and style the UI elements.

- **pymxs:**  
  This is 3ds Max’s Python module, which provides access to MaxScript functionality (e.g., using `rt.filein` to execute a script file) and directories used by 3ds Max (like the user macros folder).

- **qtmax:**  
  Likely a helper module that gives access to 3ds Max’s main window so that the custom dock widget can be attached to the 3ds Max UI.

---

### 3. **Configuration and Styling (The `Config` Class)**

- **UI Settings:**  
  Contains settings like minimum dock dimensions, splitter ratios, icon sizes, and column widths. These determine the layout and sizing of the widget.

- **Style Settings:**  
  Defines color themes for various elements (backgrounds, text, hover effects, etc.) and includes a template for a stylesheet that is dynamically adjusted based on the screen size.

- **Path Settings:**  
  Specifies which file extensions are supported and provides methods to determine the file paths for saving favorites and run counts (using 3ds Max’s user macros directory).

- **Menu Definitions:**  
  Predefines context menus for different elements (files, folders, favorites) with labels, shortcuts, and icons. This allows the UI to offer quick actions like “Run Script,” “Open Script,” “Show in Explorer,” and options to pin/unpin favorites.

---

### 4. **Core Components and Their Functionality**

#### **a. UI Widgets**

- **ScriptItem (Tree Widget Item):**  
  Represents an individual script file or folder in the tree view.  
  - It tracks whether it’s a folder, its run count, and if it’s pinned.  
  - It dynamically sets icons, defers loading of detailed tooltips (like file size and last modified date), and even adjusts its background color based on specific patterns (using the `blend` function).

- **FavoriteItem (List Widget Item):**  
  Represents a pinned or “favorite” script in a separate list. It displays the script name, keeps its path and run count, and updates its tooltip accordingly.

#### **b. The Dock Widget (`Dock` Class)**

- **Main Container:**  
  Inherits from `QtWidgets.QDockWidget` so that it can be docked inside the 3ds Max interface. It is given a unique object name so that previous instances can be replaced when the script is re-run.

- **Layout and Sub-Widgets:**
  - **Favorites Panel:**  
    A list widget that shows pinned scripts.
  - **Scripts Tree:**  
    A tree widget that displays the hierarchical structure of the scripts directory.
  - **Search Bar:**  
    A line edit that lets you quickly filter scripts by name.

- **Functionality within the Dock:**
  - **Scanning and Populating the Tree:**  
    The `scan_dir` function recursively reads the scripts directory and builds a nested dictionary. The `populate_tree` method then uses this structure to add `ScriptItem` entries to the tree view.
  - **File Watching:**  
    A file system watcher monitors the scripts directory for changes and automatically refreshes the tree.
  - **Context Menus and Shortcuts:**  
    Right-clicking on items brings up context menus (built by the `create_menu` function) offering actions such as running a script, opening the file location, or pinning/unpinning favorites. Keyboard shortcuts are also set up for quick actions.
  - **Script Execution:**  
    When a script is double-clicked or run via a shortcut, the script file is executed in 3ds Max using `rt.filein()`. The run count is incremented and stored.

- **Drag-and-Drop:**  
  Supports dragging script files onto the widget to pin them to favorites.

---

### 5. **Utility Functions and Entry Point**

- **Helper Functions:**
  - **`create_menu`:**  
    Builds context menus dynamically from a list of dictionaries defining menu items.
  - **`blend`:**  
    Blends two colors, used to apply a tint to tree items based on naming patterns.
  - **`scan_dir`:**  
    Recursively scans the designated scripts directory, filtering for supported file types.

- **Dock Widget Creation (`create_dock_widget` Function):**  
  Attaches the custom dock widget to 3ds Max’s main window. It first removes any existing widget with the same identifier before adding the new one.

- **Main Function (`main`):**  
  Determines the appropriate scripts directory (using either a dedicated attribute or the user macros directory), then creates and shows the dock widget.

- **Script Entry Point:**  
  The check `if __name__ == "__main__": main()` ensures that when this script is run directly within 3ds Max’s Python environment, it executes and integrates into the 3ds Max UI.

---

### **In Summary**

- **What It Is:**  
  A comprehensive Python script that creates a dockable interface in 3ds Max. It provides a tree view of script files, a list for favorites, and integrated features for running scripts and managing them.

- **What It’s For:**  
  It is designed to help users quickly access, execute, and organize their custom scripts within 3ds Max. By integrating with the 3ds Max UI, it streamlines the workflow for script-based automation and customization.

This script is a robust utility that leverages PySide6 for UI, pymxs for 3ds Max integration, and standard Python libraries to manage file operations and user preferences. It essentially transforms script management into a user-friendly, interactive experience right inside 3ds Max.

## Message 7

all of the keybindings for the "Scripts" widget (files and folder) works fine, but none of the keybindings work for the "Favorites" widget. are you able to pinpoint the exact reason for this?

## Message 8

**Investigating keybinding issue**

The user is asking about keybindings that work for the "Scripts" widget but not for the "Favorites" widget. To figure this out, I'll start by searching for keybinding definitions in the code for both. I need to look for any differences between how the shortcuts are set up, especially since the "Scripts" widget creates shortcuts through the `init_shortcuts` function in the Dock. I wonder if the "Favorites" widget is missing a similar setup. Let me explore both cases and compare.

**Checking favorites keybinding issue**

The favorites shortcuts are attached to the Dock, not directly to the `favorites_widget`. The keybindings only work when `favorites_widget` has focus, so if it’s not receiving focus events, the shortcuts won’t trigger. The `handle_fav_*` functions check if the widget has focus before executing. Potential issues could be that the widget isn’t properly receiving focus due to event handling or it being a `QListWidget`, which might capture input differently. I’ll now check the relevant code in `Dock`'s `init_ui` to see what’s going on.

**Diagnosing focus and shortcut conflict**

The code sets the focus policy for `favorites_widget` to StrongFocus, meaning it should be able to gain focus when clicked. However, I suspect it isn't receiving focus because the `Dock` widget may have focus instead, as the favorites shortcuts are attached to it directly, not the widget. This could prevent the shortcuts from being triggered. Additionally, there might be focus conflicts with the `tree_widget`. Finally, it's worth checking if `QAbstractItemView`'s behavior affects the `hasFocus()` check when a list item is selected.


## Message 9

Yes. The root of the problem is that the shortcuts for the Favorites widget are attached to the Dock (i.e. `self`) and then gated by a focus check that never passes as expected. In other words, the code for the favorites’ keybindings does this:

```python
def handle_fav_run(self):
    # Only trigger if the favorites widget is focused.
    if self.favorites_widget.hasFocus():
        self.run_selected_favorites()
```

Even when a favorite item is selected, the call to `self.favorites_widget.hasFocus()` returns false (because the focus is actually on one of its internal child widgets or the selection isn’t causing the overall QListWidget to report focus). As a result, the favorites shortcuts (such as Return, Ctrl+Shift+O, etc.) never get activated.

In contrast, the scripts widget’s shortcuts are attached directly to that widget (i.e. `self.tree_widget`), so they reliably receive focus and work as expected.

To fix this, you can either:

1. **Attach the Favorites shortcuts directly to the favorites widget** instead of the Dock, or  
2. **Modify the focus check** to recognize when a child of the favorites widget is focused (for example, by checking if the current focus widget is a descendant of the favorites widget).

This focus issue is the exact reason why none of the keybindings for the Favorites widget appear to work.

## Message 10

please show the solution

## Message 11

**Providing code solution**

The task is clear: show the solution for attaching shortcuts to the Favorites widget to fix the focus issue. The previous answer indicated that the shortcuts were attached to the dock instead. One possible fix might involve assigning the shortcuts to the "favorites_widget" directly, using something like `Qt.WidgetWithChildrenShortcut` for focus management. Now, I need to craft a code snippet to demonstrate how we can implement this fix. Let me work that out.

**Providing code update**

The original code attaches shortcuts to the main window ("self"), but to fix the focus issue, we should attach these shortcuts directly to the `favorites_widget`. I'll adjust the code snippet accordingly, attaching each shortcut to the widget itself instead of the main window. This direct attachment eliminates the need for a focus check since the shortcuts will only trigger when the favorites widget is focused.

Here’s the updated code for `init_shortcuts()`:
```
def init_shortcuts(self):
    fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
    fav_run_sc.activated.connect(self.handle_fav_run)
    fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.favorites_widget)
    fav_open_sc.activated.connect(self.handle_fav_open)
    fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.favorites_widget)
    fav_explorer_sc.activated.connect(self.handle_fav_explorer)
    fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self.favorites_widget)
    fav_unpin_sc.activated.connect(self.handle_fav_unpin)
```
You'll no longer need the `hasFocus()` check in the handler, as the shortcuts are only active when the widget is focused.

## Message 12

Below is one way to fix the issue by attaching the Favorites widget’s shortcuts directly to the favorites widget instead of the Dock. In the original code, the favorites shortcuts were created with the Dock (`self`) as their parent and then checked if the favorites widget had focus. By making the favorites widget the parent of these shortcuts, they will trigger only when the favorites widget (or one of its children) has focus, removing the need for extra focus checks.

---

### **Revised `init_shortcuts` for Favorites**

Replace the original block for the favorites shortcuts:

```python
# Original code attaching favorites shortcuts to the Dock:
fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self)
fav_run_sc.setContext(Qt.WidgetWithChildrenShortcut)
fav_run_sc.activated.connect(self.handle_fav_run)
fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self)
fav_open_sc.setContext(Qt.WidgetWithChildrenShortcut)
fav_open_sc.activated.connect(self.handle_fav_open)
fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self)
fav_explorer_sc.setContext(Qt.WidgetWithChildrenShortcut)
fav_explorer_sc.activated.connect(self.handle_fav_explorer)
fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
fav_unpin_sc.setContext(Qt.WidgetWithChildrenShortcut)
fav_unpin_sc.activated.connect(self.handle_fav_unpin)
```

with this updated code that attaches the shortcuts directly to the `favorites_widget`:

```python
# Updated code attaching favorites shortcuts to the Favorites widget directly:
fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
fav_run_sc.activated.connect(self.handle_fav_run)
fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.favorites_widget)
fav_open_sc.activated.connect(self.handle_fav_open)
fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.favorites_widget)
fav_explorer_sc.activated.connect(self.handle_fav_explorer)
fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self.favorites_widget)
fav_unpin_sc.activated.connect(self.handle_fav_unpin)
```

---

### **Additional Adjustment**

Since the shortcuts are now parented to `self.favorites_widget`, you can remove the explicit focus checks from the handlers. For example, change:

```python
def handle_fav_run(self):
    # Only trigger if the favorites widget is focused.
    if self.favorites_widget.hasFocus():
        self.run_selected_favorites()
```

to a simplified version:

```python
def handle_fav_run(self):
    self.run_selected_favorites()
```

Apply a similar change for the other favorites-related handler functions (e.g., `handle_fav_open`, `handle_fav_explorer`, `handle_fav_unpin`).

---

### **Result**

With these changes, the keybindings for the Favorites widget will now work correctly because:

- **Direct Attachment:** The shortcuts are directly attached to the widget that should handle them, so they will be active when the Favorites widget (or any of its child items) is focused.
- **No Redundant Focus Check:** Removing the extra `hasFocus()` check ensures that the shortcut handlers run as soon as the corresponding key sequence is pressed while the widget has focus.

This solution should resolve the issue of the Favorites widget’s keybindings not working in your 3ds Max dockable interface.

## Message 13

please show the full updated code with a proper implementation of your proposed solution for this scenario and context, do this while retaining the existing functionality and creating an in-place replacement

## Message 14

**Updating keybindings**

Okay, the user is asking for an updated implementation. Specifically, in the `init_shortcuts` method of the Dock class, we need to update keybindings for the favorites widget. Right now, these shortcuts are tied to "self" (the Dock), but we should attach them to `self.favorites_widget` instead. We also need to remove the explicit focus checks in the favorites handlers. This means we have to provide a complete replacement, using the provided original code. It's going to be a large chunk, but I think I can do it!

**Modifying the favorites shortcuts**

I see that the user is asking for an updated version of the code. The main changes involve modifying the `init_shortcuts` method to attach favorites shortcuts directly to `self.favorites_widget` instead of the Dock, and removing the explicit focus checks for those favorites handlers.

I’ll replace the relevant section in the `init_shortcuts` block and in each of the handler methods. This will streamline things by removing unnecessary focus checks!

I'll now work on implementing these updates throughout the full code provided.



## Message 15

Below is the full updated code. In this version, the favorites shortcuts are attached directly to the favorites widget (instead of the Dock), and the explicit focus checks in the favorites shortcut handlers have been removed. You can replace your existing code with the following in-place replacement:

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # These keybindings appear in the context menus.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        # Set the Favorites widget to receive focus
        self.favorites_widget.setFocusPolicy(Qt.StrongFocus)
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        # Global shortcuts for the Dock:
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)
        # Shortcuts for the Scripts (tree) widget:
        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
        explorer_sc.activated.connect(self.handle_explorer_shortcut)
        file_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.tree_widget)
        file_open_sc.activated.connect(self.open_selected_script)
        expand_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+E"), self.tree_widget)
        expand_sc.activated.connect(self.expand_selected)
        collapse_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+C"), self.tree_widget)
        collapse_sc.activated.connect(self.collapse_selected)
        # Attach favorites shortcuts directly to the favorites widget:
        fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
        fav_run_sc.activated.connect(self.handle_fav_run)
        fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.favorites_widget)
        fav_open_sc.activated.connect(self.handle_fav_open)
        fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.favorites_widget)
        fav_explorer_sc.activated.connect(self.handle_fav_explorer)
        fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self.favorites_widget)
        fav_unpin_sc.activated.connect(self.handle_fav_unpin)

    # --- Helper methods for the Scripts widget ---
    def handle_explorer_shortcut(self):
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.script_path:
                    if item.is_folder:
                        self.open_in_explorer(item.script_path)
                    else:
                        folder = os.path.dirname(item.script_path)
                        self.open_in_explorer(folder)
                    break

    def open_selected_script(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.open_script_file(it.script_path)

    def expand_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.expand_all(item)

    def collapse_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.collapse_all(item)

    # --- Helper methods for the Favorites widget ---
    def handle_fav_run(self):
        self.run_selected_favorites()

    def handle_fav_open(self):
        self.open_selected_favorite_script()

    def handle_fav_explorer(self):
        self.explorer_selected_favorites()

    def handle_fav_unpin(self):
        self.unpin_selected()

    def run_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            self.run_script_from_favorites(item)

    def open_selected_favorite_script(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                self.open_script_file(p)

    def explorer_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                folder = os.path.dirname(p)
                self.open_in_explorer(folder)
                break

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return
        if w == self.favorites_widget:
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']
        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus():
            self.run_selected()
        elif self.favorites_widget.hasFocus():
            self.run_selected_favorites()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:
        scripts_dir = rt.getDir(rt.Name('userMacros'))
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation of the Changes

1. **Favorites Shortcuts Updated:**
   - In the `init_shortcuts` method, the favorites shortcuts are now created with `self.favorites_widget` as their parent:
     ```python
     fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
     fav_run_sc.activated.connect(self.handle_fav_run)
     ```
     This ensures they will trigger when the Favorites widget (or one of its child items) has focus.

2. **Removed Redundant Focus Checks:**
   - In the favorites handler functions (`handle_fav_run`, `handle_fav_open`, etc.), the explicit checks for focus have been removed so that the handlers execute immediately when the corresponding shortcut is pressed.

This updated version retains the existing functionality for both the Scripts and Favorites widgets while fixing the keybinding issue for the Favorites widget.

## Message 16

**Making specific modifications**

I'm working through updating the script by attaching favorites widget shortcuts to `self.favorites_widget` in the `init_shortcuts` method and removing focus checks from `handle_fav_run`, `handle_fav_open`, `handle_fav_explorer`, and `handle_fav_unpin`.

**Preserving existing functionality**

I'm thinking about replicating the entire script while incorporating the requested changes, ensuring all original functions remain intact.

**Adjusting shortcut attachments**

I’m updating the script to link the favorites widget shortcuts directly to `self.favorites_widget` instead of the Dock, while removing focus checks from the `handle_fav_*` methods.

## Message 17

Below is the **complete updated script** showing an **in-place replacement** of the favorites shortcuts so that they actually work when the Favorites list is focused. The only changes from your original code are:

1. **In `init_shortcuts()`:** The favorites shortcuts (`Return`, `Ctrl+Shift+O`, `Ctrl+Shift+E`, `Delete`) are now parented to `self.favorites_widget` rather than `self`.  
2. **In the four “handle_fav_*” methods:** The focus checks (`if self.favorites_widget.hasFocus():`) have been removed so the code runs immediately when those shortcuts fire (since they now only fire when the Favorites widget is focused anyway).

Everything else remains the same to preserve existing functionality.

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # These keybindings appear in the context menus.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        # Set the Favorites widget to receive focus
        self.favorites_widget.setFocusPolicy(Qt.StrongFocus)
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        # Global shortcuts for the Dock (applicable to the Scripts tree):
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)

        # Shortcuts specifically for the Scripts widget
        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
        explorer_sc.activated.connect(self.handle_explorer_shortcut)
        file_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.tree_widget)
        file_open_sc.activated.connect(self.open_selected_script)
        expand_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+E"), self.tree_widget)
        expand_sc.activated.connect(self.expand_selected)
        collapse_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+C"), self.tree_widget)
        collapse_sc.activated.connect(self.collapse_selected)

        # --- UPDATED: Favorites shortcuts attached to self.favorites_widget ---
        fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
        fav_run_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_run_sc.activated.connect(self.handle_fav_run)

        fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.favorites_widget)
        fav_open_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_open_sc.activated.connect(self.handle_fav_open)

        fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.favorites_widget)
        fav_explorer_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_explorer_sc.activated.connect(self.handle_fav_explorer)

        fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self.favorites_widget)
        fav_unpin_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_unpin_sc.activated.connect(self.handle_fav_unpin)

    # --- Helper methods for the Scripts widget ---
    def handle_explorer_shortcut(self):
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.script_path:
                    if item.is_folder:
                        self.open_in_explorer(item.script_path)
                    else:
                        folder = os.path.dirname(item.script_path)
                        self.open_in_explorer(folder)
                    break

    def open_selected_script(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.open_script_file(it.script_path)

    def expand_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.expand_all(item)

    def collapse_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.collapse_all(item)

    # --- Helper methods for the Favorites widget (via the Dock) ---
    def handle_fav_run(self):
        # Directly run selected favorites (no need to check focus now)
        self.run_selected_favorites()

    def handle_fav_open(self):
        self.open_selected_favorite_script()

    def handle_fav_explorer(self):
        self.explorer_selected_favorites()

    def handle_fav_unpin(self):
        self.unpin_selected()

    def run_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            self.run_script_from_favorites(item)

    def open_selected_favorite_script(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                self.open_script_file(p)

    def explorer_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                folder = os.path.dirname(p)
                self.open_in_explorer(folder)
                break

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return
        if w == self.favorites_widget:
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']
        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus():
            self.run_selected()
        elif self.favorites_widget.hasFocus():
            self.run_selected_favorites()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:
        scripts_dir = rt.getDir(rt.Name('userMacros'))
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation of the Fix

1. **Shortcuts for Favorites Are Now Attached to `self.favorites_widget`:**  
   ```python
   fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
   fav_run_sc.setContext(Qt.WidgetWithChildrenShortcut)
   fav_run_sc.activated.connect(self.handle_fav_run)
   ...
   ```
   This ensures that these shortcuts become active when the Favorites list widget (or one of its child widgets) has focus.

2. **Removed Redundant Focus Checks:**  
   Since the shortcut is only active when the Favorites widget is focused, we don’t need to check `if self.favorites_widget.hasFocus():` anymore.  
   ```python
   def handle_fav_run(self):
       self.run_selected_favorites()
   ```
   If a user presses `Return` while an item in the Favorites list is selected, the script is run immediately.

With these changes, the Favorites shortcuts work as intended, retaining all other functionality from your original script.

## Message 18

none of the keybindings work for Favorites, could this be because they're occupied elsewhere in the code?

## Message 19

**Checking for keybinding conflicts**

The user is wondering if the keybindings for Favorites aren't working because they might be occupied elsewhere in the code. It's a valid possibility — they could be conflicting with global shortcuts or those defined for the script widget.

From what I see in the code, the `Return` key is used globally in the Dock shortcuts, and then there's another `Return` keybinding for the favorites widget. That could be a conflict! Similar issues might happen with other favorites-specific shortcuts like `Ctrl+Shift+O`, or `Delete`.

**Investigating shortcut conflicts**

The user mentioned that the favorites shortcuts might be conflicting with global ones. In Qt, if two shortcuts share the same key sequence, focus matters: the highest-priority shortcut fires. It looks like the favorites shortcuts are in direct control when the favorites widget has focus, but global shortcuts may still trigger accidentally.

The issue could be with the context setting, as the global shortcut is attached to `self`, not `favorites_widget`. I'll need to make sure the contexts for both are set correctly. Qt says the child widget's shortcuts should take precedence if properly configured.

**Examining shortcut conflicts**

The issue might be that the Return key is bound to the favorites widget as well as globally to the Dock widget (`self`). Both shortcuts have the same context, and Qt could prioritize the one it deems more relevant. This could be why the favorites-specific shortcut isn’t triggering.

It seems both the favorites widget and the Dock widget are fighting for control of the Return key with the same context. The global Return action on the Dock could override the favorites action. I'll need to reconsider how the contexts are set to fix this.

## Message 20

Yes, it’s quite possible. In your code the same key sequences (for example, **Return** and **Delete**) are defined twice:

1. **Global shortcuts on the Dock widget (`self`):**  
   ```python
   enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
   enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
   enter_sc.activated.connect(self.run_selected_if_focused)
   ```
   and  
   ```python
   unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
   unpin_sc.activated.connect(self.unpin_selected)
   ```

2. **Favorites-specific shortcuts on the Favorites widget:**  
   ```python
   fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
   fav_run_sc.setContext(Qt.WidgetWithChildrenShortcut)
   fav_run_sc.activated.connect(self.handle_fav_run)
   ```
   and similarly for **Delete** (as well as **Ctrl+Shift+O** and **Ctrl+Shift+E**).

Because both the Dock (parent) and its child (the favorites widget) are using the same key sequences with a context of `Qt.WidgetWithChildrenShortcut`, the parent's shortcuts can “steal” the event before the favorites widget’s shortcuts ever see it. This is why none of the Favorites keybindings seem to work—they’re likely being occupied (or overridden) by the global shortcuts defined on the Dock widget.

### What Can You Do?

There are a few ways to address this:

- **Remove or change the conflicting global shortcuts:**  
  If the global **Return** (and **Delete**) shortcuts are intended only for the Scripts tree, you might attach those shortcuts only to that widget instead of the entire Dock. This will ensure that when the Favorites widget has focus, its own shortcuts aren’t preempted by the parent's shortcuts.

- **Change the shortcut context or use different key sequences for Favorites:**  
  For example, if you want to keep a global **Return** shortcut for the Dock, consider using a different key for Favorites (or adjust the shortcut context so that the favorites widget’s shortcuts have precedence when it has focus).

### In Summary

Yes—the Favorites shortcuts may not work because their key sequences (like **Return** and **Delete**) are already used by global shortcuts on the parent Dock widget (with a context that allows them to catch events from child widgets). This conflict prevents the Favorites widget’s shortcuts from triggering as intended.

## Message 21

what do we need to do in order to use the same keybindings for the different widgets? as an example, i would like ctrl+shift+e show in explorer regardless whether it's in favorites or in scripts

## Message 22

**Considering keybinding conflicts**

The user wants keybindings like Ctrl+Shift+E to work consistently across different widgets — in this case, both favorites and scripts. The keybinding conflict happens due to overlapping contexts. One solution would be adjusting the context priorities, allowing a global shortcut to handle Ctrl+Shift+E for both widgets. Alternatively, we could remove conflicting keybindings from the parent widget, but there's also the complexity of a dock widget containing both a favorites and a scripts widget, where each has its own context menu.

**Solving global keybinding issue**

The user wants Ctrl+Shift+E to trigger the "show in explorer" function, whether the favorites or scripts widget is focused. One solution is to create a global shortcut handled by the Dock or main window. Then, the handler checks which widget is focused — if it's the favorites widget, one function is executed; if it's the scripts widget, another function is triggered. Alternatively, using an event filter for the key press events could also manage this functionality.

**Simplifying keybinding handling**

To solve the keybinding issue, we can define a single global shortcut at the Dock level using `Qt.ApplicationShortcut`, which would work regardless of which widget is focused. In the shortcut's callback, we'll check which widget is currently in focus — if the favorites widget is focused, call the function for the favorites explorer; if the tree widget is focused, call the function for the tree explorer. This way, we avoid conflicts and ensure Ctrl+Shift+E always works for both widgets as intended.

## Message 23

Below is one complete updated version of the code. In this version, we remove the separate explorer shortcuts attached to the scripts and favorites widgets and instead define a single global shortcut for **Ctrl+Shift+E** on the Dock widget. That shortcut uses a global context (using `Qt.ApplicationShortcut`) so that it’s always active regardless of which widget is focused. In the callback, we check which widget currently has focus and then call the appropriate “show in explorer” routine.

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # These keybindings appear in the context menus.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        self.favorites_widget.setFocusPolicy(Qt.StrongFocus)
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        # Global shortcuts for the Dock (for Scripts tree actions)
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)
        file_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.tree_widget)
        file_open_sc.activated.connect(self.open_selected_script)
        expand_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+E"), self.tree_widget)
        expand_sc.activated.connect(self.expand_selected)
        collapse_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+C"), self.tree_widget)
        collapse_sc.activated.connect(self.collapse_selected)

        # --- Global Explorer Shortcut for Both Widgets ---
        global_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self)
        global_explorer_sc.setContext(Qt.ApplicationShortcut)
        global_explorer_sc.activated.connect(self.handle_global_explorer)

        # The following favorites shortcuts have been removed to avoid conflicts:
        # Instead of attaching separate shortcuts to the favorites widget,
        # the global shortcut above will handle explorer for both widgets.

        # Similarly, if you want unified behavior for other keys,
        # you can add global shortcuts and check the focus within their handlers.

    # --- Global shortcut handler for Explorer ---
    def handle_global_explorer(self):
        # Check which widget is focused and call the appropriate explorer action.
        if self.favorites_widget.hasFocus():
            self.explorer_selected_favorites()
        elif self.tree_widget.hasFocus():
            self.handle_explorer_shortcut()
        else:
            # Default to tree widget if no specific focus is found.
            self.handle_explorer_shortcut()

    # --- Helper methods for the Scripts widget ---
    def handle_explorer_shortcut(self):
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.script_path:
                    if item.is_folder:
                        self.open_in_explorer(item.script_path)
                    else:
                        folder = os.path.dirname(item.script_path)
                        self.open_in_explorer(folder)
                    break

    def open_selected_script(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.open_script_file(it.script_path)

    def expand_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.expand_all(item)

    def collapse_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.collapse_all(item)

    # --- Helper methods for the Favorites widget ---
    def run_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            self.run_script_from_favorites(item)

    def open_selected_favorite_script(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                self.open_script_file(p)

    def explorer_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                folder = os.path.dirname(p)
                self.open_in_explorer(folder)
                break

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return
        if w == self.favorites_widget:
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']
        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus():
            self.run_selected()
        elif self.favorites_widget.hasFocus():
            self.run_selected_favorites()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:
        scripts_dir = rt.getDir(rt.Name('userMacros'))
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation

1. **Global Explorer Shortcut:**  
   In `init_shortcuts()`, the two separate explorer shortcuts (one for the tree widget and one for the favorites widget) have been removed. Instead, we add a single global shortcut for **Ctrl+Shift+E** on the Dock widget:
   ```python
   global_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self)
   global_explorer_sc.setContext(Qt.ApplicationShortcut)
   global_explorer_sc.activated.connect(self.handle_global_explorer)
   ```
   Setting the context to `Qt.ApplicationShortcut` ensures that this shortcut is active regardless of which child widget has focus.

2. **Unified Handler:**  
   The new method `handle_global_explorer()` checks which widget is focused and calls either `explorer_selected_favorites()` (if the favorites widget is focused) or `handle_explorer_shortcut()` (if the scripts tree is focused). This lets you use the same keybinding for both.

3. **Maintaining Existing Functionality:**  
   All other shortcuts and functions remain unchanged so that your overall workflow is preserved.

With these changes, pressing **Ctrl+Shift+E** will show in Explorer whether you’re in the Favorites widget or the Scripts tree.
