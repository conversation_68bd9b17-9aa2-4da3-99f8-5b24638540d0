# Data Sync Solutions Windows NAS

Conversation ID: 68496af5-8514-8008-90a1-71e6dcc3ade7

## Message 1

What are the best and most well-suited alternatives to synchronize data between two Windows computers and a NAS on a local network, considering one is wired and one is wireless, and the solution should be simple, robust, not a 'black box', and able to handle features like propagating deletions better than Robocopy or Syncthing?

## Message 2

**Evaluating data synchronization**

I’m gathering info on alternatives like FreeFileSync, Unison, Bvckup 2, SyncBack, Resilio Sync, CsSync, and rclone. Ensuring they meet needs for one-way or two-way sync, deletion propagation, and network condition handling.

## Message 3

**In a nutshell:**  
For a transparent, deletion‑aware, LAN‑only workflow your best bets are **Unison**, **FreeFileSync + RealTimeSync**, and a **scripted rsync port (cwRsync/WSL or DeltaCopy)**. Each is open source or at least fully inspectable, logs every action, and handles two‑way deletion propagation better than Robocopy or the default Syncthing behaviour. If you already run a “server‑class” edition of Windows, *DFS‑R* is a rock‑solid option baked into the OS. GoodSync (commercial) and Resilio Sync (closed‑source) are also capable, but they are less aligned with your “not a black box” requirement. The table and sections below compare the trade‑offs and suggest concrete deployment patterns for one wired PC, one Wi‑Fi PC, and a NAS.

---

## 1  Why Robocopy & Syncthing struggle here
* **Robocopy** only mirrors deletions in one direction (`/MIR`) and cannot resolve simultaneous edits without custom scripting; logs are good, but it is fundamentally a *backup* tool, not true bidirectional sync.citeturn0search2  
* **Syncthing** excels at real‑time peer‑to‑peer sync, yet deletion handling relies on “tombstones” that expire if a device is offline too long, potentially resurrecting files when the wireless PC reconnects.citeturn0search12  

---

## 2  Selection criteria
1. **Transparency & Auditability** – clear logs, human‑readable configuration, open protocol/format.  
2. **Bidirectional with Deletion Propagation** – must push *adds, edits and deletes* in both directions.  
3. **Local‑network Friendly** – works over SMB or raw TCP; no cloud relay.  
4. **Ease of Use** – simple scheduling or background service; minimal CLI plumbing.  
5. **Performance on mixed wired/WLAN links** – resume‑capable, delta transfer, bandwidth caps.

---

## 3  Tool‑by‑tool evaluation

### 3.1  Unison
* **What it is** – a cross‑platform OCaml tool that keeps two “replicas” identical and asks you only when there is a genuine conflict.citeturn0search0turn0search7  
* **Strengths** –  
  * Two‑way sync with explicit deletion tracking – nothing is lost silently.  
  * Works over direct UNC paths or SSH; no background daemon, so it is completely visible.  
  * Runs fine on Windows (native binaries or WSL).  
* **Caveats** – manual or Task‑Scheduler driven; GUI is basic; first scan can be slow on very large trees.  
* **Community evidence** – many users moved *back* to Unison after frustrations with Syncthing tombstones.citeturn0search5  

### 3.2  FreeFileSync + RealTimeSync
* **What it is** – an open‑source GUI that can run in *Mirror* (one‑way) or *Two‑way* modes, plus a lightweight service that watches for changes.citeturn1search4  
* **Deletion propagation** – enabled by default in Two‑way; Mirror mode gives Robocopy‑style `/MIR` behaviour.citeturn1search1  
* **Logging & rollback** – keeps an SQLite DB and can version deleted items; forum posts show deliberate design to avoid “ghost re‑copy” issues.citeturn0search1  
* **Caveats** – watch service uses Windows file‑system notifications; over flaky Wi‑Fi you may prefer timed batch runs.

### 3.3  Rsync variants
