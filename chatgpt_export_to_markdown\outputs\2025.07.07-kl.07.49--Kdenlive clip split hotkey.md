# Kdenlive clip split hotkey

Conversation ID: 686b5fe4-a2f0-8008-a820-4870014267e4

## Message 1

what's the hotkey for splitting a clip on the current marker in kdenlive?

## Message 2

The default keyboard shortcut in Kdenlive to split the clip at the playhead (or at a marker on the clip) is:

### ✂️ **Shift + R**  
This cuts the **selected clip** exactly where your playhead (current marker) is positioned citeturn0search2turn0search4.

You can also access this function via the menu path:

```
Timeline → Current Clip → Cut Clip
```

Or by right-clicking on the clip and selecting **Cut Clip** from the context menu citeturn0search2turn0search9.

---

### Quick Tips

- **Ensure the clip is selected** (click on it) before pressing Shift + R, otherwise the action won't apply.
- For more general cuts (all tracks at once), use **Ctrl + Shift + R**, which cuts every clip under the playhead citeturn0search0turn0search2.
- To slice using the Razor Tool manually, press **X**, click where you want to cut—but remember Shift + R is faster and more precise.

---

So whenever your playhead aligns with a marker and the clip is selected, just hit **Shift + R** to cut it right at that point. Let me know if you'd like help customizing shortcuts or automating batch splits!

## Message 3

after splitting a clip using shift+r, what is the hotkeys for setting start/end to selected clip and export it?

## Message 4

Here are the default keyboard shortcuts in **Kdenlive** for setting the start/end of a clip and exporting your project:

---

## 🎯 Setting In/Out for a Selected Clip

- **I**: Set **IN** point (start of zone) at the playhead  
- **O**: Set **OUT** point (end of zone) at the playhead citeturn0search5turn0search9  

Use these after pressing **Shift + R** (to cut) if you want to define a specific region.

---

## 🚀 Exporting (Rendering)

- **Ctrl + Enter** (or **Ctrl + Return**): Open the **Render** dialog citeturn0search2turn0search9  

Once you have your in/out points set (I/O), you can:

1. Press **Ctrl + Enter** to open the dialog.
2. Choose **Selected zone** if you only want to export that section.
3. Click **Render to File** (or press Enter again, depending on your configuration).

---

## ✅ Summary

| Task                       | Hotkey         |
|----------------------------|----------------|
| Set In Point (start zone)  | **I**          |
| Set Out Point (end zone)   | **O**          |
| Open Render Dialog         | **Ctrl + Enter** |

Let me know if you'd like help customizing these shortcuts or automating exports!
