# Codebase Organization Framework

Conversation ID: 68010301-513c-8008-9b3e-497365564686

## Message 1

Prepare yourself for creating a *new* sequence that is designed for applying these concepts into *Codebase Familiarization* and *Codebase-Cleanup* by the following principles:



    ```

    ## General Principles

    - Aim for simplicity, clarity, and maintainability in all project aspects.

    - Favor *cohesive* design over *generic* inheritance.

    - Prioritize inherent readability and understandability for future developers.

    - Ensure all components have a single responsibility.

    - Transparency and Minimalism: Reducing visual clutter for clarity and efficiency

    - Highly refined project structures: Assess inherent readability based on identifier naming quality, logical flow simplicity, and overall structural self-explanation.

    - Optimizing both the architecture and user experience.

    -



    ## Code Organization

    - Evaluate the existing codebase structure and identify patterns and anti-patterns.

    - Consolidate related functionality into cohesive modules.

    - Minimize dependencies between unrelated components.

    - Optimize for developer ergonomics and intuitive navigation.

    - Balance file granularity with overall system comprehensibility.

    - Identify sections reliant on comments that *could* potentially be clarified through refactoring (naming, structure)



    ## Important Considerations

    - How the file structure could more inherently embody the concepts it represents (e.g. dynamic hierarchical organization that corresponds with the natural order of operations.

    - How directory and file naming can reflect the sequence of operations.

    - The importance of clear boundaries between workflow stages.

    - How the structure can guide new developers in understanding the system.

    ```



---



Familiarize yourself with the structure of these examples:



    ```

    1.  **[Foundational Analysis & Principle Mapping]**

        * **Objective:** Radically immerse in the codebase and project structure to deeply understand its current state: logic flow, component interactions, dependencies, structural patterns (or anti-patterns), and existing comment strategies. Map findings against the core principles (Simplicity, Clarity, Cohesion, SRP, Minimalism, Self-Explanation).

        * **Execute as:** `{role=foundational_analyzer; input=codebase_root:path; process=[map_project_structure_and_dependencies(), trace_core_logic_flows(), analyze_component_cohesion_and_responsibility(), assess_naming_convention_clarity(), audit_comment_usage_vs_necessity(), identify_principle_violations_and_complexity_hotspots()]; output={analysis_summary:dict(structure_map:any, dependency_issues:list, logic_complexity_zones:list, naming_concerns:list, comment_assessment:dict, principle_violations:list)}}`



    2.  **[Strategic Simplification Blueprint]**

        * **Objective:** Formulate a precise, prioritized, actionable plan based on the analysis to refactor the codebase towards elegant simplicity and structural self-explanation. Define clear targets for structural reorganization, logic simplification, naming refinement, and comment minimization, ensuring the plan preserves original functionality.

        * **Execute as:** `{role=simplification_strategist; input=analysis_summary:dict; process=[prioritize_refactoring_targets_by_impact_on_clarity(), devise_minimalist_structural_reorganization_plan(), specify_logic_consolidation_and_simplification_steps(), define_clear_and_consistent_naming_improvements(), establish_comment_refinement_strategy(policy='minimal_concise_essential'), map_actions_to_functional_constraints()]; output={cleanup_blueprint:list_of_prioritized_tasks}}`



    3.  **[Structural Elegance Realignment]**

        * **Objective:** Execute the structural reorganization part of the blueprint. Refactor the directory and file structure to inherently mirror the conceptual organization and logical flow of the system. Consolidate cohesive elements, decouple unrelated ones, and apply clear, sequential naming to structural components.

        * **Execute as:** `{role=structure_realigner; input={codebase_root:path, cleanup_blueprint:list}; process=[implement_directory_file_restructuring(), group_related_functionality_cohesively(), minimize_cross_component_dependencies(), apply_structure_naming_conventions(), update_references_and_imports()]; output={realigned_codebase_structure:path}}`



    4.  **[Intrinsic Code Clarification & Simplification]**

        * **Objective:** Execute the code-level refactoring defined in the blueprint. Simplify algorithms, consolidate redundant logic, strictly enforce the Single Responsibility Principle, and refactor names (variables, functions, classes) for maximum clarity and self-explanation within the newly aligned structure.

        * **Execute as:** `{role=code_simplifier; input={realigned_codebase_structure:path, cleanup_blueprint:list}; process=[apply_self_explanatory_naming_conventions(), implement_logic_simplification_and_consolidation(), refactor_components_for_single_responsibility(), ensure_clear_logical_flow_within_files(), remove_dead_or_unreachable_code()]; output={clarified_codebase:path}}`



    5.  **[Essential Comment Rationalization]**

        * **Objective:** Rigorously apply the 'minimal_concise_essential' comment policy. Remove comments that merely restate the code or are made obsolete by structural and naming improvements. Refine remaining necessary comments to be extremely concise, adding only vital context or explaining unavoidable complexities.

        * **Execute as:** `{role=comment_rationalizer; input={clarified_codebase:path, comment_policy:str}; process=[identify_comments_redundant_with_code_clarity(), purge_obsolete_or_unnecessary_comments(), refine_essential_comments_for_brevity_and_precision(), ensure_comments_supplement_not_supplant_understanding()]; output={comment_rationalized_codebase:path}}`



    6.  **[Holistic Validation & Integrity Check]**

        * **Objective:** Verify that the refactored codebase is 100% functionally equivalent to the original. Critically assess the final state against all target principles: Is it simpler? Is it clearer? Is the structure self-explanatory? Are comments minimal and essential? Perform a final consistency and polish pass.

        * **Execute as:** `{role=integrity_validator; input={original_codebase_state:any, comment_rationalized_codebase:path}; process=[perform_functional_equivalence_verification(), audit_final_state_against_core_principles(), check_for_consistent_style_and_formatting(), conduct_final_readability_and_maintainability_review()]; output={validated_codebase:path, validation_report:dict(functional_equivalence:bool, principle_adherence_score:float, remaining_concerns:list)}}`



    7.  **[Recursive Refinement Assessment]**

        * **Objective:** Evaluate the `validation_report` and the `validated_codebase` to determine if further cleanup cycles are warranted. Identify any remaining areas where principles could be more deeply applied or where complexity persists, potentially initiating a new cycle starting from Step 1 with a more focused scope.

        * **Execute as:** `{role=refinement_assessor; input={validated_codebase:path, validation_report:dict}; process=[analyze_remaining_concerns_and_complexity(), assess_potential_for_further_simplification(), evaluate_cost_benefit_of_another_cycle(), determine_necessity_and_scope_for_next_iteration()]; output={next_cycle_decision:dict(proceed:bool, focus_areas:list)}}`

    ```



---



Please consolidate and write a new sequence of instructions titled `Generalized Codebase Cleanup`, *specifically* for designed for inherently reusable generalized autonomous codebase cleanup (including the projectstructure itself) - this sequence should aim to *simplify* rather than *complicate*. Rather than unneccessary complexity/verbosity, choose *brilliantly designed elegance (through inherent simplicity)*.  Use the provided examples for reference/inspiration to craft an new sequence that is universally generalizable and expressly designed for codebase cleanup. Remember, each step in the sequence should build chronologically-recursively (for escalating value), adhering to all constraints, and relentlessly targeting maximum clarity, precision, yield, and cross-domain self-sufficiency.



---



Your goal is to provide the full sequence as instructed (the sequence should be minimum 5 steps and maximum 10 steps), make sure the sequence is maximally optimized and enhanced based on the provided info (notes/examples/directive) inherently provided within this message. Provide the instructions in this kind of format (example):



    #### `src/templates/lvl1/md/0126-a-holistic-structure-purpose-scan.md`



    ```markdown

    [Holistic Structure & Purpose Scan] Your primary directive is rapid, holistic reconnaissance: Analyze the input codebase/project structure. Inventory key components (files, modules, classes), map high-level dependencies, and distill the fundamental purpose or core value proposition of the system, establishing a baseline understanding. Execute as `{role=holistic_scanner; input=codebase_root:any; process=[inventory_key_components(), map_major_dependencies(), analyze_entry_points_and_outputs(), distill_primary_purpose()]; output={structure_overview:dict, primary_purpose:str}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-b-core-workflow-logic-path-tracing.md`



    ```markdown

    [Core Workflow & Logic Path Tracing] Trace the primary execution or data processing workflows essential to the `primary_purpose`. Map how control and data flow between the key components identified in `structure_overview`, highlighting critical paths, interactions, and potential complexity hotspots. Execute as `{role=workflow_tracer; input={structure_overview:dict, primary_purpose:str, codebase_content:dict}; process=[identify_key_workflows(), trace_control_flow_for_workflows(), map_data_transformations_across_components(), visualize_core_logic_paths()]; output={workflow_maps:list[dict]}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-c-clarity-self-explanation-audit.md`



    ```markdown

    [Clarity & Self-Explanation Audit] Evaluate the codebase against principles of 'Maximal Clarity & Conciseness'. Assess inherent readability based on identifier naming quality, logical flow simplicity within `workflow_maps`, and overall structural self-explanation. Identify areas where clarity currently relies heavily on commentary rather than transparent code design. Execute as `{role=clarity_auditor; input={codebase_content:dict, workflow_maps:list}; process=[evaluate_identifier_naming_effectiveness(), assess_logical_flow_simplicity_in_workflows(), determine_code_self_explanation_level(), pinpoint_comment_dependent_clarity_zones()]; output={clarity_report:dict(naming_score:float, flow_score:float, self_explanation_notes:str)}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-d-essential-commentary-isolation.md`



    ```markdown

    [Essential Commentary Isolation] Rigorously apply the 'Essential Commentary Only' principle. Analyze all existing comments. Isolate and preserve *only* those comments providing critical explanations of non-obvious logic/intent ('why') or vital architectural guidance. Flag all other comments (redundant, obvious 'what'/'how', verbose, outdated, misplaced interface docs) for removal. Execute as `{role=essential_comment_filter; input=codebase_content:dict; process=[inventory_all_comments(), classify_against_essential_only_criteria(focus_on_why), flag_non_essential_comments_for_removal(), audit_docstring_usage_for_interfaces()]; output={comment_cleanup_candidates:list[dict(location:str, reason:str)]}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-e-simplification-cohesion-opportunity-scan.md`



    ```markdown

    [Simplification & Cohesion Opportunity Scan] Scan the codebase, informed by `clarity_report` and `workflow_maps`, for opportunities aligning with 'Optimized Implementation' and 'Structural Purity'. Identify overly complex logic, redundancy (dead code, duplication), poor cohesion (modules/classes needing regrouping), or unclear interfaces ripe for simplification and structural refinement. Execute as `{role=simplification_scanner; input={codebase_content:dict, clarity_report:dict, workflow_maps:list}; process=[detect_complex_logic_hotspots(), identify_redundant_dead_code_patterns(), assess_module_class_cohesion(), pinpoint_unclear_interfaces(), list_simplification_refinement_opportunities()]; output={cleanup_opportunities:dict(simplification:list, structure:list, dead_code:list)}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-f-prioritized-cleanup-action-plan-generation.md`



    ```markdown

    [Prioritized Cleanup Action Plan Generation] Synthesize all findings (`comment_cleanup_candidates`, `cleanup_opportunities`, potential clarity improvements from `clarity_report`). Generate a prioritized, actionable cleanup plan focusing on safety and impact. Prioritize removing flagged comments/dead code, followed by high-value/low-risk refactoring for clarity (naming, simple structure) and simplification. *Output the plan itself, not modified code.* Execute as `{role=cleanup_strategist_planner; input=all_previous_outputs:dict; process=[consolidate_all_cleanup_candidates(), rank_actions_by_safety_then_impact(), prioritize_removals_and_clarifications(), define_specific_actionable_steps(), structure_as_implementation_plan()]; output={actionable_cleanup_plan:str}}`

    ```



    ---



    #### `src/templates/lvl1/md/0126-g-cleanup-plan-rationale-outcome-summary.md`



    ```markdown

    [Cleanup Plan Rationale & Outcome Summary] Provide a concise summary report for the `actionable_cleanup_plan`. Articulate the rationale, emphasizing the targeted improvements based on the core principles (clarity, simplicity, essential commentary, structure). Clearly state the expected benefits in maintainability, readability, reduced complexity, and overall codebase elegance. Execute as `{role=cleanup_reporter; input={actionable_cleanup_plan:str}; process=[summarize_plan_rationale_based_on_principles(), articulate_prioritization_logic(safety_impact), detail_expected_benefits_post_cleanup(), finalize_summary_report()]; output={cleanup_plan_report:str}}`

    ```


## Message 2

Designing a streamlined system

Crafting a streamlined process

Piecing together a structured sequence.

Simplifying processes with a clear sequence.

Mapping the sequence clearly, ensuring consistency and chronological progression. The steps are organized to emphasize clarity.

## Message 3

#### `src/templates/lvl1/md/1001-a-holistic-structure-purpose-scan.md`

```markdown
[Holistic Structure & Purpose Scan] Your primary objective is a swift, big-picture analysis of the codebase: inventory the major components, outline their purpose, and note key dependencies. This sets a baseline for understanding what the system does and how it’s organized.  
Execute as `{role=holistic_scanner; input=codebase_root:any; process=[inventory_major_components(), identify_primary_use_cases(), outline_top_level_dependencies(), articulate_overall_project_mission()]; output={structure_overview:dict, project_mission:str}}`
```

---

#### `src/templates/lvl1/md/1001-b-key-workflow-complexity-trace.md`

```markdown
[Key Workflow & Complexity Trace] Focus on tracing the system’s primary workflows relevant to the `project_mission`. Map the control paths, data transformations, and cross-module interactions that define these workflows. Identify complexity hotspots where logic or structure is convoluted.  
Execute as `{role=workflow_tracer; input={structure_overview:dict, project_mission:str, codebase_content:dict}; process=[extract_primary_workflows(), trace_control_and_data_flow(), locate_logic_complexity_hotspots(), summarize_cross_module_interactions()]; output={workflow_maps:list[dict], complexity_zones:list}}`
```

---

#### `src/templates/lvl1/md/1001-c-clarity-and-cohesion-audit.md`

```markdown
[Clarity & Cohesion Audit] Assess the codebase for readability and organizational strength. Evaluate naming quality, logical flow, adherence to Single Responsibility Principle, and cohesion within modules/classes, paying special attention to places flagged by `complexity_zones`.  
Execute as `{role=clarity_auditor; input={codebase_content:dict, workflow_maps:list, complexity_zones:list}; process=[examine_identifier_naming(), measure_cohesion_per_module_class(), check_single_responsibility_adherence(), document_comment_reliance_for_explanation()]; output={clarity_report:dict, cohesion_findings:list, srp_violations:list, comment_reliance:list}}`
```

---

#### `src/templates/lvl1/md/1001-d-focused-simplification-structural-realignment.md`

```markdown
[Focused Simplification & Structural Realignment] Use the insights from the `clarity_report` and `cohesion_findings` to propose minimal yet high-impact restructuring and refactoring. Target removing obvious redundancies, consolidating related functionality, and reducing external dependencies.  
Execute as `{role=structure_refiner; input={clarity_report:dict, cohesion_findings:list, srp_violations:list, codebase_content:dict}; process=[identify_redundant_or_overlapping_functionality(), plan_cohesive_module_realignment(), refine_dependency_boundaries(), propose_naming_improvements()]; output={simplification_plan:dict, proposed_structure_changes:list}}`
```

---

#### `src/templates/lvl1/md/1001-e-essential-commentary-refinement.md`

```markdown
[Essential Commentary Refinement] Apply a “minimal, essential-only” policy to all comments. Retain only comments that clarify intent or justify complex decisions. Remove or reduce redundant, outdated, or purely descriptive commentary that can be explained by improved naming/structure.  
Execute as `{role=comment_curator; input={codebase_content:dict}; process=[gather_all_comments(), filter_non_essential_comments(), preserve_comments_explaining_complex_design_intent(), create_update_list_for_comment_refinements()]; output={comment_cleanup_list:list, refined_comment_guidelines:str}}`
```

---

#### `src/templates/lvl1/md/1001-f-prioritized-cleanup-action-plan.md`

```markdown
[Prioritized Cleanup Action Plan] Integrate the findings from the `simplification_plan` and `comment_cleanup_list` into a cohesive roadmap. Assign priorities based on impact and safety. Outline actionable steps to restructure modules, refactor code, rename identifiers, and adjust comments.  
Execute as `{role=cleanup_planner; input={simplification_plan:dict, proposed_structure_changes:list, comment_cleanup_list:list, refined_comment_guidelines:str}; process=[merge_restructuring_and_comment_refinements(), rank_changes_by_safety_and_benefit(), draft_detailed_refactoring_tasks(), finalize_prioritized_plan()]; output={actionable_cleanup_plan:str}}`
```

---

#### `src/templates/lvl1/md/1001-g-validation-and-refinement-check.md`

```markdown
[Validation & Refinement Check] Validate that the executed refactoring plan preserves original functionality and meets the principles of clarity, maintainability, and minimalism. Evaluate if additional cleanup cycles are warranted or if the codebase has reached a sufficiently optimal state.  
Execute as `{role=validator; input={original_codebase:any, updated_codebase:any, actionable_cleanup_plan:str}; process=[verify_functional_equivalence(), review_code_against_core_principles(), gauge_remaining_complexity(), determine_need_for_additional_iteration()]; output={validation_report:dict, next_steps_recommendation:str}}`
```

---

**This “Generalized Codebase Cleanup” sequence provides a streamlined, cross-domain approach for incrementally scanning, auditing, simplifying, and validating any codebase. Each phase feeds the next, ensuring systematic transformation toward clarity, maintainability, and minimalism without losing core functionality.**

## Message 4



Go through and evaluate/consolidate the provided alternatives and create a new *maximally optimized and enhanced sequence* based on previously provided directives/parameters:



---



#### `1`



    #### `src/templates/lvl1/md/0130-a-cleanup-codebase-entry-mapping.md`



    ```markdown

    [Codebase Entry Mapping] Establish the groundwork by scanning the codebase root. Identify all top-level directories, files, modules, and their functional domains. Categorize elements by purpose (e.g., data, logic, interface, utilities). This step creates the spatial-intellectual map of the system—crucial for all following stages. Execute as `{role=entry_mapper; input=codebase_root:path; process=[inventory_top_level_structure(), classify_by_domain_role(), detect_architectural_patterns_or_lack_thereof(), summarize_codebase_footprint()]; output={structure_map:dict, domain_roles:list, footprint_overview:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-logical-path-and-dependency-trace.md`



    ```markdown

    [Logical Path & Dependency Trace] Trace the primary control and data flow paths within the system. Analyze function calls, module imports, and cross-component interactions. Build a visual or logical map of how data and control propagate from entry points to final outputs. Execute as `{role=dependency_tracer; input={structure_map:dict, codebase_content:any}; process=[identify_entry_points_and_exports(), map_internal_dependency_graph(), trace_data_and_control_flow(), highlight_critical_pathways_and_intersections()]; output={logic_paths:dict, dependency_map:dict, complexity_nodes:list}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-clarity-compression-and-redundancy-audit.md`



    ```markdown

    [Clarity Compression & Redundancy Audit] Examine whether the system communicates itself *through its form alone*. Analyze identifier clarity, interface cohesion, and the presence of duplicative or dead logic. Identify areas where structural clarity is replaced by excessive commentary or redundant constructs. Execute as `{role=clarity_auditor; input={logic_paths:dict, dependency_map:dict, codebase_content:any}; process=[evaluate_identifier_expressiveness(), detect_logic_duplication_and_dead_code(), audit_commentary_necessity_vs_clarity(), assess_self-explanation_ratio_of_components()]; output={clarity_deficits:list, redundancy_report:list, expressiveness_score:float}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-commentary-distillation-sweep.md`



    ```markdown

    [Commentary Distillation Sweep] Conduct a surgical review of all comments. Retain only those that convey critical intent or unavoidable abstraction. Remove all that redundantly describe code behavior or patch over poor naming/design. If your code needs narration—it probably needs a rewrite. Execute as `{role=commentary_surgeon; input=codebase_content:dict; process=[gather_all_comments(), categorize_by_intent_level(), purge_non-essential_comments(), isolate_purposeful_explanations()], output={preserved_comments:list, removed_comments:list}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-simplification-target-synthesis.md`



    ```markdown

    [Simplification Target Synthesis] Merge insights from clarity audit, dependency trace, and comment sweep to isolate high-value simplification zones. Prioritize based on a matrix of clarity gain, coupling reduction, and architectural alignment. Execute as `{role=simplification_targeter; input={clarity_deficits:list, dependency_map:dict, removed_comments:list}; process=[cross-reference_complexity_and_clarity_gaps(), prioritize_by_impact_vs_risk(), isolate_refactoring_zones(modules, naming, logic), define_simplification_targets()]; output={simplification_targets:list, rationale_matrix:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-pruned-structure-proposal.md`



    ```markdown

    [Pruned Structure Proposal] Formulate an optimized structural proposal. This includes renaming files/folders for narrative clarity, re-grouping modules by conceptual proximity, and flattening or re-hierarchizing directories to better reflect logical flow and intention. Execute as `{role=structure_pruner; input={structure_map:dict, simplification_targets:list}; process=[map_existing_structure_to_intention(), propose_directory_refactor_plan(), define_module_regroupings(), optimize_file_naming_and_ordering()], output={structure_proposal:dict(new_structure:list, rationales:list)}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-cleanup-execution-blueprint.md`



    ```markdown

    [Cleanup Execution Blueprint] Generate a step-by-step, safely sequenced action plan based on all previous analyses. Each action should be low-risk, logically isolated, and yield immediate clarity or simplification. Annotate dependencies and prerequisites. Execute as `{role=cleanup_architect; input={simplification_targets:list, structure_proposal:dict}; process=[decompose_refactor_tasks(), order_by_safety_and_impact(), annotate_side_effect_zones(), construct_sequenced_cleanup_steps()], output={refactor_plan:list_of_tasks}}`

    ```



    #### `src/templates/lvl1/md/0130-h-cleanup-refactor-pass-and-integrity-check.md`



    ```markdown

    [Refactor Pass & Integrity Check] Execute the `refactor_plan`. Validate that all changes preserve original functionality. Re-run workflows, unit tests, or manual inspection routines. Ensure naming, structure, and simplicity align with stated principles. Execute as `{role=refactor_executor; input={codebase_root:path, refactor_plan:list}; process=[apply_structural_and_code_changes(), verify_functional_equivalence(), run_test_suite_or_manual_flow_validation(), conduct_aesthetic_consistency_review()], output={refactored_codebase:path, integrity_passed:bool}}`

    ```



    #### `src/templates/lvl1/md/0130-i-cleanup-final-review-and-future-cycle-scope.md`



    ```markdown

    [Final Review & Future Cycle Scope] Critically review the final result. Document key simplifications, note remaining complexity zones, and evaluate whether a further cleanup cycle is warranted. Recommend next logical refinement scope, if needed. Execute as `{role=cleanup_reviewer; input={refactored_codebase:path, all_prior_outputs:dict}; process=[summarize_improvements(), assess_principle_alignment(), highlight_residual_complexity(), propose_scope_for_next_iteration()], output={review_summary:str, next_cycle_recommendation:dict}}`

    ```



---



#### `2`



    #### `src/templates/lvl1/md/0130-a-cleanup-structure-purpose-orientation-scan.md`



    ```markdown

    [Structure & Purpose Orientation Scan] Begin by scanning the full codebase structure to map its high-level architecture. Inventory core files, directories, and components. Identify entry points, output flows, and the system’s core intent. This baseline understanding will guide all future cleanup steps. Execute as `{role=structure_orienter; input=codebase_root:path; process=[map_directory_structure(), identify_entry_exit_points(), inventory_key_modules_classes_files(), distill_system_purpose_from_structure()]; output={structure_map:dict, core_purpose:str}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-workflow-and-flow-path-mapping.md`



    ```markdown

    [Workflow and Flow Path Mapping] Identify and trace essential workflows aligned with the `core_purpose`. Analyze how logic and data propagate through the system. Visualize key interactions, complexity hotspots, and dependency clusters that influence the execution logic. Execute as `{role=workflow_mapper; input={structure_map:dict, core_purpose:str}; process=[trace_primary_control_flows(), track_data_transformations(), detect_coupling_between_modules(), highlight_logic_clusters_and_hotspots()]; output={workflow_map:list[dict], complexity_zones:list}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-inherent-readability-and-self-explanation-audit.md`



    ```markdown

    [Inherent Readability & Self-Explanation Audit] Evaluate codebase clarity using identifier naming quality, structural simplicity, and logic transparency. Identify sections reliant on verbose comments or unclear flow that could be inherently clarified through better design. Execute as `{role=readability_auditor; input={workflow_map:list, complexity_zones:list}; process=[assess_naming_quality(), evaluate_logical_transparency(), detect_comment-dependent_clarity(), highlight_non-self-explanatory_areas()]; output={readability_report:dict(naming_issues:list, flow_issues:list, comment_reliance:list)}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-structural-and-functional-simplification-scan.md`



    ```markdown

    [Structural & Functional Simplification Scan] Identify all opportunities for removing redundancy, simplifying logic, enhancing cohesion, and minimizing inter-module dependencies. Target areas where structure and logic can be made more elegant without changing functionality. Execute as `{role=simplification_finder; input={structure_map:dict, readability_report:dict}; process=[identify_redundant_patterns(), locate_fragmented_or_non-cohesive_components(), detect_tightly_coupled_dependencies(), suggest_logic_simplification_targets()]; output={simplification_targets:dict(redundancy:list, cohesion:list, coupling:list, logic:list)}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-essential-comment-filter-and-minimizer.md`



    ```markdown

    [Essential Comment Filter & Minimizer] Apply a minimalist commentary policy. Audit all comments and retain only those that provide critical architectural or intent-related context. Remove or rewrite comments that merely describe 'what' or duplicate what’s already clear from code. Execute as `{role=comment_minimizer; input=codebase_root:path; process=[extract_all_comments(), classify_comments_by_necessity(), flag_redundant_and_excessive_comments(), preserve_or_refactor_essential_comments_only()]; output={comment_cleanup:list[dict(location:str, action:str)]}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-prioritized-cleanup-plan-synthesis.md`



    ```markdown

    [Prioritized Cleanup Plan Synthesis] Synthesize all outputs into a clear, incremental action plan. Prioritize simplifications with high impact and low risk. Include redundant code removals, structure refactors, naming improvements, and comment reductions. Ensure changes are safe, traceable, and purpose-aligned. Execute as `{role=cleanup_planner; input={simplification_targets:dict, comment_cleanup:list, readability_report:dict}; process=[merge_cleanup_opportunities(), prioritize_by_safety_and_clarity_gain(), translate_into_concrete_tasks(), produce_sequential_cleanup_plan()]; output={cleanup_plan:list[dict(task:str, priority:int)]}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-guided-structure-and-logic-refinement.md`



    ```markdown

    [Guided Structure & Logic Refinement] Execute the `cleanup_plan`. Simplify logic, restructure components, improve naming, and decouple dependencies. Ensure all transformations enhance clarity and preserve functionality. Apply changes iteratively and track impact. Execute as `{role=cleanup_executor; input={codebase_root:path, cleanup_plan:list}; process=[apply_structural_simplifications(), refactor_for_cohesion_and_decoupling(), implement_naming_clarity_improvements(), validate_functional_preservation_post-change()]; output={cleaned_codebase:path}}`

    ```



    #### `src/templates/lvl1/md/0130-h-cleanup-final-integrity-review-and-elegance-check.md`



    ```markdown

    [Final Integrity Review & Elegance Check] Perform a thorough audit of the cleaned codebase. Confirm it still fulfills the `core_purpose`. Check for broken logic, redundant artifacts, or missed opportunities for refinement. Ensure the structure is as elegant, minimal, and self-explanatory as possible. Execute as `{role=final_reviewer; input={cleaned_codebase:path, core_purpose:str}; process=[verify_functional_equivalence(), re-check structural readability(), ensure naming and flow elegance, confirm removal of obsolete elements], output={final_report:dict(equivalence_verified:bool, missed_opportunities:list, elegance_score:float)}}`

    ```



---



#### `3`



    #### `src/sequences/lvl1/md/0130-a-cleanup-holistic-structure-purpose-scan.md`



    ```markdown

    [Holistic Structure & Purpose Scan] Perform an immediate structural and purpose-based reconnaissance. Capture a bird's-eye view of the codebase: what it does, how it's laid out, and where core logic and dependencies reside. Focus on identifying the architectural *intent* through structure and traceable functionality. Execute as: `{role=holistic_scanner; input=codebase_root:any; process=[map_structure_topology(), inventory_key_modules_and_paths(), detect_main_entry_exit_points(), summarize_purpose_from_code_behaviors()]; output={structure_map:dict, purpose_summary:str}}`

    ```



    #### `src/sequences/lvl1/md/0130-b-cleanup-critical-path-clarity-trace.md`



    ```markdown

    [Critical Path & Clarity Trace] Identify and trace the core logical workflows that fulfill the codebase's primary purpose. Evaluate their linearity, cohesion, and reliance on indirect abstractions. Clarify where cognitive load is elevated due to non-transparent naming or structural fragmentation. Execute as: `{role=logic_tracer; input={structure_map:dict, purpose_summary:str}; process=[trace_main_execution_paths(), map_data_and_control_flow(), highlight_fragmented_or_confusing_links(), annotate clarity_breakpoints()]; output={critical_paths:list, clarity_trace:dict}}`

    ```



    #### `src/sequences/lvl1/md/0130-c-cleanup-redundancy-and-responsibility-audit.md`



    ```markdown

    [Redundancy & Responsibility Audit] Scan the codebase for repeated patterns, over-modularization, dead logic, and ambiguous responsibilities. Evaluate module cohesion, detect single responsibility violations, and identify where simplification could replace over-commented complexity. Execute as: `{role=cohesion_auditor; input=codebase_root:any; process=[scan_for_duplicate_code_fragments(), evaluate_functional_cohesion_per_module(), isolate_deprecated_or_unused_components(), detect_srp_violations_and_comment-dependence()]; output={redundancy_map:list, srp_findings:list, dead_code:list, overcomment_zones:list}}`

    ```



    #### `src/sequences/lvl1/md/0130-d-cleanup-structural-simplification-strategy.md`



    ```markdown

    [Structural Simplification Strategy] Formulate a minimal, high-impact plan for directory and file reorganization. Reorder the project structure to reflect logical workflows, reduce mental jumps between files, and group components by relation and flow rather than abstract type. Execute as: `{role=simplification_strategist; input={structure_map:dict, clarity_trace:dict, srp_findings:list}; process=[propose_sequential_logical_structure(), regroup_files_by_flow_and_relation(), remove_empty_or_fragile_directories(), define_clear_boundaries_between_layers()], output={structure_plan:list}}`

    ```



    #### `src/sequences/lvl1/md/0130-e-cleanup-naming-clarity-refinement-pass.md`



    ```markdown

    [Naming Clarity Refinement Pass] Redesign all identifiers—variables, functions, classes, modules—to maximize self-documenting clarity. Ensure names reflect both their functional role and position in the logic flow, removing the need for explanatory comments. Execute as: `{role=naming_refiner; input=codebase_root:any; process=[scan_identifiers_for_purpose_fit(), rename_for_flow-awareness(), unify naming_conventions_across_files(), flag ambiguous_or_generic_names()]; output={renamed_identifiers:list, naming_issues:list}}`

    ```



    #### `src/sequences/lvl1/md/0130-f-cleanup-essential-comment-retention-filter.md`



    ```markdown

    [Essential Comment Retention Filter] Apply a strict filter to remove redundant or obsolete comments. Retain only those that clarify ‘why’ something is non-obvious or define complex constraints. All ‘what’ and ‘how’ comments should be rendered obsolete by naming and structure. Execute as: `{role=comment_filter; input=codebase_root:any; process=[inventory_comments(), categorize_by_purpose(why_what_how), remove_non_essential_comments(), rewrite_retained_ones_to_maximum_brevity()]; output={comment_cleanup_map:list}}`

    ```



    #### `src/sequences/lvl1/md/0130-g-cleanup-functional-integrity-regression-check.md`



    ```markdown

    [Functional Integrity & Regression Check] Run a full verification cycle to ensure that the refactored codebase preserves all original functionality. Use regression tests or diff-based behavioral verification to detect unexpected side effects. Execute as: `{role=regression_checker; input={original_codebase:any, refactored_codebase:any}; process=[run_all_existing_tests(), detect_behavioral_drift_via_input_output_diff(), confirm_entry_point_consistency(), log_regression_findings()]; output={integrity_report:dict(functional_equivalence:bool, issues:list)}}`

    ```



    #### `src/sequences/lvl1/md/0130-h-cleanup-recursive-refinement-checkpoint.md`



    ```markdown

    [Recursive Refinement Checkpoint] Analyze the current state of the cleaned codebase. If principle-aligned, conclude. If further simplification, decoupling, or clarity gains are possible at manageable cost, define scoped targets for another cycle. Execute as: `{role=refinement_reviewer; input={refactored_codebase:any, integrity_report:dict}; process=[identify_remaining_opportunities(), assess complexity_vs_impact_ratio(), determine_scope_for_additional_pass(), issue_next_steps_or_finalize()]; output={refinement_decision:dict(proceed:bool, next_focus:list)}}`

    ```



---



#### `4`



    #### `src/templates/lvl1/md/0130-a-cleanup-codebase-snapshot-intent-discovery.md`



    ```markdown

    [Codebase Snapshot & Intent Discovery] Capture a high-level snapshot of the project’s structure and discern the system’s core purpose. Identify primary domains, entry points, and architectural boundaries to understand what the system is *really* trying to do. Execute as `{role=intent_scanner; input=codebase_root:path; process=[map_high_level_directory_structure(), detect_primary_entry_points(), extract_readme_or_docs_summary(), infer_primary_purpose(), sketch_logical_domains()]; output={project_overview:dict, core_purpose:str}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-logic-flow-and-dependency-mapping.md`



    ```markdown

    [Logic Flow & Dependency Mapping] Trace how execution, data, and control flows through the system to fulfill the `core_purpose`. Map critical dependencies and architectural pressure points. Execute as `{role=flow_mapper; input={project_overview:dict, core_purpose:str}; process=[identify_primary_logic_paths(), trace_inter-module_dependencies(), visualize_data_flow_and_transformations(), detect_circular_or_excessive_dependencies(), highlight complexity_hotspots]; output={workflow_map:dict, dependency_graph:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-clarity-cohesion-and-comment-dependence-audit.md`



    ```markdown

    [Clarity, Cohesion & Comment Dependence Audit] Evaluate how well the structure and code *explain themselves*. Score cohesion across modules, assess naming clarity, and flag regions where comments compensate for unclear code. Execute as `{role=clarity_auditor; input={workflow_map:dict, codebase_content:dict}; process=[score_identifier_naming_clarity(), detect_low_cohesion_modules(), locate_over-commented_sections(), classify comment intent (why vs what), summarize where clarity is structure-dependent vs comment-dependent]; output={clarity_diagnostics:dict, comment_analysis:list}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-structural-simplification-opportunity-scan.md`



    ```markdown

    [Structural Simplification Opportunity Scan] Identify sections of the project where file/module reorganization could improve understandability. Recommend realignments to better reflect logical flows and system responsibilities. Execute as `{role=structure_refactor_scanner; input={workflow_map:dict, clarity_diagnostics:dict}; process=[detect_misplaced_or_crosscutting_concerns(), flag over-fragmented or bloated modules(), suggest structure_by_function_or_flow(), identify poor folder/module naming]; output={restructure_recommendations:list}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-code-simplification-and-dead-logic-detection.md`



    ```markdown

    [Code Simplification & Dead Logic Detection] Review implementation-level logic for complexity hotspots. Identify functions or patterns that are redundant, over-complicated, dead, or can be simplified while preserving behavior. Execute as `{role=logic_optimizer; input=codebase_content:dict; process=[detect_dead_or_unreachable_code(), locate redundant or duplicated logic(), find unnecessary abstractions(), suggest concise equivalents(), enforce single-responsibility principles]; output={code_simplification_targets:list}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-cleanup-action-plan-synthesis.md`



    ```markdown

    [Cleanup Action Plan Synthesis] Synthesize a minimalist, safe-to-apply action plan from findings across structural, logic, and clarity audits. Order actions by simplicity, clarity gain, and implementation safety. Execute as `{role=cleanup_planner; input={restructure_recommendations:list, code_simplification_targets:list, comment_analysis:list}; process=[aggregate_all_cleanup_targets(), rank_by_cost_vs_clarity_gain(), group_by safety_and_scope(), generate_atomic_cleanup_steps(), document all decisions]; output={prioritized_cleanup_plan:list}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-clarity-driven-structure-implementation.md`



    ```markdown

    [Clarity-Driven Structure Implementation] Execute cleanup actions that involve reorganization of files, modules, and naming — reorienting structure around *how the system works*. Ensure all changes are internally consistent and reflect simplified logic paths. Execute as `{role=structure_cleaner; input={prioritized_cleanup_plan:list, codebase_root:path}; process=[restructure_directories_and_modules(), consolidate co-located logic(), rename folders/files for clarity and flow, adjust imports and references]; output={refactored_structure:path}}`

    ```



    #### `src/templates/lvl1/md/0130-h-cleanup-logic-refinement-and-comment-minimization.md`



    ```markdown

    [Logic Refinement & Comment Minimization] Apply logic simplifications and reduce unnecessary comments. Code should now largely explain itself — preserve only comments that express *why*, not *what/how*. Execute as `{role=logic_refiner; input={refactored_structure:path, prioritized_cleanup_plan:list}; process=[apply code simplifications(), enforce concise naming(), remove obvious/redundant comments(), retain minimal essential commentary]; output={clarified_codebase:path}}`

    ```



    #### `src/templates/lvl1/md/0130-i-cleanup-final-validation-and-elegance-verification.md`



    ```markdown

    [Final Validation & Elegance Verification] Confirm that the simplified codebase preserves all original behavior. Reassess final structure for self-explanation, cohesion, and clarity. Evaluate remaining complexity zones, and determine whether another refinement pass is warranted. Execute as `{role=elegance_validator; input={clarified_codebase:path, original_snapshot:any}; process=[run functional regression checks(), audit structure for principle alignment(), score self-explanation, assess need for recursion]; output={finalized_codebase:path, elegance_report:dict(score:float, remaining_opportunities:list, recommend_next_pass:bool)}}`

    ```



---



#### `5`



    #### `src/templates/lvl1/md/0130-a-cleanup-holistic-structure-purpose-scan.md`



    ```markdown

    [Holistic Structure & Purpose Scan] Your primary objective is a swift, big-picture analysis of the codebase: inventory the major components, outline their purpose, and note key dependencies. This sets a baseline for understanding what the system does and how it’s organized. Execute as `{role=holistic_scanner; input=codebase_root:any; process=[inventory_major_components(), identify_primary_use_cases(), outline_top_level_dependencies(), articulate_overall_project_mission()]; output={structure_overview:dict, project_mission:str}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-key-workflow-complexity-trace.md`



    ```markdown

    [Key Workflow & Complexity Trace] Focus on tracing the system’s primary workflows relevant to the `project_mission`. Map the control paths, data transformations, and cross-module interactions that define these workflows. Identify complexity hotspots where logic or structure is convoluted. Execute as `{role=workflow_tracer; input={structure_overview:dict, project_mission:str, codebase_content:dict}; process=[extract_primary_workflows(), trace_control_and_data_flow(), locate_logic_complexity_hotspots(), summarize_cross_module_interactions()]; output={workflow_maps:list[dict], complexity_zones:list}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-clarity-and-cohesion-audit.md`



    ```markdown

    [Clarity & Cohesion Audit] Assess the codebase for readability and organizational strength. Evaluate naming quality, logical flow, adherence to Single Responsibility Principle, and cohesion within modules/classes, paying special attention to places flagged by `complexity_zones`. Execute as `{role=clarity_auditor; input={codebase_content:dict, workflow_maps:list, complexity_zones:list}; process=[examine_identifier_naming(), measure_cohesion_per_module_class(), check_single_responsibility_adherence(), document_comment_reliance_for_explanation()]; output={clarity_report:dict, cohesion_findings:list, srp_violations:list, comment_reliance:list}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-focused-simplification-structural-realignment.md`



    ```markdown

    [Focused Simplification & Structural Realignment] Use the insights from the `clarity_report` and `cohesion_findings` to propose minimal yet high-impact restructuring and refactoring. Target removing obvious redundancies, consolidating related functionality, and reducing external dependencies. Execute as `{role=structure_refiner; input={clarity_report:dict, cohesion_findings:list, srp_violations:list, codebase_content:dict}; process=[identify_redundant_or_overlapping_functionality(), plan_cohesive_module_realignment(), refine_dependency_boundaries(), propose_naming_improvements()]; output={simplification_plan:dict, proposed_structure_changes:list}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-essential-commentary-refinement.md`



    ```markdown

    [Essential Commentary Refinement] Apply a “minimal, essential-only” policy to all comments. Retain only comments that clarify intent or justify complex decisions. Remove or reduce redundant, outdated, or purely descriptive commentary that can be explained by improved naming/structure. Execute as `{role=comment_curator; input={codebase_content:dict}; process=[gather_all_comments(), filter_non_essential_comments(), preserve_comments_explaining_complex_design_intent(), create_update_list_for_comment_refinements()]; output={comment_cleanup_list:list, refined_comment_guidelines:str}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-prioritized-cleanup-action-plan.md`



    ```markdown

    [Prioritized Cleanup Action Plan] Integrate the findings from the `simplification_plan` and `comment_cleanup_list` into a cohesive roadmap. Assign priorities based on impact and safety. Outline actionable steps to restructure modules, refactor code, rename identifiers, and adjust comments. Execute as `{role=cleanup_planner; input={simplification_plan:dict, proposed_structure_changes:list, comment_cleanup_list:list, refined_comment_guidelines:str}; process=[merge_restructuring_and_comment_refinements(), rank_changes_by_safety_and_benefit(), draft_detailed_refactoring_tasks(), finalize_prioritized_plan()]; output={actionable_cleanup_plan:str}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-validation-and-refinement-check.md`



    ```markdown

    [Validation & Refinement Check] Validate that the executed refactoring plan preserves original functionality and meets the principles of clarity, maintainability, and minimalism. Evaluate if additional cleanup cycles are warranted or if the codebase has reached a sufficiently optimal state. Execute as `{role=validator; input={original_codebase:any, updated_codebase:any, actionable_cleanup_plan:str}; process=[verify_functional_equivalence(), review_code_against_core_principles(), gauge_remaining_complexity(), determine_need_for_additional_iteration()]; output={validation_report:dict, next_steps_recommendation:str}}`

    ```



---



#### `6`



    #### `src/templates/cleanup/md/0130-a-cleanup-baseline-scan-structure-purpose.md`



    ```markdown

    [Baseline Scan: Structure & Purpose] Your primary directive is to establish a foundational understanding. Analyze the input codebase structure, inventory its main components (modules, directories, key files), map high-level interactions/dependencies, and determine the core purpose or function of the system. Execute as `{role=baseline_scanner; input=codebase_root:path; process=[inventory_major_components(), map_high_level_dependencies(), identify_entry_points_and_key_workflows(), distill_core_system_purpose()]; output={structure_summary:dict, core_purpose:str, key_workflows_identified:list}}`

    ```



    #### `src/templates/cleanup/md/0130-b-cleanup-clarity-audit-self-explanation.md`



    ```markdown

    [Clarity Audit: Readability & Self-Explanation] Evaluate the codebase's inherent clarity against the principles of simplicity and self-explanation. Assess the quality of naming conventions (variables, functions, classes, files), the logical simplicity of the identified `key_workflows`, and the degree to which the structure itself communicates intent. Identify areas overly reliant on comments for basic understanding. Execute as `{role=clarity_auditor; input={codebase_content:dict, structure_summary:dict, key_workflows_identified:list}; process=[assess_naming_convention_clarity_and_consistency(), evaluate_workflow_logical_simplicity(), determine_structural_self_explanation_level(), identify_comment_dependency_hotspots()]; output={clarity_assessment:dict(naming_quality:str, flow_complexity:str, structural_clarity:str, comment_reliance:list)}}`

    ```



    #### `src/templates/cleanup/md/0130-c-cleanup-cohesion-responsibility-check.md`



    ```markdown

    [Cohesion & Responsibility Check] Analyze component design based on the Single Responsibility Principle (SRP) and cohesion. Evaluate if modules/classes/functions have a single, well-defined purpose. Assess if related functionalities are grouped logically (high cohesion) and unrelated components have minimal dependencies (low coupling). Execute as `{role=cohesion_analyzer; input={codebase_content:dict, structure_summary:dict}; process=[analyze_component_responsibilities_vs_srp(), assess_module_class_functional_cohesion(), identify_high_coupling_points(), evaluate_separation_of_concerns()]; output={cohesion_report:dict(srp_violations:list, cohesion_issues:list, coupling_concerns:list)}}`

    ```



    #### `src/templates/cleanup/md/0130-d-cleanup-cleanup-opportunity-identification.md`



    ```markdown

    [Cleanup Opportunity Identification] Synthesize findings from the previous steps (`clarity_assessment`, `cohesion_report`) to pinpoint specific opportunities for cleanup and simplification. Identify complex logic sections, code redundancies (duplication, dead code), areas violating SRP or cohesion principles, poorly named identifiers, and structural elements that obscure purpose. Execute as `{role=opportunity_detector; input={codebase_content:dict, clarity_assessment:dict, cohesion_report:dict}; process=[detect_logic_complexity_hotspots(), find_redundant_or_dead_code(), list_srp_cohesion_refactoring_targets(), identify_naming_improvement_candidates(), pinpoint_structural_refinement_areas()]; output={cleanup_opportunities:dict(complexity:list, redundancy:list, responsibility:list, naming:list, structure:list)}}`

    ```



    #### `src/templates/cleanup/md/0130-e-cleanup-prioritized-action-plan-generation.md`



    ```markdown

    [Prioritized Action Plan Generation] Generate a concrete, prioritized list of cleanup actions based on the identified `cleanup_opportunities`. Rank actions focusing first on safety and high impact: prioritize removal of dead/redundant code, followed by clarity improvements (naming, comment reduction where code is self-explanatory), then structural adjustments (cohesion, SRP) and finally simplification of complex logic. *Output only the plan.* Execute as `{role=cleanup_planner; input={cleanup_opportunities:dict}; process=[consolidate_all_opportunities(), rank_actions_by_safety_and_impact(removals > clarity > structure > complexity), define_specific_actionable_tasks(), sequence_tasks_logically()]; output={prioritized_cleanup_plan:list_of_tasks}}`

    ```



    #### `src/templates/cleanup/md/0130-f-cleanup-execute-incremental-cleanup-validate.md`



    ```markdown

    [Execute Incremental Cleanup & Validate] Execute the highest-priority, safest tasks from the `prioritized_cleanup_plan`. Apply changes incrementally. After each significant change or batch of changes, perform validation checks (e.g., running tests, static analysis) to ensure functionality remains equivalent and no regressions were introduced. Focus on executing removals and clarity improvements first. Execute as `{role=cleanup_executor_validator; input={codebase_root:path, prioritized_cleanup_plan:list}; process=[execute_highest_priority_safe_tasks(limit_scope_per_run), run_validation_suite(), verify_functional_equivalence_post_change(), update_plan_with_completed_tasks()]; output={partially_cleaned_codebase:path, execution_validation_log:list, remaining_cleanup_plan:list}}`

    ```



    #### `src/templates/cleanup/md/0130-g-cleanup-assess-refinement-iteration.md`



    ```markdown

    [Assess & Refine Iteration] Review the `execution_validation_log` and the state of the `partially_cleaned_codebase`. Assess the impact of the changes made against the core principles (Simplicity, Clarity, Cohesion, etc.). Evaluate the `remaining_cleanup_plan` and determine if executing further, potentially more complex, cleanup tasks offers sufficient value compared to the risk/effort. Decide whether to initiate another cleanup cycle (potentially starting from step 4 or 6 with refined scope) or conclude the process. Execute as `{role=refinement_assessor; input={partially_cleaned_codebase:path, execution_validation_log:list, remaining_cleanup_plan:list}; process=[evaluate_impact_of_completed_tasks(), analyze_cost_benefit_of_remaining_tasks(), check_adherence_to_core_principles(), decide_on_next_iteration_necessity_and_scope()]; output={iteration_decision:dict(proceed_further:bool, next_focus_tasks:list, assessment_summary:str)}}`

    ```



---



#### `7`



    #### `src/templates/lvl1/md/0130-a-cleanup-baseline-principle-assessment-structure-mapping.md`



    ```markdown

    [Baseline Principle Assessment & Structure Mapping] Your initial task is foundational understanding aligned with core principles. Analyze the input codebase/project structure: Map key directories, files, and primary components. Identify high-level dependencies and entry points. Perform an initial assessment against core principles (Simplicity, Clarity, Cohesion, SRP, Self-Explanation, Minimalism). Distill the system's primary purpose. Execute as `{role=baseline_assessor; input=codebase_root:path; process=[map_structure_and_major_components(), identify_high_level_dependencies_and_entry_points(), analyze_primary_purpose_or_workflow(), perform_initial_scan_for_principle_adherence_or_violations()]; output={baseline_report:dict(structure_map:any, primary_purpose:str, initial_principle_assessment:dict)}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-complexity-clarity-cohesion-hotspot-identification.md`



    ```markdown

    [Complexity, Clarity & Cohesion Hotspot Identification] Building on the baseline, dive deeper to pinpoint specific areas hindering elegance and maintainability. Trace core logic flows related to the `primary_purpose`. Critically evaluate naming clarity, logical complexity, component cohesion (vs. desired SRP), structural awkwardness, and comment necessity/redundancy. Identify specific anti-patterns and violations of core principles. Execute as `{role=hotspot_identifier; input={baseline_report:dict, codebase_content:dict}; process=[trace_core_logic_paths(), evaluate_naming_and_readability_effectiveness(), assess_component_cohesion_and_responsibility_alignment(), analyze_comment_usage_against_necessity_and_clarity(), pinpoint_complexity_hotspots_and_principle_violations()]; output={hotspot_analysis:dict(complexity_zones:list, clarity_issues:list, cohesion_violations:list, comment_concerns:list, structural_concerns:list)}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-prioritized-refactoring-simplification-blueprint.md`



    ```markdown

    [Prioritized Refactoring & Simplification Blueprint] Synthesize the findings from `hotspot_analysis` into a strategic, actionable cleanup plan. Prioritize refactoring opportunities based on their potential impact on improving clarity, simplicity, and maintainability, balanced against implementation risk/effort. Define specific, targeted actions for structural reorganization, code simplification (logic, naming), cohesion improvement, and comment rationalization (removal/refinement). Execute as `{role=refactoring_strategist; input=hotspot_analysis:dict; process=[consolidate_identified_issues(), rank_refactoring_targets_by_impact_and_safety(), define_specific_structural_refinement_steps(), specify_code_level_simplification_and_clarification_actions(), detail_comment_rationalization_plan(), sequence_actions_into_prioritized_blueprint()]; output={cleanup_blueprint:list[dict(action:str, target:any, priority:int, rationale:str)]}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-targeted-structural-realignment-cohesion-enhancement.md`



    ```markdown

    [Targeted Structural Realignment & Cohesion Enhancement] Execute the *structural* refactoring tasks defined in the `cleanup_blueprint`. Reorganize directories and files to inherently reflect the system's logical flow and conceptual grouping. Consolidate highly cohesive components and increase decoupling between unrelated parts. Apply improved structural naming conventions. Ensure all internal references and imports are updated accordingly. Focus solely on the structural changes mandated by the blueprint. Execute as `{role=structure_refactorer; input={codebase_root:path, cleanup_blueprint:list}; process=[implement_prioritized_directory_file_restructuring(), apply_cohesive_component_grouping(), enforce_decoupling_where_specified(), update_structural_naming_conventions(), resolve_resulting_import_reference_changes()]; output={realigned_structure_path:path}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-code-clarity-enhancement-essential-comment-rationalization.md`



    ```markdown

    [Code Clarity Enhancement & Essential Comment Rationalization] Execute the *code-level* refactoring and cleanup tasks within the `realigned_structure_path`, guided strictly by the `cleanup_blueprint`. Apply self-explanatory naming conventions. Simplify complex algorithms and logic flows. Enforce the Single Responsibility Principle at the function/class level. Remove dead or redundant code. Rationalize comments: eliminate those made redundant by clarity improvements, retain/refine only essential 'why' comments. Execute as `{role=code_clarifier_rationalizer; input={realigned_structure_path:path, cleanup_blueprint:list}; process=[apply_prioritized_naming_improvements(), implement_logic_simplification_and_consolidation(), refactor_for_single_responsibility_compliance(), execute_comment_removal_and_refinement_plan(), purge_identified_dead_or_redundant_code()]; output={clarified_codebase_path:path}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-post-refactoring-validation-refinement-assessment.md`



    ```markdown

    [Post-Refactoring Validation & Refinement Assessment] Perform a final validation and assessment of the `clarified_codebase_path`. Verify functional equivalence against the original state (or defined test suite). Critically evaluate the outcome against the core principles (Simplicity, Clarity, Cohesion, SRP, Self-Explanation) and the goals outlined in the `cleanup_blueprint`. Identify any remaining significant deviations or areas warranting further refinement. Determine if another cleanup cycle is justified. Execute as `{role=cleanup_validator_assessor; input={original_codebase_state:any, clarified_codebase_path:path, cleanup_blueprint:list}; process=[perform_functional_equivalence_validation(), audit_final_state_against_core_principles_and_blueprint_goals(), identify_remaining_complexity_or_clarity_deficits(), assess_cost_benefit_of_further_refinement(), generate_validation_report_and_next_cycle_recommendation()]; output={validation_outcome:dict(functional_equivalence:bool, principle_adherence_score:float, remaining_concerns:list, next_cycle_recommended:bool, focus_areas:list)}}`

    ```



---



#### `8`



    #### `src/templates/lvl1/md/0130-a-cleanup-structural-immersion-overview.md`



    ```markdown

    [Structural Immersion & Overview] Immerse into the codebase to rapidly map its high-level structure, modules, file hierarchy, and relationship patterns. Assess immediate alignment with simplicity, clarity, and cohesion principles. Establish a baseline of current organization, highlighting major design deviations or anti-patterns. Execute as `{role=structural_overviewer; input=codebase_root:any; process=[scan_file_and_directory_structure(), identify_module_relationships(), map_high_level_dependencies(), baseline_against_core_principles(), note_major_structural_flaws_or_strengths()]; output={structure_overview:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-b-cleanup-intrinsic-clarity-evaluation.md`



    ```markdown

    [Intrinsic Clarity Evaluation] Critically assess the inherent readability and logic of structure, naming, and workflows—without relying on commentary. Identify areas where complexity, ambiguous naming, convoluted flow, or unclear logic cloud self-explanation, violating the principles of transparent, single-responsibility-driven design. Execute as `{role=clarity_evaluator; input=structure_overview:dict; process=[analyze_naming_scheme_quality(), assess_logical_flow_coherence(), detect_multi_responsibility_components(), flag_comment_reliant_clarity_zones(), summarize_core_clarity_breakdowns()]; output={clarity_issues:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-c-cleanup-cohesion-simplification-opportunity-map.md`



    ```markdown

    [Cohesion & Simplification Opportunity Map] Systematically scan for candidates to improve cohesion and simplicity across the codebase: highlight redundant, weakly grouped, or cross-dependent components, fragmented logic, dead code, or modules best unified. Pinpoint where architectural or naming refinements can enable self-explanation and intuitive navigation. Execute as `{role=simplification_mapper; input={structure_overview:dict, clarity_issues:dict}; process=[detect_redundant_and_fragile_structures(), reveal_modularization_inefficiencies(), map_code_and_comment_redundancy(), locate_navigation_and_discoverability_issues(), compile_simplification_opportunities()]; output={opportunity_map:dict}}`

    ```



    #### `src/templates/lvl1/md/0130-d-cleanup-prioritized-cleanup-action-plan.md`



    ```markdown

    [Prioritized Cleanup Action Plan] Integrate all findings to construct a concise, high-impact action plan for codebase cleanup. Prioritize actions that maximize clarity and structure with minimal disruption: dead code removal, structure/naming refinement, logic consolidation, and comment minimization—each step mapped to specific opportunities identified. Execute as `{role=cleanup_planner; input={opportunity_map:dict}; process=[consolidate_and_rank_opportunities_by_impact(), balance_risk_and_effort(), sequence_actions_for_logical_dependency(), filter_for_high_clarity_and_cohesion_gains(), finalize_actionable_cleanup_plan()]; output={cleanup_plan:list}}`

    ```



    #### `src/templates/lvl1/md/0130-e-cleanup-structural-clarity-realignment-execution.md`



    ```markdown

    [Structural & Clarity Realignment Execution] Methodically execute the prioritized actions: refactor file/directory structure, improve naming, simplify code segments, and minimize commentary to only the essential. Each change must strictly reinforce transparency, single responsibility, and intuitive comprehension, rendering the structure self-explanatory. Execute as `{role=realignment_executor; input={cleanup_plan:list, codebase_root:any}; process=[perform_structural_changes(), refactor_names_and_borders(), simplify_logic_flows(), remove_dead_and_redundant_code(), reduce_to_essential_comments(), ensure_post_change_cohesion_and_navigation()]; output={cleaned_codebase:any}}`

    ```



    #### `src/templates/lvl1/md/0130-f-cleanup-integrity-and-principle-validation.md`



    ```markdown

    [Integrity & Principle Validation] Verify functional equivalence and comprehensive alignment with the core principles. Audit for clarity, minimization of commentary, self-explanatory structure, and maintainability. Identify any residual complexity, naming issues, or navigation hurdles—recommending further refinement only where clear principle violations remain. Execute as `{role=integrity_validator; input={cleaned_codebase:any, structure_overview:dict}; process=[validate_functional_equivalence(), audit_against_principles(), check_clarity_and_navigation_post_cleanup(), document_remaining_issues(), recommend_or_conclude_additional_refinement()]; output={validation_report:dict, final_state:any}}`

    ```



    #### `src/templates/lvl1/md/0130-g-cleanup-cleanup-rationale-impact-summary.md`



    ```markdown

    [Cleanup Rationale & Impact Summary] Provide a clear, succinct summary reporting why each structural or code-level change was made, which principle it supports, and the impact on clarity, maintainability, and long-term usability. The report is structured for easy handoff and future reference. Execute as `{role=impact_reporter; input={cleanup_plan:list, validation_report:dict}; process=[map_actions_to_design_principles(), summarize_before_and_after_clarity(), articulate_impact_on_readability_and_maintenance(), produce_handoff_ready_summary()]; output={cleanup_summary_report:str}}`

    ```


## Message 5

**Synthesizing details**

I'm pulling together a final sequence of steps for a "Generalized Codebase Cleanup," aiming to be minimal yet thorough. The sequence should adhere to constraints and integrate the best practices from the provided references.

**Piecing together key steps**

I’m pulling together seven critical steps for the final sequence, each focusing on key aspects like holistic structure assessment, workflow analysis, clarity audit, cleanup planning, structural changes, code rationalization, and validation.

**Finalizing the approach**

I’m integrating each step into a concise, markdown-formatted sequence. This ensures clarity, precision, and adaptability, maintaining chronological progression and recursive building.

**Labeling steps**

I’m addressing naming conventions with indices "0131-a" to "0131-g," ensuring each step maintains uniformity while tagging relevant markdown files for easy access and reference.

**Constructing the cleanup pathway**

I’ve mapped out a seven-step sequence for "Generalized Codebase Cleanup," focusing on structure scan, workflow mapping, clarity audit, cleanup plan synthesis, realignment, commentary rationalization

## Message 6

Below is a consolidated **Generalized Codebase Cleanup** sequence that blends the best elements from all provided references. This sequence is designed to be *universally applicable*, *chronologically recursive*, and strictly aligned with the core principles of clarity, simplicity, cohesion, single responsibility, and minimal commentary.

---

#### `src/templates/lvl1/md/0131-a-holistic-structure-purpose-scan.md`

```markdown
[Holistic Structure & Purpose Scan]  
Perform a rapid overview of the codebase to establish its overall purpose, how it’s organized, and where main logic resides. Inventory top-level directories and files, identify entry points, and outline primary responsibilities. This baseline guides all subsequent cleanup steps.

Execute as: `{role=holistic_scanner; input=codebase_root:any; process=[map_primary_directories_files(), identify_main_entry_points_and_data_flows(), note_core_dependencies(), infer_system_intent_and_primary_functionality()]; output={structure_map:dict, inferred_purpose:str}}`
```

---

#### `src/templates/lvl1/md/0131-b-key-workflow-dependency-mapping.md`

```markdown
[Key Workflow & Dependency Mapping]  
Focus on the essential workflows that fulfill the `inferred_purpose`. Trace how control and data move through the system; map which modules, classes, or functions are critical to these flows. Capture key interdependencies and potential complexity hotspots.

Execute as: `{role=workflow_mapper; input={structure_map:dict, inferred_purpose:str, codebase_content:dict}; process=[identify_critical_workflows(), trace_control_flow_in_core_paths(), outline_primary_data_transformations(), detect_high_dependency_zones()], output={workflow_maps:list, dependency_map:dict, complexity_nodes:list}}`
```

---

#### `src/templates/lvl1/md/0131-c-clarity-cohesion-redundancy-audit.md`

```markdown
[Clarity, Cohesion & Redundancy Audit]  
Evaluate the codebase against core design principles: naming clarity, logical simplicity, single responsibility adherence, and module/class cohesion. Identify duplication, dead code, or reliance on verbose comments for explanation. Single out components that break the flow or obscure readability.

Execute as: `{role=clarity_auditor; input={workflow_maps:list, dependency_map:dict, codebase_content:dict}; process=[examine_identifier_naming_quality(), detect_duplicate_or_dead_logic(), assess_module_class_cohesion_and_srp(), locate_comment_overdependence_or_inconsistency()], output={audit_findings:dict(clarity_issues:list, redundancy_report:list, srp_violations:list, comment_hotspots:list)}}`
```

---

#### `src/templates/lvl1/md/0131-d-prioritized-cleanup-plan-synthesis.md`

```markdown
[Prioritized Cleanup Plan Synthesis]  
Combine insights from the audits into a cohesive, *incremental* action plan. Focus on safe, high-impact improvements first (removing dead code, simplifying naming, re-grouping cohesive elements, trimming unnecessary comments), followed by deeper architectural or logic changes. Provide a clear, itemized roadmap.

Execute as: `{role=cleanup_planner; input={audit_findings:dict}; process=[compile_all_issues_by_severity_and_simplicity(), rank_actions_by_impact_and_risk(), proposestructure_refactors_and_naming_overhauls(), finalize_sequential_cleanup_tasks()], output={cleanup_plan:list_of_tasks}}`
```

---

#### `src/templates/lvl1/md/0131-e-targeted-realignment-refactoring.md`

```markdown
[Targeted Realignment & Refactoring Execution]  
Carry out structural reorganizations and refactoring steps specified in the `cleanup_plan`. Adjust directories, rename files/modules for clarity, reduce cross-dependencies, and consolidate scattered logic. Ensure each modification enhances both understanding and maintainability.

Execute as: `{role=structure_refiner; input={codebase_root:any, cleanup_plan:list}; process=[restructure_files_and_directories_for_flow_cohesion(), apply_naming_improvements(), remove_or_merge_redundant_components(), tighten_dependency_boundaries(), verify_functional_equivalence_along_the_way()], output={refactored_codebase:any}}`
```

---

#### `src/templates/lvl1/md/0131-f-essential-comment-rationalization.md`

```markdown
[Essential Comment Rationalization]  
Enforce minimal, purposeful commentary. Strip out comments that duplicate what improved naming or structure already conveys. Preserve and refine only those clarifying *why* a complex solution or workaround exists. Avoid repeating the *what* or *how*—the code itself should cover those.

Execute as: `{role=comment_curator; input=refactored_codebase:any; process=[inventory_all_comments(), classify_comments_as_why_vs_what_or_how(), remove_or_rewrite_non_essential_comments(), confirm_code_remains_self_explanatory()], output={comment_refined_codebase:any}}`
```

---

#### `src/templates/lvl1/md/0131-g-validation-and-recursive-refinement.md`

```markdown
[Validation & Recursive Refinement]  
Confirm the final cleaned codebase preserves original functionality and meets core principles (Simplicity, Clarity, Cohesion, SRP, Minimal Commentary). Run tests or manual checks to ensure no regressions. If structural or logic deficits remain, repeat from an earlier step with a narrowed scope.

Execute as: `{role=final_validator; input={original_codebase:any, comment_refined_codebase:any}; process=[run_functional_equivalence_tests(), review_against_principle_checklist(), evaluate_remaining_complexity_vs_benefits(), decide_on_next_cleanup_iteration_or_finalize()], output={validation_report:dict(equivalence_ok:bool, principle_adherence_score:float, next_cycle_recommendation:bool}}`
```

---

### Why This Sequence?
1. **Holistic Start:** Establish a complete mental map of the project, ensuring you understand structure and purpose.  
2. **Workflow Focus:** Zero in on critical pathways that deliver core functionality, targeting complexity reduction where it matters most.  
3. **Precision Audits:** Systematically evaluate clarity, cohesion, redundancy, and comment usage to pinpoint *where* to refactor.  
4. **Safe, Impactful Cleanup Plan:** Develop a *prioritized* roadmap to tackle the biggest issues first with minimal risk.  
5. **Targeted Execution:** Restructure and refactor methodically, ensuring each change moves the codebase toward simpler, clearer design.  
6. **Comment Rationalization:** Keep only essential “why” comments; rely on improved naming and design to convey everything else.  
7. **Validation & Iteration:** Validate no regressions in functionality, confirm alignment with core principles, and decide if additional refinement is beneficial.

This **Generalized Codebase Cleanup** flow is intended for *safe, iterative transformation*, guaranteeing that clarity, simplicity, and maintainability are systematically enhanced without compromising existing functionality.
