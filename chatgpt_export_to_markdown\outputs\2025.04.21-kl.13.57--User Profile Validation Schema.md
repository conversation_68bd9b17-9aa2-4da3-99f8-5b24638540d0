# User Profile Validation Schema

Conversation ID: 680632aa-1348-8008-8740-495a3a07116e

## Message 1

# Context:



Understand the structure of the format of these instructions, each block is enclosed with triple backticks and a structure that includes the `title`, `a short interpretive statement`, and the `transformational instructions` - wrapped in curly braces: `{}`):



    #### `0100-a-primal-essence-extraction.md`



    ```markdown

        [Primal Essence Extraction] Your task is not to interpret the input, but to isolate its inviolable core—intent, essential components, and non-negotiable constraints—discarding all contextual or narrative noise. Goal: Extract the *essential* elements from the input. Execute as `{role=essence_extractor; input=raw_input:any; process=[penetrate_to_core(), extract_intent_components_constraints(), discard_noise()]; output={core_essence:dict}}`

    ```



    ---



    #### `0100-b-intrinsic-value-prioritization.md`



    ```markdown

        [Intrinsic Value Prioritization] Your function is not to retain all elements, but to rigorously evaluate and rank each extracted element by intrinsic significance, clarity, impact, and contextual relevance to the core objective. Goal: Retain only the *highest-value* components. Execute as `{role=value_prioritizer; input=core_essence:dict; process=[assess_intrinsic_value(), rank_by_impact_and_relevance(), isolate_highest_impact_elements()]; output={prioritized_elements:list}}`

    ```



    ---



    #### `0100-c-structural-logic-relationship-mapping.md`



    ```markdown

        [Structural Logic & Relationship Mapping] Your responsibility is not to assemble arbitrarily, but to architect a maximally coherent structure by mapping relationships, dependencies, and logical flows between prioritized elements, resolving any conflict, redundancy, or ambiguity. Goal: Create a *coherent* structural blueprint. Execute as `{role=structure_architect; input=prioritized_elements:list; process=[map_relationships(), resolve_conflicts(), organize_logical_flow()]; output={coherent_structure:object}}`

    ```



    ---



    #### `0100-d-potency-clarity-amplification.md`



    ```markdown

        [Potency & Clarity Amplification] Your directive is not subtle suggestion, but potent impact; intensify the clarity, precision, and inherent impact of the unified structure, amplifying all valuable attributes while maintaining strict self-explanatory integrity. Goal: Produce an *amplified* and self-explanatory structure. Execute as `{role=clarity_amplifier; input=coherent_structure:object; process=[refine_language(), optimize_for_self_explanation(), maximize_conciseness_and_impact()]; output={amplified_output:any}}`

    ```



    ---



    #### `0100-e-adaptive-optimization-universalization.md`



    ```markdown

        [Adaptive Optimization & Universalization] Your final requirement is not limited application, but universal usability; polish the amplified result to ensure peak clarity, utility, and adaptability. Encode the output using a universally translatable schema for seamless usability (across Markdown, JSON, LLM, etc.) and ongoing refinement. Goal: Ensure *adaptable*, schema-driven output. Execute as `{role=optimizer_universalizer; input=amplified_output:any; process=[validate_fidelity_to_intent(), structure_as_universal_schema(), maximize_cross-format_adaptability(), embed_refinement_hooks()]; output={final_instruction_system:any}}`

    ```



# Objective:



Your first task will be to rewrite this example to match the patterns and formats of the provided example, and to do so with the aim of producing a maximally llm-optimized system-instruction. Here's the instruction to rewrite:



    ```markdown

    Please generate a structured instruction block that strictly adheres to the following three-part format. This format is designed to be parsed by code expecting a specific pattern:



    1.  **Title:** Start with a concise title or label enclosed in square brackets `[...]`.

        * *Example Content:* A short phrase identifying the overall task or concept, like `[Data Validation Rule]` or `[Image Processing Parameters]`.



    2.  **Interpretation:** Immediately following the closing square bracket `]` of the Title (optional whitespace is acceptable), provide a natural language explanation, description, context, or goal related to the Title. This section should *not* contain the specific transformation block structure.

        * *Example Content:* A few sentences explaining what the rule/parameters are for, the objective, or any constraints in plain language.



    3.  **Transformation:** Immediately following the Interpretation text (optional whitespace is acceptable), provide a structured configuration, command, or data block enclosed *specifically* within backticks and curly braces `` `{...}` ``.

        * *Example Content:* A dictionary-like structure defining parameters, roles, inputs, outputs, process steps, or constraints, like `` `{action=validate; schema=user_v1; required_fields=[name, email]; constraints=[email_format()]}` ``.



    **Constraint:** The final output must contain exactly these three parts in this order, using the specified delimiters (`[...]` for Title, plain text for Interpretation, and `` `{...}` `` for Transformation) precisely. Do not add any introductory or concluding text outside of this structure.



    **Now, generate an example instruction block following this structure, using the concept of 'User Profile Update Validation'.**

    ```

## Message 2

Ah, a task as delightfully rigid as a Bauhaus chair—form, function, and not a millimeter of deviation. Very well, behold the reborn instruction block, sculpted to the exacting proportions of your sacred schema:

---

#### `0100-f-user-profile-update-validation.md`

```markdown
[User Profile Update Validation] This instruction defines a validation routine for incoming user profile update requests. Its purpose is to ensure data integrity, enforce required formatting, and confirm the presence of all critical fields before processing updates. It acts as a safeguard against malformed or incomplete user data submissions during profile modifications.

`{action=validate_profile_update; schema=user_profile_v2; required_fields=[user_id, name, email, updated_at]; constraints=[email_format(), timestamp_format(updated_at), max_length(name, 100)]; failure_mode=reject_on_error}`
```
