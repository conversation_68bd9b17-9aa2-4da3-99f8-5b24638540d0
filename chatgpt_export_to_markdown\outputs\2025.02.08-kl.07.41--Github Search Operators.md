# Github Search Operators

Conversation ID: 67a6fc93-afc0-8008-9f9c-6c9ee96823fb

## Message 1

<template>

    <metadata>

        <agent_name value="GithubSearchSyntax"/>

        <description value="The GithubSearchSyntax XML template uniquely details advanced search capabilities, providing exhaustive specifications for utilizing specialized GitHub repository, code, issue, and user search operators to refine search accuracy and results efficiency."/>

        <version value="a"/>

        <status value="todo"/>

    </metadata>

    <search_operators>

        <category value="Advanced Features">

            <item value="Exact phrase matching using quotes" description="Use double quotes to find an exact phrase" example='"hello world"' />

            <item value="Wildcard matching" description="Use ? for single-char or * for multi-char wildcards" example='te?t or test*' />

            <item value="Range queries" description="Use double-dot syntax for numeric ranges" example='stars:10..50' />

            <item value="Greater than/less than" description="Use < or > for numeric ranges" example='stars:>1000' />

        </category>

        <category value="Boolean Operators">

            <item value="AND" description="Combine search terms (default operator)" example='stars:>50 AND language:Python' />

            <item value="OR" description="Match one term or another; use parentheses for clarity" example='language:ruby OR language:cpp OR language:csharp' />

            <item value="NOT" description="Exclude results containing a term (often replaced by '-')" example='language:Java NOT language:Python' />

        </category>

        <category value="Code Search Operators">

            <item tag="string" value="/string/" description="Search using regular expressions (next-gen code search)" example='/hello.*world/' />

            <item tag="string" value="extension:string" description="Search for files with a specific extension" example='extension:js' />

            <item tag="string" value="filename:string" description="Search for files with a specific name" example='filename:Dockerfile' />

            <item tag="string" value="language:string" description="Search within code of a specific language" example='language:Ruby' />

            <item tag="string" value="path:string" description="Search within a specific file path" example='path:/src/' />

            <item tag="string" value="repo:string" description="Search within a specific repository" example='repo:octocat/Hello-World' />

        </category>

        <category value="Commit Search Operators">

            <item tag="bool" value="fork:bool" description="Search for forks" example='fork:true' />

            <item tag="bool" value="merge:bool" description="Search for merge commits" example='merge:true' />

            <item tag="date" value="author-date:YYYY-MM-DD" description="Commits authored on a specific date" example='author-date:2023-01-01' />

            <item tag="date" value="committer-date:YYYY-MM-DD" description="Commits committed on a specific date" example='committer-date:>2023-01-01' />

            <item tag="string" value="committer:string" description="Commits committed by username" example='committer:octocat' />

        </category>

        <category value="Commits">

            <item tag="string" value="author:string" description="Commits authored by username" example='author:octocat' />

            <item tag="string" value="review-requested:string" description="Commits with review requested from a user" example='review-requested:octocat' />

            <item tag="string" value="reviewed-by:string" description="Commits reviewed by a specific user" example='reviewed-by:octocat' />

        </category>

        <category value="Issues">

            <item tag="string" value="milestone:string" description="Search by milestone" example='milestone:v1.1' />

        </category>

        <category value="Issues/Pull Request">

            <item tag="date" value="closed:YYYY-MM-DD" description="Issues / Pull Requests closed on a specific date" example='type:pr closed:>2023-01-15' />

            <item tag="string" value="assignee:string" description="Issues / Pull Requests assigned by username" example='assignee:octocat' />

            <item tag="string" value="commenter:string" description="Issues / Pull Requests by commenter" example='commenter:octocat' />

            <item tag="string" value="involves:string" description="Issues / Pull Requests that involve username" example='involves:octocat' />

            <item tag="string" value="linked:string" description="Issues linked to a pull request" example='linked:pr' />

            <item tag="string" value="mentions:string" description="Issues / Pull Requests that mention username" example='mentions:octocat' />

            <item tag="string" value="team:string" description="Issues / Pull Requests by team mentioned" example='team:github/docs' />

        </category>

        <category value="Issues/Pull Requests/Discussions">

            <item tag="is" value="is:closed" description="Search for closed Issues / Pull Requests" example="type:issue is:closed" />

            <item tag="is" value="is:open" description="Search for open Issues / Pull Requests" example="type:issue is:open" />

            <item tag="no" value="no:assignee" description="Issues/Pull Request/Discussions with no assignee" example='type:pr no:assignee' />

            <item tag="no" value="no:label" description="Issues/Pull Request/Discussions with no label" example='type:issue no:label' />

            <item tag="no" value="no:milestone" description="Issues/Pull Request/Discussions with no milestone" example='no:milestone' />

            <item tag="no" value="no:project" description="Issues/Pull Request/Discussions with no project" example='no:project' />

            <item tag="num" value="comments:>num" description="Issues/Pull Request/Discussions with more than x comments" example='comments:>100' />

            <item tag="num" value="reactions:>num" description="Issues/Pull Request/Discussions with more than x reactions" example='reactions:>10' />

            <item tag="string" value="label:string" description="Issues / Pull Requests with label" example="label:bug" />

            <item tag="date" value="created:YYYY-MM-DD" description="Issues/PRs created on a specific date" example='created:2023-01-01' />

            <item tag="date" value="updated:YYYY-MM-DD" description="Issues/PRs updated on a specific date" example='updated:>2023-01-10' />

        </category>

        <category value="Pull Requests">

            <item tag="bool" value="draft:true" description="Pull Requests with pending status checks" example='draft:true' />

            <item tag="const" value="review:approved" description="Pull Requests that have been approved" example='review:approved' />

            <item tag="const" value="review:changes_requested" description="Pull Requests where changes have been requested" example='review:changes_requested' />

            <item tag="const" value="status:pending" description="Pull Requests with pending status checks" example='status:pending' />

            <item tag="date" value="merged:YYYY-MM-DD" description="Pull Requests merged on a specific date" example='merged:>2023-02-01' />

            <item tag="is" value="is:draft" description="Pull Requests with pending status checks" example='is:draft' />

        </category>

        <category value="Repository Search Operators">

            <item tag="bool" value="archived:bool" description="Include/Exclude archived repositories" example="archived:true" />

            <item tag="bool" value="mirror:bool" description="Include/Exclude mirrored repositories" example="mirror:true" />

            <item tag="date" value="created:YYYY-MM-DD" description="Repositories created on a specific date" example="created:>2025-01-01" />

            <item tag="date" value="pushed:YYYY-MM-DD" description="Repositories pushed to on a specific date" example="pushed:>2025-01-01" />

            <item tag="in" value="in:description" description="Search within repository descriptions" example="framework in:description" />

            <item tag="in" value="in:name" description="Search within repository names" example="python in:name" />

            <item tag="in" value="in:readme" description="Search within README files" example="python in:readme" />

            <item tag="is" value="is:private" description="Private repositories (for authenticated users)" example="is:private" />

            <item tag="is" value="is:public" description="Public repositories" example="is:public" />

            <item tag="num" value="forks:num" description="Repositories with a specific number of forks" example="forks:>100" />

            <item tag="num" value="size:num" description="Repositories of a certain size (in KB)" example="size:>10000" />

            <item tag="num" value="stars:num" description="Repositories with a specific number of stars" example="stars:>100000" />

            <item tag="num" value="topics:num" description="Repositories with more than x topics" example='topics:>10' />

            <item tag="string" value="language:string" description="Repositories of a specific programming language" example="language:maxscript" />

            <item tag="string" value="org:string" description="Repositories within a specific organization" example="python org:github" />

            <item tag="string" value="topic:string" description="Repositories with a specific topic" example="python topic:sublime" />

            <item tag="string" value="user:string" description="Repositories owned by a specific user" example="created:>2020-01-01 user:pythontoday" />

        </category>

        <category value="User Search Operators">

            <item tag="const" value="type:issue" description="Search for issues" example="type:issue" />

            <item tag="const" value="type:org" description="Search for organizations" example="type:org" />

            <item tag="const" value="type:pr" description="Search for pull requests" example="type:pr" />

            <item tag="const" value="type:user" description="Search for users" example="type:user" />

            <item tag="date" value="created:YYYY-MM-DD" description="User accounts created on a specific date" example='created:2023-01-01' />

            <item tag="in" value="in:bio" description="Search within user bios" example='developer in:bio' />

            <item tag="in" value="in:email" description="Search within email addresses" example='"<EMAIL>" in:email' />

            <item tag="in" value="in:fullname" description="Search within full names" example='"John Doe" in:fullname' />

            <item tag="in" value="in:name" description="Search within usernames" example='"john_doe" in:name' />

            <item tag="num" value="followers:num" description="Users with a specific number of followers" example='followers:>100' />

            <item tag="string" value="language:string" description="Users who primarily use a specific language" example='language:Python' />

            <item tag="string" value="location:string" description="Users in a specific location" example='location:oslo' />

        </category>

    </search_operators>

</template>



INPUT_PROMPT: Produce a search query to find python code that contains llm interactions using the model "o3-mini"

## Message 2

None

**Formulating search query**

I should craft a search query for GitHub, specifically to look for Python code that contains LLM interactions using the model "o3-mini." From the XML template, I can clearly see operators like searching by language and using strings for matching terms.

I’ll go with something like: `language:Python "llm" "o3-mini"`.

This leverages the `language` operator to ensure results are Python code and uses quotes to search for the exact phrases. Should be a clean search!

**Creating the search query**

For this, I need a query that finds Python code containing references to "o3-mini" and "LLM interactions." Based on the search operators, this can be achieved by using the `language:Python` operator to restrict the language, and then using quotes for exact phrases like `"o3-mini"` and `"llm"`. 

So, a good search query would be: `language:Python "o3-mini" "llm"`.

If necessary, I can refine it further by adding extra terms like "interaction," but this should work for now!

## Message 3

```plaintext
language:Python "o3-mini" "llm"
```
