# Auto-snapshot script explanation

Conversation ID: 67d16510-0224-8008-9548-02c2a9a88f23

## Message 1

The attached scripts reside within the `script` folder of a React/TypeScript project, you should famialize yourself with it to such degree that you can explain *exactly* what it is and *exactly* what purpose it serve:



    # Dir `scripts`



    ### File Structure



    ```

    ├── auto-snapshot.js

    ├── dev-with-snapshots.js

    ├── refresh-screenshots.js

    ├── screenshot-manager.js

    ├── tidy-screenshots.js

    ├── config

    │   └── screenshot-config.js

    └── utils

        └── screenshot-utils.js

    ```





    #### `auto-snapshot.js`



    ```javascript

    import { exec } from "child_process";

    import { promisify } from "util";

    import fs from "fs";

    import path from "path";

    import { fileURLToPath } from "url";

    import puppeteer from "puppeteer";



    const execAsync = promisify(exec);



    // Get current directory

    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "..");



    // Configuration

    const CONFIG = {

        // Development server port

        port: 5173,



        // Delay before capturing screenshots (ms)

        captureDelay: 2000,



        // AI analysis directory

        aiDir: path.join(projectRoot, ".ai-analysis"),



        // Latest screenshots directory

        latestDir: path.join(projectRoot, ".ai-analysis", "latest"),



        // Maximum number of snapshots to keep

        maxSnapshots: 10,



        // Pages to capture

        pages: [

            { path: "/", name: "home" },

            { path: "/hvem-er-vi", name: "about" },

            { path: "/hva-vi-gjor", name: "services" },

            { path: "/prosjekter", name: "projects" },

            { path: "/kontakt", name: "contact" },

        ],



        // Viewport to use for AI analysis

        viewport: { width: 1440, height: 900, name: "desktop" },

    };



    /**

     * Ensures the AI analysis directory exists

     */

    function setupAiDirectory() {

        // Create main AI directory if it doesn't exist

        if (!fs.existsSync(CONFIG.aiDir)) {

            fs.mkdirSync(CONFIG.aiDir, { recursive: true });

        }



        // Create latest directory if it doesn't exist

        if (!fs.existsSync(CONFIG.latestDir)) {

            fs.mkdirSync(CONFIG.latestDir, { recursive: true });

        }



        // Create a metadata.json file if it doesn't exist

        const metadataPath = path.join(CONFIG.aiDir, "metadata.json");

        if (!fs.existsSync(metadataPath)) {

            fs.writeFileSync(

                metadataPath,

                JSON.stringify(

                    {

                        snapshots: [],

                        lastSnapshot: null,

                    },

                    null,

                    2

                )

            );

        }



        return metadataPath;

    }



    /**

     * Updates the metadata file with snapshot information

     */

    function updateMetadata(metadataPath, snapshotId) {

        const metadata = JSON.parse(fs.readFileSync(metadataPath, "utf8"));



        // Add new snapshot

        metadata.snapshots.push({

            id: snapshotId,

            timestamp: new Date().toISOString(),

            pages: CONFIG.pages.map((page) => page.name),

        });



        // Keep only the most recent snapshots

        if (metadata.snapshots.length > CONFIG.maxSnapshots) {

            const removedSnapshots = metadata.snapshots.slice(

                0,

                metadata.snapshots.length - CONFIG.maxSnapshots

            );

            metadata.snapshots = metadata.snapshots.slice(

                metadata.snapshots.length - CONFIG.maxSnapshots

            );



            // Remove old snapshot directories

            removedSnapshots.forEach((snapshot) => {

                const snapshotDir = path.join(CONFIG.aiDir, snapshot.id);

                if (fs.existsSync(snapshotDir)) {

                    fs.rmSync(snapshotDir, { recursive: true, force: true });

                }

            });

        }



        // Update last snapshot

        metadata.lastSnapshot = snapshotId;



        // Write updated metadata

        fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));



        return metadata;

    }



    /**

     * Captures a screenshot of a page

     */

    async function capturePage(page, snapshotDir) {

        const url = `http://localhost:${CONFIG.port}${page.path}`;

        const outputPath = path.join(snapshotDir, `${page.name}.png`);

        const latestOutputPath = path.join(CONFIG.latestDir, `${page.name}.png`);



        try {

            // Use puppeteer directly instead of via a temporary script

            const browser = await puppeteer.launch();

            const browserPage = await browser.newPage();



            await browserPage.setViewport({

                width: CONFIG.viewport.width,

                height: CONFIG.viewport.height,

            });



            await browserPage.goto(url, {

                waitUntil: "networkidle2",

                timeout: 10000,

            });



            // Wait for any animations

            await new Promise((resolve) =>

                setTimeout(resolve, CONFIG.captureDelay)

            );



            // Take the screenshot

            await browserPage.screenshot({

                path: outputPath,

                fullPage: true,

            });



            // Copy to latest directory

            fs.copyFileSync(outputPath, latestOutputPath);



            await browser.close();



            console.log(`Captured ${page.name} page`);

            return { outputPath, latestOutputPath };

        } catch (error) {

            console.error(`Error capturing ${page.name} page:`, error.message);

            return null;

        }

    }



    /**

     * Generates an AI-readable summary of the snapshot

     */

    function generateAiSummary(snapshotDir, metadata) {

        const summaryPath = path.join(snapshotDir, "ai-summary.md");

        const latestSummaryPath = path.join(CONFIG.latestDir, "ai-summary.md");



        const summary = `# Website Snapshot for AI Analysis



    ## Snapshot Information

    - **ID**: ${metadata.lastSnapshot}

    - **Timestamp**: ${new Date().toISOString()}

    - **Pages Captured**: ${CONFIG.pages.map((page) => page.name).join(", ")}



    ## Screenshot Paths

    ${CONFIG.pages

        .map(

            (page) =>

                `- **${page.name}**: \`${path.join(

                    snapshotDir,

                    `${page.name}.png`

                )}\``

        )

        .join("\n")}



    ## Latest Screenshot Paths (Always Current)

    ${CONFIG.pages

        .map(

            (page) =>

                `- **${page.name}**: \`${path.join(

                    CONFIG.latestDir,

                    `${page.name}.png`

                )}\``

        )

        .join("\n")}



    ## Viewport Information

    - **Width**: ${CONFIG.viewport.width}px

    - **Height**: ${CONFIG.viewport.height}px

    - **Name**: ${CONFIG.viewport.name}



    ## Usage Instructions

    These screenshots can be used by AI assistants to understand the current state of the website.

    When making code changes, reference these images to understand the visual impact of your changes.



    ## Access Paths

    To reference the specific snapshot in conversations, use:

    \`\`\`

    /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/${

            metadata.lastSnapshot

        }

    \`\`\`



    To always reference the latest screenshots, use:

    \`\`\`

    /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest

    \`\`\`

    `;



        // Write to snapshot directory

        fs.writeFileSync(summaryPath, summary);



        // Write to latest directory

        fs.writeFileSync(latestSummaryPath, summary);



        return { summaryPath, latestSummaryPath };

    }



    /**

     * Main function to capture snapshots for AI analysis

     */

    async function captureForAiAnalysis() {

        try {

            console.log("Capturing website snapshots for AI analysis...");



            // Setup AI directory

            const metadataPath = setupAiDirectory();



            // Create a unique ID for this snapshot

            const snapshotId = `snapshot-${Date.now()}`;

            const snapshotDir = path.join(CONFIG.aiDir, snapshotId);

            fs.mkdirSync(snapshotDir, { recursive: true });



            // Capture screenshots for each page

            for (const page of CONFIG.pages) {

                await capturePage(page, snapshotDir);

            }



            // Update metadata

            const metadata = updateMetadata(metadataPath, snapshotId);



            // Generate AI summary

            const { summaryPath, latestSummaryPath } = generateAiSummary(

                snapshotDir,

                metadata

            );



            console.log(`\nSnapshot captured successfully: ${snapshotId}`);

            console.log(`AI summary generated at: ${summaryPath}`);

            console.log(`Latest summary updated at: ${latestSummaryPath}`);



            console.log(

                `\nTo reference the specific snapshot in conversations, use:`

            );

            console.log(

                `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/${metadata.lastSnapshot}`

            );



            console.log(`\nTo always reference the latest screenshots, use:`);

            console.log(

                `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`

            );



            return {

                success: true,

                snapshotId,

                snapshotDir,

                latestDir: CONFIG.latestDir,

                summaryPath,

                latestSummaryPath,

            };

        } catch (error) {

            console.error("Error capturing snapshots for AI analysis:", error);

            return {

                success: false,

                error: error.message,

            };

        }

    }



    // Run the capture process

    captureForAiAnalysis();

    ```





    #### `dev-with-snapshots.js`



    ```javascript

    import { exec, spawn } from "child_process";

    import { promisify } from "util";

    import fs from "fs";

    import path from "path";

    import { fileURLToPath } from "url";

    import { createServer } from "http";



    const execAsync = promisify(exec);



    // Get current directory

    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "..");



    // Configuration

    const CONFIG = {

        // Minimum time between snapshots (ms)

        snapshotCooldown: 30000, // 30 seconds



        // Port for the snapshot trigger server

        triggerPort: 5174,



        // Development server port

        devPort: 5173,



        // Latest screenshots directory

        latestDir: path.join(projectRoot, ".ai-analysis", "latest"),

    };



    // Track the last snapshot time

    let lastSnapshotTime = 0;



    /**

     * Starts the development server

     */

    function startDevServer() {

        console.log("Starting development server...");



        const devProcess = spawn("npm", ["run", "dev"], {

            cwd: projectRoot,

            stdio: "inherit",

            shell: true,

        });



        devProcess.on("error", (error) => {

            console.error("Error starting development server:", error);

        });



        return devProcess;

    }



    /**

     * Takes a snapshot if enough time has passed since the last one

     */

    async function takeSnapshotIfNeeded() {

        const now = Date.now();



        // Check if enough time has passed since the last snapshot

        if (now - lastSnapshotTime < CONFIG.snapshotCooldown) {

            console.log("Snapshot cooldown active, skipping...");

            return false;

        }



        // Update the last snapshot time

        lastSnapshotTime = now;



        try {

            console.log("Taking snapshot for AI analysis...");

            await execAsync(`npm run ai:snapshot`, { cwd: projectRoot });



            // Display the latest directory path for easy reference

            console.log("\n=== LATEST SCREENSHOTS READY FOR AI REFERENCE ===");

            console.log(

                `Path: /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`

            );

            console.log("=================================================\n");



            return true;

        } catch (error) {

            console.error("Error taking snapshot:", error.message);

            return false;

        }

    }



    /**

     * Creates a simple HTTP server to trigger snapshots

     */

    function createTriggerServer() {

        const server = createServer(async (req, res) => {

            // Set CORS headers

            res.setHeader("Access-Control-Allow-Origin", "*");

            res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");

            res.setHeader("Access-Control-Allow-Headers", "Content-Type");



            // Handle preflight requests

            if (req.method === "OPTIONS") {

                res.writeHead(204);

                res.end();

                return;

            }



            // Handle snapshot trigger

            if (req.url === "/trigger-snapshot") {

                console.log("Snapshot triggered via HTTP request");



                const success = await takeSnapshotIfNeeded();



                res.writeHead(200, { "Content-Type": "application/json" });

                res.end(

                    JSON.stringify({

                        success,

                        message: success

                            ? "Snapshot taken successfully"

                            : "Snapshot skipped (cooldown active)",

                        latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,

                    })

                );

                return;

            }



            // Handle status check

            if (req.url === "/status") {

                res.writeHead(200, { "Content-Type": "application/json" });

                res.end(

                    JSON.stringify({

                        status: "running",

                        lastSnapshot: new Date(lastSnapshotTime).toISOString(),

                        cooldownActive:

                            Date.now() - lastSnapshotTime < CONFIG.snapshotCooldown,

                        latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,

                    })

                );

                return;

            }



            // Handle latest path request

            if (req.url === "/latest-path") {

                res.writeHead(200, { "Content-Type": "application/json" });

                res.end(

                    JSON.stringify({

                        latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,

                    })

                );

                return;

            }



            // Default response

            res.writeHead(404, { "Content-Type": "application/json" });

            res.end(JSON.stringify({ error: "Not found" }));

        });



        server.listen(CONFIG.triggerPort, () => {

            console.log(

                `Snapshot trigger server running at http://localhost:${CONFIG.triggerPort}`

            );

            console.log(

                `- To trigger a snapshot: http://localhost:${CONFIG.triggerPort}/trigger-snapshot`

            );

            console.log(

                `- To check status: http://localhost:${CONFIG.triggerPort}/status`

            );

            console.log(

                `- To get latest path: http://localhost:${CONFIG.triggerPort}/latest-path`

            );

        });



        return server;

    }



    /**

     * Creates a file watcher to trigger snapshots on changes

     */

    function createFileWatcher() {

        // Directories to watch

        const watchDirs = [path.join(projectRoot, "src")];



        // Extensions to watch

        const watchExtensions = [

            ".ts",

            ".tsx",

            ".js",

            ".jsx",

            ".css",

            ".scss",

            ".html",

        ];



        // Create watchers for each directory

        const watchers = watchDirs.map((dir) => {

            console.log(`Watching directory for changes: ${dir}`);



            return fs.watch(

                dir,

                { recursive: true },

                async (eventType, filename) => {

                    if (!filename) return;



                    // Check if the file extension should trigger a snapshot

                    const ext = path.extname(filename).toLowerCase();

                    if (!watchExtensions.includes(ext)) return;



                    console.log(`File changed: ${filename}`);

                    await takeSnapshotIfNeeded();

                }

            );

        });



        return watchers;

    }



    /**

     * Main function to run development with automatic snapshots

     */

    async function devWithSnapshots() {

        try {

            console.log("Starting development with automatic snapshots...");



            // Start the development server

            const devProcess = startDevServer();



            // Wait for the development server to start

            console.log("Waiting for development server to start...");

            await new Promise((resolve) => setTimeout(resolve, 5000));



            // Create the trigger server

            const triggerServer = createTriggerServer();



            // Create file watchers

            const watchers = createFileWatcher();



            // Take an initial snapshot

            console.log("Taking initial snapshot...");

            await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait for dev server to be fully ready

            await takeSnapshotIfNeeded();



            console.log("\nDevelopment with automatic snapshots is running!");

            console.log("- Development server: http://localhost:5173");

            console.log(

                "- Snapshot trigger: http://localhost:5174/trigger-snapshot"

            );

            console.log(

                "- File changes in src/ will automatically trigger snapshots"

            );

            console.log("- Latest screenshots are always available at:");

            console.log(

                `  /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`

            );

            console.log("- Press Ctrl+C to stop");



            // Handle process termination

            process.on("SIGINT", () => {

                console.log("Shutting down...");



                // Close file watchers

                watchers.forEach((watcher) => watcher.close());



                // Close trigger server

                triggerServer.close();



                // Kill development server

                devProcess.kill();



                process.exit(0);

            });

        } catch (error) {

            console.error("Error starting development with snapshots:", error);

        }

    }



    // Run the main function

    devWithSnapshots();

    ```





    #### `refresh-screenshots.js`



    ```javascript

    import { exec } from "child_process";

    import { promisify } from "util";

    import path from "path";

    import { fileURLToPath } from "url";



    const execAsync = promisify(exec);



    // Get current directory

    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "..");



    /**

     * Runs a screenshot manager command

     * @param {string} command The command to run

     * @param {string[]} args Additional arguments

     */

    async function runScreenshotCommand(command, args = []) {

        const commandString = `node ${path.join(

            projectRoot,

            "scripts",

            "screenshot-manager.js"

        )} ${command} ${args.join(" ")}`;

        console.log(`Running: ${commandString}`);



        try {

            const { stdout, stderr } = await execAsync(commandString);

            if (stdout) console.log(stdout);

            if (stderr) console.error(stderr);

        } catch (error) {

            console.error(`Error running ${command}:`, error.message);

            throw error;

        }

    }



    /**

     * Refreshes screenshots by capturing, tidying, and cleaning up

     */

    async function refreshScreenshots() {

        try {

            console.log("Starting screenshot refresh process...");



            // Step 1: Capture new screenshots

            console.log("\n=== Step 1: Capturing new screenshots ===");

            await runScreenshotCommand("capture");



            // Step 2: Tidy up the screenshots directory

            console.log("\n=== Step 2: Tidying up screenshots directory ===");

            await runScreenshotCommand("tidy");



            // Step 3: Clean up old screenshots (keep last 7 days)

            console.log("\n=== Step 3: Cleaning up old screenshots ===");

            await runScreenshotCommand("clean", ["7"]);



            console.log("\nScreenshot refresh process completed successfully!");

            console.log(

                "You can view the latest screenshots in the screenshot-report.html file."

            );

        } catch (error) {

            console.error("Screenshot refresh process failed:", error.message);

        }

    }



    // Run the refresh process

    refreshScreenshots();

    ```





    #### `screenshot-manager.js`



    ```javascript

    import { exec } from "child_process";

    import { promisify } from "util";

    import fs from "fs";

    import path from "path";

    import { CONFIG, COMMANDS } from "./config/screenshot-config.js";

    import {

        captureScreenshots,

        cleanOldScreenshots,

        tidyScreenshots,

        ensureDirectories,

    } from "./utils/screenshot-utils.js";



    const execAsync = promisify(exec);



    /**

     * Displays help information

     */

    function showHelp() {

        console.log(`

    Screenshot Manager - Utility for managing website screenshots



    Usage:

      node screenshot-manager.js [command] [options]



    Commands:

      capture                 Capture screenshots now

      clean [days]           Clean screenshots older than specified days (default: ${CONFIG.defaultRetention})

      compare [dir1] [dir2]  Compare two screenshot directories

      schedule [interval]    Schedule automatic captures (interval in hours, default: 24)

      unschedule            Remove scheduled captures

      tidy                  Tidy up the screenshots directory

      help                  Show this help message



    Examples:

      node screenshot-manager.js capture

      node screenshot-manager.js clean 3

      node screenshot-manager.js schedule 12

      node screenshot-manager.js tidy

    `);

    }



    /**

     * Schedules automatic screenshot captures

     * @param {number} interval Interval in hours

     */

    async function scheduleCaptures(interval = 24) {

        try {

            const isWindows = process.platform === "win32";



            if (isWindows) {

                const scriptPath = path.join(

                    CONFIG.projectRoot,

                    "scripts",

                    "run-capture.bat"

                );

                const batchContent = `@echo off\ncd ${CONFIG.projectRoot}\nnode ${CONFIG.captureScript}\n`;

                fs.writeFileSync(scriptPath, batchContent);



                await execAsync(

                    `schtasks /create /tn "${CONFIG.windowsTaskName}" /tr "${scriptPath}" /sc HOURLY /mo ${interval} /f`

                );

                console.log(

                    `Scheduled screenshot capture every ${interval} hours using Windows Task Scheduler`

                );

            } else {

                const cronCommand = `0 */${interval} * * * cd ${CONFIG.projectRoot} && node ${CONFIG.captureScript}`;

                const { stdout: currentCrontab } = await execAsync(

                    'crontab -l 2>/dev/null || echo ""'

                );



                if (currentCrontab.includes(CONFIG.captureScript)) {

                    console.log("Screenshot capture is already scheduled");

                    return;

                }



                const newCrontab =

                    currentCrontab +

                    `\n# Ringerike Landskap Screenshot Capture\n${cronCommand}\n`;

                const tempFile = path.join(CONFIG.projectRoot, "temp-crontab");

                fs.writeFileSync(tempFile, newCrontab);

                await execAsync(`crontab ${tempFile}`);

                fs.unlinkSync(tempFile);



                console.log(

                    `Scheduled screenshot capture every ${interval} hours using crontab`

                );

            }

        } catch (error) {

            console.error("Error scheduling captures:", error.message);

        }

    }



    /**

     * Removes scheduled screenshot captures

     */

    async function unscheduleCaptures() {

        try {

            const isWindows = process.platform === "win32";



            if (isWindows) {

                try {

                    await execAsync(

                        `schtasks /delete /tn "${CONFIG.windowsTaskName}" /f`

                    );

                    console.log(

                        "Scheduled screenshot capture removed from Windows Task Scheduler"

                    );

                } catch (error) {

                    console.log("No scheduled task found");

                }

            } else {

                const { stdout: currentCrontab } = await execAsync(

                    'crontab -l 2>/dev/null || echo ""'

                );



                if (!currentCrontab.includes(CONFIG.captureScript)) {

                    console.log("No scheduled screenshot capture found");

                    return;

                }



                const newCrontab = currentCrontab

                    .split("\n")

                    .filter(

                        (line) =>

                            !line.includes(CONFIG.captureScript) &&

                            !line.includes(

                                "# Ringerike Landskap Screenshot Capture"

                            )

                    )

                    .join("\n");



                const tempFile = path.join(CONFIG.projectRoot, "temp-crontab");

                fs.writeFileSync(tempFile, newCrontab);

                await execAsync(`crontab ${tempFile}`);

                fs.unlinkSync(tempFile);



                console.log("Scheduled screenshot capture removed from crontab");

            }

        } catch (error) {

            console.error("Error removing scheduled captures:", error.message);

        }

    }



    /**

     * Main function to handle commands

     */

    async function main() {

        // Ensure directories exist

        ensureDirectories();



        const [command = COMMANDS.help, ...args] = process.argv.slice(2);



        switch (command) {

            case COMMANDS.capture:

                await captureScreenshots();

                break;



            case COMMANDS.clean:

                const days = parseInt(args[0]) || CONFIG.defaultRetention;

                await cleanOldScreenshots(days);

                break;



            case COMMANDS.schedule:

                const interval = parseInt(args[0]) || 24;

                await scheduleCaptures(interval);

                break;



            case COMMANDS.unschedule:

                await unscheduleCaptures();

                break;



            case COMMANDS.tidy:

                await tidyScreenshots();

                break;



            case COMMANDS.help:

            default:

                showHelp();

                break;

        }

    }



    main().catch((error) => {

        console.error("Error:", error);

        process.exit(1);

    });

    ```





    #### `tidy-screenshots.js`



    ```javascript

    import fs from "fs";

    import path from "path";

    import { fileURLToPath } from "url";



    // Get current directory

    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "..");



    // Screenshot directory

    const screenshotsDir = path.join(projectRoot, "screenshots");



    /**

     * Creates a .gitignore file in the screenshots directory

     */

    function createGitignore() {

        const gitignorePath = path.join(screenshotsDir, ".gitignore");

        const gitignoreContent = `# Ignore all files in this directory except README.md and .gitignore

    *

    !README.md

    !.gitignore

    !.gitkeep



    # Allow the latest directory but ignore its contents

    !latest/

    latest/*

    !latest/.gitkeep



    # Allow the directory structure but not the actual screenshots

    !*/

    */*/

    `;



        fs.writeFileSync(gitignorePath, gitignoreContent);

        console.log("Created .gitignore file in screenshots directory");

    }



    /**

     * Creates .gitkeep files to preserve directory structure

     */

    function createGitkeepFiles() {

        // Create .gitkeep in the screenshots directory

        fs.writeFileSync(path.join(screenshotsDir, ".gitkeep"), "");



        // Create .gitkeep in the latest directory

        const latestDir = path.join(screenshotsDir, "latest");

        if (fs.existsSync(latestDir)) {

            fs.writeFileSync(path.join(latestDir, ".gitkeep"), "");



            // Create .gitkeep in viewport subdirectories

            ["mobile", "tablet", "desktop"].forEach((viewport) => {

                const viewportDir = path.join(latestDir, viewport);

                if (fs.existsSync(viewportDir)) {

                    fs.writeFileSync(path.join(viewportDir, ".gitkeep"), "");

                }

            });

        }



        console.log("Created .gitkeep files to preserve directory structure");

    }



    /**

     * Removes the legacy backup directory

     */

    function removeLegacyBackup() {

        const legacyBackupDir = path.join(screenshotsDir, "legacy-backup");



        if (fs.existsSync(legacyBackupDir)) {

            try {

                fs.rmSync(legacyBackupDir, { recursive: true, force: true });

                console.log("Removed legacy-backup directory");

            } catch (error) {

                console.error(

                    "Error removing legacy-backup directory:",

                    error.message

                );

            }

        } else {

            console.log("No legacy-backup directory found");

        }

    }



    /**

     * Removes any loose files in the screenshots directory

     */

    function removeLooseFiles() {

        const files = fs.readdirSync(screenshotsDir);



        const looseFiles = files.filter((file) => {

            const filePath = path.join(screenshotsDir, file);

            const isDirectory = fs.statSync(filePath).isDirectory();



            // Keep directories with timestamp format or 'latest'

            if (isDirectory) {

                return !(

                    file === "latest" ||

                    /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(file)

                );

            }



            // Keep README.md, .gitignore, and .gitkeep

            if (

                [

                    "README.md",

                    ".gitignore",

                    ".gitkeep",

                    "screenshot-report.html",

                ].includes(file)

            ) {

                return false;

            }



            // All other files should be removed

            return true;

        });



        if (looseFiles.length === 0) {

            console.log("No loose files found in screenshots directory");

            return;

        }



        console.log(

            `Found ${looseFiles.length} loose files in screenshots directory`

        );



        looseFiles.forEach((file) => {

            const filePath = path.join(screenshotsDir, file);

            try {

                fs.rmSync(filePath, { force: true });

                console.log(`Removed loose file: ${file}`);

            } catch (error) {

                console.error(`Error removing ${file}:`, error.message);

            }

        });

    }



    /**

     * Optimizes the screenshot report by reducing image sizes

     */

    function optimizeScreenshotReport() {

        const reportPath = path.join(screenshotsDir, "screenshot-report.html");



        if (!fs.existsSync(reportPath)) {

            console.log("Screenshot report not found");

            return;

        }



        try {

            let reportContent = fs.readFileSync(reportPath, "utf8");



            // Add lazy loading to images

            reportContent = reportContent.replace(

                /<img src="([^"]+)"/g,

                '<img src="$1" loading="lazy"'

            );



            // Add viewport meta tag for better mobile display if not already present

            if (!reportContent.includes("viewport")) {

                reportContent = reportContent.replace(

                    "<head>",

                    '<head>\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">'

                );

            }



            // Add a note about the tidied directory

            reportContent = reportContent.replace(

                '<p class="timestamp">Generated',

                '<p class="note">Screenshots directory has been tidied up for better organization.</p>\n        <p class="timestamp">Generated'

            );



            fs.writeFileSync(reportPath, reportContent);

            console.log("Optimized screenshot report");

        } catch (error) {

            console.error("Error optimizing screenshot report:", error.message);

        }

    }



    /**

     * Main function to tidy up the screenshots directory

     */

    function tidyScreenshotsDirectory() {

        console.log("Tidying up screenshots directory...");



        // Create .gitignore file

        createGitignore();



        // Create .gitkeep files

        createGitkeepFiles();



        // Remove legacy backup

        removeLegacyBackup();



        // Remove loose files

        removeLooseFiles();



        // Optimize screenshot report

        optimizeScreenshotReport();



        console.log("Screenshots directory tidied up successfully");

    }



    // Run the tidy function

    tidyScreenshotsDirectory();

    ```





    #### `config\screenshot-config.js`



    ```javascript

    import path from "path";

    import { fileURLToPath } from "url";



    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "../..");



    export const CONFIG = {

        // Core paths

        projectRoot,

        screenshotsDir: path.join(projectRoot, "screenshots"),

        aiAnalysisDir: path.join(projectRoot, ".ai-analysis"),



        // Latest directories

        latestScreenshotsDir: path.join(projectRoot, "screenshots", "latest"),

        latestAiDir: path.join(projectRoot, ".ai-analysis", "latest"),



        // Scripts

        captureScript: path.join(projectRoot, "capture-website.js"),

        tidyScript: path.join(projectRoot, "scripts", "tidy-screenshots.js"),



        // Development settings

        devPort: 5173,

        triggerPort: 5174,

        snapshotCooldown: 30000, // 30 seconds



        // Screenshot settings

        defaultRetention: 7,

        watchExtensions: [".ts", ".tsx", ".js", ".jsx", ".css", ".scss", ".html"],



        // Task scheduling

        windowsTaskName: "RingerikeLandskapScreenshots",

        cronJobLabel: "# Ringerike Landskap Screenshot Capture",

    };



    export const COMMANDS = {

        capture: "capture",

        clean: "clean",

        compare: "compare",

        help: "help",

        schedule: "schedule",

        unschedule: "unschedule",

        tidy: "tidy",

    };

    ```





    #### `utils\screenshot-utils.js`



    ```javascript

    import { exec } from "child_process";

    import { promisify } from "util";

    import fs from "fs";

    import path from "path";

    import { CONFIG } from "../config/screenshot-config.js";



    const execAsync = promisify(exec);



    /**

     * Captures screenshots using the capture script

     */

    export async function captureScreenshots() {

        try {

            console.log("Capturing screenshots...");

            await execAsync(`node ${CONFIG.captureScript}`);

            console.log("Screenshots captured successfully");

            return true;

        } catch (error) {

            console.error("Error capturing screenshots:", error.message);

            return false;

        }

    }



    /**

     * Cleans up screenshots older than the specified number of days

     * @param {number} days Number of days to keep screenshots

     */

    export async function cleanOldScreenshots(days = CONFIG.defaultRetention) {

        try {

            const baseDir = CONFIG.screenshotsDir;

            if (!fs.existsSync(baseDir)) {

                console.log("Screenshots directory does not exist");

                return false;

            }



            const now = new Date().getTime();

            const maxAge = days * 24 * 60 * 60 * 1000;



            const dirs = fs

                .readdirSync(baseDir)

                .filter((name) => {

                    const fullPath = path.join(baseDir, name);

                    return (

                        fs.statSync(fullPath).isDirectory() &&

                        name !== "latest" &&

                        /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(name)

                    );

                })

                .map((name) => ({

                    name,

                    path: path.join(baseDir, name),

                    time: fs.statSync(path.join(baseDir, name)).mtime.getTime(),

                }))

                .filter((dir) => now - dir.time > maxAge);



            if (dirs.length === 0) {

                console.log(`No screenshots older than ${days} days found`);

                return true;

            }



            console.log(

                `Found ${dirs.length} screenshot directories older than ${days} days`

            );



            dirs.forEach((dir) => {

                console.log(`Removing old snapshot: ${dir.name}`);

                fs.rmSync(dir.path, { recursive: true, force: true });

            });



            console.log(`Cleaned up ${dirs.length} old screenshot directories`);

            return true;

        } catch (error) {

            console.error("Error cleaning old screenshots:", error);

            return false;

        }

    }



    /**

     * Runs the tidy script to organize screenshots

     */

    export async function tidyScreenshots() {

        try {

            console.log("Tidying screenshots...");

            await execAsync(`node ${CONFIG.tidyScript}`);

            console.log("Screenshots tidied successfully");

            return true;

        } catch (error) {

            console.error("Error tidying screenshots:", error.message);

            return false;

        }

    }



    /**

     * Ensures screenshot directories exist

     */

    export function ensureDirectories() {

        const dirs = [

            CONFIG.screenshotsDir,

            CONFIG.aiAnalysisDir,

            CONFIG.latestScreenshotsDir,

            CONFIG.latestAiDir,

        ];



        dirs.forEach((dir) => {

            if (!fs.existsSync(dir)) {

                fs.mkdirSync(dir, { recursive: true });

            }

        });

    }

    ```


## Message 2

**Capturing website state**

I’m gathering info on each script in the `script` folder, beginning with `auto-snapshot.js`. This script captures the current website state using Puppeteer, streamlining the process of taking a snapshot.

**Running and triggering**

I’m detailing `dev-with-snapshots.js`, which launches a development server, sets up watchers for file changes to trigger snapshots, and allows manual snapshots via an HTTP server on port 5174.

**Snapshot management**

`Screenshot-manager.js` is a command-line utility for managing screenshot tasks, including capturing, cleaning, scheduling, and organizing. It efficiently handles these processes through various commands.

## Message 3

Below is a high-level and *exact* walkthrough of each file in the `scripts` folder. The overall purpose of these scripts is to manage automated screenshot captures, store them in organized folders, and maintain a “latest” reference for quick AI or human analysis. They collectively provide features such as:

1. **Capturing** website screenshots (using Puppeteer).  
2. **Running** or **watching** a local development server while triggering snapshots on demand or on file changes.  
3. **Tidying** screenshot folders (cleaning old captures, structuring .gitignore, etc.).  
4. **Scheduling** regular captures.  

Below is a file-by-file breakdown.

---

## 1. `auto-snapshot.js`

### **What it is**  
A standalone script that captures one complete set of screenshots for AI analysis. It **does not** handle scheduling, dev server launching, or triggers from watchers. Instead, it runs a single “snapshot” process immediately when called.

### **How it works**  
1. **Configuration (`CONFIG`)**  
   - Specifies server port (5173 by default).  
   - Tells Puppeteer what pages to capture (the `pages` array).  
   - Defines where to store snapshots (`.ai-analysis/`) and how many to keep (`maxSnapshots`).  
   - Sets up a `viewport` size for consistent screenshots.

2. **`setupAiDirectory()`**  
   - Makes sure the `.ai-analysis` folder (and `latest` subfolder) exists.  
   - Creates or updates a `metadata.json` file to track snapshots.

3. **`capturePage()`**  
   - Spawns Puppeteer, visits each page (`url` = `http://localhost:5173/...`).  
   - Waits for `captureDelay` milliseconds (to allow animations or dynamic data to load).  
   - Saves the screenshot both in a timestamped snapshot folder and in `.ai-analysis/latest`.

4. **`updateMetadata()`**  
   - Appends the new snapshot info (including a unique ID: `snapshot-${Date.now()}`) to `metadata.json`.  
   - Removes oldest snapshots if the total exceeds `maxSnapshots`.

5. **`generateAiSummary()`**  
   - Creates a `.md` file summarizing what was captured, listing paths to the screenshots.  
   - Duplicates this summary into the “latest” folder.

6. **`captureForAiAnalysis()`** (the main function)  
   - Sets up directories.  
   - Captures screenshots for each configured page.  
   - Updates metadata.  
   - Generates a summary.  
   - Prints out instructions on how to reference the snapshot(s).

Essentially, **`auto-snapshot.js`** is your “one-shot” automated screenshotter and metadata manager.

---

## 2. `dev-with-snapshots.js`

### **What it is**  
A script that **launches your development server** (`npm run dev`), then **automatically captures** screenshots:
- On a cooldown basis (default 30 seconds) whenever file changes are detected.
- On-demand via a simple local HTTP endpoint.

### **How it works**  
1. **Starts the Dev Server**  
   - Spawns `npm run dev` in a child process.

2. **Cooldown-based Snapshots**  
   - Has a `snapshotCooldown` setting (30s).  
   - If file changes happen in `src/` (or the relevant watch directories), the script checks how long it has been since the last snapshot. If it’s been more than 30s, it triggers `npm run ai:snapshot`.

3. **Local HTTP Server** (Port 5174)  
   - Responds to endpoints like `/trigger-snapshot`, `/status`, `/latest-path`.  
   - `/trigger-snapshot` → calls the snapshot function if the cooldown allows.  
   - `/status` → returns a small JSON with status info (e.g., last snapshot time, is cooldown active, etc.).  
   - This is optional convenience to trigger or check snapshots from a browser or other scripts.

4. **File Watchers**  
   - Watches changes in certain directories (`src`) and file types (`.ts`, `.tsx`, `.js`, `.jsx`, `.css`, `.scss`, `.html`).  
   - On change, attempts a snapshot (if outside cooldown).

5. **Ties everything together**  
   - After launching dev server and watchers, it does an **initial snapshot**.  
   - Continues running until you manually exit (`Ctrl+C`).

In short, **`dev-with-snapshots.js`** is a developer convenience tool. You run it instead of just `npm run dev`, and it keeps `.ai-analysis/latest` updated when you make code changes or request a snapshot.

---

## 3. `refresh-screenshots.js`

### **What it is**  
A small orchestrator that **sequentially** runs three commands (through the `screenshot-manager.js` behind the scenes):

1. **Capture** new screenshots  
2. **Tidy** the screenshots directory  
3. **Clean** older screenshots beyond a certain retention (default 7 days)

### **How it works**  
1. Calls `runScreenshotCommand("capture")` → triggers capturing.  
2. Calls `runScreenshotCommand("tidy")` → organizes the screenshot folder.  
3. Calls `runScreenshotCommand("clean", ["7"])` → removes screenshots older than 7 days.  

Finally, it prints a completion message. The script is basically a one-click refresh of your screenshot environment, so you always have new captures plus a cleaned-up directory.

---

## 4. `screenshot-manager.js`

### **What it is**  
A **command-line utility** script offering multiple commands:  
```
capture, clean, compare, schedule, unschedule, tidy, help
```
Each command delegates tasks to shared utility functions (in `screenshot-utils.js`) or does scheduling-related logic.

### **How it works**  
1. **Processes a “command”** from CLI args.  
2. Maps that command to functions:
   - `capture` → runs `captureScreenshots()`  
   - `clean [days]` → runs `cleanOldScreenshots(days)`  
   - `tidy` → runs `tidyScreenshots()`  
   - `schedule [interval]` → sets up a cron job (Linux/macOS) or a Windows Task for recurring captures  
   - `unschedule` → removes that scheduled job  
   - `compare` → (not shown in detail, but presumably compares screenshot sets)  
   - `help` → prints usage instructions

**`screenshot-manager.js`** is your single entry point to manage all screenshot tasks from the terminal.

---

## 5. `tidy-screenshots.js`

### **What it is**  
A housekeeping script that **organizes** the `screenshots` directory (not `.ai-analysis`). Typically run as a step (or via `screenshot-manager.js tidy`).

### **How it works**  
1. **Creates/Updates `.gitignore`** inside `screenshots` so that the actual images are ignored, except certain keepers (e.g., `README.md`, `.gitkeep`).  
2. **Places `.gitkeep`** in relevant subdirectories (to preserve folder structure in Git).  
3. **Removes** any `legacy-backup` folder if found.  
4. **Removes** “loose” files that do not match the standard naming/time-stamp pattern.  
5. **Optionally modifies** a `screenshot-report.html` to insert lazy loading or a note about tidying.

In essence, **`tidy-screenshots.js`** ensures that your screenshot directories remain well-structured, minimize Git noise, and maintain relevant placeholders.

---

## 6. `config/screenshot-config.js`

### **What it is**  
A **central configuration file** providing:
- Common paths (like `projectRoot`, `screenshotsDir`, `.ai-analysis` paths).  
- Default retention (`defaultRetention`) for old screenshots.  
- Script references (for capturing, tidying).  
- Cron or Windows Task labels.  
- The `COMMANDS` object enumerating each recognized action.

Any script that needs to know the “official” directories or command identifiers pulls from here.

---

## 7. `utils/screenshot-utils.js`

### **What it is**  
A shared library of **helper** functions for screenshot-related tasks:
1. **`captureScreenshots()`**  
   - Calls `node ${CONFIG.captureScript}` (the main capturing script).  
   - Prints success/failure results.

2. **`cleanOldScreenshots(days)`**  
   - Removes timestamped subfolders older than `days`.

3. **`tidyScreenshots()`**  
   - Invokes the `node ${CONFIG.tidyScript}` to do the actual tidying (i.e., calls `tidy-screenshots.js`).

4. **`ensureDirectories()`**  
   - Makes sure the necessary directories exist: `screenshots`, `.ai-analysis`, and each `latest/` path.

Everything here is geared toward reuse by **`screenshot-manager.js`** or other higher-level scripts.

---

# Summary

- **`auto-snapshot.js`**  
  *One-shot script* to capture all pages into `.ai-analysis`, update metadata, and write a summary.

- **`dev-with-snapshots.js`**  
  *Development server + auto-snapshots*. Watches files, runs dev server, triggers snapshots via cooldown or HTTP requests.

- **`refresh-screenshots.js`**  
  *Single command chain* that captures, tidies, and cleans out old screenshots in one go.

- **`screenshot-manager.js`**  
  *Command-line entry point* offering commands like capture, clean, schedule, unschedule, tidy, help.

- **`tidy-screenshots.js`**  
  *Housekeeping script* for the `screenshots/` folder, generating `.gitignore`, `.gitkeep`, removing old/loose files, and updating the `screenshot-report.html`.

- **`config/screenshot-config.js`**  
  *Central config* for directories, script references, and default settings.

- **`utils/screenshot-utils.js`**  
  *Shared utilities* to capture screenshots, clean old ones, tidy them, or ensure structure.

Taken together, these scripts create a fully automated system for generating, maintaining, and referencing screenshots for both development and AI-based analysis.

## Message 4

The attached filestructure represents the particular project they belong to, please propose an optimal way of consolidating the screenshot utility (to execute only on demand) into a single `website-snapshot.js`:



    ```

    ├── .gitignore

    ├── .gitkeep

    ├── capture-website.js

    ├── eslint.config.js

    ├── index.html

    ├── package-lock.json

    ├── package.json

    ├── postcss.config.js

    ├── rl-website-initial-notes.md

    ├── tailwind.config.js

    ├── tsconfig.app.json

    ├── tsconfig.json

    ├── tsconfig.node.json

    ├── vite.config.ts

    ├── public

    │   ├── robots.txt

    │   ├── site.webmanifest

    │   ├── sitemap.xml

    │   └── images

    │       ├── metadata.json

    │       ├── categorized

    │       │   ├── belegg

    │       │   ├── ferdigplen

    │       │   ├── hekk

    │       │   ├── kantstein

    │       │   ├── platting

    │       │   ├── stål

    │       │   ├── støttemur

    │       │   └── trapp-repo

    │       ├── site

    │       └── team

    ├── screenshots

    │   ├── .gitignore

    │   ├── .gitkeep

    │   ├── README.md

    │   ├── screenshot-report.html

    │   └── latest

    │       ├── .gitkeep

    │       ├── desktop

    │       │   ├── about-desktop.html

    │       │   ├── contact-desktop.html

    │       │   ├── home-desktop.html

    │       │   ├── projects-desktop.html

    │       │   └── services-desktop.html

    │       ├── mobile

    │       │   ├── about-mobile.html

    │       │   ├── contact-mobile.html

    │       │   ├── home-mobile.html

    │       │   ├── projects-mobile.html

    │       │   └── services-mobile.html

    │       └── tablet

    │           ├── about-tablet.html

    │           ├── contact-tablet.html

    │           ├── home-tablet.html

    │           ├── projects-tablet.html

    │           └── services-tablet.html

    ├── scripts

    │   ├── auto-snapshot.js

    │   ├── dev-with-snapshots.js

    │   ├── refresh-screenshots.js

    │   ├── screenshot-manager.js

    │   ├── tidy-screenshots.js

    │   ├── config

    │   │   └── screenshot-config.js

    │   └── utils

    │       └── screenshot-utils.js

    └── src

        ├── App.tsx

        ├── index.css

        ├── index.html

        ├── main.tsx

        ├── vite-env.d.ts

        ├── components

        │   ├── Navbar.tsx

        │   ├── ServiceCard.tsx

        │   ├── index.ts

        │   ├── common

        │   │   ├── Button.tsx

        │   │   ├── Container.tsx

        │   │   ├── Hero.tsx

        │   │   ├── ImageGallery.tsx

        │   │   ├── LocalExpertise.tsx

        │   │   ├── LocalServiceArea.tsx

        │   │   ├── Logo.tsx

        │   │   ├── SeasonalCTA.tsx

        │   │   ├── ServiceAreaList.tsx

        │   │   ├── WeatherAdaptedServices.tsx

        │   │   └── index.ts

        │   ├── contact

        │   │   └── ContactForm.tsx

        │   ├── layout

        │   │   ├── Footer.tsx

        │   │   ├── Header.tsx

        │   │   ├── Hero.tsx

        │   │   ├── Layout.tsx

        │   │   ├── Meta.tsx

        │   │   ├── Navbar.tsx

        │   │   └── index.ts

        │   ├── local

        │   │   ├── SeasonalGuide.tsx

        │   │   ├── ServiceAreaMap.tsx

        │   │   └── WeatherNotice.tsx

        │   ├── projects

        │   │   ├── ProjectCard.tsx

        │   │   ├── ProjectFilter.tsx

        │   │   ├── ProjectGallery.tsx

        │   │   └── ProjectGrid.tsx

        │   ├── seo

        │   │   └── TestimonialsSchema.tsx

        │   ├── services

        │   │   ├── Gallery.tsx

        │   │   └── ServiceCard.tsx

        │   ├── shared

        │   │   ├── ErrorBoundary.tsx

        │   │   ├── Elements

        │   │   │   ├── Card.tsx

        │   │   │   ├── Icon.tsx

        │   │   │   ├── Image.tsx

        │   │   │   ├── Link.tsx

        │   │   │   ├── Loading.tsx

        │   │   │   ├── index.ts

        │   │   │   └── Form

        │   │   │       ├── Input.tsx

        │   │   │       ├── Select.tsx

        │   │   │       ├── Textarea.tsx

        │   │   │       └── index.ts

        │   │   └── Layout

        │   │       ├── Layout.tsx

        │   │       └── index.ts

        │   └── ui

        │       ├── Button.tsx

        │       ├── Container.tsx

        │       ├── Hero.tsx

        │       ├── Intersection.tsx

        │       ├── Logo.tsx

        │       ├── Notifications.tsx

        │       ├── SeasonalCTA.tsx

        │       ├── ServiceAreaList.tsx

        │       ├── Skeleton.tsx

        │       ├── Transition.tsx

        │       └── index.ts

        ├── config

        │   ├── images.ts

        │   ├── routes.ts

        │   └── site.ts

        ├── content

        │   ├── index.ts

        │   ├── services

        │   │   └── index.ts

        │   ├── team

        │   │   └── index.ts

        │   └── testimonials

        │       └── index.ts

        ├── data

        │   ├── projects.ts

        │   ├── services.ts

        │   └── testimonials.ts

        ├── features

        │   ├── home.tsx

        │   ├── projects.tsx

        │   ├── services.tsx

        │   ├── team.tsx

        │   ├── testimonials.tsx

        │   ├── home

        │   │   ├── FilteredServicesSection.tsx

        │   │   ├── SeasonalProjectsCarousel.tsx

        │   │   ├── SeasonalServicesSection.tsx

        │   │   └── index.ts

        │   ├── projects

        │   │   ├── ProjectCard.tsx

        │   │   ├── ProjectFilter.tsx

        │   │   ├── ProjectGallery.tsx

        │   │   ├── ProjectGrid.tsx

        │   │   ├── ProjectsCarousel.tsx

        │   │   └── index.ts

        │   ├── services

        │   │   ├── ServiceCard.tsx

        │   │   ├── ServiceFeature.tsx

        │   │   ├── ServiceGrid.tsx

        │   │   ├── data.ts

        │   │   └── index.ts

        │   └── testimonials

        │       ├── AverageRating.tsx

        │       ├── Testimonial.tsx

        │       ├── TestimonialFilter.tsx

        │       ├── TestimonialSlider.tsx

        │       ├── TestimonialsSection.tsx

        │       ├── data.ts

        │       └── index.ts

        ├── hooks

        │   └── useData.ts

        ├── lib

        │   ├── constants.ts

        │   ├── content.ts

        │   ├── hooks.ts

        │   ├── index.ts

        │   ├── types.ts

        │   ├── utils.ts

        │   ├── api

        │   │   └── index.ts

        │   ├── config

        │   │   ├── images.ts

        │   │   ├── index.ts

        │   │   ├── paths.ts

        │   │   └── site.ts

        │   ├── context

        │   │   └── AppContext.tsx

        │   ├── hooks

        │   │   ├── images.ts

        │   │   ├── index.ts

        │   │   ├── useAnalytics.ts

        │   │   ├── useDebounce.ts

        │   │   ├── useEventListener.ts

        │   │   ├── useForm.ts

        │   │   ├── useFormField.ts

        │   │   ├── useIntersectionObserver.ts

        │   │   ├── useLocalStorage.ts

        │   │   └── useMediaQuery.ts

        │   ├── types

        │   │   ├── common.ts

        │   │   ├── components.ts

        │   │   ├── content.ts

        │   │   └── index.ts

        │   └── utils

        │       ├── analytics.ts

        │       ├── date.ts

        │       ├── images.ts

        │       ├── index.ts

        │       ├── seo.ts

        │       └── validation.ts

        ├── pages

        │   ├── ProjectDetail.tsx

        │   ├── Projects.tsx

        │   ├── ServiceDetail.tsx

        │   ├── Services.tsx

        │   ├── TestimonialsPage.tsx

        │   ├── about

        │   │   └── index.tsx

        │   ├── contact

        │   │   └── index.tsx

        │   ├── home

        │   │   └── index.tsx

        │   ├── projects

        │   │   ├── detail.tsx

        │   │   └── index.tsx

        │   ├── services

        │   │   ├── detail.tsx

        │   │   └── index.tsx

        │   └── testimonials

        │       └── index.tsx

        ├── styles

        │   ├── animations.css

        │   ├── base.css

        │   └── utilities.css

        ├── types

        │   ├── content.ts

        │   └── index.ts

        └── utils

            └── imageLoader.ts

    ```


## Message 5

**Streamlining the script**

I’m working on unifying script logic into `website-snapshot.js`, removing references to scripts like `dev-with-snapshots` and `screenshot-manager`. The goal is a single script that can execute on demand.

**Weighing script options**

I’m considering consolidating the screenshot utility into `website-snapshot.js` using puppeteer, with a choice of config sources. The script should exclude watchers, dev server launch, and scheduling.

**Streamlining the script**

I’m focusing on a single `website-snapshot.js` script with puppeteer for capturing, potentially incorporating optional arguments and a config object. The inclusion of old snapshot removal or metadata processing is undecided.

**Formulating the script**

I’m proposing `website-snapshot.js` to capture snapshots with optional features like tidying, removing old snapshots, storing metadata, AI summarization, and configuration management.

**Consolidating scripts**

I’m merging `auto-snapshot.js`, `screenshot-manager.js`, and `screenshot-utils.js` into a single `website-snapshot.js` script, focusing on capturing functionality and integrating an inline configuration object.



## Message 6

Below is an example **single** script—**`website-snapshot.js`**—that captures screenshots on demand, tidies the screenshot directory, and optionally cleans out old snapshots. This combines the essential parts of your existing `auto-snapshot.js`, `tidy-screenshots.js`, and some “clean” logic. All scheduling, file-watching, or dev-server logic is omitted (you said “only on demand”), so this script is intentionally lighter.

You can place `website-snapshot.js` at your project root (same level as `package.json`) and remove or archive the older multi-script setup.

---

## 1. Proposed File: `website-snapshot.js`

> **Note:** This is an illustrative example. Feel free to rename objects or tweak paths, pages, or console outputs as you see fit.

```js
#!/usr/bin/env node

/**
 * website-snapshot.js
 * -------------------
 * A single script to:
 *  1) Capture screenshots of configured pages (using Puppeteer)
 *  2) Tidy the screenshots directory (.gitignore, .gitkeep, remove stray files)
 *  3) Optionally clean out old snapshots older than N days
 *
 * Usage:
 *  node website-snapshot.js capture
 *  node website-snapshot.js tidy
 *  node website-snapshot.js clean [days]
 *  node website-snapshot.js help
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { exec } from "child_process";
import { promisify } from "util";
import puppeteer from "puppeteer";

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ----------------------------------------------------------------------------
// CONFIGURATION
// ----------------------------------------------------------------------------
const CONFIG = {
  // Where to store screenshots
  screenshotsDir: path.join(__dirname, "screenshots"),
  // Directory for "latest" screenshots
  latestDir: path.join(__dirname, "screenshots", "latest"),
  // Pages to capture
  pages: [
    { route: "/", name: "home" },
    { route: "/hvem-er-vi", name: "about" },
    { route: "/hva-vi-gjor", name: "services" },
    { route: "/prosjekter", name: "projects" },
    { route: "/kontakt", name: "contact" },
  ],
  // Capture settings
  serverPort: 5173,
  captureDelay: 2000,
  viewports: [
    { name: "desktop", width: 1440, height: 900 },
    { name: "tablet", width: 768, height: 1024 },
    { name: "mobile", width: 375, height: 812 },
  ],
  // Cleanup settings
  defaultRetentionDays: 7,
};

// ----------------------------------------------------------------------------
// 1) CAPTURE SCREENSHOTS
// ----------------------------------------------------------------------------
async function captureScreenshots() {
  console.log("\n[CAPTURE] Starting screenshot capture...");

  // Ensure the screenshotsDir exists
  if (!fs.existsSync(CONFIG.screenshotsDir)) {
    fs.mkdirSync(CONFIG.screenshotsDir, { recursive: true });
  }
  // Ensure the latestDir exists
  if (!fs.existsSync(CONFIG.latestDir)) {
    fs.mkdirSync(CONFIG.latestDir, { recursive: true });
  }

  // Create a timestamped folder for this snapshot
  const timeStr = new Date()
    .toISOString()
    .replace(/T/, "_")
    .replace(/:/g, "-")
    .split(".")[0]; // e.g. "2023-05-01_12-34-56"
  const snapshotDir = path.join(CONFIG.screenshotsDir, timeStr);
  fs.mkdirSync(snapshotDir, { recursive: true });

  // Launch one browser for the entire capture run
  const browser = await puppeteer.launch();

  // For each viewport
  for (const vp of CONFIG.viewports) {
    // Create subfolder in both the timestamped folder and "latest"
    const viewportDir = path.join(snapshotDir, vp.name);
    const latestViewportDir = path.join(CONFIG.latestDir, vp.name);
    if (!fs.existsSync(viewportDir)) fs.mkdirSync(viewportDir, { recursive: true });
    if (!fs.existsSync(latestViewportDir)) fs.mkdirSync(latestViewportDir, { recursive: true });

    // For each page
    for (const { route, name } of CONFIG.pages) {
      const url = `http://localhost:${CONFIG.serverPort}${route}`;
      const timestampedPath = path.join(viewportDir, `${name}-${vp.name}.html`);
      const latestPath = path.join(latestViewportDir, `${name}-${vp.name}.html`);

      try {
        const page = await browser.newPage();
        await page.setViewport({ width: vp.width, height: vp.height });
        console.log(`[CAPTURE] Navigating to: ${url} [${vp.name}]`);
        await page.goto(url, { waitUntil: "networkidle2", timeout: 10000 });

        // Wait a bit for final rendering
        await new Promise((resolve) => setTimeout(resolve, CONFIG.captureDelay));

        // Instead of PNG screenshots, you might store the page content (HTML),
        // or if you prefer images, do: page.screenshot({ path: ... })
        const content = await page.content();
        fs.writeFileSync(timestampedPath, content, "utf-8");
        // Copy to latest
        fs.writeFileSync(latestPath, content, "utf-8");
        await page.close();

        console.log(`[CAPTURE] Saved: ${timestampedPath}`);
      } catch (error) {
        console.error(`[CAPTURE] Error capturing ${name} [${vp.name}]:`, error);
      }
    }
  }

  await browser.close();
  console.log(`[CAPTURE] Completed. Snapshot stored in: ${snapshotDir}\n`);

  return snapshotDir;
}

// ----------------------------------------------------------------------------
// 2) TIDY SCREENSHOTS DIRECTORY
//    - Creates .gitignore, .gitkeep
//    - Removes any loose files that do not match typical naming
//    - (Optional) can also tweak an HTML report if needed
// ----------------------------------------------------------------------------
function tidyScreenshots() {
  console.log("\n[TIDY] Tidying up screenshots directory...");

  if (!fs.existsSync(CONFIG.screenshotsDir)) {
    console.log("[TIDY] No screenshots directory found, skipping...");
    return;
  }

  // Create a .gitignore
  const gitignorePath = path.join(CONFIG.screenshotsDir, ".gitignore");
  const gitignoreContent = `# Ignore all actual screenshots or snapshot directories
*
!.gitignore
!.gitkeep
!README.md
!latest/
latest/*
!latest/.gitkeep
`;

  fs.writeFileSync(gitignorePath, gitignoreContent);
  console.log("[TIDY] Created/updated .gitignore in screenshots/");

  // Create a .gitkeep
  const gitkeepPath = path.join(CONFIG.screenshotsDir, ".gitkeep");
  if (!fs.existsSync(gitkeepPath)) {
    fs.writeFileSync(gitkeepPath, "");
    console.log("[TIDY] Created .gitkeep in screenshots/");
  }

  // Create .gitkeep in "latest/"
  const gitkeepLatest = path.join(CONFIG.latestDir, ".gitkeep");
  if (!fs.existsSync(gitkeepLatest)) {
    fs.writeFileSync(gitkeepLatest, "");
    console.log("[TIDY] Created .gitkeep in screenshots/latest/");
  }

  // Remove "loose" files that are not recognized
  const allEntries = fs.readdirSync(CONFIG.screenshotsDir);
  for (const entry of allEntries) {
    if ([".gitignore", ".gitkeep", "README.md", "latest"].includes(entry)) {
      continue;
    }
    // If it's a directory that looks like a timestamp, leave it. Example: "2023-05-01_12-34-56"
    const isTimestampDir = /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(entry);
    if (isTimestampDir) continue;

    const toRemove = path.join(CONFIG.screenshotsDir, entry);
    try {
      fs.rmSync(toRemove, { recursive: true, force: true });
      console.log(`[TIDY] Removed loose file/folder: ${entry}`);
    } catch (error) {
      console.error(`[TIDY] Error removing ${entry}:`, error);
    }
  }

  console.log("[TIDY] Screenshots directory tidied successfully.\n");
}

// ----------------------------------------------------------------------------
// 3) CLEAN OUT OLD SNAPSHOTS
//    - Removes any directories older than N days
// ----------------------------------------------------------------------------
function cleanOldScreenshots(days = CONFIG.defaultRetentionDays) {
  console.log(`\n[CLEAN] Removing snapshots older than ${days} day(s)...`);

  if (!fs.existsSync(CONFIG.screenshotsDir)) {
    console.log("[CLEAN] No screenshots directory found, skipping...");
    return;
  }

  const now = Date.now();
  const cutoff = now - days * 24 * 60 * 60 * 1000;

  const allEntries = fs.readdirSync(CONFIG.screenshotsDir);
  let removedCount = 0;

  for (const entry of allEntries) {
    // Skip known special items
    if ([".gitignore", ".gitkeep", "README.md", "latest"].includes(entry)) {
      continue;
    }
    const fullPath = path.join(CONFIG.screenshotsDir, entry);
    // Check if it’s a directory that matches the timestamp pattern
    const isTimestampDir = /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(entry);
    if (!isTimestampDir) continue; // skip anything that’s not a dated folder

    // If older than cutoff, remove it
    const stats = fs.statSync(fullPath);
    const modifiedTime = stats.mtime.getTime();
    if (modifiedTime < cutoff) {
      fs.rmSync(fullPath, { recursive: true, force: true });
      console.log(`[CLEAN] Removed old snapshot folder: ${entry}`);
      removedCount++;
    }
  }

  if (removedCount === 0) {
    console.log("[CLEAN] No old snapshot folders found to remove.");
  } else {
    console.log(`[CLEAN] Removed ${removedCount} old snapshot folder(s).`);
  }
  console.log("[CLEAN] Done.\n");
}

// ----------------------------------------------------------------------------
// HELP TEXT
// ----------------------------------------------------------------------------
function showHelp() {
  console.log(`
Usage: node website-snapshot.js [command] [options]

Commands:
  capture            Capture snapshots of the configured pages
  tidy               Organize (tidy up) the screenshots folder
  clean [days]       Remove snapshot folders older than [days] (default = ${CONFIG.defaultRetentionDays})
  help               Show this help message

Examples:
  node website-snapshot.js capture
  node website-snapshot.js tidy
  node website-snapshot.js clean 3
  node website-snapshot.js help
`);
}

// ----------------------------------------------------------------------------
// MAIN
// ----------------------------------------------------------------------------
async function main() {
  const [command, arg] = process.argv.slice(2);

  switch (command) {
    case "capture":
      await captureScreenshots();
      break;
    case "tidy":
      tidyScreenshots();
      break;
    case "clean":
      cleanOldScreenshots(parseInt(arg) || CONFIG.defaultRetentionDays);
      break;
    case "help":
    default:
      showHelp();
      break;
  }
}

main().catch((error) => {
  console.error("Error:", error);
  process.exit(1);
});
```

### How it Works

1. **`capture`**  
   - Opens Puppeteer only once.  
   - Cycles through each **viewport** (`desktop`, `tablet`, `mobile`) and each **page** you define.  
   - Saves results into a newly created **timestamped** folder (e.g., `screenshots/2025-03-12_14-05-07`).  
   - Also overwrites the **latest** folder for each viewport.  
   - By default, it’s saving **HTML** with `page.content()` calls. If you actually want image files, replace those lines with:
     ```js
     await page.screenshot({ path: someFilePath, fullPage: true });
     ```
   - You can modify the page array or port in the `CONFIG` block.

2. **`tidy`**  
   - Ensures a minimal `.gitignore` and `.gitkeep` in `screenshots/`, removing random files or folders that don’t match the typical naming.  
   - Leaves your `latest` folder and any timestamped folders intact.

3. **`clean [days]`**  
   - Looks at each timestamped folder (e.g., `2025-03-12_14-05-07`) in `screenshots/` and checks its modification date.  
   - If older than `[days]` (7 by default), it removes it.

4. **`help`**  
   - Prints usage instructions.

Since it’s a single script, you can run:
```bash
node website-snapshot.js capture
node website-snapshot.js tidy
node website-snapshot.js clean 10
```
…and so on.

---

## 2. Removing/Archiving Old Scripts

After introducing `website-snapshot.js`, you can **delete** or **archive** the contents of `scripts/` (`auto-snapshot.js`, `dev-with-snapshots.js`, etc.) if you no longer need them. The same goes for the specialized config and utils files, since everything is now consolidated in one place.

---

## 3. Points to Adjust or Keep

1. **Screenshot Output**:  
   - The sample above saves HTML pages. If you prefer **PNG** images, replace:
     ```js
     const content = await page.content();
     fs.writeFileSync(timestampedPath, content, "utf-8");
     ```
     with:
     ```js
     await page.screenshot({ path: timestampedPath, fullPage: true });
     ```
     and adjust file extensions from `.html` to `.png`.

2. **Directory Names**:  
   - By default, it uses `screenshots/`. Adjust if you prefer a different folder.

3. **Viewport Array**:  
   - Edit or remove `tablet`/`mobile` if you only want `desktop`.

4. **Port**:  
   - If your development server runs on a port other than 5173, change `serverPort`.

5. **Cron / Scheduling**:  
   - If you still need automatic scheduling, simply wrap `node website-snapshot.js capture` in a cron job or Windows Task. No extra code needed in this script.

---

**End result**: You have a single, straightforward **“on-demand”** snapshot script that captures all needed pages, tidies up, and optionally removes older snapshots. This eliminates the multi-script overhead and keeps everything in one place.
