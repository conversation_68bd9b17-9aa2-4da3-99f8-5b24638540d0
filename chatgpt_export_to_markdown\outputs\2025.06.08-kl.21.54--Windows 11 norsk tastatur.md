# Windows 11 norsk tastatur

Conversation ID: 6845ea5b-66b8-8008-85b6-f1dfe313a67c

## Message 1

i live in norway and use the letters ae, oo, etc. whenever i set up a new windows 11 installation i forget how i can get the keyboard input to use norwegian keyboard, and have decide to write a bat script to automate this. i\ve attached the current version of the script, please add a new section that fixes this issue (setting keyboard to norwegian)?

@ECHO OFF

TITLE Windows Customization Script



:: ----------------------------------------------------------------------------

:: Request Administrator Privileges

:: ----------------------------------------------------------------------------

NET SESSION >nul 2>&1

IF %ERRORLEVEL% NEQ 0 (

    ECHO This script requires administrator privileges.

    ECHO Please right-click the script and select 'Run as administrator'.

    ECHO.

    PAUSE

    EXIT /B 1

)



ECHO Applying registry customizations...

ECHO.



:: ----------------------------------------------------------------------------

:: Add Applications to Run Dialog (App Paths)

:: ----------------------------------------------------------------------------

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /ve /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\Everything64.exe" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /v Path /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\" /f

::



:: ----------------------------------------------------------------------------

:: File/Extension Associations

:: ----------------------------------------------------------------------------

ECHO Setting up file/extension associations...



assoc .txt=txtfile

ftype txtfile="%SystemRoot%\System32\notepad.exe" "%%1"



:: Example: Associate .BAT files with Notepad (optional)

:: NOTE: By default, .bat is associated with 'batfile' (to run).

::       This is only if you want to open/edit .bat with Notepad by double-click.

:: assoc .bat=batfile

:: ftype batfile="%SystemRoot%\System32\notepad.exe" "%%1"



:: Add more associations as needed...



ECHO File associations set.

ECHO.





:: ----------------------------------------------------------------------------

:: Explorer Customizations

:: ----------------------------------------------------------------------------

ECHO Customizing File Explorer...

:: Display full path in the File Explorer title bar (0:Hide | 1:Show)

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState" /v FullPath /t REG_DWORD /d 0 /f



:: Change Windows Explorer to automatically open 'This PC'

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v LaunchTo /t REG_DWORD /d 1 /f



:: Remove the "Shortcut" suffix when creating shortcuts

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "ShortcutNameTemplate" /t REG_SZ /d "%%s.lnk" /f



:: Set suffix added to filename when copying files

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "CopyNameTemplate" /t REG_SZ /d "%%s 1" /f



:: Hide frequently and recently used files/folders in quick access

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowFrequent" /t REG_DWORD /d "0" /f

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowRecent" /t REG_DWORD /d "0" /f



:: Disable Automatic Folder Type Discovery

REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f

REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f

ECHO Explorer customizations applied.

ECHO.



:: ----------------------------------------------------------------------------

:: System Behavior

:: ----------------------------------------------------------------------------

ECHO Adjusting system behavior...

:: Set Keyboard Delay and Keyboard Repeat Rate

reg add "HKCU\Control Panel\Keyboard" /v KeyboardDelay /t REG_SZ /d 0 /f

reg add "HKCU\Control Panel\Keyboard" /v KeyboardSpeed /t REG_SZ /d 31 /f

RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters



:: Disable "This App is Preventing Shutdown or Restart" Screen

REG ADD "HKCU\Control Panel\Desktop" /v "AutoEndTasks" /t REG_SZ /d "1" /f

REG ADD "HKCU\Control Panel\Desktop" /v "HungAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKCU\Control Panel\Desktop" /v "WaitToKillAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKLM\System\CurrentControlSet\Control" /v "WaitToKillServiceTimeout" /t REG_SZ /d "1000" /f

ECHO System behavior adjustments applied.

ECHO.



:: ----------------------------------------------------------------------------

:: Control Panel

:: ----------------------------------------------------------------------------

ECHO Configuring Control Panel options...

:: Show all tasks on control panel

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "InfoTip" /t REG_SZ /d "View list of all Control Panel tasks" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "System.ControlPanel.Category" /t REG_SZ /d "5" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\DefaultIcon" /ve /t REG_SZ /d "C:\Windows\System32\imageres.dll,-27" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\Shell\Open\Command" /ve /t REG_SZ /d "explorer.exe shell:::{ED7BA470-8E54-465E-825C-99712043E01C}" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel\NameSpace\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f

ECHO Control Panel options configured.

ECHO.



:: ----------------------------------------------------------------------------

:: Theme and Appearance

:: ----------------------------------------------------------------------------

ECHO Setting theme and appearance...

:: Enable Dark Theme

reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f >nul

reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f >nul

reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 1 /f >nul

ECHO Theme and appearance set.

ECHO.



:: ----------------------------------------------------------------------------

:: Taskbar Customizations

:: ----------------------------------------------------------------------------

ECHO Customizing Taskbar...

:: Taskbar: Disable Meet Now

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

:: Taskbar: Disable People

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

REG ADD "HKCU\Software\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

:: Taskbar: Hide People icon (even if policy is not set)

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced\People" /v PeopleBand /t REG_DWORD /d 0 /f

:: Taskbar: Disable Weather, News and Interests on taskbar

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds" /v EnableFeeds /t REG_DWORD /d 0 /f

:: Taskbar: Hide Weather, News and Interests on taskbar (set view mode to off)

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Feeds" /v ShellFeedsTaskbarViewMode /t REG_DWORD /d 2 /f

:: Changes the Windows 11 taskbar alignment to the left

REG ADD HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced /v TaskbarAl /t REG_DWORD /d 0 /f

ECHO Taskbar customizations applied.

ECHO.



:: ----------------------------------------------------------------------------

:: Disable Features

:: ----------------------------------------------------------------------------

ECHO Disabling selected features...

:: Disable Cortana

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortana /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortanaAboveLock /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaConsent /t REG_DWORD /d 0 /f

ECHO Selected features disabled.

ECHO.



:: ----------------------------------------------------------------------------

:: Photo Viewer

:: ----------------------------------------------------------------------------

ECHO Setting up Legacy Photo Viewer...

:: Enable legacy photo viewer file associations

for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (

    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" /v ".%%~a" /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f

)

:: Set legacy photo viewer as default for current user (this part may require user to choose manually in Default Apps for some extensions)

for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (

    REG ADD "HKCU\SOFTWARE\Classes\.%%~a" /ve /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f

)

ECHO Legacy Photo Viewer setup attempted. You might need to manually set defaults for some image types.

ECHO.



:: ----------------------------------------------------------------------------

:: Apply System Parameter Changes and Restart Explorer

:: ----------------------------------------------------------------------------

ECHO Applying system parameter changes and restarting Explorer...

RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters

taskkill /f /im explorer.exe

start explorer.exe

ECHO Explorer restarted.

ECHO.



:: ----------------------------------------------------------------------------

:: Exit with message (auto-close after 15 seconds)

:: ----------------------------------------------------------------------------

ECHO.

ECHO All customizations have been applied.

ECHO Window will close automatically in 15 seconds ...

PING 127.0.0.1 -n 16 > NUL

EXIT

:: ----------------------------------------------------------------------------

## Message 2

**Setting keyboard locale**

I’m starting by setting the Norwegian keyboard layout in the Windows Registry and considering PowerShell for smooth integration within a .bat script.

**Setting up keyboard layout**

I’m incorporating a script portion to set the Norwegian keyboard layout using registry commands for both the current and default users, ensuring it aligns with the Windows 11 interface.

## Message 3

Below is one example of how you can add a new section in your script to set the default keyboard layout to Norwegian (Bokmål). This uses the standard locale ID `00000414` for Norwegian (Bokmål) and also sets a fallback to English (US) (`00000409`) as the second layout. You can remove or reorder these if you wish.

Simply insert the following **before** your final restart of Explorer or near the other `REG` sections:

```batch
:: ----------------------------------------------------------------------------
:: Keyboard Layout - Norwegian
:: ----------------------------------------------------------------------------
ECHO Setting Norwegian keyboard layout...
:: Set Norwegian (Bokmål) as Layout #1
REG ADD "HKCU\Keyboard Layout\Preload" /v 1 /t REG_SZ /d 00000414 /f

:: (Optional) Keep English (US) as Layout #2
REG ADD "HKCU\Keyboard Layout\Preload" /v 2 /t REG_SZ /d 00000409 /f

:: (Optional) Ensure the system knows these are valid
REG ADD "HKCU\Keyboard Layout\Substitutes" /v 00000414 /t REG_SZ /d 00000414 /f
REG ADD "HKCU\Keyboard Layout\Substitutes" /v 00000409 /t REG_SZ /d 00000409 /f

:: Apply changes
RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters
ECHO Norwegian keyboard layout set.
ECHO.
```

### Explanation

1. **Preload**  
   - The `Preload` registry key in `HKCU\Keyboard Layout\Preload` contains the list of input locales (keyboards) in priority order.  
   - `1` maps to the primary/default layout; `2`, `3`, etc. map to additional layouts.

2. **Locale IDs**  
   - Norwegian (Bokmål) is typically `00000414` (locale ID 0414).  
   - English (US) is typically `00000409`.

3. **Substitutes**  
   - The `Substitutes` key tells Windows what the actual layout mapping is if Windows tries to interpret a slightly different ID. Setting them to the same values ensures Windows knows exactly which keyboards you want.

4. **Applying Changes**  
   - `RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters` forces Windows to refresh the user’s system parameters, reducing the chance you’ll need to log out or reboot.

After adding this section, rerun your `.bat` script with Administrator privileges. On the next sign-in (or sometimes immediately after the registry refresh), your default input locale should switch to Norwegian so that you have access to `æ`, `ø`, `å`, and other Norwegian-specific characters.

## Message 4

Sure

## Message 5

**Ensuring correct registry edits**

Verifying the registry changes for keyboard repeat rate, including delay and speed settings, and potential approaches like control panel opening or system restart.

**Finalizing the script**

I’m tweaking the script to ensure it sets the keyboard repeat rate, considering system sign-out and the use of "REG_SZ" for delay and speed values.

## Message 6

Below is an example of a dedicated section that sets the keyboard repeat rate in the registry. Note that Windows often requires either signing out or restarting for these changes to take effect reliably. The existing `RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters` call sometimes updates user parameters immediately, but in many cases a **logoff/logon** cycle is needed for the repeat-rate change to fully apply.

Insert this near the other registry sections in your script (before the final restart of Explorer is fine):

```batch
:: ----------------------------------------------------------------------------
:: Keyboard Repeat Rate
:: ----------------------------------------------------------------------------
ECHO Setting keyboard repeat rate...
:: Valid range for KeyboardDelay is 0 to 3 (shorter delay -> faster repeat start)
:: Valid range for KeyboardSpeed is 0 to 31 (higher -> faster character repeat)
REG ADD "HKCU\Control Panel\Keyboard" /v KeyboardDelay /t REG_SZ /d 0 /f
REG ADD "HKCU\Control Panel\Keyboard" /v KeyboardSpeed /t REG_SZ /d 31 /f

:: Force a per-user parameter update (though a sign out or reboot is more reliable)
RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters

ECHO Keyboard repeat rate set. You may need to sign out or restart for changes to be fully recognized.
ECHO.
```

### Why logoff/logon?
- Certain keyboard- and user-profile-related registry settings (especially under `HKCU\Control Panel\Keyboard`) don’t always get honored until the next session. While calling `UpdatePerUserSystemParameters` does refresh many user settings, **the fastest repeat rate and minimal delay in the keyboard control panel often require a sign out and sign in** to guarantee the change is recognized by Windows.

### Confirming Settings
- After reboot/sign-out, you can check the effective settings in **Control Panel → Keyboard** (if available), or test by typing in a text editor to confirm the new faster repeat rate.

## Message 7

Sure

## Message 8

can you please add a section that correctly sets the keyboard repeat rate? the current regedit change doesn't sem to work

## Message 9

**Validating settings**

I’m looking at the syntax and data types for each command block. Ensuring precision, I’m adjusting from `REG_SZ` to `REG_DWORD` where needed.

**Ensuring syntax accuracy**

I'm comparing `REG_SZ` and `REG_DWORD` entries, checking potential spelling issues, and affirming the correct approach to disable automatic folder type discovery.

## Message 10

Below are a few notes about the registry tweaks in your script and how they behave under Windows 11. Overall, most of your commands will still run without error, but there are a few areas where Windows 11 either ignores or has changed how certain registry values work. The short answer is that **none of your lines are “incorrect” to the point of breaking,** but a few either won’t do anything or might require additional/different keys to achieve the desired effect.

---

## 1. Full Path in Explorer Title Bar

```bat
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState" /v FullPath /t REG_DWORD /d 0 /f
```
- **Behavior**: A value of `1` should show the full path in the title bar, and `0` hides it. If you truly want the “full path” visible, change `0` → `1`.
- **Windows 11**: Modern File Explorer windows don’t always honor the old “title bar text” in the same manner. You may see minimal effect unless you have the “classic” folder options.  

---

## 2. Disabling Automatic Folder Type Discovery

```bat
REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f
REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f
```
- **Behavior**: This clears your saved folder view “Bags” and sets all folders to “General Items.”  
- **Windows 11**: This should still work. Just keep in mind that Windows 11 might recreate certain folder-specific views by default. Also, you already call `taskkill /f /im explorer.exe` and `start explorer.exe` at the end, which is good to ensure changes apply.

---

## 3. Control Panel “God Mode” (All Tasks)

```bat
:: "All Tasks" (God Mode) 
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{...}" ...
```
- **Behavior**: This hack exposes the “God Mode” folder in older Windows versions. On Windows 11, it still typically works but does **not** show in Control Panel quite as seamlessly as in Windows 7/8/10. 
- **Windows 11**: If it doesn’t appear in Control Panel the way you expect, you can still access it by creating a folder named `GodMode.{ED7BA470-8E54-465E-825C-99712043E01C}` on your desktop.

---

## 4. Dark Theme Settings

```bat
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f
```
- **Behavior**: These keys should work the same in Windows 10 and 11. A value of `0` = Dark, `1` = Light.  
- **No changes needed**: This is correct for enabling dark mode on Windows 11.

---

## 5. Taskbar Customizations

You have lines disabling Meet Now, People, News/Interests, and moving the taskbar alignment to the left:

```bat
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f
REG ADD "HKCU\Software\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds" /v EnableFeeds /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Feeds" /v ShellFeedsTaskbarViewMode /t REG_DWORD /d 2 /f
REG ADD HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced /v TaskbarAl /t REG_DWORD /d 0 /f
```

- **Behavior**: On Windows 11, these can disable some leftover Windows 10 features if they still appear. 
- **Widgets**: Windows 11 “widgets” might need a separate registry or group policy tweak:
  - You can disable it by creating or setting  
    `REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Dsh" /v AllowNewsAndInterests /t REG_DWORD /d 0 /f`  
  - or by turning off widgets in **Settings** → **Personalization** → **Taskbar**.
- **Taskbar Alignment**: The `TaskbarAl` key is recognized by most Windows 11 builds and should move the icons to the left. Some versions of Windows 11 might override it with user settings in the new Taskbar personalization, but typically it does work.

---

## 6. Disabling Cortana

```bat
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortana /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortanaAboveLock /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaEnabled /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaConsent /t REG_DWORD /d 0 /f
```
- **Behavior**: Cortana is mostly phased out in Windows 11. These registry settings do no harm and will ensure it’s fully disabled in case it tries to show up in certain builds.

---

## 7. Keyboard Delay/Repeat Rate

```bat
reg add "HKCU\Control Panel\Keyboard" /v KeyboardDelay /t REG_SZ /d 0 /f
reg add "HKCU\Control Panel\Keyboard" /v KeyboardSpeed /t REG_SZ /d 31 /f
```
- **Behavior**: As mentioned, Windows typically stores these values as `REG_SZ` in the “Control Panel\Keyboard” key. That part is correct, but Windows 11 often requires a **sign out and sign in** to see the effect. The `RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters` may not always be enough.

---

## 8. Photo Viewer Associations

This is the classic “restore Photo Viewer” hack:
```bat
for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (
    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" ...
)
for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (
    REG ADD "HKCU\SOFTWARE\Classes\.%%~a" ...
)
```
- **Windows 11**: These changes still allow Windows Photo Viewer to appear as an “Open with” option, but you or the user may still need to pick it as the default viewer in **Settings** → **Apps** → **Default apps**.  
- **No changes needed**: This is the standard method to re-enable the old photo viewer.

---

# Conclusion

1. **No “Critical” Edits Needed**  
   All of your registry commands will run and should not break your Windows 11 setup. However, **some** either don’t apply to Windows 11 as they did in Windows 10 or might need additional steps (like logging out) to take effect.

2. **Double-Check Desired Settings**  
   - If you truly want the full path in Explorer’s title bar, you may want `FullPath = 1` instead of `0`.  
   - Consider adding the extra registry key to disable Windows 11 **Widgets** (the “AllowNewsAndInterests” key), if that’s important to you.  

3. **Sign Out / Restart**  
   - Keyboard settings (repeat rate/delay) often require a fresh session to reliably apply.  
   - Explorer changes also sometimes need a sign out/in even though you restart Explorer.exe.

In short, your script is fine as is, but realize Windows 11 may override or ignore a few old keys, and the biggest improvement is ensuring that you **log out and back in** if certain changes (like keyboard speed) do not appear immediately.

## Message 11

are there any other of the commands that should be changed to work properly?





:: ----------------------------------------------------------------------------

:: Explorer Customizations

:: ----------------------------------------------------------------------------

ECHO Customizing File Explorer...

:: Display full path in the File Explorer title bar (0:Hide | 1:Show)

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState" /v FullPath /t REG_DWORD /d 0 /f



:: Change Windows Explorer to automatically open 'This PC'

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v LaunchTo /t REG_DWORD /d 1 /f



:: Remove the "Shortcut" suffix when creating shortcuts

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "ShortcutNameTemplate" /t REG_SZ /d "%%s.lnk" /f



:: Set suffix added to filename when copying files

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "CopyNameTemplate" /t REG_SZ /d "%%s 1" /f



:: Hide frequently and recently used files/folders in quick access

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowFrequent" /t REG_DWORD /d "0" /f

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowRecent" /t REG_DWORD /d "0" /f



:: Disable Automatic Folder Type Discovery

REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f

REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f

ECHO Explorer customizations applied.

ECHO.



:: ----------------------------------------------------------------------------

:: System Behavior

:: ----------------------------------------------------------------------------

ECHO Adjusting system behavior...

:: Set Keyboard Delay and Keyboard Repeat Rate

reg add "HKCU\Control Panel\Keyboard" /v KeyboardDelay /t REG_SZ /d 0 /f

reg add "HKCU\Control Panel\Keyboard" /v KeyboardSpeed /t REG_SZ /d 31 /f

RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters



:: Disable "This App is Preventing Shutdown or Restart" Screen

REG ADD "HKCU\Control Panel\Desktop" /v "AutoEndTasks" /t REG_SZ /d "1" /f

REG ADD "HKCU\Control Panel\Desktop" /v "HungAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKCU\Control Panel\Desktop" /v "WaitToKillAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKLM\System\CurrentControlSet\Control" /v "WaitToKillServiceTimeout" /t REG_SZ /d "1000" /f

ECHO System behavior adjustments applied.

ECHO.



:: ----------------------------------------------------------------------------

:: Control Panel

:: ----------------------------------------------------------------------------

ECHO Configuring Control Panel options...

:: Show all tasks on control panel

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "InfoTip" /t REG_SZ /d "View list of all Control Panel tasks" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "System.ControlPanel.Category" /t REG_SZ /d "5" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\DefaultIcon" /ve /t REG_SZ /d "C:\Windows\System32\imageres.dll,-27" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\Shell\Open\Command" /ve /t REG_SZ /d "explorer.exe shell:::{ED7BA470-8E54-465E-825C-99712043E01C}" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel\NameSpace\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f

ECHO Control Panel options configured.

ECHO.



:: ----------------------------------------------------------------------------

:: Theme and Appearance

:: ----------------------------------------------------------------------------

ECHO Setting theme and appearance...

:: Enable Dark Theme

reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f >nul

reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f >nul

reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 1 /f >nul

ECHO Theme and appearance set.

ECHO.



:: ----------------------------------------------------------------------------

:: Taskbar Customizations

:: ----------------------------------------------------------------------------

ECHO Customizing Taskbar...

:: Taskbar: Disable Meet Now

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

:: Taskbar: Disable People

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

REG ADD "HKCU\Software\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

:: Taskbar: Hide People icon (even if policy is not set)

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced\People" /v PeopleBand /t REG_DWORD /d 0 /f

:: Taskbar: Disable Weather, News and Interests on taskbar

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds" /v EnableFeeds /t REG_DWORD /d 0 /f

:: Taskbar: Hide Weather, News and Interests on taskbar (set view mode to off)

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Feeds" /v ShellFeedsTaskbarViewMode /t REG_DWORD /d 2 /f

:: Changes the Windows 11 taskbar alignment to the left

REG ADD HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced /v TaskbarAl /t REG_DWORD /d 0 /f

ECHO Taskbar customizations applied.

ECHO.



:: ----------------------------------------------------------------------------

:: Disable Features

:: ----------------------------------------------------------------------------

ECHO Disabling selected features...

:: Disable Cortana

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortana /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortanaAboveLock /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaConsent /t REG_DWORD /d 0 /f

ECHO Selected features disabled.

ECHO.



:: ----------------------------------------------------------------------------

:: Photo Viewer

:: ----------------------------------------------------------------------------

ECHO Setting up Legacy Photo Viewer...

:: Enable legacy photo viewer file associations

for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (

    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" /v ".%%~a" /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f

)

:: Set legacy photo viewer as default for current user (this part may require user to choose manually in Default Apps for some extensions)

for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (

    REG ADD "HKCU\SOFTWARE\Classes\.%%~a" /ve /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f

)

ECHO Legacy Photo Viewer setup attempted. You might need to manually set defaults for some image types.

ECHO.



:: ----------------------------------------------------------------------------

:: Apply System Parameter Changes and Restart Explorer

:: ----------------------------------------------------------------------------

ECHO Applying system parameter changes and restarting Explorer...

RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters

taskkill /f /im explorer.exe

start explorer.exe

ECHO Explorer restarted.

ECHO.

## Message 12

**Organizing sections**

I'm working through each script section, ensuring it follows a consistent, annotated pattern. This involves renaming, adding mini descriptions, and aligning with the provided structure.

**Transferring functionalities**

I'm piecing together the script, ensuring smooth transitions. Each section consistently aligns with the specified pattern, maintaining clear and concise instructions throughout.

**Setting parameters**

OK, let me see. I'm configuring system parameters like keyboard settings and taskbar adjustments, ensuring each tweak is methodically applied and clearly documented.

**Applying tweaks**

Mapping out Registry customizations for CloudSearch, Cortana, photo viewer associations, and finalizing with system parameter changes and taskbar adjustments. Each step is detailed and methodically executed.

## Message 13

Below is your script with each section following a consistent format, similar to your **Keyboard Delay** section example. The pattern is:

1. **Section header block**:
   ```batch
   :: ----------------------------------------------------------------------------
   :: <Short descriptive section title>
   :: ----------------------------------------------------------------------------
   :: - (Optional bullet lines describing what this section does)
   ECHO <Descriptive echo text>...
   ```
2. **Commands/Registry operations**  
3. **Concluding ECHO** (optional)

```batch
@ECHO OFF
TITLE Windows Customization Script

:: ----------------------------------------------------------------------------
:: Request Administrator Privileges
:: ----------------------------------------------------------------------------
ECHO Checking for administrator privileges...
NET SESSION >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    ECHO This script requires administrator privileges.
    ECHO Please right-click the script and select 'Run as administrator'.
    ECHO.
    PAUSE
    EXIT /B 1
)
ECHO Administrator privileges confirmed.
ECHO.

:: ----------------------------------------------------------------------------
:: Add Applications to Run Dialog (App Paths)
:: ----------------------------------------------------------------------------
:: - Adds custom app paths so you can launch them via Run dialog
ECHO Adding custom applications to Run dialog...
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /ve /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\Everything64.exe" /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /v Path /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\" /f
ECHO App Paths added.
ECHO.

:: ----------------------------------------------------------------------------
:: File/Extension Associations
:: ----------------------------------------------------------------------------
:: - Associates certain file types with chosen default programs
ECHO Setting up file/extension associations...
assoc .txt=txtfile
ftype txtfile="%SystemRoot%\System32\notepad.exe" "%%1"

:: Example: Associate .BAT files with Notepad (optional)
:: NOTE: By default, .bat is associated with 'batfile' (to run).
::       Uncomment below if you want to edit .bat by double-click.
:: assoc .bat=batfile
:: ftype batfile="%SystemRoot%\System32\notepad.exe" "%%1"

:: Add more associations as needed...

ECHO File associations set.
ECHO.

:: ----------------------------------------------------------------------------
:: Explorer Customizations
:: ----------------------------------------------------------------------------
:: - Sets Explorer to open 'This PC'
:: - Hides frequent/recent, removes suffixes, etc.
ECHO Customizing File Explorer...

:: Display full path in the File Explorer title bar (0:Hide | 1:Show)
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState" /v FullPath /t REG_DWORD /d 0 /f

:: Change Windows Explorer to automatically open 'This PC'
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v LaunchTo /t REG_DWORD /d 1 /f

:: Remove the "Shortcut" suffix when creating shortcuts
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "ShortcutNameTemplate" /t REG_SZ /d "%%s.lnk" /f

:: Set suffix added to filename when copying files
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "CopyNameTemplate" /t REG_SZ /d "%%s 1" /f

:: Hide frequently and recently used files/folders in quick access
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowFrequent" /t REG_DWORD /d "0" /f
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowRecent" /t REG_DWORD /d "0" /f

:: Disable Automatic Folder Type Discovery
REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f
REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f

ECHO Explorer customizations applied.
ECHO.

:: ----------------------------------------------------------------------------
:: System Behavior
:: ----------------------------------------------------------------------------
ECHO Adjusting system behavior...
ECHO (Combines multiple small tweaks into categories.)
ECHO.

:: ----------------------------------------------------------------------------
:: System: Set Keyboard Delay and Keyboard Repeat Rate
:: ----------------------------------------------------------------------------
:: - KeyboardDelay:0 (minimum)
:: - KeyboardSpeed:31 (maximum)
ECHO Setting keyboard repeat rate and delay...
powershell -NoProfile -Command "$code = '[DllImport(\"user32.dll\")] public static extern bool SystemParametersInfo(uint uiAction, uint uiParam, uint pvParam, uint fWinIni);'; $sysInfo = Add-Type -MemberDefinition $code -Name User32 -PassThru; $sysInfo::SystemParametersInfo(23, 0, 0, 3); $sysInfo::SystemParametersInfo(11, 31, 0, 3)"
ECHO.

:: ----------------------------------------------------------------------------
:: Disable "This App is Preventing Shutdown or Restart" Screen
:: ----------------------------------------------------------------------------
:: - Speeds up shutdown by auto-ending tasks quickly
ECHO Disabling 'Preventing Shutdown' confirmations...
REG ADD "HKCU\Control Panel\Desktop" /v "AutoEndTasks" /t REG_SZ /d "1" /f
REG ADD "HKCU\Control Panel\Desktop" /v "HungAppTimeout" /t REG_SZ /d "1000" /f
REG ADD "HKCU\Control Panel\Desktop" /v "WaitToKillAppTimeout" /t REG_SZ /d "1000" /f
REG ADD "HKLM\System\CurrentControlSet\Control" /v "WaitToKillServiceTimeout" /t REG_SZ /d "1000" /f
ECHO System behavior adjustments applied.
ECHO.

:: ----------------------------------------------------------------------------
:: Control Panel
:: ----------------------------------------------------------------------------
:: - Adds 'All Tasks' (God Mode) in Control Panel
ECHO Configuring Control Panel options...
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "InfoTip" /t REG_SZ /d "View list of all Control Panel tasks" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "System.ControlPanel.Category" /t REG_SZ /d "5" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\DefaultIcon" /ve /t REG_SZ /d "C:\Windows\System32\imageres.dll,-27" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\Shell\Open\Command" /ve /t REG_SZ /d "explorer.exe shell:::{ED7BA470-8E54-465E-825C-99712043E01C}" /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel\NameSpace\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f
ECHO Control Panel options configured.
ECHO.

:: ----------------------------------------------------------------------------
:: Theme and Appearance
:: ----------------------------------------------------------------------------
:: - Enables dark theme, transparency, etc.
ECHO Setting theme and appearance...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f >nul
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f >nul
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 1 /f >nul
ECHO Theme and appearance set.
ECHO.

:: ----------------------------------------------------------------------------
:: Taskbar Customizations
:: ----------------------------------------------------------------------------
:: - Disables Meet Now, People, Weather, News/Interests, aligns taskbar left
ECHO Customizing Taskbar...
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f
REG ADD "HKCU\Software\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced\People" /v PeopleBand /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds" /v EnableFeeds /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Feeds" /v ShellFeedsTaskbarViewMode /t REG_DWORD /d 2 /f
REG ADD HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced /v TaskbarAl /t REG_DWORD /d 0 /f
ECHO Taskbar customizations applied.
ECHO.

:: ----------------------------------------------------------------------------
:: Disable Features
:: ----------------------------------------------------------------------------
:: - Disables Cortana
ECHO Disabling selected features...
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortana /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortanaAboveLock /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaEnabled /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaConsent /t REG_DWORD /d 0 /f
ECHO Selected features disabled.
ECHO.

:: ----------------------------------------------------------------------------
:: Photo Viewer
:: ----------------------------------------------------------------------------
:: - Enables legacy Windows Photo Viewer & sets associations
ECHO Setting up Legacy Photo Viewer...
for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (
    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" /v ".%%~a" /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f
)
for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (
    REG ADD "HKCU\SOFTWARE\Classes\.%%~a" /ve /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f
)
ECHO Legacy Photo Viewer setup attempted. You might need to manually set defaults for some image types.
ECHO.

:: ----------------------------------------------------------------------------
:: Apply System Parameter Changes and Restart Explorer
:: ----------------------------------------------------------------------------
:: - Forces user setting refresh, restarts Explorer
ECHO Applying system parameter changes and restarting Explorer...
RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters
taskkill /f /im explorer.exe
start explorer.exe
ECHO Explorer restarted.
ECHO.

:: ----------------------------------------------------------------------------
:: Exit with message (auto-close after 15 seconds)
:: ----------------------------------------------------------------------------
ECHO.
ECHO All customizations have been applied.
ECHO Window will close automatically in 15 seconds ...
PING 127.0.0.1 -n 16 > NUL
EXIT
:: ----------------------------------------------------------------------------
```

## What Changed / Summary

1. **Section Headers**: Each main section now has:
   - A start/end line of dashes.
   - A concise section title (e.g., `:: System Behavior`).
   - (Optional) bullet lines describing what that section does.
2. **Echo Statements**: Each section starts with an `ECHO` describing the purpose, and ends with an `ECHO` summarizing the action.
3. **Keyboard Delay/Speed**: Uses your specified PowerShell snippet for consistency with the same “pattern” block.

This layout is now consistent across all sections, mirroring the structure you requested.

## Message 14

Sure

## Message 15

please make of the sections concistent, following this pattern:

```

:: ----------------------------------------------------------------------------

:: System: Set Keyboard Delay and Keyboard Repeat Rate

:: ----------------------------------------------------------------------------

:: - KeyboardDelay:0 (minimum)

:: - KeyboardSpeed:31 (maximum)

ECHO Setting keyboard repeat rate and delay...

powershell -NoProfile -Command "$code = '[DllImport(\"user32.dll\")] public static extern bool SystemParametersInfo(uint uiAction, uint uiParam, uint pvParam, uint fWinIni);'; $sysInfo = Add-Type -MemberDefinition $code -Name User32 -PassThru; $sysInfo::SystemParametersInfo(23, 0, 0, 3); $sysInfo::SystemParametersInfo(11, 31, 0, 3)"

ECHO.

```



@ECHO OFF

TITLE Windows Customization Script



:: ----------------------------------------------------------------------------

:: Request Administrator Privileges

:: ----------------------------------------------------------------------------

NET SESSION >nul 2>&1

IF %ERRORLEVEL% NEQ 0 (

    ECHO This script requires administrator privileges.

    ECHO Please right-click the script and select 'Run as administrator'.

    ECHO.

    PAUSE

    EXIT /B 1

)



ECHO Applying registry customizations...

ECHO.



:: ----------------------------------------------------------------------------

:: Add Applications to Run Dialog (App Paths)

:: ----------------------------------------------------------------------------

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /ve /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\Everything64.exe" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /v Path /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\" /f

::



:: ----------------------------------------------------------------------------

:: File/Extension Associations

:: ----------------------------------------------------------------------------

ECHO Setting up file/extension associations...



assoc .txt=txtfile

ftype txtfile="%SystemRoot%\System32\notepad.exe" "%%1"



:: Example: Associate .BAT files with Notepad (optional)

:: NOTE: By default, .bat is associated with 'batfile' (to run).

::       This is only if you want to open/edit .bat with Notepad by double-click.

:: assoc .bat=batfile

:: ftype batfile="%SystemRoot%\System32\notepad.exe" "%%1"



:: Add more associations as needed...



ECHO File associations set.

ECHO.





:: ----------------------------------------------------------------------------

:: Explorer Customizations

:: ----------------------------------------------------------------------------

ECHO Customizing File Explorer...

:: Display full path in the File Explorer title bar (0:Hide | 1:Show)

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState" /v FullPath /t REG_DWORD /d 0 /f



:: Change Windows Explorer to automatically open 'This PC'

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v LaunchTo /t REG_DWORD /d 1 /f



:: Remove the "Shortcut" suffix when creating shortcuts

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "ShortcutNameTemplate" /t REG_SZ /d "%%s.lnk" /f



:: Set suffix added to filename when copying files

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "CopyNameTemplate" /t REG_SZ /d "%%s 1" /f



:: Hide frequently and recently used files/folders in quick access

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowFrequent" /t REG_DWORD /d "0" /f

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowRecent" /t REG_DWORD /d "0" /f



:: Disable Automatic Folder Type Discovery

REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f

REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f

ECHO Explorer customizations applied.

ECHO.



:: ----------------------------------------------------------------------------

:: System Behavior

:: ----------------------------------------------------------------------------

ECHO Adjusting system behavior...



:: ----------------------------------------------------------------------------

:: System: Set Keyboard Delay and Keyboard Repeat Rate

:: ----------------------------------------------------------------------------

:: - KeyboardDelay:0 (minimum)

:: - KeyboardSpeed:31 (maximum)

ECHO Setting keyboard repeat rate and delay...

powershell -NoProfile -Command "$code = '[DllImport(\"user32.dll\")] public static extern bool SystemParametersInfo(uint uiAction, uint uiParam, uint pvParam, uint fWinIni);'; $sysInfo = Add-Type -MemberDefinition $code -Name User32 -PassThru; $sysInfo::SystemParametersInfo(23, 0, 0, 3); $sysInfo::SystemParametersInfo(11, 31, 0, 3)"

ECHO.



:: ----------------------------------------------------------------------------

:: Disable "This App is Preventing Shutdown or Restart" Screen

:: ----------------------------------------------------------------------------

ECHO xxx ...

REG ADD "HKCU\Control Panel\Desktop" /v "AutoEndTasks" /t REG_SZ /d "1" /f

REG ADD "HKCU\Control Panel\Desktop" /v "HungAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKCU\Control Panel\Desktop" /v "WaitToKillAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKLM\System\CurrentControlSet\Control" /v "WaitToKillServiceTimeout" /t REG_SZ /d "1000" /f

ECHO System behavior adjustments applied.

ECHO.



:: ----------------------------------------------------------------------------

:: Control Panel

:: ----------------------------------------------------------------------------

ECHO Configuring Control Panel options...

:: Show all tasks on control panel

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "InfoTip" /t REG_SZ /d "View list of all Control Panel tasks" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "System.ControlPanel.Category" /t REG_SZ /d "5" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\DefaultIcon" /ve /t REG_SZ /d "C:\Windows\System32\imageres.dll,-27" /f

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\Shell\Open\Command" /ve /t REG_SZ /d "explorer.exe shell:::{ED7BA470-8E54-465E-825C-99712043E01C}" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel\NameSpace\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f

ECHO Control Panel options configured.

ECHO.



:: ----------------------------------------------------------------------------

:: Theme and Appearance

:: ----------------------------------------------------------------------------

ECHO Setting theme and appearance...

:: Enable Dark Theme

reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f >nul

reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f >nul

reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 1 /f >nul

ECHO Theme and appearance set.

ECHO.



:: ----------------------------------------------------------------------------

:: Taskbar Customizations

:: ----------------------------------------------------------------------------

ECHO Customizing Taskbar...

:: Taskbar: Disable Meet Now

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

:: Taskbar: Disable People

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

REG ADD "HKCU\Software\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

:: Taskbar: Hide People icon (even if policy is not set)

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced\People" /v PeopleBand /t REG_DWORD /d 0 /f

:: Taskbar: Disable Weather, News and Interests on taskbar

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds" /v EnableFeeds /t REG_DWORD /d 0 /f

:: Taskbar: Hide Weather, News and Interests on taskbar (set view mode to off)

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Feeds" /v ShellFeedsTaskbarViewMode /t REG_DWORD /d 2 /f

:: Changes the Windows 11 taskbar alignment to the left

REG ADD HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced /v TaskbarAl /t REG_DWORD /d 0 /f

ECHO Taskbar customizations applied.

ECHO.



:: ----------------------------------------------------------------------------

:: Disable Features

:: ----------------------------------------------------------------------------

ECHO Disabling selected features...

:: Disable Cortana

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortana /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortanaAboveLock /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaConsent /t REG_DWORD /d 0 /f

ECHO Selected features disabled.

ECHO.



:: ----------------------------------------------------------------------------

:: Photo Viewer

:: ----------------------------------------------------------------------------

ECHO Setting up Legacy Photo Viewer...

:: Enable legacy photo viewer file associations

for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (

    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" /v ".%%~a" /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f

)

:: Set legacy photo viewer as default for current user (this part may require user to choose manually in Default Apps for some extensions)

for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (

    REG ADD "HKCU\SOFTWARE\Classes\.%%~a" /ve /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f

)

ECHO Legacy Photo Viewer setup attempted. You might need to manually set defaults for some image types.

ECHO.



:: ----------------------------------------------------------------------------

:: Apply System Parameter Changes and Restart Explorer

:: ----------------------------------------------------------------------------

ECHO Applying system parameter changes and restarting Explorer...

RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters

taskkill /f /im explorer.exe

start explorer.exe

ECHO Explorer restarted.

ECHO.



:: ----------------------------------------------------------------------------

:: Exit with message (auto-close after 15 seconds)

:: ----------------------------------------------------------------------------

ECHO.

ECHO All customizations have been applied.

ECHO Window will close automatically in 15 seconds ...

PING 127.0.0.1 -n 16 > NUL

EXIT

:: ----------------------------------------------------------------------------

## Message 16

Below is a structured review of your script to confirm its correctness, consistency, and potential optimizations. Overall, your script looks very cohesive—nice job. We’ll walk through each major section to verify that the commands do what you intend on Windows 11.

---

## 1. Request Administrator Privileges

```batch
NET SESSION >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    ECHO This script requires administrator privileges.
    ECHO Please right-click the script and select 'Run as administrator'.
    ECHO.
    PAUSE
    EXIT /B 1
)
```
- **Works**: This is a common check to ensure elevated privileges are present. If not, it instructs the user to re-run as admin.  
- **Consistent**: Good practice.

---

## 2. System: Set PowerShell Execution Policy

```batch
powershell -Command "Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force"
```
- **Works**: This will set the execution policy to `RemoteSigned` for **LocalMachine**.  
- **Consideration**: Changing the machine-level execution policy is somewhat heavy-handed if the user only needs script-running privileges for the local user. But it’s valid if you want to ensure all user accounts can run local scripts.

---

## 3. Add Applications to Run Dialog (App Paths)

```batch
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" ...
```
- **Works**: Adding custom paths under `HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\<appname>.exe` is a standard approach.  
- **Consistent**: Properly sets the default (`/ve`) value and the `Path` value. This should let the user type `Everything.exe` in the Run dialog.

---

## 4. System: Set Default File Associations

```batch
assoc .txt=txtfile
ftype txtfile="%SystemRoot%\System32\notepad.exe" "%%1"
```
- **Works**: Correct for Notepad as the default text editor.  
- **Note**: On Windows 11, MS sometimes tries to push the user to the new “default apps” interface. The association still works at a registry level, though Windows can override or prompt in certain builds. Still standard.

---

## 5. Explorer: Customize Folder Views

```batch
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState" /v FullPath /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v LaunchTo /t REG_DWORD /d 1 /f
REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f
REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f
```
- **Works**:
  - `FullPath=0` → No full path in title bar.  
  - `LaunchTo=1` → Explorer opens to “This PC” instead of Quick Access.  
  - Removing “Bags” resets saved folder views. Then `FolderType=NotSpecified` disables folder-type discovery.  
- **Consistent**: Values are correct for Windows 10/11.

---

## 6. Explorer: Customize Naming Templates

```batch
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "ShortcutNameTemplate" ...
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "CopyNameTemplate" ...
```
- **Works**: This effectively removes “Shortcut” from new shortcuts and sets the suffix for copies to ` 1`.  
- **Consistent**: Correct syntax.

---

## 7. Explorer: Customize Quick Access

```batch
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowFrequent" /t REG_DWORD /d "0" /f
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowRecent" /t REG_DWORD /d "0" /f
```
- **Works**: This should hide frequently/recently used files and folders from Quick Access.

---

## 8. System: Set Keyboard Delay and Repeat Rate

```batch
powershell -NoProfile -Command "$code = '[DllImport(\"user32.dll\")] ...
```
- **Works**: This PInvoke-based approach calls `SystemParametersInfo(SPI_SETKEYBOARDDELAY)` and `SystemParametersInfo(SPI_SETKEYBOARDSPEED)`.  
- **Note**: Typically, for the maximum speed, you pass `31` for `uiParam` in `SPI_SETKEYBOARDSPEED`, and for minimum delay, you pass `0` in `SPI_SETKEYBOARDDELAY` → which you’ve done.  
- **Might Require**: A logoff/logon for the setting to fully apply in some cases. But the script is correct as-is.

---

## 9. System: Set Norwegian Keyboard Layout

```batch
PowerShell -Command "Set-WinUserLanguageList -LanguageList 'nb-NO' -Force"
PowerShell -Command "Set-WinUILanguageOverride -Language 'nb-NO'"
PowerShell -Command "Set-WinSystemLocale -SystemLocale 'nb-NO'"
PowerShell -Command "Set-Culture -CultureInfo 'nb-NO'"
PowerShell -Command "Set-WinHomeLocation -GeoId 177"
```
- **Works**: This forces Windows to use Norwegian (Bokmål).  
- **Consideration**: `Set-WinSystemLocale`, `Set-Culture`, and `Set-WinHomeLocation` typically require a **restart** or log-off/log-on** to fully apply. But it’s correct to set them.  
- **`GeoId 177`**: This is correct for Norway.

---

## 10. System: Speed Up System Shutdown

```batch
REG ADD "HKCU\Control Panel\Desktop" /v "AutoEndTasks" /t REG_SZ /d "1" /f
...
```
- **Works**: Standard “fast shutdown” tweaks.  
- **Consistent**: Values look good for automatically ending tasks, shorter timeouts.

---

## 11. System: Disable Fast Startup & Mouse Acceleration

```batch
REG ADD "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Power" /v HiberbootEnabled /t REG_DWORD /d 0 /f
REG ADD "HKCU\Control Panel\Mouse" /v MouseSpeed /t REG_SZ /d "0" /f
REG ADD "HKCU\Control Panel\Mouse" /v MouseThreshold1 /t REG_SZ /d "0" /f
REG ADD "HKCU\Control Panel\Mouse" /v MouseThreshold2 /t REG_SZ /d "0" /f
```
- **Works**: 
  - `HiberbootEnabled=0` → Disables Fast Startup. Usually needs a reboot to take effect.  
  - `MouseSpeed=0`, `MouseThreshold1=0`, `MouseThreshold2=0` → Disables “Enhanced pointer precision.”  
- **Consistent**: Good.

---

## 12. Explorer: Show all tasks in Control Panel

```batch
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" ...
```
- **Works**: This is the “God Mode” approach. Should still appear in Windows 11, though the UI might present it differently in Control Panel.  
- **Consistent**: Good usage.

---

## 13. Explorer: Show File Extensions and Hidden Files

```batch
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v "HideFileExt" /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v "Hidden" /t REG_DWORD /d 1 /f
```
- **Works**: 
  - `HideFileExt=0` → Show file extensions.  
  - `Hidden=1` → Show hidden files. (0=don’t show, 1=show, 2=show system files too depending on `ShowSuperHidden`. You could also set `ShowSuperHidden=1` if you want to show protected operating system files.)

---

## 14. Explorer: Hide Unnecessary Navigation Items

```batch
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FolderDescriptions\{31C0DD25-9439-4F12-BF41-7FF4EDA38722}\PropertyBag" /v "ThisPCPolicy" /t REG_SZ /d "Hide" /f
...
```
- **Works**: 
  - Hides “3D Objects,” “Music,” etc. from navigation pane.  
  - `System.IsPinnedToNameSpaceTree=0` → hides the item from navigation.  
- **Consistent**: This is a fairly standard tweak.  

---

## 15. Appearance: Set Dark Theme

```batch
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 1 /f
```
- **Works**: Sets Windows 11 into full dark mode with transparency.  
- **No issues**: Good for a dark theme.

---

## 16. Taskbar: Set Icon and Feature Visibility

```batch
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f
...
```
- **Works**: Disables Meet Now, People bar, weather/feeds, sets alignment to the left, hides search box, hides task view.  
- **SearchboxTaskbarMode**: Setting it to `0` hides the search box in Windows 11.  
- **All consistent**: Yes, these are typical. Some might partially be overridden by user settings if changed in the GUI, but they do the job.  

---

## 17. Taskbar: Enable End Task Option

```batch
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced\TaskbarDeveloperSettings" /v TaskbarEndTask /t REG_DWORD /d 1 /f
```
- **Works**: As of Windows 11 22H2 or later Insider builds, this is recognized. It adds an “End Task” entry to the right-click menu for open apps in the taskbar.  
- **Note**: On stable releases before that feature shipped, it might do nothing. But it's correct for newer Windows 11 builds.

---

## 18. Features: Disable Cortana

```batch
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch ...
```
- **Works**: This is a well-known set of registry keys. Cortana is mostly phased out in Windows 11 anyway, but these ensure it’s disabled.

---

## 19. Privacy: Disable Telemetry and Data Collection

```batch
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\AdvertisingInfo" /v Enabled /t REG_DWORD /d 0 /f
...
```
- **Works**: Minimizes telemetry and ad/diagnostic data. Some settings might be partially ignored in Windows 11 Home vs. Pro, but they do no harm and usually reduce telemetry if you have Pro/Enterprise with group policy.  
- **No issues**: Good approach.

---

## 20. Privacy: Disable Windows Suggestions and Tips

```batch
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SubscribedContent-338393Enabled /t REG_DWORD /d 0 /f
...
```
- **Works**: This turns off tips, suggestions, preinstalled apps, etc.  
- **Consistent**: Good approach for a less cluttered experience.

---

## 21. Apps: Enable Legacy Windows Photo Viewer

```batch
for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (
    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" ...
)
```
- **Works**: Re-adds Photo Viewer for image file types.  
- **Manual Action**: Yes, the user typically must set it as default in Settings → Apps → Default Apps for certain file types. This is expected.

---

## 22. Context Menu: Clean Up Unnecessary Options

```batch
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Shell Extensions\Blocked" ...
```
- **Works**: Blocks various shell extensions so they no longer show “Share,” “Give access to…,” etc. in the context menu.  
- **Consistent**: Good.

---

## 23. Search: Disable Bing Integration and Web Search

```batch
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v BingSearchEnabled /t REG_DWORD /d 0 /f
...
```
- **Works**: Disables Bing-based web results in search.  
- **Note**: On some builds, additional keys (like `AllowSearchToUseLocation=0`) might be helpful, but what you have is sufficient.

---

## 24. Start Menu: Disable Recommendations and Improve Performance

```batch
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v Start_ShowRecommendations /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HideRecommendedSection /t REG_DWORD /d 1 /f
```
- **Works**: Removes recommended section in Start menu for Windows 11.  
- **No issues**: Looks correct.

---

## 25. Apply System Parameter Changes and Restart Explorer

```batch
RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters
taskkill /f /im explorer.exe
start explorer.exe
```
- **Works**: Forces some user-setting refresh, then restarts Explorer. This is a standard approach.  
- **Note**: Some changes (like keyboard layout or region changes) might not apply until sign out or reboot, but the Explorer restart covers many shell tweaks.  

---

## 26. Final Exit

```batch
ECHO All customizations have been applied.
ECHO Window will close automatically in 15 seconds ...
PING 127.0.0.1 -n 16 > NUL
EXIT
```
- **Works**: Gives user a message, then auto-exits.

---

# Final Assessment

1. **Correctness**: All major registry tweaks and PowerShell commands match known methods to configure Windows 11.  
2. **Consistency**: Each section has a descriptive heading, short bullet for what it does, then the necessary commands. The script is very readable.  
3. **Potential Issues**:  
   - Some settings (language, fast startup disable, or advanced telemetry changes) may require a **reboot** or a full sign-out/in to take effect. The script is correct but user must typically do that step.  
   - Over time, Microsoft might rename or override certain keys, but currently these are the recognized ones.  
4. **Optimizations**:  
   - *Optional*: If you want to ensure certain changes absolutely take effect (like keyboard layout or system locale), you could schedule a forced reboot. But that’s a design choice.  
   - *Optional*: You may want to store repeated registry paths or script segments in variables, though that’s more a matter of personal preference than an actual performance improvement.

**Conclusion**: Your script is **well-structured** and **fully functional** for Windows 11. All registry values and commands appear valid, and the layout is consistent with your described pattern. It’s a great “one-stop” script for Windows 11 customizations.

## Message 17

please go through and check/verify that all of the updates in the provided script works correctly and is concistent and optimized:

@ECHO OFF

SETLOCAL ENABLEDELAYEDEXPANSION

IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

TITLE Windows Customization Script



:: This script customizes Windows 11 settings.

:: - It should be run as an Administrator.



:: ----------------------------------------------------------------------------

:: Request Administrator Privileges

:: ----------------------------------------------------------------------------

NET SESSION >nul 2>&1

IF %ERRORLEVEL% NEQ 0 (

    ECHO This script requires administrator privileges.

    ECHO Please right-click the script and select 'Run as administrator'.

    ECHO.

    PAUSE

    EXIT /B 1

)



:: ----------------------------------------------------------------------------

:: System: Set PowerShell Execution Policy

:: ----------------------------------------------------------------------------

:: - ExecutionPolicy:RemoteSigned (Allows local scripts to run)

:: - Scope:LocalMachine (Applies to all users)

ECHO Setting PowerShell Execution Policy...

powershell -Command "Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force"

ECHO.



:: ----------------------------------------------------------------------------

:: Add Applications to Run Dialog (App Paths)

:: ----------------------------------------------------------------------------

:: - Path: C:\...\Everything64.exe (Sets the application path for the Run dialog)

ECHO Configuring App Paths for quick launch...

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /ve /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\Everything64.exe" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /v Path /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\" /f

ECHO.



:: ----------------------------------------------------------------------------

:: System: Set Default File Associations

:: ----------------------------------------------------------------------------

:: - .txt:Notepad.exe

ECHO Setting default file associations...

assoc .txt=txtfile

ftype txtfile="%SystemRoot%\System32\notepad.exe" "%%1"

ECHO.



:: Example: Associate .BAT files with Notepad (optional)

:: NOTE: By default, .bat is associated with 'batfile' (to run).

::       This is only if you want to open/edit .bat with Notepad by double-click.

:: assoc .bat=batfile

:: ftype batfile="%SystemRoot%\System32\notepad.exe" "%%1"



:: Add more associations as needed...



:: ----------------------------------------------------------------------------

:: Explorer: Customize Folder Views

:: ----------------------------------------------------------------------------

:: - FullPath:0 (Hide full path in title bar)

:: - LaunchTo:1 (Open Explorer to 'This PC')

:: - FolderType:NotSpecified (Disables automatic folder type discovery)

ECHO Customizing File Explorer folder views...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState" /v FullPath /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v LaunchTo /t REG_DWORD /d 1 /f

REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f >nul 2>&1

REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Customize Naming Templates

:: ----------------------------------------------------------------------------

:: - ShortcutNameTemplate:%%s.lnk (Removes '- Shortcut' suffix)

:: - CopyNameTemplate:%%s 1 (Changes '- Copy' suffix)

ECHO Customizing file naming templates...

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "ShortcutNameTemplate" /t REG_SZ /d "%%s.lnk" /f

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "CopyNameTemplate" /t REG_SZ /d "%%s 1" /f

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Customize Quick Access

:: ----------------------------------------------------------------------------

:: - ShowFrequent:0 (Hide frequently used folders)

:: - ShowRecent:0 (Hide recently used files)

ECHO Customizing Quick Access history...

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowFrequent" /t REG_DWORD /d "0" /f

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowRecent" /t REG_DWORD /d "0" /f

ECHO.



:: ----------------------------------------------------------------------------

:: System: Set Keyboard Delay and Keyboard Repeat Rate

:: ----------------------------------------------------------------------------

:: - KeyboardDelay:0 (minimum)

:: - KeyboardSpeed:31 (maximum)

ECHO Setting keyboard repeat rate and delay...

powershell -NoProfile -Command "$code = '[DllImport(\"user32.dll\")] public static extern bool SystemParametersInfo(uint uiAction, uint uiParam, uint pvParam, uint fWinIni);'; $sysInfo = Add-Type -MemberDefinition $code -Name User32 -PassThru; $sysInfo::SystemParametersInfo(23, 0, 0, 3); $sysInfo::SystemParametersInfo(11, 31, 0, 3)"

ECHO.



:: ----------------------------------------------------------------------------

:: System: Set Norwegian Keyboard Layout

:: ----------------------------------------------------------------------------

:: - LanguageList:nb-NO (Norwegian, Bokmål)

ECHO Setting Norwegian (Bokmål) as the default keyboard layout...

PowerShell -Command "Set-WinUserLanguageList -LanguageList 'nb-NO' -Force"

PowerShell -Command "Set-WinUILanguageOverride -Language 'nb-NO'"

PowerShell -Command "Set-WinSystemLocale -SystemLocale 'nb-NO'"

PowerShell -Command "Set-Culture -CultureInfo 'nb-NO'"

PowerShell -Command "Set-WinHomeLocation -GeoId 177"

ECHO.



:: ----------------------------------------------------------------------------

:: System: Speed Up System Shutdown

:: ----------------------------------------------------------------------------

:: - AutoEndTasks:1 (Automatically end tasks without waiting for user input)

:: - HungAppTimeout:1000 (Timeout for frozen apps in ms)

:: - WaitToKillAppTimeout:1000 (Timeout before killing apps in ms)

:: - WaitToKillServiceTimeout:1000 (Timeout for services in ms)

ECHO Speeding up system shutdown...

REG ADD "HKCU\Control Panel\Desktop" /v "AutoEndTasks" /t REG_SZ /d "1" /f

REG ADD "HKCU\Control Panel\Desktop" /v "HungAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKCU\Control Panel\Desktop" /v "WaitToKillAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKLM\System\CurrentControlSet\Control" /v "WaitToKillServiceTimeout" /t REG_SZ /d "1000" /f

ECHO.



:: ----------------------------------------------------------------------------

:: System: Disable Fast Startup and Mouse Acceleration

:: ----------------------------------------------------------------------------

:: - HiberbootEnabled:0 (Disable fast startup for better stability)

:: - MouseSpeed:0, MouseThreshold1:0, MouseThreshold2:0 (Disable mouse acceleration)

ECHO Disabling fast startup and mouse acceleration...

REG ADD "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Power" /v HiberbootEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Control Panel\Mouse" /v MouseSpeed /t REG_SZ /d "0" /f

REG ADD "HKCU\Control Panel\Mouse" /v MouseThreshold1 /t REG_SZ /d "0" /f

REG ADD "HKCU\Control Panel\Mouse" /v MouseThreshold2 /t REG_SZ /d "0" /f

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Show all tasks in Control Panel

:: ----------------------------------------------------------------------------

:: - CLSID: {D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}

ECHO Show all tasks in Control Panel ...

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f >nul

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "InfoTip" /t REG_SZ /d "View list of all Control Panel tasks" /f >nul

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "System.ControlPanel.Category" /t REG_SZ /d "5" /f >nul

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\DefaultIcon" /ve /t REG_SZ /d "C:\Windows\System32\imageres.dll,-27" /f >nul

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\Shell\Open\Command" /ve /t REG_SZ /d "explorer.exe shell:::{ED7BA470-8E54-465E-825C-99712043E01C}" /f >nul

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel\NameSpace\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f >nul

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Show File Extensions and Hidden Files

:: ----------------------------------------------------------------------------

:: - HideFileExt:0 (Show file extensions for known file types)

:: - Hidden:1 (Show hidden files and folders)

ECHO Configuring File Explorer visibility settings...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v "HideFileExt" /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v "Hidden" /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Hide Unnecessary Navigation Items

:: ----------------------------------------------------------------------------

:: - Hide 3D Objects folder (rarely used)

:: - Hide Music folder from navigation pane

:: - Hide duplicate removable drives

ECHO Cleaning up File Explorer navigation pane...

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FolderDescriptions\{31C0DD25-9439-4F12-BF41-7FF4EDA38722}\PropertyBag" /v "ThisPCPolicy" /t REG_SZ /d "Hide" /f

REG ADD "HKLM\SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Explorer\FolderDescriptions\{31C0DD25-9439-4F12-BF41-7FF4EDA38722}\PropertyBag" /v "ThisPCPolicy" /t REG_SZ /d "Hide" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FolderDescriptions\{a0c69a99-21c8-4671-8703-7934162fcf1d}\PropertyBag" /v "ThisPCPolicy" /t REG_SZ /d "Hide" /f

REG ADD "HKLM\SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Explorer\FolderDescriptions\{a0c69a99-21c8-4671-8703-7934162fcf1d}\PropertyBag" /v "ThisPCPolicy" /t REG_SZ /d "Hide" /f

REG ADD "HKCU\Software\Classes\CLSID\{F5FB2C77-0E2F-4A16-A381-3E560C68BC83}" /v "System.IsPinnedToNameSpaceTree" /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Appearance: Set Dark Theme and Transparency

:: ----------------------------------------------------------------------------

:: - AppsUseLightTheme:0 (Dark Mode for Apps)

:: - SystemUsesLightTheme:0 (Dark Mode for System UI)

:: - EnableTransparency:1 (On)

ECHO Enabling system-wide dark theme and transparency...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f >nul

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f >nul

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 1 /f >nul

ECHO.



:: ----------------------------------------------------------------------------

:: Taskbar: Set Icon and Feature Visibility

:: ----------------------------------------------------------------------------

:: - HideSCAMeetNow:1 (Hide 'Meet Now' icon)

:: - HidePeopleBar:1 (Hide 'People' bar)

:: - PeopleBand:0 (Hide 'People' icon)

:: - EnableFeeds:0 (Disable 'Widgets' feature)

:: - ShellFeedsTaskbarViewMode:2 (Hide 'Widgets' icon)

:: - TaskbarAl:0 (Left alignment)

:: - SearchboxTaskbarMode:0 (Hide search box)

:: - ShowTaskViewButton:0 (Hide task view button)

ECHO Customizing taskbar icon visibility...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

REG ADD "HKCU\Software\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced\People" /v PeopleBand /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds" /v EnableFeeds /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Feeds" /v ShellFeedsTaskbarViewMode /t REG_DWORD /d 2 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v TaskbarAl /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v SearchboxTaskbarMode /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v ShowTaskViewButton /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Taskbar: Enable End Task Option

:: ----------------------------------------------------------------------------

:: - TaskbarEndTask:1 (Enable "End Task" option in taskbar context menu)

ECHO Enabling End Task option in taskbar...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced\TaskbarDeveloperSettings" /v TaskbarEndTask /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Features: Disable Features

:: ----------------------------------------------------------------------------

:: - AllowCloudSearch:0

:: - AllowCortana:0

:: - AllowCortanaAboveLock:0

:: - CortanaEnabled:0

:: - CortanaConsent:0

ECHO Disabling Cortana integration...

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortana /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortanaAboveLock /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaConsent /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Privacy: Disable Telemetry and Data Collection

:: ----------------------------------------------------------------------------

:: - Enabled:0 (Disable advertising ID)

:: - TailoredExperiencesWithDiagnosticDataEnabled:0 (Disable tailored experiences)

:: - AllowTelemetry:0 (Send only required diagnostic data)

:: - Start_TrackProgs:0 (Disable app launch tracking)

ECHO Disabling telemetry and data collection...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\AdvertisingInfo" /v Enabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Privacy" /v TailoredExperiencesWithDiagnosticDataEnabled /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\DataCollection" /v AllowTelemetry /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v Start_TrackProgs /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\System" /v PublishUserActivities /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Privacy: Disable Windows Suggestions and Tips

:: ----------------------------------------------------------------------------

:: - SubscribedContent-338393Enabled:0 (Disable suggestions in Settings)

:: - SubscribedContent-353694Enabled:0 (Disable suggestions in Start)

:: - SubscribedContent-353696Enabled:0 (Disable suggestions in Timeline)

ECHO Disabling Windows suggestions and tips...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SubscribedContent-338393Enabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SubscribedContent-353694Enabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SubscribedContent-353696Enabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SystemPaneSuggestionsEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SilentInstalledAppsEnabled /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Apps: Enable Legacy Windows Photo Viewer

:: ----------------------------------------------------------------------------

:: - Registers Photo Viewer capabilities for image file types.

ECHO Enabling Legacy Windows Photo Viewer...

for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (

    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" /v ".%%~a" /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f >nul

)

ECHO Legacy Photo Viewer has been enabled.

ECHO MANUAL ACTION REQUIRED: You must set it as the default photo app in Windows Settings.

ECHO Go to: Settings ^> Apps ^> Default apps ^> Photos.

ECHO.



:: ----------------------------------------------------------------------------

:: Context Menu: Clean Up Unnecessary Options

:: ----------------------------------------------------------------------------

:: - Hide "Share", "Give access to", and "Include in library" options

ECHO Cleaning up context menu options...

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Shell Extensions\Blocked" /v "{e2bf9676-5f8f-435c-97eb-11607a5bedf7}" /t REG_SZ /d "" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Shell Extensions\Blocked" /v "{f81e9010-6ea4-11ce-a7ff-00aa003ca9f6}" /t REG_SZ /d "" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Shell Extensions\Blocked" /v "{596AB062-B4D2-4215-9F74-E9109B0A8153}" /t REG_SZ /d "" /f

ECHO.



:: ----------------------------------------------------------------------------

:: Search: Disable Bing Integration and Web Search

:: ----------------------------------------------------------------------------

:: - BingSearchEnabled:0 (Disable Bing web search in Start menu)

:: - CortanaConsent:0 (Disable Cortana consent)

:: - DisableSearchBoxSuggestions:1 (Disable search suggestions)

ECHO Disabling Bing search and web search integration...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v BingSearchEnabled /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v DisableWebSearch /t REG_DWORD /d 1 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v ConnectedSearchUseWeb /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v DisableSearchBoxSuggestions /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Start Menu: Disable Recommendations and Improve Performance

:: ----------------------------------------------------------------------------

:: - ShowRecommendations:0 (Hide recommended section in Start menu)

:: - Start_Layout:1 (Use more space for pinned apps)

ECHO Optimizing Start menu layout and recommendations...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v Start_ShowRecommendations /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HideRecommendedSection /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Apply System Parameter Changes and Restart Explorer

:: ----------------------------------------------------------------------------

ECHO Applying system parameter changes and restarting Explorer...

RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters

taskkill /f /im explorer.exe

start explorer.exe

ECHO Explorer restarted.

ECHO.



:: ----------------------------------------------------------------------------

:: Exit with message (auto-close after 15 seconds)

:: ----------------------------------------------------------------------------

ECHO.

ECHO All customizations have been applied.

ECHO Window will close automatically in 15 seconds ...

PING 127.0.0.1 -n 16 > NUL

EXIT

:: ----------------------------------------------------------------------------

## Message 18

**Organizing system settings**

OK, let me see. Beginning with system-level and progression through Explorer and appearance settings. Arriving at taskbar, privacy, and apps configurations.

## Message 19

Below is a recommended, *optimized* ordering of sections that groups related system modifications together. This order helps ensure that broader system settings (like keyboard, power settings, language, etc.) come before shell/UI customizations (Explorer, Taskbar). Privacy/telemetry tweaks are often kept together for clarity. Finally, we apply changes and restart Explorer at the end.

> **Important:** The order isn’t *strictly* mandatory—Windows will still process most of these registry changes whenever they appear. But logically, this order groups related categories (System, Explorer, Taskbar, Privacy, etc.) and ensures that foundational system tweaks (like language and fast startup) happen before layering on shell/Explorer customizations.

---

## 1. Request Admin + Preliminary Setup

1. **Request Administrator Privileges**  
2. **Set PowerShell Execution Policy**  
3. **Add Applications to Run Dialog**  
4. **Set Default File Associations**  

This cluster deals with **initial environment** setup—ensuring admin rights, letting PowerShell scripts run, setting up path shortcuts, and basic file-type associations.

---

## 2. System-Level Changes (Core OS Settings)

1. **Set Keyboard Delay and Repeat Rate**  
2. **Set Norwegian Keyboard Layout**  
3. **Speed Up System Shutdown**  
4. **Disable Fast Startup and Mouse Acceleration**  
5. **Disable Xbox Game DVR (Gaming)**  
6. **Disable Sticky Keys Shortcut**  

These changes affect core system behavior: language/keyboard, shutdown times, power settings, gaming, and accessibility. Grouping them together clarifies that these are broad OS-level tweaks.

---

## 3. Explorer Customizations

1. **Customize Folder Views** (FullPath/LaunchTo/Bags)  
2. **Customize Naming Templates** (Shortcuts, CopyName)  
3. **Customize Quick Access** (Hide frequent/recent)  
4. **Show all tasks in Control Panel** (aka “God Mode”)  
5. **Show File Extensions and Hidden Files**  
6. **Hide Unnecessary Navigation Items**  

These sections revolve around how Explorer behaves (folder views, quick access, control panel, navigation pane). Putting them together makes sense as they’re all about the File Explorer experience.

---

## 4. Appearance and UI Theming

1. **Set Dark Theme and Transparency**  

This is a straightforward tweak for visuals and can come right after Explorer modifications.

---

## 5. Taskbar Customizations

1. **Set Icon and Feature Visibility** (Meet Now, Widgets, alignment, etc.)  
2. **Enable End Task Option**  

All changes to the Taskbar or Taskbar context menu logically belong in a single block.

---

## 6. Disabling Unwanted Windows Features

1. **Disable Cortana**  
2. **Disable Copilot and AI Features**  
3. **Disable Windows Recall Snapshots (Windows AI)**  

Here, we group all the “feature removal” steps to handle built-in Windows 11 features you don’t want.

---

## 7. Privacy & Telemetry

1. **Disable Telemetry/Data Collection**  
2. **Disable Windows Suggestions and Tips**  
3. **Disable Lockscreen Tips and Settings Home**  

All privacy/telemetry toggles are best grouped so you can see at a glance how you’re limiting data collection and suggestions.

---

## 8. Other Custom App Tweaks

1. **Enable Legacy Windows Photo Viewer**  

You might put **Photo Viewer** and any other “apps” sections here—these are specialized items that don’t fit into Explorer or system-level categories.

---

## 9. Context Menu & Search

1. **Clean Up Context Menu Options**  
2. **Disable Bing Integration and Web Search**  

These revolve around right-click menu items and the Windows Search experience. They’re often UI-level preferences, so we keep them together near the end.

---

## 10. Start Menu Tweaks

1. **Disable Recommendations**  
2. **Improve Start Menu Layout**  

Finish off with Start Menu changes—also UI-level but separate enough from the taskbar to have its own section.

---

## 11. Finalize Changes

1. **Apply System Parameter Changes and Restart Explorer**  
2. **Exit with 15-Second Countdown**  

Finally, we refresh user settings, restart Explorer (to load the new shell changes), and then exit.  

---

# Example Reordered Outline

Here’s how your script might look when sections are re-sequenced according to the logic above:

1. **Request Administrator Privileges**  
2. **Set PowerShell Execution Policy**  
3. **Add Applications to Run Dialog**  
4. **Set Default File Associations**  
5. **System-Level Changes**  
   - Keyboard Delay/Repeat  
   - Norwegian Layout  
   - Speed Up Shutdown  
   - Disable Fast Startup & Mouse Acceleration  
   - Disable Game DVR  
   - Disable Sticky Keys  
6. **Explorer Customizations**  
   - Folder Views  
   - Naming Templates  
   - Quick Access  
   - “All Tasks” in Control Panel  
   - Show File Extensions/Hidden Files  
   - Hide Navigation Items  
7. **Appearance: Dark Theme/Transparency**  
8. **Taskbar Customizations**  
   - Icon Visibility, alignment, etc.  
   - Enable End Task Option  
9. **Disable Windows Features**  
   - Cortana  
   - Copilot + AI  
10. **Privacy & Telemetry**  
   - Disable Telemetry & Data Collection  
   - Disable Suggestions & Tips  
   - Disable Lockscreen Tips  
11. **Enable Legacy Windows Photo Viewer**  
12. **Context Menu Cleanup**  
13. **Search: Disable Bing Integration**  
14. **Start Menu Tweaks**  
   - Hide Recommendations  
   - More pinned apps  
15. **Apply Changes & Restart Explorer**  
16. **Exit**  

Following this *flow*, the most “global” or “foundation” changes happen first, then narrower UI customizations, then final niceties or advanced toggles. This should make it clearer for anyone reading or maintaining the script.

## Message 20

Can you please go through and make sure the *order* of each section is optimally set?



```

@ECHO OFF

SETLOCAL ENABLEDELAYEDEXPANSION

IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

TITLE Windows Customization Script



:: This script customizes Windows 11 settings.

:: - It should be run as an Administrator.



:: ----------------------------------------------------------------------------

:: Request Administrator Privileges

:: ----------------------------------------------------------------------------

NET SESSION >nul 2>&1

IF %ERRORLEVEL% NEQ 0 (

    ECHO This script requires administrator privileges.

    ECHO Please right-click the script and select 'Run as administrator'.

    ECHO.

    PAUSE

    EXIT /B 1

)



:: ----------------------------------------------------------------------------

:: System: Set PowerShell Execution Policy

:: ----------------------------------------------------------------------------

:: - ExecutionPolicy:RemoteSigned (Allows local scripts to run)

:: - Scope:LocalMachine (Applies to all users)

ECHO Setting PowerShell Execution Policy...

powershell -Command "Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force"

ECHO.



:: ----------------------------------------------------------------------------

:: Add Applications to Run Dialog (App Paths)

:: ----------------------------------------------------------------------------

:: - Path: C:\...\Everything64.exe (Sets the application path for the Run dialog)

ECHO Configuring App Paths for quick launch...

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /ve /t REG_SZ /d "%USERPROFILE%\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\Everything64.exe" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /v Path /t REG_SZ /d "%USERPROFILE%\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\" /f

ECHO.



:: ----------------------------------------------------------------------------

:: System: Set Default File Associations

:: ----------------------------------------------------------------------------

:: - .txt:Notepad.exe

ECHO Setting default file associations...

assoc .txt=txtfile

ftype txtfile="%SystemRoot%\System32\notepad.exe" "%%1"

ECHO.



:: Example: Associate .BAT files with Notepad (optional)

:: NOTE: By default, .bat is associated with 'batfile' (to run).

::       This is only if you want to open/edit .bat with Notepad by double-click.

:: assoc .bat=batfile

:: ftype batfile="%SystemRoot%\System32\notepad.exe" "%%1"



:: Add more associations as needed...



:: ----------------------------------------------------------------------------

:: Explorer: Customize Folder Views

:: ----------------------------------------------------------------------------

:: - FullPath:0 (Hide full path in title bar)

:: - LaunchTo:1 (Open Explorer to 'This PC')

:: - FolderType:NotSpecified (Disables automatic folder type discovery)

ECHO Customizing File Explorer folder views...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState" /v FullPath /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v LaunchTo /t REG_DWORD /d 1 /f

REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f >nul 2>&1

REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Customize Naming Templates

:: ----------------------------------------------------------------------------

:: - ShortcutNameTemplate:%%s.lnk (Removes '- Shortcut' suffix)

:: - CopyNameTemplate:%%s 1 (Changes '- Copy' suffix)

ECHO Customizing file naming templates...

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "ShortcutNameTemplate" /t REG_SZ /d "%%s.lnk" /f

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "CopyNameTemplate" /t REG_SZ /d "%%s 1" /f

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Customize Quick Access

:: ----------------------------------------------------------------------------

:: - ShowFrequent:0 (Hide frequently used folders)

:: - ShowRecent:0 (Hide recently used files)

ECHO Customizing Quick Access history...

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowFrequent" /t REG_DWORD /d "0" /f

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowRecent" /t REG_DWORD /d "0" /f

ECHO.



:: ----------------------------------------------------------------------------

:: System: Set Keyboard Delay and Keyboard Repeat Rate

:: ----------------------------------------------------------------------------

:: - KeyboardDelay:0 (minimum)

:: - KeyboardSpeed:31 (maximum)

ECHO Setting keyboard repeat rate and delay...

powershell -NoProfile -Command "$code = '[DllImport(\"user32.dll\")] public static extern bool SystemParametersInfo(uint uiAction, uint uiParam, uint pvParam, uint fWinIni);'; $sysInfo = Add-Type -MemberDefinition $code -Name User32 -PassThru; $sysInfo::SystemParametersInfo(23, 0, 0, 3); $sysInfo::SystemParametersInfo(11, 31, 0, 3)"

ECHO.



:: ----------------------------------------------------------------------------

:: System: Set Norwegian Keyboard Layout

:: ----------------------------------------------------------------------------

:: - LanguageList:nb-NO (Norwegian, Bokmål)

ECHO Setting Norwegian (Bokmål) as the default keyboard layout...

PowerShell -Command "Set-WinUserLanguageList -LanguageList 'nb-NO' -Force"

PowerShell -Command "Set-WinUILanguageOverride -Language 'nb-NO'"

PowerShell -Command "Set-WinSystemLocale -SystemLocale 'nb-NO'"

PowerShell -Command "Set-Culture -CultureInfo 'nb-NO'"

PowerShell -Command "Set-WinHomeLocation -GeoId 177"

ECHO.



:: ----------------------------------------------------------------------------

:: System: Speed Up System Shutdown

:: ----------------------------------------------------------------------------

:: - AutoEndTasks:1 (Automatically end tasks without waiting for user input)

:: - HungAppTimeout:1000 (Timeout for frozen apps in ms)

:: - WaitToKillAppTimeout:1000 (Timeout before killing apps in ms)

:: - WaitToKillServiceTimeout:1000 (Timeout for services in ms)

ECHO Speeding up system shutdown...

REG ADD "HKCU\Control Panel\Desktop" /v "AutoEndTasks" /t REG_SZ /d "1" /f

REG ADD "HKCU\Control Panel\Desktop" /v "HungAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKCU\Control Panel\Desktop" /v "WaitToKillAppTimeout" /t REG_SZ /d "1000" /f

REG ADD "HKLM\System\CurrentControlSet\Control" /v "WaitToKillServiceTimeout" /t REG_SZ /d "1000" /f

ECHO.



:: ----------------------------------------------------------------------------

:: System: Disable Fast Startup and Mouse Acceleration

:: ----------------------------------------------------------------------------

:: - HiberbootEnabled:0 (Disable fast startup for better stability)

:: - Mouse Acceleration:Disabled

ECHO Disabling fast startup and mouse acceleration...

REG ADD "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Power" /v HiberbootEnabled /t REG_DWORD /d 0 /f

powershell -NoProfile -Command "$code = '[DllImport(\"user32.dll\")] public static extern bool SystemParametersInfo(uint uiAction, uint uiParam, IntPtr pvParam, uint fWinIni);'; $sysInfo = Add-Type -MemberDefinition $code -Name User32 -PassThru; $sysInfo::SystemParametersInfo(113, 0, [IntPtr]::Zero, 3)"

ECHO.



:: ----------------------------------------------------------------------------

:: Gaming: Disable Xbox Game DVR and Recording

:: ----------------------------------------------------------------------------

:: - GameDVR_Enabled:0 (Disable Xbox Game DVR)

:: - AllowGameDVR:0 (Disable Game DVR policy)

ECHO Disabling Xbox Game DVR and recording features...

REG ADD "HKCU\System\GameConfigStore" /v GameDVR_Enabled /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\GameDVR" /v AllowGameDVR /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: System: Disable Sticky Keys Shortcut

:: ----------------------------------------------------------------------------

:: - Flags:506 (Disable Sticky Keys activation shortcut)

ECHO Disabling Sticky Keys shortcut activation...

REG ADD "HKCU\Control Panel\Accessibility\StickyKeys" /v Flags /t REG_SZ /d "506" /f

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Show all tasks in Control Panel

:: ----------------------------------------------------------------------------

:: - CLSID: {D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}

ECHO Show all tasks in Control Panel ...

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f >nul

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "InfoTip" /t REG_SZ /d "View list of all Control Panel tasks" /f >nul

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "System.ControlPanel.Category" /t REG_SZ /d "5" /f >nul

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\DefaultIcon" /ve /t REG_SZ /d "C:\Windows\System32\imageres.dll,-27" /f >nul

REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\Shell\Open\Command" /ve /t REG_SZ /d "explorer.exe shell:::{ED7BA470-8E54-465E-825C-99712043E01C}" /f >nul

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel\NameSpace\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f >nul

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Show File Extensions and Hidden Files

:: ----------------------------------------------------------------------------

:: - HideFileExt:0 (Show file extensions for known file types)

:: - Hidden:1 (Show hidden files and folders)

ECHO Configuring File Explorer visibility settings...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v "HideFileExt" /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v "Hidden" /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Explorer: Hide Unnecessary Navigation Items

:: ----------------------------------------------------------------------------

:: - Hide 3D Objects folder (rarely used)

:: - Hide Music folder from navigation pane

:: - Hide duplicate removable drives

:: - Hide Home and Gallery sections (Windows 11)

ECHO Cleaning up File Explorer navigation pane...

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FolderDescriptions\{31C0DD25-9439-4F12-BF41-7FF4EDA38722}\PropertyBag" /v "ThisPCPolicy" /t REG_SZ /d "Hide" /f

REG ADD "HKLM\SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Explorer\FolderDescriptions\{31C0DD25-9439-4F12-BF41-7FF4EDA38722}\PropertyBag" /v "ThisPCPolicy" /t REG_SZ /d "Hide" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FolderDescriptions\{a0c69a99-21c8-4671-8703-7934162fcf1d}\PropertyBag" /v "ThisPCPolicy" /t REG_SZ /d "Hide" /f

REG ADD "HKLM\SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Explorer\FolderDescriptions\{a0c69a99-21c8-4671-8703-7934162fcf1d}\PropertyBag" /v "ThisPCPolicy" /t REG_SZ /d "Hide" /f

REG ADD "HKCU\Software\Classes\CLSID\{F5FB2C77-0E2F-4A16-A381-3E560C68BC83}" /v "System.IsPinnedToNameSpaceTree" /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Classes\CLSID\{f874310e-b6b7-47dc-bc84-b9e6b38f5903}" /v "System.IsPinnedToNameSpaceTree" /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Classes\CLSID\{e88865ea-0e1c-4e20-9aa6-edcd0212c87c}" /v "System.IsPinnedToNameSpaceTree" /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Appearance: Set Dark Theme and Transparency

:: ----------------------------------------------------------------------------

:: - AppsUseLightTheme:0 (Dark Mode for Apps)

:: - SystemUsesLightTheme:0 (Dark Mode for System UI)

:: - EnableTransparency:1 (On)

ECHO Enabling system-wide dark theme and transparency...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f >nul

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f >nul

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 1 /f >nul

ECHO.



:: ----------------------------------------------------------------------------

:: Taskbar: Set Icon and Feature Visibility

:: ----------------------------------------------------------------------------

:: - HideSCAMeetNow:1 (Hide 'Meet Now' icon)

:: - HidePeopleBar:1 (Hide 'People' bar)

:: - PeopleBand:0 (Hide 'People' icon)

:: - EnableFeeds:0 (Disable 'Widgets' feature)

:: - ShellFeedsTaskbarViewMode:2 (Hide 'Widgets' icon)

:: - TaskbarAl:0 (Left alignment)

:: - SearchboxTaskbarMode:0 (Hide search box)

:: - ShowTaskViewButton:0 (Hide task view button)

ECHO Customizing taskbar icon visibility...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

REG ADD "HKCU\Software\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f

REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced\People" /v PeopleBand /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds" /v EnableFeeds /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Feeds" /v ShellFeedsTaskbarViewMode /t REG_DWORD /d 2 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v TaskbarAl /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v SearchboxTaskbarMode /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v ShowTaskViewButton /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Taskbar: Enable End Task Option

:: ----------------------------------------------------------------------------

:: - TaskbarEndTask:1 (Enable "End Task" option in taskbar context menu)

ECHO Enabling End Task option in taskbar...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced\TaskbarDeveloperSettings" /v TaskbarEndTask /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Features: Disable Features

:: ----------------------------------------------------------------------------

:: - AllowCloudSearch:0

:: - AllowCortana:0

:: - AllowCortanaAboveLock:0

:: - CortanaEnabled:0

:: - CortanaConsent:0

ECHO Disabling Cortana integration...

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortana /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortanaAboveLock /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaConsent /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Windows 11: Disable Copilot and AI Features

:: ----------------------------------------------------------------------------

:: - ShowCopilotButton:0 (Hide Copilot button from taskbar)

:: - TurnOffWindowsCopilot:1 (Disable Copilot service)

ECHO Disabling Microsoft Copilot and AI features...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v ShowCopilotButton /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsCopilot" /v TurnOffWindowsCopilot /t REG_DWORD /d 1 /f

REG ADD "HKCU\Software\Policies\Microsoft\Windows\WindowsCopilot" /v TurnOffWindowsCopilot /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Windows 11: Disable Windows Recall Snapshots

:: ----------------------------------------------------------------------------

:: - DisableAIDataAnalysis:1 (Disable AI data analysis)

ECHO Disabling Windows Recall snapshots and AI data analysis...

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsAI" /v DisableAIDataAnalysis /t REG_DWORD /d 1 /f

REG ADD "HKCU\Software\Policies\Microsoft\Windows\WindowsAI" /v DisableAIDataAnalysis /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Privacy: Disable Telemetry and Data Collection

:: ----------------------------------------------------------------------------

:: - Enabled:0 (Disable advertising ID)

:: - TailoredExperiencesWithDiagnosticDataEnabled:0 (Disable tailored experiences)

:: - AllowTelemetry:0 (Send only required diagnostic data)

:: - Start_TrackProgs:0 (Disable app launch tracking)

ECHO Disabling telemetry and data collection...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\AdvertisingInfo" /v Enabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Privacy" /v TailoredExperiencesWithDiagnosticDataEnabled /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\DataCollection" /v AllowTelemetry /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v Start_TrackProgs /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\System" /v PublishUserActivities /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Privacy: Disable Windows Suggestions and Tips

:: ----------------------------------------------------------------------------

:: - SubscribedContent-338393Enabled:0 (Disable suggestions in Settings)

:: - SubscribedContent-353694Enabled:0 (Disable suggestions in Start)

:: - SubscribedContent-353696Enabled:0 (Disable suggestions in Timeline)

ECHO Disabling Windows suggestions and tips...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SubscribedContent-338393Enabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SubscribedContent-353694Enabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SubscribedContent-353696Enabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SystemPaneSuggestionsEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SilentInstalledAppsEnabled /t REG_DWORD /d 0 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Privacy: Disable Lockscreen Tips and Settings Home

:: ----------------------------------------------------------------------------

:: - RotatingLockScreenEnabled:0 (Disable lockscreen tips)

:: - RotatingLockScreenOverlayEnabled:0 (Disable lockscreen overlays)

:: - SettingsPageVisibility:hide (Hide Settings Home page)

ECHO Disabling lockscreen tips and Settings Home page...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v RotatingLockScreenEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v RotatingLockScreenOverlayEnabled /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager" /v SubscribedContent-338387Enabled /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v SettingsPageVisibility /t REG_SZ /d "hide:home" /f

ECHO.



:: ----------------------------------------------------------------------------

:: Apps: Enable Legacy Windows Photo Viewer

:: ----------------------------------------------------------------------------

:: - Registers Photo Viewer capabilities for image file types.

ECHO Enabling Legacy Windows Photo Viewer...

for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (

    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" /v ".%%~a" /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f >nul

)

ECHO Legacy Photo Viewer has been enabled.

ECHO MANUAL ACTION REQUIRED: You must set it as the default photo app in Windows Settings.

ECHO Go to: Settings ^> Apps ^> Default apps ^> Photos.

ECHO.



:: ----------------------------------------------------------------------------

:: Context Menu: Clean Up Unnecessary Options

:: ----------------------------------------------------------------------------

:: - Hide "Share", "Give access to", and "Include in library" options

ECHO Cleaning up context menu options...

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Shell Extensions\Blocked" /v "{e2bf9676-5f8f-435c-97eb-11607a5bedf7}" /t REG_SZ /d "" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Shell Extensions\Blocked" /v "{f81e9010-6ea4-11ce-a7ff-00aa003ca9f6}" /t REG_SZ /d "" /f

REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Shell Extensions\Blocked" /v "{596AB062-B4D2-4215-9F74-E9109B0A8153}" /t REG_SZ /d "" /f

ECHO.



:: ----------------------------------------------------------------------------

:: Search: Disable Bing Integration and Web Search

:: ----------------------------------------------------------------------------

:: - BingSearchEnabled:0 (Disable Bing web search in Start menu)

:: - CortanaConsent:0 (Disable Cortana consent)

:: - DisableSearchBoxSuggestions:1 (Disable search suggestions)

ECHO Disabling Bing search and web search integration...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v BingSearchEnabled /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v DisableWebSearch /t REG_DWORD /d 1 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v ConnectedSearchUseWeb /t REG_DWORD /d 0 /f

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v DisableSearchBoxSuggestions /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Start Menu: Disable Recommendations and Improve Performance

:: ----------------------------------------------------------------------------

:: - ShowRecommendations:0 (Hide recommended section in Start menu)

:: - Start_Layout:1 (Use more space for pinned apps)

ECHO Optimizing Start menu layout and recommendations...

REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v Start_ShowRecommendations /t REG_DWORD /d 0 /f

REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HideRecommendedSection /t REG_DWORD /d 1 /f

ECHO.



:: ----------------------------------------------------------------------------

:: Apply System Parameter Changes and Restart Explorer

:: ----------------------------------------------------------------------------

ECHO Applying system parameter changes and restarting Explorer...

RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters

taskkill /f /im explorer.exe

start explorer.exe

ECHO Explorer restarted.

ECHO.



:: ----------------------------------------------------------------------------

:: Exit with message (auto-close after 15 seconds)

:: ----------------------------------------------------------------------------

ECHO.

ECHO All customizations have been applied.

ECHO Window will close automatically in 15 seconds ...

PING 127.0.0.1 -n 16 > NUL

EXIT

:: ----------------------------------------------------------------------------

```
