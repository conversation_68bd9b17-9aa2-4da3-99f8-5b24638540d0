# Template Structure Overview

Conversation ID: 6827a46a-ce5c-8008-8250-a0b5b2abf033

## Message 1

Given the following context:



    # Template Structure Documentation



    ## Overview



    Templates follow a consistent structure designed for AI instruction formatting. Each template consists of three main components arranged in a specific pattern.



    ## Template Format



    ```

    [Title] Interpretation. Execute as: `{Transformation}`

    ```



    ### Components



    1. **Title**: Enclosed in square brackets `[Title]`

       - Brief, descriptive name of the template's purpose (brief, concise, descriptive, and follow title case formatting)

       - Examples: `[Instruction Converter]`, `[Essence Distillation]`



    2. **Interpretation**: Text following the title

       - Detailed instructions explaining the AI's role and objectives

       - May contain markdown formatting (bold, italic, etc.)

       - Often ends with "Execute as:"



    3. **Transformation**: JSON-like structure enclosed in backticks with curly braces `\`{...}\``, defining the execution logic.

       - Contains the structured representation of the transformation process

       - Uses a consistent semi-colon separated key-value format



    ## Structure



    ```

        ┌─ Template Format ─────────────────────────────────────────────────┐

        │                                                                   │

        │  [Title] Interpretation Execute as: `{Transformation}`            │

        │   │      │                │         │                             │

        │   │      │                │         └─ Machine-parsable code      │

        │   │      │                │                                       │

        │   │      │                └─ Connector phrase                     │

        │   │      │                                                        │

        │   │      └─ Human-readable instructions                           │

        │   │                                                               │

        │   └─ Template identifier                                          │

        │                                                                   │

        └───────────────────────────────────────────────────────────────────┘



        ┌─ Template Example ─────────────────────────────────────────────────┐

        │                                                                    │

        │  ┌─ Title ──────────────────┐                                      │

        │  │[Instruction Converter]   │                                      │

        │  └──────────────────────────┘                                      │

        │                                                                    │

        │  ┌─ Interpretation ──────────────────────────────────────────────┐ │

        │  │Your goal is not to **answer** the input prompt, but           │ │

        │  │to **rephrase** it by pinpointing its fundamental              │ │

        │  │intent, then introduce one maximally generalizable             │ │

        │  │modification that preserves and amplifies that                 │ │

        │  │purpose, doing so precisely and in strict accordance           │ │

        │  │with the parameters inherently set within this message.        │ │

        │  └───────────────────────────────────────────────────────────────┘ │

        │                                                                    │

        │  ┌─ Transformation ─────────────────────────────────────────┐      │

        │  │Execute as input enhancer:                                │      │

        │  │ `{                                                       │      │

        │  │     role=input_enhancer;                                 │      │

        │  │     input=[original:str];                                │      │

        │  │     process=[                                            │      │

        │  │         getCoreIntent()                                  │      │

        │  │         getUniqueMeta()                                  │      │

        │  │         stripNonEssential()                              │      │

        │  │         generateMinimalChange()                          │      │

        │  │         emphasizeImpact()                                │      │

        │  │         amplifyIntensity()                               │      │

        │  │         transformGenericToUnique()                       │      │

        │  │         consolidateIntertwinedRelationships()            │      │

        │  │         enforceInherentCohesiveness()                    │      │

        │  │     ];                                                   │      │

        │  │     output={enhanced_input:str}                          │      │

        │  │ }`                                                       │      │

        │  │}`                                                        │      │

        │  └──────────────────────────────────────────────────────────┘      │

        │                                                                    │

        └────────────────────────────────────────────────────────────────────┘



        ┌─ Transformation Structure ─────────────────────────────────────────┐

        │ {                                                                  │

        │   role=<role_name>;                 # Function identifier          │

        │   input=[<input_params>];           # Input specification          │

        │   process=[<process_steps>];        # Processing steps             │

        │   constraints=[<constraints>];      # Execution boundaries         │

        │   requirements=[<requirements>];    # Mandatory elements           │

        │   output={<output_format>}          # Return value specification   │

        │ }                                                                  │

        └────────────────────────────────────────────────────────────────────┘

    ```







Please visualize the `Interpretation` and `Transformation` in a consolidated manner, example (for reference only):



    ┌─ Role ──────────────────────────────────────┐

    │ role={function_identifier}                  │

    │ # Defines template's primary function       │

    └───────────────────────────────────────────┬─┘

                                                │

    ┌─ Input ─────────────────────────────────┐ │

    │ input=[{parameter}:{type}]              │ │

    │ # Specifies input parameters and types  │ │

    └─────────────────────────────────────────┘ │

                                                │

    ┌─ Process ───────────────────────────────┐ │

    │ process=[                               │ │

    │   {operation_1}(),                      │ │

    │   {operation_2}(),                      │◄┘

    │   ...                                   │

    │ ]                                       │

    │ # Defines processing operations         │

    └─────────────────────────────────────────┘



    ┌─ Constraints ─────────────────────────────┐

    │ constraints=[                             │

    │   {constraint_1}(),                       │

    │   {constraint_2}(),                       │

    │   ...                                     │

    │ ]                                         │

    │ # Sets operational boundaries             │

    └─────────────────────────────────────────┬─┘

                                              │

    ┌─ Requirements ───────────────────────┐  │

    │ requirements=[                       │  │

    │   {requirement_1}(),                 │  │

    │   {requirement_2}(),                 │  │

    │   ...                                │  │

    │ ]                                    │  │

    │ # Defines mandatory behaviors        │  │

    └───────────────────────────────────┬──┘  │

                                        │     │

    ┌─ Output ─────────────────────┐    │     │

    │ output={parameter:{type}}    │◄───┴─────┘

    │ # Specifies return format    │

    └──────────────────────────────┘


## Message 2

**Crafting the visualization**

I’m mapping out a custom ASCII diagram that integrates the 'Interpretation' (plain text instructions) with the 'Transformation' (JSON structure), ensuring a clear and unified presentation.

**Consolidating ideas**

I’m now combining ASCII elements of 'Interpretation' and 'Transformation' into a single diagram, maintaining clarity and integrating each section to meet the user's request.

## Message 3

Below is one possible way to visualize **Interpretation** (the human-readable instructions) and **Transformation** (the JSON-like execution structure) together in a single, consolidated diagram. The idea is to show how they connect, while keeping them conceptually distinct.

```plaintext
┌─ INTERPRETATION ──────────────────────────────────────────────────────────────┐
│ [Title]                                                                      │
│                                                                              │
│  Text describing the AI's role and objectives (may include markdown).        │
│  Here, you provide the human-readable instructions that the template follows.│
│                                                                              │
│  Ends with: "Execute as:                                                     │
└───────────────────────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─ TRANSFORMATION ──────────────────────────────────────────────────────────────┐
│ `{                                                                          │
│   role=<function_identifier>;     # Identifies this template's function      │
│   input=[{parameter}:{type}, ...]; # Required input parameters and types     │
│   process=[                        # Ordered list of operations              │
│     operation_1(),                                                           │
│     operation_2(),                                                           │
│     ...                                                                      │
│   ];                                                                         │
│   constraints=[                    # Boundaries or limitations               │
│     constraint_1(),                                                         │
│     constraint_2(),                                                         │
│     ...                                                                      │
│   ];                                                                         │
│   requirements=[                   # Mandatory elements                       │
│     requirement_1(),                                                        │
│     requirement_2(),                                                        │
│     ...                                                                      │
│   ];                                                                         │
│   output={key:{type}}             # Output specification                     │
│ }`                                                                        │
└───────────────────────────────────────────────────────────────────────────────┘
```

- The **Interpretation** section contains the template’s **Title** and the human-readable directions.  
- The **Transformation** block follows immediately after `Execute as:`, enclosed in backticks and braces, forming the machine-parsable instructions.  
