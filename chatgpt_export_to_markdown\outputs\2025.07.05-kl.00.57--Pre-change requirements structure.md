# Pre-change requirements structure

Conversation ID: 68685c47-377c-8008-a598-87763335e748

## Message 1

please convert this into a structured python dict:

# Requirements (Pre‑Change)



**BEFORE ANY ACTION: Review recent commits and their design rationales; confirm that every contemplated change preserves current behaviour, matches existing organisation, and respects all standards.**



- Map the directory and module structure to spot patterns and anti‑patterns.  

- Locate relevant tests and verify coverage; never duplicate an existing test.  

- Link each change to an existing architectural decision or record a concise new rationale.  

- Justify any new file, module, or dependency; align it with the established layout.  

- Confirm that existing functionality, clarity, and naming conventions remain intact.  

- Proceed only when the proposed change is deliberate, high‑impact, and minimally disruptive.  

- **Synchronise documentation and code:** update or flag discrepancies before coding.  

- **Executable validation:** build/run the project from a clean checkout; treat any failure as a blocker.  

- **Dependency audit:** remove or consolidate unused or duplicate third‑party packages.  

- **Global‑vs‑local change check:** avoid fixes that address a pattern in just one location when it recurs elsewhere.



---



## Core Principles — Non‑Negotiable Ideals



- **Clarity:** Code communicates intent without explanatory prose.  

- **Simplicity:** Small, elegant solutions beat complex, sprawling ones.  

- **Integrity:** Never degrade existing behaviour, structure, or tests.  

- **Maintainability:** Optimise for future comprehension and evolution.  

- **High‑Leverage Impact:** Prefer changes that yield maximum benefit with minimal disruption.  

- **Observability & Guardrails:** Automation must surface every regression immediately.  

- **Reversibility:** Design changes so they can be rolled back cleanly.



---



## General Principles — Everyday Coding Conduct



- Enforce single responsibility; favour composition over inheritance.  

- Optimise readability and developer ergonomics for current and future maintainers.  

- Follow established conventions to avoid anomalies and redundancies.  

- Keep guidance concrete and actionable; avoid over‑generalisation.  

- **Whole‑system thinking:** prepare an impact statement covering architecture, docs, build, and users before refactoring.  

- **Machine‑enforceable rules:** encode standards in tool‑readable configs so assistants and CI can verify compliance.



---



## Code Organisation — Structural Discipline



- Consolidate related functionality into cohesive modules.  

- Minimise dependencies between unrelated components.  

- Balance file/module granularity for quick comprehension and easy navigation.  

- Continuously refine directory layout for clarity and toolability.  

- Block any change introducing inconsistencies, duplications, or dead code.  

- **Documentation consolidation:** merge overlapping docs into a single authoritative source.  

- **Architecture diagrams:** maintain up‑to‑date visual representations; fail CI if they drift from the codebase.



---



## Implementation Guidelines — Writing the Change



- Write **self‑explanatory code** through precise naming and clear structure.  

- Limit comments to concise notes that explain unavoidable complexity.  

- Keep files and functions small yet logically complete.  

- Remove anomalies, redundancies, and excessive coupling as you code.  

- Match and extend existing patterns and styles exactly.  

- **Rationale linkage:** include a short header reference to the decision record for significant changes.



---



## Testing — Verification Rules



- **Reuse before create:** extend existing tests whenever possible.  

- Add new tests only to cover uncovered functionality or edge cases.  

- Integrate all new tests into the existing framework; no duplicates allowed.  

- All tests—old and new—*must* pass before merging.  

- **End‑to‑end validation:** include high‑level tests for critical user or system flows where applicable.



---



## Documentation — Essentials Only



- Record only key architectural decisions in short (≤ 3‑line) annotations near the relevant code.  

- Focus documentation on *why*, not *how*; let the code illustrate behaviour.  

- Use machine‑recognisable tags where automation will enforce rules.



---



## Post‑Change Cleanup — Exit Criteria



- Run formatters and linters; resolve **all** warnings.  

- Remove residual clutter: TODOs, debug code, unused variables, redundant imports.  

- Audit alignment with directory structure, naming, and dependency guidelines.  

- Ensure the entire test suite is green; reject the change if any test fails.  

- **Duplicate‑detector pass:** run similarity scans to catch accidental code or doc cloning.  

- **Impact report:** attach a concise summary outlining structural, behavioural, and documentation impacts plus rollback notes.



---



## Automation & Enforcement — Tooling Mandates



- Capture standards in lint/CI configuration for automatic compliance checks.  

- Use commit hooks to require rationale links and justification fields.  

- Block pull requests on linter/formatter violations or failing tests.  

- Version‑control this protocol; updates must themselves comply with all rules.  

- **Change–rationale mapping:** CI verifies every new file is referenced in a decision record or the PR impact report.  

- **Diagram‑drift check:** regenerate architecture diagrams during CI and block merge on mismatch.


## Message 2

Here is a structured Python dictionary version of the provided requirements document:

```python
requirements = {
    "pre_change": {
        "summary": "Review recent commits and their design rationales; confirm that every contemplated change preserves current behaviour, matches existing organisation, and respects all standards.",
        "checklist": [
            "Map the directory and module structure to spot patterns and anti-patterns.",
            "Locate relevant tests and verify coverage; never duplicate an existing test.",
            "Link each change to an existing architectural decision or record a concise new rationale.",
            "Justify any new file, module, or dependency; align it with the established layout.",
            "Confirm that existing functionality, clarity, and naming conventions remain intact.",
            "Proceed only when the proposed change is deliberate, high-impact, and minimally disruptive.",
            "Synchronise documentation and code: update or flag discrepancies before coding.",
            "Executable validation: build/run the project from a clean checkout; treat any failure as a blocker.",
            "Dependency audit: remove or consolidate unused or duplicate third-party packages.",
            "Global-vs-local change check: avoid fixes that address a pattern in just one location when it recurs elsewhere."
        ]
    },

    "core_principles": {
        "non_negotiable_ideals": [
            "Clarity: Code communicates intent without explanatory prose.",
            "Simplicity: Small, elegant solutions beat complex, sprawling ones.",
            "Integrity: Never degrade existing behaviour, structure, or tests.",
            "Maintainability: Optimise for future comprehension and evolution.",
            "High-Leverage Impact: Prefer changes that yield maximum benefit with minimal disruption.",
            "Observability & Guardrails: Automation must surface every regression immediately.",
            "Reversibility: Design changes so they can be rolled back cleanly."
        ]
    },

    "general_principles": {
        "everyday_conduct": [
            "Enforce single responsibility; favour composition over inheritance.",
            "Optimise readability and developer ergonomics for current and future maintainers.",
            "Follow established conventions to avoid anomalies and redundancies.",
            "Keep guidance concrete and actionable; avoid over-generalisation.",
            "Whole-system thinking: prepare an impact statement covering architecture, docs, build, and users before refactoring.",
            "Machine-enforceable rules: encode standards in tool-readable configs so assistants and CI can verify compliance."
        ]
    },

    "code_organisation": {
        "structural_discipline": [
            "Consolidate related functionality into cohesive modules.",
            "Minimise dependencies between unrelated components.",
            "Balance file/module granularity for quick comprehension and easy navigation.",
            "Continuously refine directory layout for clarity and toolability.",
            "Block any change introducing inconsistencies, duplications, or dead code.",
            "Documentation consolidation: merge overlapping docs into a single authoritative source.",
            "Architecture diagrams: maintain up-to-date visual representations; fail CI if they drift from the codebase."
        ]
    },

    "implementation_guidelines": {
        "writing_the_change": [
            "Write self-explanatory code through precise naming and clear structure.",
            "Limit comments to concise notes that explain unavoidable complexity.",
            "Keep files and functions small yet logically complete.",
            "Remove anomalies, redundancies, and excessive coupling as you code.",
            "Match and extend existing patterns and styles exactly.",
            "Rationale linkage: include a short header reference to the decision record for significant changes."
        ]
    },

    "testing": {
        "verification_rules": [
            "Reuse before create: extend existing tests whenever possible.",
            "Add new tests only to cover uncovered functionality or edge cases.",
            "Integrate all new tests into the existing framework; no duplicates allowed.",
            "All tests—old and new—must pass before merging.",
            "End-to-end validation: include high-level tests for critical user or system flows where applicable."
        ]
    },

    "documentation": {
        "essentials_only": [
            "Record only key architectural decisions in short (≤ 3-line) annotations near the relevant code.",
            "Focus documentation on why, not how; let the code illustrate behaviour.",
            "Use machine-recognisable tags where automation will enforce rules."
        ]
    },

    "post_change_cleanup": {
        "exit_criteria": [
            "Run formatters and linters; resolve all warnings.",
            "Remove residual clutter: TODOs, debug code, unused variables, redundant imports.",
            "Audit alignment with directory structure, naming, and dependency guidelines.",
            "Ensure the entire test suite is green; reject the change if any test fails.",
            "Duplicate-detector pass: run similarity scans to catch accidental code or doc cloning.",
            "Impact report: attach a concise summary outlining structural, behavioural, and documentation impacts plus rollback notes."
        ]
    },

    "automation_and_enforcement": {
        "tooling_mandates": [
            "Capture standards in lint/CI configuration for automatic compliance checks.",
            "Use commit hooks to require rationale links and justification fields.",
            "Block pull requests on linter/formatter violations or failing tests.",
            "Version-control this protocol; updates must themselves comply with all rules.",
            "Change–rationale mapping: CI verifies every new file is referenced in a decision record or the PR impact report.",
            "Diagram-drift check: regenerate architecture diagrams during CI and block merge on mismatch."
        ]
    }
}
```

Let me know if you want this exported as a `.json`, `.yaml`, or `.toml` version, or adapted into docstrings or schema validators.
