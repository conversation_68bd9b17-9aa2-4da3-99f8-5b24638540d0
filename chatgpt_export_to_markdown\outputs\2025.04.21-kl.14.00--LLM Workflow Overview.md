# LLM Workflow Overview

Conversation ID: 68063375-b788-8008-a215-2667930e8494

## Message 1

Please familiarize yourself with this system and express it's rules in a single line:

# LLM Schema System: Analysis Lineage



This document captures high-value realizations about the codebase in precise, non-verbose language to enable progressive understanding and serve as a reasoning trail for LLMs and autonomous agents.



## 1. Structure & Purpose Orientation (2025-04-20)



### 1.1 Codebase Structure Map



```mermaid

graph TD

    A[Root Directory] --> B[src/]

    A --> C[notes/]

    A --> D[GOAL.md]

    

    B --> E[litellm_sequence_executor.py]

    B --> F[py_venv_init.bat]

    B --> G[requirements.txt]

    B --> H[templates/]

    B --> I[venv/]

    

    H --> J[lvl1/]

    

    J --> K[templates_lvl1_md_catalog_generator.py]

    J --> L[templates_lvl1_md_catalog.json]

    J --> M[md/]

    

    M --> N[Template Files *.md]

    

    subgraph "Core Execution Engine"

    E

    end

    

    subgraph "Template Management"

    K

    L

    M

    N

    end

    

    subgraph "Environment Setup"

    F

    G

    I

    end

    

    subgraph "Documentation"

    C

    D

    end

```



### 1.2 Component Classification



```json

{

  "core_components": {

    "litellm_sequence_executor.py": {

      "type": "execution_engine",

      "purpose": "Main execution engine that processes instruction sequences against LLM models",

      "key_functions": [

        "execute_sequence",

        "Config",

        "CostTracker"

      ]

    },

    "templates_lvl1_md_catalog_generator.py": {

      "type": "template_manager",

      "purpose": "Generates a catalog of templates from markdown files",

      "key_functions": [

        "generate_catalog",

        "load_catalog",

        "get_sequence",

        "get_system_instruction"

      ]

    },

    "templates_lvl1_md_catalog.json": {

      "type": "data_store",

      "purpose": "Stores the catalog of templates and sequences",

      "structure": {

        "catalog_meta": "Metadata about the catalog",

        "templates": "Dictionary of templates",

        "sequences": "Dictionary of sequences"

      }

    },

    "template_files": {

      "type": "instruction_templates",

      "purpose": "Define instruction templates for LLM interactions",

      "format": "[Title] Interpretation. Execute as: `{role=role_name; input=[...]; process=[...]; output={...}}`"

    }

  },

  "support_components": {

    "py_venv_init.bat": {

      "type": "environment_setup",

      "purpose": "Sets up Python virtual environment and installs dependencies"

    },

    "requirements.txt": {

      "type": "dependency_list",

      "purpose": "Lists Python package dependencies",

      "key_dependencies": [

        "litellm",

        "pydantic",

        "openai",

        "anthropic",

        "google"

      ]

    },

    "notes/": {

      "type": "documentation",

      "purpose": "Contains notes and examples of the template system"

    },

    "GOAL.md": {

      "type": "documentation",

      "purpose": "Defines the project's goals and purpose"

    }

  }

}

```



### 1.3 Entry/Exit Points



```json

{

  "entry_points": {

    "main": {

      "file": "litellm_sequence_executor.py",

      "function": "main()",

      "purpose": "CLI entry point for executing instruction sequences"

    },

    "catalog_generation": {

      "file": "templates_lvl1_md_catalog_generator.py",

      "function": "regenerate_catalog()",

      "purpose": "Generates the template catalog"

    }

  },

  "data_flow": {

    "input": {

      "user_prompt": "Text input from the user",

      "sequence_id": "ID of the instruction sequence to execute",

      "models": "List of LLM models to use"

    },

    "processing": {

      "template_loading": "Load templates from markdown files",

      "sequence_execution": "Execute each step in the sequence against the models",

      "cost_tracking": "Track the cost of LLM API calls"

    },

    "output": {

      "execution_results": "Results of executing the sequence",

      "output_file": "JSON file containing the results"

    }

  }

}

```



### 1.4 Core Purpose Statement



The system is designed to transform LLM interactions from conversational exchanges into structured, function-like operations by applying a schema-driven approach to prompt engineering. It provides a framework for defining, organizing, and executing multi-step instruction sequences that process user inputs through a series of transformations, each following a precise pattern of role definition, input/output specification, and process steps. The core purpose is to enable more deterministic, reliable, and powerful LLM interactions by treating the LLM as a specialized component within a larger system rather than as a conversational partner.



### 1.5 Key Insights



1. **Schema-Driven Architecture**: The system uses a consistent schema for defining instructions, with each instruction following a pattern of `[Title] Interpretation. Execute as: {role=X; input=Y; process=[...]; output=Z}`. This schema provides a structured way to define how LLMs should process inputs.



2. **Sequence-Based Processing**: Instructions are organized into sequences (e.g., 0142, 0100) where each sequence represents a multi-step process. Each step in a sequence builds on the output of the previous step, creating a pipeline of transformations.



3. **Template Management**: The system includes tools for managing templates, including loading them from markdown files, organizing them into a catalog, and retrieving them for execution.



4. **Execution Engine**: The core execution engine (`litellm_sequence_executor.py`) processes instruction sequences against LLM models, handling API calls, streaming responses, and tracking costs.



5. **Provider Abstraction**: The system abstracts away the details of different LLM providers (OpenAI, Anthropic, Google, etc.) through a centralized configuration system.



### 1.6 Template Schema Structure



The template schema is the core innovation of this system, providing a structured way to define how LLMs should process inputs. Each template follows this pattern:



```

[Title] Interpretation. Execute as: `{role=role_name; input=[...]; process=[...]; output={...}}`

```



Breaking this down:



1. **Title**: A concise label enclosed in square brackets that identifies the transformation's purpose.

2. **Interpretation**: A description of what the transformation does, often using an oppositional framing ("not X, but Y").

3. **Transformation Block**: A code-like block enclosed in backticks that defines:

   - **role**: A snake_case identifier for the function being performed

   - **input**: The expected input format and types

   - **process**: An ordered list of steps to perform, expressed as function calls

   - **output**: The expected output format and types



This schema enables:

- **Deterministic Processing**: By clearly defining inputs, outputs, and process steps

- **Composability**: Templates can be chained together in sequences

- **Self-Documentation**: The schema itself documents how the transformation works

- **Consistency**: All templates follow the same structure, making them easier to understand and use



The system's power comes from treating LLM interactions as function calls with well-defined interfaces rather than as free-form conversations.



## 2. Logic Path & Dependency Trace (2025-04-20)



The system's execution flow follows a clear pipeline pattern:



```mermaid

graph TD

    A[User Input] --> B[Template Selection]

    B --> C[Template Loading]

    C --> D[Sequence Execution]

    D --> E[LLM API Calls]

    E --> F[Result Processing]

    F --> G[Output Generation]

    

    subgraph "Core Data Flow"

    A --> G

    end

    

    subgraph "Template Management"

    H[Template Files] --> I[Catalog Generator]

    I --> J[Catalog JSON]

    J --> C

    end

    

    subgraph "Execution Engine"

    D --> K[Cost Tracking]

    D --> L[Provider Selection]

    L --> E

    end

```



### 2.1 Critical Dependencies



1. **Template → Execution**: The execution engine depends on properly formatted templates following the schema pattern.

2. **Catalog → Template Access**: Template access depends on a properly generated catalog.

3. **LiteLLM → Provider APIs**: The system uses LiteLLM as an abstraction layer for different LLM providers.



### 2.2 High-Impact Logic Routes



1. **Template Processing Pipeline**:

   - Template files → Catalog generation → Sequence retrieval → System instruction extraction → LLM execution

   

2. **Execution Flow**:

   - User prompt + Sequence ID → Load sequence → Process each step → Stream results → Output JSON



### 2.3 Coupling Zones



1. **Template Format Coupling**: The system is tightly coupled to the specific template format `[Title] Interpretation. Execute as: {role=X; input=Y; process=[...]; output=Z}`.

2. **LiteLLM Integration**: The execution engine is coupled to LiteLLM's API for provider abstraction.

3. **JSON Structure Coupling**: The output format is coupled to a specific JSON structure.



### 2.4 Key Realization



The system's architecture follows a clear separation between template definition (static) and template execution (dynamic). This separation enables a flexible, extensible system where new templates can be added without modifying the execution engine. The template schema serves as the contract between these two components, ensuring they can evolve independently while maintaining compatibility.



## 3. Clarity, Cohesion & Self-Explanation Audit (2025-04-20)



### 3.1 Naming Clarity Assessment



```json

{

  "high_clarity_components": [

    "litellm_sequence_executor.py - Clear purpose indication",

    "templates_lvl1_md_catalog_generator.py - Explicit function",

    "Config class - Well-defined configuration management",

    "CostTracker class - Obvious purpose"

  ],

  "naming_patterns": {

    "consistent": [

      "snake_case for functions and variables",

      "CamelCase for classes",

      "ALL_CAPS for constants"

    ],

    "semantic_clarity": "Names directly reflect component purposes"

  }

}

```



### 3.2 Structural Cohesion Analysis



The codebase exhibits strong cohesion in its organization:



1. **Functional Cohesion**: Each module has a single, well-defined purpose

   - `litellm_sequence_executor.py`: Execution engine

   - `templates_lvl1_md_catalog_generator.py`: Template management



2. **Sequential Cohesion**: The execution flow follows a logical sequence

   - Template loading → Sequence retrieval → Step execution → Result processing



3. **Communicational Cohesion**: Components operate on the same data structures

   - Template catalog as the shared data structure

   - Execution results as the output structure



### 3.3 Self-Explanation Assessment



The codebase demonstrates strong self-explanation through:



1. **Structural Clarity**: Component organization reveals system purpose

2. **Naming Transparency**: Function and variable names explain their roles

3. **Schema Consistency**: Uniform template structure creates predictability



However, the system relies on comments for explaining:

- The philosophical foundation (templates_lvl1_md_catalog_generator.py)

- Section boundaries (litellm_sequence_executor.py)



### 3.4 Key Realization



The system achieves clarity through a consistent architectural pattern: each component is designed as a transformation in a pipeline, with clear inputs and outputs. This pattern is reflected at multiple levels:



1. **Macro level**: The entire system transforms user prompts into structured LLM interactions

2. **Template level**: Each template transforms inputs according to a specific role

3. **Sequence level**: Steps in a sequence form a transformation pipeline



This consistent pattern creates a self-explanatory architecture where the structure itself communicates the system's purpose and operation. The template schema is not just a format but an architectural principle that permeates the entire system.



## 4. Codebase Improvements (2025-04-20)



Based on the analysis, several improvements were implemented to enhance the system's clarity, robustness, and maintainability:



1. **Configuration Management**: Fixed the provider model configuration by replacing multiple "model_name" entries with a clear structure that separates default models from available models.



2. **Template Validation**: Added schema validation to ensure templates follow the correct format, with warnings for invalid templates.



3. **Enhanced Error Handling**: Improved error handling for LLM API calls with detailed error categorization, recovery suggestions, and structured error output.



4. **Cost Reporting**: Included cost information in the JSON output for better tracking and analysis.



5. **Improved Default Prompt**: Replaced the verbose default prompt with a concise, clear one that better explains the expected output.



6. **Documentation**: Created a README.md file with usage examples and explanations of the system's features.



These improvements enhance the system's clarity, error handling, user experience, and maintainability while preserving its core architecture and purpose.



## 5. Meta-Instruction Sequence Generator (2025-04-20)



A new instruction sequence (0190) has been created to transform any input into schema-compliant instruction sequences. This meta-sequence follows the Universal Instruction Schema pattern and is designed to be self-referential, producing outputs that mirror its own structure.



### 5.1 Sequence Structure



The sequence consists of seven alphabetically ordered steps, each performing an atomic transformation in the conversion process:



```mermaid

graph TD

    A["0190-a: Directive Decomposition"] --> B["0190-b: Final Assembly & Deployment"]

    C["0190-c: Instruction Arc Design"] --> E["0190-e: Schema-Bound Generation"]

    D["0190-d: Meta-Request Analysis"] --> A

    A --> C

    E --> F["0190-f: Sequence Integrity Audit"]

    F --> G["0190-g: Step Validation & Enhancement"]

    G --> B

```



### 5.2 Step Descriptions



1. **Directive Decomposition (0190-a)**: Extracts the core objective, constraints, and requirements from the transformation request.

2. **Final Assembly & Deployment (0190-b)**: Integrates validated instruction steps into a deployment-ready sequence with proper identifiers.

3. **Instruction Arc Design (0190-c)**: Architects a minimal, progressive sequence of atomic transformation steps.

4. **Meta-Request Analysis (0190-d)**: Extracts the transformational intent from the original request.

5. **Schema-Bound Generation (0190-e)**: Instantiates each step from the transformation blueprint as a complete instruction.

6. **Sequence Integrity Audit (0190-f)**: Audits each instruction for schema compliance and functional coherence.

7. **Step Validation & Enhancement (0190-g)**: Enforces linguistic potency while preserving functional purpose.



### 5.3 Key Features



1. **Self-Referential Design**: The sequence can analyze itself and produce structurally similar sequences.

2. **Schema-Driven Structure**: Each step strictly follows the Universal Instruction Schema.

3. **Step Atomicity**: Each instruction performs exactly one well-defined transformation.

4. **Meta-Validation**: Includes steps to ensure schema compliance and structural integrity.

5. **Recursive Applicability**: Output can be used as input for further transformations.

6. **Parser Compatibility**: Produces output that can be extracted by the template system.



### 5.4 Architectural Significance



This meta-sequence represents a significant advancement in the system's capabilities by:



1. **Self-Replication**: Enabling the system to generate new instruction sequences using its own schema

2. **Meta-Level Processing**: Providing a way to analyze and transform arbitrary inputs into schema-compliant instructions

3. **Schema Enforcement**: Guaranteeing that all generated instructions follow the Universal Instruction Schema

4. **Atomicity & Coherence**: Ensuring that generated sequences consist of atomic, coherent steps with clear dependencies



This sequence fulfills the ultimate goal stated in GOAL.md: to create a system that can transform any input into highly optimized system_message instructions designed to take any input and generate a new, complete, multi-step instruction sequence that is maximally LLM-optimized, universally applicable, and strictly adheres to the established universal schema format.


## Message 2

**Single-line rule expression:**

*This system transforms user prompts into structured, deterministic LLM workflows by executing schema-bound instruction sequences through a modular pipeline of templates, templates catalogs, and an execution engine.*

## Message 3

I'm specifically looking to create a prompt specifically designed to concistently transform *any* prompt into the following structure:



    ```markdown

    [Module Label] Your goal is not to <negated_behavior>, but to <core_transformation_objective>. Operate strictly under the role definition and transformation schema provided below.

    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<soft_rules>]; requirements=[<hard_mandates>]; output={<output_structure>}}`

    ```



Here's an example:

    

    "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`"

## Message 4

Perfect — you're asking for a **universal meta-prompt** that transforms any input prompt into a highly structured schema-compliant instruction format.

Here’s a strong draft that reflects your intent and ensures the output always follows your specified transformation structure:

---

**🧠 Meta-Prompt for Schema Instruction Generation:**

> You are a transformation schema converter. Your task is to take *any* input prompt and reframe it as a schema-bound transformation directive using the following exact format:
>
> ```markdown
> [Module Label] Your goal is not to <negated_behavior>, but to <core_transformation_objective>. Operate strictly under the role definition and transformation schema provided below.
> `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<soft_rules>]; requirements=[<hard_mandates>]; output={<output_structure>}}`
> ```
>
> When generating the transformation:
> - **Module Label** should be a descriptive label summarizing the transformation goal (e.g. [Instruction Converter], [Summarization Agent]).
> - **Negated Behavior** should describe what this module *should not* do (e.g. "answer questions", "summarize casually").
> - **Core Transformation Objective** should describe the intended outcome (e.g. "rephrase into instructional format", "generate technical summary").
> - **Role Name** should be a function-like snake_case identifier of the transformation role.
> - **Input** defines the types and format of inputs (e.g. `text:str`, `query:str`, etc.).
> - **Process** is a list of transformation functions, logically ordered and atomic.
> - **Constraints** are soft rules that guide tone or form but can flex.
> - **Requirements** are hard mandates that must be followed.
> - **Output** describes the expected output structure and type.
>
> Your output must **always** follow the exact markdown schema above. Do not include explanations, just the formatted output block.

---

Would you like me to now test this meta-prompt against an example prompt input and show you what the output would look like?
