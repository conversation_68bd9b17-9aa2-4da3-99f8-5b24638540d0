# Simplifying Code Complexity

Conversation ID: 67c8bf52-95c8-8008-8bd7-764334bc746a

## Message 1

what is the single most effective way to reduce the unneccessary complexity and reduce the amount of code needed:
#!/usr/bin/env python3

"""

llm_template_runner_clean.py



A single-file script for managing templates, LLM interactions, and orchestrating prompt refinements.

"""



import os

import sys

import re

import glob

import json

import time

import uuid

from datetime import datetime

from pathlib import Path

from typing import List, Dict, Union, Optional



from dotenv import load_dotenv

from loguru import logger



# Provider SDKs

from openai import OpenAI

from anthropic import Anthropic

from google import generativeai as genai



# LangChain

from langchain_openai import ChatOpenAI

from langchain_anthropic import ChatAnthropic

from langchain_google_genai import ChatGoogleGenerativeAI



# ========================================================

# 1. Global Configuration

# ========================================================

class Config:

    """

    Global settings

    - Always selects the item at the end, simply reordering these lines allows for quick change.

    """



    PROVIDER_ANTHROPIC = "anthropic"

    PROVIDER_DEEPSEEK = "deepseek"

    PROVIDER_GOOGLE = "google"

    PROVIDER_OPENAI = "openai"



    API_KEY_ENV_VARS = {

        PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",

        PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",

        PROVIDER_GOOGLE: "GOOGLE_API_KEY",

        PROVIDER_OPENAI: "OPENAI_API_KEY",

    }



    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC

    DEFAULT_PROVIDER = PROVIDER_GOOGLE

    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK

    DEFAULT_PROVIDER = PROVIDER_OPENAI



    DEFAULT_MODEL_PARAMS = {

        PROVIDER_ANTHROPIC: {

            "model_name": "claude-3-opus-20240229",      # (d1) [exorbitant]

            "model_name": "claude-2.1",                  # (c1) [expensive]

            "model_name": "claude-3-sonnet-20240229",    # (b1) [medium]

            "model_name": "claude-3-haiku-********",     # (a1) [cheap]

        },

        PROVIDER_DEEPSEEK: {

            "model_name": "deepseek-reasoner",           # (a3) [cheap]

            "model_name": "deepseek-coder",              # (a2) [cheap]

            "model_name": "deepseek-chat",               # (a1) [cheap]

        },

        PROVIDER_GOOGLE: {

            "model_name": "gemini-2.0-pro-experimental", # (c4) [expensive]

            "model_name": "gemini-2.0-flash-exp",        # (a4) [ultra-cheap]

            "model_name": "gemini-1.5-flash",            # (c4) [expensive]

            "model_name": "gemini-1.5-flash-8b",         # (a4) [ultra-cheap]

            "model_name": "gemini-2.0-flash",            # (b4) [medium]

        },

        PROVIDER_OPENAI: {

            "model_name": "o1",                          # (c3) [expensive]

            "model_name": "gpt-4-turbo-preview",         # (c2) [expensive]

            "model_name": "gpt-4-turbo",                 # (c1) [expensive]

            "model_name": "o1-mini",                     # (b3) [medium]

            "model_name": "gpt-4o",                      # (b2) [medium]

            "model_name": "gpt-3.5-turbo",               # (a3) [cheap]

            "model_name": "gpt-4o-mini",                 # (a1) [cheap]

            "model_name": "gpt-3.5-turbo-1106",          # (a2) [cheap]

            "model_name": "o3-mini",                     # (b1) [medium]

        },

    }



    SUPPORTED_MODELS = {

        PROVIDER_ANTHROPIC: {

            "claude-3-haiku-********":             {"pricing": "0.80/4.00", "description": "Claude 3 Haiku"},

            "claude-3-sonnet-20240229":            {"pricing": "3.00/15.00", "description": "Claude 3 Sonnet"},

            "claude-2":                            {"pricing": "8.00/24.00", "description": "Base Claude 2 model"},

            "claude-2.0":                          {"pricing": "8.00/24.00", "description": "Enhanced Claude 2.0"},

            "claude-2.1":                          {"pricing": "8.00/24.00", "description": "Latest Claude 2.1 release"},

            "claude-3-opus-20240229":              {"pricing": "15.00/75.00", "description": "Claude 3 Opus"},

        },

        PROVIDER_DEEPSEEK: {

            "deepseek-coder":                      {"pricing": "0.10/0.20", "description": "Code-specialized model"},

            "deepseek-chat":                       {"pricing": "0.14/0.28", "description": "DeepSeek Chat model"},

            "deepseek-reasoner":                   {"pricing": "0.55/2.19", "description": "Specialized reasoning model"},

        },

        PROVIDER_GOOGLE: {

            'gemini-1.5-flash-8b':                 {"pricing": "0.0375/0.15", "description": "Cost-optimized 8B param Flash"},

            'gemini-1.5-flash-8b-001':             {"pricing": "0.0375/0.15", "description": "Versioned 8B Flash model"},

            'gemini-1.5-flash-8b-latest':          {"pricing": "0.0375/0.15", "description": "Latest 8B Flash iteration"},

            'gemini-1.5-flash':                    {"pricing": "0.075/0.30", "description": "Base Gemini 1.5 Flash model"},

            'gemini-1.5-flash-001':                {"pricing": "0.075/0.30", "description": "Initial Gemini 1.5 Flash"},

            'gemini-1.5-flash-002':                {"pricing": "0.075/0.30", "description": "Updated 1.5 Flash version"},

            'gemini-1.5-flash-latest':             {"pricing": "0.075/0.30", "description": "Latest Gemini 1.5 Flash"},

            'gemini-2.0-flash-lite-preview':       {"pricing": "0.075/0.30", "description": "Preview of 2.0 Flash-Lite"},

            'gemini-2.0-flash':                    {"pricing": "0.10/0.40", "description": "Production 2.0 Flash (multimodal)"},

            'gemini-2.0-flash-001':                {"pricing": "0.10/0.40", "description": "Versioned 2.0 Flash release"},

            'gemini-2.0-flash-exp':                {"pricing": "0.10/0.40", "description": "Experimental 2.0 Flash precursor"},

            'gemini-1.5-pro':                      {"pricing": "0.45/2.40", "description": "Base Gemini 1.5 Pro model"},

            'gemini-1.5-pro-001':                  {"pricing": "0.45/2.40", "description": "Initial Gemini 1.5 Pro release"},

            'gemini-1.5-pro-002':                  {"pricing": "0.45/2.40", "description": "Updated Gemini 1.5 Pro version"},

            'gemini-1.5-pro-latest':               {"pricing": "0.45/2.40", "description": "Latest Gemini 1.5 Pro (2M token context)"},

            'gemini-1.0-pro':                      {"pricing": "0.50/1.50", "description": "Base Gemini 1.0 Pro model"},

            'gemini-1.0-pro-001':                  {"pricing": "0.50/1.50", "description": "Versioned Gemini 1.0 Pro"},

            'gemini-1.0-pro-latest':               {"pricing": "0.50/1.50", "description": "Latest Gemini 1.0 Pro (text)"},

            'gemini-1.0-pro-vision-latest':        {"pricing": "0.50/1.50", "description": "Gemini 1.0 Pro with vision"},

            'gemini-pro':                          {"pricing": "0.50/1.50", "description": "Legacy name for Gemini 1.0 Pro"},

            'gemini-pro-vision':                   {"pricing": "0.50/1.50", "description": "Legacy vision-enabled model"},

            'gemini-2.0-pro-exp':                  {"pricing": "0.75/3.00", "description": "Experimental 2.0 Pro variant"},

            'gemini-1.5-flash-001-tuning':         {"pricing": "8.00/-", "description": "Tunable 1.5 Flash variant"},

            'gemini-1.5-flash-8b-exp-0827':        {"pricing": "??/??", "description": "???"},

            'gemini-1.5-flash-8b-exp-0924':        {"pricing": "??/??", "description": "???"},

            'gemini-2.0-flash-thinking-exp':       {"pricing": "??/??", "description": "Reasoning-optimized Flash"},

            'gemini-2.0-flash-thinking-exp-01-21': {"pricing": "??/??", "description": "???"},

            'gemini-2.0-flash-thinking-exp-1219':  {"pricing": "??/??", "description": "???"},

            'gemini-2.0-pro-exp-02-05':            {"pricing": "??/??", "description": "2025-02-05 Pro experiment"},

            'gemini-exp-1206':                     {"pricing": "??/??", "description": "Experimental 2023-12-06 model"},

            'learnlm-1.5-pro-experimental':        {"pricing": "??/??", "description": "Specialized learning model"},

        },

        PROVIDER_OPENAI: {

            "gpt-4o-mini":                         {"pricing": "0.15/0.60", "description": "Lightweight GPT-4o variant"},

            "gpt-4o-mini-audio-preview":           {"pricing": "0.15/0.60", "description": "GPT-4o Mini Audio (Text)"},

            "gpt-3.5-turbo":                       {"pricing": "0.50/1.50", "description": "Base GPT-3.5 Turbo model"},

            "gpt-3.5-turbo-1106":                  {"pricing": "0.50/1.50", "description": "Enhanced GPT-3.5 Turbo"},

            "gpt-4o-mini-realtime-preview":        {"pricing": "0.60/2.40", "description": "GPT-4o Mini Realtime (Text)"},

            "o3-mini":                             {"pricing": "0.60/2.40", "description": "Lightweight reasoning model for STEM"},

            "gpt-4o":                              {"pricing": "2.50/10.00", "description": "Base GPT-4o model"},

            "gpt-4o-audio-preview":                {"pricing": "2.50/10.00", "description": "GPT-4o Audio (Text)"},

            "o1-mini":                             {"pricing": "3.00/12.00", "description": "Lightweight o1 variant"},

            "gpt-4o-realtime-preview":             {"pricing": "5.00/20.00", "description": "GPT-4o Realtime (Text)"},

            "gpt-4-0125-preview":                  {"pricing": "10.00/30.00", "description": "Preview GPT-4 Turbo"},

            "gpt-4-1106-preview":                  {"pricing": "10.00/30.00", "description": "Preview GPT-4 Turbo"},

            "gpt-4-turbo":                         {"pricing": "10.00/30.00", "description": "Latest GPT-4 Turbo release"},

            "gpt-4-turbo-2024-04-09":              {"pricing": "10.00/30.00", "description": "GPT-4 Turbo w/ vision"},

            "gpt-4-turbo-preview":                 {"pricing": "10.00/30.00", "description": "Latest preview GPT-4 Turbo"},

            "o1":                                  {"pricing": "15.00/60.00", "description": "Frontier reasoning model"},

            "o1-preview":                          {"pricing": "15.00/60.00", "description": "Preview of o1 model"},

            "gpt-4":                               {"pricing": "30.00/60.00", "description": "Latest GPT-4 stable release"},

            "gpt-4-0613":                          {"pricing": "30.00/60.00", "description": "June 2023 GPT-4 snapshot"},

        },

    }



    def __init__(self):

        load_dotenv()

        self.enable_utf8_encoding()

        self.provider = self.DEFAULT_PROVIDER.lower()

        self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]

        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))

        self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()

        self.initialize_logger()



    def enable_utf8_encoding(self):

        """

        Ensure UTF-8 encoding for standard output and error streams.

        """

        if hasattr(sys.stdout, "reconfigure"):

            sys.stdout.reconfigure(encoding="utf-8", errors="replace")

        if hasattr(sys.stderr, "reconfigure"):

            sys.stderr.reconfigure(encoding="utf-8", errors="replace")



    def initialize_logger(self):

        """

        YAML logging via Loguru: clears logs, sets global context, and configures sinks

        """

        log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"

        log_filepath = os.path.join(self.log_dir, log_filename)

        open(log_filepath, "w").close()

        logger.remove()

        logger.configure(extra={"provider": self.provider, "model": self.model_params.get("model_name")})



        def yaml_logger_sink(log_message):

            log_record = log_message.record

            formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")

            formatted_level = f"!{log_record['level'].name}"

            logger_name = log_record["name"]

            formatted_function_name = f"*{log_record['function']}"

            line_number = log_record["line"]

            extra_provider = log_record["extra"].get("provider")

            extra_model = log_record["extra"].get("model")

            log_message_content = log_record["message"]



            if "\n" in log_message_content:

                formatted_message = "|\n" + "\n".join(

                    f"  {line}" for line in log_message_content.splitlines()

                )

            else:

                formatted_message = (

                    f"'{log_message_content}'"

                    if ":" in log_message_content

                    else log_message_content

                )



            log_lines = [

                f"- time: {formatted_timestamp}",

                f"  level: {formatted_level}",

                f"  name: {logger_name}",

                f"  funcName: {formatted_function_name}",

                f"  lineno: {line_number}",

            ]

            if extra_provider is not None:

                log_lines.append(f"  provider: {extra_provider}")

            if extra_model is not None:

                log_lines.append(f"  model: {extra_model}")



            log_lines.append(f"  message: {formatted_message}")

            log_lines.append("")



            with open(log_filepath, "a", encoding="utf-8") as log_file:

                log_file.write("\n".join(log_lines) + "\n")



        logger.add(yaml_logger_sink, level="DEBUG", enqueue=True, format="{message}")



# ========================================================

# 1.3 Recipe Configuration

# ========================================================

class RecipeConfig:

    """

    Manages recipe configuration and validation.

    """

    def __init__(self, config_path="config.json"):

        self.config_path = config_path

        self.config = self._load_config()



    def _load_config(self) -> dict:

        """Load and validate configuration from JSON file."""

        try:

            with open(self.config_path, 'r', encoding='utf-8') as f:

                config = json.load(f)

            self._validate_config(config)

            return config

        except FileNotFoundError:

            logger.warning(f"Config file {self.config_path} not found, using defaults")

            return self._get_default_config()

        except json.JSONDecodeError as e:

            logger.error(f"Error parsing config file: {e}")

            return self._get_default_config()



    def _validate_config(self, config: dict) -> None:

        """Validate configuration structure."""

        required_fields = ['initial_prompt', 'recipe_steps']

        for field in required_fields:

            if field not in config:

                raise ValueError(f"Missing required field '{field}' in config")



        if not isinstance(config['recipe_steps'], list):

            raise ValueError("'recipe_steps' must be a list")



        for step in config['recipe_steps']:

            if not isinstance(step, dict):

                raise ValueError("Each recipe step must be a dictionary")

            if 'chain' not in step:

                raise ValueError("Each recipe step must have a 'chain' field")



    def _get_default_config(self) -> dict:

        """Return default configuration."""

        return {

            "initial_prompt": "Propose a better alternative that is **cohesive** with the existing code",

            "recipe_steps": [

                {

                    "chain": ["OneshotConverter"],

                    "repeats": 1,

                    "gather": True,

                    "aggregator_chain": ["MultiResponseSelector"]

                }

            ],

            "default_provider": "openai",

            "logging": {

                "verbosity": "low",

                "format": "yaml"

            }

        }



    def get_initial_prompt(self) -> str:

        """Get the initial prompt from config."""

        return self.config.get('initial_prompt', '')



    def get_recipe_steps(self) -> List[Dict]:

        """Get the recipe steps from config."""

        return self.config.get('recipe_steps', [])



    def get_default_provider(self) -> str:

        """Get the default provider from config."""

        return self.config.get('default_provider', 'openai')



    def get_logging_config(self) -> Dict:

        """Get logging configuration."""

        return self.config.get('logging', {'verbosity': 'low', 'format': 'yaml'})



# ========================================================

# 1.5 Breadcrumb Management

# ========================================================

class BreadcrumbManager:

    """

    Manages breadcrumb file generation with hierarchical structure and content deduplication.

    """



    def __init__(self, base_output_dir=None):

        self.config = {

            'file_types': {

                'history': {'extension': '.txt', 'required': True},

                'response': {'extension': '.txt', 'required': True},

                'user_prompt': {'extension': '.txt', 'required': True},

                'template_name': {'extension': '.txt', 'required': True},

                'system_message': {'extension': '.txt', 'required': True},

                'system_instructions_raw': {'extension': None, 'required': False},  # Uses template extension

                'system_enhanced_prompt': {'extension': None, 'required': False},  # Uses template extension

            },

            'metadata_mapping': {

                'system_message': 'system_message',

                'system_instructions_raw': 'system_instructions_raw',

                'system_enhanced_prompt': 'system_enhanced_prompt',

            },

            'timestamp_format': '%Y.%m.%d_%H%M%S'

        }



        self.base_output_dir = base_output_dir or os.path.join(

            os.path.dirname(os.path.abspath(sys.argv[0])), "outputs"

        )



        self.registry = ContentRegistry(os.path.join(self.base_output_dir, "registry"))

        os.makedirs(self.base_output_dir, exist_ok=True)



    def _get_next_sequence_number(self, template_dir, template_name, depth_indicator):

        """

        Determine the next sequence number for a template by scanning existing files.



        Args:

            template_dir: Directory containing the template's output files

            template_name: Name of the template

            depth_indicator: Depth indicator character (a, b, c, etc.)



        Returns:

            Next sequence number as a string formatted as '001', '002', etc.

        """

        if not os.path.exists(template_dir):

            return "001"



        # Pattern to match: TemplateName_NNN_DepthIndicator

        pattern = re.compile(f"{re.escape(template_name)}_([0-9]{{3}})_{depth_indicator}\\.")



        max_seq = 0

        for filename in os.listdir(template_dir):

            match = pattern.search(filename)

            if match:

                seq_num = int(match.group(1))

                max_seq = max(max_seq, seq_num)



        return f"{max_seq + 1:03d}"



    def build_hierarchy_path(self, template_name: str, template_path_hierarchy: str = None) -> str:

        """Build hierarchical path for template output."""

        if template_path_hierarchy:

            # Use the provided hierarchy path

            hierarchy_parts = template_path_hierarchy.split(os.path.sep)

            return os.path.join(self.base_output_dir, *hierarchy_parts)

        else:

            # Use just the template name

            return os.path.join(self.base_output_dir, template_name)



    def build_file_path(self, template_name, file_type, session_id, depth_indicator,

                       template_path_hierarchy=None, extension=None):

        """

        Build file path for output using sequential numbering instead of timestamps.



        Args:

            template_name: Name of the template

            file_type: Type of file (history, response, etc.)

            session_id: Session identifier

            depth_indicator: Depth indicator (a, b, c, etc.)

            template_path_hierarchy: Optional path hierarchy for nested templates

            extension: Optional file extension (defaults to .txt or template extension for system files)



        Returns:

            Complete file path for the output file

        """

        # Determine template directory based on hierarchy

        template_dir = self.build_hierarchy_path(template_name, template_path_hierarchy)

        os.makedirs(template_dir, exist_ok=True)



        # Get the next sequence number for this template

        seq_number = self._get_next_sequence_number(template_dir, template_name, depth_indicator)



        # Determine file extension

        if not extension:

            if file_type in ['system_instructions_raw', 'system_enhanced_prompt']:

                # Use template extension for system files if available

                extension = os.path.splitext(template_name)[1] if os.path.splitext(template_name)[1] else '.txt'

            else:

                extension = self.config['file_types'].get(file_type, {}).get('extension', '.txt')



        # Build filename: TemplateName_SequentialNumber_DepthIndicator.FileType.Extension

        filename = f"{template_name}_{seq_number}_{depth_indicator}.{file_type}{extension}"



        return os.path.join(template_dir, filename)



    def write_output(self, content, template_name, file_type, session_id, depth_indicator,

                    template_path_hierarchy=None, extension=None, mode='w'):

        """

        Write output to file with content deduplication.



        If content already exists in registry, creates a symlink instead of duplicate file.

        """

        # Build file path

        file_path = self.build_file_path(

            template_name=template_name,

            file_type=file_type,

            session_id=session_id,

            depth_indicator=depth_indicator,

            template_path_hierarchy=template_path_hierarchy,

            extension=extension

        )



        # Store in registry and get content hash

        content_hash = self.registry.store_content(file_path, content)



        try:

            # Ensure directory exists

            os.makedirs(os.path.dirname(file_path), exist_ok=True)



            # Write the content to file

            with open(file_path, mode, encoding='utf-8') as f:

                f.write(content)



            return file_path

        except Exception as e:

            logger.error(f"Error writing to {file_path}: {str(e)}")

            return None



    def format_history_block(self, provider, model_name, template_filename,

                           template_input, response_text, system_instructions=""):

        """Format the history block with metadata and input/output."""

        seq_number = os.path.basename(template_filename).split('_')[1] if '_' in os.path.basename(template_filename) else "XXX"

        depth_indicator = os.path.basename(template_filename).split('_')[2].split('.')[0] if '_' in os.path.basename(template_filename) else "X"



        formatted_block = (

            f"=== INTERACTION {seq_number}-{depth_indicator} ===\n"

            f"Provider: {provider}\n"

            f"Model: {model_name}\n"

            f"Template: {os.path.basename(template_filename)}\n\n"

            f"User Prompt:\n{template_input}\n\n"

            f"System Instructions:\n{system_instructions}\n\n"

            f"Response:\n{response_text}\n\n"

            f"{'=' * 40}\n\n"

        )

        return formatted_block



    def write_interaction_outputs(self, provider, model_name, template_name,

                                template_input, response_text, session_id,

                                depth_indicator, template_path_hierarchy=None,

                                template_extension='.xml', metadata=None):

        """

        Write all interaction outputs using sequential numbering for files.



        Args:

            provider: LLM provider name

            model_name: Model name

            template_name: Template name

            template_input: User input

            response_text: LLM response

            session_id: Session ID

            depth_indicator: Depth indicator (a, b, c, etc.)

            template_path_hierarchy: Optional path hierarchy for nested templates

            template_extension: Extension of the template file

            metadata: Optional metadata dictionary with additional outputs

        """

        metadata = metadata or {}

        system_message = metadata.get('system_message', '')



        # Template extension without the dot

        template_ext = template_extension



        # Write standard outputs (always using .txt extension)

        self.write_output(

            template_input,

            template_name,

            'user_prompt',

            session_id,

            depth_indicator,

            template_path_hierarchy

        )



        self.write_output(

            template_name,

            template_name,

            'template_name',

            session_id,

            depth_indicator,

            template_path_hierarchy

        )



        self.write_output(

            response_text,

            template_name,

            'response',

            session_id,

            depth_indicator,

            template_path_hierarchy

        )



        self.write_output(

            system_message,

            template_name,

            'system_message',

            session_id,

            depth_indicator,

            template_path_hierarchy

        )



        # Format and write history

        history_content = self.format_history_block(

            provider,

            model_name,

            template_name,

            template_input,

            response_text,

            system_instructions=system_message

        )



        self.write_output(

            history_content,

            template_name,

            'history',

            session_id,

            depth_indicator,

            template_path_hierarchy

        )



        # Write metadata-based outputs with special handling for system files

        for metadata_key, output_type in self.config['metadata_mapping'].items():

            if metadata_key in metadata and metadata[metadata_key]:

                # Use template extension for system files

                ext = template_ext if output_type in ['system_instructions_raw', 'system_enhanced_prompt'] else '.txt'

                self.write_output(

                    metadata[metadata_key],

                    template_name,

                    output_type,

                    session_id,

                    depth_indicator,

                    template_path_hierarchy,

                    extension=ext

                )



    def get_available_templates(self, level_path=None) -> List[str]:

        """Get available templates at the specified hierarchy level."""

        instruction_templates_dir = os.path.join(

            os.path.dirname(os.path.abspath(sys.argv[0])),

            "instruction_templates"

        )



        if level_path:

            # Get subdirectories of the specified path

            search_path = os.path.join(instruction_templates_dir, level_path)

            if os.path.isdir(search_path):

                return [os.path.join(level_path, d) for d in os.listdir(search_path)

                       if os.path.isdir(os.path.join(search_path, d))]

        else:

            # Get root level templates/directories

            return [d for d in os.listdir(instruction_templates_dir)

                   if os.path.isdir(os.path.join(instruction_templates_dir, d))]



        return []



    def get_system_instructions(self, template_path: str) -> List[str]:

        """Get available system instructions for a template."""

        instruction_templates_dir = os.path.join(

            os.path.dirname(os.path.abspath(sys.argv[0])),

            "instruction_templates"

        )



        template_dir = os.path.join(instruction_templates_dir, template_path)

        if os.path.isdir(template_dir):

            # Get all XML files in the template directory

            return [f for f in os.listdir(template_dir)

                   if f.endswith(".xml") and os.path.isfile(os.path.join(template_dir, f))]



        return []



# ========================================================

# 2. Low-Level I/O Communicator

# ========================================================

class LowestLevelCommunicator:

    """

    Records raw request and response streams, preserving the entire stream

    before any filtering or transformation.

    """

    def __init__(self):

        self.raw_interactions = []

        self.breadcrumb_manager = BreadcrumbManager()



    def record_api_request(self, provider: str, model_name: str, messages: List[Dict[str, str]], metadata: dict = None):

        timestamp = datetime.now().strftime("%Y.%m.%d %H:%M:%S")

        self.raw_interactions.append({

            "direction": "request",

            "provider": provider,

            "model_name": model_name,

            "messages": messages,

            "timestamp": timestamp,

            "metadata": metadata or {},

        })



    def record_api_response(self, provider: str, model_name: str, response_text: str, metadata: dict = None):

        timestamp = datetime.now().strftime("%Y.%m.%d %H:%M:%S")

        self.raw_interactions.append({

            "direction": "response",

            "provider": provider,

            "model_name": model_name,

            "content": response_text,

            "timestamp": timestamp,

            "metadata": metadata or {},

        })

        # Stream output to files

        self._stream_output(provider, model_name, response_text, metadata or {})



    def _stream_output(self, provider: str, model_name: str, response_text: str, metadata: dict):

        """

        Centralized method to handle streaming outputs using BreadcrumbManager.

        """

        template_name = metadata.get('template_name', 'default_template')

        session_id = metadata.get('session_id', '001')

        depth_indicator = metadata.get('depth_indicator', 'a')

        template_path_hierarchy = metadata.get('template_path_hierarchy', template_name)

        template_extension = metadata.get('template_extension', '.txt')



        self.breadcrumb_manager.write_interaction_outputs(

            provider=provider,

            model_name=model_name,

            template_name=template_name,

            template_input=metadata.get('template_input', ''),

            response_text=response_text,

            session_id=session_id,

            depth_indicator=depth_indicator,

            template_path_hierarchy=template_path_hierarchy,

            template_extension=template_extension,

            metadata={

                'system_message': metadata.get('system_message', ''),

                'raw_instructions': metadata.get('raw_instructions', ''),

                'enhanced_prompt': metadata.get('enhanced_prompt', '')

            }

        )



    def get_interaction_history(self) -> List[Dict]:

        return self.raw_interactions



    def format_interaction_log(self) -> str:

        """Format the interaction log for display."""

        formatted_parts = []

        for interaction in self.raw_interactions:

            direction = interaction["direction"].upper()

            provider = interaction["provider"]

            model_name = interaction["model_name"]

            timestamp = interaction["timestamp"]



            if direction == "REQUEST":

                messages = interaction.get("messages", [])

                formatted_content = "\n".join([

                    f"{msg.get('role', 'unknown')}: {msg.get('content', '')}"

                    for msg in messages

                ])

            else:  # RESPONSE

                formatted_content = interaction.get("content", "")



            formatted_parts.append(

                f"[{timestamp}] {direction} - {provider}.{model_name}\n"

                f"{'=' * 40}\n"

                f"{formatted_content}\n"

                f"{'=' * 40}\n"

            )



        return "\n".join(formatted_parts)





# ========================================================

# 3. LLM Interactions

# ========================================================

class LLMInteractions:

    """

    Communicates with the chosen LLM provider via LangChain, logging requests/responses.

    """



    LANGCHAIN_CLIENTS = {

        Config.PROVIDER_OPENAI: ChatOpenAI,

        Config.PROVIDER_ANTHROPIC: ChatAnthropic,

        Config.PROVIDER_GOOGLE: ChatGoogleGenerativeAI,

        Config.PROVIDER_DEEPSEEK: lambda **kwargs: ChatOpenAI(base_url="https://api.deepseek.com", **kwargs),

    }



    def __init__(self, api_key=None, model_name=None, provider=None):

        self.config = Config()

        self.provider = provider or self.config.provider

        self.model_name = model_name or self.config.model_params["model_name"]

        self.communicator = LowestLevelCommunicator()

        self.client = self._initialize_llm_client(api_key)



    def _initialize_llm_client(self, api_key=None):

        api_key_env = Config.API_KEY_ENV_VARS.get(self.provider)

        used_api_key = api_key or os.getenv(api_key_env)

        client_class = self.LANGCHAIN_CLIENTS.get(self.provider)

        if not client_class:

            raise ValueError(f"Unsupported LLM provider: {self.provider}")

        return client_class(api_key=used_api_key, model=self.model_name)



    def request_llm_response(self, messages, model_name=None, metadata=None):

        """

        Sends a request to the LLM, records request/response, returns raw text.

        """

        used_model = model_name or self.model_name

        if metadata is None:

            metadata = {}



        self.communicator.record_api_request(self.provider, used_model, messages, metadata)

        try:

            prompt = "\n".join(msg["content"] for msg in messages if msg["role"] in ["system", "user"])

            response = self.client.invoke(prompt)

            raw_text = response.content

            self.communicator.record_api_response(self.provider, used_model, raw_text, metadata)

            return raw_text

        except Exception as exc:

            logger.error(f"Error calling {self.provider}.{used_model}: {exc}")

            return None







# ========================================================

# 4. Template File Manager

# ========================================================

class TemplateFileManager:



    ALLOWED_FILE_EXTS = (".xml", ".py", ".json", ".md", ".txt")

    EXCLUDED_FILE_NAMES = ["ignore_me", "do_not_load", "temp_file"]

    EXCLUDED_FILE_PATHS = ["\\_md\\"]

    EXCLUDED_PATTERNS = [r".*\.\d{2}\.\d{2}.*"]

    MAX_TEMPLATE_SIZE_KB = 100

    REQUIRED_CONTENT_MARKERS = ["<template>", "[INPUT_PROMPT]"]



    def __init__(self):

        self.template_dir = os.getcwd()

        self.template_cache = {}



    def refresh_template_cache(self):

        """ Clears and reloads the template cache by scanning the working directory. """

        self.template_cache.clear()

        pattern = os.path.join(self.template_dir, "**", "*.*")

        for filepath in glob.glob(pattern, recursive=True):

            name = os.path.splitext(os.path.basename(filepath))[0]

            if self.validate_template(filepath):

                self.template_cache[name] = filepath



    def validate_template(self, filepath):

        _, ext = os.path.splitext(filepath)

        filename = os.path.basename(filepath)

        basename, _ = os.path.splitext(filename)

        filepath_lower = filepath.lower()



        if ext.lower() not in self.ALLOWED_FILE_EXTS:

            return False

        if basename in self.EXCLUDED_FILE_NAMES:

            return False

        if any(excluded in filepath_lower for excluded in self.EXCLUDED_FILE_PATHS):

            return False

        if any(re.match(pattern, filename, re.IGNORECASE) for pattern in self.EXCLUDED_PATTERNS):

            return False

        try:

            filesize_kb = os.path.getsize(filepath) / 1024

            if filesize_kb > self.MAX_TEMPLATE_SIZE_KB:

                return False

            with open(filepath, "r", encoding="utf-8") as f:

                content = f.read()

            if not all(marker in content for marker in self.REQUIRED_CONTENT_MARKERS):

                return False

        except Exception:

            return False



        return True



    def load_templates(self, template_name_list):

        # Preloads specified templates into the cache if found.

        for name in template_name_list:

            _ = self.find_template_path(name)



    def find_template_path(self, template_name):

        # Retrieves the template path from cache; searches if not found.

        if template_name in self.template_cache:

            return self.template_cache[template_name]

        for ext in self.ALLOWED_FILE_EXTS:

            search_pattern = os.path.join(self.template_dir, "**", f"{template_name}{ext}")

            files = glob.glob(search_pattern, recursive=True)

            if files:

                self.template_cache[template_name] = files[0]

                return files[0]

        return None



    def parse_template_content(self, template_path):

        # Reads file content, extracts placeholders, and returns structured data.

        try:

            with open(template_path, "r", encoding="utf-8") as f:

                content = f.read()

            placeholders = list(set(re.findall(r"\[(.*?)\]", content)))

            template_data = {"path": template_path, "content": content, "placeholders": placeholders}

            return template_data

        except Exception as e:

            logger.error(f"Error parsing template {template_path}: {e}")

            return {}



    def extract_placeholders(self, template_name):

        # Returns a list of placeholders found in a specific template.

        template_path = self.find_template_path(template_name)

        if not template_path:

            return []

        parsed_template = self.parse_template_content(template_path)

        if not parsed_template:

            return []

        return parsed_template.get("placeholders", [])



    def extract_template_metadata(self, template_name):

        # Extracts metadata (agent_name, version, status, description, etc.) from a template.

        template_path = self.find_template_path(template_name)

        if not template_path:

            return {}

        parsed_template = self.parse_template_content(template_path)

        if not parsed_template:

            return {}



        content = parsed_template["content"]

        metadata = {

            "agent_name": self.extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),

            "version": self.extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),

            "status": self.extract_value_from_content(content, r'<status value="([^"]*)"\s*/>'),

            "description": self.extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),

            "system_prompt": self.extract_value_from_content(content, r'<system_prompt value="([^"]*)"\s*/>')

        }

        return metadata



    def extract_value_from_content(self, content, pattern):

        match = re.search(pattern, content)

        return match.group(1) if match else None



    def list_available_templates(

        self,

        exclude_paths=None,

        exclude_names=None,

        exclude_versions=None,

        exclude_statuses=None,

        exclude_none_versions=False,

        exclude_none_statuses=False,

    ):

        """

        Lists templates filtered by various exclusion criteria.

        """

        search_pattern = os.path.join(self.template_dir, "**", "*.*")

        templates_info = {}



        for filepath in glob.glob(search_pattern, recursive=True):

            if not self.validate_template(filepath):

                continue

            template_name = os.path.splitext(os.path.basename(filepath))[0]

            parsed_template = self.parse_template_content(filepath)

            if not parsed_template:

                logger.warning(f"Skipping {filepath} due to parsing error.")

                continue

            content = parsed_template["content"]

            try:

                templates_info[template_name] = {

                    "path": filepath,

                    "name": self.extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),

                    "description": self.extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),

                    "version": self.extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),

                    "status": self.extract_value_from_content(content, r'<status value="([^"]*)"\s*/>'),

                }

            except Exception as e:

                logger.error(f"Error loading template from {filepath}: {e}")



        filtered_templates = {}

        for name, info in templates_info.items():

            if (

                (not exclude_paths or info["path"] not in exclude_paths)

                and (not exclude_names or info["name"] not in exclude_names)

                and (not exclude_versions or info["version"] not in exclude_versions)

                and (not exclude_statuses or info["status"] not in exclude_statuses)

                and (not exclude_none_versions or info["version"] is not None)

                and (not exclude_none_statuses or info["status"] is not None)

            ):

                filtered_templates[name] = info



        return filtered_templates



    def prepare_template(self, template_filepath, input_prompt=""):

        """

        Reads the template file, replaces placeholders with default or dynamic values, and returns the final content.

        """

        parsed_template = self.parse_template_content(template_filepath)

        if not parsed_template:

            return None



        content = parsed_template["content"]

        placeholders = {

            "[HEADER]": f"```{os.path.splitext(template_filepath)[1]}",

            "[FILENAME]": os.path.basename(template_filepath),

            "[OUTPUT_FORMAT]": "plain_text",

            "[ORIGINAL_PROMPT_LENGTH]": str(len(input_prompt)),

            "[RESPONSE_PROMPT_LENGTH]": str(int(len(input_prompt) * 0.9)),

            "[INPUT_PROMPT]": input_prompt,

            "[ADDITIONAL_CONSTRAINTS]": "",

            "[ADDITIONAL_PROCESS_STEPS]": "",

            "[ADDITIONAL_GUIDELINES]": "",

            "[ADDITIONAL_REQUIREMENTS]": "",

            "[FOOTER]": "```",

        }



        for placeholder, value in placeholders.items():

            value_str = str(value)

            content = content.replace(placeholder, value_str)



        return [content, {"template_name": os.path.basename(template_filepath), "template_input": input_prompt}]



    def get_template_content(self, template_name): # previously missing

        """

        Retrieves the raw content of a template given its name.

        """

        template_path = self.find_template_path(template_name)

        if template_path:

            return self.parse_template_content(template_path)["content"]

        return None



    def apply_template(self, template_content, input_prompt): # previously missing

        """

        Applies the template content and input prompt by replacing placeholders.

        """

        placeholders = {

            "[INPUT_PROMPT]": input_prompt,

        }

        content = template_content

        for placeholder, value in placeholders.items():

            content = content.replace(placeholder, value)

        return content



    def extract_template_parts(self, raw_text):

        """

        Extracts specific sections from the raw template text (system_prompt, etc.).

        """

        system_prompt_match = re.search(r'<system_prompt value="([^"]*)"\s*/>', raw_text)

        system_prompt = system_prompt_match.group(1) if system_prompt_match else ""



        template_prompt_match = re.search(r"\[TEMPLATE_START\](.*?)\[TEMPLATE_END\]", raw_text, re.DOTALL)

        template_prompt = template_prompt_match.group(1) if template_prompt_match else raw_text



        return system_prompt, template_prompt



# ========================================================

# 1.4 Content Registry

# ========================================================

class ContentRegistry:

    """

    Manages content deduplication through hashing and maintains a global mapping table.

    """

    def __init__(self, registry_dir="registry"):

        self.registry_dir = registry_dir

        self.hashes_dir = os.path.join(registry_dir, "hashes")

        self.mapping_file = os.path.join(registry_dir, "mapping.json")

        self._ensure_dirs()

        self.mapping = self._load_mapping()



    def _ensure_dirs(self):

        """Create registry directories if they don't exist."""

        os.makedirs(self.hashes_dir, exist_ok=True)



    def _load_mapping(self) -> dict:

        """Load the mapping table from disk."""

        try:

            with open(self.mapping_file, 'r', encoding='utf-8') as f:

                return json.load(f)

        except (FileNotFoundError, json.JSONDecodeError):

            return {}



    def _save_mapping(self):

        """Save the current mapping table to disk."""

        with open(self.mapping_file, 'w', encoding='utf-8') as f:

            json.dump(self.mapping, f, indent=2)



    def _hash_content(self, content: str) -> str:

        """Generate a hash for the given content."""

        import hashlib

        return hashlib.sha256(content.encode('utf-8')).hexdigest()



    def store_content(self, breadcrumb_path: str, content: str) -> str:

        """

        Store content and return its hash. If content already exists,

        return existing hash and update mapping.

        """

        content_hash = self._hash_content(content)

        hash_path = os.path.join(self.hashes_dir, f"{content_hash}.txt")



        # Store content if it doesn't exist

        if not os.path.exists(hash_path):

            with open(hash_path, 'w', encoding='utf-8') as f:

                f.write(content)



        # Update mapping

        self.mapping[breadcrumb_path] = content_hash

        self._save_mapping()



        return content_hash



    def get_content(self, breadcrumb_path: str) -> Optional[str]:

        """Retrieve content for a given breadcrumb path."""

        content_hash = self.mapping.get(breadcrumb_path)

        if not content_hash:

            return None



        hash_path = os.path.join(self.hashes_dir, f"{content_hash}.txt")

        try:

            with open(hash_path, 'r', encoding='utf-8') as f:

                return f.read()

        except FileNotFoundError:

            return None



    def get_hash(self, breadcrumb_path: str) -> Optional[str]:

        """Get the hash for a given breadcrumb path."""

        return self.mapping.get(breadcrumb_path)



    def list_similar_content(self, content: str, threshold=0.9) -> List[str]:

        """Find breadcrumb paths with similar content."""

        from difflib import SequenceMatcher

        content_hash = self._hash_content(content)

        similar_paths = []



        for path, hash_value in self.mapping.items():

            if hash_value == content_hash:

                similar_paths.append(path)

                continue



            stored_content = self.get_content(path)

            if stored_content:

                similarity = SequenceMatcher(None, content, stored_content).ratio()

                if similarity >= threshold:

                    similar_paths.append(path)



        return similar_paths



# ========================================================

# 5. Prompt Refinement Engine

# ========================================================

class RefinementWorkflow:

    """

    Core engine for refining prompts using templates and LLMs.

    """

    def __init__(self, llm_interactions: LLMInteractions, template_manager: TemplateFileManager):

        self.llm_interactions = llm_interactions

        self.template_manager = template_manager

        self.breadcrumb_manager = BreadcrumbManager()



    def run_template(self, template_name: str, input_prompt: str, session_id=None, depth_indicator=None, template_path_hierarchy=None) -> str:

        """

        Executes a single template refinement step.

        """

        template_path = self.template_manager.find_template_path(template_name)

        if template_path is None:

            logger.error(f"Template '{template_name}' not found.")

            return input_prompt



        template_content = self.template_manager.get_template_content(template_name)

        if template_content is None:

            logger.error(f"Template content for '{template_name}' could not be loaded.")

            return input_prompt



        instructions, metadata = self.template_manager.prepare_template(template_path, input_prompt)

        instructions = instructions if instructions else template_content

        enhanced_prompt = self.template_manager.apply_template(instructions, input_prompt)



        system_prompt_text, agent_instructions = self.template_manager.extract_template_parts(template_content)

        messages = [

            {"role": "system", "content": system_prompt_text.strip()},

            {"role": "user", "content": agent_instructions.replace("[INPUT_PROMPT]", enhanced_prompt).strip()}

        ]



        template_extension = os.path.splitext(template_path)[1]



        # Enhanced metadata with all necessary information

        metadata.update({

            "template_name": template_name,

            "template_input": input_prompt,

            "session_id": session_id,

            "depth_indicator": depth_indicator,

            "template_path_hierarchy": template_path_hierarchy,

            "template_extension": template_extension,

            "system_message": system_prompt_text,

            "raw_instructions": template_content,

            "enhanced_prompt": enhanced_prompt

        })



        response_text = self.llm_interactions.request_llm_response(

            messages=messages,

            metadata=metadata

        )



        return response_text if response_text else input_prompt



    def run_refinement_recipe(self, recipe: List[Dict], initial_prompt: str, session_id=None) -> Union[str, Dict]:

        """

        Executes a sequence of template refinements as defined in the recipe, including aggregator chains,

        creating hierarchical output.

        """

        current_prompt = initial_prompt

        depth_counter = 0

        template_hierarchy_path = ""



        for step_config in recipe:

            chain = step_config.get("chain", [])

            repeats = step_config.get("repeats", 1)

            aggregator_chain = step_config.get("aggregator_chain", [])



            # Process regular chain

            for template_name in chain:

                depth_counter += 1

                depth_indicator_char = chr(ord('a') + depth_counter - 1)

                template_hierarchy_path = os.path.join(template_hierarchy_path, template_name)



                for _ in range(repeats):

                    current_prompt = self.run_template(

                        template_name,

                        current_prompt,

                        session_id=session_id,

                        depth_indicator=depth_indicator_char,

                        template_path_hierarchy=template_hierarchy_path

                    )



            # Process aggregator chain if it exists

            if aggregator_chain:

                aggregator_input_prompt = current_prompt # Use current prompt as input for aggregator

                aggregator_hierarchy_path = template_hierarchy_path # Aggregator is within the same hierarchy level



                for aggregator_template_name in aggregator_chain:

                    depth_counter += 1 # Increment depth for aggregator template

                    depth_indicator_char = chr(ord('a') + depth_counter - 1) # New depth indicator for aggregator

                    aggregator_hierarchy_path = os.path.join(aggregator_hierarchy_path, aggregator_template_name) # Hierarchy for aggregator



                    current_prompt = self.run_template(

                        aggregator_template_name,

                        aggregator_input_prompt, # Use aggregator input prompt

                        session_id=session_id,

                        depth_indicator=depth_indicator_char, # Depth indicator for aggregator step

                        template_path_hierarchy=aggregator_hierarchy_path # Hierarchy path for aggregator

                    )

                    aggregator_input_prompt = current_prompt # Output of aggregator step becomes input for next aggregator step



        return current_prompt





# ========================================================

# 6. Main Execution Logic

# ========================================================

class Execution:

    def __init__(self, config_path="config.json"):

        self.recipe_config = RecipeConfig(config_path)

        self.config = Config()

        self._initialize_components()

        self.session_counter = 0



    def _initialize_components(self):

        """Initialize all components with proper configuration."""

        provider = os.getenv("LLM_PROVIDER", self.recipe_config.get_default_provider()).lower()

        self.config.provider = provider

        self.llm_interactions = LLMInteractions(provider=provider)

        self.template_manager = TemplateFileManager()

        self.refinement_engine = RefinementWorkflow(self.llm_interactions, self.template_manager)

        self.breadcrumb_manager = BreadcrumbManager()



        # Configure logging

        log_config = self.recipe_config.get_logging_config()

        self.config.verbosity = log_config.get('verbosity', 'low')



        # Ensure LLM provider and model are properly set

        self.llm_interactions.provider = provider

        self.llm_interactions.model_name = self.config.DEFAULT_MODEL_PARAMS[provider]["model_name"]



    def _select_from_list(self, items: List[str], prompt: str) -> Optional[str]:

        """Display numbered list and get user selection."""

        if not items:

            print("No items available.")

            return None



        print(f"\n{prompt}")

        for i, item in enumerate(items, 1):

            print(f"{i}. {item}")



        while True:

            try:

                choice = input("\nEnter number (or 'q' to quit): ").strip()

                if choice.lower() == 'q':

                    return None

                idx = int(choice) - 1

                if 0 <= idx < len(items):

                    return items[idx]

                print("Invalid selection. Try again.")

            except ValueError:

                print("Please enter a number or 'q' to quit.")



    def run_interactive(self):

        """Run in interactive mode with template selection."""

        self.template_manager.refresh_template_cache()

        self.session_counter += 1

        session_id = f"{self.session_counter:01d}"

        current_level = None



        while True:

            # Get available templates at current level

            templates = self.breadcrumb_manager.get_available_templates(current_level)



            if not current_level:

                # Root level template selection

                template = self._select_from_list(

                    templates,

                    "Select a root template:"

                )

                if not template:

                    break

                current_level = template

            else:

                # Get system instructions for current template

                instructions = self.breadcrumb_manager.get_system_instructions(current_level)

                instruction = self._select_from_list(

                    instructions,

                    f"Select system instruction for {current_level}:"

                )

                if not instruction:

                    current_level = None  # Go back to root

                    continue



                # Get user input

                user_input = input("\nEnter your prompt (or 'q' to go back): ").strip()

                if user_input.lower() == 'q':

                    current_level = None

                    continue



                # Process the interaction

                depth_indicator = chr(ord('a') + len(current_level.split(os.path.sep)) - 1)

                response = self.refinement_engine.run_template(

                    template_name=os.path.basename(current_level),

                    input_prompt=user_input,

                    session_id=session_id,

                    depth_indicator=depth_indicator,

                    template_path_hierarchy=current_level

                )



                # Display response

                print("\nResponse:")

                print("=" * 40)

                print(response)

                print("=" * 40)



                # Ask to save to history

                save = input("\nSave to history? (y/n): ").strip().lower()

                if save == 'y':

                    self.breadcrumb_manager.write_interaction_outputs(

                        provider=self.llm_interactions.provider,

                        model_name=self.llm_interactions.model_name,

                        template_name=os.path.basename(current_level),

                        template_input=user_input,

                        response_text=response,

                        session_id=session_id,

                        depth_indicator=depth_indicator,

                        template_path_hierarchy=current_level

                    )

                    print("Interaction saved.")



                # Ask to continue with current template

                cont = input("\nContinue with current template? (y/n): ").strip().lower()

                if cont != 'y':

                    current_level = None



    def run(self, interactive=False):

        """Main execution method."""

        if interactive:

            self.run_interactive()

        else:

            # Standard recipe execution

            self.template_manager.refresh_template_cache()

            self.session_counter += 1

            session_id = f"{self.session_counter:01d}"



            initial_prompt = self.recipe_config.get_initial_prompt()

            recipe_steps = self.recipe_config.get_recipe_steps()



            recipe_result = self.refinement_engine.run_refinement_recipe(

                recipe=recipe_steps,

                initial_prompt=initial_prompt,

                session_id=session_id

            )



            logger.info(f"Refinement process completed. Final Output: {recipe_result}")

            print("\n=== Initial Input (Raw I/O) ===")

            print(f'\n"""{initial_prompt}"""\n')

            print("\n=== Full Communicator Log (Raw I/O) ===")

            print(self.llm_interactions.communicator.format_interaction_log())



# ========================================================

# 7. Script Entry Point

# ========================================================

if __name__ == "__main__":

    try:

        # Initialize execution with config

        runner = Execution("config.json")



        # Run the refinement process

        runner.run()

    except Exception as e:

        logger.error(f"Error during execution: {e}")

        sys.exit(1)


## Message 2

A **single** change that dramatically shrinks the code is to **remove or drastically simplify the “breadcrumb” and “content registry” machinery**. Right now, the combination of `BreadcrumbManager` and `ContentRegistry` is responsible for a lot of repeated file-writing logic, hashing/deduplication, hierarchical path building, etc. If you drop those two pieces and use standard logging (with or without a simple output folder), you can cut hundreds of lines and remove the densest chunk of complexity.

Below is a concise reasoning:

1. **`BreadcrumbManager`**  
   - It does sequential naming, directory creation, symlinking, deduplication, aggregator file output, etc.  
   - A simpler approach: **immediately write each step’s result to a single file** (or single folder) without a layered numbering scheme or symbolic links. If you need session-based grouping, just do `session-{timestamp}.txt`.

2. **`ContentRegistry`**  
   - Maintains a global hash-based table and a “mapping” JSON file for deduplicating text.  
   - The overhead (computing hashes, storing them, cross-referencing, or rewriting the same text as a symlink) typically isn’t essential for most scripts. Simply writing the text or skipping duplicates based on your own rules in a few lines of code can achieve the same result if you even need it at all.

By dropping those two classes (and any calls into them), you remove:
- The entire content-hashing system,
- The hierarchical naming logic,
- The extra layers of I/O abstractions,
- The large method set for “building paths,” “writing files,” “store_content,” “list_similar_content,” etc.

All of that is best replaced by simple, direct file output (or direct logging) in the one or two places you really need it. This single change alone is the most straightforward way to **reduce code size** and **cut out unnecessary complexity**.

## Message 3

**Identifying complexity**

I’m analyzing a script with multiple classes handling separate aspects like config, logging, and recipes. Noticing code repetition and unnecessary dynamic code, there’s a lot of potential for streamlining.

**Streamlining code**

I’m evaluating unnecessary 'RefinementRecipe' code and overly complex classes. Unifying config reading and logging, and removing redundant checks and customizations, could simplify the script significantly.

**Implementing simplification**

To streamline the script, I’m consolidating or removing classes managing config and logging, eliminating duplication, and removing session management, aiming to reduce the code complexity substantially.

**Simplifying code complexity**

I’m leaning towards removing the "BreadcrumbManager" and "ContentRegistry" for direct logging. This cut-down strategy should streamline the script significantly by reducing its overall complexity.

**Evaluating complexity sources**

OK, let me see; the "BreadcrumbManager" and "ContentRegistry" are main complexity sources. Consolidating these could streamline the script by reducing redundant code and managing extensive logging tasks more effectively.

## Message 4

what is the single most effective way to reduce the unneccessary complexity and reduce the amount of code needed while retaining the existing functionality?

code:
#!/usr/bin/env python3
"""
llm_template_runner_clean.py

A single-file script for managing templates, LLM interactions, and orchestrating prompt refinements.
"""

import os
import sys
import re
import glob
import json
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Union, Optional

from dotenv import load_dotenv
from loguru import logger

# Provider SDKs
from openai import OpenAI
from anthropic import Anthropic
from google import generativeai as genai

# LangChain
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_google_genai import ChatGoogleGenerativeAI

# ========================================================
# 1. Global Configuration
# ========================================================
class Config:
    """
    Global settings
    - Always selects the item at the end, simply reordering these lines allows for quick change.
    """

    PROVIDER_ANTHROPIC = "anthropic"
    PROVIDER_DEEPSEEK = "deepseek"
    PROVIDER_GOOGLE = "google"
    PROVIDER_OPENAI = "openai"

    API_KEY_ENV_VARS = {
        PROVIDER_ANTHROPIC: "ANTHROPIC_API_KEY",
        PROVIDER_DEEPSEEK: "DEEPSEEK_API_KEY",
        PROVIDER_GOOGLE: "GOOGLE_API_KEY",
        PROVIDER_OPENAI: "OPENAI_API_KEY",
    }

    DEFAULT_PROVIDER = PROVIDER_ANTHROPIC
    DEFAULT_PROVIDER = PROVIDER_GOOGLE
    DEFAULT_PROVIDER = PROVIDER_DEEPSEEK
    DEFAULT_PROVIDER = PROVIDER_OPENAI

    DEFAULT_MODEL_PARAMS = {
        PROVIDER_ANTHROPIC: {
            "model_name": "claude-3-opus-20240229",      # (d1) [exorbitant]
            "model_name": "claude-2.1",                  # (c1) [expensive]
            "model_name": "claude-3-sonnet-20240229",    # (b1) [medium]
            "model_name": "claude-3-haiku-********",     # (a1) [cheap]
        },
        PROVIDER_DEEPSEEK: {
            "model_name": "deepseek-reasoner",           # (a3) [cheap]
            "model_name": "deepseek-coder",              # (a2) [cheap]
            "model_name": "deepseek-chat",               # (a1) [cheap]
        },
        PROVIDER_GOOGLE: {
            "model_name": "gemini-2.0-pro-experimental", # (c4) [expensive]
            "model_name": "gemini-2.0-flash-exp",        # (a4) [ultra-cheap]
            "model_name": "gemini-1.5-flash",            # (c4) [expensive]
            "model_name": "gemini-1.5-flash-8b",         # (a4) [ultra-cheap]
            "model_name": "gemini-2.0-flash",            # (b4) [medium]
        },
        PROVIDER_OPENAI: {
            "model_name": "o1",                          # (c3) [expensive]
            "model_name": "gpt-4-turbo-preview",         # (c2) [expensive]
            "model_name": "gpt-4-turbo",                 # (c1) [expensive]
            "model_name": "o1-mini",                     # (b3) [medium]
            "model_name": "gpt-4o",                      # (b2) [medium]
            "model_name": "gpt-3.5-turbo",               # (a3) [cheap]
            "model_name": "gpt-4o-mini",                 # (a1) [cheap]
            "model_name": "gpt-3.5-turbo-1106",          # (a2) [cheap]
            "model_name": "o3-mini",                     # (b1) [medium]
        },
    }

    SUPPORTED_MODELS = {
        PROVIDER_ANTHROPIC: {
            "claude-3-haiku-********":             {"pricing": "0.80/4.00", "description": "Claude 3 Haiku"},
            "claude-3-sonnet-20240229":            {"pricing": "3.00/15.00", "description": "Claude 3 Sonnet"},
            "claude-2":                            {"pricing": "8.00/24.00", "description": "Base Claude 2 model"},
            "claude-2.0":                          {"pricing": "8.00/24.00", "description": "Enhanced Claude 2.0"},
            "claude-2.1":                          {"pricing": "8.00/24.00", "description": "Latest Claude 2.1 release"},
            "claude-3-opus-20240229":              {"pricing": "15.00/75.00", "description": "Claude 3 Opus"},
        },
        PROVIDER_DEEPSEEK: {
            "deepseek-coder":                      {"pricing": "0.10/0.20", "description": "Code-specialized model"},
            "deepseek-chat":                       {"pricing": "0.14/0.28", "description": "DeepSeek Chat model"},
            "deepseek-reasoner":                   {"pricing": "0.55/2.19", "description": "Specialized reasoning model"},
        },
        PROVIDER_GOOGLE: {
            'gemini-1.5-flash-8b':                 {"pricing": "0.0375/0.15", "description": "Cost-optimized 8B param Flash"},
            'gemini-1.5-flash-8b-001':             {"pricing": "0.0375/0.15", "description": "Versioned 8B Flash model"},
            'gemini-1.5-flash-8b-latest':          {"pricing": "0.0375/0.15", "description": "Latest 8B Flash iteration"},
            'gemini-1.5-flash':                    {"pricing": "0.075/0.30", "description": "Base Gemini 1.5 Flash model"},
            'gemini-1.5-flash-001':                {"pricing": "0.075/0.30", "description": "Initial Gemini 1.5 Flash"},
            'gemini-1.5-flash-002':                {"pricing": "0.075/0.30", "description": "Updated 1.5 Flash version"},
            'gemini-1.5-flash-latest':             {"pricing": "0.075/0.30", "description": "Latest Gemini 1.5 Flash"},
            'gemini-2.0-flash-lite-preview':       {"pricing": "0.075/0.30", "description": "Preview of 2.0 Flash-Lite"},
            'gemini-2.0-flash':                    {"pricing": "0.10/0.40", "description": "Production 2.0 Flash (multimodal)"},
            'gemini-2.0-flash-001':                {"pricing": "0.10/0.40", "description": "Versioned 2.0 Flash release"},
            'gemini-2.0-flash-exp':                {"pricing": "0.10/0.40", "description": "Experimental 2.0 Flash precursor"},
            'gemini-1.5-pro':                      {"pricing": "0.45/2.40", "description": "Base Gemini 1.5 Pro model"},
            'gemini-1.5-pro-001':                  {"pricing": "0.45/2.40", "description": "Initial Gemini 1.5 Pro release"},
            'gemini-1.5-pro-002':                  {"pricing": "0.45/2.40", "description": "Updated Gemini 1.5 Pro version"},
            'gemini-1.5-pro-latest':               {"pricing": "0.45/2.40", "description": "Latest Gemini 1.5 Pro (2M token context)"},
            'gemini-1.0-pro':                      {"pricing": "0.50/1.50", "description": "Base Gemini 1.0 Pro model"},
            'gemini-1.0-pro-001':                  {"pricing": "0.50/1.50", "description": "Versioned Gemini 1.0 Pro"},
            'gemini-1.0-pro-latest':               {"pricing": "0.50/1.50", "description": "Latest Gemini 1.0 Pro (text)"},
            'gemini-1.0-pro-vision-latest':        {"pricing": "0.50/1.50", "description": "Gemini 1.0 Pro with vision"},
            'gemini-pro':                          {"pricing": "0.50/1.50", "description": "Legacy name for Gemini 1.0 Pro"},
            'gemini-pro-vision':                   {"pricing": "0.50/1.50", "description": "Legacy vision-enabled model"},
            'gemini-2.0-pro-exp':                  {"pricing": "0.75/3.00", "description": "Experimental 2.0 Pro variant"},
            'gemini-1.5-flash-001-tuning':         {"pricing": "8.00/-", "description": "Tunable 1.5 Flash variant"},
            'gemini-1.5-flash-8b-exp-0827':        {"pricing": "??/??", "description": "???"},
            'gemini-1.5-flash-8b-exp-0924':        {"pricing": "??/??", "description": "???"},
            'gemini-2.0-flash-thinking-exp':       {"pricing": "??/??", "description": "Reasoning-optimized Flash"},
            'gemini-2.0-flash-thinking-exp-01-21': {"pricing": "??/??", "description": "???"},
            'gemini-2.0-flash-thinking-exp-1219':  {"pricing": "??/??", "description": "???"},
            'gemini-2.0-pro-exp-02-05':            {"pricing": "??/??", "description": "2025-02-05 Pro experiment"},
            'gemini-exp-1206':                     {"pricing": "??/??", "description": "Experimental 2023-12-06 model"},
            'learnlm-1.5-pro-experimental':        {"pricing": "??/??", "description": "Specialized learning model"},
        },
        PROVIDER_OPENAI: {
            "gpt-4o-mini":                         {"pricing": "0.15/0.60", "description": "Lightweight GPT-4o variant"},
            "gpt-4o-mini-audio-preview":           {"pricing": "0.15/0.60", "description": "GPT-4o Mini Audio (Text)"},
            "gpt-3.5-turbo":                       {"pricing": "0.50/1.50", "description": "Base GPT-3.5 Turbo model"},
            "gpt-3.5-turbo-1106":                  {"pricing": "0.50/1.50", "description": "Enhanced GPT-3.5 Turbo"},
            "gpt-4o-mini-realtime-preview":        {"pricing": "0.60/2.40", "description": "GPT-4o Mini Realtime (Text)"},
            "o3-mini":                             {"pricing": "0.60/2.40", "description": "Lightweight reasoning model for STEM"},
            "gpt-4o":                              {"pricing": "2.50/10.00", "description": "Base GPT-4o model"},
            "gpt-4o-audio-preview":                {"pricing": "2.50/10.00", "description": "GPT-4o Audio (Text)"},
            "o1-mini":                             {"pricing": "3.00/12.00", "description": "Lightweight o1 variant"},
            "gpt-4o-realtime-preview":             {"pricing": "5.00/20.00", "description": "GPT-4o Realtime (Text)"},
            "gpt-4-0125-preview":                  {"pricing": "10.00/30.00", "description": "Preview GPT-4 Turbo"},
            "gpt-4-1106-preview":                  {"pricing": "10.00/30.00", "description": "Preview GPT-4 Turbo"},
            "gpt-4-turbo":                         {"pricing": "10.00/30.00", "description": "Latest GPT-4 Turbo release"},
            "gpt-4-turbo-2024-04-09":              {"pricing": "10.00/30.00", "description": "GPT-4 Turbo w/ vision"},
            "gpt-4-turbo-preview":                 {"pricing": "10.00/30.00", "description": "Latest preview GPT-4 Turbo"},
            "o1":                                  {"pricing": "15.00/60.00", "description": "Frontier reasoning model"},
            "o1-preview":                          {"pricing": "15.00/60.00", "description": "Preview of o1 model"},
            "gpt-4":                               {"pricing": "30.00/60.00", "description": "Latest GPT-4 stable release"},
            "gpt-4-0613":                          {"pricing": "30.00/60.00", "description": "June 2023 GPT-4 snapshot"},
        },
    }

    def __init__(self):
        load_dotenv()
        self.enable_utf8_encoding()
        self.provider = self.DEFAULT_PROVIDER.lower()
        self.model_params = self.DEFAULT_MODEL_PARAMS[self.provider]
        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        self.verbosity = os.getenv("LOG_VERBOSITY", "low").lower()
        self.initialize_logger()

    def enable_utf8_encoding(self):
        """
        Ensure UTF-8 encoding for standard output and error streams.
        """
        if hasattr(sys.stdout, "reconfigure"):
            sys.stdout.reconfigure(encoding="utf-8", errors="replace")
        if hasattr(sys.stderr, "reconfigure"):
            sys.stderr.reconfigure(encoding="utf-8", errors="replace")

    def initialize_logger(self):
        """
        YAML logging via Loguru: clears logs, sets global context, and configures sinks
        """
        log_filename = f"{os.path.splitext(os.path.basename(sys.argv[0]))[0]}.log.yml"
        log_filepath = os.path.join(self.log_dir, log_filename)
        open(log_filepath, "w").close()
        logger.remove()
        logger.configure(extra={"provider": self.provider, "model": self.model_params.get("model_name")})

        def yaml_logger_sink(log_message):
            log_record = log_message.record
            formatted_timestamp = log_record["time"].strftime("%Y.%m.%d %H:%M:%S")
            formatted_level = f"!{log_record['level'].name}"
            logger_name = log_record["name"]
            formatted_function_name = f"*{log_record['function']}"
            line_number = log_record["line"]
            extra_provider = log_record["extra"].get("provider")
            extra_model = log_record["extra"].get("model")
            log_message_content = log_record["message"]

            if "\n" in log_message_content:
                formatted_message = "|\n" + "\n".join(
                    f"  {line}" for line in log_message_content.splitlines()
                )
            else:
                formatted_message = (
                    f"'{log_message_content}'"
                    if ":" in log_message_content
                    else log_message_content
                )

            log_lines = [
                f"- time: {formatted_timestamp}",
                f"  level: {formatted_level}",
                f"  name: {logger_name}",
                f"  funcName: {formatted_function_name}",
                f"  lineno: {line_number}",
            ]
            if extra_provider is not None:
                log_lines.append(f"  provider: {extra_provider}")
            if extra_model is not None:
                log_lines.append(f"  model: {extra_model}")

            log_lines.append(f"  message: {formatted_message}")
            log_lines.append("")

            with open(log_filepath, "a", encoding="utf-8") as log_file:
                log_file.write("\n".join(log_lines) + "\n")

        logger.add(yaml_logger_sink, level="DEBUG", enqueue=True, format="{message}")

# ========================================================
# 1.3 Recipe Configuration
# ========================================================
class RecipeConfig:
    """
    Manages recipe configuration and validation.
    """
    def __init__(self, config_path="config.json"):
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> dict:
        """Load and validate configuration from JSON file."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self._validate_config(config)
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {self.config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing config file: {e}")
            return self._get_default_config()

    def _validate_config(self, config: dict) -> None:
        """Validate configuration structure."""
        required_fields = ['initial_prompt', 'recipe_steps']
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field '{field}' in config")

        if not isinstance(config['recipe_steps'], list):
            raise ValueError("'recipe_steps' must be a list")

        for step in config['recipe_steps']:
            if not isinstance(step, dict):
                raise ValueError("Each recipe step must be a dictionary")
            if 'chain' not in step:
                raise ValueError("Each recipe step must have a 'chain' field")

    def _get_default_config(self) -> dict:
        """Return default configuration."""
        return {
            "initial_prompt": "Propose a better alternative that is **cohesive** with the existing code",
            "recipe_steps": [
                {
                    "chain": ["OneshotConverter"],
                    "repeats": 1,
                    "gather": True,
                    "aggregator_chain": ["MultiResponseSelector"]
                }
            ],
            "default_provider": "openai",
            "logging": {
                "verbosity": "low",
                "format": "yaml"
            }
        }

    def get_initial_prompt(self) -> str:
        """Get the initial prompt from config."""
        return self.config.get('initial_prompt', '')

    def get_recipe_steps(self) -> List[Dict]:
        """Get the recipe steps from config."""
        return self.config.get('recipe_steps', [])

    def get_default_provider(self) -> str:
        """Get the default provider from config."""
        return self.config.get('default_provider', 'openai')

    def get_logging_config(self) -> Dict:
        """Get logging configuration."""
        return self.config.get('logging', {'verbosity': 'low', 'format': 'yaml'})

# ========================================================
# 1.5 Breadcrumb Management
# ========================================================
class BreadcrumbManager:
    """
    Manages breadcrumb file generation with hierarchical structure and content deduplication.
    """

    def __init__(self, base_output_dir=None):
        self.config = {
            'file_types': {
                'history': {'extension': '.txt', 'required': True},
                'response': {'extension': '.txt', 'required': True},
                'user_prompt': {'extension': '.txt', 'required': True},
                'template_name': {'extension': '.txt', 'required': True},
                'system_message': {'extension': '.txt', 'required': True},
                'system_instructions_raw': {'extension': None, 'required': False},  # Uses template extension
                'system_enhanced_prompt': {'extension': None, 'required': False},  # Uses template extension
            },
            'metadata_mapping': {
                'system_message': 'system_message',
                'system_instructions_raw': 'system_instructions_raw',
                'system_enhanced_prompt': 'system_enhanced_prompt',
            },
            'timestamp_format': '%Y.%m.%d_%H%M%S'
        }

        self.base_output_dir = base_output_dir or os.path.join(
            os.path.dirname(os.path.abspath(sys.argv[0])), "outputs"
        )

        self.registry = ContentRegistry(os.path.join(self.base_output_dir, "registry"))
        os.makedirs(self.base_output_dir, exist_ok=True)

    def _get_next_sequence_number(self, template_dir, template_name, depth_indicator):
        """
        Determine the next sequence number for a template by scanning existing files.

        Args:
            template_dir: Directory containing the template's output files
            template_name: Name of the template
            depth_indicator: Depth indicator character (a, b, c, etc.)

        Returns:
            Next sequence number as a string formatted as '001', '002', etc.
        """
        if not os.path.exists(template_dir):
            return "001"

        # Pattern to match: TemplateName_NNN_DepthIndicator
        pattern = re.compile(f"{re.escape(template_name)}_([0-9]{{3}})_{depth_indicator}\\.")

        max_seq = 0
        for filename in os.listdir(template_dir):
            match = pattern.search(filename)
            if match:
                seq_num = int(match.group(1))
                max_seq = max(max_seq, seq_num)

        return f"{max_seq + 1:03d}"

    def build_hierarchy_path(self, template_name: str, template_path_hierarchy: str = None) -> str:
        """Build hierarchical path for template output."""
        if template_path_hierarchy:
            # Use the provided hierarchy path
            hierarchy_parts = template_path_hierarchy.split(os.path.sep)
            return os.path.join(self.base_output_dir, *hierarchy_parts)
        else:
            # Use just the template name
            return os.path.join(self.base_output_dir, template_name)

    def build_file_path(self, template_name, file_type, session_id, depth_indicator,
                       template_path_hierarchy=None, extension=None):
        """
        Build file path for output using sequential numbering instead of timestamps.

        Args:
            template_name: Name of the template
            file_type: Type of file (history, response, etc.)
            session_id: Session identifier
            depth_indicator: Depth indicator (a, b, c, etc.)
            template_path_hierarchy: Optional path hierarchy for nested templates
            extension: Optional file extension (defaults to .txt or template extension for system files)

        Returns:
            Complete file path for the output file
        """
        # Determine template directory based on hierarchy
        template_dir = self.build_hierarchy_path(template_name, template_path_hierarchy)
        os.makedirs(template_dir, exist_ok=True)

        # Get the next sequence number for this template
        seq_number = self._get_next_sequence_number(template_dir, template_name, depth_indicator)

        # Determine file extension
        if not extension:
            if file_type in ['system_instructions_raw', 'system_enhanced_prompt']:
                # Use template extension for system files if available
                extension = os.path.splitext(template_name)[1] if os.path.splitext(template_name)[1] else '.txt'
            else:
                extension = self.config['file_types'].get(file_type, {}).get('extension', '.txt')

        # Build filename: TemplateName_SequentialNumber_DepthIndicator.FileType.Extension
        filename = f"{template_name}_{seq_number}_{depth_indicator}.{file_type}{extension}"

        return os.path.join(template_dir, filename)

    def write_output(self, content, template_name, file_type, session_id, depth_indicator,
                    template_path_hierarchy=None, extension=None, mode='w'):
        """
        Write output to file with content deduplication.

        If content already exists in registry, creates a symlink instead of duplicate file.
        """
        # Build file path
        file_path = self.build_file_path(
            template_name=template_name,
            file_type=file_type,
            session_id=session_id,
            depth_indicator=depth_indicator,
            template_path_hierarchy=template_path_hierarchy,
            extension=extension
        )

        # Store in registry and get content hash
        content_hash = self.registry.store_content(file_path, content)

        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Write the content to file
            with open(file_path, mode, encoding='utf-8') as f:
                f.write(content)

            return file_path
        except Exception as e:
            logger.error(f"Error writing to {file_path}: {str(e)}")
            return None

    def format_history_block(self, provider, model_name, template_filename,
                           template_input, response_text, system_instructions=""):
        """Format the history block with metadata and input/output."""
        seq_number = os.path.basename(template_filename).split('_')[1] if '_' in os.path.basename(template_filename) else "XXX"
        depth_indicator = os.path.basename(template_filename).split('_')[2].split('.')[0] if '_' in os.path.basename(template_filename) else "X"

        formatted_block = (
            f"=== INTERACTION {seq_number}-{depth_indicator} ===\n"
            f"Provider: {provider}\n"
            f"Model: {model_name}\n"
            f"Template: {os.path.basename(template_filename)}\n\n"
            f"User Prompt:\n{template_input}\n\n"
            f"System Instructions:\n{system_instructions}\n\n"
            f"Response:\n{response_text}\n\n"
            f"{'=' * 40}\n\n"
        )
        return formatted_block

    def write_interaction_outputs(self, provider, model_name, template_name,
                                template_input, response_text, session_id,
                                depth_indicator, template_path_hierarchy=None,
                                template_extension='.xml', metadata=None):
        """
        Write all interaction outputs using sequential numbering for files.

        Args:
            provider: LLM provider name
            model_name: Model name
            template_name: Template name
            template_input: User input
            response_text: LLM response
            session_id: Session ID
            depth_indicator: Depth indicator (a, b, c, etc.)
            template_path_hierarchy: Optional path hierarchy for nested templates
            template_extension: Extension of the template file
            metadata: Optional metadata dictionary with additional outputs
        """
        metadata = metadata or {}
        system_message = metadata.get('system_message', '')

        # Template extension without the dot
        template_ext = template_extension

        # Write standard outputs (always using .txt extension)
        self.write_output(
            template_input,
            template_name,
            'user_prompt',
            session_id,
            depth_indicator,
            template_path_hierarchy
        )

        self.write_output(
            template_name,
            template_name,
            'template_name',
            session_id,
            depth_indicator,
            template_path_hierarchy
        )

        self.write_output(
            response_text,
            template_name,
            'response',
            session_id,
            depth_indicator,
            template_path_hierarchy
        )

        self.write_output(
            system_message,
            template_name,
            'system_message',
            session_id,
            depth_indicator,
            template_path_hierarchy
        )

        # Format and write history
        history_content = self.format_history_block(
            provider,
            model_name,
            template_name,
            template_input,
            response_text,
            system_instructions=system_message
        )

        self.write_output(
            history_content,
            template_name,
            'history',
            session_id,
            depth_indicator,
            template_path_hierarchy
        )

        # Write metadata-based outputs with special handling for system files
        for metadata_key, output_type in self.config['metadata_mapping'].items():
            if metadata_key in metadata and metadata[metadata_key]:
                # Use template extension for system files
                ext = template_ext if output_type in ['system_instructions_raw', 'system_enhanced_prompt'] else '.txt'
                self.write_output(
                    metadata[metadata_key],
                    template_name,
                    output_type,
                    session_id,
                    depth_indicator,
                    template_path_hierarchy,
                    extension=ext
                )

    def get_available_templates(self, level_path=None) -> List[str]:
        """Get available templates at the specified hierarchy level."""
        instruction_templates_dir = os.path.join(
            os.path.dirname(os.path.abspath(sys.argv[0])),
            "instruction_templates"
        )

        if level_path:
            # Get subdirectories of the specified path
            search_path = os.path.join(instruction_templates_dir, level_path)
            if os.path.isdir(search_path):
                return [os.path.join(level_path, d) for d in os.listdir(search_path)
                       if os.path.isdir(os.path.join(search_path, d))]
        else:
            # Get root level templates/directories
            return [d for d in os.listdir(instruction_templates_dir)
                   if os.path.isdir(os.path.join(instruction_templates_dir, d))]

        return []

    def get_system_instructions(self, template_path: str) -> List[str]:
        """Get available system instructions for a template."""
        instruction_templates_dir = os.path.join(
            os.path.dirname(os.path.abspath(sys.argv[0])),
            "instruction_templates"
        )

        template_dir = os.path.join(instruction_templates_dir, template_path)
        if os.path.isdir(template_dir):
            # Get all XML files in the template directory
            return [f for f in os.listdir(template_dir)
                   if f.endswith(".xml") and os.path.isfile(os.path.join(template_dir, f))]

        return []

# ========================================================
# 2. Low-Level I/O Communicator
# ========================================================
class LowestLevelCommunicator:
    """
    Records raw request and response streams, preserving the entire stream
    before any filtering or transformation.
    """
    def __init__(self):
        self.raw_interactions = []
        self.breadcrumb_manager = BreadcrumbManager()

    def record_api_request(self, provider: str, model_name: str, messages: List[Dict[str, str]], metadata: dict = None):
        timestamp = datetime.now().strftime("%Y.%m.%d %H:%M:%S")
        self.raw_interactions.append({
            "direction": "request",
            "provider": provider,
            "model_name": model_name,
            "messages": messages,
            "timestamp": timestamp,
            "metadata": metadata or {},
        })

    def record_api_response(self, provider: str, model_name: str, response_text: str, metadata: dict = None):
        timestamp = datetime.now().strftime("%Y.%m.%d %H:%M:%S")
        self.raw_interactions.append({
            "direction": "response",
            "provider": provider,
            "model_name": model_name,
            "content": response_text,
            "timestamp": timestamp,
            "metadata": metadata or {},
        })
        # Stream output to files
        self._stream_output(provider, model_name, response_text, metadata or {})

    def _stream_output(self, provider: str, model_name: str, response_text: str, metadata: dict):
        """
        Centralized method to handle streaming outputs using BreadcrumbManager.
        """
        template_name = metadata.get('template_name', 'default_template')
        session_id = metadata.get('session_id', '001')
        depth_indicator = metadata.get('depth_indicator', 'a')
        template_path_hierarchy = metadata.get('template_path_hierarchy', template_name)
        template_extension = metadata.get('template_extension', '.txt')

        self.breadcrumb_manager.write_interaction_outputs(
            provider=provider,
            model_name=model_name,
            template_name=template_name,
            template_input=metadata.get('template_input', ''),
            response_text=response_text,
            session_id=session_id,
            depth_indicator=depth_indicator,
            template_path_hierarchy=template_path_hierarchy,
            template_extension=template_extension,
            metadata={
                'system_message': metadata.get('system_message', ''),
                'raw_instructions': metadata.get('raw_instructions', ''),
                'enhanced_prompt': metadata.get('enhanced_prompt', '')
            }
        )

    def get_interaction_history(self) -> List[Dict]:
        return self.raw_interactions

    def format_interaction_log(self) -> str:
        """Format the interaction log for display."""
        formatted_parts = []
        for interaction in self.raw_interactions:
            direction = interaction["direction"].upper()
            provider = interaction["provider"]
            model_name = interaction["model_name"]
            timestamp = interaction["timestamp"]

            if direction == "REQUEST":
                messages = interaction.get("messages", [])
                formatted_content = "\n".join([
                    f"{msg.get('role', 'unknown')}: {msg.get('content', '')}"
                    for msg in messages
                ])
            else:  # RESPONSE
                formatted_content = interaction.get("content", "")

            formatted_parts.append(
                f"[{timestamp}] {direction} - {provider}.{model_name}\n"
                f"{'=' * 40}\n"
                f"{formatted_content}\n"
                f"{'=' * 40}\n"
            )

        return "\n".join(formatted_parts)


# ========================================================
# 3. LLM Interactions
# ========================================================
class LLMInteractions:
    """
    Communicates with the chosen LLM provider via LangChain, logging requests/responses.
    """

    LANGCHAIN_CLIENTS = {
        Config.PROVIDER_OPENAI: ChatOpenAI,
        Config.PROVIDER_ANTHROPIC: ChatAnthropic,
        Config.PROVIDER_GOOGLE: ChatGoogleGenerativeAI,
        Config.PROVIDER_DEEPSEEK: lambda **kwargs: ChatOpenAI(base_url="https://api.deepseek.com", **kwargs),
    }

    def __init__(self, api_key=None, model_name=None, provider=None):
        self.config = Config()
        self.provider = provider or self.config.provider
        self.model_name = model_name or self.config.model_params["model_name"]
        self.communicator = LowestLevelCommunicator()
        self.client = self._initialize_llm_client(api_key)

    def _initialize_llm_client(self, api_key=None):
        api_key_env = Config.API_KEY_ENV_VARS.get(self.provider)
        used_api_key = api_key or os.getenv(api_key_env)
        client_class = self.LANGCHAIN_CLIENTS.get(self.provider)
        if not client_class:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")
        return client_class(api_key=used_api_key, model=self.model_name)

    def request_llm_response(self, messages, model_name=None, metadata=None):
        """
        Sends a request to the LLM, records request/response, returns raw text.
        """
        used_model = model_name or self.model_name
        if metadata is None:
            metadata = {}

        self.communicator.record_api_request(self.provider, used_model, messages, metadata)
        try:
            prompt = "\n".join(msg["content"] for msg in messages if msg["role"] in ["system", "user"])
            response = self.client.invoke(prompt)
            raw_text = response.content
            self.communicator.record_api_response(self.provider, used_model, raw_text, metadata)
            return raw_text
        except Exception as exc:
            logger.error(f"Error calling {self.provider}.{used_model}: {exc}")
            return None



# ========================================================
# 4. Template File Manager
# ========================================================
class TemplateFileManager:

    ALLOWED_FILE_EXTS = (".xml", ".py", ".json", ".md", ".txt")
    EXCLUDED_FILE_NAMES = ["ignore_me", "do_not_load", "temp_file"]
    EXCLUDED_FILE_PATHS = ["\\_md\\"]
    EXCLUDED_PATTERNS = [r".*\.\d{2}\.\d{2}.*"]
    MAX_TEMPLATE_SIZE_KB = 100
    REQUIRED_CONTENT_MARKERS = ["<template>", "[INPUT_PROMPT]"]

    def __init__(self):
        self.template_dir = os.getcwd()
        self.template_cache = {}

    def refresh_template_cache(self):
        """ Clears and reloads the template cache by scanning the working directory. """
        self.template_cache.clear()
        pattern = os.path.join(self.template_dir, "**", "*.*")
        for filepath in glob.glob(pattern, recursive=True):
            name = os.path.splitext(os.path.basename(filepath))[0]
            if self.validate_template(filepath):
                self.template_cache[name] = filepath

    def validate_template(self, filepath):
        _, ext = os.path.splitext(filepath)
        filename = os.path.basename(filepath)
        basename, _ = os.path.splitext(filename)
        filepath_lower = filepath.lower()

        if ext.lower() not in self.ALLOWED_FILE_EXTS:
            return False
        if basename in self.EXCLUDED_FILE_NAMES:
            return False
        if any(excluded in filepath_lower for excluded in self.EXCLUDED_FILE_PATHS):
            return False
        if any(re.match(pattern, filename, re.IGNORECASE) for pattern in self.EXCLUDED_PATTERNS):
            return False
        try:
            filesize_kb = os.path.getsize(filepath) / 1024
            if filesize_kb > self.MAX_TEMPLATE_SIZE_KB:
                return False
            with open(filepath, "r", encoding="utf-8") as f:
                content = f.read()
            if not all(marker in content for marker in self.REQUIRED_CONTENT_MARKERS):
                return False
        except Exception:
            return False

        return True

    def load_templates(self, template_name_list):
        # Preloads specified templates into the cache if found.
        for name in template_name_list:
            _ = self.find_template_path(name)

    def find_template_path(self, template_name):
        # Retrieves the template path from cache; searches if not found.
        if template_name in self.template_cache:
            return self.template_cache[template_name]
        for ext in self.ALLOWED_FILE_EXTS:
            search_pattern = os.path.join(self.template_dir, "**", f"{template_name}{ext}")
            files = glob.glob(search_pattern, recursive=True)
            if files:
                self.template_cache[template_name] = files[0]
                return files[0]
        return None

    def parse_template_content(self, template_path):
        # Reads file content, extracts placeholders, and returns structured data.
        try:
            with open(template_path, "r", encoding="utf-8") as f:
                content = f.read()
            placeholders = list(set(re.findall(r"\[(.*?)\]", content)))
            template_data = {"path": template_path, "content": content, "placeholders": placeholders}
            return template_data
        except Exception as e:
            logger.error(f"Error parsing template {template_path}: {e}")
            return {}

    def extract_placeholders(self, template_name):
        # Returns a list of placeholders found in a specific template.
        template_path = self.find_template_path(template_name)
        if not template_path:
            return []
        parsed_template = self.parse_template_content(template_path)
        if not parsed_template:
            return []
        return parsed_template.get("placeholders", [])

    def extract_template_metadata(self, template_name):
        # Extracts metadata (agent_name, version, status, description, etc.) from a template.
        template_path = self.find_template_path(template_name)
        if not template_path:
            return {}
        parsed_template = self.parse_template_content(template_path)
        if not parsed_template:
            return {}

        content = parsed_template["content"]
        metadata = {
            "agent_name": self.extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),
            "version": self.extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),
            "status": self.extract_value_from_content(content, r'<status value="([^"]*)"\s*/>'),
            "description": self.extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),
            "system_prompt": self.extract_value_from_content(content, r'<system_prompt value="([^"]*)"\s*/>')
        }
        return metadata

    def extract_value_from_content(self, content, pattern):
        match = re.search(pattern, content)
        return match.group(1) if match else None

    def list_available_templates(
        self,
        exclude_paths=None,
        exclude_names=None,
        exclude_versions=None,
        exclude_statuses=None,
        exclude_none_versions=False,
        exclude_none_statuses=False,
    ):
        """
        Lists templates filtered by various exclusion criteria.
        """
        search_pattern = os.path.join(self.template_dir, "**", "*.*")
        templates_info = {}

        for filepath in glob.glob(search_pattern, recursive=True):
            if not self.validate_template(filepath):
                continue
            template_name = os.path.splitext(os.path.basename(filepath))[0]
            parsed_template = self.parse_template_content(filepath)
            if not parsed_template:
                logger.warning(f"Skipping {filepath} due to parsing error.")
                continue
            content = parsed_template["content"]
            try:
                templates_info[template_name] = {
                    "path": filepath,
                    "name": self.extract_value_from_content(content, r'<agent_name value="([^"]*)"\s*/>'),
                    "description": self.extract_value_from_content(content, r'<description value="([^"]*)"\s*/>'),
                    "version": self.extract_value_from_content(content, r'<version value="([^"]*)"\s*/>'),
                    "status": self.extract_value_from_content(content, r'<status value="([^"]*)"\s*/>'),
                }
            except Exception as e:
                logger.error(f"Error loading template from {filepath}: {e}")

        filtered_templates = {}
        for name, info in templates_info.items():
            if (
                (not exclude_paths or info["path"] not in exclude_paths)
                and (not exclude_names or info["name"] not in exclude_names)
                and (not exclude_versions or info["version"] not in exclude_versions)
                and (not exclude_statuses or info["status"] not in exclude_statuses)
                and (not exclude_none_versions or info["version"] is not None)
                and (not exclude_none_statuses or info["status"] is not None)
            ):
                filtered_templates[name] = info

        return filtered_templates

    def prepare_template(self, template_filepath, input_prompt=""):
        """
        Reads the template file, replaces placeholders with default or dynamic values, and returns the final content.
        """
        parsed_template = self.parse_template_content(template_filepath)
        if not parsed_template:
            return None

        content = parsed_template["content"]
        placeholders = {
            "[HEADER]": f"```{os.path.splitext(template_filepath)[1]}",
            "[FILENAME]": os.path.basename(template_filepath),
            "[OUTPUT_FORMAT]": "plain_text",
            "[ORIGINAL_PROMPT_LENGTH]": str(len(input_prompt)),
            "[RESPONSE_PROMPT_LENGTH]": str(int(len(input_prompt) * 0.9)),
            "[INPUT_PROMPT]": input_prompt,
            "[ADDITIONAL_CONSTRAINTS]": "",
            "[ADDITIONAL_PROCESS_STEPS]": "",
            "[ADDITIONAL_GUIDELINES]": "",
            "[ADDITIONAL_REQUIREMENTS]": "",
            "[FOOTER]": "```",
        }

        for placeholder, value in placeholders.items():
            value_str = str(value)
            content = content.replace(placeholder, value_str)

        return [content, {"template_name": os.path.basename(template_filepath), "template_input": input_prompt}]

    def get_template_content(self, template_name): # previously missing
        """
        Retrieves the raw content of a template given its name.
        """
        template_path = self.find_template_path(template_name)
        if template_path:
            return self.parse_template_content(template_path)["content"]
        return None

    def apply_template(self, template_content, input_prompt): # previously missing
        """
        Applies the template content and input prompt by replacing placeholders.
        """
        placeholders = {
            "[INPUT_PROMPT]": input_prompt,
        }
        content = template_content
        for placeholder, value in placeholders.items():
            content = content.replace(placeholder, value)
        return content

    def extract_template_parts(self, raw_text):
        """
        Extracts specific sections from the raw template text (system_prompt, etc.).
        """
        system_prompt_match = re.search(r'<system_prompt value="([^"]*)"\s*/>', raw_text)
        system_prompt = system_prompt_match.group(1) if system_prompt_match else ""

        template_prompt_match = re.search(r"\[TEMPLATE_START\](.*?)\[TEMPLATE_END\]", raw_text, re.DOTALL)
        template_prompt = template_prompt_match.group(1) if template_prompt_match else raw_text

        return system_prompt, template_prompt

# ========================================================
# 1.4 Content Registry
# ========================================================
class ContentRegistry:
    """
    Manages content deduplication through hashing and maintains a global mapping table.
    """
    def __init__(self, registry_dir="registry"):
        self.registry_dir = registry_dir
        self.hashes_dir = os.path.join(registry_dir, "hashes")
        self.mapping_file = os.path.join(registry_dir, "mapping.json")
        self._ensure_dirs()
        self.mapping = self._load_mapping()

    def _ensure_dirs(self):
        """Create registry directories if they don't exist."""
        os.makedirs(self.hashes_dir, exist_ok=True)

    def _load_mapping(self) -> dict:
        """Load the mapping table from disk."""
        try:
            with open(self.mapping_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}

    def _save_mapping(self):
        """Save the current mapping table to disk."""
        with open(self.mapping_file, 'w', encoding='utf-8') as f:
            json.dump(self.mapping, f, indent=2)

    def _hash_content(self, content: str) -> str:
        """Generate a hash for the given content."""
        import hashlib
        return hashlib.sha256(content.encode('utf-8')).hexdigest()

    def store_content(self, breadcrumb_path: str, content: str) -> str:
        """
        Store content and return its hash. If content already exists,
        return existing hash and update mapping.
        """
        content_hash = self._hash_content(content)
        hash_path = os.path.join(self.hashes_dir, f"{content_hash}.txt")

        # Store content if it doesn't exist
        if not os.path.exists(hash_path):
            with open(hash_path, 'w', encoding='utf-8') as f:
                f.write(content)

        # Update mapping
        self.mapping[breadcrumb_path] = content_hash
        self._save_mapping()

        return content_hash

    def get_content(self, breadcrumb_path: str) -> Optional[str]:
        """Retrieve content for a given breadcrumb path."""
        content_hash = self.mapping.get(breadcrumb_path)
        if not content_hash:
            return None

        hash_path = os.path.join(self.hashes_dir, f"{content_hash}.txt")
        try:
            with open(hash_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            return None

    def get_hash(self, breadcrumb_path: str) -> Optional[str]:
        """Get the hash for a given breadcrumb path."""
        return self.mapping.get(breadcrumb_path)

    def list_similar_content(self, content: str, threshold=0.9) -> List[str]:
        """Find breadcrumb paths with similar content."""
        from difflib import SequenceMatcher
        content_hash = self._hash_content(content)
        similar_paths = []

        for path, hash_value in self.mapping.items():
            if hash_value == content_hash:
                similar_paths.append(path)
                continue

            stored_content = self.get_content(path)
            if stored_content:
                similarity = SequenceMatcher(None, content, stored_content).ratio()
                if similarity >= threshold:
                    similar_paths.append(path)

        return similar_paths

# ========================================================
# 5. Prompt Refinement Engine
# ========================================================
class RefinementWorkflow:
    """
    Core engine for refining prompts using templates and LLMs.
    """
    def __init__(self, llm_interactions: LLMInteractions, template_manager: TemplateFileManager):
        self.llm_interactions = llm_interactions
        self.template_manager = template_manager
        self.breadcrumb_manager = BreadcrumbManager()

    def run_template(self, template_name: str, input_prompt: str, session_id=None, depth_indicator=None, template_path_hierarchy=None) -> str:
        """
        Executes a single template refinement step.
        """
        template_path = self.template_manager.find_template_path(template_name)
        if template_path is None:
            logger.error(f"Template '{template_name}' not found.")
            return input_prompt

        template_content = self.template_manager.get_template_content(template_name)
        if template_content is None:
            logger.error(f"Template content for '{template_name}' could not be loaded.")
            return input_prompt

        instructions, metadata = self.template_manager.prepare_template(template_path, input_prompt)
        instructions = instructions if instructions else template_content
        enhanced_prompt = self.template_manager.apply_template(instructions, input_prompt)

        system_prompt_text, agent_instructions = self.template_manager.extract_template_parts(template_content)
        messages = [
            {"role": "system", "content": system_prompt_text.strip()},
            {"role": "user", "content": agent_instructions.replace("[INPUT_PROMPT]", enhanced_prompt).strip()}
        ]

        template_extension = os.path.splitext(template_path)[1]

        # Enhanced metadata with all necessary information
        metadata.update({
            "template_name": template_name,
            "template_input": input_prompt,
            "session_id": session_id,
            "depth_indicator": depth_indicator,
            "template_path_hierarchy": template_path_hierarchy,
            "template_extension": template_extension,
            "system_message": system_prompt_text,
            "raw_instructions": template_content,
            "enhanced_prompt": enhanced_prompt
        })

        response_text = self.llm_interactions.request_llm_response(
            messages=messages,
            metadata=metadata
        )

        return response_text if response_text else input_prompt

    def run_refinement_recipe(self, recipe: List[Dict], initial_prompt: str, session_id=None) -> Union[str, Dict]:
        """
        Executes a sequence of template refinements as defined in the recipe, including aggregator chains,
        creating hierarchical output.
        """
        current_prompt = initial_prompt
        depth_counter = 0
        template_hierarchy_path = ""

        for step_config in recipe:
            chain = step_config.get("chain", [])
            repeats = step_config.get("repeats", 1)
            aggregator_chain = step_config.get("aggregator_chain", [])

            # Process regular chain
            for template_name in chain:
                depth_counter += 1
                depth_indicator_char = chr(ord('a') + depth_counter - 1)
                template_hierarchy_path = os.path.join(template_hierarchy_path, template_name)

                for _ in range(repeats):
                    current_prompt = self.run_template(
                        template_name,
                        current_prompt,
                        session_id=session_id,
                        depth_indicator=depth_indicator_char,
                        template_path_hierarchy=template_hierarchy_path
                    )

            # Process aggregator chain if it exists
            if aggregator_chain:
                aggregator_input_prompt = current_prompt # Use current prompt as input for aggregator
                aggregator_hierarchy_path = template_hierarchy_path # Aggregator is within the same hierarchy level

                for aggregator_template_name in aggregator_chain:
                    depth_counter += 1 # Increment depth for aggregator template
                    depth_indicator_char = chr(ord('a') + depth_counter - 1) # New depth indicator for aggregator
                    aggregator_hierarchy_path = os.path.join(aggregator_hierarchy_path, aggregator_template_name) # Hierarchy for aggregator

                    current_prompt = self.run_template(
                        aggregator_template_name,
                        aggregator_input_prompt, # Use aggregator input prompt
                        session_id=session_id,
                        depth_indicator=depth_indicator_char, # Depth indicator for aggregator step
                        template_path_hierarchy=aggregator_hierarchy_path # Hierarchy path for aggregator
                    )
                    aggregator_input_prompt = current_prompt # Output of aggregator step becomes input for next aggregator step

        return current_prompt


# ========================================================
# 6. Main Execution Logic
# ========================================================
class Execution:
    def __init__(self, config_path="config.json"):
        self.recipe_config = RecipeConfig(config_path)
        self.config = Config()
        self._initialize_components()
        self.session_counter = 0

    def _initialize_components(self):
        """Initialize all components with proper configuration."""
        provider = os.getenv("LLM_PROVIDER", self.recipe_config.get_default_provider()).lower()
        self.config.provider = provider
        self.llm_interactions = LLMInteractions(provider=provider)
        self.template_manager = TemplateFileManager()
        self.refinement_engine = RefinementWorkflow(self.llm_interactions, self.template_manager)
        self.breadcrumb_manager = BreadcrumbManager()

        # Configure logging
        log_config = self.recipe_config.get_logging_config()
        self.config.verbosity = log_config.get('verbosity', 'low')

        # Ensure LLM provider and model are properly set
        self.llm_interactions.provider = provider
        self.llm_interactions.model_name = self.config.DEFAULT_MODEL_PARAMS[provider]["model_name"]

    def _select_from_list(self, items: List[str], prompt: str) -> Optional[str]:
        """Display numbered list and get user selection."""
        if not items:
            print("No items available.")
            return None

        print(f"\n{prompt}")
        for i, item in enumerate(items, 1):
            print(f"{i}. {item}")

        while True:
            try:
                choice = input("\nEnter number (or 'q' to quit): ").strip()
                if choice.lower() == 'q':
                    return None
                idx = int(choice) - 1
                if 0 <= idx < len(items):
                    return items[idx]
                print("Invalid selection. Try again.")
            except ValueError:
                print("Please enter a number or 'q' to quit.")

    def run_interactive(self):
        """Run in interactive mode with template selection."""
        self.template_manager.refresh_template_cache()
        self.session_counter += 1
        session_id = f"{self.session_counter:01d}"
        current_level = None

        while True:
            # Get available templates at current level
            templates = self.breadcrumb_manager.get_available_templates(current_level)

            if not current_level:
                # Root level template selection
                template = self._select_from_list(
                    templates,
                    "Select a root template:"
                )
                if not template:
                    break
                current_level = template
            else:
                # Get system instructions for current template
                instructions = self.breadcrumb_manager.get_system_instructions(current_level)
                instruction = self._select_from_list(
                    instructions,
                    f"Select system instruction for {current_level}:"
                )
                if not instruction:
                    current_level = None  # Go back to root
                    continue

                # Get user input
                user_input = input("\nEnter your prompt (or 'q' to go back): ").strip()
                if user_input.lower() == 'q':
                    current_level = None
                    continue

                # Process the interaction
                depth_indicator = chr(ord('a') + len(current_level.split(os.path.sep)) - 1)
                response = self.refinement_engine.run_template(
                    template_name=os.path.basename(current_level),
                    input_prompt=user_input,
                    session_id=session_id,
                    depth_indicator=depth_indicator,
                    template_path_hierarchy=current_level
                )

                # Display response
                print("\nResponse:")
                print("=" * 40)
                print(response)
                print("=" * 40)

                # Ask to save to history
                save = input("\nSave to history? (y/n): ").strip().lower()
                if save == 'y':
                    self.breadcrumb_manager.write_interaction_outputs(
                        provider=self.llm_interactions.provider,
                        model_name=self.llm_interactions.model_name,
                        template_name=os.path.basename(current_level),
                        template_input=user_input,
                        response_text=response,
                        session_id=session_id,
                        depth_indicator=depth_indicator,
                        template_path_hierarchy=current_level
                    )
                    print("Interaction saved.")

                # Ask to continue with current template
                cont = input("\nContinue with current template? (y/n): ").strip().lower()
                if cont != 'y':
                    current_level = None

    def run(self, interactive=False):
        """Main execution method."""
        if interactive:
            self.run_interactive()
        else:
            # Standard recipe execution
            self.template_manager.refresh_template_cache()
            self.session_counter += 1
            session_id = f"{self.session_counter:01d}"

            initial_prompt = self.recipe_config.get_initial_prompt()
            recipe_steps = self.recipe_config.get_recipe_steps()

            recipe_result = self.refinement_engine.run_refinement_recipe(
                recipe=recipe_steps,
                initial_prompt=initial_prompt,
                session_id=session_id
            )

            logger.info(f"Refinement process completed. Final Output: {recipe_result}")
            print("\n=== Initial Input (Raw I/O) ===")
            print(f'\n"""{initial_prompt}"""\n')
            print("\n=== Full Communicator Log (Raw I/O) ===")
            print(self.llm_interactions.communicator.format_interaction_log())

# ========================================================
# 7. Script Entry Point
# ========================================================
if __name__ == "__main__":
    try:
        # Initialize execution with config
        runner = Execution("config.json")

        # Run the refinement process
        runner.run()
    except Exception as e:
        logger.error(f"Error during execution: {e}")
        sys.exit(1)


## Message 5

A *single* high‑impact change is to **extract all of those enormous hardcoded dictionaries (e.g. `DEFAULT_MODEL_PARAMS`, `SUPPORTED_MODELS`) and other big config blocks into one or more external JSON/YAML files** and then load them at runtime. In other words, **stop embedding large provider/model configs directly into the code**. 

Why this helps:

1. **Major code reduction:** Those multi‑screen dictionaries in `Config` (and elsewhere) account for a large fraction of the code. Shifting them into external files immediately slashes the Python file size (often by hundreds of lines).  

2. **Cleaner, more maintainable logic:** Instead of scrolling through huge constant blocks in your Python file, you cleanly separate “configuration/data” from “behavior/logic.” This makes the code easier to navigate, and you won’t have to re‑deploy code just to tweak a model or provider.

3. **Retains current functionality:** You still load the same data, only now from structured config files. The overall application flow and logic can remain exactly the same.

---

### Example Approach

1. **Create a new file** (e.g., `model_configs.json`) to hold all the `DEFAULT_MODEL_PARAMS` and `SUPPORTED_MODELS` values. For instance:

   ```json
   {
     "providers": {
       "anthropic": {
         "default_model_params": {
           "model_name": "claude-3-haiku-********"
         },
         "supported_models": {
           "claude-3-haiku-********": {
             "pricing": "0.80/4.00",
             "description": "Claude 3 Haiku"
           },
           ...
         }
       },
       "deepseek": {
         "default_model_params": {
           "model_name": "deepseek-chat"
         },
         "supported_models": {
           "deepseek-chat": {
             "pricing": "0.14/0.28",
             "description": "DeepSeek Chat model"
           },
           ...
         }
       },
       ...
     }
   }
   ```

2. **Load that file** in `Config.__init__` (or wherever you first need these dictionaries):

   ```python
   def __init__(self, model_config_path="model_configs.json"):
       load_dotenv()
       self.enable_utf8_encoding()

       # Instead of large dicts, just read from JSON
       with open(model_config_path, "r", encoding="utf-8") as f:
           model_data = json.load(f)

       # Example of reading your provider’s default
       self.provider = os.getenv("LLM_PROVIDER", self.DEFAULT_PROVIDER).lower()
       self.model_params = model_data["providers"][self.provider]["default_model_params"]

       self.supported_models = model_data["providers"][self.provider]["supported_models"]
       ...
   ```

3. **Remove** all those giant dictionaries from the Python file, and keep just minimal code that references the externally loaded data. 

---

That’s it. You’ve **materially shrunk the code** while keeping the same logic and feature set. From a maintenance perspective, you now have a *much* simpler Python script, and any future updates to providers/models/pricing/descriptions can be done in your JSON/YAML file—no big code diffs required.

## Message 6

**Simplifying complexity**

I'm digging into the user's request to reduce unnecessary complexity and code volume. The code includes multiple classes such as Config, RecipeConfig, BreadcrumbManager, and Execution. Separating them into modules for individual testing might help.

**Streamlining classes and configs**

I’m thinking about merging singleton classes or eliminating redundant config classes. Also, I’m noting the potential of storing large dictionaries in JSON files to reduce code size.

**Evaluating library options**

I'm mapping out the removal of a custom logger with YAML sink in favor of Loguru and examining the possibility of streamlining the "lowest-level communicator" and "BreadcrumbManager" with existing libraries.

**Refactoring code**

I’m figuring out how to streamline repetitive environment variables and config data by consolidating them into a single dictionary. This should reduce verbosity and enhance clarity.

**Tackling aggregator complexity**

I'm piecing together the challenge of simplifying aggregation code and removing redundant logic. Considering the impact on overall functionality and code flow helps refine the approach effectively.

**Consolidating classes**

I'm thinking through combining singular classes like "RefinementWorkflow" and "Execution" to streamline code. Plus, shifting large model config dictionaries to a dynamic JSON file might further reduce complexity.

## Message 7

A single change that slashes the code size (without dropping features) is to **move all of the large hard-coded model/provider dictionaries and configuration data into a separate JSON or YAML file** and then load that file at runtime. Right now, those huge dictionaries (e.g. `SUPPORTED_MODELS`, `DEFAULT_MODEL_PARAMS`) account for a massive chunk of the script, yet they are basically just data rather than logic. Offloading them into config files drastically cuts down your Python code while preserving the full range of providers, models, and parameters.

## Message 8

**Streamlining the code**

I’m focusing on reducing code complexity by merging classes, removing duplication, and adopting single context objects for shared references.

**Exploring optimization**

I'm examining various strategies to streamline the code, such as removing unnecessary placeholders and merging classes like "BreadcrumbManager" and "ContentRegistry" for more efficient log deduplication.

**Identifying large-scale optimization**

I’m focusing on reducing complexity by merging redundant classes like "BreadcrumbManager" and "ContentRegistry," removing excessive configurations and dictionaries, and potentially unifying the entire code into fewer classes.

**Reimagining model definitions**

I’m considering using a separate JSON or YAML file to store large model definition dictionaries, which can be read at runtime, to keep the codebase simpler while preserving functionality.
