# RigOfficeDownloader README Merge

Conversation ID: 682af61e-23c4-8008-8796-e419609a1679

## Message 1

Please create a single README.md based on taking the best from each part of these variations:



    # Dir `readme_variations`



    ### File Structure



    ```

    ├── README_v1.md

    ├── README_v2.md

    ├── README_v3.md

    ├── README_v4.md

    ├── README_v5.md

    ├── README_v6.md

    └── README_v7.md

    ```



    ---



    #### `README_v1.md`



    ```markdown

        # RigOfficeDownloader

        

        ## Overview

        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.

        

        ## Key Features

        - Three-stage workflow: Documents -> Files -> Downloads

        - Interactive menu for flexible execution

        - User control points via Markdown interfaces

        - Smart file organization with subfolder support

        - Configurable filter chains

        

        ## Setup & Usage

        1. Run `py_venv_init.bat` to create the Python environment

        2. Run `RigOfficeDownloader-v4.bat` to start the application

        3. Use the interactive menu to configure and execute workflow steps

        

        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Preserves document context and relationships

        

        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser (for Selenium automation)

    ```



    ---



    #### `README_v2.md`



    ```markdown

        # RigOfficeDownloader

        

        ## The Problem

        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:

        - Tedious and repetitive

        - Error-prone

        - A poor use of skilled engineering time

        

        ## The Solution

        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:

        1. **Document Retrieval**: Automatically scrapes document metadata

        2. **File Metadata**: Fetches file information for selected documents

        3. **Smart Downloads**: Downloads files with intelligent naming and organization

        

        ## How It Works

        - Uses Selenium to automate web interactions with RigDoc

        - Exports data to Markdown for user review and selection

        - Applies configurable filters to pre-select relevant documents

        - Organizes downloads with consistent naming patterns

        

        ## Getting Started

        1. Run `py_venv_init.bat` to set up the environment

        2. Run `RigOfficeDownloader-v4.bat` to launch the application

        3. Follow the interactive menu prompts

        

        ## Impact

        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.

    ```



    ---



    #### `README_v3.md`



    ```markdown

        # RigOfficeDownloader

        

        ## Technical Overview

        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.

        

        ## Architecture

        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown

        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads

        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support

        

        ## Workflow Steps

        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information

        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing

        3. **Selection Import**: `md_table_to_json()` imports user selections

        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents

        5. **File Selection**: Export/import cycle for user selection of files

        6. **Download Process**: `download_files()` retrieves selected files with smart naming

        

        ## Filter Chain System

        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.

        

        ## Setup Instructions

        1. Run `py_venv_init.bat` to initialize the Python environment

        2. Run `RigOfficeDownloader-v4.bat` to execute the application

        

        ## Version History

        - v1: Basic document retrieval and download

        - v2: JSON/Markdown conversion and user selection

        - v3: Improved error handling and field organization

        - v4: Subfolder support, filter chains, field ordering

    ```



    ---



    #### `README_v4.md`



    ```markdown

        # RigOfficeDownloader

        > Automate document retrieval from NOV's RigDoc system

        

        ## What This Tool Does

        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.

        

        ## Quick Start Guide

        1. **Setup**: Run `py_venv_init.bat` to create the Python environment

        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application

        3. **Configure**: Enter your rig number and search URLs when prompted

        4. **Run**: Follow the numbered menu to execute each step of the workflow

        

        ## Workflow Steps Explained

        0. **Change Parameters**: Update rig number and search URLs

        1. **Configure Filters**: Set up automatic document/file selection rules

        2. **Fetch Documents**: Retrieve document metadata from RigDoc

        3. **Review Documents**: Edit the Markdown file to select which documents to process

        4. **Import Selections**: Load your document selections

        5. **Fetch Files**: Get file metadata for selected documents

        6. **Review Files**: Edit the Markdown file to select which files to download

        7. **Import File Selections**: Load your file selections

        8. **Download Files**: Retrieve the selected files

        

        ## Tips for Success

        - Use filters to automatically pre-select relevant documents

        - Review the Markdown files carefully before proceeding to the next step

        - Files will be organized in subfolders based on '/' in their generated names

        

        ## Need Help?

        Check the source code comments for detailed information about each function and workflow step.

    ```



    ---



    #### `README_v5.md`



    ```markdown

        # RigOfficeDownloader

        

        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.

        

        ```

        Documents -> Files -> Downloads

        ```

        

        ## Features

        - Three-stage workflow with user control points

        - Interactive menu for flexible execution

        - Configurable filter chains for automatic selection

        - Smart file organization with subfolder support

        - Markdown interfaces for document/file review

        

        ## Quick Start

        ```

        1. Run py_venv_init.bat

        2. Run RigOfficeDownloader-v4.bat

        3. Follow the interactive menu

        ```

        

        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Handles errors gracefully

        

        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser

    ```



    ---



    #### `README_v6.md`



    ```markdown

        # RigOfficeDownloader

        

        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.

        

        ## Key Features

        - **Three-Stage Workflow**: Document selection → File selection → Download

        - **Metadata Preservation**: Structured naming with revision/case numbers

        - **Interactive Review**: Markdown tables for manual inclusion flags

        - **Smart Organization**: Automatic subfolder creation via naming patterns

        - **Filter System**: Pattern-based inclusion/exclusion rules

        - **Browser Automation**: Smart waiting strategies and session management

        

        ## Workflow Process

        ```python

        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json

        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true

        3. Import Selections     # Update JSON with user choices

        4. Fetch Files           # Get file listings → <rig>-b-files.json

        5. Export Files MD       # <rig>-b-files.md - Set item_download=true

        6. Import File Choices   # Update file selections

        7. Download Files        # Auto-organized with subfolder support

        ```

        

        ## Configuration (CONFIG Section)

        ```python

        {

            "rig_number": "R0000.020",  # Target rig ID

            "search_urls": [            # Predefined search templates

                "https://rigdoc.nov.com/search/rigsearch?q=...",

                "https://rigdoc.nov.com/search/rigsearch?q=..."

            ],

            "filters": [                # Sequential processing rules

                {

                    "type": "docs",     # Apply to documents/files

                    "pattern": "*DRILL*FLOOR*",  # Glob-style matching

                    "field": "item_include",     # Field to modify

                    "value": True       # Set True/False based on match

                }

            ]

        }

        ```

        

        ## Setup & Usage

        ```bash

        # Initialize environment

        py_venv_init.bat

        py_venv_pip_install.bat

        

        # Run modes

        RigOfficeDownloader-v4.py [rig_number] [mode]

        

        Modes:

        --auto         # Full automation

        --interactive  # Step-by-step control

        --config       # Modify search templates/filters

        ```

        

        ## Key Configuration Patterns

        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)

        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)

        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern

        

        ## Requirements

        - Chrome Browser + ChromeDriver

        - Active RigDoc credentials

        - Python 3.8+ with dependencies from requirements.txt

        - Network access to rigdoc.nov.com

        

        ## Advanced Features

        - **Path Sanitization**: Auto-clean special chars while preserving /

        - **Deduplication**: Hash-based conflict resolution

        - **Field Ordering**: Customizable JSON/Markdown columns

        - **Smart Scrolling**: Progressive page loading detection

        

        > Reduces documentation prep time by 60-75% compared to manual retrieval

        > Version 4.0 | Active development with subfolder support

    ```



    ---



    #### `README_v7.md`



    ```markdown

        

        ## Overview

        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.

        - Use case: curate, review, and batch-download rig-related documents and technical files.

        

        ## Directory Structure

        

        * `outputs/` (BASE\_OUTPUT): All results stored here

        * `outputs/data/` (DATA\_DIR): Document and file metadata (JSON/MD)

        * `outputs/downloads/` (DL\_DIR): Downloaded PDF and file outputs

        

        ## Pipeline Overview

        

        1. Change search parameters (rig number, URLs)

        2. Configure filter chain (add, edit, delete, toggle, reorder filters)

        3. Fetch docs (scrape data from rigdoc.nov.com)

        4. Export docs to Markdown (for selection/editing)

        5. Import docs from Markdown (sync edited selection)

        6. Fetch candidate files linked to selected docs

        7. Export file list to Markdown (for editing/selecting files for download)

        8. Import updated file list from Markdown

        9. Download marked files (PDFs only, via Chrome)

        

        ## Manual Editing

        

        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing

        * Set `item_include` (docs) and `item_download` (files) fields

        

        ## Running the Tool

        

        ```bash

        python rigdocscraper.py

        ```

        

        * Interactive menu enables step selection (numbers/comma/space-separated)

        * Supports adjusting parameters, filter configuration, and reviewing batch steps

        * Prompts will guide through editing, import/export, and download procedures

        

        ## Troubleshooting

        

        * Requires functioning Chrome installation; verify webdriver-manager compatibility

        * Common issues: browser launch failures, login/captcha requirements, file permissions

        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress

    ```




## Message 2

**Synthesizing information**

I’m crafting a unified README.md by merging the best elements from several variations, including problem statement, solution, workflow, setup, key features, architecture, advanced features, and troubleshooting.

**Synthesizing and detailing**

I’m refining the README.md to ensure it’s comprehensive yet concise, incorporating key features, architecture, data flow, quick start, and configuration details to enhance clarity and usability.

**Pulling together**

Piecing together directory structure, configuration, requirements, troubleshooting, benefits, and version history to craft a detailed and cohesive README.md.

## Message 3

```markdown
# RigOfficeDownloader
> Automate document retrieval from NOV’s RigDoc system, reducing repetitive manual work and letting engineers focus on higher-value tasks.

---

## 1. The Problem
Engineers often waste significant time navigating RigDoc to locate and gather technical documentation before they can start critical 3D modeling or design tasks. This manual process is repetitive, error-prone, and detracts from core engineering work.

## 2. The Solution
RigOfficeDownloader addresses this inefficiency by automating the entire document retrieval process:
1. **Document Gathering**: Scrape and filter relevant documentation metadata.
2. **File Selection**: Determine which files merit download through configurable rules and user review.
3. **Smart Downloads**: Automatically retrieve files with consistent naming and organized subfolders.

By introducing an interactive review phase (via Markdown tables), engineers remain in control of final document selection but avoid hours of tedious clicking and file organization.

---

## 3. Key Features
- **Three-Stage Workflow**  
  “Documents → Files → Downloads” with clear checkpoints.
- **Interactive Review**  
  Export to and import from Markdown tables for human oversight.
- **Configurable Filters**  
  Pattern-based rules to include/exclude documents and files automatically.
- **Smart File Organization**  
  Automatic creation of subfolders using naming patterns (e.g., `Rig0000/3DModels/`).
- **Metadata Preservation**  
  Structured naming includes revision, case numbers, or any custom fields.
- **Selenium-Based Automation**  
  Uses ChromeDriver and Python to reliably navigate the RigDoc website.

---

## 4. Workflow / Pipeline Overview

1. **Change Search Parameters**  
   Adjust the target rig number and search URLs.
2. **Configure Filter Chain**  
   Add or edit filters that help pre-select or exclude certain documents and files.
3. **Fetch Documents**  
   Scrape relevant document metadata from RigDoc.
4. **Export Documents to Markdown**  
   Generate a `.md` table so you can mark which documents to include.
5. **Import Selections**  
   Reads the updated Markdown to finalize your chosen documents.
6. **Fetch Files**  
   Retrieves file metadata for the selected documents.
7. **Export Files to Markdown**  
   Another `.md` table to pick which files to download.
8. **Import File Selections**  
   Syncs your file choices back into the system.
9. **Download Files**  
   Automated file retrieval, naming, and subfolder organization.

---

## 5. Directory Structure
All outputs are stored under an `outputs/` folder to keep things organized:
```
outputs/
├── data/        # JSON and Markdown files with document/file metadata
└── downloads/   # Downloaded PDFs and other files
```

---

## 6. Setup & Requirements
1. **Windows OS**  
   (Tested primarily on Windows; other OSes may require modifications.)
2. **Python 3.6+**  
   Ensure you have Python installed and accessible in your system path.
3. **Chrome Browser + ChromeDriver**  
   Required for Selenium automation. Make sure the versions are compatible.
4. **Active RigDoc Credentials**  
   You must be able to log into RigDoc for the scraper to function.

**Setting Up the Environment**
```bash
# Initialize Python virtual environment
py_venv_init.bat

# (Optional) Install dependencies explicitly, if needed
py_venv_pip_install.bat
```

---

## 7. Quick Start Guide
1. **Initialize**  
   Run `py_venv_init.bat` to create and activate a virtual environment.
2. **Launch**  
   Run `RigOfficeDownloader-v4.bat` (or the Python script directly) to start.
3. **Configure**  
   Input your rig number and search URLs when prompted.
4. **Execute**  
   Follow the interactive menu steps:
   - Configure filters
   - Fetch documents
   - Export and review the documents `.md` file
   - Import selections
   - Fetch file metadata
   - Export and review the file `.md` file
   - Import file selections
   - Download files

---

## 8. Configuration
Below is an example `CONFIG` snippet to illustrate how you might tailor the tool’s behavior:

```json
{
  "rig_number": "R0000.020",
  "search_urls": [
    "https://rigdoc.nov.com/search/rigsearch?q=...",
    "https://rigdoc.nov.com/search/rigsearch?q=..."
  ],
  "filters": [
    {
      "type": "docs",
      "pattern": "*DRILL*FLOOR*",
      "field": "item_include",
      "value": true
    }
  ]
}
```
- **`rig_number`**: The primary rig or project identifier.
- **`search_urls`**: One or more RigDoc search endpoints to scrape.
- **`filters`**: Pattern-based rules for inclusion or exclusion.

---

## 9. Manual Editing
After fetching document or file metadata, you’ll find `.md` tables under `outputs/data/`. Each record includes flags like `item_include` or `item_download`. Manually set these to `true` or `false` to control what gets processed in the next steps.

Example table snippet in `MyRig-a-docs.md`:
```markdown
| doc_id   | title                  | item_include |
|----------|------------------------|--------------|
| 123456   | DRILLFLOOR Assembly   | false        |
| 789012   | PipeHandling Diagram  | true         |
```
Edit `item_include` for each row, save the file, then run “Import Selections” to apply changes.

---

## 10. Advanced Features
- **Path Sanitization**  
  Automatically cleans special characters in file names while preserving folder separators (`/`).
- **Deduplication**  
  Prevents duplicate downloads by comparing existing file hashes.
- **Field Ordering**  
  Customize the JSON and Markdown columns to highlight essential metadata.
- **Smart Scrolling**  
  Dynamically scrolls or paginates in RigDoc to capture all relevant entries.
- **Error Handling**  
  Graceful shutdown or skip logic when network or browser issues occur.

---

## 11. Tips for Success
- **Use Filters Wisely**  
  Automate the selection of high-priority documents (e.g., rig assembly drawings) and exclude irrelevant ones (e.g., BOP or VOID docs).
- **Review the Markdown Thoroughly**  
  Confirm your final picks before moving to the download step.
- **Check ChromeDriver Version**  
  Mismatched browser and driver versions can cause Selenium to fail.

---

## 12. Version History
- **v1**  
  Basic document scraping and downloading.
- **v2**  
  JSON ↔ Markdown conversions for human review.
- **v3**  
  Improved error handling and organization fields.
- **v4**  
  Subfolder support, advanced filter chains, field ordering.

---

## 13. Troubleshooting
- **Browser Launch Failures**  
  Verify Chrome is installed and that ChromeDriver is up to date.
- **Login/Captcha Interruptions**  
  If RigDoc prompts for extra security checks, complete them manually, then re-run.
- **Permissions**  
  Ensure your user has write access to `outputs/` and that your antivirus/firewall doesn’t block downloads.
- **Logging & Debug**  
  Check the console output for warnings/errors and look in `outputs/data/` for partial JSON/Markdown results.

---

## 14. Impact
By automating repetitive searches, metadata retrieval, and file organization, RigOfficeDownloader cuts down document preparation time by **75%** or more—allowing engineers to devote more energy to critical 3D modeling work.

---

*Happy Downloading!*

```


## Message 4

# CONTEXT



RigOffice is a proprietary application used within my company (thousands of employees), it's the application that all projects and documents resides in. Since document search and retrieval is a frequent occurrence (every time we're working on a new project or further development on existing projects), it eventually became a very frustrating part of my job because it's so tedious (and should be easy to automate). Since the application itself doesn't offer an api i've decided to leverage their web-interface (as the interface with which i can automate through python and selenium-just as a note, the reason for selenium is to prevent issues and ratelimiting).



# SOLUTION



i've given the utility the temporary name `RigOfficeDownloader`, but this might change (as it feels lazy and non-proprietary). it's a utility that'll be used internally in my department (in a large engineering company) to automate (speed up the process) when working on new projects. Although the default mode of the utility is to rely on the predefined search templates and filters to automate the full process, it is also designed (and thoughtfully set up) such that it can either be used manually or interactively (e.g. if i just want to download a list of specific documents) while adhering to the *same* concepts. although this utility springs out from the need of a small subset of the company (just a small department within the company of <20 people) it's important to always keep in mind that since it'll *eventually* be used by other people (in other departmens within the company), ALWAYS ENSURE **inherent and fundamental cohesiveness**; because **simplicity and elegance** is __INTEGRAL__. you will understand exactly why i mean by this as you start understanding the fundamental concepts (especially with regards to the workflow itself) utilized in the utility, it's **extremely adaptable** (the workflow/concepts/methods for how it works is *meticiously crafted*; there's *a reason for everything*).



## WORKFLOW



This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow follows these steps:



    Interactive Menu

    - The utility provides an interactive menu where the user can choose which steps to execute

    - This allows for flexibility in the workflow, enabling the user to run specific steps as needed

    - The user can also update the rig number and search URLs through this menu



    Key Features

    - Automatic document and file metadata scraping

    - User-friendly Markdown editing interface

    - Customizable file naming with item_generated_name

    - Support for subfolder organization in downloads

    - Deduplication of documents and files

    - Configurable field ordering for JSON and Markdown exports



    Technical Implementation

    - Uses Selenium with Chrome WebDriver for web scraping

    - Implements smart waiting strategies for page loading

    - Handles browser sessions with proper cleanup

    - Provides progress feedback during operations

    - Sanitizes filenames for valid paths



    1. Fetch Documents

    - The utility starts by scraping document metadata from predefined search URLs

    - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory

    - Each document entry includes metadata like title, document number, revision, etc.

    - All documents are initially marked with item_include=False

    - Each document gets an item_generated_name for better identification



    2. Export Documents to Markdown

    - The JSON data is exported to a Markdown table: <rig>-a-docs.md

    - This allows the user to easily review and edit which documents to include

    - The user is expected to edit the Markdown file and set item_include=true for desired documents



    3. Import Updated Document Data

    - After the user edits the Markdown file, the utility imports the changes back to the JSON file

    - This updates which documents are marked for file retrieval



    4. Fetch Files for Selected Documents

    - For each document with item_include=true, the utility scrapes file metadata

    - File data is saved to <rig>-b-files.json

    - Each file is initially marked with item_download=False

    - Files inherit the document's item_generated_name with additional identifiers



    5. Export Files to Markdown

    - The file data is exported to a Markdown table: <rig>-b-files.md

    - The user reviews and edits which files to download by setting item_download=true



    6. Import Updated File Data

    - After editing, the utility imports the changes back to the JSON file

    - This updates which files are marked for download



    7. Download Selected Files

    - Files with item_download=true are downloaded

    - Files are named according to their item_generated_name + extension

    - The utility supports creating subfolders based on '/' in the item_generated_name

    - Files are saved to the outputs/downloads/<rig> directory



# CONSIDERATIONS



since our company use internal github repos for all development we do this utility will also be uploaded to github's company page (accessible for everyone within the company), i want to use this as an opportunity to impress (with unique and original simplicity and elegance). although 99.99% of developers in similar situations will choose to adhere to what they perceive as the concencus best practices and restructure (organize and refactor) the utility's codebase/project/filestructure based on these "rules", **i no longer blindly adhere to established concepts**, i *build* on them through *inherent understanding*. as an example, best practices are *great* for large and complex systems, and in some cases even **neccessary** - but such standardized practices does *not apply to small utilities the same way*, small utilities can often be transformed from a working utility within a single file of <500 lines, and after transforming it to "established best practices" will transform it into ~30 files and thousands of lines of code; __I WANT TO DO THE OPPOSITE OF THIS__. i want **balance** and **cognizeant and inherent understanding**, i want to leverage my own artistic vision onto the code. i find reduction of complexity through perpetually evaluation everything through different contexts through simulating (internally within my mind) different visual representations of components/sections/files in relation to each other.



# FILESTRUCTURE



this is the current filestructure:

```

RigOfficeDownloader

└── RigOfficeDownloader_v4

    ├── outputs

    │   ├── data

    │   │   ├── R5385.010-a-docs.json

    │   │   ├── R5385.010-a-docs.md

    │   │   ├── R5385.010-b-files.json

    │   │   └── R5385.010-b-files.md

    │   └── downloads

    │       └── ...

    ├── readme_variations

    │   ├── README_v01.md

    │   ├── README_v02.md

    │   ├── README_v03.md

    │   ├── README_v04.md

    │   ├── README_v05.md

    │   ├── README_v06.md

    │   ├── README_v07.md

    │   ├── README_v08.md

    │   ├── README_v09.md

    │   ├── README_v10.md

    │   └── README_v11.md

    ├── src

    │   ├── .gitignore

    │   ├── RigOfficeDownloader-v4.bat

    │   ├── RigOfficeDownloader-v4.py

    │   └── code_guidelines.md

    ├── .gitignore

    ├── GOAL.md

    ├── README.md

    ├── RigOfficeDownloader-v4.sublime-project

    ├── py_venv_init.bat

    ├── requirements.txt

    └── understanding_the_environment.md

```



# OBJECTIVE



Please create a single README.md based on taking the best from each part of these variations:



    # Dir `readme_variations`



    ### File Structure



    ```

    ├── README_v01.md

    ├── README_v02.md

    ├── README_v03.md

    ├── README_v04.md

    ├── README_v05.md

    ├── README_v06.md

    ├── README_v07.md

    ├── README_v08.md

    ├── README_v09.md

    ├── README_v10.md

    ├── README_v11.md

    └── README_v12.md

    ```



    ---



    #### `README_v01.md`



    ```markdown

        # RigOfficeDownloader



        ## Overview

        RigOfficeDownloader automates document retrieval from NOV's RigDoc system, eliminating tedious manual processes and allowing engineers to focus on their primary work.



        ## Key Features

        - Three-stage workflow: Documents -> Files -> Downloads

        - Interactive menu for flexible execution

        - User control points via Markdown interfaces

        - Smart file organization with subfolder support

        - Configurable filter chains



        ## Setup & Usage

        1. Run `py_venv_init.bat` to create the Python environment

        2. Run `RigOfficeDownloader-v4.bat` to start the application

        3. Use the interactive menu to configure and execute workflow steps



        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Preserves document context and relationships



        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser (for Selenium automation)

    ```



    ---



    #### `README_v02.md`



    ```markdown

        # RigOfficeDownloader



        ## The Problem

        Engineers waste significant time navigating NOV's RigDoc system to gather technical documentation before they can begin their actual 3D modeling work. This manual process is:

        - Tedious and repetitive

        - Error-prone

        - A poor use of skilled engineering time



        ## The Solution

        RigOfficeDownloader automates the document retrieval process through a three-stage workflow:

        1. **Document Retrieval**: Automatically scrapes document metadata

        2. **File Metadata**: Fetches file information for selected documents

        3. **Smart Downloads**: Downloads files with intelligent naming and organization



        ## How It Works

        - Uses Selenium to automate web interactions with RigDoc

        - Exports data to Markdown for user review and selection

        - Applies configurable filters to pre-select relevant documents

        - Organizes downloads with consistent naming patterns



        ## Getting Started

        1. Run `py_venv_init.bat` to set up the environment

        2. Run `RigOfficeDownloader-v4.bat` to launch the application

        3. Follow the interactive menu prompts



        ## Impact

        Reduces documentation gathering time by 75%+, allowing engineers to focus on value-adding 3D modeling work instead of tedious document retrieval.

    ```



    ---



    #### `README_v03.md`



    ```markdown

        # RigOfficeDownloader



        ## Technical Overview

        RigOfficeDownloader is a Python-based automation tool that uses Selenium WebDriver to interact with NOV's RigDoc system. It implements a linear multi-stage pipeline architecture with human checkpoints between stages.



        ## Architecture

        - **Core Technologies**: Python, Selenium, BeautifulSoup, JSON, Markdown

        - **Data Flow**: Web scraping -> JSON storage -> Markdown interface -> User selection -> Automated downloads

        - **File Organization**: Hierarchical naming system with metadata embedding and subfolder support



        ## Workflow Steps

        1. **Document Metadata Retrieval**: `fetch_docs()` scrapes document information

        2. **Document Selection**: `json_to_md_table()` exports to Markdown for user editing

        3. **Selection Import**: `md_table_to_json()` imports user selections

        4. **File Metadata Retrieval**: `fetch_files()` gets file information for selected documents

        5. **File Selection**: Export/import cycle for user selection of files

        6. **Download Process**: `download_files()` retrieves selected files with smart naming



        ## Filter Chain System

        Configurable sequential filters can be applied to automatically select documents and files based on patterns in various fields.



        ## Setup Instructions

        1. Run `py_venv_init.bat` to initialize the Python environment

        2. Run `RigOfficeDownloader-v4.bat` to execute the application



        ## Version History

        - v1: Basic document retrieval and download

        - v2: JSON/Markdown conversion and user selection

        - v3: Improved error handling and field organization

        - v4: Subfolder support, filter chains, field ordering

    ```



    ---



    #### `README_v04.md`



    ```markdown

        # RigOfficeDownloader

        > Automate document retrieval from NOV's RigDoc system



        ## What This Tool Does

        RigOfficeDownloader helps you quickly gather technical documentation from RigDoc without the tedious manual process of searching, selecting, and downloading files one by one.



        ## Quick Start Guide

        1. **Setup**: Run `py_venv_init.bat` to create the Python environment

        2. **Launch**: Run `RigOfficeDownloader-v4.bat` to start the application

        3. **Configure**: Enter your rig number and search URLs when prompted

        4. **Run**: Follow the numbered menu to execute each step of the workflow



        ## Workflow Steps Explained

        0. **Change Parameters**: Update rig number and search URLs

        1. **Configure Filters**: Set up automatic document/file selection rules

        2. **Fetch Documents**: Retrieve document metadata from RigDoc

        3. **Review Documents**: Edit the Markdown file to select which documents to process

        4. **Import Selections**: Load your document selections

        5. **Fetch Files**: Get file metadata for selected documents

        6. **Review Files**: Edit the Markdown file to select which files to download

        7. **Import File Selections**: Load your file selections

        8. **Download Files**: Retrieve the selected files



        ## Tips for Success

        - Use filters to automatically pre-select relevant documents

        - Review the Markdown files carefully before proceeding to the next step

        - Files will be organized in subfolders based on '/' in their generated names



        ## Need Help?

        Check the source code comments for detailed information about each function and workflow step.

    ```



    ---



    #### `README_v05.md`



    ```markdown

        # RigOfficeDownloader



        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.



        ```

        Documents -> Files -> Downloads

        ```



        ## Features

        - Three-stage workflow with user control points

        - Interactive menu for flexible execution

        - Configurable filter chains for automatic selection

        - Smart file organization with subfolder support

        - Markdown interfaces for document/file review



        ## Quick Start

        ```

        1. Run py_venv_init.bat

        2. Run RigOfficeDownloader-v4.bat

        3. Follow the interactive menu

        ```



        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Handles errors gracefully



        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser

    ```



    ---



    #### `README_v06.md`



    ```markdown

        # RigOfficeDownloader



        Automated document retrieval system for NOV RigDoc, engineered to optimize engineering workflows through intelligent automation and human oversight.



        ## Key Features

        - **Three-Stage Workflow**: Document selection → File selection → Download

        - **Metadata Preservation**: Structured naming with revision/case numbers

        - **Interactive Review**: Markdown tables for manual inclusion flags

        - **Smart Organization**: Automatic subfolder creation via naming patterns

        - **Filter System**: Pattern-based inclusion/exclusion rules

        - **Browser Automation**: Smart waiting strategies and session management



        ## Workflow Process

        ```python

        1. Fetch Documents       # Scrape metadata → <rig>-a-docs.json

        2. Export to Markdown    # <rig>-a-docs.md - Set item_include=true

        3. Import Selections     # Update JSON with user choices

        4. Fetch Files           # Get file listings → <rig>-b-files.json

        5. Export Files MD       # <rig>-b-files.md - Set item_download=true

        6. Import File Choices   # Update file selections

        7. Download Files        # Auto-organized with subfolder support

        ```



        ## Configuration (CONFIG Section)

        ```python

        {

            "rig_number": "R0000.020",  # Target rig ID

            "search_urls": [            # Predefined search templates

                "https://rigdoc.nov.com/search/rigsearch?q=...",

                "https://rigdoc.nov.com/search/rigsearch?q=..."

            ],

            "filters": [                # Sequential processing rules

                {

                    "type": "docs",     # Apply to documents/files

                    "pattern": "*DRILL*FLOOR*",  # Glob-style matching

                    "field": "item_include",     # Field to modify

                    "value": True       # Set True/False based on match

                }

            ]

        }

        ```



        ## Setup & Usage

        ```bash

        # Initialize environment

        py_venv_init.bat

        py_venv_pip_install.bat



        # Run modes

        RigOfficeDownloader-v4.py [rig_number] [mode]



        Modes:

        --auto         # Full automation

        --interactive  # Step-by-step control

        --config       # Modify search templates/filters

        ```



        ## Key Configuration Patterns

        - **Inclusion Filters**: `*G000*`, `*A000*` (core equipment drawings)

        - **Exclusion Filters**: `*VOID*`, `*BOP*` (void documents/systems)

        - **File Types**: Auto-prioritize PDFs with `*.pdf` pattern



        ## Requirements

        - Chrome Browser + ChromeDriver

        - Active RigDoc credentials

        - Python 3.8+ with dependencies from requirements.txt

        - Network access to rigdoc.nov.com



        ## Advanced Features

        - **Path Sanitization**: Auto-clean special chars while preserving /

        - **Deduplication**: Hash-based conflict resolution

        - **Field Ordering**: Customizable JSON/Markdown columns

        - **Smart Scrolling**: Progressive page loading detection



        > Reduces documentation prep time by 60-75% compared to manual retrieval

        > Version 4.0 | Active development with subfolder support

    ```



    ---



    #### `README_v07.md`



    ```markdown



        ## Overview

        - Automated tool for scraping, filtering, and downloading document files (e.g., PDFs) from rigdoc.nov.com.

        - Use case: curate, review, and batch-download rig-related documents and technical files.



        ## Directory Structure



        * `outputs/` (BASE\_OUTPUT): All results stored here

        * `outputs/data/` (DATA\_DIR): Document and file metadata (JSON/MD)

        * `outputs/downloads/` (DL\_DIR): Downloaded PDF and file outputs



        ## Pipeline Overview



        1. Change search parameters (rig number, URLs)

        2. Configure filter chain (add, edit, delete, toggle, reorder filters)

        3. Fetch docs (scrape data from rigdoc.nov.com)

        4. Export docs to Markdown (for selection/editing)

        5. Import docs from Markdown (sync edited selection)

        6. Fetch candidate files linked to selected docs

        7. Export file list to Markdown (for editing/selecting files for download)

        8. Import updated file list from Markdown

        9. Download marked files (PDFs only, via Chrome)



        ## Manual Editing



        * Edit the exported Markdown tables (`outputs/data/*.md`) to include or exclude records before batch processing

        * Set `item_include` (docs) and `item_download` (files) fields



        ## Running the Tool



        ```bash

        python rigdocscraper.py

        ```



        * Interactive menu enables step selection (numbers/comma/space-separated)

        * Supports adjusting parameters, filter configuration, and reviewing batch steps

        * Prompts will guide through editing, import/export, and download procedures



        ## Troubleshooting



        * Requires functioning Chrome installation; verify webdriver-manager compatibility

        * Common issues: browser launch failures, login/captcha requirements, file permissions

        * Output logs and warnings shown in terminal; inspect `outputs/data/` for progress

    ```



    ---



    #### `README_v08.md`



    ```markdown

        # RigOfficeDownloader



        > **Automate** document retrieval from NOV's RigDoc system, **saving** engineering time and **streamlining** 3D modeling preparation.



        ---



        ## 1. Overview



        RigOfficeDownloader is a Python-based automation tool that interacts with NOV's RigDoc system using **Selenium WebDriver**. It implements a **multi-stage workflow** (Documents → Files → Downloads) with **human checkpoints** in between. This approach eliminates tedious manual processes, preserving engineering time for high-value tasks like 3D modeling.



        ---



        ## 2. The Problem



        - Engineers lose valuable hours **manually searching** and **downloading** technical documentation.

        - The repetitive process is **error-prone** and distracts from real engineering work.



        ---



        ## 3. The Solution



        - **Automates** the document search, scraping, and download phases, cutting the time required by **75%+**.

        - Offers **three main stages**—document metadata retrieval, file metadata retrieval, and file downloads—each controlled by user-edited Markdown tables for fine-tuned selection.



        ---



        ## 4. Key Features



        - **Three-Stage Workflow**

          1. **Documents**: Gather doc metadata, mark `item_include` in Markdown.

          2. **Files**: Fetch file info for included docs, mark `item_download` in Markdown.

          3. **Downloads**: Organize files with subfolder support based on `'/'` in `item_generated_name`.



        - **Interactive Menu**: Allows step-by-step or fully automated runs.

        - **User Review via Markdown**: You decide which items to include or skip by editing `.md` tables.

        - **Configurable Filter Chains**: Glob-style pattern matching to auto-select or exclude docs/files.

        - **Smart Organization**: Hierarchical naming that embeds rig/drawing metadata, plus subfolder creation.

        - **Error Handling**: Graceful session management, robust page-scrolling logic, and deduplication.



        ---



        ## 5. Workflow Steps



        1. **Change Parameters**: (Optional) Update rig number, search URLs.

        2. **Configure Filters**: Setup or edit filter rules to auto-include/exclude docs/files.

        3. **Fetch Docs**: Scrape document metadata into `<rig>-a-docs.json` (all `item_include=false` initially).

        4. **Export Docs**: Generate `<rig>-a-docs.md`; manually set `item_include=true` for desired items.

        5. **Import Updated Docs**: Sync changes back from Markdown to JSON.

        6. **Fetch Files**: Retrieve file metadata for those docs, saved as `<rig>-b-files.json` (`item_download=false`).

        7. **Export Files**: Create `<rig>-b-files.md`; set `item_download=true` for target files.

        8. **Import Updated Files**: Sync file selection from Markdown to JSON.

        9. **Download Files**: Acquire and organize all selected files under `outputs/downloads/<rig>`.



        ---



        ## 6. Architecture & Technology



        - **Core Stack**:

          - **Python 3.6+**

          - **Selenium** for browser automation

          - **BeautifulSoup** for HTML parsing

          - **JSON/Markdown** for data storage and user edits

        - **Linear Multi-Stage** design with user checkpoints ensures incremental control and reusability.

        - **Filter System**: Pattern-based matching (inclusion/exclusion) on fields like `item_generated_name` or `item_case_description`.

        - **File Organization**: Subfolder creation from `'/'` in `item_generated_name`, plus sanitization of invalid characters.



        ---



        ## 7. Directory Structure



    ```



    ---



    #### `README_v09.md`



    ```markdown

        ## Intent

        To automate and streamline the tedious process of retrieving project documentation from NOV's RigOffice system, enabling 3D engineers to spend less time gathering information and more time on actual 3D modeling work.



        ## Key Project Aspects



        ### Primary Problem

        Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.



        ### Solution Approach

        A Python-based utility that:

        1. Automatically scrapes document metadata from RigOffice

        2. Extracts file information from those documents

        3. Downloads and organizes selected files based on user criteria



        ### Current State

        Functional working prototype that:

        - Uses a 3-step workflow (document metadata → file metadata → download)

        - Stores intermediate results in JSON format

        - Allows user intervention between steps

        - Provides progress feedback



        ### Critical Next Steps

        1. **Separate JSON files** when using different search approaches to avoid merging unrelated searches

        2. **Implement file hash checking** to prevent redundant downloads

        3. **Improve progress visibility** during lengthy scraping operations



        ### Core Technical Pattern

        A single-file, modular approach using:

        - Selenium for browser automation

        - JSON for data storage

        - Three-stage processing with user control points

        - Incremental updates to avoid redundant work



        ### Key Success Metrics

        - Reduce documentation gathering time by 75%+

        - Ensure reliable retrieval of required documentation

        - Organize files in a way that streamlines workflow

        - Support both broad searches (by rig number) and targeted searches (by specific documents)

    ```



    ---



    #### `README_v10.md`



    ```markdown

        ### RigOfficeDownloader Utility Workflow



        This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:



        1. Fetch Documents

        - The utility starts by scraping document metadata from predefined search URLs

        - Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory

        - Each document entry includes metadata like title, document number, revision, etc.

        - All documents are initially marked with item_include=False

        - Each document gets an item_generated_name for better identification



        2. Export Documents to Markdown

        - The JSON data is exported to a Markdown table: <rig>-a-docs.md

        - This allows the user to easily review and edit which documents to include

        - The user is expected to edit the Markdown file and set item_include=true for desired documents



        3. Import Updated Document Data

        - After the user edits the Markdown file, the utility imports the changes back to the JSON file

        - This updates which documents are marked for file retrieval



        4. Fetch Files for Selected Documents

        - For each document with item_include=true, the utility scrapes file metadata

        - File data is saved to <rig>-b-files.json

        - Each file is initially marked with item_download=False

        - Files inherit the document's item_generated_name with additional identifiers



        5. Export Files to Markdown

        - The file data is exported to a Markdown table: <rig>-b-files.md

        - The user reviews and edits which files to download by setting item_download=true



        6. Import Updated File Data

        - After editing, the utility imports the changes back to the JSON file

        - This updates which files are marked for download



        7. Download Selected Files

        - Files with item_download=true are downloaded

        - Files are named according to their item_generated_name + extension

        - The utility supports creating subfolders based on '/' in the item_generated_name

        - Files are saved to the outputs/downloads/<rig> directory



        Interactive Menu

        - The utility provides an interactive menu where the user can choose which steps to execute

        - This allows for flexibility in the workflow, enabling the user to run specific steps as needed

        - The user can also update the rig number and search URLs through this menu



        Key Features

        - Automatic document and file metadata scraping

        - User-friendly Markdown editing interface

        - Customizable file naming with item_generated_name

        - Support for subfolder organization in downloads

        - Deduplication of documents and files

        - Configurable field ordering for JSON and Markdown exports



        Technical Implementation

        - Uses Selenium with Chrome WebDriver for web scraping

        - Implements smart waiting strategies for page loading

        - Handles browser sessions with proper cleanup

        - Provides progress feedback during operations

        - Sanitizes filenames for valid paths

    ```



    ---



    #### `README_v11.md`



    ```markdown

        # RigOfficeDownloader



        ## Overview

        RigOfficeDownloader is an automation tool designed to streamline the process of retrieving and downloading technical documentation from NOV's RigDoc system. It eliminates the tedious, time-consuming process of manual document retrieval, allowing engineers to focus on their primary work.



        ## Key Features

        - **Three-Stage Workflow**: Documents → Files → Downloads

        - **Interactive Menu**: Choose which steps to execute

        - **User Control Points**: Review and select documents/files via Markdown interfaces

        - **Smart File Organization**: Subfolder support based on naming patterns

        - **Configurable Filters**: Apply filter chains to automatically select relevant documents



        ## Workflow

        1. **Document Retrieval**: Scrapes document metadata from RigDoc

        2. **Document Selection**: Exports to Markdown for user review and selection

        3. **File Metadata**: Fetches file information for selected documents

        4. **File Selection**: Exports to Markdown for user review and selection

        5. **Download**: Downloads selected files with intelligent naming and organization



        ## Setup

        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment

        2. The script will:

           - Find available Python installations

           - Create a virtual environment

           - Install required packages from requirements.txt



        ## Usage

        1. Run `RigOfficeDownloader-v4.bat` to start the application

        2. Use the interactive menu to:

           - Configure search parameters

           - Run specific workflow steps

           - Apply filter chains

           - Review and select documents/files



        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Handles errors gracefully

        - Preserves document context and relationships



        ## File Structure

        ```

        outputs/

        ├── data/           # JSON and Markdown files for documents and files

        └── downloads/      # Downloaded files organized by rig number

        ```



        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser (for Selenium automation)

    ```



    ---



    #### `README_v12.md`



    ```markdown

        # RigOfficeDownloader



        Automate document retrieval from NOV's RigDoc system to save engineering time and streamline 3D modeling preparation.



        ```

        Documents -> Files -> Downloads

        ```



        ## Overview

        RigOfficeDownloader eliminates the tedious, time-consuming process of manually retrieving technical documentation from NOV's RigDoc system. Engineers can focus on their primary work instead of navigating complex document repositories.



        ## Key Features

        - Three-stage workflow with user control points

        - Interactive menu for flexible execution

        - Configurable filter chains for automatic selection

        - Smart file organization with subfolder support

        - Markdown interfaces for document/file review



        ## Workflow

        1. **Document Retrieval**: Scrapes document metadata from RigDoc

        2. **Document Selection**: Exports to Markdown for user review and selection

        3. **File Metadata**: Fetches file information for selected documents

        4. **File Selection**: Exports to Markdown for user review and selection

        5. **Download**: Downloads selected files with intelligent naming and organization



        ## Setup

        1. Run `py_venv_init.bat` to create and initialize the Python virtual environment

        2. Run `RigOfficeDownloader-v4.bat` to start the application



        ## Usage

        Choose from the interactive menu:

        ```

        [0] Change search parameters

        [1] Configure filter chain

        [2] Fetch docs (scrape initial data)

        [3] Export docs (to Markdown for editing)

        [4] Import updated doc data

        [5] Fetch files (prepare files for download)

        [6] Export files (to Markdown for editing)

        [7] Import updated file data

        [8] Download files

        ```



        ## Benefits

        - Reduces documentation gathering time by 75%+

        - Maintains consistent file organization

        - Provides user control at key decision points

        - Handles errors gracefully



        ## Requirements

        - Windows OS

        - Python 3.6+

        - Chrome browser (for Selenium automation)



        ## Version History

        - v1: Basic document retrieval and download

        - v2: JSON/Markdown conversion and user selection

        - v3: Improved error handling and field organization

        - v4: Subfolder support, filter chains, field ordering

    ```


## Message 5

**Synthesizing readme variations**

I’m mapping out a single README.md that brings together the best aspects of various readme versions, ensuring clarity and cohesive structure.

**Incorporating elements**

I'm pulling together components from various readme documents to craft a detailed but concise single readme, ensuring it isn't overly extensive.

## Message 6

```markdown
# RigOfficeDownloader
> **Automate** document retrieval from NOV’s RigDoc system, saving valuable engineering time and **streamlining** project preparation.

---

## 1. Overview
RigOfficeDownloader is a **Python-based** automation tool that interacts with NOV’s RigDoc system via **Selenium WebDriver**. Its goal is to **eliminate** the tedious, repetitive process of manually searching for, selecting, and downloading required documentation. By integrating **human checkpoints** at critical stages (through user-edited Markdown files), it preserves user control while slashing the overall retrieval time by **75%+**.

**Why It Matters**  
Engineers routinely spend hours manually gathering rig-related documents before actual 3D modeling or design work can commence. This utility frees them from the manual overhead, delivering essential documents in a fraction of the time. The workflow is inherently **adaptable** and **cohesive**, ensuring long-term maintainability and ease of extension—without sacrificing simplicity.

---

## 2. Key Features

1. **Three-Stage Workflow**  
   - **Documents** → **Files** → **Downloads**  
   Automates each stage while allowing user intervention in between.

2. **Interactive Menu**  
   - Step-by-step or fully automated runs.
   - Allows dynamic updates to rig number, search URLs, and filter settings.

3. **User Review via Markdown**  
   - Exports scraped data to `.md` tables, where you set `item_include=true` / `item_download=true` to select desired items.

4. **Configurable Filter Chains**  
   - Pattern-based (glob-style) rules to auto-select or exclude documents/files.

5. **Smart File Organization**  
   - Subfolders created from `'/'` within the `item_generated_name`.
   - Deduplication and **sanitized** file paths preserve clarity.

6. **Robust Automation**  
   - Selenium-based approach with Chrome WebDriver.
   - Smart scrolling and waiting strategies to handle dynamic pages.

7. **Efficient Data Flow**  
   - Data stored in JSON, toggled in Markdown, re-imported back to JSON.
   - Minimizes errors and keeps the process transparent.

---

## 3. The Problem
- **Wasted Time**: Manual retrieval of documents consumes a large part of engineers’ work, delaying real design tasks.
- **Error-Prone**: Frequent searching, repeated clicking, and manual downloading fosters mistakes and disorganization.
- **Poor Use of Skills**: Highly skilled engineers shouldn’t be bogged down by tedious admin tasks.

---

## 4. The Solution
RigOfficeDownloader introduces an **automated pipeline** that:
1. **Scrapes** and **filters** document metadata from RigDoc (using your rig number or custom search URLs).
2. **Exports** all metadata into Markdown, letting you fine-tune inclusion of documents and files.
3. **Downloads** selected files with **intelligent naming** and subfolder structure, avoiding duplicates or rework.

By combining automation with human-in-the-loop validation (via Markdown), the tool maintains accuracy without sacrificing speed.

---

## 5. Workflow Steps

1. **Change Parameters**  
   - (Optional) Update your rig number and search URLs via an interactive menu.  
2. **Configure Filters**  
   - Define or edit filter rules (inclusion/exclusion) for documents and files.
3. **Fetch Documents**  
   - Scrape document metadata into `<rig>-a-docs.json`; initially, `item_include=false` for each doc.
4. **Export Docs to Markdown**  
   - Generates `<rig>-a-docs.md`. Manually set `item_include=true` for the docs you want.
5. **Import Updated Docs**  
   - Merges your `.md` edits back into `<rig>-a-docs.json`.
6. **Fetch Files**  
   - For each included document, gathers file metadata into `<rig>-b-files.json` (default `item_download=false`).
7. **Export Files to Markdown**  
   - Outputs `<rig>-b-files.md`. Mark `item_download=true` to select files for download.
8. **Import Updated Files**  
   - Syncs your `.md` changes into `<rig>-b-files.json`.
9. **Download Files**  
   - Grabs each selected file, auto-generating subfolders from the naming patterns.

> **Tip**: After any export to Markdown, open the `.md` file, set the flags, then import again to proceed.

---

## 6. Directory Structure

```
RigOfficeDownloader_v4/
├── outputs
│   ├── data
│   │   ├── R5385.010-a-docs.json
│   │   ├── R5385.010-a-docs.md
│   │   ├── R5385.010-b-files.json
│   │   └── R5385.010-b-files.md
│   └── downloads
│       └── ... (organized subfolders & files)
├── src
│   ├── RigOfficeDownloader-v4.py
│   ├── RigOfficeDownloader-v4.bat
│   └── code_guidelines.md
├── py_venv_init.bat
├── requirements.txt
└── README.md
```

- **`outputs/data/`**: Stores JSON & Markdown for docs/files.
- **`outputs/downloads/`**: Stores downloaded files by rig number (creates subfolders from naming patterns).

---

## 7. Setup & Requirements

1. **System Requirements**  
   - Windows OS  
   - Python **3.6+**  
   - Chrome Browser + matching ChromeDriver  
   - Network access to `rigdoc.nov.com`  

2. **Initialize Environment**  
   ```bash
   # Creates a virtual environment and installs dependencies
   py_venv_init.bat
   ```

3. **Install Dependencies**  
   If needed, confirm additional dependencies from `requirements.txt`.  
   ```bash
   pip install -r requirements.txt
   ```

---

## 8. Usage

1. **Start the Application**  
   ```bash
   RigOfficeDownloader-v4.bat
   ```
   or  
   ```bash
   python RigOfficeDownloader-v4.py
   ```

2. **Interactive Menu**  
   You’ll see options like:
   ```
   [0] Change search parameters
   [1] Configure filter chain
   [2] Fetch documents
   [3] Export docs to Markdown
   [4] Import updated doc data
   [5] Fetch files
   [6] Export files to Markdown
   [7] Import updated file data
   [8] Download files
   ```
   *Choose a step (or sequence of steps) to run.*

3. **Editing in Markdown**  
   - For docs: `<rig>-a-docs.md`  
   - For files: `<rig>-b-files.md`  
   Mark `item_include=true` or `item_download=true` to finalize your selections.

4. **Automated or Manual**  
   - **Full Automation**: Preconfigure filters and let the tool auto-select your documents and files.  
   - **Selective/Manual**: Decide which steps to run, and carefully review each `.md` file.

---

## 9. Advanced Configuration

- **Filter Chains**  
  ```json
  {
    "type": "docs",
    "pattern": "*DRILL*FLOOR*",
    "field": "item_include",
    "value": true
  }
  ```
  Set multiple rules to automatically tag items.  

- **Smart Naming & Subfolders**  
  - The field `item_generated_name` can contain `'/'` to create nested directories.  
  - Names are sanitized to ensure valid paths.

- **Deduplication**  
  - Identical files (by hash) can be skipped to avoid wasted downloads.

- **Field Ordering**  
  - JSON & Markdown columns can be customized for clarity.

---

## 10. Benefits
- **Time Savings**: Cuts document-gathering time by **75%+**, letting engineers focus on actual design work.  
- **Consistent File Organization**: Ensures predictable subfolder structures and naming conventions.  
- **Interactive Control**: Step-by-step interface plus Markdown review.  
- **Error Reduction**: Less manual clicking, automated file handling.  
- **Scalability**: Adaptable filter system for broader or more targeted searches.

---

## 11. Troubleshooting
- **Browser Launch Failure**: Check Chrome installation and ChromeDriver compatibility.  
- **Login/Captcha Interruptions**: If RigDoc prompts for multi-factor or captcha, complete it manually, then restart.  
- **File Permission Errors**: Ensure your user account can write to `outputs/`.  
- **Partial Downloads**: Check console logs in case of network or credential timeouts.

---

## 12. Version History
- **v1**: Basic scraping and file downloads.  
- **v2**: JSON ↔ Markdown exports for user selection.  
- **v3**: Enhanced error handling and organizational fields.  
- **v4**: Subfolder support, filter chains, field ordering, and improved user menu flow.

---

## 13. Concluding Remarks
RigOfficeDownloader embodies a **balanced** approach to automation: minimal overhead, straightforward structure, and flexible user control. It’s designed to **scale** beyond a single department while remaining **simple** enough to modify or debug in a single-file or small-file layout.  

Feel free to explore the code, contribute improvements, and tailor filters to your team’s exact needs. If you have questions or feedback, check the source code comments and the interactive menu’s help prompts.

**Happy Downloading!**
```

