#!/usr/bin/env python3
"""
Robust ConversationSet
----------------------
A more robust version of the ConversationSet class that can handle validation errors.
"""

from __future__ import annotations

import json
import sys
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

from pydantic import BaseModel, Field
from tqdm import tqdm

try:
    from .utils import make_long_path_safe
except ImportError:
    # Handle direct execution
    from utils import make_long_path_safe

# Import from convoviz
import convoviz.models
from convoviz.utils import get_archive, sanitize




# Define our own Message and Conversation classes
class Message(BaseModel):
    """Minimal Message model with fallback for missing content."""
    content: Optional[str] = None
    id: Optional[str] = None
    author: Optional[Dict[str, Any]] = None

    def __str__(self) -> str:
        """Return a string representation of the message."""
        if self.content:
            return self.content
        return f"[No content available for message ID: {self.id}]"

class Conversation(BaseModel):
    """Minimal Conversation model."""
    title: str = "Untitled Conversation"
    conversation_id: str = ""
    create_time: Optional[float] = None
    messages: List[Message] = Field(default_factory=list)

    def save(self, filepath: Union[str, Path]) -> None:
        """Save the conversation to a markdown file."""
        filepath = Path(filepath)
        safe_filepath = make_long_path_safe(filepath)

        with safe_filepath.open("w", encoding="utf-8") as f:
            f.write(f"# {self.title}\n\n")
            f.write(f"Conversation ID: {self.conversation_id}\n")

            if self.messages:
                for i, msg in enumerate(self.messages):
                    f.write(f"\n## Message {i+1}\n\n")

                    # Handle missing content
                    content = msg.content

                    # Different types of content handling
                    if content is None:
                        msg_id = getattr(msg, 'id', 'unknown')
                        f.write(f"[No content available for message ID: {msg_id}]\n")
                    elif isinstance(content, str):
                        f.write(f"{content}\n")
                    else:
                        # For any other type, convert to string
                        try:
                            f.write(f"{str(content)}\n")
                        except Exception:
                            f.write("[Content in unsupported format]\n")


class RobustConversationSet(BaseModel):
    """A more robust version of the ConversationSet class."""

    array: List[Conversation]
    skipped_conversations: List[Dict] = []

    @classmethod
    def from_json(cls, filepath: Union[str, Path], verbose: bool = False) -> RobustConversationSet:
        """Load from a JSON file, handling validation errors."""
        filepath = Path(filepath)
        safe_filepath = make_long_path_safe(filepath)

        try:
            with safe_filepath.open(encoding="utf-8") as file:
                data = json.load(file)
        except Exception as e:
            raise RuntimeError(f"Error loading JSON file: {e}") from e

        return cls.from_list(data, verbose=verbose)

    @classmethod
    def from_list(cls, data: List[Dict], verbose: bool = False) -> RobustConversationSet:
        """Create a RobustConversationSet from a list of conversation dictionaries."""
        valid_conversations = []
        skipped_conversations = []

        for i, convo_data in enumerate(tqdm(data, desc="Processing conversations")):
            try:
                # Try to create a Conversation object using convoviz first
                try:
                    # Try using the convoviz Conversation model
                    conversation = convoviz.models.Conversation(**convo_data)
                    # Convert to our Conversation model
                    local_conversation = cls._convert_to_local_conversation(conversation)
                    valid_conversations.append(local_conversation)
                except Exception as e:
                    # If that fails, try our more flexible approach
                    conversation = cls._create_robust_conversation(convo_data)
                    valid_conversations.append(conversation)
                    if verbose:
                        print(f"Note: Conversation #{i} processed with fallback method: {type(e).__name__}")
            except Exception as e:
                # Store the skipped conversation
                skipped_conversations.append(convo_data)
                if verbose:
                    print(f"Warning: Skipping conversation #{i} due to error: {type(e).__name__}: {e}")
                continue

        return cls(array=valid_conversations, skipped_conversations=skipped_conversations)

    @staticmethod
    def _convert_to_local_conversation(convoviz_conversation) -> Conversation:
        """Convert a convoviz Conversation to our local Conversation model."""
        # Extract basic information
        title = getattr(convoviz_conversation, 'title', 'Untitled Conversation')
        conversation_id = getattr(convoviz_conversation, 'id',
                                 getattr(convoviz_conversation, 'conversation_id', ''))
        create_time = getattr(convoviz_conversation, 'create_time', None)

        # Convert messages
        messages = []
        convoviz_messages = getattr(convoviz_conversation, 'messages', [])

        for msg in convoviz_messages:
            content = getattr(msg, 'content', None)
            msg_id = getattr(msg, 'id', 'unknown')
            author = getattr(msg, 'author', None)

            messages.append(Message(
                content=content,
                id=msg_id,
                author=author
            ))

        # Create and return our local Conversation
        return Conversation(
            title=title,
            conversation_id=conversation_id,
            create_time=create_time,
            messages=messages
        )

    @staticmethod
    def _create_robust_conversation(data: Dict) -> Conversation:
        """Create a Conversation object with more flexible validation."""
        # Extract basic information with fallbacks
        title = data.get('title', 'Untitled Conversation')
        conversation_id = data.get('id', data.get('conversation_id', ''))
        create_time = data.get('create_time')

        # Handle messages with more flexibility
        messages = []

        # First try the standard format with a 'messages' list
        raw_messages = data.get('messages', [])

        # If no messages found, try to extract from ChatGPT export format
        if not raw_messages and 'mapping' in data and 'current_node' in data:
            # This is likely a ChatGPT export format
            try:
                mapping = data.get('mapping', {})
                current_node = data.get('current_node')

                # Collect all messages by traversing the mapping
                message_nodes = []
                node_queue = [current_node]
                visited = set()

                while node_queue:
                    node_id = node_queue.pop(0)
                    if node_id in visited or node_id is None:
                        continue

                    visited.add(node_id)
                    node = mapping.get(node_id)
                    if not node:
                        continue

                    # Add this node's message if it exists
                    if node.get('message') and 'content' in node.get('message', {}):
                        message_nodes.append(node)

                    # Add parent to queue
                    parent = node.get('parent')
                    if parent and parent not in visited:
                        node_queue.append(parent)

                    # Add children to queue
                    for child in node.get('children', []):
                        if child not in visited:
                            node_queue.append(child)

                # Sort messages by create_time if available
                message_nodes.sort(
                    key=lambda n: n.get('message', {}).get('create_time', 0)
                    if n.get('message') and n.get('message').get('create_time') is not None
                    else 0
                )

                # Extract content from each message node
                for node in message_nodes:
                    msg = node.get('message', {})
                    if not msg:
                        continue

                    msg_id = msg.get('id', 'unknown')
                    author = msg.get('author', {}).get('role', 'unknown')

                    # Extract content
                    content = None
                    msg_content = msg.get('content', {})

                    if isinstance(msg_content, dict) and 'parts' in msg_content:
                        parts = msg_content.get('parts', [])
                        if parts and isinstance(parts, list):
                            content = '\n'.join([str(p) for p in parts if p])

                    # Create a message with the extracted content
                    if content:
                        messages.append(Message(
                            content=content,
                            id=msg_id,
                            author={'role': author}
                        ))
            except Exception as e:
                print(f"Error extracting messages from ChatGPT export format: {e}")

        # Process standard message format if available
        if isinstance(raw_messages, list):
            for msg_data in raw_messages:
                try:
                    # Try to extract content with fallbacks
                    content = None
                    msg_id = msg_data.get('id', 'unknown')

                    # Handle different content structures
                    if 'content' in msg_data:
                        content = msg_data['content']
                    elif 'message' in msg_data and 'content' in msg_data['message']:
                        content = msg_data['message']['content']

                    # Create a message with the extracted content
                    if content is not None:
                        messages.append(Message(content=content, id=msg_id))
                    else:
                        # Create a message with empty content but valid ID
                        messages.append(Message(id=msg_id))
                except Exception:
                    # If we can't process this message, create a placeholder
                    messages.append(Message(id='unknown'))

        # Create and return the conversation
        return Conversation(
            title=title,
            conversation_id=conversation_id,
            create_time=create_time,
            messages=messages
        )

    @classmethod
    def from_zip(cls, filepath: Union[str, Path], verbose: bool = False) -> RobustConversationSet:
        """Load from a ZIP file, handling validation errors."""
        filepath = Path(filepath)

        try:
            # Extract the zip file using convoviz
            extract_dir = get_archive(filepath)
            convos_path = extract_dir / "conversations.json"

            if not convos_path.exists():
                raise FileNotFoundError(f"conversations.json not found in {extract_dir}")

            return cls.from_json(convos_path, verbose=verbose)
        except FileNotFoundError as e:
            # If convoviz fails to extract properly, try manual extraction
            if verbose:
                print(f"ConvoViz extraction failed: {e}")
                print("Attempting manual extraction...")

            try:
                extract_dir = cls._extract_zip_manual(filepath)
                convos_path = extract_dir / "conversations.json"

                if not convos_path.exists():
                    raise FileNotFoundError(f"conversations.json not found in {extract_dir}")

                return cls.from_json(convos_path, verbose=verbose)
            except Exception as manual_e:
                raise RuntimeError(f"Both ConvoViz and manual extraction failed. ConvoViz error: {e}. Manual error: {manual_e}") from manual_e
        except Exception as e:
            raise RuntimeError(f"Error loading zip file: {e}") from e

    def save(self, dir_path: Union[str, Path], *, progress_bar: bool = False) -> None:
        """Save the conversation set to the directory."""
        dir_path = Path(dir_path)
        safe_dir_path = make_long_path_safe(dir_path)
        safe_dir_path.mkdir(parents=True, exist_ok=True)

        successful = 0
        failed = 0

        for conversation in tqdm(
            self.array,
            "Writing Markdown files",
            disable=not progress_bar,
        ):
            try:
                # Format create_time if available
                create_time_prefix = ""
                if conversation.create_time:
                    # Convert timestamp to datetime and format
                    from datetime import datetime
                    try:
                        # Handle both integer and float timestamps
                        timestamp = float(conversation.create_time)
                        dt = datetime.fromtimestamp(timestamp)
                        create_time_prefix = f"{dt.year}.{dt.month:02d}.{dt.day:02d}-kl.{dt.hour:02d}.{dt.minute:02d}--"
                    except (ValueError, TypeError, OverflowError) as e:
                        # If timestamp conversion fails, try parsing as ISO format string
                        try:
                            if isinstance(conversation.create_time, str):
                                dt = datetime.fromisoformat(conversation.create_time.replace('Z', '+00:00'))
                                create_time_prefix = f"{dt.year}.{dt.month:02d}.{dt.day:02d}-kl.{dt.hour:02d}.{dt.minute:02d}--"
                        except Exception:
                            print(f"Warning: Could not parse create_time: {conversation.create_time}")

                # Create a safe filename using convoviz
                safe_title = sanitize(f"{conversation.title}")

                # Combine prefix and title
                filepath = dir_path / f"{create_time_prefix}{safe_title}.md"

                # Check for existing files with the same title but without timestamp
                old_filepath = dir_path / f"{safe_title}.md"
                if old_filepath.exists() and old_filepath != filepath:
                    try:
                        old_filepath.unlink()  # Remove the old file
                    except Exception as e:
                        print(f"Warning: Could not remove old file {old_filepath}: {e}")

                # Use our conversation's save method
                conversation.save(filepath)
                successful += 1
            except Exception as e:
                print(f"Warning: Failed to save conversation '{conversation.title}': {e}")
                failed += 1
                continue

        print(f"\nSaved {successful} conversations successfully. Failed to save {failed} conversations.")

    @staticmethod
    def _extract_zip_manual(zip_path: Union[str, Path]) -> Path:
        """Manual zip extraction as fallback when convoviz fails. Only extracts conversations.json."""
        from zipfile import ZipFile
        import tempfile
        import hashlib

        zip_path = Path(zip_path)
        safe_zip_path = make_long_path_safe(zip_path)

        # Create a very short extraction directory in temp to avoid path length issues
        zip_hash = hashlib.md5(str(zip_path).encode()).hexdigest()[:8]
        extract_dir = Path(tempfile.gettempdir()) / f"chatgpt_{zip_hash}"
        safe_extract_dir = make_long_path_safe(extract_dir)

        try:
            # Ensure the extraction directory exists
            safe_extract_dir.mkdir(parents=True, exist_ok=True)

            with ZipFile(safe_zip_path) as zip_file:
                # Only extract conversations.json - we don't need the image files for markdown conversion
                conversations_found = False
                for member in zip_file.namelist():
                    if member.endswith('conversations.json'):
                        # Extract just the conversations.json file
                        target_path = extract_dir / "conversations.json"
                        safe_target_path = make_long_path_safe(target_path)

                        with zip_file.open(member) as source:
                            with safe_target_path.open('wb') as target:
                                target.write(source.read())
                        conversations_found = True
                        break

                if not conversations_found:
                    raise FileNotFoundError("conversations.json not found in zip file")

            return extract_dir
        except Exception as e:
            raise RuntimeError(f"Error extracting zip file manually: {e}") from e



    def save_skipped(self, filepath: Union[str, Path]) -> None:
        """Save the skipped conversations to a JSON file."""
        filepath = Path(filepath)
        safe_filepath = make_long_path_safe(filepath)

        if not self.skipped_conversations:
            return

        try:
            with safe_filepath.open("w", encoding="utf-8") as f:
                json.dump(self.skipped_conversations, f, indent=2)
            print(f"Skipped conversations saved to: {filepath}")
        except Exception as e:
            print(f"Error saving skipped conversations: {e}")





if __name__ == "__main__":
    print("This module is not meant to be run directly.")
    print("Use main.py instead.")
