# Public MCP Server URLs

Conversation ID: 68500267-fa7c-8008-87ba-77094cad8ac1

## Message 1



the usecase is that i'm a developer that already use mcp's through vscode+augment:

```

# vscode:augment:mcp:browsertools:

{

  "servers": {

    "browser-tools": {

      "command": "cmd",

      "args": ["/c", "npx", "-y", "browser-tools-mcp"]

    }

  }

}

```



```

# vscode:augment:mcp:context7:

{

  "servers": {

    "context7": {

      "command": "cmd",

      "args": ["/c", "npx", "-y", "@upstash/context7-mcp"]

    }

  }

}

```



```

# vscode:augment:mcp:puppeteer:

{

  "servers": {

    "puppeteer": {

      "command": "cmd",

      "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-puppeteer"]

    }

  }

}

```



```

# vscode:augment:mcp:filesystem:

{

  "servers": {

    "file-system-mcp": {

      "command": "cmd",

      "args": ["/c", "npx", "-y", "@shtse8/filesystem-mcp"]

    }

  }

}

```



all of those are public and hosted in the cloud, i'm now looking for public server urls for these mcp's:

- `DeltaTask MCP Server`

- `Zettelkasten MCP Server`

## Message 2

Here are the public MCP servers you requested:

---

## ✅ DeltaTask MCP Server  
A hosted instance is available at:  
```
https://mcp.so/server/DeltaTask/brysontang
```  
This is the public URL for the DeltaTask MCP server, maintained by Bryson Tang citeturn0search0turn0search2.

---

## 🧠 Zettelkasten MCP Server  
The Zettelkasten-based MCP server hosted by entanglr is accessible at:  
```
https://mcp.so/server/zettelkasten-mcp/entanglr
```  
This provides the public endpoint for the Zettelkasten MCP server citeturn0search7turn0search1.

---

### 🔧 Example JSON context config
Here’s how you can configure them in your `vscode:augment:mcp` settings:
```json
# vscode:augment:mcp:deltatask:
{
  "servers": {
    "deltatask": {
      "url": "https://mcp.so/server/DeltaTask/brysontang"
    }
  }
}

# vscode:augment:mcp:zettelkasten:
{
  "servers": {
    "zettelkasten": {
      "url": "https://mcp.so/server/zettelkasten-mcp/entanglr"
    }
  }
}
```

---

Let me know if you'd like help testing connectivity, configuring authentication tokens, or integrating them into your VS Code setup!
