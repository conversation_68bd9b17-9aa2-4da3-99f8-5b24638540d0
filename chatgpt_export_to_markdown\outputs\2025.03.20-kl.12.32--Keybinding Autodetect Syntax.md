# Keybinding Autodetect Syntax

Conversation ID: 67dbfce0-88e0-8008-9740-34864036088d

## Message 1

i'm working on `keybindings.json` for vscode, how can i set a keybinding to make "ctrl+shift+alt+a" to autodetect syntax (in the current tab)?

## Message 2

To set a keybinding in Visual Studio Code that triggers syntax autodetection in the current tab using `Ctrl+Shift+Alt+A`, you can define the following in your `keybindings.json` file:


```json
{

