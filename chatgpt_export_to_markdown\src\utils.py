#!/usr/bin/env python3
"""
Shared Utilities
----------------
Common utility functions used across the application.
"""

import os
from pathlib import Path


def make_long_path_safe(path: Path) -> Path:
    """
    Convert path to long-path-safe format on Windows using \\\\?\\ prefix.
    This bypasses the 260-character MAX_PATH limitation and allows paths up to ~32,767 characters.
    
    Args:
        path: The path to make safe for long path operations
        
    Returns:
        Path object with long path prefix if needed on Windows
    """
    if os.name == 'nt':  # Windows only
        path_str = str(path.resolve())
        # Check if path is approaching the 260-character limit (use 240 as buffer)
        if len(path_str) > 240:
            # Add \\?\ prefix if not already present
            if not path_str.startswith('\\\\?\\'):
                return Path(f"\\\\?\\{path_str}")
    return path
