# Repo Comparison Summary

Conversation ID: 67f0ddfd-1f08-8008-8da9-eb6c9e84c759

## Message 1

The repositories you've listed are all tools designed to optimize and customize Windows systems, each with its unique approach and features. Here's a comparative overview:

**1. ChrisTitusTech/winutil**

- **Description**: A comprehensive utility that automates various Windows tasks, including software installation, system debloating, troubleshooting configurations, and Windows update fixes. citeturn0search0

- **Key Features**:
  - Streamlines software installations.
  - Applies system tweaks to remove bloatware.
  - Assists in troubleshooting and configuring system settings.
  - Provides fixes for common Windows update issues.

- **Unique Aspect**: Emphasizes automation of routine tasks to enhance system performance and user experience.

**2. farag2/Sophia-Script-for-Windows**

- **Description**: A robust PowerShell module offering over 150 tweaks for fine-tuning and automating routine tasks in Windows 10 and 11. citeturn0search1

- **Key Features**:
  - Extensive collection of customizable system tweaks.
  - Automation of routine maintenance tasks.
  - Focus on enhancing system performance without compromising stability.

- **Unique Aspect**: Provides a script-based approach, allowing advanced users to tailor Windows settings precisely to their preferences.

**3. hellzerg/optimizer**

- **Description**: An advanced configuration utility aimed at enhancing privacy and security on Windows systems, particularly recommended after a fresh installation. citeturn0search2

- **Key Features**:
  - Disables unnecessary Windows services and telemetry.
  - Applies system tweaks to improve performance.
  - Offers a user-friendly interface for applying changes.

- **Unique Aspect**: Prioritizes user privacy and security through a straightforward interface, making it accessible for users who prefer a more guided experience.

**4. Sophia-Community/SophiApp**

- **Description**: A free, open-source application that serves as a graphical user interface (GUI) for fine-tuning Windows 10 and 11, offering over 130 unique tweaks. citeturn0search3

- **Key Features**:
  - Modern and intuitive UI/UX.
  - Extensive set of system tweaks accessible via GUI.
  - Designed to configure Windows settings safely without causing harm.

- **Unique Aspect**: Bridges the gap between powerful script-based customization and user-friendly interfaces, catering to users who prefer visual tools over command-line scripts.

In summary, while all four tools aim to optimize Windows systems, they differ in their methods and user interfaces:

- **ChrisTitusTech/winutil** focuses on automating installations and system tweaks through scripted processes.

- **farag2/Sophia-Script-for-Windows** offers a comprehensive PowerShell module for detailed customization.

- **hellzerg/optimizer** provides an easy-to-use application emphasizing privacy and security enhancements.

- **Sophia-Community/SophiApp** delivers a GUI-based experience for users seeking extensive tweaks with a modern interface. 

## Message 2

The two repositories you've mentioned—**Sophia Script for Windows** and **SophiApp**—are interconnected projects aimed at fine-tuning and automating tasks in Windows 10 and 11. Here's a detailed comparison:

**1. Sophia Script for Windows**

- **Nature**: A comprehensive PowerShell module.

- **Functionality**: Offers over 150 unique tweaks for automating routine tasks and customizing Windows settings.

- **User Interaction**: Requires users to edit and execute PowerShell scripts.

- **Customization**: Users can tailor the script by enabling or disabling specific functions before execution.

- **Target Audience**: Geared towards users comfortable with scripting and seeking granular control over system configurations.

- **Repository**: [Sophia Script for Windows](https://github.com/farag2/Sophia-Script-for-Windows)

**2. SophiApp**

- **Nature**: A graphical user interface (GUI) application.

- **Functionality**: Provides a modern UI/UX with more than 130 unique tweaks for Windows customization.

- **User Interaction**: Allows users to apply tweaks through an intuitive interface without scripting.

- **Portability**: Fully portable; doesn't require installation or modify system registry.

- **Target Audience**: Designed for users who prefer a visual interface for system customization.

- **Repository**: [SophiApp](https://github.com/Sophia-Community/SophiApp)

**Key Differences**:

- **Interface**: Sophia Script is script-based, requiring manual editing, while SophiApp offers a user-friendly GUI.

- **Ease of Use**: SophiApp simplifies the tweaking process, making it accessible to users without scripting knowledge.

- **Development Focus**: SophiApp is considered the successor to Sophia Script, aiming to encompass all its functionalities in a more user-friendly package.

In summary, while both tools serve the purpose of Windows customization, SophiApp provides a more approachable interface for a broader user base, whereas Sophia Script caters to advanced users comfortable with scripting. 

## Message 3

The two repositories, [Sophia-Script-for-Windows](https://github.com/farag2/Sophia-Script-for-Windows) and [SophiApp](https://github.com/Sophia-Community/SophiApp), are both tools designed for fine-tuning and automating tasks in Windows 10 and 11. While they share a common goal, they differ in their implementation and user interfaces.

**1. Sophia-Script-for-Windows**

- **Nature**: A comprehensive PowerShell module.

- **Functionality**: Offers over 150 unique tweaks for automating routine tasks and customizing Windows settings.

- **User Interaction**: Requires users to edit and execute PowerShell scripts, making it more suited for advanced users comfortable with scripting.

- **Customization**: Highly customizable through script modifications, allowing precise control over applied tweaks.

**2. SophiApp**

- **Nature**: A standalone, portable application with a graphical user interface (GUI).

- **Functionality**: Provides a modern UI/UX with more than 130 unique tweaks for Windows customization.

- **User Interaction**: Designed for users who prefer a GUI over scripting, making it more accessible to a broader audience.

- **Portability**: Fully portable and does not require installation; it doesn't save any data into the registry.

In summary, **Sophia-Script-for-Windows** is a script-based tool offering extensive customization through PowerShell, ideal for users comfortable with scripting. In contrast, **SophiApp** provides a user-friendly GUI for Windows customization, catering to those who prefer a visual interface. 

## Message 4



what's the difference between these repos?



    https://github.com/ChrisTitusTech/winutil



    https://github.com/farag2/Sophia-Script-for-Windows



    https://github.com/hellzerg/optimizer



    https://github.com/Sophia-Community/SophiApp


## Message 5

You're observing correctly: the latest stable **release** (version 1.0.97) is dated July 27, 2023, yet the repository itself has ongoing **commits** as recent as two days ago.

Here's what that typically indicates:

- **Continuous Development:**  
  Development is actively ongoing, with regular updates, improvements, or bug fixes being committed directly to the main branch.

- **Release Management:**  
  Despite ongoing development, stable releases are less frequent. The maintainers likely bundle multiple improvements into significant release versions rather than frequent incremental updates.

- **Recommendation:**  
  For the most up-to-date features, consider using the repository directly (cloning or downloading from GitHub). If you prefer stability and fewer bugs, stick to the official release versions.

This scenario is common with open-source projects, balancing between active development and stable deployment.

## Message 6

what's the difference between these two repos?



    https://github.com/farag2/Sophia-Script-for-Windows

    https://github.com/Sophia-Community/SophiApp

## Message 7

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-Ug25aG4bHy7NU6mFWTcLE7', 'size_bytes': 284201, 'width': 1845, 'height': 1188, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
if you look at the screenshot from the repo the most recent release was all the way back in 2023, while the repo itself has commits recent as two days ago. 

## Message 8

The provided README explains why you're seeing a discrepancy between the recent commits and the older stable release (version **1.0.97** from July 27, 2023):

### Important Note from README:
> ⚠️ **CAUTION:**  
> **Please avoid using Sophia 1.0.97**  
> Sophia 1.0.97 is deprecated. Please avoid using it until `SophiApp 2.0 | Daria` will be released (**2025 H2**). Follow the development in a test branch [here](https://github.com/Sophia-Community/SophiApp/commits/dev-winappsdk). [Details](https://t.me/SophiaNews/3897).

### Explanation:
- The project has moved beyond version **1.0.97**, marking it as deprecated and discouraging its continued use.
- Current active development is focused on a new major update (`SophiApp 2.0 | Daria`), scheduled for release in the second half of 2025.
- Ongoing commits and updates you're seeing are happening on a development branch (`dev-winappsdk`), not yet merged into an official stable release.

### Current Recommendation:
- For stable, daily usage, avoid using the deprecated version **1.0.97**.
- To follow or test current developments, use the latest commits from the development branch [dev-winappsdk](https://github.com/Sophia-Community/SophiApp/commits/dev-winappsdk).

This approach indicates a major overhaul or extensive feature set being developed, typical of large jumps between major releases in software development.

## Message 9

To download and run the current portable development version (**SophiApp 2.0 | Daria**), follow these steps carefully:

### ✅ **Step-by-Step Instructions:**

**1. Go to the Development Branch**  
Visit the GitHub branch where the new version is actively being developed:

- [**SophiApp dev-winappsdk Branch**](https://github.com/Sophia-Community/SophiApp/tree/dev-winappsdk)

---

**2. Download the Latest Development Build**  
- Navigate to the Actions tab for the repository:  
  [**GitHub Actions for SophiApp**](https://github.com/Sophia-Community/SophiApp/actions)
- Look for the most recent successful build on the `dev-winappsdk` branch.
- Download the **Artifacts** from that build:
  - The artifact will typically be named something like **`SophiApp.zip`** or similar.

> **Note:** You'll need a GitHub account logged in to download artifacts directly from GitHub Actions.

---

**3. Extract and Run (Portable)**
- Extract the downloaded ZIP file to a folder on your computer.
- Locate and run:
  ```
  SophiApp.exe
  ```
  directly from the extracted folder.  

> **Note:** This method ensures the application remains portable, as no installation or registry modification occurs.

---

### ✅ **Alternative Easy Method (If Available):**
  
Occasionally, the developers release pre-built beta versions via standard releases:

- Check the releases page for pre-release tags:  
  [**Releases Page**](https://github.com/Sophia-Community/SophiApp/releases)

If a pre-release (beta) version tagged as **`2.0`** or **`Daria`** appears, you can directly download it as a packaged archive (`.zip`) and run it portably.

---

### ⚠️ **Important Reminders:**
- **Development Versions** might contain bugs or incomplete features.  
- It's advised to use them cautiously or for testing purposes only.
- **Always read the latest notes** on the GitHub Actions build or Telegram channel updates.

---

By following these steps, you'll successfully obtain the current portable working version of **SophiApp 2.0** for testing and usage.

## Message 10

here's the readme

<div align="right">

    This page also in:

    <a title="Русский" href="README_ru-ru.md"><img src="https://upload.wikimedia.org/wikipedia/commons/f/f3/Flag_of_Russia.svg" height="11px"/></a>

    <a title="Italiano" href="README_it-it.md"><img src="https://upload.wikimedia.org/wikipedia/commons/0/03/Flag_of_Italy.svg" height="11px"/></a>

    <a title="Українська" href="README_uk-ua.md"><img src="https://upload.wikimedia.org/wikipedia/commons/4/49/Flag_of_Ukraine.svg" height="11px"/></a>

</div>



# SophiApp. The next chapter of the [Sophia Script](https://github.com/farag2/Sophia-Script-for-Windows) project



<img src="https://raw.githubusercontent.com/Sophia-Community/SophiApp/master/img/sophiapp_big.svg" alt="Sophia Script" width='350' align="right">



<img src="https://upload.wikimedia.org/wikipedia/commons/0/05/Windows_10_Logo.svg" height="30px"/> &emsp; <img src="https://upload.wikimedia.org/wikipedia/commons/e/e6/Windows_11_logo.svg" height="30px"/> &emsp; <a href="https://pvs-studio.com/pvs-studio"><img src="https://cdn.pvs-studio.com/static/images/logo/pvs_logo_7.svg" height="50px"/></a>



<p align="left">

  <a href="https://github.com/Sophia-Community/SophiApp/actions"><img src="https://img.shields.io/github/actions/workflow/status/Sophia-Community/SophiApp/SophiApp.yml?label=GitHub%20Actions&logo=GitHub"></a> <a href="https://github.com/Sophia-Community/SophiApp/commits/main"><img src="https://img.shields.io/github/commit-activity/m/Sophia-Community/SophiApp?label=Commits&style=flat"></a>



  <a href="https://github.com/Sophia-Community/sophiapp/releases/latest"><img src="https://img.shields.io/github/v/release/Sophia-Community/SophiApp"></a>

  <a href="https://github.com/Sophia-Community/sophiapp/releases"><img src="https://img.shields.io/github/v/release/Sophia-Community/SophiApp?include_prereleases&label=pre-release&style=flat"></a>

  <a href="https://github.com/Sophia-Community/sophiapp/releases"><img src="https://img.shields.io/endpoint?url=https://gist.githubusercontent.com/farag2/c9f6b02ff4037050dcaade30c574bac7/raw/SophiApp.json"></a>



  <a href="https://github.com/Sophia-Community/sophiapp/releases"><img src="https://img.shields.io/github/downloads/Sophia-Community/sophiapp/total?label=downloads%20%28since%20September%202021%29"></a>

  <a href="https://community.chocolatey.org/packages/sophiapp"><img src="https://img.shields.io/chocolatey/dt/sophiapp?color=blue&label=chocolatey%20package"></a>



  <a href="https://www.linkedin.com/in/vladimir-nameless-132745a1"><img src="https://img.shields.io/badge/UI/UX%20by-Vladimir%20Nameless-blue?style=flat&logo=linkedin"></a>

  <a href="https://www.linkedin.com/in/наталия-гуменюк-ba4a04161"><img src="https://img.shields.io/badge/Logo%20by-Natalia-blue?style=flat&logo=linkedin"></a>

  <img src="https://img.shields.io/badge/Made%20with-149ce2.svg?color=149ce2"><img src="https://raw.githubusercontent.com/Sophia-Community/SophiApp/master/img/heart.svg" height="17px"/>



  [telegram-news-badge]: https://img.shields.io/badge/Sophia%20News-Telegram-blue?style=flat&logo=Telegram

  [telegram-news]: https://t.me/sophianews

  [telegram-group]: https://t.me/sophia_chat

  [telegram-group-badge]: https://img.shields.io/badge/Sophia%20Chat-Telegram-blue?style=flat&logo=Telegram



  [![Telegram][telegram-news-badge]][telegram-news]

  [![Telegram][telegram-group-badge]][telegram-group]



  [discord-news-badge]: https://discordapp.com/api/guilds/1006179075263561779/widget.png?style=shield

  [discord-link]: https://discord.gg/sSryhaEv79



  [![Discord][discord-news-badge]][discord-link]

</p>



<p align="left">

  <img title="English" src="https://upload.wikimedia.org/wikipedia/commons/a/a5/Flag_of_the_United_Kingdom_(1-2).svg" height="20px"/>

  &nbsp;

  <img title="中国人" src="https://upload.wikimedia.org/wikipedia/commons/f/fa/Flag_of_the_People's_Republic_of_China.svg" height="20px"/>

  &nbsp;

  <img title="Deutsch" src="https://upload.wikimedia.org/wikipedia/commons/b/ba/Flag_of_Germany.svg" height="20px"/>

  &nbsp;

  <img title="Français" src="https://upload.wikimedia.org/wikipedia/commons/c/c3/Flag_of_France.svg" height="20px"/>

  &nbsp;

  <img title="Italiano" src="https://upload.wikimedia.org/wikipedia/commons/0/03/Flag_of_Italy.svg" height="20px"/>

  &nbsp;

  <img title="Русский" src="https://upload.wikimedia.org/wikipedia/commons/f/f3/Flag_of_Russia.svg" height="20px"/>

  &nbsp;

  <img title="Українська" src="https://upload.wikimedia.org/wikipedia/commons/4/49/Flag_of_Ukraine.svg" height="20px"/>

  &nbsp;

  <img title="Türkçe" src="https://upload.wikimedia.org/wikipedia/commons/b/b4/Flag_of_Turkey.svg" height="20px"/>

  &nbsp;

  <img title="Español" src="https://upload.wikimedia.org/wikipedia/commons/9/9a/Flag_of_Spain.svg" height="20px"/>

  &nbsp;

  <img title="Português" src="https://upload.wikimedia.org/wikipedia/commons/5/5c/Flag_of_Portugal.svg" height="20px"/>

  &nbsp;

  <img title="Magyar" src="https://upload.wikimedia.org/wikipedia/commons/c/c1/Flag_of_Hungary.svg" height="20px"/>

  &nbsp;

  <img title="Polski" src="https://upload.wikimedia.org/wikipedia/commons/1/12/Flag_of_Poland.svg" height="20px"/>

</p>



***



<a href="https://github.com/Sophia-Community/SophiApp/releases/latest"><img src="https://raw.githubusercontent.com/Sophia-Community/SophiApp/refs/heads/dev-winappsdk/img/get-it-on-github.svg" width="50%" height="50%"></a>



***



<p align="center">

    •

    <a href="#donations">Donations</a>

    •

    <a href="#system-requirements">System Requirements</a>

    •

    <a href="#installation">Installation</a>

    •

    <a href="#key-features">Key features</a>

    •

    <a href="#videos">Videos</a>

    •

    <a href="#screenshots">Screenshots</a>

    •

    <a href="#addendum">Addendum</a>

    •

    <a href="#translating">Translating</a>

    •

    <a href="#media">Media</a>

    •

    <a href="https://github.com/Sophia-Community/SophiApp/blob/master/CHANGELOG.md">Changelog</a>

</p>



***



> [!CAUTION]  

> **Please avoid using Sophia 1.0.97**

>

> Sophia 1.0.97 is deprecated. Please avoid using it until `SophiApp 2.0 | Daria` will be release (2025 H2). Follow the development in a test branch [here](https://github.com/Sophia-Community/SophiApp/commits/dev-winappsdk). [Details](https://t.me/SophiaNews/3897).



## About SophiApp



![Typing SVG](https://readme-typing-svg.herokuapp.com?font=Fira+Code&size30&pause=1000&width=435&lines=Made+with+%E2%9D%A4%EF%B8%8F+of+Windows%C2%AE)



> **Note**: `SophiApp` is a free, open-source app for fine-tuning `Windows 10` & `Windows 11`. It offers a modern UI/UX, more than 130 unique tweaks, and shows how Windows can be configured without making any harm to Windows.



## Donations



<a href="https://yoomoney.ru/to/4100116615568835"><img src="https://yoomoney.ru/i/shop/iomoney_logo_color_example.png" width=220px height=46px align="left"></a>

<a href="https://ko-fi.com/farag"><img src="https://www.ko-fi.com/img/githubbutton_sm.svg" width=220px height=46px align="left"></a>



| ![ko-fi](https://img.shields.io/badge/tether-168363?style=for-the-badge&logo=tether&logoColor=white) |

|:----------------------------------------------------------------------------------------------------:|

|                                             USDT (TRC20)                                             |

|                                 `TQtMjdocUWbKAeg1kLtB4ApjAVHt1v8Rtf`                                 |



### System Requirements



|             Version             |   Marketing name    |    Build    | Arch |         Editions         |

|:-------------------------------:|:-------------------:|:-----------:|:----:|:------------------------:|

| Windows 11 Insider Preview 23H2 | 2023 Update         | 25206+      |      | Home/Pro/Enterprise      |

| Windows 11 22H2                 | 2022 Update         | 22621+      |      | Home/Pro/Enterprise      |

| Windows 11 21H2                 |                     | 22000.739+  |      | Home/Pro/Enterprise      |

| Windows 10 22H2                 | 2022 Update         | 19045.2006+ | x64  | Home/Pro/Enterprise      |

| Windows 10 21H2                 | October 2021 Update | 19044.1706+ | x64  | Home/Pro/Enterprise/LTSC |



> **Note**: Check out the [Windows 10](https://support.microsoft.com/en-us/topic/windows-10-update-history-857b8ccb-71e4-49e5-b3f6-7073197d98fb), [Windows 11](https://support.microsoft.com/topic/windows-11-update-history-a19cd327-b57f-44b9-84e0-26ced7109ba9), and [Windows 11 Insider Preview](https://docs.microsoft.com/en-us/windows-insider/flight-hub/) release history.



## Installation



### Download SophiApp via PowerShell/Chocolatey/Scoop



Download the always latest SophiApp archive by invoking (`not as administrator too`) in PowerShell



```powershell

irm app.sophi.app -useb | iex

```



[Chocolatey](https://community.chocolatey.org/packages/sophiapp)



```powershell

choco install sophiapp --confirm

```



[Scoop](https://scoop.sh/#/apps?q=sophiapp&s=2&d=1&o=true)



```powershell

scoop bucket add extras

scoop install sophiapp

```



[Beta versions](https://github.com/Sophia-Community/SophiApp/releases)



> **Note**: `SophiApp` is fully portable: it doesn't have any config (yet) and doesn't save any data into the registry. Just extract the `SophiApp` folder with `Bin` folder and `SophiApp.exe.config` file, and run `SophiApp.exe`.



***



### Warning



* It's allowed to be logged in as one admin user only during application startup.

* 🔥🔥🔥`SophiApp` may not work on a homebrew Windows. Especially, if the homebrew image was created by OS makers being all thumbs who break Microsoft Defender and disable OS telemetry by purposely uprooting system components



## Key features



* 130+ tweaks.

* Configure your Windows by officially documented methods.

* SophiApp has a dynamically rendered UI—nothing is hardcoded. 👻

* SophiApp displays the current state of every feature for your Windows.

* `SophiApp` uses the [MVVM](https://en.wikipedia.org/wiki/Model-view-viewmodel) pattern.

* Multithreading support.

* Checked by the [static analyzer](https://pvs-studio.com/pvs-studio), the license for which by courtesy of PVS-Studio.

  * Big thanks to them for providing us the [license](https://pvs-studio.com/en/order/open-source-license).

* All builds are compiled in cloud via [GitHub Actions](https://github.com/Sophia-Community/SophiApp/actions)

  * You may compare a zip archive hash sum on the release page with the hash in cloud console in the `Compress Files` category to be sure that the archive wasn't spoofed (you have to be logged into your GitHub account to be able to view Actions logs);

* High resolutions support.

* Built-in search engine.

  * Functions can be found by searching their headers and descriptions.

* Dark & light themes support.

  * The app can change its' theme instantly when you change your default Windows theme mode for apps.

* Configure Privacy & Telemetry.

* Configure UI & Personalization of your system.

* Install the latest `Microsoft Visual C++ Redistributable Packages 2015–2022 x86/x64`;

* Install the latest `.NET Desktop Runtime 7 x86/x64`;

* Uninstall OneDrive "correctly".

* Uninstall UWP apps using a dynamically rendered list of apps with localized packages names and native icons.

* Download and install the [HEVC Video Extensions from Device Manufacturer](https://www.microsoft.com/p/hevc-video-extensions-from-device-manufacturer/9n4wgh0z6vhq) to let you open .heic and .heif formats.

* Create a scheduled task to clean up Windows unused files and updates using a native toast notification.

  * A native toast notification will be displayed where you can choose to snooze, run the cleanup task or dismiss.

* Configure Windows Security.

* The ability to copy functions' descriptions or headers.

* Many unique tweaks.



## Videos



[![YT](https://img.youtube.com/vi/J0cvbVG9TGw/2.jpg)](https://www.youtube.com/watch?v=J0cvbVG9TGw&t=387s) [![YT](https://img.youtube.com/vi/CyA-oAkybFo/2.jpg)](https://www.youtube.com/watch?v=CyA-oAkybFo)



## Screenshots



![Image](https://github.com/Sophia-Community/SophiApp/raw/master/img/0.gif)

![Image](https://github.com/Sophia-Community/SophiApp/raw/master/img/1.png)

![Image](https://github.com/Sophia-Community/SophiApp/raw/master/img/2.png)

![Image](https://github.com/Sophia-Community/SophiApp/raw/master/img/3.png)



## Localized UWP packages names



![Image](https://github.com/Sophia-Community/SophiApp/raw/master/img/4.png)

![Image](https://github.com/Sophia-Community/SophiApp/raw/master/img/5.png)



## Searching feature



![Image](https://github.com/Sophia-Community/SophiApp/raw/master/img/search.gif)



## Instantly changing theme



![Image](https://github.com/Sophia-Community/SophiApp/raw/master/img/theme.gif)



### Native interactive toasts for the `Windows Cleanup` scheduled task



![Image](https://github.com/Sophia-Community/SophiApp/raw/master/img/Toasts.png)



## Addendum



* Some functions depend on Internet access. If not, they will be hidden in UI until the access appears back.

* You can enable hidden functions in UI by turning on the `Advanced settings` in the Settings.

  * The hidden functions will be marked with a gear in UI.

* After closing `SophiApp`, it creates a log file that you can attach to an open issue (or send to the [Telegram](https://t.me/sophia_chat) group) to help us understand the bug. The log file doesn't contain any sensitive personal information. We do not store any data neither in the Windows registry or any other server.

* The list of domains the app interacts with

  * <https://raw.githubusercontent.com>

  * <https://github.com>

  * <https://download.visualstudio.microsoft.com>

  * <https://builds.dotnet.microsoft.com>

  * <https://www.google.com>

  * <https://g.live.com>

  * <https://oneclient.sfx.ms>



## Translating



* Feel free to translate the UI into your language by taking as a basis one of [UIData_xx.json](https://github.com/Sophia-Community/SophiApp/tree/master/src/SophiApp/Localizations) and creating a new [.xaml](https://github.com/Sophia-Community/SophiApp/tree/master/src/SophiApp/Localizations) file.



## Media



* [![Discord](https://discordapp.com/api/guilds/1006179075263561779/widget.png?style=shield)](https://discord.gg/sSryhaEv79)

* [Telegram discussion group](https://t.me/sophia_chat)

* [Telegram channel](https://t.me/sophianews)

* [Ru-Board](https://forum.ru-board.com/topic.cgi?forum=5&topic=50903)

* [RuTracker.org](https://rutracker.org/forum/viewtopic.php?t=6218047)

* [Comss.ru](https://www.comss.ru/page.php?id=9679)

* [MajorGeeks.Com](https://www.majorgeeks.com/files/details/sophiapp.html)

* [Softpedia](https://www.softpedia.com/get/Tweak/System-Tweak/SophiApp.shtml)

* [Deskmodder.de](https://www.deskmodder.de/blog/2022/04/08/sophiapp-1-0-0-50-als-finale-version-jetzt-auch-in-deutsch)

* [Reddit](https://www.reddit.com/r/Windows11/comments/tzx74s/sophiapp_the_next_chapter_of_the_sophia_script/)

* [My Digital Life](https://forums.mydigitallife.net/threads/win32-sophiapp-for-windows-10-windows-11-1-0-0-50-x64-2022.85225/)

* [DTF](https://dtf.ru/flood/1325292-sophiapp-ili-kak-my-delali-opensors-programmu-dlya-nastroyki-windows-10-11)

* [Habr](https://habr.com/post/683452/)

## Message 11

so what do i need to do to download a portable working version of SphiaApp 2.0?

