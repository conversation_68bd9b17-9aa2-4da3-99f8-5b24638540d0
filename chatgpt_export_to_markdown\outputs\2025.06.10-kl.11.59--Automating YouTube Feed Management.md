# Automating YouTube Feed Management

Conversation ID: 68480216-1828-8008-b702-0c0818c1e3b0

## Message 1

Although the continual progression of advancements in technology (AI/LLM in particular), the progression has started to gain speed towards exponentials. In recent months and weeks the amount AI services and utilities has not only exploded, but the way they're *used* has transformed to such degree that that we're now able to *utilize their potential*. Services (such as bolt, v0, lovable, replit, etc) and autonomous coding assistants (such as cursor, vscode+augment/cline etc) has made it easy to build new tools and fullstack services that work on all devices, and with the (added standardization) inclusion of MCP's there's now established pipelines for any kind of tools and/or services.



I'm currently in the process of utilizing and leveraging these possibilities to simplify my own information feeds (such as gmail, youtube, instagram, facebook, etc) programmatically such that i can automate retrieval of things of interest, and remove things *not* of interest. My first stage in starting on this project is to initiate a simple testcase to establish a baseline that shows proof of concept and that will reveal each component in the pipeline-and the *pipeline* (itself and representations of it) will eventually be the single (and most abstract) source of truth.



In aiming for a good usecase that will help me pinpoint my needs and serve as a structure i can generalize and scaffold upon, i've decided the testcase to automation of the retrieval and filtering of youtube channels i subscribe to. This is a powerful usecase because i'll actually solve a real problem for myself; as i subscribe to more than a thousand creators and the youtube app doesn't offer any way of structuring (or automatically filter or setting up rules) to control my own feed. The effect of this/these uncontrolled (and continually growing) streams of information is that they've become so *vast* that the only way for me to draw value from it is to control it-because i can't decide upon something i never see; which i currently can't because without the ability for automation and filtering of the feed it's simply *too much information*.



My YouTube subscription management challenge represents the perfect crucible: with 1000+ creator subscriptions creating an overwhelming information deluge, YouTube's complete absence of organizational infrastructure, filtering mechanisms, or feed customization creates a fundamentally broken discovery system. This untamed torrent of content has reached critical mass where valuable information inevitably drowns beneath algorithmic chaos. Without sophisticated automation architecture, I remain blind to potentially transformative content buried within information saturation. This testcase delivers immediate tactical advantage while establishing the foundational framework I can methodically scale to dominate and restructure all information streams.



What fundamental architectural principles, technical integration requirements, and systematic orchestration questions must be definitively resolved before commencing development on this information management infrastructure that will serve as the blueprint for all future information sovereignty systems?

## Message 2

Based on your sophisticated vision for building an information sovereignty architecture starting with YouTube subscription automation, here's a **Lovable-optimized prompt** using best practices from their Prompting Bible and library 【8†source】【9†source】.

---

### 💡 Optimized Prompt for Lovable (Start in Chat Mode):

---

# Context
I'm building an automation architecture to regain control over fragmented information streams. As a first step in this system, I want to implement a test case that demonstrates filtering and retrieving YouTube content from my 1000+ subscriptions. YouTube lacks any form of native organizational tools, which creates an overwhelming, algorithm-driven feed. This project will serve both as a functional solution and a modular, reusable blueprint for automating information pipelines (across Gmail, Instagram, etc.).

# Task
Design and scaffold a YouTube subscription management tool that allows:
1. Automatic retrieval of new videos from all subscribed channels.
2. Filtering based on tags, keywords, and personalized scoring logic.
3. A UI that displays only relevant videos according to those filters.
4. Logging and visualization of filtering behavior (optional for later stages).

Start with a minimal working version that fetches and displays recent uploads from all subscribed channels (via YouTube Data API v3), and sets up the architecture to filter by channel name and keyword.

# Guidelines
- Use `Supabase` for storage and backend.
- Use `Next.js` + `Tailwind CSS` for frontend.
- Set up modular components: content fetcher, filter logic, scoring engine, UI display.
- Focus only on YouTube for now.
- Write reusable code that can later be adapted to other sources (RSS feeds, emails, etc.).
- Structure code so the **pipeline itself** (fetch → score → filter → display) is its own abstraction and can be stored/displayed.

# Constraints
- Do not include OAuth flow for now — assume static list of subscriptions from a YouTube export or pre-loaded.
- Do not fetch more than 100 items initially.
- Avoid usage of paid or proprietary libraries.
- Only create files related to this YouTube test case—no modifications to unrelated parts of the project.

---

### 💬 Follow-up Prompt Suggestions:
Once you confirm the initial setup:
- “Refactor the pipeline into isolated modules and log each stage.”
- “Implement filtering logic based on a weighted scoring system (channel, keywords, duration, etc.).”
- “Visualize the pipeline as a UI component—each stage should be a box showing current input/output.”

---

This structured prompt fits Lovable’s ideal “Training Wheels” format and uses strategies like Chat Mode planning, step-by-step architecture, and scoped constraints【8†source】【9†source】. Would you like me to generate the Knowledge Base and PRD entries for this too?

## Message 3

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message


































All posts

Published January 16, 2025 in dev lifecycle

The Lovable Prompting Bible

Author: Stephane at Lovable

Most people think prompting is just typing a request into AI and hoping for the

best. Wrong.

The difference between getting a mediocre response and having AI build

entire workflows for you comes down to how you prompt. Whether youʼre a

developer or a non-technical user, mastering prompt engineering can help

you:

Automate repetitive tasks

Debug faster with AI-generated solutions

Build and optimize workflows effortlessly

And the best part? You donʼt need to be an expert. With the right prompting

techniques, you can unlock AIʼs full potential in Lovable, make.com, and n8n—

without wasting time on trial and error.

Letʼs dive in.

TL;DR:

Effective prompting matters: Structure your prompts to save hours of

troubleshooting.

Meta prompting: Use AI itself to refine your prompts for better accuracy.

Reverse meta prompting: Save debugging sessions to optimize future

requests.

Automation tools: Extend Lovableʼs capabilities with APIs using make.com

and n8n.

Chat mode vs. default mode: When to use each for debugging and

iteration.

Handling webhooks: Automate Lovable applications with powerful

integrations.

Why Prompting Is Critical for AI Development

17 min read

The Lovable Prompting Bible

Why Prompting Is Critical for AI

Development

Mastering Prompting: The Four Levels

Prompt Library

Debugging in Lovable

Using Automation Tools Like make.com

and n8n

Last Thoughts

Share this

Support Launched Learn Sign in Sign up

https://lovable.dev/blog
https://discord.gg/lovable-dev
https://x.com/
https://lovable.dev/
https://lovable.dev/support
https://launched.lovable.dev/
https://docs.lovable.dev/
https://x.com/lovable_dev
https://www.linkedin.com/company/lovable-dev/
https://discord.gg/lovable-dev
https://lovable.dev/login
https://lovable.dev/signup


Unlike traditional coding, AI applications rely on structured communication.

Providing AI with clear context and constraints ensures high-quality output. In

a Lovable expert session at Lovable, Mark from Prompt Advisors

demonstrated how developers and non-technical users can enhance their AI

prompting techniques to build faster, debug smarter, and automate complex

workflows.

Watch on

Master Prompt Engineering – Build Smarter AI Apps with Lovable!Master Prompt Engineering – Build Smarter AI Apps with Lovable!
ShareShare

Understanding the AI s̓ "Mindset"

AI models, including those powering Lovable, do not "understand" in a human

way—they predict responses based on patterns. To guide them effectively:

Be explicit: Instead of “build a login page,” specify “create a login page

using React, with email/password authentication and JWT handling.”

Set constraints: If you need a specific tech stack (e.g., Supabase for

authentication), state it clearly.

Use formatting tricks: AI prioritizes the beginning and end of prompts—

put important details upfront.

Mastering Prompting: The Four Levels

1. Training Wheels Prompting

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://www.youtube.com/watch?v=IqWfKj4mUIo&embeds_referring_euri=https%3A%2F%2Flovable.dev%2F
https://www.youtube.com/watch?v=IqWfKj4mUIo


A structured, labeled approach for clear AI instructions:

# Context

## Task

### Guidelines

#### Constraints

Example:

You are a world-class prompt engineer. Write me a prompt that will gen

2. No Training Wheels

More conversational prompts while maintaining clarity.

3. Meta Prompting

Leverage AI to refine your prompts:

Rewrite this prompt to be more concise and detailed: 'Create a secure 

4. Reverse Meta Prompting

When debugging, have AI document the process for future use:

Summarize the errors we encountered while setting up JWT authenticatio

Prompt Library

Enhance Prompt

The quality of your prompts significantly influences the output of AI. This is the

essence of effective prompting: the more refined your prompt, the higher the

quality of the output you receive. A comprehensive and well-organized prompt

can save you both credits and time by reducing errors. Therefore, these steps

are definitely worth considering:

Provide as much details as you can in the input field.

Use the "Select" feature to precise edit your component. 

Enhance your prompt with the experimental "Chat mode". 

Starting a new project

Use this proven structure for starting a new project:

Start with "I need a [type] application with:"

Elaborate on tech stack - including Frontend, styling, Authorization a

Elaborate on core features including main and secondary features. 

Then direct the AI to start somewhere like: "Start with the main page 

However, we consistently recommend that users begin with a blank project

and gradually build upon it. This approach allows the AI to grasp the

fundamental concepts effectively before delving into the specifics.

Diff & Select

Whenever you request Lovable to implement a particular change in any file, it

will rewrite the entire file or modify the existing content. To ensure that the AI

only updates relevant files, provide clear instructions. This approach

encourages the AI to edit only the necessary sections, resulting in minimal



changes to just a few lines of code. By doing so, you can reduce loading times

and prevent error loops.

An effective prompt Iʼve applied previously when adjusting an existing feature

is:

Implement modifications to the feature while ensuring core functionali

Lock Files

Lovable currently lacks a built-in file locking system. However, you can guide

the AI with slight modifications to your prompts. Just include this instruction in

each prompt: "Please refrain from altering pages X or Y and focus changes

solely on page Z."

You can also try this prompt if you are updating an existing feature without the

intention of modifying something sensible:

This update is quite delicate and requires utmost precision. Carefully

Design

Designing something on Lovable is effective as Lovable already has great taste

;) Nevertheless, those below prompts can help you improve those design

implementations:

��� UI Changes:

Make solely visual enhancements—ensure functionality and logic 

��� Optimize for Mobile:

Enhance the app's mobile experience while preserving its exist

��� Responsiveness and Breakpoints Prompt:

Make certain that all designs are completely responsive at eve

��� Planning:

Before editing any code, create a phased plan for implementing 

Before making any code edits, develop a structured plan for implementing

responsiveness. Begin with the largest layout components and gradually work

down to smaller elements and specific components. Ensure that the plan

outlines definitive steps for testing responsiveness at all breakpoints to

guarantee consistency and a smooth user experience. Present the plan for

feedback before moving forward.

Knowledge base

https://lovable.dev/blog/%3E)%5B%5D(%3C


Providing detailed context about your project is crucial, especially early on in

the project. What is the project's purpose? What does the user flow look like?

What tech stack are you utilizing? What is the scope of work? At Lovable, we

refer to this as the "Knowledge Base," and it can be easily found in your

project settings.

Creating a solid framework for AI ensures it operates effectively and adheres

to your outlined plan with every prompt you provide. Incorporate these

elements within your project:

��� Project Requirements Document �PRD�� This section is crucial for any AI

coding project. It outlines a comprehensive summary covering essential

elements such as the introduction, app flow, core features, tech stack, and

the distinctions between in-scope and out-of-scope items. Essentially, it

serves as your project's roadmap, which you can present to AI coding

models.

��� Application or user flow: This clarity will aid the AI model in understanding

the connections between pages and processing all features and limitations

effectively.

Users begin their experience on the landing page, where they ca

��� Tech stack: This section must encompass all technical specifics regarding

the project, such as the Frontend Tech Stack, Backend Tech Stack, API

Integrations, Deployment Instructions, and any other open-source libraries

you plan to utilize. This information will facilitate the AI model's

understanding of which packages and dependencies to install.

��� Frontend guidelines: This section should outline your project's visual

appearance in detail: Design Principles, Styling Guidelines, Page Layout,

Navigation Structure, Color Palettes, and Typography. This serves as the

aesthetic foundation of your project. The clearer your explanations, the

more visually appealing your application will become.

��� Backend structure: This section will explain to AI model about: Backend

Tech like Supabase, User Authentication, Database Architecture, Storage

buckets, API Endpoints, Security measures, Hosting Solutions. This is the

main brain of your project. Your app will fetch and display data from your

backend.

Once you initiate the project with the initial prompt, be sure to incorporate this

Knowledge Base to reduce errors and prevent AI hallucinations. Additionally,

you can prompt the AI with:



Before you write any code, please review the Knowledge Base and share 

Utilize the "Chat mode" for this task to ensure that no modifications are made

to your projects while you are providing guidance.

Mobile First

The issue (and somewhat hidden truth) is that most developers prioritize

desktop design simply because it looks better on a large, vibrant screen.

However, the reality is that we should have been focusing on mobile-first

design for years now.

A great prompt that was shared by a Champion on Discord:

Always make things responsive on all breakpoints, with a focus on mobi

Use modern UI/UX best practices for determining how breakpoints should 

Use shadcn and tailwind built in breakpoints instead of anything custo

Optimize the app for mobile without changing its design or functionali

But if you're already far along into your project, you can fix this by telling it to

update things to be responsive starting with the largest layout components

down to the smallest. Then get to the individual components.

Details

When working with Lovable, itʼs crucial to provide the AI with clear and

specific requests. Rather than simply saying, "move the button to the right," try

stating, "in the top header, shift the sign-up button to the left side of the page,

ensuring the styling remains consistent." The more precise your instructions

are, the fewer errors youʼll encounter, and youʼll save on credits!

Basically, I always suggest adding instructions on how you want Lovable to

approach every task. My example:

Key Guidelines: Approach problems systematically and articulate your r

Step by Step

Avoid assigning five tasks to Lovable simultaneously! Doing so may lead the AI

to create confusion. Hereʼs a better approach:

Start with Front design, page by page, section by section. 

The plug backend using Supabase as Lovable integration is natively bui

Then, refine the UX/UI if needed. 

This step-by-step process enables AI to concentrate on one task at a time,

reducing the likelihood of errors and hallucinations.

Don't loose components

You can also implement this after significant changes and following a series of

minor adjustments. This practice has been invaluable in maintaining project

consistency and preventing sudden loss of components. Regularly refer to our

filesExplainer.md document to ensure we accurately record changes in code

and components, keeping our file structure organized and up to date.

Refactoring

Refactoring is essential to your development lifecycle within Lovable. It is often

suggested by the AI to minimize the loading time and errors. Here are great

prompts you can use:



��� Refactoring After Request Made by Lovable:

Refactor this file while ensuring that the user interface and f

��� Refactoring Planning:

Develop a comprehensive plan to refactor this file while keepi

��� Comprehensive Refactoring:

Develop a comprehensive plan for a site-wide codebase review a

��� Post Refactoring:

Conduct a detailed post-refactor review to verify that no issue

��� Codebase Structure Audit Prompt:

Perform a comprehensive regression and audit of the codebase to

��� Folder Review:

Conduct a thorough examination of the folder [Folder Name] alo

��� Post Restructuring Cleanup:

Ensure all routing and file imports are thoroughly updated and 

��� Codebase Check for Refactoring:

Perform a thorough audit of the codebase to assess its structu

Stripe

Stripe seamlessly integrates with Lovable and can be set up with minimal

effort. However, there are several factors that may hinder Stripe's functionality:

Initiate a Stripe connection in test mode using the configuration deta

�Disclaimer: Use your Stripe Secret Key and Webhook Signing Secret

securely in the Supabase Edge Function Secrets and avoid including them in

the prompt for safety.*

Ask for help

Avoid the tendency to rely on Lovable for every small change. Many minor

adjustments can be made directly within your code, even if you arenʼt a

professional engineer. If you need assistance, feel free to consult ChatGPT or

Claude for help. Utilize the browserʼs Inspect tool to identify the elements you

want to modify. You can experiment with changes at the browser level, and if

youʼre pleased with the outcome, make those adjustments in the code. This

way, you wonʼt need to involve Lovable at all.

While I'm not an engineer, having a basic understanding of coding significantly

aids my progress. Utilizing tools like GitHub and Sonnet, I frequently implement

enhancements beyond Lovable, allowing me to reserve my prompts for more

complex tasks.

Debugging in Lovable

Debugging is an integral part of the Lovable experience, and mastering this

debugging flow can significantly reduce frustration—especially when clicking

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook


the “Try to Fix” button, which does not count as credits.

Chat Mode vs. Default Mode

The new "Chat mode" is excellent for fostering creativity and generating ideas.

Begin by outlining your concept, as this could be the most critical step.

Visualizing screens, features, and layouts in your mind isnʼt as effective for

tracking changes.

A traditional scenario of using the "Chat mode" is:

Default Mode: High-level feature creation.

Review the app and tell me where there is outdated code.

Chat Mode: Troubleshooting—ask AI to analyze errors before making

changes. Go to your account settings and enable Labs feature.

Follow this plan and act on all those items

I think I've read these below super prompts from an X user then found it back

on Discord:

Perform a comprehensive regression and audit of the codebase to determ

Generate a comprehensive report that outlines key areas for enhancemen

DON'T GIVE ME HIGH-LEVEL STUFF. IF I ASK FOR A FIX OR AN EXPLANATION, 

In terms of large codebase, it's beneficial to engage with Lovable by using the

"Chat mode" to weigh the advantages and disadvantages of various

approaches. Since you're all eager to learn, try explaining your features to an

AI, encouraging it to ask clarifying questions about structure, trade-offs,

technology, and more.

It's a fact that code and features evolve continuously, reflecting the ever-

changing nature of business. Much of the code is opinionated, often crafted

with a specific vision for the future in mind. While you mentioned a steel

foundation, you might initially decide to make component X very robust while

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook


keeping component Y flexible, only to later realize that X should have been

dynamic and Y solid. This is a common scenario.

Handling Errors Effectively

Check browser developer tools �Console logs, Network requests).

Use reasoning models (e.g., GPT�4 Turbo, DeepSeek, Mistral) for

debugging.

Feed errors into AI for deeper analysis.

Debugging Prompts

To effectively address the errors you're encountering, avoid tackling them all

at once! I recommend attempting the "Try to fix" option up to three times. If the

AI is still unable to resolve the issue, try this technique: Copy the error

message and paste it into "Chat mode," then say, "Use chain-of-thought

reasoning to identify the root cause." This approach allows both the AI and you

to analyze the situation and understand the underlying issues before

transitioning to "Edit mode" for making corrections.

This guidebook was provided by a champion customer on Discord, and I

believe youʼll find it appealing:

��� Initial Investigation:

The same error continues to occur. Take a moment to perform a 

��� Deep Analysis:

The issue persists without resolution. Perform a thorough analy

��� Full System Review:

This is a pressing issue that necessitates a thorough re-evalua

��� Comprehensive Audit:

The problem continues and now calls for a comprehensive, system

��� Rethink and Rebuild:

This problem remains unresolved, and it's imperative to pause a

��� Clean up Console Logs:

Could you devise a strategy to systematically identify and elim

��� Encouragement:

Lovable, you’re doing an outstanding job, and I genuinely appre



��� Checking Complexity:

Take a moment to reflect on whether this solution can be simpl

��� Confirming Findings:

Before moving ahead, are you entirely convinced that you have 

���� Explaining Errors:

Explain the meaning of this error, its origins, and the logica

Debugging Flow

Debugging in prompt engineering involves isolating errors, analyzing

dependencies, and refining prompts to achieve the desired output. Whether

you are creating applications, integrating APIs, or building AI systems,

debugging follows a systematic flow:

��� Task Identification – Prioritize issues based on impact.

��� Internal Review – Validate solutions before deploying.

��� Reporting Issues – Clearly define current vs. expected behavior.

��� Validation – Verify changes render correctly in the DOM.

��� Breakpoints – Isolate and test specific components.

��� Error Handling & Logging – Use verbose logging and debug incrementally.

��� Code Audit – Document issues and proposed fixes before making

changes.

��� Use the 'Try to Fix' Button – Automatically detects and resolves errors in

Lovable.

��� Leverage Visuals – Upload screenshots to clarify UI-based errors.

���� Revert to Stable Version – Use the 'Revert' button to go back if needed.

Understanding 'Unexpected Behavior'

Sometimes, your code runs without errors, but your app isnʼt functioning as

expected. This is known as Unexpected Behavior, and it can be tricky to

debug. Strategies include:

Retracing Your Steps – Review what you initially asked Lovable to do.

Breaking It Down – Identify if specific sections are misaligned.

Using Images – Show Lovable the UI result versus the intended outcome.

Writing Better Prompts to Avoid Errors

A well-structured prompt reduces debugging time. Use this best practice

format:

Project Overview – Describe what youʼre building.

Page Structure – List key pages and components.

Navigation Logic – Explain user movement through the app.

Screenshots/Wireframes – Provide visuals if available.

Implementation Order – Follow a logical sequence, e.g.:



Create pages before integrating the database

Debugging Strategies in Lovable

1. Using Developer Tools for Debugging

Console Logs – Review error logs and DevTools notifications.

Breakpoints – Pause execution to inspect state changes.

Network Requests – Validate data flow between frontend and backend.

2. Common Debugging Scenarios

Minor Errors – Investigate thoroughly before making changes.

Persistent Errors – Stop changes and re-examine dependencies.

Major Errors – If necessary, rebuild the flow from scratch while

documenting findings.

3. Advanced Troubleshooting

If the 'Try to Fix' button isnʼt resolving your issue, consider:

Being More Specific – Describe the problem in detail, including expected

vs. actual results.

Using Images – Screenshots help AI understand UI-based issues.

Asking Lovable for Debugging Help – Example:

What solutions have been tried so far? What else can be done?

Reverting to a Previous Working State – If debugging leads to more

issues, roll back to a known good version.

4. Debugging Specific Issues

UI-related problems: Upload screenshots and ask,

Why is this UI behaving this way? What’s the best fix?

API integration issues: Ensure youʼre using the latest API schema and that

backend connections are correctly set up.

When completely stuck: Prompt Lovable with:

Analyze the error and suggest an alternative approach.

Debugging doesnʼt have to be frustrating. Lovable provides powerful tools to

auto-fix errors, analyze problems, and iterate efficiently. By following

structured prompting techniques, using images, and leveraging AI-driven

debugging, you can overcome any coding challenge.

Using Automation Tools Like make.com and n8n

When to Use Automation

Edge Functions: Direct Supabase API calls.

make.com: Integrating external services �Slack, Stripe, CRM tools).

n8n: Self-hosted, scalable automation.

Example: Automating a Dental Consultation App



��� Create a landing page in Lovable with a form for dental issues.

��� Send data to make.com via Webhooks.

��� Use an AI API (e.g., Perplexity AI) for live research.

��� Determine eligibility using Mistral or GPT�4 reasoning models.

https://chatgpt.com/g/g-67aa992a22188191a57023d5f96afed2�

lovable-visual-editor

https://chatgpt.com/g/g-67aa992a22188191a57023d5f96afed2�

lovable-visual-editor

��� Return a response to Lovable with recommended next steps.

Webhooks and API Calls: Advanced Use Cases

Validate responses: Ensure correct processing of webhook responses.

Test incrementally: Send minimal data first before building complex API

workflows.

Use reasoning models: Debug errors by asking AI to analyze incorrect

responses.

Last Thoughts

Mastering prompt engineering isnʼt just about better AI interactions—itʼs about

boosting efficiency, reducing development cycles, and unlocking new

automation possibilities. Whether youʼre debugging existing workflows,

optimizing AI outputs, or integrating complex automations, structured

prompting helps you get there faster and with fewer headaches.

Focus on your big ideas—Lovable and automation tools will handle the

execution. Whether you're a seasoned developer refining 15-year-old code or

a non-technical user crafting innovative applications, the right prompting

strategy is your most powerful tool.

Additional resources exist:

Prompt Engineering on our documentation.

Troubleshooting guide on our documentation.

Prompts guides you can use for integrations.

Good luck!

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://docs.lovable.dev/tips-tricks/prompting
https://docs.lovable.dev/tips-tricks/troubleshooting
https://docs.lovable.dev/integrations/prompt-integrations


Product

About

Solutions

Blog

Careers

Changelog

Hire a Partner

Become a Partner

Roadmap

Resources

Builder Hall of Fame

Learn

Integrations

Support

Launched

Legal

Privacy Policy

Terms & Conditions

Report Abuse

https://lovable.dev/
https://twitter.com/lovable_dev
https://www.linkedin.com/company/lovable-dev/
https://discord.gg/lovable-dev
https://lovable.dev/gpt-engineer
https://lovable.dev/solutions
https://lovable.dev/blog
https://lovable.dev/careers
https://docs.lovable.dev/changelog
https://lovable.dev/partners
https://lovable.dev/partners/apply
https://feedback.lovable.dev/
https://lovable.dev/hall-of-fame
https://docs.lovable.dev/
https://docs.lovable.dev/integrations/introduction
https://lovable.dev/support
https://launched.lovable.dev/
https://lovable.dev/privacy
https://lovable.dev/terms
https://lovable.dev/abuse



## Message 4

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message


































Prompt Engineering

Prompting 1.1
Prompt structure, levels of prompting, meta/reverse meta prompting, and

foundational tactics with examples.

Heads up!

To help you make the most out of Lovable, we compiled a list of prompting

strategies and approaches. Some of these were collected from our teamʼs

experience, and others were shared with us by our community members. Since

Lovable relies on large language models �LLMs�, effective prompting strategies can

significantly improve its efficiency and accuracy.

What is Prompting?

Prompting refers to the textual instructions you give an AI system to perform a task .

In Lovable (an AI-powered app builder), prompts are how you “tell” the AI what to

do – from creating a UI to writing backend logic. Effective prompting is critical

because Lovable uses large language models �LLMs�, so clear, well-crafted

prompts can greatly improve the AIʼs efficiency and accuracy in building your app .

In short, better prompts lead to better results.

Structuring Effective Prompts

For consistent outcomes, it helps to structure your prompt into clear sections. A

recommended format (like “training wheels” for prompting) uses labeled sections

for Context, Task, Guidelines, and Constraints :

By structuring your prompt, you reduce ambiguity and help the AI focus.

Remember to put the most important details up front – AI models tend to pay extra

attention to the beginning and end of your prompt. And if you need a specific tech

or approach, state it explicitly (for instance, if you require Supabase for auth, say

so) .

The Four Levels of Prompting

Prompting is a skill you can develop. Think of it as progressing through levels, from

very guided prompts to more advanced techniques 

Watch on

Master Prompt Engineering – Build Smarter AI Apps with LMaster Prompt Engineering – Build Smarter AI Apps with L……
ShareShare

Context: Give background or the bigger picture. Example: “Weʼre building a

project management tool for tracking tasks and deadlines.” This sets the stage

for the AI.

Task: State exactly what you want done now. Example: “Create the UI for the

project creation page.”

Guidelines: Specify how to approach the task or any preferences. Example:

“Use a clean design, following Material UI principles, and ensure itʼs mobile-

responsive.”

Constraints: Declare any hard limits or must-nots. Example: “Do not use any

paid libraries, and do not alter the login page code.”

On this page

Heads up!

What is Prompting?

Structuring Effective Prompts

The Four Levels of Prompting

Training Wheels Prompting

No Training Wheels

Meta Prompting

Reverse Meta Prompting

Additional Prmpting tips

Be specific, avoid vagueness

Incremental prompting

Include Constraints and

Requirements

Avoid ambiguity in wording

Mind your tone and courtesy

Use formatting to your advantage

Leverage examples or references

Using image prompts

Feedback integration

Emphasizing Accessibility

Predefined Components and

Libraries

Multilingual Prompting

Defining Project Structure and File

Management

Applying These Strategies in Different

Tools

In Lovableʼs Builder

With make.com or n8n (workflow

automation)

Edge cases and external integrations

Summary

Documentation

Community

Product Announcement

Request Feature

Youtube

Introduction

User Guides

Prompt Engineering

Integrations

Tips and Tricks

Resources

Welcome

Messaging Limits

FAQ

Quickstart

Figma to Lovable

Visual Edit

Knowledge Files

Labs

Deploy

Launched

Prompting 1.1

Prompt Library

Debugging Prompts

Prompts & Integrations

Integrations

GitHub Integration

Supabase Integration

Stripe & Payments

Resend Integration

Clerk Integration

Make Integration

Replicate Integration

Troubleshooting

Using images in Lovable

Using Videos

Chrome Adds-on

Using Custom �Google� Fonts

Using npm packages

SEO

Changelog

Video tutorial

Community

Search or ask... Ctrl K Support App

https://www.youtube.com/watch?v=IqWfKj4mUIo&embeds_referring_euri=https%3A%2F%2Fdocs.lovable.dev%2F
https://www.youtube.com/watch?v=IqWfKj4mUIo
https://docs.lovable.dev/introduction
https://discord.gg/lovable-dev
https://lovable.dev/blog?category=announcements
https://feedback.lovable.dev/
https://www.youtube.com/@lovable-labs/
https://docs.lovable.dev/introduction
https://docs.lovable.dev/user-guides/messaging-limits
https://docs.lovable.dev/faq
https://docs.lovable.dev/user-guides/quickstart
https://docs.lovable.dev/features/figma-to-lovable
https://docs.lovable.dev/features/visual-edit
https://docs.lovable.dev/features/knowledge
https://docs.lovable.dev/features/labs
https://docs.lovable.dev/features/deploy
https://docs.lovable.dev/features/launched
https://docs.lovable.dev/tips-tricks/prompting-one
https://docs.lovable.dev/tips-tricks/prompting-library
https://docs.lovable.dev/tips-tricks/prompting-debugging
https://docs.lovable.dev/integrations/prompt-integrations
https://docs.lovable.dev/integrations/introduction
https://docs.lovable.dev/integrations/git-integration
https://docs.lovable.dev/integrations/supabase
https://docs.lovable.dev/tips-tricks/setting-up-payments
https://docs.lovable.dev/integrations/resend
https://docs.lovable.dev/integrations/clerk
https://docs.lovable.dev/integrations/make
https://docs.lovable.dev/integrations/replicate
https://docs.lovable.dev/tips-tricks/troubleshooting
https://docs.lovable.dev/tips-tricks/using-images
https://docs.lovable.dev/tips-tricks/using-videos
https://docs.lovable.dev/integrations/chrome-extensions
https://docs.lovable.dev/tips-tricks/custom-fonts
https://docs.lovable.dev/features/npm-packages
https://docs.lovable.dev/tips-tricks/seo
https://docs.lovable.dev/changelog
https://docs.lovable.dev/user-guides/video-tutorials
https://docs.lovable.dev/tips-tricks/community
https://docs.lovable.dev/
mailto:<EMAIL>
https://lovable.dev/


Training Wheels Prompting

This is a highly structured approach, great for beginners. You clearly label sections

(as above) to ensure nothing is missed . It might feel verbose, but it leaves little

room for misunderstanding.

Example �Training Wheels prompt):

Context

We are developing an e-commerce platform for eco-friendly products.

Task

Create a product listing page with filters for category and price.

Guidelines

Make the UI clean and modern, using Tailwind CSS for styling. Include sample

product data for testing.

Constraints

Use Supabase for the database. Do not include any payment functionality yet.

This prompt gives the AI context about the project, the specific task, guidance on

style/tech, and constraints on scope. Itʼs explicit and easy for the AI to follow.

No Training Wheels

Once youʼre comfortable, you can drop the section labels and write in a more

conversational tone – but still remain clear and organized . Essentially, youʼll include

the same information (context, task, etc.) but in paragraph form. This feels more

natural while still guiding the AI.

Example �No Training Wheels prompt):

Iʼm building an e-commerce web app with a Supabase backend. Right now, I need

you to create a product listing page for eco-friendly products. It should have a

clean, modern UI (using Tailwind CSS) with filters for category and price. Please

make sure to use dummy data for now, and donʼt add any payment features yet.

This reads more like how youʼd explain the task to a colleague, but itʼs still specific

about the requirements and limitations (notice we still mentioned the tech stack

and the “no payments” constraint, just without formal headings).

Meta Prompting

Meta prompting means asking the AI to help improve your prompts. You use AI on

itself. For example, you might provide a draft prompt and then ask, “Can you

rewrite this prompt to be more concise and detailed?”. This leverages the AIʼs

strength in language to refine your instructions before execution. Itʼs like getting a

second pair of eyes on your prompt.

Use meta prompting when you feel your instruction could be better but youʼre not

sure how to improve it. The AI might respond with a cleaned-up, more precise

version of your request. You can then use that improved prompt to get the actual

work done. 

Example �Meta prompt request):

User: Rewrite this prompt to be more clear and specific – _“Create a secure login

page in React using Supabase.”_ 

AI (improved prompt): “Implement a secure login page in React using Supabase
authentication. Include fields for email and password, handle error messages for
failed logins, and ensure you properly store JWTs for session management.”

Here the AI elaborated the prompt, adding details (like error handling) that make

the instruction more robust. Meta prompting is a powerful way to polish your

commands.

Support Policy

Glossary

https://docs.lovable.dev/user-guides/support-policy
https://docs.lovable.dev/glossary


Reverse Meta Prompting

Reverse meta prompting flips the script: now you ask the AI to summarize what it

did and turn it into a prompt for future use . This is especially handy for debugging

or recurring tasks. After the AI solves a problem, you can have it generate a prompt

that would reproduce that solution or avoid the issue next time. Essentially, the AI

documents the process for you.

Example �Reverse Meta prompt):

After fixing an authentication bug, you might say: _“Summarize the errors we

encountered setting up JWT auth and how we resolved them. Based on that, create

a prompt I can use next time to set up authentication correctly.”_ . The AI could

output a concise recap of the bug and a step-by-step prompt for avoiding it in the

future. This turns lessons learned into reusable prompts.

Reverse meta prompting is great for building your own library of “recipes” – the AI

helps you formalize solutions so you can apply them again.

Additional Prmpting tips

Be specific, avoid vagueness

Vague prompts lead to vague results. Always clarify what you want and how.

DONʼT:

Another example:

DO:

The latter gives clear direction on scope and expected outcome.

Another example:

Incremental prompting

Itʼs usually best to tackle complex projects in pieces rather than one giant prompt.

Lovable responds well to an iterative approach.

DONʼT:

DO:

This step-by-step progression   helps the AI stay focused and accurate, and you can

catch issues early:

Make this app better.

Create a form for user input

Refactor the app to clean up unused components and improve perfor

Create a user registration form with fields for username, email, 

Build a CRM app with Supabase, auth, Google Sheets export, and da



Another example:

Include Constraints and Requirements

Donʼt shy away from spelling out constraints. If something must or must not be

done, say so.

Adding constraints

Such limits keep the AI from over-engineering. Adding a constraint like a max

number of items or a performance target can focus the AI on whatʼs important .

Avoid ambiguity in wording

If a term could be interpreted in different ways, clarify it. The clearer you are, the

less the AI has to guess.

DONʼT:

DO:

The latter gives clear direction on scope and expected outcome.

Set up a Supabase-connected CRM backend.

Great! Could you please add a secure authentication flow with use

Thank you! The next step is to integrate Google Sheets to export 

Set up a database schema for user information.

Develop an API endpoint to retrieve user data please

Create a simple to-do app with a maximum of 3 tasks visible at a 

Include the ability to add, edit, and delete tasks.

Optimize this code, but ensure the UI and core functionality rema

Use at most 3 API calls for this, and ensure no external library 

The page should display a maximum of 3 tasks at a time.

Add a profile feature

Support notifications

Add a user profile page with fields X, Y, Z.



Mind your tone and courtesy

While it doesnʼt change functionality, a polite tone can sometimes yield better

results . Phrases like “please” or a respectful ask can add context and make the

prompt a bit more descriptive, which can help the AI. For example,

Please refrain from modifying the homepage, focus only on the dashboard
component.

This reads as polite, and it explicitly tells the AI what not to do. Itʼs not about the

AIʼs feelings – itʼs about packing in detail. �Plus, it never hurts to be nice \\\!�

Use formatting to your advantage

Structure lists or steps when appropriate. If you want the AI to output a list or follow

a sequence, enumerate them in the prompt. By numbering steps, you hint the AI to

respond in kind.

First, explain the approach. Second, show the code. Third, give a test
example.

Leverage examples or references

If you have a target design or code style, mention it or provide an example.

Providing an example (image or code snippet) gives the AI a concrete reference to

emulate.

Setting the context

Another example:

Another example:

Send an email notification on form submission.

Let's think through the process of setting up a secure authentica

1. What are the necessary components?

2. How should they interact?

3. Provide the implementation code.

We are building a project management tool that helps teams track 

This tool should have features like:

 - user authentication

 - project creation

 - task assignments

 - reporting

Now, for the first task, create the UI for project creation.

I need a CRM app with Supabase integration and a secure auth flow

We are developing an e-commerce platform focusing on eco-friendly



Using image prompts

Lovable even allows image uploads with your prompt, so you can show a design

and say “match this style”.

There are two main approaches here. The first one is a simple prompting approach.

Simple image upload prompting

You can upload an image and then add an example prompt like this:

Or, you can help AI better understand the content of the image and some additional

specifics about it. Excellent results can be achieved by adding specific instructions

to the image uploaded. While the image is worth a thousand words, adding a

couple of your own to describe desired functionality can go a long way - especially

since interactions cannot always be obvious from a static image.

Image prompting with detailed instructions

Feedback integration

Review the AIʼs output and provide specific feedback for refinements.

Emphasizing Accessibility

Encourage the generation of code that adheres to accessibility standards and

modern best practices. This ensures that the output is not only functional but also

user-friendly and compliant with accessibility guidelines.

Predefined Components and Libraries

Specify the use of certain UI libraries or components to maintain consistency and

efficiency in your project. This directs the AI to utilize specific tools, ensuring

compatibility and a uniform design language across your application.

Create and implement a UI that looks as similar as possible to th

This screenshot shows a layout issue on mobile. Adjust margins an

I want you to create the app as similar as possible to the one sh

It's essentially a kanban clone.

It should have the ability to add new cards (tickets) in each col

Feel free to use the Pangea home dnd npm package for drag-and-dro

The login form looks good, but please add validation for the emai

Generate a React component for a login form that follows accessib

Create a responsive navigation bar using the shadcn/ui library wi



Multilingual Prompting

When working in a multilingual environment, specify the desired language for both

code comments and documentation. This ensures that the generated content is

accessible to team members who speak different languages, enhancing

collaboration.

Defining Project Structure and File Management

Clearly outline the project structure, including file names and paths, to ensure

organized and maintainable code generation. This provides clarity on where new

components should reside within the project, maintaining a coherent file

organization.

Applying These Strategies in Different Tools

The prompting principles above apply not just in Lovableʼs chat, but anywhere you

interact with AI or automation tools:

In Lovable s̓ Builder

Youʼll primarily use these prompts in the Lovable chat interface to build and refine

your app.

��� Start with a broad project prompt, then iterate feature by feature.

��� Use Chat-Only mode when you need to discuss or debug without changing

code.

With make.com or n8n (workflow automation)

You might not prompt these platforms in natural language the same way, but

designing an automation still benefits from clear AI instructions.

For instance, you can have Lovable generate integration logic:

When a form is submitted, send the data to a Make.com webhook for Slack
notification.

In fact, Lovable can help set up automation by integrating with webhooks. If your

app needs to hand off tasks (like sending emails, updating a CRM�, you can prompt

Lovable to use Make or n8n.

After a user signs up in the app, trigger a Make.com workflow that creates a record
in Salesforce.

Lovable will write the code to call that webhook or API. Keeping the prompt

structured ensures the AI knows exactly how to connect Lovable with those

external services.

Generate a Python script that calculates the Fibonacci sequence. 

Create a new React component named 'UserProfile' and save it as 

http://make.com/
http://make.com/
http://make.com/
http://make.com/
http://make.com/


Was this page helpful? Yes No

Launched Prompt Library

Powered by Mintlify

Edge cases and external integrations

Lovable integrates with many services �Stripe, GitHub, Supabase, etc.). When

prompting for these, treat the integration details as part of your

Context/Constraints. For example,

Connect the form to Stripe (test mode) for payments. On success, redirect to /thank-
you.

Be clear about what external services should do. The same goes for using n8n

(self-hosted automation) – you might write,

Send a POST request to the n8n webhook URL after form submission, and wait for
its response to show a confirmation message.

Clarity here is key so the AI produces the correct calls.

Summary

Happy prompting\\\!

Strong prompting is about clarity, structure, and context. Whether youʼre

telling Lovable to build a feature, or orchestrating a Make.com scenario, the

goal is to paint a picture of what you want.

Start with structured prompts if youʼre unsure, and evolve to more

conversational style as you gain confidence.

Use meta techniques to improve and learn from each interaction.

With practice, youʼll guide the AI like an extension of your dev team – and it will

feel natural to get exactly the output you need.

https://docs.lovable.dev/features/launched
https://docs.lovable.dev/tips-tricks/prompting-library
https://x.com/lovable_dev
https://github.com/lovablelabs
https://linkedin.com/company/lovable-dev
https://mintlify.com/preview-request?utm_campaign=poweredBy&utm_medium=docs&utm_source=docs.lovable.dev
http://make.com/



## Message 5

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message


































Prompt Engineering

Prompt Library
List of prompting strategies and approaches.

Welcome to the prompt library\! Here weʼve collected a set of reusable prompt

patterns and examples for common scenarios when building with AI. Think of these

as templates or inspiration that you can tailor to your own project. Each section

covers a particular use case – from kicking off a new project to integrating

payments – with guidance on when to use it and an example prompt.

Feel free to copy these, modify the details, and use them in Lovable or any AI

builder. The tone is official yet casual – just like talking to a colleague – and each

prompt provides enough context so the AI knows exactly what to do.

Starting Projects

When to use: At the very beginning of a project. This prompt helps the AI

understand the high-level requirements and start building the foundation. Use it to

kick off a new app by specifying what youʼre building, the tech stack, and core

features. Itʼs your project brief.

How to use: Outline the type of application, key technologies (frontend framework,

backend, any services), and the primary features or pages. Then, direct the AI on

where to start (often the main page or an important feature). This establishes the

project scope and initial focus.

Example Prompt – Starting a New Project:

This prompt follows a proven structure for new projects . It first states the app type

and tech stack, then lists core features, and finally tells the AI where to begin (the

main dashboard page, with specifics). By doing this, you give Lovable a clear

roadmap to initiate the project. �Pro tip: Itʼs often wise to start with an empty project

and build up gradually, so the AI doesnʼt get overwhelmed .)

UI/UX Design

When to use: Any time you want to refine the look and feel of your app without

changing its functionality. This could be polishing the UI, adjusting layouts, or

implementing a specific design style.

How to use: Clearly specify the scope of the design changes and emphasize that

functionality should remain intact. The AI is quite good at styling, but you should

guide it on what “look” you want (e.g. modern, minimalist, match a certain design

system). If you have multiple changes, tackle them one at a time (e.g. first layout,

then colors). Always mention if there are parts of the UI that must not be altered

logic-wise.

Lovable has pretty good taste out of the box, but a targeted prompt can help

achieve a specific aesthetic or UX improvement . For example, you might want to

restyle a button, improve form layout, or ensure consistency in spacing.

Example Prompt – UI Only Changes:

I need a **task management** application with:

- **Tech Stack:** Next.js frontend, Tailwind CSS for styling, Supabase fo

- **Core Features:** Project and task creation, assigning tasks to users,

Start by building the **main dashboard page**, containing:

- A header with navigation,

- A list of projects with their status,

- and a button to create a new project.

Provide dummy data for now, and ensure the design is clean and responsive

On this page

Starting Projects

UI/UX Design

Responsiveness

Refactoring

Locking Files / Limiting Scope

Planning

Stripe Setup

Using Chat Mode vs Default Mode

Writing Knowledge Bases and PRDs

Documentation

Community

Product Announcement

Request Feature

Youtube

Introduction

User Guides

Prompt Engineering

Integrations

Tips and Tricks

Resources

Welcome

Messaging Limits

FAQ

Quickstart

Figma to Lovable

Visual Edit

Knowledge Files

Labs

Deploy

Launched

Prompting 1.1

Prompt Library

Debugging Prompts

Prompts & Integrations

Integrations

GitHub Integration

Supabase Integration

Stripe & Payments

Resend Integration

Clerk Integration

Make Integration

Replicate Integration

Troubleshooting

Using images in Lovable

Using Videos

Chrome Adds-on

Using Custom �Google� Fonts

Using npm packages

SEO

Changelog

Video tutorial

Community

Search or ask... Ctrl K Support App

https://docs.lovable.dev/introduction
https://discord.gg/lovable-dev
https://lovable.dev/blog?category=announcements
https://feedback.lovable.dev/
https://www.youtube.com/@lovable-labs/
https://docs.lovable.dev/introduction
https://docs.lovable.dev/user-guides/messaging-limits
https://docs.lovable.dev/faq
https://docs.lovable.dev/user-guides/quickstart
https://docs.lovable.dev/features/figma-to-lovable
https://docs.lovable.dev/features/visual-edit
https://docs.lovable.dev/features/knowledge
https://docs.lovable.dev/features/labs
https://docs.lovable.dev/features/deploy
https://docs.lovable.dev/features/launched
https://docs.lovable.dev/tips-tricks/prompting-one
https://docs.lovable.dev/tips-tricks/prompting-library
https://docs.lovable.dev/tips-tricks/prompting-debugging
https://docs.lovable.dev/integrations/prompt-integrations
https://docs.lovable.dev/integrations/introduction
https://docs.lovable.dev/integrations/git-integration
https://docs.lovable.dev/integrations/supabase
https://docs.lovable.dev/tips-tricks/setting-up-payments
https://docs.lovable.dev/integrations/resend
https://docs.lovable.dev/integrations/clerk
https://docs.lovable.dev/integrations/make
https://docs.lovable.dev/integrations/replicate
https://docs.lovable.dev/tips-tricks/troubleshooting
https://docs.lovable.dev/tips-tricks/using-images
https://docs.lovable.dev/tips-tricks/using-videos
https://docs.lovable.dev/integrations/chrome-extensions
https://docs.lovable.dev/tips-tricks/custom-fonts
https://docs.lovable.dev/features/npm-packages
https://docs.lovable.dev/tips-tricks/seo
https://docs.lovable.dev/changelog
https://docs.lovable.dev/user-guides/video-tutorials
https://docs.lovable.dev/tips-tricks/community
https://docs.lovable.dev/
mailto:<EMAIL>
https://lovable.dev/


In this prompt, we explicitly say to make solely visual enhancements and not affect

how the app works . This is crucial – it tells the AI “donʼt touch the logic.” We list

specifics (card design, color contrast, spacing) so the AI knows what aspects of

the UI to tweak. This kind of prompt is perfect after youʼve built features and want

to beautify the interface.

Responsiveness

When to use: When your appʼs layout needs to work across different screen sizes

(mobile, tablet, desktop). If you notice things look good on desktop but break on

mobile, itʼs time for a responsiveness prompt. Itʼs also wise to do this as a final pass

on any UI-heavy task.

How to use: Emphasize a mobile-first approach and ask the AI to ensure the design

is responsive at all standard breakpoints . If using a CSS framework like Tailwind,

mention to use its grid/flex and built-in breakpoints. You can also instruct the AI to

avoid any fixed widths or anything that would prevent fluid resizing. Providing an

example of what breaks on small screens (if you have one) can help, or simply say

“make everything adapt to smaller screens gracefully.”

��Example Prompt – Mobile Responsiveness:** 

In this prompt, we explicitly instruct the AI to make all designs responsive at every

breakpoint, focusing on mobile first . We even reference Tailwindʼs standard

breakpoints to guide the implementation. We clarify that the design and

functionality shouldnʼt fundamentally change; it should just work well on smaller

screens. This sets a clear expectation: the outcome should look the same design-

wise, but fluidly resize and re-stack for responsiveness.

�Using Lovableʼs image upload? You could attach a screenshot of a broken mobile
layout and ask: “Make it look like this on mobile.” Visual prompts can reinforce what
you describe.)

Refactoring

When to use: Periodically during development, especially if the AI or you have

added a lot of code and things are getting messy or slow. Refactoring means

cleaning up the code without changing what it does – improving structure,

readability, or performance. Lovable might even suggest refactoring if it detects a lot

of repeated patterns or long functions.

How to use: Identify the scope: is it a single file, a specific feature, or the whole

codebase? For a single file or component, you can prompt something like “Refactor

this file for clarity and efficiency, but do not alter its functionality or output.”

Emphasize that everything should behave the same after refactoring . If you want,

specify what to focus on (e.g., reduce duplication, improve variable names, simplify

logic). For larger-scale refactoring, itʼs wise to ask the AI to plan the refactor in steps

(see the next section on Planning) or audit the code structure first.

The app UI should be improved, **without changing any functionality**. 

- Keep all existing logic and state management as is.

- **Visual Enhancements:** Update the styling of the dashboard page: use 

- Ensure these changes do **not break any functionality or data flow**.

*Goal:* purely cosmetic improvements for a more polished look, with the a

Our app needs to be **fully responsive** across mobile, tablet, and deskt

- Follow a **mobile-first** strategy: prioritize the layout for small scr

- Use modern UI/UX best practices for responsive design. (For Tailwind CS

- Ensure every page (especially the dashboard and project detail pages) r

- **Do not change the core design or functionality**, just make sure it f

After making changes, please double-check the layout at iPhone 12 dimensi

Support Policy

Glossary

https://docs.lovable.dev/user-guides/support-policy
https://docs.lovable.dev/glossary


��Example Prompt – Safe File Refactor:** 

This prompt clearly states the component to refactor and the constraints (no

functional changes allowed). It prioritizes structure and maintainability . The AI will

go through the file, maybe reordering functions, renaming things for clarity,

commenting tricky parts, etc., but the output of the app should remain identical.

This helps prevent the dreaded scenario of a “refactor” accidentally breaking

something.

For bigger refactoring efforts (like many files or an entire project), consider having

the AI analyze the codebase first. You can use a prompt to get a report on what

could be improved and where (see the Debugging sectionʼs Full System Review

prompt for an idea). Then, apply changes incrementally. Refactor in small pieces

and test as you go, rather than one massive overhaul.

Locking Files / Limiting Scope

When to use: Sometimes you want the AI to focus on specific parts of the project

and leave everything else untouched – essentially “lock” certain files or areas so

they are not modified. This is useful if youʼve manually written some code or have a

stable component you donʼt want altered while the AI works on something else.

Since Lovable doesnʼt have a literal file-lock feature yet, using the prompt to

constrain scope is the next best thing.

How to use: In your prompt, explicitly instruct the AI not to change certain files or

components. You might say, “Donʼt edit the authentication files,” or “Keep the

HomePage component unchanged.” Also, be clear about where the AI should focus

changes. This directive should be included each time you prompt during that

sensitive period, to remind the AI of the boundary.

Example Prompt – Limit Scope of Changes:

Here we included a very direct constraint: _“refrain from altering pages X or Y and

focus changes solely on page Z.”_ . By repeating this in the prompt, we guide the

AIʼs attention. The task itself (adding a dashboard section) is given, but we

wrapped it with instructions about scope. This greatly reduces the chance of

Lovable tinkering with your login system while trying to add a dashboard feature.

Another scenario is when updating a very delicate feature. In such cases, you can

combine scope limitation with a cautionary tone. For example: “This update is

sensitive; proceed very carefully and avoid touching anything unrelated”. This was

demonstrated in a prompt like: _“This update is quite delicate… Steer clear of

shortcuts or assumptions — take a moment to seek clarification if unsure. Precision

is crucial.”_ . Including a line like that sets the AIʼs “mindset” to be extra cautious.

Planning

Refactor the **ProjectList component file**, but **keep its behavior and 

Goals:

- Improve the code structure and readability (simplify complex functions,

- Remove any unused variables or imports.

- Ensure the file follows best practices and is well-documented.

Do **not** introduce any new features or change how the component works f

Please **focus only on the Dashboard page** for this change. 

- Do **not modify** the `LoginPage.tsx` or `AuthProvider.tsx` files at al

- Concentrate your code edits on `Dashboard.tsx` and related dashboard co

Task: Add a new section to the Dashboard that shows “Tasks due this week”

*(Again, no changes to login or auth files – those are off-limits.)*



When to use: Before diving into a complex or multi-step implementation, or when

you have a big feature that could be broken into sub-tasks. Planning prompts are

also useful if you want the AI to outline an approach before writing code, so you can

verify the plan (and adjust it) without burning through code-generation credits on a

wrong path. Essentially, use this when the strategy isnʼt straightforward and youʼd

like the AIʼs help to think it through.

How to use: Ask the AI to produce a plan or checklist. You can say, “Outline a step-

by-step plan for X” or “Before coding, list the steps you will take to implement Y.”

This can be done in Chat mode to ensure it doesnʼt execute any code changes while

planning . After getting the plan, you might even discuss it (maybe have the AI

explain why each step is needed) and then proceed to implementation step by step.

Planning prompts are meta – they donʼt build the app directly, but they set the stage

for a smoother build.

Example Prompt – Planning a Feature Implementation:

This prompt tells the AI to act as a planner. It asks for a sequenced plan to

implement an “email notifications for overdue tasks” feature. We explicitly say not

to code yet (so weʼd run this in Chat mode or just trust that the AI will output a

plan). The AI might respond with something like:

��� Add a timestamp field to tasks for due date (if not already present).

��� Create a server-side function (or scheduled job) to check for overdue tasks

periodically.

��� Integrate email sending using an email service (e.g., Resend or SMTP) when

an overdue task is found.

��� Update the UI to allow users to toggle notifications on/off for a task (optional

setting).

��� Test the flow with a task that just passed its due time to ensure an email is

sent.

By reviewing such a plan, you can catch any issues (maybe we realize we need a

new DB table, or maybe step 4 is out-of-scope for now, etc.) before any coding

happens. Itʼs a lot easier to tweak the plan than to rewrite bad code. Planning

prompts save time in complex features by getting the approach right from the start .

Stripe Setup

When to use: When you want to integrate payments into your app using Stripe.

Lovable has integration points for Stripe, but it requires setting up keys, webhooks,

and UI for checkout. A prompt can handle the boilerplate of connecting to Stripeʼs

API. Use this when you need to add commerce (selling a product, subscription, etc.)

in your project.

How to use: Provide the details Stripe needs: mode (test or live), product or pricing

info, and redirect URLs after payment. Also, instruct how the UI should behave (e.g.,

a checkout form/modal). Itʼs crucial to mention that sensitive keys will be provided

securely (not hard-coded in the prompt)  – you typically store those in environment

variables or Lovableʼs secret storage. So you can say “assume I have set the API

keys in the environment.” This way, the AI will know to call the keys, not include

them literally. Additionally, specify not to alter unrelated code while setting up Stripe

(to avoid accidental changes).

��Example Prompt – Integrating Stripe Payments:** 

Before writing any code, **plan out the implementation** of the new Notif

- List each step required to add email notifications when a task is overd

- Consider both frontend (UI changes, if any) and backend (creating sched

- Ensure the plan keeps the current functionality stable – we can’t break

- Provide the plan as an ordered list (1, 2, 3, ...), with a brief explan

Once you outline the plan, pause for review. **Do not make any code chang

I want to **add Stripe payments** to the app.

- Use **Stripe in test mode** for now.



This prompt gives all the key details for Stripe: test mode, product IDs, what

happens on success/cancel, and where to put the checkout button. It explicitly

says not to touch anything else. The AI (and Lovableʼs Stripe integration helper) will

use this to scaffold the Stripe integration. Under the hood, Lovable might create a

serverless function (if using Supabase) to handle webhooks, etc., but you donʼt

have to prompt that separately – the instruction here is usually enough for a basic

setup .

Note: We included a line about API keys being secure because we never want

secret keys in the prompt. The docs remind us: _“Use your Stripe Secret Key in the

Supabase Edge Function secrets, and avoid including them in the prompt”_ . So by

telling the AI “assume itʼs configured,” you ensure the code will reference an

environment variable or config, not a plaintext key.

After running this prompt, test the payment flow with Stripeʼs test card numbers. If

something isnʼt working (e.g., the webhook), Lovable might show errors which you

can then debug or refine with another prompt.

Using Chat Mode vs Default Mode

When to use: Lovable has two modes for prompting: Default Mode (which

immediately applies changes to your project) and Chat-Only Mode (which is more

like a conversation without altering code until you say so). Knowing when to use

each can streamline your workflow. Use Default for straight coding tasks and use

Chat mode for brainstorming, debugging, or when you want to discuss changes

before executing them.

How to use:

Example use case – Default vs Chat:

Suppose you suspect there is outdated code in your project that needs cleaning

up. In Default mode, you might directly prompt:

Review the app and tell me where there is outdated code.

Lovable could try to both identify and possibly start refactoring it in one go . But

maybe you actually want to be careful and just get advice. In that case, youʼd

switch to Chat mode and ask something like:

Iʼm seeing some deprecated library warnings. What parts of the code might be
outdated, and how should we update them?

Now the AI will discuss this with you, rather than immediately rewriting files.

Similarly, if you have an error and you want the AI to analyze it, Chat mode is safer.

You can copy an error message and ask:

- We have a product in Stripe with ID `prod_12345` and a price ID `price_

- Implement a checkout button on the **Pricing page** that starts a Strip

- After successful payment, redirect the user to `/payment-success`. If t

Important:

- Assume API keys and webhook secrets are configured securely (do **not**

- Do **not** modify any other pages or features unrelated to payments.

Once done, provide any webhook endpoint setup instructions I need (e.g., 

Default Mode is great for when you have a well-defined feature to build or

change to make. You give the instruction, and Lovable will do it in one go if

possible.

Chat Mode, on the other hand, is useful if you want to have a back-and-forth or

analyze something (like asking “Why is this not working?” or “Whatʼs the best
way to do X?”) without immediately changing the codebase . In Chat Mode, the

AI will respond with analysis or a plan, and you usually have to explicitly say “go

ahead and implement” when ready.



What does this error mean, and how can we fix it?

The AI will explain and propose a solution. Once you agree, you might switch to

Default mode or explicitly instruct to apply the fix.

In summary, use Default Mode for straightforward building (“do X for me”), and

use Chat Mode for troubleshooting or design discussions (“why or how should we

do X?”). One concrete workflow is: Brainstorm in Chat (get a plan or identify

issues), then execute in Default. This approach is even recommended: for example,

_use Chat Mode to have the AI analyze errors before making changes_ . Itʼs like

using ChatGPT for advice and then applying the changes once youʼre confident.

�Make sure Chat-Only Mode is enabled in your settings under Labs if you donʼt see

it . This mode ensures the AI wonʼt write code to your project until you say so,

which is perfect for safe experimentation.)

Writing Knowledge Bases and PRDs

When to use: At the start of a project and whenever you have more context to give

the AI than can fit in a single prompt. In Lovable, the Knowledge Base is a special

place in your project settings where you can store background information,

requirements, and guidelines that persist across prompts . A Project Requirements

Document �PRD� is a comprehensive summary of your appʼs objectives and specs –

essentially, the blueprint of what youʼre building. Use these tools to prevent

misunderstanding and to anchor the AI with the big picture of your project.

How to use: Begin by filling out the Knowledge Base in Lovable with key info about

your project. This can include:

This might sound like a lot, but writing it down pays off. The AI will use this info on

every prompt to stay aligned with your projectʼs needs . Itʼs like giving the AI long-

term memory about your app. You typically set this up once (and update as

needed).

After populating the knowledge base, you can literally tell the AI to use it. For

example, your very first prompt to Lovable might be: _“Before writing any code,

please review the Knowledge Base and confirm you understand the project.”_  This

lets the AI summarize or acknowledge the context, and you can catch if it missed

something.

Example Prompt – Using the Knowledge Base:

This kind of summary from the AI confirms it has ingested your specifications

correctly. If it misunderstood anything, you can correct it in Chat mode. Once

confirmed, you proceed with building (the AI now “knows” the overall plan).

Writing a PRD and filling the Knowledge Base might feel like extra upfront work, but

it pays dividends throughout development. It reduces errors and AI hallucinations

because the AI always has a reference for what the app should do  . Itʼs especially

helpful for larger projects that canʼt be built in one or two prompts. Think of it as

seeding the AI with the same understanding you have in your head.

Overview & Goals: What the project is and the problem it solves.

User Flow: A description of how users navigate or use the app (e.g. “Users sign

up, then see a dashboard, can create tasks, etc.”) .

Core Features & Scope: What features are in scope (and maybe note any out-

of-scope to avoid feature creep) .

Tech Stack: What technologies, APIs, and services will be used .

Design Guidelines: Any specific UI/UX instructions – branding, color scheme,

layouts, accessibility requirements .

Backend Details: Outline data models, key database tables, integrations like

Stripe or Supabase, etc. ..

User: Before you write any code, please review the Knowledge Base and sha

AI (likely response): “Sure. Based on the Knowledge Base: You’re building



Was this page helpful? Yes No

Prompting 1.1 Debugging Prompts

Powered by Mintlify

Lastly, maintain the Knowledge Base as your project evolves. If you add a major

feature or change the scope, update the PRD/Knowledge Base document. This

way, future prompts will consider the new information. Itʼs much easier than re-

explaining context every time. In essence, the Knowledge Base + PRD is your AI

project handbook – it keeps everyone (you and the AI) on the same page about

what youʼre building and how.

https://docs.lovable.dev/tips-tricks/prompting-one
https://docs.lovable.dev/tips-tricks/prompting-debugging
https://x.com/lovable_dev
https://github.com/lovablelabs
https://linkedin.com/company/lovable-dev
https://mintlify.com/preview-request?utm_campaign=poweredBy&utm_medium=docs&utm_source=docs.lovable.dev



## Message 6

All the files uploaded by the user have been fully loaded. Searching won't provide additional information.

## Message 7

Although the continual progression of advancements in technology (AI/LLM in particular), the progression has started to gain speed towards exponentials. In recent months and weeks the amount AI services and utilities has not only exploded, but the way they're *used* has transformed to such degree that that we're now able to *utilize their potential*. Services (such as bolt, v0, lovable, replit, etc) and autonomous coding assistants (such as cursor, vscode+augment/cline etc) has made it easy to build new tools and fullstack services that work on all devices, and with the (added standardization) inclusion of MCP's there's now established pipelines for any kind of tools and/or services.



I'm currently in the process of utilizing and leveraging these possibilities to simplify my own information feeds (such as gmail, youtube, instagram, facebook, etc) programmatically such that i can automate retrieval of things of interest, and remove things *not* of interest. My first stage in starting on this project is to initiate a simple testcase to establish a baseline that shows proof of concept and that will reveal each component in the pipeline-and the *pipeline* (itself and representations of it) will eventually be the single (and most abstract) source of truth.



In aiming for a good usecase that will help me pinpoint my needs and serve as a structure i can generalize and scaffold upon, i've decided the testcase to automation of the retrieval and filtering of youtube channels i subscribe to. This is a powerful usecase because i'll actually solve a real problem for myself; as i subscribe to more than a thousand creators and the youtube app doesn't offer any way of structuring (or automatically filter or setting up rules) to control my own feed. The effect of this/these uncontrolled (and continually growing) streams of information is that they've become so *vast* that the only way for me to draw value from it is to control it-because i can't decide upon something i never see; which i currently can't because without the ability for automation and filtering of the feed it's simply *too much information*.



My YouTube subscription management challenge represents the perfect crucible: with 1000+ creator subscriptions creating an overwhelming information deluge, YouTube's complete absence of organizational infrastructure, filtering mechanisms, or feed customization creates a fundamentally broken discovery system. This untamed torrent of content has reached critical mass where valuable information inevitably drowns beneath algorithmic chaos. Without sophisticated automation architecture, I remain blind to potentially transformative content buried within information saturation. This testcase delivers immediate tactical advantage while establishing the foundational framework I can methodically scale to dominate and restructure all information streams.



What fundamental architectural principles, technical integration requirements, and systematic orchestration questions must be definitively resolved before commencing development on this information management infrastructure that will serve as the blueprint for all future information sovereignty systems?

## Message 8

To proceed with your YouTube channel management pipeline—and eventually generalize it into a full-fledged "information sovereignty system"—you’ll need to address three foundational categories:

---

### 🧱 **1. Architectural Principles**
These form the strategic basis of your system and must be settled before implementation.

#### a. **Source of Truth Design**
- Should you implement a **central knowledge base** (à la Lovable’s PRD+Knowledge Base model【11†source】)?
- What’s the minimal metadata needed to tag, prioritize, and audit each data flow (channel, rule, result)?
- Will your orchestration layer be declarative (rules-as-data) or imperative (procedural logic)?

#### b. **Modularity and Composability**
- Can you break the pipeline into **reusable blocks**: ingestion, filtering, ranking, enrichment, display?
- Use **separation of concern** design: e.g., avoid tight coupling between YouTube scraping and preference ranking.

#### c. **Event-Driven or Scheduled?**
- Will it react to new videos in real-time (e.g. Pub/Sub with YouTube APIs or Webhooks)?
- Or batch-process and re-rank feeds on a schedule?

#### d. **Human-in-the-loop design**
- How much automation vs. oversight? E.g. Should auto-filtered channels still display a digest for human override?

---

### ⚙️ **2. Technical Integration Requirements**
To make the system programmable, maintainable, and future-proof:

#### a. **APIs and SDKs**
- Use YouTube Data API v3 for channel/video metadata.
- Consider integrating `make.com` or `n8n` for orchestrating multi-platform workflows【14†source】.

#### b. **Storage Layer**
- Choose a scalable backend: Supabase, Firebase, or PostgreSQL.
- Decide whether filtering and prioritization rules live in the DB, environment config, or code.

#### c. **Frontend Interaction Layer**
- Will you visualize ranked content via a dashboard? Consider Lovable's visual editor + Supabase combo for this.

#### d. **Authentication & Permissions**
- Will the system require login/auth (e.g., OAuth2 for YouTube)? If so, how do you persist session tokens securely?

#### e. **Standards and MCP Pipelines**
- If you're aiming for MCP (Modular Composable Pipelines), define interfaces early: e.g., every module should accept `{input[], config}` and return `{output[], meta}` to maintain interoperability.

---

### 🔁 **3. Systematic Orchestration Questions**
How will the whole machine coordinate over time?

#### a. **Feedback Looping**
- How will you track what worked or didn't? Do you plan to reverse-meta-prompt your own system for improvement?【15†source】

#### b. **Prompting as Interface**
- Will you use structured prompting as the core UX layer for editing filters or building workflows? If so, adopt Lovable’s **Training Wheels format**:
  - `# Context`, `## Task`, `### Guidelines`, `#### Constraints`【12†source】

#### c. **Error Handling and Observability**
- How do you handle edge cases: failed API requests, channel deletions, unexpected formats?
- Should logs/metrics be stored for review or fed back into prompt-based debugging (as per Lovable's debugging loop)?【14†source】

#### d. **Workflow Interruption and Restart**
- If part of the pipeline fails, does the system retry? Skip? Pause for input?
- Consider n8n-style step checkpoints and Lovable's step-by-step prompting advice to isolate issues without corrupting state【12†source】.

---

### ✅ Next Step Prompt (for Lovable)
Here’s how you might initiate this project in Lovable using a structured prompt:

---

**Prompt:**

> # Context  
> I'm building a personal information management system that automates the retrieval and filtering of YouTube channels I subscribe to, targeting 1000+ subscriptions. The goal is to declutter my YouTube feed and surface only content that matches my evolving preferences.  
> 
> ## Task  
> Build the first module: an ingestion + filter UI that imports all subscriptions via YouTube API and allows me to apply tag-based filters to include/exclude content creators.  
> 
> ### Guidelines  
> Use Supabase for data storage, TailwindCSS for styling, and Lovable's chat mode for planning. Provide a dashboard interface for visualizing channels and their metadata (title, category, upload frequency, tags).  
> 
> #### Constraints  
> Do not integrate real-time push features yet. Do not implement video content views—this version only filters creators. Assume API keys are securely stored.

---

Would you like me to format this as a full Lovable project setup prompt with a Knowledge Base definition too?

## Message 9

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message


































All posts

Published January 16, 2025 in dev lifecycle

The Lovable Prompting Bible

Author: Stephane at Lovable

Most people think prompting is just typing a request into AI and hoping for the

best. Wrong.

The difference between getting a mediocre response and having AI build

entire workflows for you comes down to how you prompt. Whether youʼre a

developer or a non-technical user, mastering prompt engineering can help

you:

Automate repetitive tasks

Debug faster with AI-generated solutions

Build and optimize workflows effortlessly

And the best part? You donʼt need to be an expert. With the right prompting

techniques, you can unlock AIʼs full potential in Lovable, make.com, and n8n—

without wasting time on trial and error.

Letʼs dive in.

TL;DR:

Effective prompting matters: Structure your prompts to save hours of

troubleshooting.

Meta prompting: Use AI itself to refine your prompts for better accuracy.

Reverse meta prompting: Save debugging sessions to optimize future

requests.

Automation tools: Extend Lovableʼs capabilities with APIs using make.com

and n8n.

Chat mode vs. default mode: When to use each for debugging and

iteration.

Handling webhooks: Automate Lovable applications with powerful

integrations.

Why Prompting Is Critical for AI Development

17 min read

The Lovable Prompting Bible

Why Prompting Is Critical for AI

Development

Mastering Prompting: The Four Levels

Prompt Library

Debugging in Lovable

Using Automation Tools Like make.com

and n8n

Last Thoughts

Share this

Support Launched Learn Sign in Sign up

https://lovable.dev/blog
https://discord.gg/lovable-dev
https://x.com/
https://lovable.dev/
https://lovable.dev/support
https://launched.lovable.dev/
https://docs.lovable.dev/
https://x.com/lovable_dev
https://www.linkedin.com/company/lovable-dev/
https://discord.gg/lovable-dev
https://lovable.dev/login
https://lovable.dev/signup


Unlike traditional coding, AI applications rely on structured communication.

Providing AI with clear context and constraints ensures high-quality output. In

a Lovable expert session at Lovable, Mark from Prompt Advisors

demonstrated how developers and non-technical users can enhance their AI

prompting techniques to build faster, debug smarter, and automate complex

workflows.

Watch on

Master Prompt Engineering – Build Smarter AI Apps with Lovable!Master Prompt Engineering – Build Smarter AI Apps with Lovable!
ShareShare

Understanding the AI s̓ "Mindset"

AI models, including those powering Lovable, do not "understand" in a human

way—they predict responses based on patterns. To guide them effectively:

Be explicit: Instead of “build a login page,” specify “create a login page

using React, with email/password authentication and JWT handling.”

Set constraints: If you need a specific tech stack (e.g., Supabase for

authentication), state it clearly.

Use formatting tricks: AI prioritizes the beginning and end of prompts—

put important details upfront.

Mastering Prompting: The Four Levels

1. Training Wheels Prompting

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://www.youtube.com/watch?v=IqWfKj4mUIo&embeds_referring_euri=https%3A%2F%2Flovable.dev%2F
https://www.youtube.com/watch?v=IqWfKj4mUIo


A structured, labeled approach for clear AI instructions:

# Context

## Task

### Guidelines

#### Constraints

Example:

You are a world-class prompt engineer. Write me a prompt that will gen

2. No Training Wheels

More conversational prompts while maintaining clarity.

3. Meta Prompting

Leverage AI to refine your prompts:

Rewrite this prompt to be more concise and detailed: 'Create a secure 

4. Reverse Meta Prompting

When debugging, have AI document the process for future use:

Summarize the errors we encountered while setting up JWT authenticatio

Prompt Library

Enhance Prompt

The quality of your prompts significantly influences the output of AI. This is the

essence of effective prompting: the more refined your prompt, the higher the

quality of the output you receive. A comprehensive and well-organized prompt

can save you both credits and time by reducing errors. Therefore, these steps

are definitely worth considering:

Provide as much details as you can in the input field.

Use the "Select" feature to precise edit your component. 

Enhance your prompt with the experimental "Chat mode". 

Starting a new project

Use this proven structure for starting a new project:

Start with "I need a [type] application with:"

Elaborate on tech stack - including Frontend, styling, Authorization a

Elaborate on core features including main and secondary features. 

Then direct the AI to start somewhere like: "Start with the main page 

However, we consistently recommend that users begin with a blank project

and gradually build upon it. This approach allows the AI to grasp the

fundamental concepts effectively before delving into the specifics.

Diff & Select

Whenever you request Lovable to implement a particular change in any file, it

will rewrite the entire file or modify the existing content. To ensure that the AI

only updates relevant files, provide clear instructions. This approach

encourages the AI to edit only the necessary sections, resulting in minimal



changes to just a few lines of code. By doing so, you can reduce loading times

and prevent error loops.

An effective prompt Iʼve applied previously when adjusting an existing feature

is:

Implement modifications to the feature while ensuring core functionali

Lock Files

Lovable currently lacks a built-in file locking system. However, you can guide

the AI with slight modifications to your prompts. Just include this instruction in

each prompt: "Please refrain from altering pages X or Y and focus changes

solely on page Z."

You can also try this prompt if you are updating an existing feature without the

intention of modifying something sensible:

This update is quite delicate and requires utmost precision. Carefully

Design

Designing something on Lovable is effective as Lovable already has great taste

;) Nevertheless, those below prompts can help you improve those design

implementations:

��� UI Changes:

Make solely visual enhancements—ensure functionality and logic 

��� Optimize for Mobile:

Enhance the app's mobile experience while preserving its exist

��� Responsiveness and Breakpoints Prompt:

Make certain that all designs are completely responsive at eve

��� Planning:

Before editing any code, create a phased plan for implementing 

Before making any code edits, develop a structured plan for implementing

responsiveness. Begin with the largest layout components and gradually work

down to smaller elements and specific components. Ensure that the plan

outlines definitive steps for testing responsiveness at all breakpoints to

guarantee consistency and a smooth user experience. Present the plan for

feedback before moving forward.

Knowledge base

https://lovable.dev/blog/%3E)%5B%5D(%3C


Providing detailed context about your project is crucial, especially early on in

the project. What is the project's purpose? What does the user flow look like?

What tech stack are you utilizing? What is the scope of work? At Lovable, we

refer to this as the "Knowledge Base," and it can be easily found in your

project settings.

Creating a solid framework for AI ensures it operates effectively and adheres

to your outlined plan with every prompt you provide. Incorporate these

elements within your project:

��� Project Requirements Document �PRD�� This section is crucial for any AI

coding project. It outlines a comprehensive summary covering essential

elements such as the introduction, app flow, core features, tech stack, and

the distinctions between in-scope and out-of-scope items. Essentially, it

serves as your project's roadmap, which you can present to AI coding

models.

��� Application or user flow: This clarity will aid the AI model in understanding

the connections between pages and processing all features and limitations

effectively.

Users begin their experience on the landing page, where they ca

��� Tech stack: This section must encompass all technical specifics regarding

the project, such as the Frontend Tech Stack, Backend Tech Stack, API

Integrations, Deployment Instructions, and any other open-source libraries

you plan to utilize. This information will facilitate the AI model's

understanding of which packages and dependencies to install.

��� Frontend guidelines: This section should outline your project's visual

appearance in detail: Design Principles, Styling Guidelines, Page Layout,

Navigation Structure, Color Palettes, and Typography. This serves as the

aesthetic foundation of your project. The clearer your explanations, the

more visually appealing your application will become.

��� Backend structure: This section will explain to AI model about: Backend

Tech like Supabase, User Authentication, Database Architecture, Storage

buckets, API Endpoints, Security measures, Hosting Solutions. This is the

main brain of your project. Your app will fetch and display data from your

backend.

Once you initiate the project with the initial prompt, be sure to incorporate this

Knowledge Base to reduce errors and prevent AI hallucinations. Additionally,

you can prompt the AI with:



Before you write any code, please review the Knowledge Base and share 

Utilize the "Chat mode" for this task to ensure that no modifications are made

to your projects while you are providing guidance.

Mobile First

The issue (and somewhat hidden truth) is that most developers prioritize

desktop design simply because it looks better on a large, vibrant screen.

However, the reality is that we should have been focusing on mobile-first

design for years now.

A great prompt that was shared by a Champion on Discord:

Always make things responsive on all breakpoints, with a focus on mobi

Use modern UI/UX best practices for determining how breakpoints should 

Use shadcn and tailwind built in breakpoints instead of anything custo

Optimize the app for mobile without changing its design or functionali

But if you're already far along into your project, you can fix this by telling it to

update things to be responsive starting with the largest layout components

down to the smallest. Then get to the individual components.

Details

When working with Lovable, itʼs crucial to provide the AI with clear and

specific requests. Rather than simply saying, "move the button to the right," try

stating, "in the top header, shift the sign-up button to the left side of the page,

ensuring the styling remains consistent." The more precise your instructions

are, the fewer errors youʼll encounter, and youʼll save on credits!

Basically, I always suggest adding instructions on how you want Lovable to

approach every task. My example:

Key Guidelines: Approach problems systematically and articulate your r

Step by Step

Avoid assigning five tasks to Lovable simultaneously! Doing so may lead the AI

to create confusion. Hereʼs a better approach:

Start with Front design, page by page, section by section. 

The plug backend using Supabase as Lovable integration is natively bui

Then, refine the UX/UI if needed. 

This step-by-step process enables AI to concentrate on one task at a time,

reducing the likelihood of errors and hallucinations.

Don't loose components

You can also implement this after significant changes and following a series of

minor adjustments. This practice has been invaluable in maintaining project

consistency and preventing sudden loss of components. Regularly refer to our

filesExplainer.md document to ensure we accurately record changes in code

and components, keeping our file structure organized and up to date.

Refactoring

Refactoring is essential to your development lifecycle within Lovable. It is often

suggested by the AI to minimize the loading time and errors. Here are great

prompts you can use:



��� Refactoring After Request Made by Lovable:

Refactor this file while ensuring that the user interface and f

��� Refactoring Planning:

Develop a comprehensive plan to refactor this file while keepi

��� Comprehensive Refactoring:

Develop a comprehensive plan for a site-wide codebase review a

��� Post Refactoring:

Conduct a detailed post-refactor review to verify that no issue

��� Codebase Structure Audit Prompt:

Perform a comprehensive regression and audit of the codebase to

��� Folder Review:

Conduct a thorough examination of the folder [Folder Name] alo

��� Post Restructuring Cleanup:

Ensure all routing and file imports are thoroughly updated and 

��� Codebase Check for Refactoring:

Perform a thorough audit of the codebase to assess its structu

Stripe

Stripe seamlessly integrates with Lovable and can be set up with minimal

effort. However, there are several factors that may hinder Stripe's functionality:

Initiate a Stripe connection in test mode using the configuration deta

�Disclaimer: Use your Stripe Secret Key and Webhook Signing Secret

securely in the Supabase Edge Function Secrets and avoid including them in

the prompt for safety.*

Ask for help

Avoid the tendency to rely on Lovable for every small change. Many minor

adjustments can be made directly within your code, even if you arenʼt a

professional engineer. If you need assistance, feel free to consult ChatGPT or

Claude for help. Utilize the browserʼs Inspect tool to identify the elements you

want to modify. You can experiment with changes at the browser level, and if

youʼre pleased with the outcome, make those adjustments in the code. This

way, you wonʼt need to involve Lovable at all.

While I'm not an engineer, having a basic understanding of coding significantly

aids my progress. Utilizing tools like GitHub and Sonnet, I frequently implement

enhancements beyond Lovable, allowing me to reserve my prompts for more

complex tasks.

Debugging in Lovable

Debugging is an integral part of the Lovable experience, and mastering this

debugging flow can significantly reduce frustration—especially when clicking

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook


the “Try to Fix” button, which does not count as credits.

Chat Mode vs. Default Mode

The new "Chat mode" is excellent for fostering creativity and generating ideas.

Begin by outlining your concept, as this could be the most critical step.

Visualizing screens, features, and layouts in your mind isnʼt as effective for

tracking changes.

A traditional scenario of using the "Chat mode" is:

Default Mode: High-level feature creation.

Review the app and tell me where there is outdated code.

Chat Mode: Troubleshooting—ask AI to analyze errors before making

changes. Go to your account settings and enable Labs feature.

Follow this plan and act on all those items

I think I've read these below super prompts from an X user then found it back

on Discord:

Perform a comprehensive regression and audit of the codebase to determ

Generate a comprehensive report that outlines key areas for enhancemen

DON'T GIVE ME HIGH-LEVEL STUFF. IF I ASK FOR A FIX OR AN EXPLANATION, 

In terms of large codebase, it's beneficial to engage with Lovable by using the

"Chat mode" to weigh the advantages and disadvantages of various

approaches. Since you're all eager to learn, try explaining your features to an

AI, encouraging it to ask clarifying questions about structure, trade-offs,

technology, and more.

It's a fact that code and features evolve continuously, reflecting the ever-

changing nature of business. Much of the code is opinionated, often crafted

with a specific vision for the future in mind. While you mentioned a steel

foundation, you might initially decide to make component X very robust while

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook


keeping component Y flexible, only to later realize that X should have been

dynamic and Y solid. This is a common scenario.

Handling Errors Effectively

Check browser developer tools �Console logs, Network requests).

Use reasoning models (e.g., GPT�4 Turbo, DeepSeek, Mistral) for

debugging.

Feed errors into AI for deeper analysis.

Debugging Prompts

To effectively address the errors you're encountering, avoid tackling them all

at once! I recommend attempting the "Try to fix" option up to three times. If the

AI is still unable to resolve the issue, try this technique: Copy the error

message and paste it into "Chat mode," then say, "Use chain-of-thought

reasoning to identify the root cause." This approach allows both the AI and you

to analyze the situation and understand the underlying issues before

transitioning to "Edit mode" for making corrections.

This guidebook was provided by a champion customer on Discord, and I

believe youʼll find it appealing:

��� Initial Investigation:

The same error continues to occur. Take a moment to perform a 

��� Deep Analysis:

The issue persists without resolution. Perform a thorough analy

��� Full System Review:

This is a pressing issue that necessitates a thorough re-evalua

��� Comprehensive Audit:

The problem continues and now calls for a comprehensive, system

��� Rethink and Rebuild:

This problem remains unresolved, and it's imperative to pause a

��� Clean up Console Logs:

Could you devise a strategy to systematically identify and elim

��� Encouragement:

Lovable, you’re doing an outstanding job, and I genuinely appre



��� Checking Complexity:

Take a moment to reflect on whether this solution can be simpl

��� Confirming Findings:

Before moving ahead, are you entirely convinced that you have 

���� Explaining Errors:

Explain the meaning of this error, its origins, and the logica

Debugging Flow

Debugging in prompt engineering involves isolating errors, analyzing

dependencies, and refining prompts to achieve the desired output. Whether

you are creating applications, integrating APIs, or building AI systems,

debugging follows a systematic flow:

��� Task Identification – Prioritize issues based on impact.

��� Internal Review – Validate solutions before deploying.

��� Reporting Issues – Clearly define current vs. expected behavior.

��� Validation – Verify changes render correctly in the DOM.

��� Breakpoints – Isolate and test specific components.

��� Error Handling & Logging – Use verbose logging and debug incrementally.

��� Code Audit – Document issues and proposed fixes before making

changes.

��� Use the 'Try to Fix' Button – Automatically detects and resolves errors in

Lovable.

��� Leverage Visuals – Upload screenshots to clarify UI-based errors.

���� Revert to Stable Version – Use the 'Revert' button to go back if needed.

Understanding 'Unexpected Behavior'

Sometimes, your code runs without errors, but your app isnʼt functioning as

expected. This is known as Unexpected Behavior, and it can be tricky to

debug. Strategies include:

Retracing Your Steps – Review what you initially asked Lovable to do.

Breaking It Down – Identify if specific sections are misaligned.

Using Images – Show Lovable the UI result versus the intended outcome.

Writing Better Prompts to Avoid Errors

A well-structured prompt reduces debugging time. Use this best practice

format:

Project Overview – Describe what youʼre building.

Page Structure – List key pages and components.

Navigation Logic – Explain user movement through the app.

Screenshots/Wireframes – Provide visuals if available.

Implementation Order – Follow a logical sequence, e.g.:



Create pages before integrating the database

Debugging Strategies in Lovable

1. Using Developer Tools for Debugging

Console Logs – Review error logs and DevTools notifications.

Breakpoints – Pause execution to inspect state changes.

Network Requests – Validate data flow between frontend and backend.

2. Common Debugging Scenarios

Minor Errors – Investigate thoroughly before making changes.

Persistent Errors – Stop changes and re-examine dependencies.

Major Errors – If necessary, rebuild the flow from scratch while

documenting findings.

3. Advanced Troubleshooting

If the 'Try to Fix' button isnʼt resolving your issue, consider:

Being More Specific – Describe the problem in detail, including expected

vs. actual results.

Using Images – Screenshots help AI understand UI-based issues.

Asking Lovable for Debugging Help – Example:

What solutions have been tried so far? What else can be done?

Reverting to a Previous Working State – If debugging leads to more

issues, roll back to a known good version.

4. Debugging Specific Issues

UI-related problems: Upload screenshots and ask,

Why is this UI behaving this way? What’s the best fix?

API integration issues: Ensure youʼre using the latest API schema and that

backend connections are correctly set up.

When completely stuck: Prompt Lovable with:

Analyze the error and suggest an alternative approach.

Debugging doesnʼt have to be frustrating. Lovable provides powerful tools to

auto-fix errors, analyze problems, and iterate efficiently. By following

structured prompting techniques, using images, and leveraging AI-driven

debugging, you can overcome any coding challenge.

Using Automation Tools Like make.com and n8n

When to Use Automation

Edge Functions: Direct Supabase API calls.

make.com: Integrating external services �Slack, Stripe, CRM tools).

n8n: Self-hosted, scalable automation.

Example: Automating a Dental Consultation App



��� Create a landing page in Lovable with a form for dental issues.

��� Send data to make.com via Webhooks.

��� Use an AI API (e.g., Perplexity AI) for live research.

��� Determine eligibility using Mistral or GPT�4 reasoning models.

https://chatgpt.com/g/g-67aa992a22188191a57023d5f96afed2�

lovable-visual-editor

https://chatgpt.com/g/g-67aa992a22188191a57023d5f96afed2�

lovable-visual-editor

��� Return a response to Lovable with recommended next steps.

Webhooks and API Calls: Advanced Use Cases

Validate responses: Ensure correct processing of webhook responses.

Test incrementally: Send minimal data first before building complex API

workflows.

Use reasoning models: Debug errors by asking AI to analyze incorrect

responses.

Last Thoughts

Mastering prompt engineering isnʼt just about better AI interactions—itʼs about

boosting efficiency, reducing development cycles, and unlocking new

automation possibilities. Whether youʼre debugging existing workflows,

optimizing AI outputs, or integrating complex automations, structured

prompting helps you get there faster and with fewer headaches.

Focus on your big ideas—Lovable and automation tools will handle the

execution. Whether you're a seasoned developer refining 15-year-old code or

a non-technical user crafting innovative applications, the right prompting

strategy is your most powerful tool.

Additional resources exist:

Prompt Engineering on our documentation.

Troubleshooting guide on our documentation.

Prompts guides you can use for integrations.

Good luck!

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://docs.lovable.dev/tips-tricks/prompting
https://docs.lovable.dev/tips-tricks/troubleshooting
https://docs.lovable.dev/integrations/prompt-integrations


Product

About

Solutions

Blog

Careers

Changelog

Hire a Partner

Become a Partner

Roadmap

Resources

Builder Hall of Fame

Learn

Integrations

Support

Launched

Legal

Privacy Policy

Terms & Conditions

Report Abuse

https://lovable.dev/
https://twitter.com/lovable_dev
https://www.linkedin.com/company/lovable-dev/
https://discord.gg/lovable-dev
https://lovable.dev/gpt-engineer
https://lovable.dev/solutions
https://lovable.dev/blog
https://lovable.dev/careers
https://docs.lovable.dev/changelog
https://lovable.dev/partners
https://lovable.dev/partners/apply
https://feedback.lovable.dev/
https://lovable.dev/hall-of-fame
https://docs.lovable.dev/
https://docs.lovable.dev/integrations/introduction
https://lovable.dev/support
https://launched.lovable.dev/
https://lovable.dev/privacy
https://lovable.dev/terms
https://lovable.dev/abuse



## Message 10

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message


































Prompt Engineering

Prompting 1.1
Prompt structure, levels of prompting, meta/reverse meta prompting, and

foundational tactics with examples.

Heads up!

To help you make the most out of Lovable, we compiled a list of prompting

strategies and approaches. Some of these were collected from our teamʼs

experience, and others were shared with us by our community members. Since

Lovable relies on large language models �LLMs�, effective prompting strategies can

significantly improve its efficiency and accuracy.

What is Prompting?

Prompting refers to the textual instructions you give an AI system to perform a task .

In Lovable (an AI-powered app builder), prompts are how you “tell” the AI what to

do – from creating a UI to writing backend logic. Effective prompting is critical

because Lovable uses large language models �LLMs�, so clear, well-crafted

prompts can greatly improve the AIʼs efficiency and accuracy in building your app .

In short, better prompts lead to better results.

Structuring Effective Prompts

For consistent outcomes, it helps to structure your prompt into clear sections. A

recommended format (like “training wheels” for prompting) uses labeled sections

for Context, Task, Guidelines, and Constraints :

By structuring your prompt, you reduce ambiguity and help the AI focus.

Remember to put the most important details up front – AI models tend to pay extra

attention to the beginning and end of your prompt. And if you need a specific tech

or approach, state it explicitly (for instance, if you require Supabase for auth, say

so) .

The Four Levels of Prompting

Prompting is a skill you can develop. Think of it as progressing through levels, from

very guided prompts to more advanced techniques 

Watch on

Master Prompt Engineering – Build Smarter AI Apps with LMaster Prompt Engineering – Build Smarter AI Apps with L……
ShareShare

Context: Give background or the bigger picture. Example: “Weʼre building a

project management tool for tracking tasks and deadlines.” This sets the stage

for the AI.

Task: State exactly what you want done now. Example: “Create the UI for the

project creation page.”

Guidelines: Specify how to approach the task or any preferences. Example:

“Use a clean design, following Material UI principles, and ensure itʼs mobile-

responsive.”

Constraints: Declare any hard limits or must-nots. Example: “Do not use any

paid libraries, and do not alter the login page code.”

On this page

Heads up!

What is Prompting?

Structuring Effective Prompts

The Four Levels of Prompting

Training Wheels Prompting

No Training Wheels

Meta Prompting

Reverse Meta Prompting

Additional Prmpting tips

Be specific, avoid vagueness

Incremental prompting

Include Constraints and

Requirements

Avoid ambiguity in wording

Mind your tone and courtesy

Use formatting to your advantage

Leverage examples or references

Using image prompts

Feedback integration

Emphasizing Accessibility

Predefined Components and

Libraries

Multilingual Prompting

Defining Project Structure and File

Management

Applying These Strategies in Different

Tools

In Lovableʼs Builder

With make.com or n8n (workflow

automation)

Edge cases and external integrations

Summary

Documentation

Community

Product Announcement

Request Feature

Youtube

Introduction

User Guides

Prompt Engineering

Integrations

Tips and Tricks

Resources

Welcome

Messaging Limits

FAQ

Quickstart

Figma to Lovable

Visual Edit

Knowledge Files

Labs

Deploy

Launched

Prompting 1.1

Prompt Library

Debugging Prompts

Prompts & Integrations

Integrations

GitHub Integration

Supabase Integration

Stripe & Payments

Resend Integration

Clerk Integration

Make Integration

Replicate Integration

Troubleshooting

Using images in Lovable

Using Videos

Chrome Adds-on

Using Custom �Google� Fonts

Using npm packages

SEO

Changelog

Video tutorial

Community

Search or ask... Ctrl K Support App

https://www.youtube.com/watch?v=IqWfKj4mUIo&embeds_referring_euri=https%3A%2F%2Fdocs.lovable.dev%2F
https://www.youtube.com/watch?v=IqWfKj4mUIo
https://docs.lovable.dev/introduction
https://discord.gg/lovable-dev
https://lovable.dev/blog?category=announcements
https://feedback.lovable.dev/
https://www.youtube.com/@lovable-labs/
https://docs.lovable.dev/introduction
https://docs.lovable.dev/user-guides/messaging-limits
https://docs.lovable.dev/faq
https://docs.lovable.dev/user-guides/quickstart
https://docs.lovable.dev/features/figma-to-lovable
https://docs.lovable.dev/features/visual-edit
https://docs.lovable.dev/features/knowledge
https://docs.lovable.dev/features/labs
https://docs.lovable.dev/features/deploy
https://docs.lovable.dev/features/launched
https://docs.lovable.dev/tips-tricks/prompting-one
https://docs.lovable.dev/tips-tricks/prompting-library
https://docs.lovable.dev/tips-tricks/prompting-debugging
https://docs.lovable.dev/integrations/prompt-integrations
https://docs.lovable.dev/integrations/introduction
https://docs.lovable.dev/integrations/git-integration
https://docs.lovable.dev/integrations/supabase
https://docs.lovable.dev/tips-tricks/setting-up-payments
https://docs.lovable.dev/integrations/resend
https://docs.lovable.dev/integrations/clerk
https://docs.lovable.dev/integrations/make
https://docs.lovable.dev/integrations/replicate
https://docs.lovable.dev/tips-tricks/troubleshooting
https://docs.lovable.dev/tips-tricks/using-images
https://docs.lovable.dev/tips-tricks/using-videos
https://docs.lovable.dev/integrations/chrome-extensions
https://docs.lovable.dev/tips-tricks/custom-fonts
https://docs.lovable.dev/features/npm-packages
https://docs.lovable.dev/tips-tricks/seo
https://docs.lovable.dev/changelog
https://docs.lovable.dev/user-guides/video-tutorials
https://docs.lovable.dev/tips-tricks/community
https://docs.lovable.dev/
mailto:<EMAIL>
https://lovable.dev/


Training Wheels Prompting

This is a highly structured approach, great for beginners. You clearly label sections

(as above) to ensure nothing is missed . It might feel verbose, but it leaves little

room for misunderstanding.

Example �Training Wheels prompt):

Context

We are developing an e-commerce platform for eco-friendly products.

Task

Create a product listing page with filters for category and price.

Guidelines

Make the UI clean and modern, using Tailwind CSS for styling. Include sample

product data for testing.

Constraints

Use Supabase for the database. Do not include any payment functionality yet.

This prompt gives the AI context about the project, the specific task, guidance on

style/tech, and constraints on scope. Itʼs explicit and easy for the AI to follow.

No Training Wheels

Once youʼre comfortable, you can drop the section labels and write in a more

conversational tone – but still remain clear and organized . Essentially, youʼll include

the same information (context, task, etc.) but in paragraph form. This feels more

natural while still guiding the AI.

Example �No Training Wheels prompt):

Iʼm building an e-commerce web app with a Supabase backend. Right now, I need

you to create a product listing page for eco-friendly products. It should have a

clean, modern UI (using Tailwind CSS) with filters for category and price. Please

make sure to use dummy data for now, and donʼt add any payment features yet.

This reads more like how youʼd explain the task to a colleague, but itʼs still specific

about the requirements and limitations (notice we still mentioned the tech stack

and the “no payments” constraint, just without formal headings).

Meta Prompting

Meta prompting means asking the AI to help improve your prompts. You use AI on

itself. For example, you might provide a draft prompt and then ask, “Can you

rewrite this prompt to be more concise and detailed?”. This leverages the AIʼs

strength in language to refine your instructions before execution. Itʼs like getting a

second pair of eyes on your prompt.

Use meta prompting when you feel your instruction could be better but youʼre not

sure how to improve it. The AI might respond with a cleaned-up, more precise

version of your request. You can then use that improved prompt to get the actual

work done. 

Example �Meta prompt request):

User: Rewrite this prompt to be more clear and specific – _“Create a secure login

page in React using Supabase.”_ 

AI (improved prompt): “Implement a secure login page in React using Supabase
authentication. Include fields for email and password, handle error messages for
failed logins, and ensure you properly store JWTs for session management.”

Here the AI elaborated the prompt, adding details (like error handling) that make

the instruction more robust. Meta prompting is a powerful way to polish your

commands.

Support Policy

Glossary

https://docs.lovable.dev/user-guides/support-policy
https://docs.lovable.dev/glossary


Reverse Meta Prompting

Reverse meta prompting flips the script: now you ask the AI to summarize what it

did and turn it into a prompt for future use . This is especially handy for debugging

or recurring tasks. After the AI solves a problem, you can have it generate a prompt

that would reproduce that solution or avoid the issue next time. Essentially, the AI

documents the process for you.

Example �Reverse Meta prompt):

After fixing an authentication bug, you might say: _“Summarize the errors we

encountered setting up JWT auth and how we resolved them. Based on that, create

a prompt I can use next time to set up authentication correctly.”_ . The AI could

output a concise recap of the bug and a step-by-step prompt for avoiding it in the

future. This turns lessons learned into reusable prompts.

Reverse meta prompting is great for building your own library of “recipes” – the AI

helps you formalize solutions so you can apply them again.

Additional Prmpting tips

Be specific, avoid vagueness

Vague prompts lead to vague results. Always clarify what you want and how.

DONʼT:

Another example:

DO:

The latter gives clear direction on scope and expected outcome.

Another example:

Incremental prompting

Itʼs usually best to tackle complex projects in pieces rather than one giant prompt.

Lovable responds well to an iterative approach.

DONʼT:

DO:

This step-by-step progression   helps the AI stay focused and accurate, and you can

catch issues early:

Make this app better.

Create a form for user input

Refactor the app to clean up unused components and improve perfor

Create a user registration form with fields for username, email, 

Build a CRM app with Supabase, auth, Google Sheets export, and da



Another example:

Include Constraints and Requirements

Donʼt shy away from spelling out constraints. If something must or must not be

done, say so.

Adding constraints

Such limits keep the AI from over-engineering. Adding a constraint like a max

number of items or a performance target can focus the AI on whatʼs important .

Avoid ambiguity in wording

If a term could be interpreted in different ways, clarify it. The clearer you are, the

less the AI has to guess.

DONʼT:

DO:

The latter gives clear direction on scope and expected outcome.

Set up a Supabase-connected CRM backend.

Great! Could you please add a secure authentication flow with use

Thank you! The next step is to integrate Google Sheets to export 

Set up a database schema for user information.

Develop an API endpoint to retrieve user data please

Create a simple to-do app with a maximum of 3 tasks visible at a 

Include the ability to add, edit, and delete tasks.

Optimize this code, but ensure the UI and core functionality rema

Use at most 3 API calls for this, and ensure no external library 

The page should display a maximum of 3 tasks at a time.

Add a profile feature

Support notifications

Add a user profile page with fields X, Y, Z.



Mind your tone and courtesy

While it doesnʼt change functionality, a polite tone can sometimes yield better

results . Phrases like “please” or a respectful ask can add context and make the

prompt a bit more descriptive, which can help the AI. For example,

Please refrain from modifying the homepage, focus only on the dashboard
component.

This reads as polite, and it explicitly tells the AI what not to do. Itʼs not about the

AIʼs feelings – itʼs about packing in detail. �Plus, it never hurts to be nice \\\!�

Use formatting to your advantage

Structure lists or steps when appropriate. If you want the AI to output a list or follow

a sequence, enumerate them in the prompt. By numbering steps, you hint the AI to

respond in kind.

First, explain the approach. Second, show the code. Third, give a test
example.

Leverage examples or references

If you have a target design or code style, mention it or provide an example.

Providing an example (image or code snippet) gives the AI a concrete reference to

emulate.

Setting the context

Another example:

Another example:

Send an email notification on form submission.

Let's think through the process of setting up a secure authentica

1. What are the necessary components?

2. How should they interact?

3. Provide the implementation code.

We are building a project management tool that helps teams track 

This tool should have features like:

 - user authentication

 - project creation

 - task assignments

 - reporting

Now, for the first task, create the UI for project creation.

I need a CRM app with Supabase integration and a secure auth flow

We are developing an e-commerce platform focusing on eco-friendly



Using image prompts

Lovable even allows image uploads with your prompt, so you can show a design

and say “match this style”.

There are two main approaches here. The first one is a simple prompting approach.

Simple image upload prompting

You can upload an image and then add an example prompt like this:

Or, you can help AI better understand the content of the image and some additional

specifics about it. Excellent results can be achieved by adding specific instructions

to the image uploaded. While the image is worth a thousand words, adding a

couple of your own to describe desired functionality can go a long way - especially

since interactions cannot always be obvious from a static image.

Image prompting with detailed instructions

Feedback integration

Review the AIʼs output and provide specific feedback for refinements.

Emphasizing Accessibility

Encourage the generation of code that adheres to accessibility standards and

modern best practices. This ensures that the output is not only functional but also

user-friendly and compliant with accessibility guidelines.

Predefined Components and Libraries

Specify the use of certain UI libraries or components to maintain consistency and

efficiency in your project. This directs the AI to utilize specific tools, ensuring

compatibility and a uniform design language across your application.

Create and implement a UI that looks as similar as possible to th

This screenshot shows a layout issue on mobile. Adjust margins an

I want you to create the app as similar as possible to the one sh

It's essentially a kanban clone.

It should have the ability to add new cards (tickets) in each col

Feel free to use the Pangea home dnd npm package for drag-and-dro

The login form looks good, but please add validation for the emai

Generate a React component for a login form that follows accessib

Create a responsive navigation bar using the shadcn/ui library wi



Multilingual Prompting

When working in a multilingual environment, specify the desired language for both

code comments and documentation. This ensures that the generated content is

accessible to team members who speak different languages, enhancing

collaboration.

Defining Project Structure and File Management

Clearly outline the project structure, including file names and paths, to ensure

organized and maintainable code generation. This provides clarity on where new

components should reside within the project, maintaining a coherent file

organization.

Applying These Strategies in Different Tools

The prompting principles above apply not just in Lovableʼs chat, but anywhere you

interact with AI or automation tools:

In Lovable s̓ Builder

Youʼll primarily use these prompts in the Lovable chat interface to build and refine

your app.

��� Start with a broad project prompt, then iterate feature by feature.

��� Use Chat-Only mode when you need to discuss or debug without changing

code.

With make.com or n8n (workflow automation)

You might not prompt these platforms in natural language the same way, but

designing an automation still benefits from clear AI instructions.

For instance, you can have Lovable generate integration logic:

When a form is submitted, send the data to a Make.com webhook for Slack
notification.

In fact, Lovable can help set up automation by integrating with webhooks. If your

app needs to hand off tasks (like sending emails, updating a CRM�, you can prompt

Lovable to use Make or n8n.

After a user signs up in the app, trigger a Make.com workflow that creates a record
in Salesforce.

Lovable will write the code to call that webhook or API. Keeping the prompt

structured ensures the AI knows exactly how to connect Lovable with those

external services.

Generate a Python script that calculates the Fibonacci sequence. 

Create a new React component named 'UserProfile' and save it as 

http://make.com/
http://make.com/
http://make.com/
http://make.com/
http://make.com/


Was this page helpful? Yes No

Launched Prompt Library

Powered by Mintlify

Edge cases and external integrations

Lovable integrates with many services �Stripe, GitHub, Supabase, etc.). When

prompting for these, treat the integration details as part of your

Context/Constraints. For example,

Connect the form to Stripe (test mode) for payments. On success, redirect to /thank-
you.

Be clear about what external services should do. The same goes for using n8n

(self-hosted automation) – you might write,

Send a POST request to the n8n webhook URL after form submission, and wait for
its response to show a confirmation message.

Clarity here is key so the AI produces the correct calls.

Summary

Happy prompting\\\!

Strong prompting is about clarity, structure, and context. Whether youʼre

telling Lovable to build a feature, or orchestrating a Make.com scenario, the

goal is to paint a picture of what you want.

Start with structured prompts if youʼre unsure, and evolve to more

conversational style as you gain confidence.

Use meta techniques to improve and learn from each interaction.

With practice, youʼll guide the AI like an extension of your dev team – and it will

feel natural to get exactly the output you need.

https://docs.lovable.dev/features/launched
https://docs.lovable.dev/tips-tricks/prompting-library
https://x.com/lovable_dev
https://github.com/lovablelabs
https://linkedin.com/company/lovable-dev
https://mintlify.com/preview-request?utm_campaign=poweredBy&utm_medium=docs&utm_source=docs.lovable.dev
http://make.com/



## Message 11

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message


































Prompt Engineering

Prompt Library
List of prompting strategies and approaches.

Welcome to the prompt library\! Here weʼve collected a set of reusable prompt

patterns and examples for common scenarios when building with AI. Think of these

as templates or inspiration that you can tailor to your own project. Each section

covers a particular use case – from kicking off a new project to integrating

payments – with guidance on when to use it and an example prompt.

Feel free to copy these, modify the details, and use them in Lovable or any AI

builder. The tone is official yet casual – just like talking to a colleague – and each

prompt provides enough context so the AI knows exactly what to do.

Starting Projects

When to use: At the very beginning of a project. This prompt helps the AI

understand the high-level requirements and start building the foundation. Use it to

kick off a new app by specifying what youʼre building, the tech stack, and core

features. Itʼs your project brief.

How to use: Outline the type of application, key technologies (frontend framework,

backend, any services), and the primary features or pages. Then, direct the AI on

where to start (often the main page or an important feature). This establishes the

project scope and initial focus.

Example Prompt – Starting a New Project:

This prompt follows a proven structure for new projects . It first states the app type

and tech stack, then lists core features, and finally tells the AI where to begin (the

main dashboard page, with specifics). By doing this, you give Lovable a clear

roadmap to initiate the project. �Pro tip: Itʼs often wise to start with an empty project

and build up gradually, so the AI doesnʼt get overwhelmed .)

UI/UX Design

When to use: Any time you want to refine the look and feel of your app without

changing its functionality. This could be polishing the UI, adjusting layouts, or

implementing a specific design style.

How to use: Clearly specify the scope of the design changes and emphasize that

functionality should remain intact. The AI is quite good at styling, but you should

guide it on what “look” you want (e.g. modern, minimalist, match a certain design

system). If you have multiple changes, tackle them one at a time (e.g. first layout,

then colors). Always mention if there are parts of the UI that must not be altered

logic-wise.

Lovable has pretty good taste out of the box, but a targeted prompt can help

achieve a specific aesthetic or UX improvement . For example, you might want to

restyle a button, improve form layout, or ensure consistency in spacing.

Example Prompt – UI Only Changes:

I need a **task management** application with:

- **Tech Stack:** Next.js frontend, Tailwind CSS for styling, Supabase fo

- **Core Features:** Project and task creation, assigning tasks to users,

Start by building the **main dashboard page**, containing:

- A header with navigation,

- A list of projects with their status,

- and a button to create a new project.

Provide dummy data for now, and ensure the design is clean and responsive

On this page

Starting Projects

UI/UX Design

Responsiveness

Refactoring

Locking Files / Limiting Scope

Planning

Stripe Setup

Using Chat Mode vs Default Mode

Writing Knowledge Bases and PRDs

Documentation

Community

Product Announcement

Request Feature

Youtube

Introduction

User Guides

Prompt Engineering

Integrations

Tips and Tricks

Resources

Welcome

Messaging Limits

FAQ

Quickstart

Figma to Lovable

Visual Edit

Knowledge Files

Labs

Deploy

Launched

Prompting 1.1

Prompt Library

Debugging Prompts

Prompts & Integrations

Integrations

GitHub Integration

Supabase Integration

Stripe & Payments

Resend Integration

Clerk Integration

Make Integration

Replicate Integration

Troubleshooting

Using images in Lovable

Using Videos

Chrome Adds-on

Using Custom �Google� Fonts

Using npm packages

SEO

Changelog

Video tutorial

Community

Search or ask... Ctrl K Support App

https://docs.lovable.dev/introduction
https://discord.gg/lovable-dev
https://lovable.dev/blog?category=announcements
https://feedback.lovable.dev/
https://www.youtube.com/@lovable-labs/
https://docs.lovable.dev/introduction
https://docs.lovable.dev/user-guides/messaging-limits
https://docs.lovable.dev/faq
https://docs.lovable.dev/user-guides/quickstart
https://docs.lovable.dev/features/figma-to-lovable
https://docs.lovable.dev/features/visual-edit
https://docs.lovable.dev/features/knowledge
https://docs.lovable.dev/features/labs
https://docs.lovable.dev/features/deploy
https://docs.lovable.dev/features/launched
https://docs.lovable.dev/tips-tricks/prompting-one
https://docs.lovable.dev/tips-tricks/prompting-library
https://docs.lovable.dev/tips-tricks/prompting-debugging
https://docs.lovable.dev/integrations/prompt-integrations
https://docs.lovable.dev/integrations/introduction
https://docs.lovable.dev/integrations/git-integration
https://docs.lovable.dev/integrations/supabase
https://docs.lovable.dev/tips-tricks/setting-up-payments
https://docs.lovable.dev/integrations/resend
https://docs.lovable.dev/integrations/clerk
https://docs.lovable.dev/integrations/make
https://docs.lovable.dev/integrations/replicate
https://docs.lovable.dev/tips-tricks/troubleshooting
https://docs.lovable.dev/tips-tricks/using-images
https://docs.lovable.dev/tips-tricks/using-videos
https://docs.lovable.dev/integrations/chrome-extensions
https://docs.lovable.dev/tips-tricks/custom-fonts
https://docs.lovable.dev/features/npm-packages
https://docs.lovable.dev/tips-tricks/seo
https://docs.lovable.dev/changelog
https://docs.lovable.dev/user-guides/video-tutorials
https://docs.lovable.dev/tips-tricks/community
https://docs.lovable.dev/
mailto:<EMAIL>
https://lovable.dev/


In this prompt, we explicitly say to make solely visual enhancements and not affect

how the app works . This is crucial – it tells the AI “donʼt touch the logic.” We list

specifics (card design, color contrast, spacing) so the AI knows what aspects of

the UI to tweak. This kind of prompt is perfect after youʼve built features and want

to beautify the interface.

Responsiveness

When to use: When your appʼs layout needs to work across different screen sizes

(mobile, tablet, desktop). If you notice things look good on desktop but break on

mobile, itʼs time for a responsiveness prompt. Itʼs also wise to do this as a final pass

on any UI-heavy task.

How to use: Emphasize a mobile-first approach and ask the AI to ensure the design

is responsive at all standard breakpoints . If using a CSS framework like Tailwind,

mention to use its grid/flex and built-in breakpoints. You can also instruct the AI to

avoid any fixed widths or anything that would prevent fluid resizing. Providing an

example of what breaks on small screens (if you have one) can help, or simply say

“make everything adapt to smaller screens gracefully.”

��Example Prompt – Mobile Responsiveness:** 

In this prompt, we explicitly instruct the AI to make all designs responsive at every

breakpoint, focusing on mobile first . We even reference Tailwindʼs standard

breakpoints to guide the implementation. We clarify that the design and

functionality shouldnʼt fundamentally change; it should just work well on smaller

screens. This sets a clear expectation: the outcome should look the same design-

wise, but fluidly resize and re-stack for responsiveness.

�Using Lovableʼs image upload? You could attach a screenshot of a broken mobile
layout and ask: “Make it look like this on mobile.” Visual prompts can reinforce what
you describe.)

Refactoring

When to use: Periodically during development, especially if the AI or you have

added a lot of code and things are getting messy or slow. Refactoring means

cleaning up the code without changing what it does – improving structure,

readability, or performance. Lovable might even suggest refactoring if it detects a lot

of repeated patterns or long functions.

How to use: Identify the scope: is it a single file, a specific feature, or the whole

codebase? For a single file or component, you can prompt something like “Refactor

this file for clarity and efficiency, but do not alter its functionality or output.”

Emphasize that everything should behave the same after refactoring . If you want,

specify what to focus on (e.g., reduce duplication, improve variable names, simplify

logic). For larger-scale refactoring, itʼs wise to ask the AI to plan the refactor in steps

(see the next section on Planning) or audit the code structure first.

The app UI should be improved, **without changing any functionality**. 

- Keep all existing logic and state management as is.

- **Visual Enhancements:** Update the styling of the dashboard page: use 

- Ensure these changes do **not break any functionality or data flow**.

*Goal:* purely cosmetic improvements for a more polished look, with the a

Our app needs to be **fully responsive** across mobile, tablet, and deskt

- Follow a **mobile-first** strategy: prioritize the layout for small scr

- Use modern UI/UX best practices for responsive design. (For Tailwind CS

- Ensure every page (especially the dashboard and project detail pages) r

- **Do not change the core design or functionality**, just make sure it f

After making changes, please double-check the layout at iPhone 12 dimensi

Support Policy

Glossary

https://docs.lovable.dev/user-guides/support-policy
https://docs.lovable.dev/glossary


��Example Prompt – Safe File Refactor:** 

This prompt clearly states the component to refactor and the constraints (no

functional changes allowed). It prioritizes structure and maintainability . The AI will

go through the file, maybe reordering functions, renaming things for clarity,

commenting tricky parts, etc., but the output of the app should remain identical.

This helps prevent the dreaded scenario of a “refactor” accidentally breaking

something.

For bigger refactoring efforts (like many files or an entire project), consider having

the AI analyze the codebase first. You can use a prompt to get a report on what

could be improved and where (see the Debugging sectionʼs Full System Review

prompt for an idea). Then, apply changes incrementally. Refactor in small pieces

and test as you go, rather than one massive overhaul.

Locking Files / Limiting Scope

When to use: Sometimes you want the AI to focus on specific parts of the project

and leave everything else untouched – essentially “lock” certain files or areas so

they are not modified. This is useful if youʼve manually written some code or have a

stable component you donʼt want altered while the AI works on something else.

Since Lovable doesnʼt have a literal file-lock feature yet, using the prompt to

constrain scope is the next best thing.

How to use: In your prompt, explicitly instruct the AI not to change certain files or

components. You might say, “Donʼt edit the authentication files,” or “Keep the

HomePage component unchanged.” Also, be clear about where the AI should focus

changes. This directive should be included each time you prompt during that

sensitive period, to remind the AI of the boundary.

Example Prompt – Limit Scope of Changes:

Here we included a very direct constraint: _“refrain from altering pages X or Y and

focus changes solely on page Z.”_ . By repeating this in the prompt, we guide the

AIʼs attention. The task itself (adding a dashboard section) is given, but we

wrapped it with instructions about scope. This greatly reduces the chance of

Lovable tinkering with your login system while trying to add a dashboard feature.

Another scenario is when updating a very delicate feature. In such cases, you can

combine scope limitation with a cautionary tone. For example: “This update is

sensitive; proceed very carefully and avoid touching anything unrelated”. This was

demonstrated in a prompt like: _“This update is quite delicate… Steer clear of

shortcuts or assumptions — take a moment to seek clarification if unsure. Precision

is crucial.”_ . Including a line like that sets the AIʼs “mindset” to be extra cautious.

Planning

Refactor the **ProjectList component file**, but **keep its behavior and 

Goals:

- Improve the code structure and readability (simplify complex functions,

- Remove any unused variables or imports.

- Ensure the file follows best practices and is well-documented.

Do **not** introduce any new features or change how the component works f

Please **focus only on the Dashboard page** for this change. 

- Do **not modify** the `LoginPage.tsx` or `AuthProvider.tsx` files at al

- Concentrate your code edits on `Dashboard.tsx` and related dashboard co

Task: Add a new section to the Dashboard that shows “Tasks due this week”

*(Again, no changes to login or auth files – those are off-limits.)*



When to use: Before diving into a complex or multi-step implementation, or when

you have a big feature that could be broken into sub-tasks. Planning prompts are

also useful if you want the AI to outline an approach before writing code, so you can

verify the plan (and adjust it) without burning through code-generation credits on a

wrong path. Essentially, use this when the strategy isnʼt straightforward and youʼd

like the AIʼs help to think it through.

How to use: Ask the AI to produce a plan or checklist. You can say, “Outline a step-

by-step plan for X” or “Before coding, list the steps you will take to implement Y.”

This can be done in Chat mode to ensure it doesnʼt execute any code changes while

planning . After getting the plan, you might even discuss it (maybe have the AI

explain why each step is needed) and then proceed to implementation step by step.

Planning prompts are meta – they donʼt build the app directly, but they set the stage

for a smoother build.

Example Prompt – Planning a Feature Implementation:

This prompt tells the AI to act as a planner. It asks for a sequenced plan to

implement an “email notifications for overdue tasks” feature. We explicitly say not

to code yet (so weʼd run this in Chat mode or just trust that the AI will output a

plan). The AI might respond with something like:

��� Add a timestamp field to tasks for due date (if not already present).

��� Create a server-side function (or scheduled job) to check for overdue tasks

periodically.

��� Integrate email sending using an email service (e.g., Resend or SMTP) when

an overdue task is found.

��� Update the UI to allow users to toggle notifications on/off for a task (optional

setting).

��� Test the flow with a task that just passed its due time to ensure an email is

sent.

By reviewing such a plan, you can catch any issues (maybe we realize we need a

new DB table, or maybe step 4 is out-of-scope for now, etc.) before any coding

happens. Itʼs a lot easier to tweak the plan than to rewrite bad code. Planning

prompts save time in complex features by getting the approach right from the start .

Stripe Setup

When to use: When you want to integrate payments into your app using Stripe.

Lovable has integration points for Stripe, but it requires setting up keys, webhooks,

and UI for checkout. A prompt can handle the boilerplate of connecting to Stripeʼs

API. Use this when you need to add commerce (selling a product, subscription, etc.)

in your project.

How to use: Provide the details Stripe needs: mode (test or live), product or pricing

info, and redirect URLs after payment. Also, instruct how the UI should behave (e.g.,

a checkout form/modal). Itʼs crucial to mention that sensitive keys will be provided

securely (not hard-coded in the prompt)  – you typically store those in environment

variables or Lovableʼs secret storage. So you can say “assume I have set the API

keys in the environment.” This way, the AI will know to call the keys, not include

them literally. Additionally, specify not to alter unrelated code while setting up Stripe

(to avoid accidental changes).

��Example Prompt – Integrating Stripe Payments:** 

Before writing any code, **plan out the implementation** of the new Notif

- List each step required to add email notifications when a task is overd

- Consider both frontend (UI changes, if any) and backend (creating sched

- Ensure the plan keeps the current functionality stable – we can’t break

- Provide the plan as an ordered list (1, 2, 3, ...), with a brief explan

Once you outline the plan, pause for review. **Do not make any code chang

I want to **add Stripe payments** to the app.

- Use **Stripe in test mode** for now.



This prompt gives all the key details for Stripe: test mode, product IDs, what

happens on success/cancel, and where to put the checkout button. It explicitly

says not to touch anything else. The AI (and Lovableʼs Stripe integration helper) will

use this to scaffold the Stripe integration. Under the hood, Lovable might create a

serverless function (if using Supabase) to handle webhooks, etc., but you donʼt

have to prompt that separately – the instruction here is usually enough for a basic

setup .

Note: We included a line about API keys being secure because we never want

secret keys in the prompt. The docs remind us: _“Use your Stripe Secret Key in the

Supabase Edge Function secrets, and avoid including them in the prompt”_ . So by

telling the AI “assume itʼs configured,” you ensure the code will reference an

environment variable or config, not a plaintext key.

After running this prompt, test the payment flow with Stripeʼs test card numbers. If

something isnʼt working (e.g., the webhook), Lovable might show errors which you

can then debug or refine with another prompt.

Using Chat Mode vs Default Mode

When to use: Lovable has two modes for prompting: Default Mode (which

immediately applies changes to your project) and Chat-Only Mode (which is more

like a conversation without altering code until you say so). Knowing when to use

each can streamline your workflow. Use Default for straight coding tasks and use

Chat mode for brainstorming, debugging, or when you want to discuss changes

before executing them.

How to use:

Example use case – Default vs Chat:

Suppose you suspect there is outdated code in your project that needs cleaning

up. In Default mode, you might directly prompt:

Review the app and tell me where there is outdated code.

Lovable could try to both identify and possibly start refactoring it in one go . But

maybe you actually want to be careful and just get advice. In that case, youʼd

switch to Chat mode and ask something like:

Iʼm seeing some deprecated library warnings. What parts of the code might be
outdated, and how should we update them?

Now the AI will discuss this with you, rather than immediately rewriting files.

Similarly, if you have an error and you want the AI to analyze it, Chat mode is safer.

You can copy an error message and ask:

- We have a product in Stripe with ID `prod_12345` and a price ID `price_

- Implement a checkout button on the **Pricing page** that starts a Strip

- After successful payment, redirect the user to `/payment-success`. If t

Important:

- Assume API keys and webhook secrets are configured securely (do **not**

- Do **not** modify any other pages or features unrelated to payments.

Once done, provide any webhook endpoint setup instructions I need (e.g., 

Default Mode is great for when you have a well-defined feature to build or

change to make. You give the instruction, and Lovable will do it in one go if

possible.

Chat Mode, on the other hand, is useful if you want to have a back-and-forth or

analyze something (like asking “Why is this not working?” or “Whatʼs the best
way to do X?”) without immediately changing the codebase . In Chat Mode, the

AI will respond with analysis or a plan, and you usually have to explicitly say “go

ahead and implement” when ready.



What does this error mean, and how can we fix it?

The AI will explain and propose a solution. Once you agree, you might switch to

Default mode or explicitly instruct to apply the fix.

In summary, use Default Mode for straightforward building (“do X for me”), and

use Chat Mode for troubleshooting or design discussions (“why or how should we

do X?”). One concrete workflow is: Brainstorm in Chat (get a plan or identify

issues), then execute in Default. This approach is even recommended: for example,

_use Chat Mode to have the AI analyze errors before making changes_ . Itʼs like

using ChatGPT for advice and then applying the changes once youʼre confident.

�Make sure Chat-Only Mode is enabled in your settings under Labs if you donʼt see

it . This mode ensures the AI wonʼt write code to your project until you say so,

which is perfect for safe experimentation.)

Writing Knowledge Bases and PRDs

When to use: At the start of a project and whenever you have more context to give

the AI than can fit in a single prompt. In Lovable, the Knowledge Base is a special

place in your project settings where you can store background information,

requirements, and guidelines that persist across prompts . A Project Requirements

Document �PRD� is a comprehensive summary of your appʼs objectives and specs –

essentially, the blueprint of what youʼre building. Use these tools to prevent

misunderstanding and to anchor the AI with the big picture of your project.

How to use: Begin by filling out the Knowledge Base in Lovable with key info about

your project. This can include:

This might sound like a lot, but writing it down pays off. The AI will use this info on

every prompt to stay aligned with your projectʼs needs . Itʼs like giving the AI long-

term memory about your app. You typically set this up once (and update as

needed).

After populating the knowledge base, you can literally tell the AI to use it. For

example, your very first prompt to Lovable might be: _“Before writing any code,

please review the Knowledge Base and confirm you understand the project.”_  This

lets the AI summarize or acknowledge the context, and you can catch if it missed

something.

Example Prompt – Using the Knowledge Base:

This kind of summary from the AI confirms it has ingested your specifications

correctly. If it misunderstood anything, you can correct it in Chat mode. Once

confirmed, you proceed with building (the AI now “knows” the overall plan).

Writing a PRD and filling the Knowledge Base might feel like extra upfront work, but

it pays dividends throughout development. It reduces errors and AI hallucinations

because the AI always has a reference for what the app should do  . Itʼs especially

helpful for larger projects that canʼt be built in one or two prompts. Think of it as

seeding the AI with the same understanding you have in your head.

Overview & Goals: What the project is and the problem it solves.

User Flow: A description of how users navigate or use the app (e.g. “Users sign

up, then see a dashboard, can create tasks, etc.”) .

Core Features & Scope: What features are in scope (and maybe note any out-

of-scope to avoid feature creep) .

Tech Stack: What technologies, APIs, and services will be used .

Design Guidelines: Any specific UI/UX instructions – branding, color scheme,

layouts, accessibility requirements .

Backend Details: Outline data models, key database tables, integrations like

Stripe or Supabase, etc. ..

User: Before you write any code, please review the Knowledge Base and sha

AI (likely response): “Sure. Based on the Knowledge Base: You’re building



Was this page helpful? Yes No

Prompting 1.1 Debugging Prompts

Powered by Mintlify

Lastly, maintain the Knowledge Base as your project evolves. If you add a major

feature or change the scope, update the PRD/Knowledge Base document. This

way, future prompts will consider the new information. Itʼs much easier than re-

explaining context every time. In essence, the Knowledge Base + PRD is your AI

project handbook – it keeps everyone (you and the AI) on the same page about

what youʼre building and how.

https://docs.lovable.dev/tips-tricks/prompting-one
https://docs.lovable.dev/tips-tricks/prompting-debugging
https://x.com/lovable_dev
https://github.com/lovablelabs
https://linkedin.com/company/lovable-dev
https://mintlify.com/preview-request?utm_campaign=poweredBy&utm_medium=docs&utm_source=docs.lovable.dev



## Message 12

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message


































All posts

Published January 16, 2025 in dev lifecycle

The Lovable Prompting Bible

Author: Stephane at Lovable

Most people think prompting is just typing a request into AI and hoping for the

best. Wrong.

The difference between getting a mediocre response and having AI build

entire workflows for you comes down to how you prompt. Whether youʼre a

developer or a non-technical user, mastering prompt engineering can help

you:

Automate repetitive tasks

Debug faster with AI-generated solutions

Build and optimize workflows effortlessly

And the best part? You donʼt need to be an expert. With the right prompting

techniques, you can unlock AIʼs full potential in Lovable, make.com, and n8n—

without wasting time on trial and error.

Letʼs dive in.

TL;DR:

Effective prompting matters: Structure your prompts to save hours of

troubleshooting.

Meta prompting: Use AI itself to refine your prompts for better accuracy.

Reverse meta prompting: Save debugging sessions to optimize future

requests.

Automation tools: Extend Lovableʼs capabilities with APIs using make.com

and n8n.

Chat mode vs. default mode: When to use each for debugging and

iteration.

Handling webhooks: Automate Lovable applications with powerful

integrations.

Why Prompting Is Critical for AI Development

17 min read

The Lovable Prompting Bible

Why Prompting Is Critical for AI

Development

Mastering Prompting: The Four Levels

Prompt Library

Debugging in Lovable

Using Automation Tools Like make.com

and n8n

Last Thoughts

Share this

Support Launched Learn Sign in Sign up

https://lovable.dev/blog
https://discord.gg/lovable-dev
https://x.com/
https://lovable.dev/
https://lovable.dev/support
https://launched.lovable.dev/
https://docs.lovable.dev/
https://x.com/lovable_dev
https://www.linkedin.com/company/lovable-dev/
https://discord.gg/lovable-dev
https://lovable.dev/login
https://lovable.dev/signup


Unlike traditional coding, AI applications rely on structured communication.

Providing AI with clear context and constraints ensures high-quality output. In

a Lovable expert session at Lovable, Mark from Prompt Advisors

demonstrated how developers and non-technical users can enhance their AI

prompting techniques to build faster, debug smarter, and automate complex

workflows.

Watch on

Master Prompt Engineering – Build Smarter AI Apps with Lovable!Master Prompt Engineering – Build Smarter AI Apps with Lovable!
ShareShare

Understanding the AI s̓ "Mindset"

AI models, including those powering Lovable, do not "understand" in a human

way—they predict responses based on patterns. To guide them effectively:

Be explicit: Instead of “build a login page,” specify “create a login page

using React, with email/password authentication and JWT handling.”

Set constraints: If you need a specific tech stack (e.g., Supabase for

authentication), state it clearly.

Use formatting tricks: AI prioritizes the beginning and end of prompts—

put important details upfront.

Mastering Prompting: The Four Levels

1. Training Wheels Prompting

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://www.youtube.com/watch?v=IqWfKj4mUIo&embeds_referring_euri=https%3A%2F%2Flovable.dev%2F
https://www.youtube.com/watch?v=IqWfKj4mUIo


A structured, labeled approach for clear AI instructions:

# Context

## Task

### Guidelines

#### Constraints

Example:

You are a world-class prompt engineer. Write me a prompt that will gen

2. No Training Wheels

More conversational prompts while maintaining clarity.

3. Meta Prompting

Leverage AI to refine your prompts:

Rewrite this prompt to be more concise and detailed: 'Create a secure 

4. Reverse Meta Prompting

When debugging, have AI document the process for future use:

Summarize the errors we encountered while setting up JWT authenticatio

Prompt Library

Enhance Prompt

The quality of your prompts significantly influences the output of AI. This is the

essence of effective prompting: the more refined your prompt, the higher the

quality of the output you receive. A comprehensive and well-organized prompt

can save you both credits and time by reducing errors. Therefore, these steps

are definitely worth considering:

Provide as much details as you can in the input field.

Use the "Select" feature to precise edit your component. 

Enhance your prompt with the experimental "Chat mode". 

Starting a new project

Use this proven structure for starting a new project:

Start with "I need a [type] application with:"

Elaborate on tech stack - including Frontend, styling, Authorization a

Elaborate on core features including main and secondary features. 

Then direct the AI to start somewhere like: "Start with the main page 

However, we consistently recommend that users begin with a blank project

and gradually build upon it. This approach allows the AI to grasp the

fundamental concepts effectively before delving into the specifics.

Diff & Select

Whenever you request Lovable to implement a particular change in any file, it

will rewrite the entire file or modify the existing content. To ensure that the AI

only updates relevant files, provide clear instructions. This approach

encourages the AI to edit only the necessary sections, resulting in minimal



changes to just a few lines of code. By doing so, you can reduce loading times

and prevent error loops.

An effective prompt Iʼve applied previously when adjusting an existing feature

is:

Implement modifications to the feature while ensuring core functionali

Lock Files

Lovable currently lacks a built-in file locking system. However, you can guide

the AI with slight modifications to your prompts. Just include this instruction in

each prompt: "Please refrain from altering pages X or Y and focus changes

solely on page Z."

You can also try this prompt if you are updating an existing feature without the

intention of modifying something sensible:

This update is quite delicate and requires utmost precision. Carefully

Design

Designing something on Lovable is effective as Lovable already has great taste

;) Nevertheless, those below prompts can help you improve those design

implementations:

��� UI Changes:

Make solely visual enhancements—ensure functionality and logic 

��� Optimize for Mobile:

Enhance the app's mobile experience while preserving its exist

��� Responsiveness and Breakpoints Prompt:

Make certain that all designs are completely responsive at eve

��� Planning:

Before editing any code, create a phased plan for implementing 

Before making any code edits, develop a structured plan for implementing

responsiveness. Begin with the largest layout components and gradually work

down to smaller elements and specific components. Ensure that the plan

outlines definitive steps for testing responsiveness at all breakpoints to

guarantee consistency and a smooth user experience. Present the plan for

feedback before moving forward.

Knowledge base

https://lovable.dev/blog/%3E)%5B%5D(%3C


Providing detailed context about your project is crucial, especially early on in

the project. What is the project's purpose? What does the user flow look like?

What tech stack are you utilizing? What is the scope of work? At Lovable, we

refer to this as the "Knowledge Base," and it can be easily found in your

project settings.

Creating a solid framework for AI ensures it operates effectively and adheres

to your outlined plan with every prompt you provide. Incorporate these

elements within your project:

��� Project Requirements Document �PRD�� This section is crucial for any AI

coding project. It outlines a comprehensive summary covering essential

elements such as the introduction, app flow, core features, tech stack, and

the distinctions between in-scope and out-of-scope items. Essentially, it

serves as your project's roadmap, which you can present to AI coding

models.

��� Application or user flow: This clarity will aid the AI model in understanding

the connections between pages and processing all features and limitations

effectively.

Users begin their experience on the landing page, where they ca

��� Tech stack: This section must encompass all technical specifics regarding

the project, such as the Frontend Tech Stack, Backend Tech Stack, API

Integrations, Deployment Instructions, and any other open-source libraries

you plan to utilize. This information will facilitate the AI model's

understanding of which packages and dependencies to install.

��� Frontend guidelines: This section should outline your project's visual

appearance in detail: Design Principles, Styling Guidelines, Page Layout,

Navigation Structure, Color Palettes, and Typography. This serves as the

aesthetic foundation of your project. The clearer your explanations, the

more visually appealing your application will become.

��� Backend structure: This section will explain to AI model about: Backend

Tech like Supabase, User Authentication, Database Architecture, Storage

buckets, API Endpoints, Security measures, Hosting Solutions. This is the

main brain of your project. Your app will fetch and display data from your

backend.

Once you initiate the project with the initial prompt, be sure to incorporate this

Knowledge Base to reduce errors and prevent AI hallucinations. Additionally,

you can prompt the AI with:



Before you write any code, please review the Knowledge Base and share 

Utilize the "Chat mode" for this task to ensure that no modifications are made

to your projects while you are providing guidance.

Mobile First

The issue (and somewhat hidden truth) is that most developers prioritize

desktop design simply because it looks better on a large, vibrant screen.

However, the reality is that we should have been focusing on mobile-first

design for years now.

A great prompt that was shared by a Champion on Discord:

Always make things responsive on all breakpoints, with a focus on mobi

Use modern UI/UX best practices for determining how breakpoints should 

Use shadcn and tailwind built in breakpoints instead of anything custo

Optimize the app for mobile without changing its design or functionali

But if you're already far along into your project, you can fix this by telling it to

update things to be responsive starting with the largest layout components

down to the smallest. Then get to the individual components.

Details

When working with Lovable, itʼs crucial to provide the AI with clear and

specific requests. Rather than simply saying, "move the button to the right," try

stating, "in the top header, shift the sign-up button to the left side of the page,

ensuring the styling remains consistent." The more precise your instructions

are, the fewer errors youʼll encounter, and youʼll save on credits!

Basically, I always suggest adding instructions on how you want Lovable to

approach every task. My example:

Key Guidelines: Approach problems systematically and articulate your r

Step by Step

Avoid assigning five tasks to Lovable simultaneously! Doing so may lead the AI

to create confusion. Hereʼs a better approach:

Start with Front design, page by page, section by section. 

The plug backend using Supabase as Lovable integration is natively bui

Then, refine the UX/UI if needed. 

This step-by-step process enables AI to concentrate on one task at a time,

reducing the likelihood of errors and hallucinations.

Don't loose components

You can also implement this after significant changes and following a series of

minor adjustments. This practice has been invaluable in maintaining project

consistency and preventing sudden loss of components. Regularly refer to our

filesExplainer.md document to ensure we accurately record changes in code

and components, keeping our file structure organized and up to date.

Refactoring

Refactoring is essential to your development lifecycle within Lovable. It is often

suggested by the AI to minimize the loading time and errors. Here are great

prompts you can use:



��� Refactoring After Request Made by Lovable:

Refactor this file while ensuring that the user interface and f

��� Refactoring Planning:

Develop a comprehensive plan to refactor this file while keepi

��� Comprehensive Refactoring:

Develop a comprehensive plan for a site-wide codebase review a

��� Post Refactoring:

Conduct a detailed post-refactor review to verify that no issue

��� Codebase Structure Audit Prompt:

Perform a comprehensive regression and audit of the codebase to

��� Folder Review:

Conduct a thorough examination of the folder [Folder Name] alo

��� Post Restructuring Cleanup:

Ensure all routing and file imports are thoroughly updated and 

��� Codebase Check for Refactoring:

Perform a thorough audit of the codebase to assess its structu

Stripe

Stripe seamlessly integrates with Lovable and can be set up with minimal

effort. However, there are several factors that may hinder Stripe's functionality:

Initiate a Stripe connection in test mode using the configuration deta

�Disclaimer: Use your Stripe Secret Key and Webhook Signing Secret

securely in the Supabase Edge Function Secrets and avoid including them in

the prompt for safety.*

Ask for help

Avoid the tendency to rely on Lovable for every small change. Many minor

adjustments can be made directly within your code, even if you arenʼt a

professional engineer. If you need assistance, feel free to consult ChatGPT or

Claude for help. Utilize the browserʼs Inspect tool to identify the elements you

want to modify. You can experiment with changes at the browser level, and if

youʼre pleased with the outcome, make those adjustments in the code. This

way, you wonʼt need to involve Lovable at all.

While I'm not an engineer, having a basic understanding of coding significantly

aids my progress. Utilizing tools like GitHub and Sonnet, I frequently implement

enhancements beyond Lovable, allowing me to reserve my prompts for more

complex tasks.

Debugging in Lovable

Debugging is an integral part of the Lovable experience, and mastering this

debugging flow can significantly reduce frustration—especially when clicking

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook


the “Try to Fix” button, which does not count as credits.

Chat Mode vs. Default Mode

The new "Chat mode" is excellent for fostering creativity and generating ideas.

Begin by outlining your concept, as this could be the most critical step.

Visualizing screens, features, and layouts in your mind isnʼt as effective for

tracking changes.

A traditional scenario of using the "Chat mode" is:

Default Mode: High-level feature creation.

Review the app and tell me where there is outdated code.

Chat Mode: Troubleshooting—ask AI to analyze errors before making

changes. Go to your account settings and enable Labs feature.

Follow this plan and act on all those items

I think I've read these below super prompts from an X user then found it back

on Discord:

Perform a comprehensive regression and audit of the codebase to determ

Generate a comprehensive report that outlines key areas for enhancemen

DON'T GIVE ME HIGH-LEVEL STUFF. IF I ASK FOR A FIX OR AN EXPLANATION, 

In terms of large codebase, it's beneficial to engage with Lovable by using the

"Chat mode" to weigh the advantages and disadvantages of various

approaches. Since you're all eager to learn, try explaining your features to an

AI, encouraging it to ask clarifying questions about structure, trade-offs,

technology, and more.

It's a fact that code and features evolve continuously, reflecting the ever-

changing nature of business. Much of the code is opinionated, often crafted

with a specific vision for the future in mind. While you mentioned a steel

foundation, you might initially decide to make component X very robust while

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook


keeping component Y flexible, only to later realize that X should have been

dynamic and Y solid. This is a common scenario.

Handling Errors Effectively

Check browser developer tools �Console logs, Network requests).

Use reasoning models (e.g., GPT�4 Turbo, DeepSeek, Mistral) for

debugging.

Feed errors into AI for deeper analysis.

Debugging Prompts

To effectively address the errors you're encountering, avoid tackling them all

at once! I recommend attempting the "Try to fix" option up to three times. If the

AI is still unable to resolve the issue, try this technique: Copy the error

message and paste it into "Chat mode," then say, "Use chain-of-thought

reasoning to identify the root cause." This approach allows both the AI and you

to analyze the situation and understand the underlying issues before

transitioning to "Edit mode" for making corrections.

This guidebook was provided by a champion customer on Discord, and I

believe youʼll find it appealing:

��� Initial Investigation:

The same error continues to occur. Take a moment to perform a 

��� Deep Analysis:

The issue persists without resolution. Perform a thorough analy

��� Full System Review:

This is a pressing issue that necessitates a thorough re-evalua

��� Comprehensive Audit:

The problem continues and now calls for a comprehensive, system

��� Rethink and Rebuild:

This problem remains unresolved, and it's imperative to pause a

��� Clean up Console Logs:

Could you devise a strategy to systematically identify and elim

��� Encouragement:

Lovable, you’re doing an outstanding job, and I genuinely appre



��� Checking Complexity:

Take a moment to reflect on whether this solution can be simpl

��� Confirming Findings:

Before moving ahead, are you entirely convinced that you have 

���� Explaining Errors:

Explain the meaning of this error, its origins, and the logica

Debugging Flow

Debugging in prompt engineering involves isolating errors, analyzing

dependencies, and refining prompts to achieve the desired output. Whether

you are creating applications, integrating APIs, or building AI systems,

debugging follows a systematic flow:

��� Task Identification – Prioritize issues based on impact.

��� Internal Review – Validate solutions before deploying.

��� Reporting Issues – Clearly define current vs. expected behavior.

��� Validation – Verify changes render correctly in the DOM.

��� Breakpoints – Isolate and test specific components.

��� Error Handling & Logging – Use verbose logging and debug incrementally.

��� Code Audit – Document issues and proposed fixes before making

changes.

��� Use the 'Try to Fix' Button – Automatically detects and resolves errors in

Lovable.

��� Leverage Visuals – Upload screenshots to clarify UI-based errors.

���� Revert to Stable Version – Use the 'Revert' button to go back if needed.

Understanding 'Unexpected Behavior'

Sometimes, your code runs without errors, but your app isnʼt functioning as

expected. This is known as Unexpected Behavior, and it can be tricky to

debug. Strategies include:

Retracing Your Steps – Review what you initially asked Lovable to do.

Breaking It Down – Identify if specific sections are misaligned.

Using Images – Show Lovable the UI result versus the intended outcome.

Writing Better Prompts to Avoid Errors

A well-structured prompt reduces debugging time. Use this best practice

format:

Project Overview – Describe what youʼre building.

Page Structure – List key pages and components.

Navigation Logic – Explain user movement through the app.

Screenshots/Wireframes – Provide visuals if available.

Implementation Order – Follow a logical sequence, e.g.:



Create pages before integrating the database

Debugging Strategies in Lovable

1. Using Developer Tools for Debugging

Console Logs – Review error logs and DevTools notifications.

Breakpoints – Pause execution to inspect state changes.

Network Requests – Validate data flow between frontend and backend.

2. Common Debugging Scenarios

Minor Errors – Investigate thoroughly before making changes.

Persistent Errors – Stop changes and re-examine dependencies.

Major Errors – If necessary, rebuild the flow from scratch while

documenting findings.

3. Advanced Troubleshooting

If the 'Try to Fix' button isnʼt resolving your issue, consider:

Being More Specific – Describe the problem in detail, including expected

vs. actual results.

Using Images – Screenshots help AI understand UI-based issues.

Asking Lovable for Debugging Help – Example:

What solutions have been tried so far? What else can be done?

Reverting to a Previous Working State – If debugging leads to more

issues, roll back to a known good version.

4. Debugging Specific Issues

UI-related problems: Upload screenshots and ask,

Why is this UI behaving this way? What’s the best fix?

API integration issues: Ensure youʼre using the latest API schema and that

backend connections are correctly set up.

When completely stuck: Prompt Lovable with:

Analyze the error and suggest an alternative approach.

Debugging doesnʼt have to be frustrating. Lovable provides powerful tools to

auto-fix errors, analyze problems, and iterate efficiently. By following

structured prompting techniques, using images, and leveraging AI-driven

debugging, you can overcome any coding challenge.

Using Automation Tools Like make.com and n8n

When to Use Automation

Edge Functions: Direct Supabase API calls.

make.com: Integrating external services �Slack, Stripe, CRM tools).

n8n: Self-hosted, scalable automation.

Example: Automating a Dental Consultation App



��� Create a landing page in Lovable with a form for dental issues.

��� Send data to make.com via Webhooks.

��� Use an AI API (e.g., Perplexity AI) for live research.

��� Determine eligibility using Mistral or GPT�4 reasoning models.

https://chatgpt.com/g/g-67aa992a22188191a57023d5f96afed2�

lovable-visual-editor

https://chatgpt.com/g/g-67aa992a22188191a57023d5f96afed2�

lovable-visual-editor

��� Return a response to Lovable with recommended next steps.

Webhooks and API Calls: Advanced Use Cases

Validate responses: Ensure correct processing of webhook responses.

Test incrementally: Send minimal data first before building complex API

workflows.

Use reasoning models: Debug errors by asking AI to analyze incorrect

responses.

Last Thoughts

Mastering prompt engineering isnʼt just about better AI interactions—itʼs about

boosting efficiency, reducing development cycles, and unlocking new

automation possibilities. Whether youʼre debugging existing workflows,

optimizing AI outputs, or integrating complex automations, structured

prompting helps you get there faster and with fewer headaches.

Focus on your big ideas—Lovable and automation tools will handle the

execution. Whether you're a seasoned developer refining 15-year-old code or

a non-technical user crafting innovative applications, the right prompting

strategy is your most powerful tool.

Additional resources exist:

Prompt Engineering on our documentation.

Troubleshooting guide on our documentation.

Prompts guides you can use for integrations.

Good luck!

https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://lovable.dev/blog/2025-01-16-lovable-prompting-handbook
https://docs.lovable.dev/tips-tricks/prompting
https://docs.lovable.dev/tips-tricks/troubleshooting
https://docs.lovable.dev/integrations/prompt-integrations


Product

About

Solutions

Blog

Careers

Changelog

Hire a Partner

Become a Partner

Roadmap

Resources

Builder Hall of Fame

Learn

Integrations

Support

Launched

Legal

Privacy Policy

Terms & Conditions

Report Abuse

https://lovable.dev/
https://twitter.com/lovable_dev
https://www.linkedin.com/company/lovable-dev/
https://discord.gg/lovable-dev
https://lovable.dev/gpt-engineer
https://lovable.dev/solutions
https://lovable.dev/blog
https://lovable.dev/careers
https://docs.lovable.dev/changelog
https://lovable.dev/partners
https://lovable.dev/partners/apply
https://feedback.lovable.dev/
https://lovable.dev/hall-of-fame
https://docs.lovable.dev/
https://docs.lovable.dev/integrations/introduction
https://lovable.dev/support
https://launched.lovable.dev/
https://lovable.dev/privacy
https://lovable.dev/terms
https://lovable.dev/abuse



## Message 13

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message


































Prompt Engineering

Prompting 1.1
Prompt structure, levels of prompting, meta/reverse meta prompting, and

foundational tactics with examples.

Heads up!

To help you make the most out of Lovable, we compiled a list of prompting

strategies and approaches. Some of these were collected from our teamʼs

experience, and others were shared with us by our community members. Since

Lovable relies on large language models �LLMs�, effective prompting strategies can

significantly improve its efficiency and accuracy.

What is Prompting?

Prompting refers to the textual instructions you give an AI system to perform a task .

In Lovable (an AI-powered app builder), prompts are how you “tell” the AI what to

do – from creating a UI to writing backend logic. Effective prompting is critical

because Lovable uses large language models �LLMs�, so clear, well-crafted

prompts can greatly improve the AIʼs efficiency and accuracy in building your app .

In short, better prompts lead to better results.

Structuring Effective Prompts

For consistent outcomes, it helps to structure your prompt into clear sections. A

recommended format (like “training wheels” for prompting) uses labeled sections

for Context, Task, Guidelines, and Constraints :

By structuring your prompt, you reduce ambiguity and help the AI focus.

Remember to put the most important details up front – AI models tend to pay extra

attention to the beginning and end of your prompt. And if you need a specific tech

or approach, state it explicitly (for instance, if you require Supabase for auth, say

so) .

The Four Levels of Prompting

Prompting is a skill you can develop. Think of it as progressing through levels, from

very guided prompts to more advanced techniques 

Watch on

Master Prompt Engineering – Build Smarter AI Apps with LMaster Prompt Engineering – Build Smarter AI Apps with L……
ShareShare

Context: Give background or the bigger picture. Example: “Weʼre building a

project management tool for tracking tasks and deadlines.” This sets the stage

for the AI.

Task: State exactly what you want done now. Example: “Create the UI for the

project creation page.”

Guidelines: Specify how to approach the task or any preferences. Example:

“Use a clean design, following Material UI principles, and ensure itʼs mobile-

responsive.”

Constraints: Declare any hard limits or must-nots. Example: “Do not use any

paid libraries, and do not alter the login page code.”

On this page

Heads up!

What is Prompting?

Structuring Effective Prompts

The Four Levels of Prompting

Training Wheels Prompting

No Training Wheels

Meta Prompting

Reverse Meta Prompting

Additional Prmpting tips

Be specific, avoid vagueness

Incremental prompting

Include Constraints and

Requirements

Avoid ambiguity in wording

Mind your tone and courtesy

Use formatting to your advantage

Leverage examples or references

Using image prompts

Feedback integration

Emphasizing Accessibility

Predefined Components and

Libraries

Multilingual Prompting

Defining Project Structure and File

Management

Applying These Strategies in Different

Tools

In Lovableʼs Builder

With make.com or n8n (workflow

automation)

Edge cases and external integrations

Summary

Documentation

Community

Product Announcement

Request Feature

Youtube

Introduction

User Guides

Prompt Engineering

Integrations

Tips and Tricks

Resources

Welcome

Messaging Limits

FAQ

Quickstart

Figma to Lovable

Visual Edit

Knowledge Files

Labs

Deploy

Launched

Prompting 1.1

Prompt Library

Debugging Prompts

Prompts & Integrations

Integrations

GitHub Integration

Supabase Integration

Stripe & Payments

Resend Integration

Clerk Integration

Make Integration

Replicate Integration

Troubleshooting

Using images in Lovable

Using Videos

Chrome Adds-on

Using Custom �Google� Fonts

Using npm packages

SEO

Changelog

Video tutorial

Community

Search or ask... Ctrl K Support App

https://www.youtube.com/watch?v=IqWfKj4mUIo&embeds_referring_euri=https%3A%2F%2Fdocs.lovable.dev%2F
https://www.youtube.com/watch?v=IqWfKj4mUIo
https://docs.lovable.dev/introduction
https://discord.gg/lovable-dev
https://lovable.dev/blog?category=announcements
https://feedback.lovable.dev/
https://www.youtube.com/@lovable-labs/
https://docs.lovable.dev/introduction
https://docs.lovable.dev/user-guides/messaging-limits
https://docs.lovable.dev/faq
https://docs.lovable.dev/user-guides/quickstart
https://docs.lovable.dev/features/figma-to-lovable
https://docs.lovable.dev/features/visual-edit
https://docs.lovable.dev/features/knowledge
https://docs.lovable.dev/features/labs
https://docs.lovable.dev/features/deploy
https://docs.lovable.dev/features/launched
https://docs.lovable.dev/tips-tricks/prompting-one
https://docs.lovable.dev/tips-tricks/prompting-library
https://docs.lovable.dev/tips-tricks/prompting-debugging
https://docs.lovable.dev/integrations/prompt-integrations
https://docs.lovable.dev/integrations/introduction
https://docs.lovable.dev/integrations/git-integration
https://docs.lovable.dev/integrations/supabase
https://docs.lovable.dev/tips-tricks/setting-up-payments
https://docs.lovable.dev/integrations/resend
https://docs.lovable.dev/integrations/clerk
https://docs.lovable.dev/integrations/make
https://docs.lovable.dev/integrations/replicate
https://docs.lovable.dev/tips-tricks/troubleshooting
https://docs.lovable.dev/tips-tricks/using-images
https://docs.lovable.dev/tips-tricks/using-videos
https://docs.lovable.dev/integrations/chrome-extensions
https://docs.lovable.dev/tips-tricks/custom-fonts
https://docs.lovable.dev/features/npm-packages
https://docs.lovable.dev/tips-tricks/seo
https://docs.lovable.dev/changelog
https://docs.lovable.dev/user-guides/video-tutorials
https://docs.lovable.dev/tips-tricks/community
https://docs.lovable.dev/
mailto:<EMAIL>
https://lovable.dev/


Training Wheels Prompting

This is a highly structured approach, great for beginners. You clearly label sections

(as above) to ensure nothing is missed . It might feel verbose, but it leaves little

room for misunderstanding.

Example �Training Wheels prompt):

Context

We are developing an e-commerce platform for eco-friendly products.

Task

Create a product listing page with filters for category and price.

Guidelines

Make the UI clean and modern, using Tailwind CSS for styling. Include sample

product data for testing.

Constraints

Use Supabase for the database. Do not include any payment functionality yet.

This prompt gives the AI context about the project, the specific task, guidance on

style/tech, and constraints on scope. Itʼs explicit and easy for the AI to follow.

No Training Wheels

Once youʼre comfortable, you can drop the section labels and write in a more

conversational tone – but still remain clear and organized . Essentially, youʼll include

the same information (context, task, etc.) but in paragraph form. This feels more

natural while still guiding the AI.

Example �No Training Wheels prompt):

Iʼm building an e-commerce web app with a Supabase backend. Right now, I need

you to create a product listing page for eco-friendly products. It should have a

clean, modern UI (using Tailwind CSS) with filters for category and price. Please

make sure to use dummy data for now, and donʼt add any payment features yet.

This reads more like how youʼd explain the task to a colleague, but itʼs still specific

about the requirements and limitations (notice we still mentioned the tech stack

and the “no payments” constraint, just without formal headings).

Meta Prompting

Meta prompting means asking the AI to help improve your prompts. You use AI on

itself. For example, you might provide a draft prompt and then ask, “Can you

rewrite this prompt to be more concise and detailed?”. This leverages the AIʼs

strength in language to refine your instructions before execution. Itʼs like getting a

second pair of eyes on your prompt.

Use meta prompting when you feel your instruction could be better but youʼre not

sure how to improve it. The AI might respond with a cleaned-up, more precise

version of your request. You can then use that improved prompt to get the actual

work done. 

Example �Meta prompt request):

User: Rewrite this prompt to be more clear and specific – _“Create a secure login

page in React using Supabase.”_ 

AI (improved prompt): “Implement a secure login page in React using Supabase
authentication. Include fields for email and password, handle error messages for
failed logins, and ensure you properly store JWTs for session management.”

Here the AI elaborated the prompt, adding details (like error handling) that make

the instruction more robust. Meta prompting is a powerful way to polish your

commands.

Support Policy

Glossary

https://docs.lovable.dev/user-guides/support-policy
https://docs.lovable.dev/glossary


Reverse Meta Prompting

Reverse meta prompting flips the script: now you ask the AI to summarize what it

did and turn it into a prompt for future use . This is especially handy for debugging

or recurring tasks. After the AI solves a problem, you can have it generate a prompt

that would reproduce that solution or avoid the issue next time. Essentially, the AI

documents the process for you.

Example �Reverse Meta prompt):

After fixing an authentication bug, you might say: _“Summarize the errors we

encountered setting up JWT auth and how we resolved them. Based on that, create

a prompt I can use next time to set up authentication correctly.”_ . The AI could

output a concise recap of the bug and a step-by-step prompt for avoiding it in the

future. This turns lessons learned into reusable prompts.

Reverse meta prompting is great for building your own library of “recipes” – the AI

helps you formalize solutions so you can apply them again.

Additional Prmpting tips

Be specific, avoid vagueness

Vague prompts lead to vague results. Always clarify what you want and how.

DONʼT:

Another example:

DO:

The latter gives clear direction on scope and expected outcome.

Another example:

Incremental prompting

Itʼs usually best to tackle complex projects in pieces rather than one giant prompt.

Lovable responds well to an iterative approach.

DONʼT:

DO:

This step-by-step progression   helps the AI stay focused and accurate, and you can

catch issues early:

Make this app better.

Create a form for user input

Refactor the app to clean up unused components and improve perfor

Create a user registration form with fields for username, email, 

Build a CRM app with Supabase, auth, Google Sheets export, and da



Another example:

Include Constraints and Requirements

Donʼt shy away from spelling out constraints. If something must or must not be

done, say so.

Adding constraints

Such limits keep the AI from over-engineering. Adding a constraint like a max

number of items or a performance target can focus the AI on whatʼs important .

Avoid ambiguity in wording

If a term could be interpreted in different ways, clarify it. The clearer you are, the

less the AI has to guess.

DONʼT:

DO:

The latter gives clear direction on scope and expected outcome.

Set up a Supabase-connected CRM backend.

Great! Could you please add a secure authentication flow with use

Thank you! The next step is to integrate Google Sheets to export 

Set up a database schema for user information.

Develop an API endpoint to retrieve user data please

Create a simple to-do app with a maximum of 3 tasks visible at a 

Include the ability to add, edit, and delete tasks.

Optimize this code, but ensure the UI and core functionality rema

Use at most 3 API calls for this, and ensure no external library 

The page should display a maximum of 3 tasks at a time.

Add a profile feature

Support notifications

Add a user profile page with fields X, Y, Z.



Mind your tone and courtesy

While it doesnʼt change functionality, a polite tone can sometimes yield better

results . Phrases like “please” or a respectful ask can add context and make the

prompt a bit more descriptive, which can help the AI. For example,

Please refrain from modifying the homepage, focus only on the dashboard
component.

This reads as polite, and it explicitly tells the AI what not to do. Itʼs not about the

AIʼs feelings – itʼs about packing in detail. �Plus, it never hurts to be nice \\\!�

Use formatting to your advantage

Structure lists or steps when appropriate. If you want the AI to output a list or follow

a sequence, enumerate them in the prompt. By numbering steps, you hint the AI to

respond in kind.

First, explain the approach. Second, show the code. Third, give a test
example.

Leverage examples or references

If you have a target design or code style, mention it or provide an example.

Providing an example (image or code snippet) gives the AI a concrete reference to

emulate.

Setting the context

Another example:

Another example:

Send an email notification on form submission.

Let's think through the process of setting up a secure authentica

1. What are the necessary components?

2. How should they interact?

3. Provide the implementation code.

We are building a project management tool that helps teams track 

This tool should have features like:

 - user authentication

 - project creation

 - task assignments

 - reporting

Now, for the first task, create the UI for project creation.

I need a CRM app with Supabase integration and a secure auth flow

We are developing an e-commerce platform focusing on eco-friendly



Using image prompts

Lovable even allows image uploads with your prompt, so you can show a design

and say “match this style”.

There are two main approaches here. The first one is a simple prompting approach.

Simple image upload prompting

You can upload an image and then add an example prompt like this:

Or, you can help AI better understand the content of the image and some additional

specifics about it. Excellent results can be achieved by adding specific instructions

to the image uploaded. While the image is worth a thousand words, adding a

couple of your own to describe desired functionality can go a long way - especially

since interactions cannot always be obvious from a static image.

Image prompting with detailed instructions

Feedback integration

Review the AIʼs output and provide specific feedback for refinements.

Emphasizing Accessibility

Encourage the generation of code that adheres to accessibility standards and

modern best practices. This ensures that the output is not only functional but also

user-friendly and compliant with accessibility guidelines.

Predefined Components and Libraries

Specify the use of certain UI libraries or components to maintain consistency and

efficiency in your project. This directs the AI to utilize specific tools, ensuring

compatibility and a uniform design language across your application.

Create and implement a UI that looks as similar as possible to th

This screenshot shows a layout issue on mobile. Adjust margins an

I want you to create the app as similar as possible to the one sh

It's essentially a kanban clone.

It should have the ability to add new cards (tickets) in each col

Feel free to use the Pangea home dnd npm package for drag-and-dro

The login form looks good, but please add validation for the emai

Generate a React component for a login form that follows accessib

Create a responsive navigation bar using the shadcn/ui library wi



Multilingual Prompting

When working in a multilingual environment, specify the desired language for both

code comments and documentation. This ensures that the generated content is

accessible to team members who speak different languages, enhancing

collaboration.

Defining Project Structure and File Management

Clearly outline the project structure, including file names and paths, to ensure

organized and maintainable code generation. This provides clarity on where new

components should reside within the project, maintaining a coherent file

organization.

Applying These Strategies in Different Tools

The prompting principles above apply not just in Lovableʼs chat, but anywhere you

interact with AI or automation tools:

In Lovable s̓ Builder

Youʼll primarily use these prompts in the Lovable chat interface to build and refine

your app.

��� Start with a broad project prompt, then iterate feature by feature.

��� Use Chat-Only mode when you need to discuss or debug without changing

code.

With make.com or n8n (workflow automation)

You might not prompt these platforms in natural language the same way, but

designing an automation still benefits from clear AI instructions.

For instance, you can have Lovable generate integration logic:

When a form is submitted, send the data to a Make.com webhook for Slack
notification.

In fact, Lovable can help set up automation by integrating with webhooks. If your

app needs to hand off tasks (like sending emails, updating a CRM�, you can prompt

Lovable to use Make or n8n.

After a user signs up in the app, trigger a Make.com workflow that creates a record
in Salesforce.

Lovable will write the code to call that webhook or API. Keeping the prompt

structured ensures the AI knows exactly how to connect Lovable with those

external services.

Generate a Python script that calculates the Fibonacci sequence. 

Create a new React component named 'UserProfile' and save it as 

http://make.com/
http://make.com/
http://make.com/
http://make.com/
http://make.com/


Was this page helpful? Yes No

Launched Prompt Library

Powered by Mintlify

Edge cases and external integrations

Lovable integrates with many services �Stripe, GitHub, Supabase, etc.). When

prompting for these, treat the integration details as part of your

Context/Constraints. For example,

Connect the form to Stripe (test mode) for payments. On success, redirect to /thank-
you.

Be clear about what external services should do. The same goes for using n8n

(self-hosted automation) – you might write,

Send a POST request to the n8n webhook URL after form submission, and wait for
its response to show a confirmation message.

Clarity here is key so the AI produces the correct calls.

Summary

Happy prompting\\\!

Strong prompting is about clarity, structure, and context. Whether youʼre

telling Lovable to build a feature, or orchestrating a Make.com scenario, the

goal is to paint a picture of what you want.

Start with structured prompts if youʼre unsure, and evolve to more

conversational style as you gain confidence.

Use meta techniques to improve and learn from each interaction.

With practice, youʼll guide the AI like an extension of your dev team – and it will

feel natural to get exactly the output you need.

https://docs.lovable.dev/features/launched
https://docs.lovable.dev/tips-tricks/prompting-library
https://x.com/lovable_dev
https://github.com/lovablelabs
https://linkedin.com/company/lovable-dev
https://mintlify.com/preview-request?utm_campaign=poweredBy&utm_medium=docs&utm_source=docs.lovable.dev
http://make.com/



## Message 14

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message


































Prompt Engineering

Prompt Library
List of prompting strategies and approaches.

Welcome to the prompt library\! Here weʼve collected a set of reusable prompt

patterns and examples for common scenarios when building with AI. Think of these

as templates or inspiration that you can tailor to your own project. Each section

covers a particular use case – from kicking off a new project to integrating

payments – with guidance on when to use it and an example prompt.

Feel free to copy these, modify the details, and use them in Lovable or any AI

builder. The tone is official yet casual – just like talking to a colleague – and each

prompt provides enough context so the AI knows exactly what to do.

Starting Projects

When to use: At the very beginning of a project. This prompt helps the AI

understand the high-level requirements and start building the foundation. Use it to

kick off a new app by specifying what youʼre building, the tech stack, and core

features. Itʼs your project brief.

How to use: Outline the type of application, key technologies (frontend framework,

backend, any services), and the primary features or pages. Then, direct the AI on

where to start (often the main page or an important feature). This establishes the

project scope and initial focus.

Example Prompt – Starting a New Project:

This prompt follows a proven structure for new projects . It first states the app type

and tech stack, then lists core features, and finally tells the AI where to begin (the

main dashboard page, with specifics). By doing this, you give Lovable a clear

roadmap to initiate the project. �Pro tip: Itʼs often wise to start with an empty project

and build up gradually, so the AI doesnʼt get overwhelmed .)

UI/UX Design

When to use: Any time you want to refine the look and feel of your app without

changing its functionality. This could be polishing the UI, adjusting layouts, or

implementing a specific design style.

How to use: Clearly specify the scope of the design changes and emphasize that

functionality should remain intact. The AI is quite good at styling, but you should

guide it on what “look” you want (e.g. modern, minimalist, match a certain design

system). If you have multiple changes, tackle them one at a time (e.g. first layout,

then colors). Always mention if there are parts of the UI that must not be altered

logic-wise.

Lovable has pretty good taste out of the box, but a targeted prompt can help

achieve a specific aesthetic or UX improvement . For example, you might want to

restyle a button, improve form layout, or ensure consistency in spacing.

Example Prompt – UI Only Changes:

I need a **task management** application with:

- **Tech Stack:** Next.js frontend, Tailwind CSS for styling, Supabase fo

- **Core Features:** Project and task creation, assigning tasks to users,

Start by building the **main dashboard page**, containing:

- A header with navigation,

- A list of projects with their status,

- and a button to create a new project.

Provide dummy data for now, and ensure the design is clean and responsive

On this page

Starting Projects

UI/UX Design

Responsiveness

Refactoring

Locking Files / Limiting Scope

Planning

Stripe Setup

Using Chat Mode vs Default Mode

Writing Knowledge Bases and PRDs

Documentation

Community

Product Announcement

Request Feature

Youtube

Introduction

User Guides

Prompt Engineering

Integrations

Tips and Tricks

Resources

Welcome

Messaging Limits

FAQ

Quickstart

Figma to Lovable

Visual Edit

Knowledge Files

Labs

Deploy

Launched

Prompting 1.1

Prompt Library

Debugging Prompts

Prompts & Integrations

Integrations

GitHub Integration

Supabase Integration

Stripe & Payments

Resend Integration

Clerk Integration

Make Integration

Replicate Integration

Troubleshooting

Using images in Lovable

Using Videos

Chrome Adds-on

Using Custom �Google� Fonts

Using npm packages

SEO

Changelog

Video tutorial

Community

Search or ask... Ctrl K Support App

https://docs.lovable.dev/introduction
https://discord.gg/lovable-dev
https://lovable.dev/blog?category=announcements
https://feedback.lovable.dev/
https://www.youtube.com/@lovable-labs/
https://docs.lovable.dev/introduction
https://docs.lovable.dev/user-guides/messaging-limits
https://docs.lovable.dev/faq
https://docs.lovable.dev/user-guides/quickstart
https://docs.lovable.dev/features/figma-to-lovable
https://docs.lovable.dev/features/visual-edit
https://docs.lovable.dev/features/knowledge
https://docs.lovable.dev/features/labs
https://docs.lovable.dev/features/deploy
https://docs.lovable.dev/features/launched
https://docs.lovable.dev/tips-tricks/prompting-one
https://docs.lovable.dev/tips-tricks/prompting-library
https://docs.lovable.dev/tips-tricks/prompting-debugging
https://docs.lovable.dev/integrations/prompt-integrations
https://docs.lovable.dev/integrations/introduction
https://docs.lovable.dev/integrations/git-integration
https://docs.lovable.dev/integrations/supabase
https://docs.lovable.dev/tips-tricks/setting-up-payments
https://docs.lovable.dev/integrations/resend
https://docs.lovable.dev/integrations/clerk
https://docs.lovable.dev/integrations/make
https://docs.lovable.dev/integrations/replicate
https://docs.lovable.dev/tips-tricks/troubleshooting
https://docs.lovable.dev/tips-tricks/using-images
https://docs.lovable.dev/tips-tricks/using-videos
https://docs.lovable.dev/integrations/chrome-extensions
https://docs.lovable.dev/tips-tricks/custom-fonts
https://docs.lovable.dev/features/npm-packages
https://docs.lovable.dev/tips-tricks/seo
https://docs.lovable.dev/changelog
https://docs.lovable.dev/user-guides/video-tutorials
https://docs.lovable.dev/tips-tricks/community
https://docs.lovable.dev/
mailto:<EMAIL>
https://lovable.dev/


In this prompt, we explicitly say to make solely visual enhancements and not affect

how the app works . This is crucial – it tells the AI “donʼt touch the logic.” We list

specifics (card design, color contrast, spacing) so the AI knows what aspects of

the UI to tweak. This kind of prompt is perfect after youʼve built features and want

to beautify the interface.

Responsiveness

When to use: When your appʼs layout needs to work across different screen sizes

(mobile, tablet, desktop). If you notice things look good on desktop but break on

mobile, itʼs time for a responsiveness prompt. Itʼs also wise to do this as a final pass

on any UI-heavy task.

How to use: Emphasize a mobile-first approach and ask the AI to ensure the design

is responsive at all standard breakpoints . If using a CSS framework like Tailwind,

mention to use its grid/flex and built-in breakpoints. You can also instruct the AI to

avoid any fixed widths or anything that would prevent fluid resizing. Providing an

example of what breaks on small screens (if you have one) can help, or simply say

“make everything adapt to smaller screens gracefully.”

��Example Prompt – Mobile Responsiveness:** 

In this prompt, we explicitly instruct the AI to make all designs responsive at every

breakpoint, focusing on mobile first . We even reference Tailwindʼs standard

breakpoints to guide the implementation. We clarify that the design and

functionality shouldnʼt fundamentally change; it should just work well on smaller

screens. This sets a clear expectation: the outcome should look the same design-

wise, but fluidly resize and re-stack for responsiveness.

�Using Lovableʼs image upload? You could attach a screenshot of a broken mobile
layout and ask: “Make it look like this on mobile.” Visual prompts can reinforce what
you describe.)

Refactoring

When to use: Periodically during development, especially if the AI or you have

added a lot of code and things are getting messy or slow. Refactoring means

cleaning up the code without changing what it does – improving structure,

readability, or performance. Lovable might even suggest refactoring if it detects a lot

of repeated patterns or long functions.

How to use: Identify the scope: is it a single file, a specific feature, or the whole

codebase? For a single file or component, you can prompt something like “Refactor

this file for clarity and efficiency, but do not alter its functionality or output.”

Emphasize that everything should behave the same after refactoring . If you want,

specify what to focus on (e.g., reduce duplication, improve variable names, simplify

logic). For larger-scale refactoring, itʼs wise to ask the AI to plan the refactor in steps

(see the next section on Planning) or audit the code structure first.

The app UI should be improved, **without changing any functionality**. 

- Keep all existing logic and state management as is.

- **Visual Enhancements:** Update the styling of the dashboard page: use 

- Ensure these changes do **not break any functionality or data flow**.

*Goal:* purely cosmetic improvements for a more polished look, with the a

Our app needs to be **fully responsive** across mobile, tablet, and deskt

- Follow a **mobile-first** strategy: prioritize the layout for small scr

- Use modern UI/UX best practices for responsive design. (For Tailwind CS

- Ensure every page (especially the dashboard and project detail pages) r

- **Do not change the core design or functionality**, just make sure it f

After making changes, please double-check the layout at iPhone 12 dimensi

Support Policy

Glossary

https://docs.lovable.dev/user-guides/support-policy
https://docs.lovable.dev/glossary


��Example Prompt – Safe File Refactor:** 

This prompt clearly states the component to refactor and the constraints (no

functional changes allowed). It prioritizes structure and maintainability . The AI will

go through the file, maybe reordering functions, renaming things for clarity,

commenting tricky parts, etc., but the output of the app should remain identical.

This helps prevent the dreaded scenario of a “refactor” accidentally breaking

something.

For bigger refactoring efforts (like many files or an entire project), consider having

the AI analyze the codebase first. You can use a prompt to get a report on what

could be improved and where (see the Debugging sectionʼs Full System Review

prompt for an idea). Then, apply changes incrementally. Refactor in small pieces

and test as you go, rather than one massive overhaul.

Locking Files / Limiting Scope

When to use: Sometimes you want the AI to focus on specific parts of the project

and leave everything else untouched – essentially “lock” certain files or areas so

they are not modified. This is useful if youʼve manually written some code or have a

stable component you donʼt want altered while the AI works on something else.

Since Lovable doesnʼt have a literal file-lock feature yet, using the prompt to

constrain scope is the next best thing.

How to use: In your prompt, explicitly instruct the AI not to change certain files or

components. You might say, “Donʼt edit the authentication files,” or “Keep the

HomePage component unchanged.” Also, be clear about where the AI should focus

changes. This directive should be included each time you prompt during that

sensitive period, to remind the AI of the boundary.

Example Prompt – Limit Scope of Changes:

Here we included a very direct constraint: _“refrain from altering pages X or Y and

focus changes solely on page Z.”_ . By repeating this in the prompt, we guide the

AIʼs attention. The task itself (adding a dashboard section) is given, but we

wrapped it with instructions about scope. This greatly reduces the chance of

Lovable tinkering with your login system while trying to add a dashboard feature.

Another scenario is when updating a very delicate feature. In such cases, you can

combine scope limitation with a cautionary tone. For example: “This update is

sensitive; proceed very carefully and avoid touching anything unrelated”. This was

demonstrated in a prompt like: _“This update is quite delicate… Steer clear of

shortcuts or assumptions — take a moment to seek clarification if unsure. Precision

is crucial.”_ . Including a line like that sets the AIʼs “mindset” to be extra cautious.

Planning

Refactor the **ProjectList component file**, but **keep its behavior and 

Goals:

- Improve the code structure and readability (simplify complex functions,

- Remove any unused variables or imports.

- Ensure the file follows best practices and is well-documented.

Do **not** introduce any new features or change how the component works f

Please **focus only on the Dashboard page** for this change. 

- Do **not modify** the `LoginPage.tsx` or `AuthProvider.tsx` files at al

- Concentrate your code edits on `Dashboard.tsx` and related dashboard co

Task: Add a new section to the Dashboard that shows “Tasks due this week”

*(Again, no changes to login or auth files – those are off-limits.)*



When to use: Before diving into a complex or multi-step implementation, or when

you have a big feature that could be broken into sub-tasks. Planning prompts are

also useful if you want the AI to outline an approach before writing code, so you can

verify the plan (and adjust it) without burning through code-generation credits on a

wrong path. Essentially, use this when the strategy isnʼt straightforward and youʼd

like the AIʼs help to think it through.

How to use: Ask the AI to produce a plan or checklist. You can say, “Outline a step-

by-step plan for X” or “Before coding, list the steps you will take to implement Y.”

This can be done in Chat mode to ensure it doesnʼt execute any code changes while

planning . After getting the plan, you might even discuss it (maybe have the AI

explain why each step is needed) and then proceed to implementation step by step.

Planning prompts are meta – they donʼt build the app directly, but they set the stage

for a smoother build.

Example Prompt – Planning a Feature Implementation:

This prompt tells the AI to act as a planner. It asks for a sequenced plan to

implement an “email notifications for overdue tasks” feature. We explicitly say not

to code yet (so weʼd run this in Chat mode or just trust that the AI will output a

plan). The AI might respond with something like:

��� Add a timestamp field to tasks for due date (if not already present).

��� Create a server-side function (or scheduled job) to check for overdue tasks

periodically.

��� Integrate email sending using an email service (e.g., Resend or SMTP) when

an overdue task is found.

��� Update the UI to allow users to toggle notifications on/off for a task (optional

setting).

��� Test the flow with a task that just passed its due time to ensure an email is

sent.

By reviewing such a plan, you can catch any issues (maybe we realize we need a

new DB table, or maybe step 4 is out-of-scope for now, etc.) before any coding

happens. Itʼs a lot easier to tweak the plan than to rewrite bad code. Planning

prompts save time in complex features by getting the approach right from the start .

Stripe Setup

When to use: When you want to integrate payments into your app using Stripe.

Lovable has integration points for Stripe, but it requires setting up keys, webhooks,

and UI for checkout. A prompt can handle the boilerplate of connecting to Stripeʼs

API. Use this when you need to add commerce (selling a product, subscription, etc.)

in your project.

How to use: Provide the details Stripe needs: mode (test or live), product or pricing

info, and redirect URLs after payment. Also, instruct how the UI should behave (e.g.,

a checkout form/modal). Itʼs crucial to mention that sensitive keys will be provided

securely (not hard-coded in the prompt)  – you typically store those in environment

variables or Lovableʼs secret storage. So you can say “assume I have set the API

keys in the environment.” This way, the AI will know to call the keys, not include

them literally. Additionally, specify not to alter unrelated code while setting up Stripe

(to avoid accidental changes).

��Example Prompt – Integrating Stripe Payments:** 

Before writing any code, **plan out the implementation** of the new Notif

- List each step required to add email notifications when a task is overd

- Consider both frontend (UI changes, if any) and backend (creating sched

- Ensure the plan keeps the current functionality stable – we can’t break

- Provide the plan as an ordered list (1, 2, 3, ...), with a brief explan

Once you outline the plan, pause for review. **Do not make any code chang

I want to **add Stripe payments** to the app.

- Use **Stripe in test mode** for now.



This prompt gives all the key details for Stripe: test mode, product IDs, what

happens on success/cancel, and where to put the checkout button. It explicitly

says not to touch anything else. The AI (and Lovableʼs Stripe integration helper) will

use this to scaffold the Stripe integration. Under the hood, Lovable might create a

serverless function (if using Supabase) to handle webhooks, etc., but you donʼt

have to prompt that separately – the instruction here is usually enough for a basic

setup .

Note: We included a line about API keys being secure because we never want

secret keys in the prompt. The docs remind us: _“Use your Stripe Secret Key in the

Supabase Edge Function secrets, and avoid including them in the prompt”_ . So by

telling the AI “assume itʼs configured,” you ensure the code will reference an

environment variable or config, not a plaintext key.

After running this prompt, test the payment flow with Stripeʼs test card numbers. If

something isnʼt working (e.g., the webhook), Lovable might show errors which you

can then debug or refine with another prompt.

Using Chat Mode vs Default Mode

When to use: Lovable has two modes for prompting: Default Mode (which

immediately applies changes to your project) and Chat-Only Mode (which is more

like a conversation without altering code until you say so). Knowing when to use

each can streamline your workflow. Use Default for straight coding tasks and use

Chat mode for brainstorming, debugging, or when you want to discuss changes

before executing them.

How to use:

Example use case – Default vs Chat:

Suppose you suspect there is outdated code in your project that needs cleaning

up. In Default mode, you might directly prompt:

Review the app and tell me where there is outdated code.

Lovable could try to both identify and possibly start refactoring it in one go . But

maybe you actually want to be careful and just get advice. In that case, youʼd

switch to Chat mode and ask something like:

Iʼm seeing some deprecated library warnings. What parts of the code might be
outdated, and how should we update them?

Now the AI will discuss this with you, rather than immediately rewriting files.

Similarly, if you have an error and you want the AI to analyze it, Chat mode is safer.

You can copy an error message and ask:

- We have a product in Stripe with ID `prod_12345` and a price ID `price_

- Implement a checkout button on the **Pricing page** that starts a Strip

- After successful payment, redirect the user to `/payment-success`. If t

Important:

- Assume API keys and webhook secrets are configured securely (do **not**

- Do **not** modify any other pages or features unrelated to payments.

Once done, provide any webhook endpoint setup instructions I need (e.g., 

Default Mode is great for when you have a well-defined feature to build or

change to make. You give the instruction, and Lovable will do it in one go if

possible.

Chat Mode, on the other hand, is useful if you want to have a back-and-forth or

analyze something (like asking “Why is this not working?” or “Whatʼs the best
way to do X?”) without immediately changing the codebase . In Chat Mode, the

AI will respond with analysis or a plan, and you usually have to explicitly say “go

ahead and implement” when ready.



What does this error mean, and how can we fix it?

The AI will explain and propose a solution. Once you agree, you might switch to

Default mode or explicitly instruct to apply the fix.

In summary, use Default Mode for straightforward building (“do X for me”), and

use Chat Mode for troubleshooting or design discussions (“why or how should we

do X?”). One concrete workflow is: Brainstorm in Chat (get a plan or identify

issues), then execute in Default. This approach is even recommended: for example,

_use Chat Mode to have the AI analyze errors before making changes_ . Itʼs like

using ChatGPT for advice and then applying the changes once youʼre confident.

�Make sure Chat-Only Mode is enabled in your settings under Labs if you donʼt see

it . This mode ensures the AI wonʼt write code to your project until you say so,

which is perfect for safe experimentation.)

Writing Knowledge Bases and PRDs

When to use: At the start of a project and whenever you have more context to give

the AI than can fit in a single prompt. In Lovable, the Knowledge Base is a special

place in your project settings where you can store background information,

requirements, and guidelines that persist across prompts . A Project Requirements

Document �PRD� is a comprehensive summary of your appʼs objectives and specs –

essentially, the blueprint of what youʼre building. Use these tools to prevent

misunderstanding and to anchor the AI with the big picture of your project.

How to use: Begin by filling out the Knowledge Base in Lovable with key info about

your project. This can include:

This might sound like a lot, but writing it down pays off. The AI will use this info on

every prompt to stay aligned with your projectʼs needs . Itʼs like giving the AI long-

term memory about your app. You typically set this up once (and update as

needed).

After populating the knowledge base, you can literally tell the AI to use it. For

example, your very first prompt to Lovable might be: _“Before writing any code,

please review the Knowledge Base and confirm you understand the project.”_  This

lets the AI summarize or acknowledge the context, and you can catch if it missed

something.

Example Prompt – Using the Knowledge Base:

This kind of summary from the AI confirms it has ingested your specifications

correctly. If it misunderstood anything, you can correct it in Chat mode. Once

confirmed, you proceed with building (the AI now “knows” the overall plan).

Writing a PRD and filling the Knowledge Base might feel like extra upfront work, but

it pays dividends throughout development. It reduces errors and AI hallucinations

because the AI always has a reference for what the app should do  . Itʼs especially

helpful for larger projects that canʼt be built in one or two prompts. Think of it as

seeding the AI with the same understanding you have in your head.

Overview & Goals: What the project is and the problem it solves.

User Flow: A description of how users navigate or use the app (e.g. “Users sign

up, then see a dashboard, can create tasks, etc.”) .

Core Features & Scope: What features are in scope (and maybe note any out-

of-scope to avoid feature creep) .

Tech Stack: What technologies, APIs, and services will be used .

Design Guidelines: Any specific UI/UX instructions – branding, color scheme,

layouts, accessibility requirements .

Backend Details: Outline data models, key database tables, integrations like

Stripe or Supabase, etc. ..

User: Before you write any code, please review the Knowledge Base and sha

AI (likely response): “Sure. Based on the Knowledge Base: You’re building



Was this page helpful? Yes No

Prompting 1.1 Debugging Prompts

Powered by Mintlify

Lastly, maintain the Knowledge Base as your project evolves. If you add a major

feature or change the scope, update the PRD/Knowledge Base document. This

way, future prompts will consider the new information. Itʼs much easier than re-

explaining context every time. In essence, the Knowledge Base + PRD is your AI

project handbook – it keeps everyone (you and the AI) on the same page about

what youʼre building and how.

https://docs.lovable.dev/tips-tricks/prompting-one
https://docs.lovable.dev/tips-tricks/prompting-debugging
https://x.com/lovable_dev
https://github.com/lovablelabs
https://linkedin.com/company/lovable-dev
https://mintlify.com/preview-request?utm_campaign=poweredBy&utm_medium=docs&utm_source=docs.lovable.dev



## Message 15

All the files uploaded by the user have been fully loaded. Searching won't provide additional information.
