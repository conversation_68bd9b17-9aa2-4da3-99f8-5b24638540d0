# ASCII UI/UX Tools

Conversation ID: 6841956c-64cc-8008-9ec3-620bf554566d

## Message 1

For generating ASCII-based UI/UX representations using custom GPT instructions, several tools and resources stand out:

---

### 🔧 Notable Tools and Resources

1. **Skill<PERSON><PERSON>p's Custom Instruction Generator**
   - **Description**: Transforms basic prompts into detailed AI training instructions.
   - **Use Case**: Ideal for creating structured prompts for UI/UX tasks.
   - **Access**: [<PERSON><PERSON><PERSON><PERSON><PERSON>'s Custom Instruction Generator](https://app.aiprm.com/gpts/g-2VxwgpMMk/custom-instruction-generator)

2. **Tibi <PERSON>’s 16 Custom Instructions for UX Designers**
   - **Description**: A collection of tailored instructions to enhance ChatGPT's UX design capabilities.
   - **Use Case**: Provides a foundation for generating ASCII UI/UX representations.
   - **Access**: [Tibi David’s Custom Instructions](https://tibidavid.gumroad.com/l/16-chatGPT-custom-instructions)

3. **Flowbite GPT**
   - **Description**: Optimizes web development workflows with custom UI components using Flowbite Library and Tailwind CSS.
   - **Use Case**: Assists in creating responsive UI components, which can be translated into ASCII representations.
   - **Access**: [Flowbite GPT](https://www.featuredgpts.com/categories/ui-ux-design/)

---

### 🎨 Enhanced ASCII Representation for `/logoexporter`

Building upon your initial ASCII layout, here's an improved version that emphasizes clarity and modularity:

```
┌──────────────────────────────────────────────────────────┐
│                   Logo Exporter Interface                │
├──────────────────────────────────────────────────────────┤
│  Base URL: https://ringerikelandskap.no/logoexporter     │
├──────────────────────────────────────────────────────────┤
│  [ Size       ]  [ Type       ]  [ Format     ]           │
│  [ 1K ▼      ]  [ Digital ▼  ]  [ PNG ▼      ]           │
│  [ 2K ▼      ]  [ Print ▼    ]  [ SVG ▼      ]           │
│  [ 4K ▼      ]                [ EPS ▼      ]           │
├──────────────────────────────────────────────────────────┤
│  [ Colorspace ]  [ Style      ]  [ Variant    ]           │
│  [ RGB ▼     ]  [ Light ▼    ]  [ A ▼        ]           │
│  [ CMYK ▼    ]  [ Dark ▼     ]  [ B ▼        ]           │
│                [ Mono ▼     ]  [ C ▼        ]           │
├──────────────────────────────────────────────────────────┤
│                  [ Process and Download ]                │
└──────────────────────────────────────────────────────────┘
```

**Notes**:

- **Dropdown Indicators**: Each option includes a "▼" to denote a dropdown selection.
- **Grouping**: Parameters are grouped logically to enhance user comprehension.
- **Clarity**: The layout emphasizes readability and ease of navigation.

---

### 🧠 Recommendations for Custom GPT Instructions

To effectively generate ASCII UI/UX representations:

- **Define Clear Roles**: Specify the GPT's role, e.g., "Act as a UX designer specializing in ASCII wireframes."
- **Set Formatting Guidelines**: Instruct the GPT to use specific ASCII characters for layout consistency.
- **Provide Examples**: Include sample ASCII representations to guide the GPT's output.
- **Iterate and Refine**: Review the generated ASCII art and provide feedback for improvements.

By leveraging these tools and guidelines, you can enhance the generation of ASCII-based UI/UX representations tailored to your specific needs.

--- 

## Message 2

find the most popular custom gpt/instruction/alternative for generating ascii ui/ux representations according to this scenario:

    please draw inspiration from this unrelated example:

    ```

    # HIERARCHICAL STRUCTURE

    System ({system_id})

    ├── Template A ({component_function})

    │   ├── [Title]

    │   ├── Interpretation

    │   └── `{Transformation}`

    ├── Template B ({component_function})

    └── Template C ({component_function})



    # TEMPLATE FORMAT

    [Title] Interpretation Execute as: `{Transformation}`

      │      │              │         └─ Machine-parsable parameters

      │      │              └─ Standard connector phrase

      │      └─ Human-readable instructions

      └─ Template identifier



    # COMPONENT VISUALIZATION



    ┌─ Title ─────────────────────────────────────┐

    │ [Instruction Converter]                     │

    └────────────────────────────────────────────┬┘

                                                 │

    ┌─ Interpretation ───────────────────────┐   │

    │ Your goal is not to **answer** the     │   │

    │ input prompt, but to **rephrase** it,  │   │

    │ and to do so by the parameters defined │   │

    │ *inherently* within this message.      │   │

    │ Execute as:                            │   │

    └───────────────────────────────────────┬┘   │

                                            │    │

    ┌─ Transformation ───────────────────┐  │    │

    │ `{                                 │  │    │

    │   role=instruction_converter;      │  │    │

    │   input=[original_text:str];       │◄─┴────┘

    │   process=[

    │     strip_first_person_references(),

    │     convert_statements_to_directives(),

    │     identify_key_actions(),

    │     ...

    │   ];

    │   constraints=[

    │     deliver_clear_actionable_commands(),

    │     preserve_original_sequence(),

    │     ...

    │   ];

    │   requirements=[

    │     remove_self_references(),

    │     use_command_voice(),

    │     ...

    │   ];

    │   output={instruction_format:str}

    │ }`

    └─────────────────────────────────────┘



    # TRANSFORMATION STRUCTURE



    ┌─ Role ──────────────────────────────────────┐

    │ role={function_identifier}                  │

    │ # Defines template's primary function       │

    └────────────────────────────────────────────┬┘

                                                 │

    ┌─ Input ─────────────────────────────────┐  │

    │ input=[{parameter}:{type}]              │  │

    │ # Specifies input parameters and types  │  │

    └─────────────────────────────────────────┘  │

                                                 │

    ┌─ Process ───────────────────────────────┐  │

    │ process=[                               │  │

    │   {operation_1}(),                      │  │

    │   {operation_2}(),                      │◄─┘

    │   ...

    │ ]

    │ # Defines processing operations

    └─────────────────────────────────────────┘



    ┌─ Constraints ─────────────────────────────┐

    │ constraints=[                             │

    │   {constraint_1}(),                       │

    │   {constraint_2}(),                       │

    │   ...                                     │

    │ ]                                         │

    │ # Sets operational boundaries             │

    └──────────────────────────────────────────┬┘

                                               │

    ┌─ Requirements ──────────────────────┐    │

    │ requirements=[                      │    │

    │   {requirement_1}(),                │    │

    │   {requirement_2}(),                │    │

    │   ...                               │    │

    │ ]                                   │    │

    │ # Defines mandatory behaviors       │    │

    └────────────────────────────────────┬┘    │

                                         │     │

    ┌─ Output ─────────────────────┐     │     │

    │ output={parameter:{type}}    │◄────┴─────┘

    │ # Specifies return format    │

    └──────────────────────────────┘

    ```



    then improve the ascii representation accordingly:

    ```json

    * Proposed Structure: `"/logoexporter"`

    * Accessible at `"https://ringerikelandskap.no/logoexporter"`

    * Accepts query params:

      - `size`       : [`1K`, `2K`, `4K`, etc]            // Default: 2K (e.g., 1K, 2K, 4K)

      - `type`       : [`Digital`, `Print`, etc.]         // Default: Digital

      - `format`     : [`svg`, `eps`, `png`, `webp`, etc] // Default: png (if Digital) / eps (if Print)

      - `colorspace` : [`RGB`, `CMYK`, etc.]              // Default: RGB (if Digital) / CMYK (if Print)

      - `style`      : [`Light`, `Dark`, `Mono`, etc.]    // Default: Light (e.g., Dark, Mono)

      - `variant`    : [`a`, `b`, `c`, etc.]              // Default: a (e.g., b, c)



        +-------------------------------------------+

        |                                           |

        |  +-------------------------------------+  |

        |  | Tool: ringerikelandskap.no/logoexp  |  |  // Base URL of the tool

        |  +-------------------------------------+  |

        |                                           |

        |   Select options for your logo:           |

        |                                           |

        |   Size:       [ 2K         ▼]             |  // Default: 2K (e.g., 1K, 2K, 4K)

        |   Type:       [ Digital    ▼]             |  // Default: Digital (e.g., Print)

        |   Format:     [ png        ▼]             |  // Default: png (if Digital) / eps (if Print)

        |   Colorspace: [ RGB        ▼]             |  // Default: RGB (if Digital) / CMYK (if Print)

        |                                           |

        |  +-------------------------------------+  |

        |  | Style:   [ Light      ▼]            |  |  // Default: Light (e.g., Dark, Mono)

        |  | Variant: [ a          ▼]            |  |  // Default: a (e.g., b, c)

        |  +-------------------------------------+  |

        |                                           |

        |  +-------------------------------------+  |

        |  |        Process and Download         |  |  // URL/Example: "../logoexporter?size=2K&type=Digital&format=png..."

        |  +-------------------------------------+  |

        |                                           |

        +-------------------------------------------+

    ```

