# Service Mapping Optimization

Conversation ID: 681cd58a-f4ec-8008-9dae-f283c35dc856

## Message 1

How could this service-mappings.ts be most drastically optimized to maximize code consolidation and minimize redundancy, reducing complexity and line count across the codebase without creating new files?

## `service-mapping.ts`

	/**
	 * Service Mappings
	 *
	 * This file provides a consolidated mapping system for services, categories, features, and seasons.
	 * It serves as a single source of truth for service relationships throughout the application.
	 */

	import { SeasonType } from '@/lib/types/content';

	/**
	 * Service category definitions with display order and service references
	 */
	export const SERVICE_CATEGORIES = {
	  // Outdoor elements - Order matches the spring season order
	  FERDIGPLEN: {
	    id: 'ferdigplen',
	    name: '<PERSON><PERSON><PERSON>ple<PERSON>',
	    order: 10, // Spring order: 1
	    serviceId: 'ferdigplen'
	  },
	  HEKK_OG_BEPLANTNING: {
	    id: 'hekk',
	    name: 'Hekk og Be<PERSON>lant<PERSON>',
	    order: 20, // Spring order: 2
	    serviceId: 'hekk'
	  },
	  BELEGNINGSSTEIN: {
	    id: 'belegning<PERSON><PERSON>',
	    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
	    order: 30, // Spring order: 3
	    serviceId: 'belegning<PERSON><PERSON>'
	  },
	  STOTTEMURER: {
	    id: 'stottemurer',
	    name: 'Støttemurer',
	    order: 40, // Spring order: 4
	    serviceId: 'stottemurer'
	  },
	  KANTSTEIN: {
	    id: 'kantstein',
	    name: 'Kantstein',
	    order: 50, // Spring order: 5
	    serviceId: 'kantstein'
	  },
	  CORTENSTAAL: {
	    id: 'cortenstaal',
	    name: 'Cortenstål',
	    order: 60, // Spring order: 6
	    serviceId: 'cortenstaal'
	  },
	  TRAPPER_OG_REPOER: {
	    id: 'trapper',
	    name: 'Trapper og Repoer',
	    order: 70, // Spring order: 7
	    serviceId: 'trapp-repo'
	  },
	  PLATTING: {
	    id: 'platting',
	    name: 'Platting',
	    order: 80, // Spring order: 8
	    serviceId: 'platting'
	  },
	  PLANLEGGING_OG_DESIGN: {
	    id: 'planlegging',
	    name: 'Planlegging og Design',
	    order: 90, // Spring order: 9
	    serviceId: 'planlegging'
	  }
	} as const;

	/**
	 * Service feature definitions with display order
	 */
	export const SERVICE_FEATURES = {
	  // Garden maintenance
	  PLANTING: { id: 'planting', name: 'Planting', order: 10 },
	  VANNING: { id: 'vanning', name: 'Vanning', order: 20 },
	  GJODSLING: { id: 'gjodsling', name: 'Gjødsling', order: 30 },
	  JORDARBEID: { id: 'jordarbeid', name: 'Jordarbeid', order: 40 },
	  BESKJAERING: { id: 'beskjaering', name: 'Beskjæring', order: 50 },
	  // Construction
	  ANLEGG: { id: 'anlegg', name: 'Anlegg', order: 60 },
	  TERRASSE: { id: 'terrasse', name: 'Terrasse', order: 70 },
	  // Maintenance
	  VEDLIKEHOLD: { id: 'vedlikehold', name: 'Vedlikehold', order: 80 },
	  DRENERING: { id: 'drenering', name: 'Drenering', order: 90 },
	  VINTERKLARGJORING: { id: 'vinterklargjoring', name: 'Vinterklargjøring', order: 100 },
	  // Planning
	  PLANLEGGING: { id: 'planlegging', name: 'Planlegging', order: 110 },
	  DESIGN: { id: 'design', name: 'Design', order: 120 },
	  PROSJEKTERING: { id: 'prosjektering', name: 'Prosjektering', order: 130 }
	} as const;

	/**
	 * Season definitions with display order
	 */
	export const SEASONS = {
	  VAR: { id: 'vår', name: 'Vår', displayName: 'Våren', englishName: 'spring', order: 10 },
	  SOMMER: { id: 'sommer', name: 'Sommer', displayName: 'Sommeren', englishName: 'summer', order: 20 },
	  HOST: { id: 'høst', name: 'Høst', displayName: 'Høsten', englishName: 'fall', order: 30 },
	  VINTER: { id: 'vinter', name: 'Vinter', displayName: 'Vinteren', englishName: 'winter', order: 40 }
	} as const;

	/**
	 * Forward mappings - the single source of truth for relationships
	 */

	// Season to Category mappings with explicit order
	export const seasonToCategoryIds: Record<SeasonType, string[]> = {
	  // Spring - Most services are available
	  'vår': [
	    SERVICE_CATEGORIES.FERDIGPLEN.id,              // sesong:vår:order:1
	    SERVICE_CATEGORIES.HEKK_OG_BEPLANTNING.id,     // sesong:vår:order:2
	    SERVICE_CATEGORIES.BELEGNINGSSTEIN.id,         // sesong:vår:order:3
	    SERVICE_CATEGORIES.STOTTEMURER.id,             // sesong:vår:order:4
	    SERVICE_CATEGORIES.KANTSTEIN.id,               // sesong:vår:order:5
	    SERVICE_CATEGORIES.CORTENSTAAL.id,             // sesong:vår:order:6
	    SERVICE_CATEGORIES.TRAPPER_OG_REPOER.id,       // sesong:vår:order:7
	    SERVICE_CATEGORIES.PLATTING.id,                // sesong:vår:order:8
	  ],
	  // Summer - Most services are available
	  'sommer': [
	    SERVICE_CATEGORIES.FERDIGPLEN.id,              // sesong:vår:order:1
	    SERVICE_CATEGORIES.HEKK_OG_BEPLANTNING.id,     // sesong:vår:order:2
	    SERVICE_CATEGORIES.BELEGNINGSSTEIN.id,         // sesong:sommer:order:2
	    SERVICE_CATEGORIES.STOTTEMURER.id,             // sesong:sommer:order:1
	    SERVICE_CATEGORIES.KANTSTEIN.id,               // sesong:sommer:order:6
	    SERVICE_CATEGORIES.CORTENSTAAL.id,             // sesong:sommer:order:5
	    SERVICE_CATEGORIES.PLATTING.id,                // sesong:sommer:order:4
	    SERVICE_CATEGORIES.TRAPPER_OG_REPOER.id,       // sesong:sommer:order:7
	  ],
	  // Fall - Some services are available
	  'høst': [
	    SERVICE_CATEGORIES.FERDIGPLEN.id,              // sesong:vår:order:1
	    SERVICE_CATEGORIES.BELEGNINGSSTEIN.id,         // sesong:høst:order:3
	    SERVICE_CATEGORIES.STOTTEMURER.id,             // sesong:høst:order:4
	    SERVICE_CATEGORIES.KANTSTEIN.id,               // sesong:høst:order:5
	    SERVICE_CATEGORIES.PLATTING.id,                // sesong:høst:order:1
	    SERVICE_CATEGORIES.CORTENSTAAL.id,             // sesong:høst:order:2
	    SERVICE_CATEGORIES.TRAPPER_OG_REPOER.id,       // sesong:høst:order:6
	    SERVICE_CATEGORIES.PLANLEGGING_OG_DESIGN.id    // sesong:vinter:order:4
	  ],
	  // Winter - Limited services are available
	  'vinter': [
	    SERVICE_CATEGORIES.BELEGNINGSSTEIN.id,         // sesong:vinter:order:1
	    SERVICE_CATEGORIES.STOTTEMURER.id,             // sesong:vinter:order:2
	    SERVICE_CATEGORIES.KANTSTEIN.id,               // sesong:vinter:order:3
	    SERVICE_CATEGORIES.PLANLEGGING_OG_DESIGN.id    // sesong:vinter:order:4
	  ]
	};

	// Season to Feature mappings with explicit order
	export const seasonToFeatureIds: Record<SeasonType, string[]> = {
	  // Spring features
	  'vår': [
	    SERVICE_FEATURES.PLANTING.id,                  // sesong:vår:feature:order:1
	    SERVICE_FEATURES.VANNING.id,                   // sesong:vår:feature:order:2
	    SERVICE_FEATURES.GJODSLING.id,                 // sesong:vår:feature:order:3
	    SERVICE_FEATURES.JORDARBEID.id,                // sesong:vår:feature:order:4
	    SERVICE_FEATURES.ANLEGG.id,                    // sesong:vår:feature:order:5
	    SERVICE_FEATURES.TERRASSE.id,                  // sesong:vår:feature:order:6
	    SERVICE_FEATURES.VEDLIKEHOLD.id,               // sesong:vår:feature:order:7
	    SERVICE_FEATURES.PLANLEGGING.id,               // sesong:vår:feature:order:8
	    SERVICE_FEATURES.DESIGN.id,                    // sesong:vår:feature:order:9
	    SERVICE_FEATURES.PROSJEKTERING.id              // sesong:vår:feature:order:10
	  ],
	  // Summer features
	  'sommer': [
	    SERVICE_FEATURES.PLANTING.id,                  // sesong:sommer:feature:order:1
	    SERVICE_FEATURES.VANNING.id,                   // sesong:sommer:feature:order:2
	    SERVICE_FEATURES.GJODSLING.id,                 // sesong:sommer:feature:order:3
	    SERVICE_FEATURES.JORDARBEID.id,                // sesong:sommer:feature:order:4
	    SERVICE_FEATURES.ANLEGG.id,                    // sesong:sommer:feature:order:5
	    SERVICE_FEATURES.TERRASSE.id,                  // sesong:sommer:feature:order:6
	    SERVICE_FEATURES.VEDLIKEHOLD.id,               // sesong:sommer:feature:order:7
	    SERVICE_FEATURES.PLANLEGGING.id,               // sesong:sommer:feature:order:8
	    SERVICE_FEATURES.DESIGN.id,                    // sesong:sommer:feature:order:9
	    SERVICE_FEATURES.PROSJEKTERING.id              // sesong:sommer:feature:order:10
	  ],
	  // Fall features
	  'høst': [
	    SERVICE_FEATURES.PLANTING.id,                  // sesong:høst:feature:order:1
	    SERVICE_FEATURES.JORDARBEID.id,                // sesong:høst:feature:order:2
	    SERVICE_FEATURES.ANLEGG.id,                    // sesong:høst:feature:order:3
	    SERVICE_FEATURES.TERRASSE.id,                  // sesong:høst:feature:order:4
	    SERVICE_FEATURES.BESKJAERING.id,               // sesong:høst:feature:order:5
	    SERVICE_FEATURES.DRENERING.id,                 // sesong:høst:feature:order:6
	    SERVICE_FEATURES.VINTERKLARGJORING.id,         // sesong:høst:feature:order:7
	    SERVICE_FEATURES.PLANLEGGING.id,               // sesong:høst:feature:order:8
	    SERVICE_FEATURES.DESIGN.id,                    // sesong:høst:feature:order:9
	    SERVICE_FEATURES.PROSJEKTERING.id              // sesong:høst:feature:order:10
	  ],
	  // Winter features
	  'vinter': [
	    SERVICE_FEATURES.PLANLEGGING.id,               // sesong:vinter:feature:order:1
	    SERVICE_FEATURES.DESIGN.id,                    // sesong:vinter:feature:order:2
	    SERVICE_FEATURES.PROSJEKTERING.id,             // sesong:vinter:feature:order:3
	    SERVICE_FEATURES.ANLEGG.id                     // sesong:vinter:feature:order:4
	  ]
	};

	// Category to Feature mappings
	export const categoryToFeatureIds: Record<string, string[]> = {
	  // Ferdigplen features
	  'ferdigplen': [
	    SERVICE_FEATURES.PLANTING.id,
	    SERVICE_FEATURES.VANNING.id,
	    SERVICE_FEATURES.GJODSLING.id,
	    SERVICE_FEATURES.JORDARBEID.id,
	    SERVICE_FEATURES.VEDLIKEHOLD.id
	  ],
	  // Hekk og Beplantning features
	  'hekk': [
	    SERVICE_FEATURES.PLANTING.id,
	    SERVICE_FEATURES.VANNING.id,
	    SERVICE_FEATURES.GJODSLING.id,
	    SERVICE_FEATURES.JORDARBEID.id,
	    SERVICE_FEATURES.BESKJAERING.id,
	    SERVICE_FEATURES.VEDLIKEHOLD.id
	  ],
	  // Platting features
	  'platting': [
	    SERVICE_FEATURES.ANLEGG.id,
	    SERVICE_FEATURES.TERRASSE.id,
	    SERVICE_FEATURES.VEDLIKEHOLD.id
	  ],
	  // Cortenstål features
	  'cortenstaal': [
	    SERVICE_FEATURES.ANLEGG.id,
	    SERVICE_FEATURES.JORDARBEID.id
	  ],
	  // Belegningsstein features
	  'belegningsstein': [
	    SERVICE_FEATURES.ANLEGG.id,
	    SERVICE_FEATURES.JORDARBEID.id,
	    SERVICE_FEATURES.VEDLIKEHOLD.id
	  ],
	  // Støttemurer features
	  'stottemurer': [
	    SERVICE_FEATURES.ANLEGG.id,
	    SERVICE_FEATURES.JORDARBEID.id,
	    SERVICE_FEATURES.DRENERING.id
	  ],
	  // Kantstein features
	  'kantstein': [
	    SERVICE_FEATURES.ANLEGG.id,
	    SERVICE_FEATURES.JORDARBEID.id
	  ],
	  // Trapper og Repoer features
	  'trapper': [
	    SERVICE_FEATURES.ANLEGG.id,
	    SERVICE_FEATURES.JORDARBEID.id
	  ],
	  // Planlegging og Design features
	  'planlegging': [
	    SERVICE_FEATURES.PLANLEGGING.id,
	    SERVICE_FEATURES.DESIGN.id,
	    SERVICE_FEATURES.PROSJEKTERING.id
	  ]
	};

	/**
	 * Utility function to build reverse mappings
	 * Creates the inverse relationship from a forward mapping
	 */
	function buildReverseMapping<K extends string, V extends string>(
	  forward: Record<K, V[]>
	): Record<string, string[]> {
	  const reversed: Record<string, string[]> = {};

	  Object.entries(forward).forEach(([key, values]) => {
	    values.forEach(value => {
	      if (!reversed[value]) {
	        reversed[value] = [];
	      }
	      reversed[value].push(key);
	    });
	  });

	  return reversed;
	}

	// Generate reverse mappings automatically
	export const categoryToSeasonIds = buildReverseMapping(seasonToCategoryIds);
	export const featureToCategoryIds = buildReverseMapping(categoryToFeatureIds);
	export const featureToSeasonIds = buildReverseMapping(seasonToFeatureIds);

	/**
	 * Generic utility function to map IDs to names
	 */
	type IdDefinition = { id: string; name: string; order?: number };

	function mapIdsToNames<T extends IdDefinition>(
	  allDefinitions: Record<string, T>,
	  ids: string[]
	): string[] {
	  return ids.map(id => {
	    const entry = Object.values(allDefinitions).find(def => def.id === id);
	    return entry ? entry.name : id;
	  });
	}

	// Specialized mapping functions for convenience
	export function mapCategoryIdsToNames(ids: string[]): string[] {
	  return mapIdsToNames(SERVICE_CATEGORIES, ids);
	}

	export function mapFeatureIdsToNames(ids: string[]): string[] {
	  return mapIdsToNames(SERVICE_FEATURES, ids);
	}

	/**
	 * Utility function to normalize a string to an ID
	 */
	function normalizeToId<T extends IdDefinition>(
	  allDefinitions: Record<string, T>,
	  nameOrId: string
	): string {
	  const entry = Object.values(allDefinitions).find(
	    def => def.name === nameOrId || def.id === nameOrId
	  );
	  return entry?.id || nameOrId;
	}

	// Specialized normalization functions for convenience
	export function normalizeCategoryToId(category: string): string {
	  return normalizeToId(SERVICE_CATEGORIES, category);
	}

	export function normalizeFeatureToId(feature: string): string {
	  return normalizeToId(SERVICE_FEATURES, feature);
	}

	/**
	 * Service ID to category mapping (for backward compatibility)
	 */
	export const SERVICE_ID_TO_CATEGORY = Object.values(SERVICE_CATEGORIES).reduce(
	  (acc, category) => {
	    acc[category.serviceId] = category.name;
	    return acc;
	  },
	  {} as Record<string, string>
	);

	/**
	 * For backward compatibility
	 */
	export const SEASON_TO_SERVICE_CATEGORIES: Record<SeasonType, string[]> = {
	  'vår': mapCategoryIdsToNames(seasonToCategoryIds['vår']),
	  'sommer': mapCategoryIdsToNames(seasonToCategoryIds['sommer']),
	  'høst': mapCategoryIdsToNames(seasonToCategoryIds['høst']),
	  'vinter': mapCategoryIdsToNames(seasonToCategoryIds['vinter'])
	};

	/**
	 * For backward compatibility
	 */
	export const SEASON_TO_SERVICE_FEATURES: Record<SeasonType, string[]> = {
	  'vår': mapFeatureIdsToNames(seasonToFeatureIds['vår']),
	  'sommer': mapFeatureIdsToNames(seasonToFeatureIds['sommer']),
	  'høst': mapFeatureIdsToNames(seasonToFeatureIds['høst']),
	  'vinter': mapFeatureIdsToNames(seasonToFeatureIds['vinter'])
	};

	/**
	 * For backward compatibility
	 */
	export const SERVICE_CATEGORY_TO_SEASONS = categoryToSeasonIds;

	/**
	 * Comprehensive mapping structure that defines all relationships (for backward compatibility)
	 */
	export const SERVICE_MAPPINGS = {
	  // Forward mappings
	  seasonToCategories: seasonToCategoryIds,
	  seasonToFeatures: seasonToFeatureIds,
	  categoryToFeatures: categoryToFeatureIds,

	  // Reverse mappings (now generated automatically)
	  categoryToSeasons: categoryToSeasonIds,
	  featureToCategories: featureToCategoryIds,
	  featureToSeasons: featureToSeasonIds
	};

	// Helper functions

	/**
	 * Helper function to get all service categories for a season
	 * @param season The season to get categories for
	 * @returns Array of category names in the defined order
	 */
	export const getServiceCategoriesForSeason = (season: SeasonType): string[] => {
	  const categoryIds = seasonToCategoryIds[season] || [];
	  return mapCategoryIdsToNames(categoryIds);
	};

	/**
	 * Helper function to get all service features for a season
	 * @param season The season to get features for
	 * @returns Array of feature names in the defined order
	 */
	export const getServiceFeaturesForSeason = (season: SeasonType): string[] => {
	  const featureIds = seasonToFeatureIds[season] || [];
	  return mapFeatureIdsToNames(featureIds);
	};

	/**
	 * Helper function to get all seasons for a service category
	 * @param category The category to get seasons for
	 * @returns Array of season names
	 */
	export const getSeasonsForServiceCategory = (category: string): SeasonType[] => {
	  const categoryId = normalizeCategoryToId(category);
	  return categoryToSeasonIds[categoryId] || [];
	};

	/**
	 * Helper function to get all features for a category
	 * @param category The category to get features for
	 * @returns Array of feature names in the defined order
	 */
	export const getFeaturesForCategory = (category: string): string[] => {
	  const categoryId = normalizeCategoryToId(category);
	  const featureIds = categoryToFeatureIds[categoryId] || [];
	  return mapFeatureIdsToNames(featureIds);
	};

	/**
	 * Helper function to get all categories for a feature
	 * @param feature The feature to get categories for
	 * @returns Array of category names in the defined order
	 */
	export const getCategoriesForFeature = (feature: string): string[] => {
	  const featureId = normalizeFeatureToId(feature);
	  const categoryIds = featureToCategoryIds[featureId] || [];
	  return mapCategoryIdsToNames(categoryIds);
	};

	/**
	 * Helper function to check if a category and feature are compatible
	 * @param category The category to check
	 * @param feature The feature to check
	 * @returns Whether the category and feature are compatible
	 */
	export const areCategoryAndFeatureCompatible = (category: string, feature: string): boolean => {
	  const featuresForCategory = getFeaturesForCategory(category);
	  return featuresForCategory.includes(feature);
	};

	/**
	 * Helper function to get the service ID for a category
	 * @param category The category name or ID to get the service ID for
	 * @returns The service ID for the category
	 */
	export const getServiceIdForCategory = (category: string): string | undefined => {
	  const categoryEntry = Object.values(SERVICE_CATEGORIES).find(
	    cat => cat.name === category || cat.id === category
	  );
	  return categoryEntry?.serviceId;
	};

	/**
	 * Helper function to get all categories in order
	 * @returns Array of category names sorted by the order property defined in SERVICE_CATEGORIES
	 *
	 * Note: The order property in SERVICE_CATEGORIES is set to match the spring season order.
	 * If you need a different order for a specific season, use getServiceCategoriesForSeason(season)
	 * which will use the order defined in the seasonToCategories arrays.
	 */
	export const getAllCategoriesInOrder = (): string[] => {
	  return Object.values(SERVICE_CATEGORIES)
	    .sort((a, b) => a.order - b.order)
	    .map(cat => cat.name);
	};

	/**
	 * Helper function to get all features in order
	 * @returns Array of feature names sorted by order
	 */
	export const getAllFeaturesInOrder = (): string[] => {
	  return Object.values(SERVICE_FEATURES)
	    .sort((a, b) => a.order - b.order)
	    .map(feat => feat.name);
	};

	/**
	 * Helper function to get all seasons in order
	 * @returns Array of season names sorted by order
	 */
	export const getAllSeasonsInOrder = (): SeasonType[] => {
	  return Object.values(SEASONS)
	    .sort((a, b) => a.order - b.order)
	    .map(season => season.id as SeasonType);
	};

## Message 2

**Revisiting redundancy**

I’m thinking about removing redundant "backward compatibility" variables and roles, merging helper functions, and integrating season to category and feature relationships into a single, more efficient data structure.

**Consolidering functions**

I’m merging functions like "getServiceCategoriesForSeason" and "getServiceFeaturesForSeason" into a single, versatile function. This streamlines forward and backward mappings, centralizing definitions and making the code more efficient.

**Streamlining functions**

I’m unifying specialized mapping functions and eliminating redundant constants to create a single, comprehensive "SERVICE_MAPPINGS" object. The goal is to ensure minimal code duplication and a more efficient codebase.

## Message 3

Below is a **conceptual** example of how you could drastically reduce line count and redundancy **within the same file**, preserving (or auto-generating) all the old exports/functions you still rely on. The core idea is:

1. **Unify data** (seasons, categories, features) into **single-source-of-truth** objects.  
2. **Store relationships** directly in those objects (e.g. `season.categories` instead of separate `seasonToCategoryIds`).  
3. Use **shared utilities** to build forward/backward maps (instead of storing them by hand).  
4. Generate any “backward compatibility” exports from that single source.  

This approach **drastically cuts down** repetitive definitions (e.g. `seasonToCategoryIds`/`categoryToSeasonIds`, `seasonToFeatureIds`/`featureToSeasonIds`, etc.) by calculating them in a few loops. Below is a sample refactor illustrating the concept:

---

## Example Refactor (Concept)

```ts
/**
 * service-mappings.ts
 *
 * A drastically condensed version showing how to unify all definitions
 * and auto-generate forward/backward relationships in one place.
 */

import { SeasonType } from '@/lib/types/content';

/** -----------------------------------------------------------
 *  1) Single unified data structure for seasons, categories, features
 *     - Each key (season/category/feature) keeps relevant metadata
 *     - Seasons list which categories + features apply to them
 *     - Categories list which features apply to them, etc.
 * ------------------------------------------------------------*/

const MAPPINGS = {
  seasons: {
    vår: {
      id: 'vår',
      name: 'Vår',
      displayName: 'Våren',
      englishName: 'spring',
      order: 10,
      categories: ['ferdigplen','hekk','belegningsstein','stottemurer','kantstein','cortenstaal','trapper','platting'],
      features: ['planting','vanning','gjodsling','jordarbeid','anlegg','terrasse','vedlikehold','planlegging','design','prosjektering']
    },
    sommer: {
      id: 'sommer',
      name: 'Sommer',
      displayName: 'Sommeren',
      englishName: 'summer',
      order: 20,
      categories: ['ferdigplen','hekk','belegningsstein','stottemurer','kantstein','cortenstaal','platting','trapper'],
      features: ['planting','vanning','gjodsling','jordarbeid','anlegg','terrasse','vedlikehold','planlegging','design','prosjektering']
    },
    høst: {
      id: 'høst',
      name: 'Høst',
      displayName: 'Høsten',
      englishName: 'fall',
      order: 30,
      categories: ['ferdigplen','belegningsstein','stottemurer','kantstein','platting','cortenstaal','trapper','planlegging'],
      features: ['planting','jordarbeid','anlegg','terrasse','beskjaering','drenering','vinterklargjoring','planlegging','design','prosjektering']
    },
    vinter: {
      id: 'vinter',
      name: 'Vinter',
      displayName: 'Vinteren',
      englishName: 'winter',
      order: 40,
      categories: ['belegningsstein','stottemurer','kantstein','planlegging'],
      features: ['planlegging','design','prosjektering','anlegg']
    }
  },

  categories: {
    ferdigplen:        { id: 'ferdigplen',        name: 'Ferdigplen',        order: 10, serviceId: 'ferdigplen',        features: ['planting','vanning','gjodsling','jordarbeid','vedlikehold'] },
    hekk:              { id: 'hekk',              name: 'Hekk og Beplantning',order: 20, serviceId: 'hekk',              features: ['planting','vanning','gjodsling','jordarbeid','beskjaering','vedlikehold'] },
    belegningsstein:   { id: 'belegningsstein',   name: 'Belegningsstein',   order: 30, serviceId: 'belegningsstein',   features: ['anlegg','jordarbeid','vedlikehold'] },
    stottemurer:       { id: 'stottemurer',       name: 'Støttemurer',       order: 40, serviceId: 'stottemurer',       features: ['anlegg','jordarbeid','drenering'] },
    kantstein:         { id: 'kantstein',         name: 'Kantstein',         order: 50, serviceId: 'kantstein',         features: ['anlegg','jordarbeid'] },
    cortenstaal:       { id: 'cortenstaal',       name: 'Cortenstål',        order: 60, serviceId: 'cortenstaal',       features: ['anlegg','jordarbeid'] },
    trapper:           { id: 'trapper',           name: 'Trapper og Repoer', order: 70, serviceId: 'trapp-repo',        features: ['anlegg','jordarbeid'] },
    platting:          { id: 'platting',          name: 'Platting',          order: 80, serviceId: 'platting',          features: ['anlegg','terrasse','vedlikehold'] },
    planlegging:       { id: 'planlegging',       name: 'Planlegging og Design', order: 90, serviceId: 'planlegging',  features: ['planlegging','design','prosjektering'] }
  },

  features: {
    planting:          { id: 'planting',          name: 'Planting',          order: 10 },
    vanning:           { id: 'vanning',           name: 'Vanning',           order: 20 },
    gjodsling:         { id: 'gjodsling',         name: 'Gjødsling',         order: 30 },
    jordarbeid:        { id: 'jordarbeid',        name: 'Jordarbeid',        order: 40 },
    beskjaering:       { id: 'beskjaering',       name: 'Beskjæring',        order: 50 },
    anlegg:            { id: 'anlegg',            name: 'Anlegg',            order: 60 },
    terrasse:          { id: 'terrasse',          name: 'Terrasse',          order: 70 },
    vedlikehold:       { id: 'vedlikehold',       name: 'Vedlikehold',       order: 80 },
    drenering:         { id: 'drenering',         name: 'Drenering',         order: 90 },
    vinterklargjoring: { id: 'vinterklargjoring', name: 'Vinterklargjøring', order: 100 },
    planlegging:       { id: 'planlegging',       name: 'Planlegging',       order: 110 },
    design:            { id: 'design',            name: 'Design',            order: 120 },
    prosjektering:     { id: 'prosjektering',     name: 'Prosjektering',     order: 130 }
  }
} as const;

/** -----------------------------------------------------------
 *  2) Build universal forward/backward lookups on the fly
 * ------------------------------------------------------------*/

// Generic helper to invert Record<key, string[]>
function buildReverseMapping<K extends string, V extends string>(
  forward: Record<K, V[]>
): Record<V, K[]> {
  const reversed: Record<string, K[]> = {};
  for (const [k, list] of Object.entries(forward)) {
    list.forEach((value) => {
      if (!reversed[value]) reversed[value] = [];
      reversed[value].push(k as K);
    });
  }
  return reversed;
}

/** Build season->categories and season->features from our single source */
export const seasonToCategoryIds: Record<SeasonType, string[]> = {} as any;
export const seasonToFeatureIds: Record<SeasonType, string[]> = {} as any;

for (const [seasonId, seasonData] of Object.entries(MAPPINGS.seasons)) {
  seasonToCategoryIds[seasonId as SeasonType] = seasonData.categories;
  seasonToFeatureIds[seasonId as SeasonType] = seasonData.features;
}

/** Build category->features from our single source */
export const categoryToFeatureIds: Record<string, string[]> = {};
for (const [catId, catData] of Object.entries(MAPPINGS.categories)) {
  categoryToFeatureIds[catId] = catData.features;
}

/** Build automatically reversed mappings */
export const categoryToSeasonIds = buildReverseMapping(seasonToCategoryIds);
export const featureToSeasonIds = buildReverseMapping(seasonToFeatureIds);
export const featureToCategoryIds = buildReverseMapping(categoryToFeatureIds);

/** -----------------------------------------------------------
 *  3) Export “definition” constants for direct usage
 *     (Seasons, Categories, Features) sorted by "order"
 * ------------------------------------------------------------*/

export const SEASONS = Object.values(MAPPINGS.seasons).reduce((acc, s) => {
  acc[s.id] = s;
  return acc;
}, {} as Record<string, typeof MAPPINGS['seasons']['vår']>);

export const SERVICE_CATEGORIES = Object.values(MAPPINGS.categories).reduce((acc, c) => {
  acc[c.id] = c;
  return acc;
}, {} as Record<string, typeof MAPPINGS['categories']['ferdigplen']>);

export const SERVICE_FEATURES = Object.values(MAPPINGS.features).reduce((acc, f) => {
  acc[f.id] = f;
  return acc;
}, {} as Record<string, typeof MAPPINGS['features']['planting']>);

/** -----------------------------------------------------------
 *  4) Generic utility: map IDs to names
 * ------------------------------------------------------------*/

type IdDefinition = { id: string; name: string; order?: number };

function mapIdsToNames<T extends IdDefinition>(
  allDefinitions: Record<string, T>,
  ids: string[]
): string[] {
  return ids.map(id => allDefinitions[id]?.name ?? id);
}

/** -----------------------------------------------------------
 *  5) “Backwards compatibility” exports, but auto-derived
 * ------------------------------------------------------------*/

// 5a) Old season->names arrays
export const SEASON_TO_SERVICE_CATEGORIES: Record<SeasonType, string[]> = {
  vår: mapIdsToNames(SERVICE_CATEGORIES, seasonToCategoryIds.vår),
  sommer: mapIdsToNames(SERVICE_CATEGORIES, seasonToCategoryIds.sommer),
  høst: mapIdsToNames(SERVICE_CATEGORIES, seasonToCategoryIds.høst),
  vinter: mapIdsToNames(SERVICE_CATEGORIES, seasonToCategoryIds.vinter),
};

export const SEASON_TO_SERVICE_FEATURES: Record<SeasonType, string[]> = {
  vår: mapIdsToNames(SERVICE_FEATURES, seasonToFeatureIds.vår),
  sommer: mapIdsToNames(SERVICE_FEATURES, seasonToFeatureIds.sommer),
  høst: mapIdsToNames(SERVICE_FEATURES, seasonToFeatureIds.høst),
  vinter: mapIdsToNames(SERVICE_FEATURES, seasonToFeatureIds.vinter),
};

// 5b) For direct object references
export const SERVICE_ID_TO_CATEGORY = Object.values(MAPPINGS.categories).reduce(
  (acc, c) => {
    acc[c.serviceId] = c.name;
    return acc;
  },
  {} as Record<string, string>
);

// 5c) For old references
export const SERVICE_CATEGORY_TO_SEASONS = categoryToSeasonIds;

/** Comprehensive structure for older usage */
export const SERVICE_MAPPINGS = {
  seasonToCategories: seasonToCategoryIds,
  seasonToFeatures: seasonToFeatureIds,
  categoryToFeatures: categoryToFeatureIds,
  categoryToSeasons: categoryToSeasonIds,
  featureToCategories: featureToCategoryIds,
  featureToSeasons: featureToSeasonIds
};

/** -----------------------------------------------------------
 *  6) All helper functions remain, but drastically simplified
 * ------------------------------------------------------------*/

function normalizeToId<T extends IdDefinition>(
  allDefinitions: Record<string, T>,
  nameOrId: string
): string {
  // Try direct ID or fallback to name
  const defById = allDefinitions[nameOrId];
  if (defById) return defById.id;

  const defByName = Object.values(allDefinitions).find(d => d.name === nameOrId);
  return defByName?.id || nameOrId;
}

// Normalizers
export function normalizeCategoryToId(cat: string): string {
  return normalizeToId(SERVICE_CATEGORIES, cat);
}
export function normalizeFeatureToId(feat: string): string {
  return normalizeToId(SERVICE_FEATURES, feat);
}

// "Get" helpers
export const getServiceCategoriesForSeason = (season: SeasonType): string[] =>
  mapIdsToNames(SERVICE_CATEGORIES, seasonToCategoryIds[season] || []);

export const getServiceFeaturesForSeason = (season: SeasonType): string[] =>
  mapIdsToNames(SERVICE_FEATURES, seasonToFeatureIds[season] || []);

export const getSeasonsForServiceCategory = (category: string): SeasonType[] => {
  const catId = normalizeCategoryToId(category);
  return categoryToSeasonIds[catId] || [];
};

export const getFeaturesForCategory = (category: string): string[] => {
  const catId = normalizeCategoryToId(category);
  return mapIdsToNames(SERVICE_FEATURES, categoryToFeatureIds[catId] || []);
};

export const getCategoriesForFeature = (feature: string): string[] => {
  const featId = normalizeFeatureToId(feature);
  return mapIdsToNames(SERVICE_CATEGORIES, featureToCategoryIds[featId] || []);
};

export const areCategoryAndFeatureCompatible = (category: string, feature: string): boolean => {
  return getFeaturesForCategory(category).includes(feature);
};

export const getServiceIdForCategory = (category: string): string | undefined => {
  const catId = normalizeCategoryToId(category);
  return SERVICE_CATEGORIES[catId]?.serviceId;
};

export const getAllCategoriesInOrder = (): string[] =>
  Object.values(SERVICE_CATEGORIES)
    .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
    .map(c => c.name);

export const getAllFeaturesInOrder = (): string[] =>
  Object.values(SERVICE_FEATURES)
    .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
    .map(f => f.name);

export const getAllSeasonsInOrder = (): SeasonType[] =>
  Object.values(SEASONS)
    .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
    .map(s => s.id as SeasonType);
```

### Key Refactoring Points

1. **A Single Source of Truth**  
   - All category/feature/season data now lives in one `MAPPINGS` object.  
   - Each season lists the categories/features it uses.  
   - Each category lists the features it supports, etc.

2. **Auto-Generated Forward/Reverse Mappings**  
   - Instead of separately maintaining arrays like `seasonToCategoryIds` and `categoryToSeasonIds`, we keep only the forward definitions in `MAPPINGS.seasons.seasonX.categories` and `MAPPINGS.categories.categoryX.features`.  
   - A simple function (`buildReverseMapping`) inverts those references to produce reverse relationships automatically.

3. **Eliminated Repetitive Exports**  
   - Because everything is built from a single data source, a few loops or reduce calls can produce all older references (`SERVICE_CATEGORIES`, `SERVICE_FEATURES`, `SEASON_TO_SERVICE_FEATURES`, etc.) for backward compatibility—**without** duplicating arrays by hand.

4. **Condensed Helper Functions**  
   - The helper functions remain conceptually the same, but drastically simpler because they all rely on the **unified data** + minimal “map IDs → names” logic.

Overall, this approach greatly **reduces line count**, **avoids repetition**, and ensures that updating a single data property (e.g., adding a feature to a season) automatically ripples through all the forward/backward relationships. And it does so **all in one file**—no new files needed.
