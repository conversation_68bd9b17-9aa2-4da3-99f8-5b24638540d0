# File Naming Format Conversion

Conversation ID: 684431f5-6b14-8008-8611-4cf12f7cddf4

## Message 1

Here is the cleaned and consistently formatted list in the requested pattern: `YYYY--MainArtistArtist-ShortSongName.exe`  
_Note: Used `.exe` as per your request format, even though these are not executable files._

```
2018--DavidGuettaSia-SheWolf.exe
2018--NF-LetYouDown.exe
2018--Sia-SheWolf.exe
2018--WisinOzuna-EscapateConmigo.exe
2019--MOOMINVALLEY-LoveMeWithAllYourHeart.exe
2020--MatthewWilder-BreakMyStride.exe
2020--MelanieMartinez-PlayDate.exe
2020--TheAuthor-Roosberg.exe
2022--DivKid-IcelandicArpeggios.exe
2022--TheXX-Intro.exe
2022--TomOdell-AnotherLove.exe
2023--ModernTalking-CheriCheriLady.exe
2023--ReliableMusic-PastLives.exe
2023--TheComeUp-TheComeUp.exe
2024--Mastermind-413Point4.exe
2024--ALMA-Starlight.exe
2024--BillieEilish-EverythingIWanted.exe
2024--ChrisHolsten-SmiletIDittEgetSpeil.exe
2024--CLANN-IHoldYou.exe
2024--Nto-AlterEgo.exe
2024--Famy-Ava.exe
2024--Famy-Ghostmw2.exe
2024--GibranAlcocer-Solas.exe
2024--Fasetya-GlimpseOfUs.exe
2024--Iniko-Jericho.exe
2024--KALEO-WayDownWeGo.exe
2024--LionelRichie-SayYouSayMe.exe
2024--Livingston-Shadow.exe
2024--Unknown-LoveCute.exe
2024--MylesSmith-Solo.exe
2024--DashaAustin-Dasha.exe
2024--TheCranberries-Zombie.exe
2024--ShaunFrankKSHMR-Heaven.exe
2024--SofieFjellvang-LovedByYou.exe
2024--ShalomMargaret-SomeoneYouLoved.exe
2024--VivaLaDirtLeague-TheConfession.exe
2024--ThierryVonDerWarth-MyHeart.exe
```

### Notes:
- When the artist was unclear or multiple (e.g., remixes or covers), I used the remixer or source when most relevant.
- In ambiguous or parody/video content (`The Confession`), I used creator name or source.
- Replaced any special formatting like “(slowed and reverb)” with more descriptive or trimmed names where possible.

Let me know if you'd prefer a different delimiter (like `_` or `-` inside the artist/song name), or prefer to preserve remix/cover info.

## Message 2



```

2018--David Guetta feat. Sia - She Wolf (Nick Kech & John Rous Remix).mp3

2018--NF-Let You Down.mp3

2018--She Wolf (Falling to Pieces).mp3

2018--Wisin Ft. Ozuna - Escapate Conmigo (CDQ).mp3

2019--MOOMINVALLEY (2019) OST - 05 - Love Me with All Your Heart [Audio] [Lyrics] [Subtitles].mp3

2020--Matthew Wilder - Break My Stride (Lyrics).mp3

2020--Melanie Martinez - Play Date (Lyrics).mp3

2020--THE AUTHOR - Roosberg (LYRICS)-oA5n4QIGfcQ.mp3

2022--DivKid ~ Icelandic Arpeggios [LOOPED].mp3

2022--The XX-Intro.mp3

2022--Tom Odell - Another Love (slowed and reverb).mp3

2023--Modern Talking - Cheri Cheri Lady + Lyrics.mp3

2023--Reliable Music-don't wake me i'm not dreaming _ Past Lives.mp3

2023--The Come Up.mkv

2024--413.4 The Mastermind.mp3

2024--ALMA - Starlight (From the 'MOOMINVALLEY' Official Soundtrack).mp3

2024--Billie Eilish - everything i wanted (Official Audio).mp3

2024--Chris Holsten - Smilet i ditt eget speil (Offisiell Musikkvideo).mp3

2024--CLANN - I Hold You.mp3

2024--Dancecode Electronics-N'to - Alter Ego.mp3

2024--Famy - Ava.mp3

2024--Famy Ava - Slowed+reverb _ Ghostmw2.mp3

2024--Gibran Alcocer - Solas (Cover).mp3

2024--Glimpse of Us (Fasetya Remix).mp3

2024--Iniko - Jericho (Official Video).mp3

2024--KALEO - Way Down We Go (Official Music Video).mp3

2024--Lionel Richie - Say You, Say Me.mp3

2024--Livingston - Shadow (Lyrics) 'don't think twice you'll be dead in a second'.mp3

2024--Love Cute.mp3

2024--Myles Smith - Solo (Official Music Video).mp3

2024--NA-Dasha-Dasha - Austin (Official Music Video).mp3

2024--NA-TheCranberriesTV-The Cranberries - Zombie (Official Music Video).mp3

2024--Shaun Frank & KSHMR - Heaven (feat. Delaney Jane) [Official Lyric Video]-GOuHbx7krCk.mp3

2024--Sofie Fjellvang - Loved By You.mp3

2024--Someone You Loved (Shalom Margaret Cover) - Lofi Remix.mp3

2024--The Confession - Bored Ep 108 (When THAT customer comes in) _ Viva La Dirt League (VLDL).mp4

2024--Thierry Von Der Warth - My Heart.mp3

```



Please rewrite the provided filenames into songnames/titles *in concistent pattern* like this: `YYYY--MainArtistArtist-ShortSongName.exe`





