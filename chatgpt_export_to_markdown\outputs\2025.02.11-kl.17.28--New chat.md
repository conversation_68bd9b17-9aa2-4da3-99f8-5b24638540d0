# New chat

Conversation ID: 67ab7aab-7b74-8008-b05f-920d76e40e08

## Message 1

your goal is to add the correct pricing and description for google's models similar to the existing ones for other providers:





    # Providers

    PROVIDER_ANTHROPIC = "anthropic"

    PROVIDER_DEEPSEEK = "deepseek"

    PROVIDER_GOOGLE = "google"

    PROVIDER_OPENAI = "openai"



    # Available models per provider

    AVAILABLE_MODELS = {

        PROVIDER_ANTHROPIC: {

            "claude-3-haiku-20240307":     {"pricing": "0.80/4.00", "description": "Claude 3 Haiku"},

            "claude-3-sonnet-20240229":    {"pricing": "3.00/15.00", "description": "Claude 3 Sonnet"},

            "claude-2":                    {"pricing": "8.00/24.00", "description": "Base Claude 2 model"},

            "claude-2.0":                  {"pricing": "8.00/24.00", "description": "Enhanced <PERSON> 2.0"},

            "claude-2.1":                  {"pricing": "8.00/24.00", "description": "Latest Claude 2.1 release"},

            "claude-3-opus-20240229":      {"pricing": "15.00/75.00", "description": "Claude 3 Opus"},

        },

        PROVIDER_DEEPSEEK: {

            "deepseek-coder":              {"pricing": "0.10/0.20", "description": "Code-specialized model"},

            "deepseek-chat":               {"pricing": "0.14/0.28", "description": "DeepSeek Chat model"},

            "deepseek-reasoner":           {"pricing": "0.55/2.19", "description": "Specialized reasoning model"},

        },

        PROVIDER_GOOGLE: {

            'gemini-1.0-pro-latest':               {"pricing": "??/??", "description": "???"}

            'gemini-1.0-pro':                      {"pricing": "??/??", "description": "???"}

            'gemini-pro':                          {"pricing": "??/??", "description": "???"}

            'gemini-1.0-pro-001':                  {"pricing": "??/??", "description": "???"}

            'gemini-1.0-pro-vision-latest':        {"pricing": "??/??", "description": "???"}

            'gemini-pro-vision':                   {"pricing": "??/??", "description": "???"}

            'gemini-1.5-pro-latest':               {"pricing": "??/??", "description": "???"}

            'gemini-1.5-pro-001':                  {"pricing": "??/??", "description": "???"}

            'gemini-1.5-pro-002':                  {"pricing": "??/??", "description": "???"}

            'gemini-1.5-pro':                      {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash-latest':             {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash-001':                {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash-001-tuning':         {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash':                    {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash-002':                {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash-8b':                 {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash-8b-001':             {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash-8b-latest':          {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash-8b-exp-0827':        {"pricing": "??/??", "description": "???"}

            'gemini-1.5-flash-8b-exp-0924':        {"pricing": "??/??", "description": "???"}

            'gemini-2.0-flash-exp':                {"pricing": "??/??", "description": "???"}

            'gemini-2.0-flash':                    {"pricing": "??/??", "description": "???"}

            'gemini-2.0-flash-001':                {"pricing": "??/??", "description": "???"}

            'gemini-2.0-flash-lite-preview':       {"pricing": "??/??", "description": "???"}

            'gemini-2.0-flash-lite-preview-02-05': {"pricing": "??/??", "description": "???"}

            'gemini-2.0-pro-exp':                  {"pricing": "??/??", "description": "???"}

            'gemini-2.0-pro-exp-02-05':            {"pricing": "??/??", "description": "???"}

            'gemini-exp-1206':                     {"pricing": "??/??", "description": "???"}

            'gemini-2.0-flash-thinking-exp-01-21': {"pricing": "??/??", "description": "???"}

            'gemini-2.0-flash-thinking-exp':       {"pricing": "??/??", "description": "???"}

            'gemini-2.0-flash-thinking-exp-1219':  {"pricing": "??/??", "description": "???"}

            'learnlm-1.5-pro-experimental':        {"pricing": "??/??", "description": "???"}

        },

        PROVIDER_OPENAI: {

            "gpt-4o-mini":                 {"pricing": "0.15/0.60", "description": "Lightweight GPT-4o variant"},

            "gpt-4o-mini-audio-preview":   {"pricing": "0.15/0.60", "description": "GPT-4o Mini Audio (Text)"},

            "gpt-3.5-turbo":               {"pricing": "0.50/1.50", "description": "Base GPT-3.5 Turbo model"},

            "gpt-3.5-turbo-1106":          {"pricing": "0.50/1.50", "description": "Enhanced GPT-3.5 Turbo"},

            "gpt-4o-mini-realtime-preview":{"pricing": "0.60/2.40", "description": "GPT-4o Mini Realtime (Text)"},

            "o3-mini":                     {"pricing": "0.60/2.40", "description": "Lightweight reasoning model for STEM"},

            "gpt-4o":                      {"pricing": "2.50/10.00", "description": "Base GPT-4o model"},

            "gpt-4o-audio-preview":        {"pricing": "2.50/10.00", "description": "GPT-4o Audio (Text)"},

            "o1-mini":                     {"pricing": "3.00/12.00", "description": "Lightweight o1 variant"},

            "gpt-4o-realtime-preview":     {"pricing": "5.00/20.00", "description": "GPT-4o Realtime (Text)"},

            "gpt-4-0125-preview":          {"pricing": "10.00/30.00", "description": "Preview GPT-4 Turbo"},

            "gpt-4-1106-preview":          {"pricing": "10.00/30.00", "description": "Preview GPT-4 Turbo"},

            "gpt-4-turbo":                 {"pricing": "10.00/30.00", "description": "Latest GPT-4 Turbo release"},

            "gpt-4-turbo-2024-04-09":      {"pricing": "10.00/30.00", "description": "GPT-4 Turbo w/ vision"},

            "gpt-4-turbo-preview":         {"pricing": "10.00/30.00", "description": "Latest preview GPT-4 Turbo"},

            "o1":                          {"pricing": "15.00/60.00", "description": "Frontier reasoning model"},

            "o1-preview":                  {"pricing": "15.00/60.00", "description": "Preview of o1 model"},

            "gpt-4":                       {"pricing": "30.00/60.00", "description": "Latest GPT-4 stable release"},

            "gpt-4-0613":                  {"pricing": "30.00/60.00", "description": "June 2023 GPT-4 snapshot"},

        },

    }

