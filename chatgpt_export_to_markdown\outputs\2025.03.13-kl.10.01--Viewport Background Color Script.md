# Viewport Background Color Script

Conversation ID: 67d29ee4-96f8-8008-81b4-c0b519676f71

## Message 1

-- Convert 0-255 RGB to normalized 0.0-1.0 UnitRGB
function fnNormalizeRGB colorVal = (
    return (color (colorVal.r/255.0) (colorVal.g/255.0) (colorVal.b/255.0))
)

-- Set Viewport Background Color (active viewport only)
function fnSetActiveViewportColor setGradient:false setBrightness:0 = (
    solidColors = #(
        color 24 24 24,     -- Very Dark
        color 28 30 36,     -- Dark
        color 107 114 121,  -- Bright
        color 204 216 224   -- Very Bright
    )
    gradientColors = #(
        #(color 0 0 0, color 35 35 35),
        #(color 31 33 37, color 63 66 86),
        #(color 67 73 84, color 120 127 133),
        #(color 183 190 199, color 240 244 246)
    )

    -- Select color set based on brightness
    solidColorSelected = fnNormalizeRGB solidColors[setBrightness+1]
    gradientColorTop = fnNormalizeRGB gradientColors[setBrightness+1][1]
    gradientColorBtm = fnNormalizeRGB gradientColors[setBrightness+1][2]

    if setGradient then (
        viewport.EnableSolidBackgroundColorMode false
        colorMan.setColor #ViewportGradientBackgroundTop gradientColorTop
        colorMan.setColor #ViewportGradientBackgroundBottom gradientColorBtm
    ) else (
        viewport.EnableSolidBackgroundColorMode true
        colorMan.setColor #ViewportBackground solidColorSelected
    )

    redrawViews()
)

-- Example usage (solid bright color on active viewport only):
fnSetActiveViewportColor setGradient:false setBrightness:2

## Message 2

Your MaxScript functions are well-structured and efficient. However, I see a few possible improvements:

### 1. **Optimization of `fnNormalizeRGB`**
- Instead of manually normalizing each color component, use `colorVal / 255.0` directly since `colorVal` is already a color type.
- This removes the need to access `.r`, `.g`, and `.b` individually.

### 2. **Improved Safety for Array Indexing**
- `setBrightness+1` is used for array indexing. If `setBrightness` is out of range (e.g., negative or too high), this will throw an error. Add a safeguard with `clamp`.

### 3. **Refactoring `fnSetActiveViewportColor`**
- Introduce a check for `viewport` mode only if necessary (reducing redundant operations).
- Improve function readability by making explicit what each step does.

Here’s a refined

## Message 3


i've attached an example that you can use as reference, but your goal is to only extract the most fundamental parts and add it to the initially provided maxscript:
    
    ```maxscript
    -- Function: Set Viewport Background Color
    -- =================================================================================
    function fnSetViewportColor singleViewIndex:false setGradient:1 setBrightness:0 distinctiveViews:1 =
    (
        -- setGradient:0 (Solid Colors)
        solidColor = #(
            #(24, 24, 24),    -- Very Dark
            #(28, 30, 36),    -- Dark
            #(107, 114, 121), -- Bright
            #(204, 216, 224)  -- Very Bright
        )
        solidColorSelected = solidColor[setBrightness+1]
    
        -- setGradient:1 (Gradient Colors)
        gradientColor = #(
            #(#(0, 0, 0), #(35, 35, 35)),         -- Very Dark
            #(#(31, 33, 37), #(63, 66, 86)),      -- Dark
            #(#(67, 73, 84), #(120, 127, 133)),   -- Bright
            #(#(183, 190, 199), #(240, 244, 246)) -- Very Bright
        )
        gradientColorSelected = gradientColor[setBrightness+1]
        -- For each available view
        initialActiveView = viewport.activeViewport
        for currentView=1 to viewport.numViewEx() do
        (
            -- Activate the current view
            viewport.activeViewportEx(currentView)
    
            -- Check if single view mode is on (and skip if it doesn't match)
            if (viewport.activeViewport!=initialActiveView and singleViewIndex!=0) do continue
    
            -- Get the current visual style for the current view
            currViewSetting = NitrousGraphicsManager.GetActiveViewportSetting()
            currVisualStyle = currViewSetting.VisualStyleMode
            -- For the following types of visual styles: [#Shaded | #Wireframe | #Realistic]
            for currStyle in #(#Shaded, #Wireframe, #Realistic) do
            (
                currViewSetting.VisualStyleMode = currStyle
                -- Normalize the colors
                currentSolidColor = for colorVal in solidColorSelected collect (colorVal / 255.0)
                currentGradiensTop = for colorVal in gradientColorSelected[1] collect (colorVal / 255.0)
                currentGradiensBtm = for colorVal in gradientColorSelected[2] collect (colorVal / 255.0)
    
                -- If distinct views are set to true, make ortographic views solid
                -- and the perspective gradient
                if (distinctiveViews == 1) or (distinctiveViews == true) then
                (
                    if (viewport.getType() == #view_persp_user) then
                    (
                        viewport.EnableSolidBackgroundColorMode false
                        colorMan.setColor #ViewportGradientBackgroundTop    [currentGradiensTop[1], currentGradiensTop[2], currentGradiensTop[3]]
                        colorMan.setColor #ViewportGradientBackgroundBottom [currentGradiensBtm[1], currentGradiensBtm[2], currentGradiensBtm[3]]
                        redrawViews()
                    )
                    else
                    (
                        -- solidColorValue = [(solidColorSelected[1] / 255.0), (solidColorSelected[2] / 255.0), (solidColorSelected[3] / 255.0)]
                        -- print("solidColorValue: " + solidColorValue as string)
                        viewport.EnableSolidBackgroundColorMode true
                        colorMan.setColor #ViewportBackground [currentSolidColor[1], currentSolidColor[2], currentSolidColor[3]]
                        redrawViews()
                    )
                )
    
                else
                (
                    if (setGradient == 0) or (setGradient == false) then
                    (
                        viewport.EnableSolidBackgroundColorMode true
                        colorMan.setColor #ViewportBackground [currentSolidColor[1], currentSolidColor[2], currentSolidColor[3]]
                        redrawViews()
                    )
                    -- Else if 'gradient' was passed in as true
                    else if (setGradient == 1) or (setGradient == true) do
                    (
                        viewport.EnableSolidBackgroundColorMode false
                        colorMan.setColor #ViewportGradientBackgroundTop    [currentGradiensTop[1], currentGradiensTop[2], currentGradiensTop[3]]
                        colorMan.setColor #ViewportGradientBackgroundBottom [currentGradiensBtm[1], currentGradiensBtm[2], currentGradiensBtm[3]]
                        redrawViews()
                    )
                )
    
                -- Set the current view style to it's original style
                currViewSetting.VisualStyleMode = currVisualStyle
    
                -- Summarize
                outputStrings1 = ("singleViewIndex: " + singleViewIndex as string + " | ")
                outputStrings2 = ("setGradient: " + setGradient as string + " | ")
                outputStrings3 = ("setBrightness: " + setBrightness as string + " | ")
                outputStrings4 = ("distinctiveViews: " + distinctiveViews as string + " | ")
                outputStringsLn = (outputStrings1 + outputStrings2 + outputStrings3 + outputStrings4)
                ReplacePrompt outputStringsLn
                -- Debug
                -- print(outputStringsLn)
    
                -- -- Personal preference
                -- if (sysInfo.username == "PaulsenJ") do ( setClipBoardText outputStringsLn )
            )
        )
        -- Re-activate original view
        viewport.activeViewportEx(initialActiveView)
    )
    ```


please add the neccessary lines to this maxscript to enable affecting *active view only*:

    ```maxscript
    -- Convert 0-255 RGB to normalized 0.0-1.0 UnitRGB
    function fnNormalizeRGB colorVal = (
        return (color (colorVal.r/255.0) (colorVal.g/255.0) (colorVal.b/255.0))
    )
    
    solidColorBlack = fnNormalizeRGB (color 0 0 0)
    solidColorWhite = fnNormalizeRGB (color 255 255 255)
    
    solidColorSelected = solidColorWhite
    
    viewport.EnableSolidBackgroundColorMode true
    colorMan.setColor #ViewportBackground solidColorSelected
    
    ```
    

## Message 4

Here's your updated MaxScript with the necessary modifications to allow affecting only the active view when specified:

## Message 5

{"name": "maxscript_viewport_color", "type": "code/maxscript", "content": "-- Function: Set Viewport Background Color (Active View Only Option Added)\n-- =================================================================================\nfunction fnSetViewportColor singleViewIndex:false setGradient:1 setBrightness:0 distinctiveViews:1 activeViewOnly:false =\n(\n    -- Convert 0-255 RGB to normalized 0.0-1.0 UnitRGB\n    function fnNormalizeRGB colorVal = (\n        return (color (colorVal.r/255.0) (colorVal.g/255.0) (colorVal.b/255.0))\n    )\n    \n    -- Solid Colors\n    solidColor = #(\n        #(24, 24, 24),    -- Very Dark\n        #(28, 30, 36),    -- Dark\n        #(107, 114, 121), -- Bright\n        #(204, 216, 224)  -- Very Bright\n    )\n    solidColorSelected = solidColor[setBrightness+1]\n    \n    -- Gradient Colors\n    gradientColor = #(\n        #(#(0, 0, 0), #(35, 35, 35

## Message 6

system_instruction: act as a maxscript developer, deeply familiar with the `maxscript` language used in `Autodesk 3ds Max 2025`. your goal is to provide the most fundamental representation of the code in order to achieve the goal inherent in the input.

here's a working maxscript to perform viewport operations

    ```maxscript
    -- Function: Set Viewport Background Color
    -- =================================================================================
    function fnSetViewportColor singleViewIndex:false setGradient:1 setBrightness:0 distinctiveViews:1 =
    (
        -- setGradient:0 (Solid Colors)
        solidColor = #(
            #(24, 24, 24),    -- Very Dark
            #(28, 30, 36),    -- Dark
            #(107, 114, 121), -- Bright
            #(204, 216, 224)  -- Very Bright
        )
        solidColorSelected = solidColor[setBrightness+1]

        -- setGradient:1 (Gradient Colors)
        gradientColor = #(
            #(#(0, 0, 0), #(35, 35, 35)),         -- Very Dark
            #(#(31, 33, 37), #(63, 66, 86)),      -- Dark
            #(#(67, 73, 84), #(120, 127, 133)),   -- Bright
            #(#(183, 190, 199), #(240, 244, 246)) -- Very Bright
        )
        gradientColorSelected = gradientColor[setBrightness+1]
        -- For each available view
        initialActiveView = viewport.activeViewport
        for currentView=1 to viewport.numViewEx() do
        (
            -- Activate the current view
            viewport.activeViewportEx(currentView)

            -- Check if single view mode is on (and skip if it doesn't match)
            if (viewport.activeViewport!=initialActiveView and singleViewIndex!=0) do continue

            -- Get the current visual style for the current view
            currViewSetting = NitrousGraphicsManager.GetActiveViewportSetting()
            currVisualStyle = currViewSetting.VisualStyleMode
            -- For the following types of visual styles: [#Shaded | #Wireframe | #Realistic]
            for currStyle in #(#Shaded, #Wireframe, #Realistic) do
            (
                currViewSetting.VisualStyleMode = currStyle
                -- Normalize the colors
                currentSolidColor = for colorVal in solidColorSelected collect (colorVal / 255.0)
                currentGradiensTop = for colorVal in gradientColorSelected[1] collect (colorVal / 255.0)
                currentGradiensBtm = for colorVal in gradientColorSelected[2] collect (colorVal / 255.0)

                -- If distinct views are set to true, make ortographic views solid
                -- and the perspective gradient
                if (distinctiveViews == 1) or (distinctiveViews == true) then
                (
                    if (viewport.getType() == #view_persp_user) then
                    (
                        viewport.EnableSolidBackgroundColorMode false
                        colorMan.setColor #ViewportGradientBackgroundTop    [currentGradiensTop[1], currentGradiensTop[2], currentGradiensTop[3]]
                        colorMan.setColor #ViewportGradientBackgroundBottom [currentGradiensBtm[1], currentGradiensBtm[2], currentGradiensBtm[3]]
                        redrawViews()
                    )
                    else
                    (
                        -- solidColorValue = [(solidColorSelected[1] / 255.0), (solidColorSelected[2] / 255.0), (solidColorSelected[3] / 255.0)]
                        -- print("solidColorValue: " + solidColorValue as string)
                        viewport.EnableSolidBackgroundColorMode true
                        colorMan.setColor #ViewportBackground [currentSolidColor[1], currentSolidColor[2], currentSolidColor[3]]
                        redrawViews()
                    )
                )

                else
                (
                    if (setGradient == 0) or (setGradient == false) then
                    (
                        viewport.EnableSolidBackgroundColorMode true
                        colorMan.setColor #ViewportBackground [currentSolidColor[1], currentSolidColor[2], currentSolidColor[3]]
                        redrawViews()
                    )
                    -- Else if 'gradient' was passed in as true
                    else if (setGradient == 1) or (setGradient == true) do
                    (
                        viewport.EnableSolidBackgroundColorMode false
                        colorMan.setColor #ViewportGradientBackgroundTop    [currentGradiensTop[1], currentGradiensTop[2], currentGradiensTop[3]]
                        colorMan.setColor #ViewportGradientBackgroundBottom [currentGradiensBtm[1], currentGradiensBtm[2], currentGradiensBtm[3]]
                        redrawViews()
                    )
                )

                -- Set the current view style to it's original style
                currViewSetting.VisualStyleMode = currVisualStyle

                -- Summarize
                outputStrings1 = ("singleViewIndex: " + singleViewIndex as string + " | ")
                outputStrings2 = ("setGradient: " + setGradient as string + " | ")
                outputStrings3 = ("setBrightness: " + setBrightness as string + " | ")
                outputStrings4 = ("distinctiveViews: " + distinctiveViews as string + " | ")
                outputStringsLn = (outputStrings1 + outputStrings2 + outputStrings3 + outputStrings4)
                ReplacePrompt outputStringsLn
                -- Debug
                -- print(outputStringsLn)

                -- -- Personal preference
                -- if (sysInfo.username == "PaulsenJ") do ( setClipBoardText outputStringsLn )
            )
        )
        -- Re-activate original view
        viewport.activeViewportEx(initialActiveView)
    )
    ```

here's a maxscript that elegantly shows how cleanly you can interact with the viewport:

    ```maxscript
    -- Convert 0-255 RGB to normalized 0.0-1.0 UnitRGB
    function fnNormalizeRGB colorVal = (
        return (color (colorVal.r/255.0) (colorVal.g/255.0) (colorVal.b/255.0))
    )

    solidColorBlack = fnNormalizeRGB (color 0 0 0)
    solidColorWhite = fnNormalizeRGB (color 255 255 255)

    solidColorSelected = solidColorWhite

    viewport.EnableSolidBackgroundColorMode true
    colorMan.setColor #ViewportBackground solidColorSelected

    ```

objective: please add the neccessary lines to the provided maxscript codeblock to enable affecting *active view only*:

    ```maxscript
    -- Convert 0-255 RGB to normalized 0.0-1.0 UnitRGB
    function fnNormalizeRGB colorVal = (
        return (color (colorVal.r/255.0) (colorVal.g/255.0) (colorVal.b/255.0))
    )

    solidColorBlack = fnNormalizeRGB (color 0 0 0)
    solidColorWhite = fnNormalizeRGB (color 255 255 255)

    solidColorSelected = solidColorWhite

    viewport.EnableSolidBackgroundColorMode true
    colorMan.setColor #ViewportBackground solidColorSelected

    ```

## Message 7

Here’s the updated MaxScript code that ensures the viewport background color change affects only the *active view*:

```maxscript
-- Convert 0-255 RGB to normalized 0.0-1.0 UnitRGB
function fnNormalizeRGB colorVal = (
    return (color (colorVal.r/255.0) (colorVal.g/255.0) (colorVal.b/255.0))
)

solidColorBlack = fnNormalizeRGB (color 0 0 0)
solidColorWhite = fnNormalizeRGB (color 255 255 255)

solidColorSelected = solidColorWhite

-- Store the currently active viewport
initialActiveView = viewport.activeViewport

-- Apply the change only to the active viewport
viewport.activeViewportEx initialActiveView
viewport.EnableSolidBackgroundColorMode true
colorMan.setColor #ViewportBackground solidColorSelected
redrawViews()
```

### **Changes & Additions:**
1. **Stored the active viewport** → `initialActiveView = viewport.activeViewport`
2. **Ensured changes only affect the active viewport** → `viewport.activeViewportEx initialActiveView`
3. **Added `redrawViews()`** → Ensures the change is reflected immediately.

This keeps the script minimal while ensuring only the active viewport is modified. 🚀

## Message 8

Here's the updated MaxScript to ensure it only affects the active view:  

```maxscript
-- Convert 0-255 RGB to normalized 0.0-1.0 UnitRGB
function fnNormalizeRGB colorVal = (
    return (color (colorVal.r/255.0) (colorVal.g/255.0) (colorVal.b/255.0))
)

solidColorBlack = fnNormalizeRGB (color 0 0 0)
solidColorWhite = fnNormalizeRGB (color 255 255 255)

solidColorSelected = solidColorWhite

-- Store the currently active viewport
initialActiveView = viewport.activeViewport

-- Set background color only for the active viewport
viewport.activeViewportEx initialActiveView
viewport.EnableSolidBackgroundColorMode true
colorMan.setColor #ViewportBackground solidColorSelected

-- Re-activate original viewport (ensuring no unintended changes)
viewport.activeViewportEx initialActiveView
```

### What’s changed:
- **Captured `initialActiveView`**: Ensures changes apply only to the active viewport.
- **Set active viewport explicitly**: Uses `viewport.activeViewportEx initialActiveView` before making changes.


## Message 9

To modify the provided MaxScript to affect only the active viewport, you can utilize the `viewport.activeViewport` property. Here's the adjusted script:

```maxscript
-- Convert 0-255 RGB to normalized 0.0-1.0 UnitRGB
function fnNormalizeRGB colorVal = (
    return (color (colorVal.r/255.0) (colorVal.g/255.0) (colorVal.b/255.0))
)

solidColorBlack = fnNormalizeRGB (color 0 0 0)
solidColorWhite = fnNormalizeRGB (color 255 255 255)

solidColorSelected = solidColorWhite

-- Store the current active viewport
activeViewport = viewport.activeViewport

-- Enable solid background color mode for the active viewport
viewport.SetBackgroundTexture activeViewport false
viewport.SetBackgroundColor activeViewport solidColorSelected
viewport.RedrawViews()
```

**Explanation:**

1. **Normalization Function:** The `fnNormalizeRGB` function converts RGB values from the 0-255 range to the 0.0-1.0 range, which is suitable for viewport color settings.

2. **Color Definitions:** `solidColorBlack` and `solidColorWhite` are defined using the normalization function.

3. **Color Selection:** `solidColorSelected` is set to `solidColorWhite`.

4. **Active Viewport:** The current active viewport is stored in the `activeViewport` variable.

5. **Set Background Color:**
   - `viewport.SetBackgroundTexture activeViewport false` disables any background texture for the active viewport.
   - `viewport.SetBackgroundColor activeViewport solidColorSelected` sets the background color of the active viewport to the selected solid color.
   - `viewport.RedrawViews()` refreshes the viewport to apply the changes.

This script ensures that only the active viewport's background color is modified, leaving other viewports unaffected. 
