# Add Google LLM Support

Conversation ID: 67ab7821-ada8-8008-b4f3-77bb7cec9376

## Message 1

please add google's llm models to this utility:

#!/usr/bin/env python3



"""

template_runner.py

Manages templates, LLM interactions, and orchestrates prompt refinements.

Passes each template's 'enhanced_prompt' as input to the next in the chain.

"""



import os

import sys

import json

import glob

import re

from pathlib import Path

from typing import List, Dict, Union, Optional

from dotenv import load_dotenv

from loguru import logger



# Provider SDKs

from openai import OpenAI

from anthropic import Anthropic



# -------------------------------------------------------

# 1. Global Configuration

# -------------------------------------------------------



class Config:

    """Global settings for API providers and logging configuration."""



    PROVIDERS = {

        "anthropic": "ANTHROPIC_API_KEY",

        "deepseek": "DEEPSEEK_API_KEY",

        "openai": "OPENAI_API_KEY",

    }



    DEFAULT_PROVIDER = "openai"



    def __init__(self):

        load_dotenv()

        self.provider = os.getenv("LLM_PROVIDER", self.DEFAULT_PROVIDER).lower()

        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))

        self.setup_logger()



    def setup_logger(self):

        """Configures logging with YAML-style output."""

        log_filepath = os.path.join(self.log_dir, "template_runner.log.yml")

        open(log_filepath, "w").close()



        logger.remove()

        logger.add(log_filepath, level="DEBUG", format="{message}")



# -------------------------------------------------------

# 2. LLM API Interface

# -------------------------------------------------------



class LLMInteractions:

    """Handles LLM API interactions across multiple providers."""



    CLIENT_FACTORIES = {

        "openai": lambda key: OpenAI(api_key=key),

        "deepseek": lambda key: OpenAI(api_key=key),

        "anthropic": lambda key: Anthropic(api_key=key),

    }



    def __init__(self, provider=None):

        self.config = Config()

        self.provider = provider or self.config.provider

        self.client = self._initialize_client()



    def _initialize_client(self):

        """Creates an API client based on the configured provider."""

        api_key = os.getenv(Config.PROVIDERS.get(self.provider))

        if not api_key:

            raise ValueError(f"Missing API key for provider: {self.provider}")

        return self.CLIENT_FACTORIES[self.provider](api_key)



    def generate_response(self, messages, model_name):

        """Executes an API call and returns the response."""

        try:

            response = self.client.chat.completions.create(model=model_name, messages=messages)

            return response.choices[0].message.content if response.choices else None

        except Exception as e:

            logger.error(f"API error ({self.provider}): {e}")

            return None



# -------------------------------------------------------

# 3. Template Management

# -------------------------------------------------------



class TemplateFileManager:

    """Handles loading and parsing template files."""



    ALLOWED_EXTS = {".xml", ".json", ".md", ".txt"}



    def __init__(self):

        self.template_dir = os.getcwd()

        self.cache = {}



    def get_template_path(self, template_name):

        """Finds a template file based on its name."""

        if template_name in self.cache:

            return self.cache[template_name]



        for ext in self.ALLOWED_EXTS:

            files = glob.glob(f"{self.template_dir}/**/{template_name}{ext}", recursive=True)

            if files:

                self.cache[template_name] = files[0]

                return files[0]



        return None



    def load_template(self, template_name, input_prompt):

        """Loads a template file and replaces placeholders."""

        path = self.get_template_path(template_name)

        if not path:

            return None



        with open(path, "r", encoding="utf-8") as file:

            content = file.read()



        placeholders = {

            "[INPUT_PROMPT]": input_prompt,

        }



        for key, value in placeholders.items():

            content = content.replace(key, value)



        return content



# -------------------------------------------------------

# 4. Prompt Refinement Orchestration

# -------------------------------------------------------



class PromptRefinementOrchestrator:

    """Executes prompt refinements using a chain of templates."""



    def __init__(self, template_manager, llm_agent):

        self.template_manager = template_manager

        self.llm_agent = llm_agent



    def _build_messages(self, system_prompt, user_prompt):

        """Formats messages for the API call."""

        return [{"role": "system", "content": system_prompt.strip()},

                {"role": "user", "content": user_prompt.strip()}]



    def execute_single_template(self, template_name, input_prompt, model_name):

        """Processes a single template refinement."""

        template_content = self.template_manager.load_template(template_name, input_prompt)

        if not template_content:

            return None



        messages = self._build_messages("Refine this input:", template_content)

        return self.llm_agent.generate_response(messages, model_name)



    def execute_template_chain(self, template_names, input_prompt, model_name):

        """Processes multiple templates in sequence."""

        refined_prompt = input_prompt

        results = []



        for template in template_names:

            refined_text = self.execute_single_template(template, refined_prompt, model_name)

            if refined_text:

                results.append(refined_text)

                refined_prompt = refined_text



        return results



# -------------------------------------------------------

# 5. Main Execution

# -------------------------------------------------------



class Execution:

    """Main script execution for template-based refinements."""



    def __init__(self, provider=None):

        self.config = Config()

        self.agent = LLMInteractions(provider)

        self.template_manager = TemplateFileManager()

        self.orchestrator = PromptRefinementOrchestrator(self.template_manager, self.agent)



    def run(self):

        """Executes a sample refinement chain."""

        templates = ["PromptOptimizerExpert", "IntensityEnhancer"]

        input_prompt = "Improve this sentence for clarity and impact."



        results = self.orchestrator.execute_template_chain(templates, input_prompt, "gpt-4o")

        print("\nFinal Output:")

        print(results[-1] if results else "No output generated.")



if __name__ == "__main__":

    Execution().run()


## Message 2

**Integrating Google's models**

I'm thinking through the integration of Google's LLM models like PaLM and Bard into the script, considering API calls and Python libraries for seamless integration.

**Crafting a new client**

I’m piecing together a Python client factory for Google LLM models, ensuring the integration is smooth with existing libraries. This involves checking for an official API and potential Python libraries.

**Integrating Google's client**

I'm working on integrating Google's `google.generativeai` client into the script, adjusting dictionaries, and creating a `generate_response` function for consistent client interactions.

**Integrating Google AI**

I'm piecing together the integration of Google's generative AI library, ensuring the code handles message structures or parsing uniformly, and considering a routing method to unify interactions.

**Integrating the user**

I’m drawing up the integration of Google's LLM models, focusing on initializing the client through `CLIENT_FACTORIES` and handling chat responses with minimal, streamlined code.

**Integrating environment configurations**

I’m enhancing the script to include Google's API key handling in environment configurations, adding `"google": "GOOGLE_API_KEY"` to `Config.PROVIDERS` and `CLIENT_FACTORIES`, and defining `GooglePalmClient`.

**Analyzing message handling**

I’m digging into how the palm library manages messages, considering both a conversation string and a list of dictionaries with role and content. This exploration helps refine integrations.

**Evaluating return options**

I’m assessing `palm.chat()` for role preservation and comparing it to the simpler `palm.generate_text()`. This explores the best method for maintaining message structure and content.

**Mapping the integration**

I’m thinking through the integration of Google’s `palm.chat()` call, considering format transformations and ensuring it aligns with the minimal and efficient approach for the user's needs.

**Enhancing the script**

I’m adding Google to the PROVIDERS dictionary, enhancing CLIENT_FACTORIES, and defining GooglePalm as a unified class. This streamlined approach ensures a seamless integration process.

**Integrating Google Palm**

I’m integrating Google Palm into the script by updating the PROVIDERS dictionary, enhancing CLIENT_FACTORIES, and defining the `GooglePalm` class. This simplifies the `generate_response` method for Google.

**Integrating Google Palm**

I’m suggesting adding Google to the PROVIDERS dictionary and CLIENT_FACTORIES, defining a Google import, and establishing a `GooglePalm` class. This approach keeps the `generate_response` method aligned with the existing `client` usage.

**Introducing Google PalmClient**

I’m creating the `GooglePalmClient` class to streamline interactions within `generate_response`. This involves aligning methods and integrating seamlessly with existing AI clients.

**Integrating the solution**

I’m noting the benefits of aligning Google with existing methods in `generate_response`, though this requires adapting OpenAI and Anthropic code to maintain consistency.

**Navigating integration**

I'm weighing the introduction of `GooglePalmClient` for Google integration, noting that this requires tweaking OpenAI and Anthropic code to ensure consistent client usage across the script.

**Designing GooglePalmClient**

I’m outlining the `GooglePalmClient` class to unify Google client usage within `generate_response`, ensuring consistent interaction methods across OpenAI and Anthropic by aligning with the existing generic client approach.

**Defining GoogleAliasClient**

I’m detailing `GoogleAliasClient` for Google AI client usage, ensuring it aligns with OpenAI and Anthropic clients by maintaining a similar interface and consistent method formatting.

**Carving out GoogleAliasClient**

I’m defining `GoogleAliasClient` to ensure it aligns with OpenAI and Anthropic by maintaining a similar interface and consistent response formatting. This unification simplifies integration.

## Message 3

Below is an example of how you might extend your existing utility to include support for Google’s PaLM/Vertex LLM. The key steps are:

1. **Add a new provider** to the `Config.PROVIDERS` dictionary.  
2. **Add a new client factory** in `LLMInteractions.CLIENT_FACTORIES`.  
3. **Implement** the Google LLM client logic to mirror the behavior of other providers.

Below is a modified version of your script with inline comments showing what changed for the Google LLM support. This example uses the [`google-generativeai`](https://pypi.org/project/google-generativeai/) library (often used for PaLM models, such as `models/chat-bison-001` or future Vertex-based LLMs). If you need a different Google library, adapt accordingly.

```python
#!/usr/bin/env python3

"""
template_runner.py
Manages templates, LLM interactions, and orchestrates prompt refinements.
Passes each template's 'enhanced_prompt' as input to the next in the chain.
"""

import os
import sys
import json
import glob
import re
from pathlib import Path
from typing import List, Dict, Union, Optional
from dotenv import load_dotenv
from loguru import logger

# Provider SDKs
from openai import OpenAI
from anthropic import Anthropic

# ----------------------------------------
#  Google generative AI (PaLM) library
# ----------------------------------------
try:
    import google.generativeai as palm
    GOOGLE_GENERATIVEAI_AVAILABLE = True
except ImportError:
    # If not installed, user can `pip install google-generativeai`
    palm = None
    GOOGLE_GENERATIVEAI_AVAILABLE = False

# -------------------------------------------------------
# 1. Global Configuration
# -------------------------------------------------------

class Config:
    """Global settings for API providers and logging configuration."""

    PROVIDERS = {
        "anthropic": "ANTHROPIC_API_KEY",
        "deepseek": "DEEPSEEK_API_KEY",
        "openai": "OPENAI_API_KEY",
        # -------------------------------------------
        # ADD a Google provider environment variable
        # -------------------------------------------
        "google": "GOOGLE_API_KEY",
    }

    DEFAULT_PROVIDER = "openai"

    def __init__(self):
        load_dotenv()
        self.provider = os.getenv("LLM_PROVIDER", self.DEFAULT_PROVIDER).lower()
        self.log_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        self.setup_logger()

    def setup_logger(self):
        """Configures logging with YAML-style output."""
        log_filepath = os.path.join(self.log_dir, "template_runner.log.yml")
        open(log_filepath, "w").close()

        logger.remove()
        logger.add(log_filepath, level="DEBUG", format="{message}")

# -------------------------------------------------------
# 2. LLM API Interface
# -------------------------------------------------------

class LLMInteractions:
    """Handles LLM API interactions across multiple providers."""

    # We'll define small wrapper classes below for Google vs. the existing providers.

    class GooglePalmClient:
        """
        Minimal wrapper around google.generativeai to mirror
        `client.chat.completions.create(model=model, messages=...)`
        """
        def __init__(self, api_key):
            if not GOOGLE_GENERATIVEAI_AVAILABLE:
                raise ImportError("google-generativeai is not installed or could not be imported.")
            palm.configure(api_key=api_key)

        def create_chat_completion(self, model, messages):
            """
            Create a completion using the google.generativeai `palm.chat` method.
            :param model: e.g. 'models/chat-bison-001'
            :param messages: a list of {role: <system|user|assistant>, content: <string>}
            :return: The last response text from the model
            """
            # google.generativeai's palm.chat expects:
            #     palm.chat(model="...", messages=[{"author": "user","content": "..."}])
            # We'll map roles to 'user' or 'system' or 'assistant'
            # (In practice, you might want to handle "system" separately.)
            converted_msgs = []
            for m in messages:
                # Convert the standard role naming to palm-compatible "user" or "system"/"assistant"
                role = m.get("role")
                if role not in ("system", "assistant", "user"):
                    role = "user"  # fallback
                # The library expects 'author' instead of 'role'
                converted_msgs.append({"author": role, "content": m.get("content", "")})

            # Send the chat request
            conversation = palm.chat(
                model=model,
                messages=converted_msgs
            )

            # conversation.last is the final response from the model
            if conversation and getattr(conversation, "last", None):
                return conversation.last
            return None

    CLIENT_FACTORIES = {
        "openai": lambda key: OpenAI(api_key=key),
        "deepseek": lambda key: OpenAI(api_key=key),
        "anthropic": lambda key: Anthropic(api_key=key),
        # --------------------------------
        # NEW: add Google provider factory
        # --------------------------------
        "google": lambda key: LLMInteractions.GooglePalmClient(api_key=key),
    }

    def __init__(self, provider=None):
        self.config = Config()
        self.provider = provider or self.config.provider
        self.client = self._initialize_client()

    def _initialize_client(self):
        """Creates an API client based on the configured provider."""
        api_key = os.getenv(Config.PROVIDERS.get(self.provider))
        if not api_key:
            raise ValueError(f"Missing API key for provider: {self.provider}")
        return self.CLIENT_FACTORIES[self.provider](api_key)

    def generate_response(self, messages, model_name):
        """Executes an API call and returns the response."""
        try:
            # For Google PaLM, we must call our `create_chat_completion` method manually
            if self.provider == "google":
                response_text = self.client.create_chat_completion(model_name, messages)
                return response_text

            # For openai / anthropic / deepseek, the usage remains the same as before:
            response = self.client.chat.completions.create(model=model_name, messages=messages)
            return response.choices[0].message.content if response.choices else None

        except Exception as e:
            logger.error(f"API error ({self.provider}): {e}")
            return None

# -------------------------------------------------------
# 3. Template Management
# -------------------------------------------------------

class TemplateFileManager:
    """Handles loading and parsing template files."""

    ALLOWED_EXTS = {".xml", ".json", ".md", ".txt"}

    def __init__(self):
        self.template_dir = os.getcwd()
        self.cache = {}

    def get_template_path(self, template_name):
        """Finds a template file based on its name."""
        if template_name in self.cache:
            return self.cache[template_name]

        for ext in self.ALLOWED_EXTS:
            files = glob.glob(f"{self.template_dir}/**/{template_name}{ext}", recursive=True)
            if files:
                self.cache[template_name] = files[0]
                return files[0]

        return None

    def load_template(self, template_name, input_prompt):
        """Loads a template file and replaces placeholders."""
        path = self.get_template_path(template_name)
        if not path:
            return None

        with open(path, "r", encoding="utf-8") as file:
            content = file.read()

        placeholders = {
            "[INPUT_PROMPT]": input_prompt,
        }

        for key, value in placeholders.items():
            content = content.replace(key, value)

        return content

# -------------------------------------------------------
# 4. Prompt Refinement Orchestration
# -------------------------------------------------------

class PromptRefinementOrchestrator:
    """Executes prompt refinements using a chain of templates."""

    def __init__(self, template_manager, llm_agent):
        self.template_manager = template_manager
        self.llm_agent = llm_agent

    def _build_messages(self, system_prompt, user_prompt):
        """Formats messages for the API call."""
        return [
            {"role": "system", "content": system_prompt.strip()},
            {"role": "user", "content": user_prompt.strip()},
        ]

    def execute_single_template(self, template_name, input_prompt, model_name):
        """Processes a single template refinement."""
        template_content = self.template_manager.load_template(template_name, input_prompt)
        if not template_content:
            return None

        messages = self._build_messages("Refine this input:", template_content)
        return self.llm_agent.generate_response(messages, model_name)

    def execute_template_chain(self, template_names, input_prompt, model_name):
        """Processes multiple templates in sequence."""
        refined_prompt = input_prompt
        results = []

        for template in template_names:
            refined_text = self.execute_single_template(template, refined_prompt, model_name)
            if refined_text:
                results.append(refined_text)
                refined_prompt = refined_text

        return results

# -------------------------------------------------------
# 5. Main Execution
# -------------------------------------------------------

class Execution:
    """Main script execution for template-based refinements."""

    def __init__(self, provider=None):
        self.config = Config()
        self.agent = LLMInteractions(provider)
        self.template_manager = TemplateFileManager()
        self.orchestrator = PromptRefinementOrchestrator(self.template_manager, self.agent)

    def run(self):
        """Executes a sample refinement chain."""
        templates = ["PromptOptimizerExpert", "IntensityEnhancer"]
        input_prompt = "Improve this sentence for clarity and impact."

        # Example model name:
        #  - For OpenAI: "gpt-4"
        #  - For Anthropic: "claude-v1"
        #  - For Google: "models/chat-bison-001" (PaLM 2)
        #
        # "gpt-4o" was used in your snippet, but adapt if using Google:
        model_name = "models/chat-bison-001" if self.agent.provider == "google" else "gpt-4"

        results = self.orchestrator.execute_template_chain(templates, input_prompt, model_name)
        print("\nFinal Output:")
        print(results[-1] if results else "No output generated.")

if __name__ == "__main__":
    Execution().run()
```

### How to Use the Google Provider

1. **Install** the Google Generative AI Python library (if you haven’t already):
   ```bash
   pip install google-generativeai
   ```

2. **Set the environment variable** (e.g., in a `.env` file or your shell):
   ```bash
   export GOOGLE_API_KEY="YOUR_GOOGLE_PALM_KEY"
   ```
   Or put it in your `.env`:
   ```
   GOOGLE_API_KEY=YOUR_GOOGLE_PALM_KEY
   ```

3. **Specify `google`** as the LLM provider:
   ```bash
   export LLM_PROVIDER="google"
   ```

4. **Run** your script:
   ```bash
   python template_runner.py
   ```

If everything is set up correctly, it will initialize the Google PaLM client and run the same template-chain logic against a Google LLM (e.g., `models/chat-bison-001`).

---

**Important Notes**  
- The example above is streamlined to mimic the usage patterns of other providers. For more advanced usage (e.g., adding context, examples, or temperature), see the [Google Generative AI Python docs](https://github.com/google/generative-ai-python).  
- The `messages` parameter is adapted to `palm.chat` by converting roles to “user,” “assistant,” or “system.” Feel free to refine that logic if you need to handle system messages differently.  
- If you are working on Vertex AI endpoints or different Google LLM endpoints, you might need to adjust the code for calling those services—but the structure (adding a provider constant, a factory method, etc.) will be very similar.
