# Python YouTube Search Library

Conversation ID: 68246c58-3658-8008-b89c-8ef2e1806971

## Message 1

what's the most popular and actively maintained Python library for interactive YouTube video searching (e.g. searching videos within a timeframe with a viewcount above 1000)?

## Message 2

To help find the best Python library for interactive YouTube video searching, could you clarify a few things?

1. Do you want the library to use the official YouTube Data API, or are you open to third-party scraping-based libraries as well?
2. What kind of interactivity are you looking for? (e.g., a CLI tool, GUI interface, Jupyter integration, etc.)
3. Should the library support filtering by upload date, duration, and other metadata in addition to view count?
4. Are you also looking for features like downloading videos, or is it purely for search and filter?

Once I have these details, I can narrow down the most popular and actively maintained options.

## Message 3

i'm open to using the official api, i don't want to scrape - just want the ability to have more control when searching for specific types of videos

## Message 4

{
  "task_violates_safety_guidelines": false,
  "user_def_doesnt_want_research": false,
  "response": "Great, I’ll look into the most popular and actively maintained Python libraries that use the official YouTube Data API and support controlled video searching—such as filtering by timeframe, view count, and other metadata.\nI'll get back to you shortly with a summary of the best options and their capabilities.",
  "title": "Best Python Libraries for YouTube API Video Searching",
  "prompt": "Identify the most popular and actively maintained Python libraries that use the official YouTube Data API for searching YouTube videos with fine-grained control. The user is specifically looking to search for videos using filters such as upload timeframe and minimum view count (e.g., videos uploaded within a certain period and with views above 1000).\n\nThe output should:\n- List and compare the top Python libraries that interface with the official YouTube Data API.\n- Highlight which libraries support advanced video searching features such as date filters, view count filters, keyword-based searching, and other metadata.\n- Mention the level of documentation, community activity, and maintenance status (e.g., last update date, GitHub stars/issues).\n- Prioritize libraries that support programmatic search with flexibility, rather than scraping or GUI-driven tools.\n- Include example usage snippets where appropriate.\n\nOnly include libraries that respect YouTube’s terms of service and use the official API. Exclude scraping-based or unofficial tools.\n\nPresent findings in English, formatted as a comparison table followed by a detailed discussion."
}

## Message 5

Great, I’ll look into the most popular and actively maintained Python libraries that use the official YouTube Data API and support controlled video searching—such as filtering by timeframe, view count, and other metadata.
I'll get back to you shortly with a summary of the best options and their capabilities.

## Message 6

## Most Popular Python YouTube Data API Libraries (with Search Support)

| **Library** | **Advanced Search Features** | **Docs & Activity** |
| --- | --- | --- |
| **Google API Client for YouTube**<br>*(`googleapiclient.discovery`) – Official Google library* | Full access to **YouTube Data API v3** search parameters:<ul><li>Keyword queries (`q`), filter by **upload date range** (`publishedAfter`/`publishedBefore`)</li><li>Restrict type to videos, channels or playlists (`type`)</li><li>Order results by relevance, date, rating, title or **viewCount**【80†L735-L742】</li><li>Region-specific results (`regionCode`), Safe Search filtering, etc.</li></ul>Does *not* natively support a “min view count” filter (the API doesn’t provide this), but you can post-filter results by retrieving video statistics. | **Official docs & samples:** Comprehensive Google documentation and code samples【71†L85-L93】. <br>**Maintenance:** Maintained by Google – regularly updated (part of the `google-api-python-client` library). Large community usage and support (Stack Overflow, Google support). |
| **PyYouTube**<br>*(`python-youtube` or `pyyoutube`) – Community wrapper* | High-level **wrapper** covering all search query parameters:<ul><li>`search_by_keywords()` utility – supports **multi-type search** (videos/channels/playlists)【80†L689-L697】</li><li>Accepts **date filters** (`published_after`, `published_before`)【80†L713-L721】</li><li>Supports **location-based** search (`location`, `location_radius`) and topic or event filters (`topic_id`, live events)</li><li>`order` parameter for sorting (e.g. by **viewCount** or date)【80†L735-L742】. (No direct “min views” filter; users can sort or fetch stats)</li></ul>Returns Pythonic data classes for results (e.g. `SearchResult` objects). | **Documentation:** Well-documented with an easy API (MkDocs site). Usage examples for search and other resources are provided【80†L713-L721】【80†L730-L738】. <br>**Community & Maintenance:** Active project – latest release v0.9.7 (Oct 28, 2024)【34†L1-L4】. ~336 stars on GitHub【51†L1-L4】. Frequent updates and issue activity. |
| **YouTube Data API (Python)**<br>*(`youtube-api` library by SMAPPNYU)* | Simplified **client** for search queries with fine-grained control:<ul><li>`YouTubeDataAPI.search()` method – supports **keywords**, channel filtering, max results, etc.</li><li>Built-in parameters for **timeframe** (`published_after` & `published_before`)【3†L1-L9】</li><li>Supports search filters like video duration, event type (live/upcoming), region code, safeSearch, etc.【3†L21-L29】</li><li>`order_by` parameter (e.g. sort by viewCount or date)【3†L25-L33】. *(No direct min-views filter; requires fetching stats separately)*</li></ul>Designed to automatically handle API requests and parse JSON into Python data. | **Documentation:** Provided via ReadTheDocs【3†L1-L9】【3†L23-L31】, with usage examples. Straightforward to initiate and call search (e.g. `yt.search('keyword')` returns results)【67†L292-L300】. <br>**Community & Maintenance:** Originally released 2018; last PyPI release in Jul 2021【33†L1-L4】. ~81 GitHub stars, moderate usage. Maintained by researchers (Leon Yin et al.), but **less active in recent years** (seeking new maintainers). Still functional for v3 data retrieval. |

Each of these libraries uses the **official YouTube Data API v3** (no HTML scraping), thus complying with YouTube’s Terms of Service. They allow programmatic video search with various filters, though none can directly filter by minimum view count via the API (that must be handled by sorting or post-processing results).

## Library Comparisons and Examples

### 1. Official Google API Client (YouTube Data API v3)  
The **Google API Python Client** is the official library to access YouTube Data API. It provides a low-level interface to all API methods. For searching videos, you build a query with the `youtube.search().list` endpoint. For example: 

```python
from googleapiclient.discovery import build
youtube = build('youtube', 'v3', developerKey=API_KEY)
request = youtube.search().list(
    q="machine learning tutorial", part="snippet", type="video",
    maxResults=50, publishedAfter="2025-01-01T00:00:00Z"
)
response = request.execute()
``` 

This will retrieve up to 50 video results for the query “machine learning tutorial” uploaded after Jan 1, 2025【71†L85-L93】. You can adjust parameters for timeframe (`publishedAfter/Before`), result type (e.g., only videos), and ordering (e.g., `order="viewCount"` to sort by views). To enforce a minimum view count, one would query a sufficiently large set ordered by viewCount and then filter the `response` by each video’s view count (which requires an additional call to `videos().list` to get the statistics for each video ID). The official client is very flexible and always up-to-date with new API features, but it’s a bit verbose – you must manually handle pagination (using `nextPageToken`) and combine data from multiple calls to filter by stats. 

*Documentation and support:* Being official, it’s thoroughly documented on Google Developers and has an extensive user community. The library is regularly maintained by Google (as part of the broader `google-api-python-client` package) and supports all features of YouTube’s API【60†L129-L137】【60†L139-L147】. This is a reliable choice if you need full control and guaranteed compatibility with YouTube’s API changes.

### 2. PyYouTube (python-youtube)  
**PyYouTube** is a popular third-party wrapper that simplifies YouTube API usage. It provides higher-level methods and Python data models for YouTube resources. For searching, PyYouTube offers a convenient **`search_by_keywords`** function. For example: 

```python
from pyyoutube import Api
api = Api(api_key="YOUR_API_KEY")
result = api.search_by_keywords(
    q="surfing", search_type=["video"], count=5, 
    published_after="2020-02-01T00:00:00Z", published_before="2020-03-01T00:00:00Z",
    order="viewCount"
)
for item in result.items:
    print(item.snippet.title, "views:", item.statistics.viewCount)
``` 

This sample uses `search_by_keywords` to find 5 video results for “surfing” uploaded in February 2020, ordered by view count【80†L713-L721】【80†L735-L742】. PyYouTube handles constructing the request and parsing the response into `SearchResult` objects. The library supports *all official search filters*: you can restrict results to videos/channels/playlists, filter by upload date range, location (e.g., within a radius of coordinates), apply topic categories or whether the content is live, and so on【80†L713-L721】【80†L730-L738】. While the YouTube API doesn’t allow filtering by view count threshold directly, PyYouTube at least makes it easy to sort by view count or retrieve video stats for filtering. 

PyYouTube’s **documentation** is robust – it provides a user guide and reference for each method (e.g. search, channels, videos). The project has an active community on GitHub and is actively maintained; as of October 2024 it had a recent release and ongoing updates【34†L1-L4】. Developers often choose PyYouTube for its balance of power and ease-of-use: it reduces boilerplate (no need to manually page through results or decode JSON) while still giving access to advanced query parameters. 

### 3. *youtube-api* (YouTube Data API Wrapper by SMAPPNYU)  
The **youtube-data-api** Python package (by the Social Media and Political Participation lab, NYU) is another wrapper aimed at simplifying video searches. It centers around a `YouTubeDataAPI` class. After instantiating with an API key, you can call its `search()` method:

```python
from youtube_api import YouTubeDataAPI
yt = YouTubeDataAPI(api_key="YOUR_API_KEY")
results = yt.search(q="latest tech review", max_results=10, published_after=1672531200, order_by="date")
for video in results:
    print(video['video_id'], video['snippet']['title'])
``` 

In this example, `yt.search` retrieves 10 recent videos for the query “latest tech review” uploaded after a given timestamp (Jan 1, 2023)【3†L1-L9】. Under the hood, this wrapper’s `search` method accepts many of the same parameters as the raw API: you can specify `published_after/before` (in Unix timestamp or datetime format) to define a timeframe, `order_by` to sort (e.g. by date or viewCount)【3†L25-L33】, `region_code`, `safe_search` level, etc., and even filter by video duration or event type【3†L21-L29】. The library automatically handles the HTTP GET requests and JSON parsing, returning results in dictionary form (with snippet and other fields). It does not inherently filter out low-view videos (you would still need to inspect the returned statistics), but it simplifies retrieving the data needed to do so.

**Documentation and status:** The project’s documentation (on ReadTheDocs) provides an API reference and usage examples【3†L1-L9】【67†L292-L300】. It was quite useful for data collection in research projects and has functionalities beyond search (e.g., convenient methods to get channel or video metadata). However, note that its maintenance has slowed – the last release was in mid-2021【33†L1-L4】. It’s still a viable library for read-only data retrieval, but the community is smaller and updates are infrequent now. If your needs are straightforward and the library’s last version supports them, it can save you some coding. Otherwise, one of the more actively maintained options above might be preferable for long-term projects.

### **Conclusion**

All the above libraries use the **official YouTube Data API** and support fine-grained video searches with filters like upload date ranges, search keywords, content types, and ordering by view count. They differ in abstraction level: the official Google client offers the most direct control (mirroring the raw API) whereas PyYouTube and similar wrappers provide more convenient methods and object representations. In practice, **PyYouTube** stands out for its active maintenance and ease of use when doing complex searches (it cleanly supports date filters, sorting, etc., within one call). The **Google API client** is a dependable choice especially if you are already using Google’s Python client for other services or need the utmost flexibility. The **SMAPPNYU YouTube Data API** wrapper can be useful for quick scripts or data analysis tasks, though its development activity has tapered off. 

Regardless of choice, you can achieve the required functionality (e.g., “find videos uploaded in the last N days with over 1,000 views”) by combining the API’s search capabilities with an extra step to check view counts. For example, use the library to search within a timeframe and then for each video ID retrieved, use a library method to fetch statistics (views) and filter in code. All three libraries make it possible to implement this workflow while respecting YouTube’s terms of service, without resorting to any unofficial scraping solutions. 

**Sources:** Official YouTube Data API documentation【80†L735-L742】【3†L1-L9】, PyYouTube documentation【80†L713-L721】【80†L730-L738】, project release info【34†L1-L4】【33†L1-L4】, and example usage from library docs and tutorials【71†L85-L93】【67†L292-L300】.

## Message 7

can the Google API Client for YouTube (or either of them) search/filter to only include videos e.g. above a specific viewcount?

## Message 8

**Clarifying API limitations**

OK, let me see. The YouTube Data API lacks a direct filter for minimum or maximum view counts. Instead, post-process results by sorting or fetching stats for specific IDs.

## Message 9

**Short Answer**:  
No, the YouTube Data API (and thus the Google API Client or wrappers like PyYouTube) does **not** have a built-in “min view count” filter. All libraries that rely on the official YouTube Data API must work around this by:

1. **Querying videos** (e.g., with keywords, timeframe filters, etc.).  
2. **Retrieving video statistics** (view count, like count, etc.).  
3. **Filtering** in your own code to keep only those with a view count above your threshold.

---

## Why No Direct `viewCount > X` Filter?
The underlying YouTube Data API itself does not provide any parameter that says “only return videos with views >= 1000,” or “only return videos with likes > 10.” Instead, you can sort by `viewCount`, but still must retrieve each result’s stats and filter them yourself.

---

## Typical Workaround Pattern

1. **Search**:  
   - Use `youtube.search().list` with relevant parameters (`q`, `publishedAfter`, `type="video"`, etc.).  
   - Optionally set `order="viewCount"` to retrieve the most viewed items first.

2. **Get Statistics**:  
   - Extract the video IDs from the search results.  
   - Call `youtube.videos().list(part="statistics", id="VIDEO_IDS")` to fetch each video’s statistics (views, likes, etc.).

3. **Filter**:  
   - For each video in the `videos().list` response, compare `statistics.viewCount` to your desired threshold (e.g., > 1000).  
   - Keep or discard the video accordingly.

---

### **Example with the Google API Client**

```python
from googleapiclient.discovery import build

# 1) Build the client
youtube = build('youtube', 'v3', developerKey='YOUR_API_KEY')

# 2) First, search for videos
search_response = youtube.search().list(
    q="python tutorial",
    part="snippet",
    type="video",
    publishedAfter="2025-01-01T00:00:00Z",
    maxResults=50,
    order="viewCount"  # optional, if you want to retrieve by highest views first
).execute()

# 3) Extract video IDs
video_ids = [item["id"]["videoId"] for item in search_response.get("items", [])]

# 4) Get each video's statistics
stats_response = youtube.videos().list(
    part="statistics",
    id=",".join(video_ids)
).execute()

videos_above_1000 = []
for item in stats_response.get("items", []):
    view_count = int(item["statistics"]["viewCount"])
    if view_count > 1000:
        videos_above_1000.append(item["id"])

print("Found these videos over 1000 views:", videos_above_1000)
```

> **Note**: You might need to handle pagination if the search returns multiple pages (using `pageToken`) and combine everything, then filter for the threshold.

---

## Final Takeaway
Because the official YouTube Data API doesn’t offer a direct “minimum view count” parameter, **all** Python libraries (including the official Google API Client and third-party wrappers like PyYouTube) must follow this same approach: **search, fetch statistics, then filter**. This is the standard method for restricting results by view count while respecting YouTube’s Terms of Service.
