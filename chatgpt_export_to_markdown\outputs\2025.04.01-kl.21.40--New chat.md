# New chat

Conversation ID: 67ec4128-e8f8-8008-a167-451e7d573a2b

## Message 1

please write a proper .gitignore for this directory (the extensions dir of portable vscode):

"app_vscode/exe/data/extensions/.specstory/"

"app_vscode/exe/data/extensions/.specstory/history/"

"app_vscode/exe/data/extensions/jorn-vscode-theme-one/themes/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/.config/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/.config/1espt/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydev_bundle/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_plugins/extensions/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/"

"app_vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/scripts/noConfigScripts/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/dist/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/unicode/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/unicode/category/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python-env-tools/bin/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/.vscode/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/deactivate/bash/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attr/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attrs-24.2.0.dist-info/licenses/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattr/preconf/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs/gen/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs-24.1.2.dist-info/licenses/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/api/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/api/refactoring/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/compiled/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/compiled/subprocess/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/apps/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/conf/locale/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admin/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admin/templatetags/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/handlers/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/management/commands/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/commands/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/flatpages/templatetags/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/models/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/humanize/templatetags/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/messages/storage/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/postgres/aggregates/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/backends/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/management/commands/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/commands/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/commands/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/cache/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/cache/backends/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/checks/security/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/mail/backends/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/management/commands/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/base/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/migrations/operations/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/models/fields/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/template/backends/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/utils/translation/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/views/decorators/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/distutils/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/distutils/command/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/email/mime/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/multiprocessing/dummy/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/_typeshed/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/lib2to3/pgen2/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/dom/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/parsers/expat/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/asyncio/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/concurrent/futures/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/distutils/command/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/email/mime/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/multiprocessing/dummy/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3.9/zoneinfo/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/concurrent/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/concurrent/futures/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/kazoo/recipe/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/six/moves/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/six/moves/urllib/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/atomicwrites/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/boto/ec2/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/backends/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/bindings/openssl/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/asymmetric/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/dateutil/tz/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/flask/json/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/compiler/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/markdown/extensions/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pymysql/constants/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pynamodb/connection/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pyVmomi/vim/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/contrib/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/packages/ssl_match_hostname/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/werkzeug/contrib/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/aiofiles/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/aiofiles/threadpool/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/docutils/parsers/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/docutils/parsers/rst/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/jwt/contrib/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/jwt/contrib/algorithms/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/six/moves/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/six/moves/urllib/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/parso/pgen2/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/pygls/lsp/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/importlib_metadata/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/importlib_metadata/compat/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/microvenv-2023.5.post1.dist-info/licenses/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/packaging/licenses/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/zipp/compat/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/vscode_datascience_helpers/tests/"

"app_vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/resources/dark/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/files/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/lxml/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/numpy/core/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/_libs/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/_libs/tslibs/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/io/sas/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/apps/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/conf/locale/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/migrations/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/handlers/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/management/commands/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/management/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/management/commands/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/flatpages/migrations/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/admin/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/base/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/models/sql/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/gdal/prototypes/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/geos/prototypes/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/humanize/templatetags/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/messages/storage/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/postgres/aggregates/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/redirects/migrations/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/backends/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/management/commands/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sitemaps/management/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sitemaps/management/commands/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sites/migrations/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/management/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/management/commands/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/cache/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/cache/backends/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/checks/compatibility/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/mail/backends/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/management/commands/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/base/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/migrations/operations/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/models/fields/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/template/backends/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/utils/translation/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/views/decorators/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/core/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/core/magics/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/terminal/pt_inputhooks/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/_api/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/approximation/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/readwrite/json_graph/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_config/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_libs/tslibs/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/api/extensions/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/arrays/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/arrays/arrow/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/clipboard/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/util/version/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/_shared/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/future/graph/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/__check_build/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/datasets/data/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/ensemble/_hist_gradient_boosting/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/externals/_packaging/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/inspection/_plot/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/linear_model/_glm/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/metrics/_pairwise_distances_reduction/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/algebras/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/assumptions/handlers/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/functions/combinatorial/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/logic/algorithms/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/matrices/expressions/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/control/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/units/definitions/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/plotting/intervalmath/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/polys/agca/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/printing/pretty/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/sets/handlers/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/solvers/diophantine/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/stats/sampling/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/strategies/branch/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/tensor/array/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/tensor/array/expressions/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/utilities/mathml/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/transformers-stubs/models/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/transformers-stubs/models/auto/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/app/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/gloo/gl/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/scene/cameras/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/util/dpi/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/collections/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/graphs/layouts/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/_typeshed/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/concurrent/futures/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/ctypes/macholib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/distutils/command/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/email/mime/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/importlib/metadata/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/lib2to3/fixes/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/multiprocessing/dummy/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/dom/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/parsers/expat/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/zipfile/_path/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/aiofiles/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/aiofiles/tempfile/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/atn/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/assertpy/assertpy/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/atheris/atheris/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/common/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/integrations/base_client/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/drafts/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth1/rfc5849/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc6749/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc6749/grants/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/core/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/core/grants/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/emitters/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/sampling/local/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/beautifulsoup4/bs4/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/beautifulsoup4/bs4/builder/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/bleach/bleach/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/boltons/boltons/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/dispute_details/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/exceptions/http/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/graphql/enums/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cachetools/cachetools/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/caldav/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/caldav/elements/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cffi/cffi/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/chevron/chevron/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-log/click_log/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-spinner/click_spinner/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-web/click_web/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-web/click_web/resources/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/colorama/colorama/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/commonmark/commonmark/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/commonmark/commonmark/render/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/format/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/sources/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/sources/taiga/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/croniter/croniter/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/calendars/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/defusedxml/defusedxml/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Deprecated/deprecated/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/import_export/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/import_export/formats/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/management/commands/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/api/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dockerfile-parse/dockerfile_parse/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/languages/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/parsers/rst/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/parsers/rst/directives/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/editdistance/editdistance/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/tags/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/tags/makernote/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fanstatic/fanstatic/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/api/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-simplify/flake8_simplify/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Cors/flask_cors/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Migrate/flask_migrate/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-SocketIO/flask_socketio/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fpdf2/fpdf/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gdb/gdb/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gdb/gdb/dap/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/geopandas/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/geopandas/io/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/_ffi/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/cloud/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/cloud/ndb/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/greenlet/greenlet/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hdbcli/hdbcli/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/_trie/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/httplib2/httplib2/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/humanfriendly/humanfriendly/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/humanfriendly/humanfriendly/terminal/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/auth_methods/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/icalendar/icalendar/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/icalendar/icalendar/timezone/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/_async/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/client/util/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/JACK-Client/jack/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Jetson.GPIO/Jetson/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Jetson.GPIO/Jetson/GPIO/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jmespath/jmespath/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jsonschema/jsonschema/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jwcrypto/jwcrypto/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/keyboard/keyboard/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/abstract/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/extend/microsoft/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/protocol/formatters/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/libsass/sassutils/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lupa/lupa/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lzstring/lzstring/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/m3u8/m3u8/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Markdown/markdown/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Markdown/markdown/extensions/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mock/mock/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mysqlclient/MySQLdb/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mysqlclient/MySQLdb/constants/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoid/nanoid/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoleafapi/nanoleafapi/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/contrib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/approximation/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/readwrite/json_graph/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/rfc5849/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/rfc5849/endpoints/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/clients/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/core/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/core/endpoints/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/olefile/olefile/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/cell/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/workbook/external_link/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/ext/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/paramiko/paramiko/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/parsimonious/parsimonious/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/crypto/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/crypto/_blowfish/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/ext/django/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/utils/compat/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passpy/passpy/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/peewee/playhouse/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pexpect/pexpect/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/adapters/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/adapters/utils/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/compiler/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psutil/psutil/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psycopg2/psycopg2/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/ber/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyAutoGUI/pyautogui/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pycocotools/pycocotools/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyflakes/pyflakes/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyflakes/pyflakes/scripts/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pygit2/pygit2/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/filters/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/pyi_splash/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/building/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/lib/modulegraph/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/utils/hooks/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyjks/jks/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyMySQL/pymysql/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyMySQL/pymysql/constants/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/pynput/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/pynput/keyboard/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyperclip/pyperclip/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyRFC3339/pyrfc3339/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyScreeze/pyscreeze/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/threaded/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pysftp/pysftp/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-datemath/datemath/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/parser/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-http-client/python_http_client/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jenkins/jenkins/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jose/jose/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jose/jose/backends/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-nmap/nmap/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/ext/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pytz/pytz/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/isapi/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32/lib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/adsi/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/axscript/client/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/adsi/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/axscript/client/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyxdg/xdg/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyYAML/yaml/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrbill/qrbill/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/styles/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/styles/moduledrawers/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/regex/regex/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/barcode/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests/requests/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests-oauthlib/requests_oauthlib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests-oauthlib/requests_oauthlib/compliance_fixes/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/retry/retry/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/RPi.GPIO/RPi/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/RPi.GPIO/RPi/GPIO/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/s2clientprotocol/s2clientprotocol/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/_core/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Send2Trash/send2trash/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/distutils/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/distutils/command/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/pkg_resources/_vendored_packaging/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/_distutils/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/_distutils/command/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/algorithms/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/simplejson/simplejson/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/moves/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/moves/urllib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/slumber/slumber/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/str2bool/str2bool/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tabulate/tabulate/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/TgCrypto/tgcrypto/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/toml/toml/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tqdm/tqdm/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tqdm/tqdm/contrib/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/translationstring/translationstring/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tree-sitter-languages/tree_sitter_languages/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ttkthemes/ttkthemes/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/unidiff/unidiff/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/vobject/@tests/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/waitress/waitress/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WebOb/webob/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/whatthepatch/whatthepatch/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/africa/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/europe/scotland/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/europe/scotland/mixins/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/csrf/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/xdgenvpy/xdgenvpy/"

"app_vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/zxcvbn/zxcvbn/"

"app_vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/dist/"

"app_vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/dist/browser/"

"app_vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/resources/walkthrough/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/.changeset/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/assets/docs/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/ca/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/codicons/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/codicons/dist/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/src/integrations/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/src/integrations/theme/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/src/integrations/theme/default-themes/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/webview-ui/.storybook/"

"app_vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/webview-ui/build/assets/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.changeset/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.github/ISSUE_TEMPLATE/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/assets/docs/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/ar-sa/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/codicons/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/codicons/dist/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/src/integrations/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/src/integrations/theme/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/src/integrations/theme/default-themes/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/webview-ui/build/"

"app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/webview-ui/build/assets/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/.github/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/.github/ISSUE_TEMPLATE/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/electron-webview/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/font-awesome/css/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/fonts/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/css/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/css/print/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/css/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/font/league-gothic/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/multiplex/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/wavedrom/skins/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/styles/preview_theme/"

"app_vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/out/native/"

## Message 2

**Crafting the .gitignore**

To address the request, I’m focusing on paths within the parent directory. Ensuring the paths are relative and formatted correctly, I’m inclined to place the .gitignore in the "extensions" folder.

**Assessing directory context**

I’m figuring out if the paths are relative to the "extensions" directory. The leading root path in the provided lines makes me think they link back to the "extensions" directory.

**Adjusting the .gitignore**

I’m piecing together the paths for the .gitignore file in the "extensions" directory. By trimming the "app_vscode/exe/data/extensions/" prefix, I ensure the paths accurately exclude specific directories.

**Designing precise exclusions**

I’m assembling the .gitignore file, ensuring paths are relative, formatted correctly, and end with a slash for directories. Safely excluding each directory individually leads to a clearer result.

**Finalizing the .gitignore**

I’m organizing the .gitignore by removing quotes, adjusting paths, and ensuring each directory entry has a trailing slash. The file's extensive nature requires careful attention to each line.

## Message 3

Below is a `.gitignore` file that assumes **it is placed directly in** the `app_vscode/exe/data/extensions` folder. All paths have been stripped of the common prefix and begin with `/`, making them relative to this `.gitignore` file. 

<details>
<summary>Click to view the complete .gitignore content</summary>

```gitignore
# .specstory
/.specstory/
/.specstory/history/

# jorn-vscode-theme-one
/jorn-vscode-theme-one/themes/

# ms-python.debugpy-2025.4.1-win32-x64
/ms-python.debugpy-2025.4.1-win32-x64/.config/
/ms-python.debugpy-2025.4.1-win32-x64/.config/1espt/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydev_bundle/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_plugins/extensions/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/
/ms-python.debugpy-2025.4.1-win32-x64/bundled/scripts/noConfigScripts/

# ms-python.python-2025.2.0-win32-x64
/ms-python.python-2025.2.0-win32-x64/dist/
/ms-python.python-2025.2.0-win32-x64/out/client/
/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/
/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/unicode/
/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/unicode/category/
/ms-python.python-2025.2.0-win32-x64/python-env-tools/bin/
/ms-python.python-2025.2.0-win32-x64/python_files/.vscode/
/ms-python.python-2025.2.0-win32-x64/python_files/deactivate/bash/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attr/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attrs-24.2.0.dist-info/licenses/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattr/preconf/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs/gen/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs-24.1.2.dist-info/licenses/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/api/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/api/refactoring/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/compiled/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/compiled/subprocess/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/apps/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/conf/locale/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admin/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admin/templatetags/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/handlers/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/management/commands/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/commands/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/flatpages/templatetags/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/models/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/humanize/templatetags/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/messages/storage/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/postgres/aggregates/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/backends/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/management/commands/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/commands/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/commands/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/cache/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/cache/backends/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/checks/security/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/mail/backends/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/management/commands/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/base/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/migrations/operations/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/models/fields/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/template/backends/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/utils/translation/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/views/decorators/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/distutils/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/distutils/command/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/email/mime/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/multiprocessing/dummy/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/_typeshed/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/lib2to3/pgen2/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/dom/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/parsers/expat/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/asyncio/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/concurrent/futures/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/distutils/command/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/email/mime/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/multiprocessing/dummy/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3.9/zoneinfo/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/concurrent/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/concurrent/futures/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/kazoo/recipe/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/six/moves/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/six/moves/urllib/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/atomicwrites/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/boto/ec2/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/backends/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/bindings/openssl/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/asymmetric/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/dateutil/tz/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/flask/json/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/compiler/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/markdown/extensions/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pymysql/constants/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pynamodb/connection/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pyVmomi/vim/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/contrib/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/packages/ssl_match_hostname/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/werkzeug/contrib/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/aiofiles/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/aiofiles/threadpool/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/docutils/parsers/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/docutils/parsers/rst/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/jwt/contrib/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/jwt/contrib/algorithms/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/six/moves/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/six/moves/urllib/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/parso/pgen2/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/pygls/lsp/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/importlib_metadata/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/importlib_metadata/compat/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/microvenv-2023.5.post1.dist-info/licenses/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/packaging/licenses/
/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/zipp/compat/
/ms-python.python-2025.2.0-win32-x64/python_files/vscode_datascience_helpers/tests/
/ms-python.python-2025.2.0-win32-x64/resources/dark/

# ms-python.vscode-pylance-2025.3.2
/ms-python.vscode-pylance-2025.3.2/dist/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/files/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/lxml/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/numpy/core/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/_libs/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/_libs/tslibs/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/io/sas/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/apps/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/conf/locale/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/migrations/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/handlers/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/management/commands/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/management/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/management/commands/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/flatpages/migrations/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/admin/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/base/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/models/sql/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/gdal/prototypes/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/geos/prototypes/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/humanize/templatetags/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/messages/storage/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/postgres/aggregates/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/redirects/migrations/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/backends/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/management/commands/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sitemaps/management/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sitemaps/management/commands/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sites/migrations/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/management/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/management/commands/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/cache/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/cache/backends/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/checks/compatibility/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/mail/backends/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/management/commands/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/base/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/migrations/operations/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/models/fields/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/template/backends/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/utils/translation/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/views/decorators/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/core/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/core/magics/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/terminal/pt_inputhooks/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/_api/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/approximation/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/readwrite/json_graph/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_config/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_libs/tslibs/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/api/extensions/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/arrays/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/arrays/arrow/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/clipboard/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/util/version/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/_shared/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/future/graph/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/__check_build/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/datasets/data/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/ensemble/_hist_gradient_boosting/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/externals/_packaging/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/inspection/_plot/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/linear_model/_glm/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/metrics/_pairwise_distances_reduction/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/algebras/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/assumptions/handlers/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/functions/combinatorial/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/logic/algorithms/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/matrices/expressions/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/control/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/units/definitions/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/plotting/intervalmath/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/polys/agca/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/printing/pretty/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/sets/handlers/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/solvers/diophantine/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/stats/sampling/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/strategies/branch/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/tensor/array/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/tensor/array/expressions/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/utilities/mathml/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/transformers-stubs/models/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/transformers-stubs/models/auto/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/app/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/gloo/gl/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/scene/cameras/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/util/dpi/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/collections/
/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/graphs/layouts/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/_typeshed/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/concurrent/futures/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/ctypes/macholib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/distutils/command/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/email/mime/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/importlib/metadata/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/lib2to3/fixes/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/multiprocessing/dummy/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/dom/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/parsers/expat/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/zipfile/_path/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/aiofiles/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/aiofiles/tempfile/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/atn/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/assertpy/assertpy/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/atheris/atheris/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/common/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/integrations/base_client/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/drafts/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth1/rfc5849/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc6749/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc6749/grants/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/core/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/core/grants/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/emitters/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/sampling/local/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/beautifulsoup4/bs4/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/beautifulsoup4/bs4/builder/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/bleach/bleach/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/boltons/boltons/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/dispute_details/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/exceptions/http/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/graphql/enums/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cachetools/cachetools/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/caldav/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/caldav/elements/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cffi/cffi/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/chevron/chevron/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-log/click_log/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-spinner/click_spinner/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-web/click_web/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-web/click_web/resources/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/colorama/colorama/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/commonmark/commonmark/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/commonmark/commonmark/render/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/format/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/sources/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/sources/taiga/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/croniter/croniter/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/calendars/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/defusedxml/defusedxml/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Deprecated/deprecated/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/import_export/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/import_export/formats/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/management/commands/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/api/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dockerfile-parse/dockerfile_parse/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/languages/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/parsers/rst/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/parsers/rst/directives/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/editdistance/editdistance/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/tags/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/tags/makernote/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fanstatic/fanstatic/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/api/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-simplify/flake8_simplify/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Cors/flask_cors/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Migrate/flask_migrate/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-SocketIO/flask_socketio/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fpdf2/fpdf/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gdb/gdb/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gdb/gdb/dap/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/geopandas/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/geopandas/io/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/_ffi/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/cloud/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/cloud/ndb/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/greenlet/greenlet/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hdbcli/hdbcli/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/_trie/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/httplib2/httplib2/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/humanfriendly/humanfriendly/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/humanfriendly/humanfriendly/terminal/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/auth_methods/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/icalendar/icalendar/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/icalendar/icalendar/timezone/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/_async/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/client/util/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/JACK-Client/jack/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Jetson.GPIO/Jetson/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Jetson.GPIO/Jetson/GPIO/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jmespath/jmespath/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jsonschema/jsonschema/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jwcrypto/jwcrypto/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/keyboard/keyboard/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/abstract/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/extend/microsoft/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/protocol/formatters/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/libsass/sassutils/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lupa/lupa/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lzstring/lzstring/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/m3u8/m3u8/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Markdown/markdown/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Markdown/markdown/extensions/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mock/mock/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mysqlclient/MySQLdb/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mysqlclient/MySQLdb/constants/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoid/nanoid/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoleafapi/nanoleafapi/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/contrib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/approximation/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/readwrite/json_graph/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/rfc5849/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/rfc5849/endpoints/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/clients/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/core/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/core/endpoints/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/olefile/olefile/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/cell/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/workbook/external_link/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/ext/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/paramiko/paramiko/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/parsimonious/parsimonious/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/crypto/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/crypto/_blowfish/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/ext/django/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/utils/compat/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passpy/passpy/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/peewee/playhouse/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pexpect/pexpect/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/adapters/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/adapters/utils/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/compiler/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psutil/psutil/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psycopg2/psycopg2/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/ber/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyAutoGUI/pyautogui/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pycocotools/pycocotools/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyflakes/pyflakes/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyflakes/pyflakes/scripts/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pygit2/pygit2/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/filters/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/pyi_splash/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/building/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/lib/modulegraph/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/utils/hooks/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyjks/jks/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyMySQL/pymysql/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyMySQL/pymysql/constants/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/pynput/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/pynput/keyboard/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyperclip/pyperclip/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyRFC3339/pyrfc3339/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyScreeze/pyscreeze/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/threaded/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pysftp/pysftp/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-datemath/datemath/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/parser/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-http-client/python_http_client/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jenkins/jenkins/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jose/jose/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jose/jose/backends/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-nmap/nmap/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/ext/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pytz/pytz/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/isapi/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32/lib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/adsi/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/axscript/client/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/adsi/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/axscript/client/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyxdg/xdg/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyYAML/yaml/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrbill/qrbill/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/styles/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/styles/moduledrawers/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/regex/regex/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/barcode/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests/requests/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests-oauthlib/requests_oauthlib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests-oauthlib/requests_oauthlib/compliance_fixes/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/retry/retry/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/RPi.GPIO/RPi/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/RPi.GPIO/RPi/GPIO/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/s2clientprotocol/s2clientprotocol/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/_core/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Send2Trash/send2trash/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/distutils/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/distutils/command/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/pkg_resources/_vendored_packaging/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/_distutils/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/_distutils/command/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/algorithms/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/simplejson/simplejson/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/moves/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/moves/urllib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/slumber/slumber/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/str2bool/str2bool/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tabulate/tabulate/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/TgCrypto/tgcrypto/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/toml/toml/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tqdm/tqdm/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tqdm/tqdm/contrib/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/translationstring/translationstring/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tree-sitter-languages/tree_sitter_languages/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ttkthemes/ttkthemes/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/unidiff/unidiff/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/vobject/@tests/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/waitress/waitress/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WebOb/webob/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/whatthepatch/whatthepatch/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/africa/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/europe/scotland/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/europe/scotland/mixins/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/csrf/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/xdgenvpy/xdgenvpy/
/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/zxcvbn/zxcvbn/

# ms-vscode-remote.remote-wsl-0.88.5
/ms-vscode-remote.remote-wsl-0.88.5/dist/
/ms-vscode-remote.remote-wsl-0.88.5/dist/browser/
/ms-vscode-remote.remote-wsl-0.88.5/resources/walkthrough/

# rooveterinaryinc.roo-cline-3.11.3
/rooveterinaryinc.roo-cline-3.11.3/.changeset/
/rooveterinaryinc.roo-cline-3.11.3/assets/docs/
/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/
/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/
/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/ca/
/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/
/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/codicons/
/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/codicons/dist/
/rooveterinaryinc.roo-cline-3.11.3/src/integrations/
/rooveterinaryinc.roo-cline-3.11.3/src/integrations/theme/
/rooveterinaryinc.roo-cline-3.11.3/src/integrations/theme/default-themes/
/rooveterinaryinc.roo-cline-3.11.3/webview-ui/.storybook/
/rooveterinaryinc.roo-cline-3.11.3/webview-ui/build/assets/

# saoudrizwan.claude-dev-3.8.5
/saoudrizwan.claude-dev-3.8.5/.changeset/
/saoudrizwan.claude-dev-3.8.5/.github/ISSUE_TEMPLATE/
/saoudrizwan.claude-dev-3.8.5/assets/docs/
/saoudrizwan.claude-dev-3.8.5/locales/ar-sa/
/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/
/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/codicons/
/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/codicons/dist/
/saoudrizwan.claude-dev-3.8.5/src/integrations/
/saoudrizwan.claude-dev-3.8.5/src/integrations/theme/
/saoudrizwan.claude-dev-3.8.5/src/integrations/theme/default-themes/
/saoudrizwan.claude-dev-3.8.5/webview-ui/build/
/saoudrizwan.claude-dev-3.8.5/webview-ui/build/assets/

# shd101wyy.markdown-preview-enhanced-0.8.18
/shd101wyy.markdown-preview-enhanced-0.8.18/.github/
/shd101wyy.markdown-preview-enhanced-0.8.18/.github/ISSUE_TEMPLATE/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/electron-webview/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/font-awesome/css/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/fonts/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/css/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/css/print/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/css/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/font/league-gothic/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/multiplex/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/wavedrom/skins/
/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/styles/preview_theme/
/shd101wyy.markdown-preview-enhanced-0.8.18/out/native/
```
</details>

---

### How to Use

1. **Create** or **open** your `.gitignore` file in the **`app_vscode/exe/data/extensions/`** directory.
2. **Paste** all of the lines above into it.
3. **Commit** or **stash** your changes so that Git recognizes these ignore rules.

With this configuration, any file or subfolder listed above will be ignored by Git in your portable VS Code extensions folder.

## Message 4

i'm looking for generalized pattern recognition of common exclusions, please write it with this updated reference (keep it short and generalized):

'vscode/exe/data/extensions/'

'vscode/exe/data/.gitignore'

'vscode/exe/data/extensions/.specstory/'

'vscode/exe/data/extensions/.new_hashes.py'

'vscode/exe/data/extensions/.specstory/history/'

'vscode/exe/data/extensions/.specstory/history/.what-is-this.md'

'vscode/exe/data/extensions/jorn-commands-0.1.0/.gitignore'

'vscode/exe/data/extensions/jorn-vscode-theme-one/themes/'

'vscode/exe/data/extensions/jorn-vscode-theme-one/logo-jorn-theme-one.eps'

'vscode/exe/data/extensions/jorn-vscode-theme-one/themes/jorn-vscode-theme-one-default.json'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/.config/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/.flake8'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/.config/1espt/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/.config/1espt/PipelineAutobaseliningConfig.yml'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/.config/guardian/.gdnbaselines'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydev_bundle/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydev_app_engine_debug_startup.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydev_bundle/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydev_runfiles/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/.gitignore'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/_pydevd_sys_monitoring.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydev_ipython/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydev_sitecustomize/__not_in_default_pythonpath.txt'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/_always_live_program.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_custom_pyeval_settrace.hpp'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/.gitignore'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/attach.cpp'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_plugins/extensions/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_plugins/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_plugins/extensions/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/adapter/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/common/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/launcher/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy/server/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/debugpy-1.8.12.dist-info/entry_points.txt'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/packaging/__init__.py'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/libs/packaging-23.2.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/scripts/noConfigScripts/'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/bundled/scripts/noConfigScripts/debugpy'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/dist/extension.js'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/l10n/bundle.l10n.cs.json'

'vscode/exe/data/extensions/ms-python.debugpy-2025.4.1-win32-x64/resources/report_issue_template.md'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/dist/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/.vsixmanifest'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/dist/extension.browser.js'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/images/addIcon.PNG'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/l10n/bundle.l10n.cs.json'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/languages/pip-requirements.json'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/extension.js'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/unicode/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/node-stream-zip.js'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/unicode/category/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/out/client/node_modules/unicode/category/Ll.js'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python-env-tools/bin/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python-env-tools/bin/pet.exe'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/.vscode/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/.env'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/.vscode/settings.json'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/deactivate/bash/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/deactivate/bash/deactivate'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/deactivate/fish/deactivate'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/deactivate/powershell/deactivate.ps1'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/deactivate/zsh/deactivate'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/jedilsp_requirements/requirements.in'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attr/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/typing_extensions.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attr/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attrs/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attrs-24.2.0.dist-info/licenses/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attrs-24.2.0.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/attrs-24.2.0.dist-info/licenses/LICENSE'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattr/preconf/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattr/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattr/preconf/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs/gen/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs/gen/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs/preconf/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs/strategies/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs-24.1.2.dist-info/licenses/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs-24.1.2.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/cattrs-24.1.2.dist-info/licenses/LICENSE'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/docstring_to_markdown/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/docstring_to_markdown-0.15.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/exceptiongroup/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/exceptiongroup-1.2.2.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/api/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/api/refactoring/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/api/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/api/refactoring/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/compiled/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/compiled/subprocess/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/compiled/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/compiled/subprocess/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/gradual/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/inference/value/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/plugins/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/LICENSE.txt'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/apps/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/apps/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/conf/locale/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/conf/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/conf/locale/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/conf/urls/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admin/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admin/templatetags/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admin/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admin/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admin/views/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/admindocs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/handlers/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/handlers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/management/commands/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/auth/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/commands/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/contenttypes/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/flatpages/templatetags/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/flatpages/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/flatpages/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/gis/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/models/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/gis/db/models/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/humanize/templatetags/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/humanize/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/humanize/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/messages/storage/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/messages/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/messages/storage/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/postgres/aggregates/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/postgres/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/postgres/aggregates/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/postgres/fields/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/redirects/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/backends/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/management/commands/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sessions/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/commands/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sitemaps/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/sites/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/commands/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/staticfiles/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/contrib/syndication/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/cache/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/cache/backends/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/cache/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/cache/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/checks/security/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/checks/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/checks/security/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/files/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/handlers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/mail/backends/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/mail/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/mail/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/management/commands/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/serializers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/core/servers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/base/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/base/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/dummy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/mysql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/postgresql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/backends/sqlite3/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/migrations/operations/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/migrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/migrations/operations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/models/fields/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/models/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/models/fields/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/models/functions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/db/models/sql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/dispatch/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/forms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/http/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/middleware/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/template/backends/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/template/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/template/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/template/loaders/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/test/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/urls/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/utils/translation/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/utils/translation/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/views/decorators/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/views/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/views/decorators/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/django-stubs/django-stubs/views/generic/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/LICENSE'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/distutils/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/__builtin__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/distutils/command/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/distutils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/distutils/command/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/email/mime/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/email/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/email/mime/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/encodings/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/multiprocessing/dummy/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/multiprocessing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/multiprocessing/dummy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2/os/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/_typeshed/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/__future__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/_typeshed/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/ctypes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/curses/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/ensurepip/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/lib2to3/pgen2/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/lib2to3/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/lib2to3/pgen2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/logging/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/msilib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/pydoc_data/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/pyexpat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/sqlite3/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/wsgiref/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/dom/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/dom/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/etree/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/parsers/expat/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/parsers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/parsers/expat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/2and3/xml/sax/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/asyncio/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/_ast.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/asyncio/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/collections/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/concurrent/futures/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/concurrent/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/concurrent/futures/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/dbm/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/distutils/command/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/distutils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/distutils/command/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/email/mime/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/email/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/email/mime/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/encodings/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/html/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/http/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/importlib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/json/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/multiprocessing/dummy/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/multiprocessing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/multiprocessing/dummy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/os/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/tkinter/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/unittest/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/urllib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/venv/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3/xmlrpc/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3.7/_py_abc.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3.9/zoneinfo/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3.9/graphlib.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/stdlib/3.9/zoneinfo/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/concurrent/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/enum.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/concurrent/futures/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/concurrent/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/concurrent/futures/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/fb303/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/kazoo/recipe/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/kazoo/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/kazoo/recipe/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/OpenSSL/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/routes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/scribe/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/six/moves/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/six/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/six/moves/urllib/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/six/moves/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/six/moves/urllib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2/tornado/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/atomicwrites/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/backports_abc.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/atomicwrites/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/attr/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/backports/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/bleach/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/boto/ec2/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/boto/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/boto/ec2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/boto/elb/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/boto/kms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/boto/s3/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cachetools/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/characteristic/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/chardet/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/click/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/backends/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/bindings/openssl/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/bindings/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/bindings/openssl/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/asymmetric/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/asymmetric/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/ciphers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/kdf/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/serialization/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/hazmat/primitives/twofactor/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/cryptography/x509/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/datetimerange/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/dateutil/tz/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/dateutil/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/dateutil/tz/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/deprecated/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/emoji/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/flask/json/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/flask/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/flask/json/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/geoip2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/compiler/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/compiler/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/internal/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/google/protobuf/util/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/jinja2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/markdown/extensions/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/markdown/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/markdown/extensions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/markupsafe/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/maxminddb/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/nmap/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/paramiko/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pymysql/constants/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pymysql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pymysql/constants/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pynamodb/connection/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pynamodb/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pynamodb/connection/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pytz/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pyVmomi/vim/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pyVmomi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pyVmomi/vim/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/pyVmomi/vmodl/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/redis/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/contrib/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/contrib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/packages/ssl_match_hostname/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/packages/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/packages/ssl_match_hostname/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/requests/packages/urllib3/util/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/retry/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/simplejson/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/slugify/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/tzlocal/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/werkzeug/contrib/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/werkzeug/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/werkzeug/contrib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/werkzeug/debug/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/werkzeug/middleware/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/2and3/yaml/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/aiofiles/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/contextvars.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/aiofiles/threadpool/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/aiofiles/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/aiofiles/threadpool/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/docutils/parsers/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/docutils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/docutils/parsers/rst/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/docutils/parsers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/docutils/parsers/rst/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/filelock/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/freezegun/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/jwt/contrib/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/jwt/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/jwt/contrib/algorithms/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/jwt/contrib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/jwt/contrib/algorithms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/pkg_resources/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/pyrfc3339/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/six/moves/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/six/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/six/moves/urllib/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/six/moves/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/six/moves/urllib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/typed_ast/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi/third_party/typeshed/third_party/3/waitress/__init__.pyi'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi-0.19.2.dist-info/AUTHORS.txt'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi_language_server/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/jedi_language_server-0.42.0.dist-info/entry_points.txt'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/lsprotocol/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/lsprotocol-2023.0.1.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/parso/pgen2/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/parso/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/parso/pgen2/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/parso/python/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/parso-0.8.4.dist-info/AUTHORS.txt'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/pygls/lsp/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/pygls/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/pygls/lsp/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/pygls/protocol/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/pygls/workspace/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/pygls-1.3.1.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/jedilsp/typing_extensions-4.12.2.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/importlib_metadata/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/typing_extensions.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/importlib_metadata/compat/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/importlib_metadata/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/importlib_metadata/compat/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/importlib_metadata-8.5.0.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/microvenv/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/microvenv-2023.5.post1.dist-info/licenses/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/microvenv-2023.5.post1.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/microvenv-2023.5.post1.dist-info/licenses/LICENSE'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/packaging/licenses/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/packaging/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/packaging/licenses/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/packaging-24.2.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/tomli/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/tomli-2.2.1.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/typing_extensions-4.12.2.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/zipp/compat/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/zipp/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/zipp/compat/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/lib/python/zipp-3.20.2.dist-info/INSTALLER'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/testing_tools/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/unittestadapter/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/vscode_datascience_helpers/tests/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/vscode_datascience_helpers/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/vscode_datascience_helpers/tests/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/python_files/vscode_pytest/__init__.py'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/resources/dark/'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/resources/report_issue_template.md'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/resources/dark/debug.svg'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/resources/light/debug.svg'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/resources/walkthrough/create-environment.svg'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/schemas/conda-environment.json'

'vscode/exe/data/extensions/ms-python.python-2025.2.0-win32-x64/syntaxes/pip-requirements.tmLanguage.json'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/.vsixmanifest'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/browser.async.bundle.js'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/files/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/files/get_pytest_options.py'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/indices/stdlib.json'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/lxml/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/lxml/etree.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/numpy/core/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/numpy/core/_multiarray_umath.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/numpy/fft/_pocketfft_internal.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/numpy/linalg/_umath_linalg.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/numpy/random/_bounded_integers.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/_libs/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/_libs/tslibs/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/_libs/algos.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/_libs/tslibs/base.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/_libs/window/aggregations.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/io/sas/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/native-stubs/pandas/io/sas/_sas.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/apps/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/apps/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/conf/locale/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/conf/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/conf/locale/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/conf/urls/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/migrations/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/migrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admin/views/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/admindocs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/handlers/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/handlers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/management/commands/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/auth/migrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/management/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/management/commands/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/contenttypes/migrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/flatpages/migrations/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/flatpages/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/flatpages/migrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/flatpages/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/admin/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/admin/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/base/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/base/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/mysql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/oracle/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/postgis/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/backends/spatialite/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/models/sql/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/models/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/db/models/sql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/forms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/gdal/prototypes/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/gdal/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/gdal/prototypes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/gdal/raster/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/geoip2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/geos/prototypes/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/geos/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/geos/prototypes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/serializers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/sitemaps/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/gis/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/humanize/templatetags/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/humanize/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/humanize/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/messages/storage/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/messages/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/messages/storage/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/postgres/aggregates/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/postgres/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/postgres/aggregates/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/postgres/fields/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/redirects/migrations/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/redirects/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/redirects/migrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/backends/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/management/commands/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sessions/migrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sitemaps/management/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sitemaps/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sitemaps/management/commands/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sitemaps/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sitemaps/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sites/migrations/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sites/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/sites/migrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/management/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/management/commands/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/staticfiles/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/contrib/syndication/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/cache/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/cache/backends/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/cache/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/cache/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/checks/compatibility/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/checks/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/checks/compatibility/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/checks/security/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/files/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/handlers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/mail/backends/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/mail/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/mail/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/management/commands/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/serializers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/core/servers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/base/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/base/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/dummy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/mysql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/oracle/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/postgresql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/backends/sqlite3/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/migrations/operations/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/migrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/migrations/operations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/models/fields/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/models/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/models/fields/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/models/functions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/db/models/sql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/dispatch/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/forms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/http/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/middleware/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/template/backends/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/template/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/template/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/template/loaders/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/test/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/urls/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/utils/translation/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/utils/translation/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/views/decorators/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/views/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/views/decorators/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/django-stubs/views/generic/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/core/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/core/magics/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/core/magics/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/extensions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/external/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/lib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/sphinxext/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/terminal/pt_inputhooks/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/terminal/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/terminal/pt_inputhooks/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/terminal/shortcuts/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/testing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/IPython-stubs/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/_api/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/_api/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/axes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/cbook/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/projections/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/style/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/testing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/matplotlib/tri/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/approximation/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/approximation/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/assortativity/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/bipartite/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/centrality/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/coloring/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/community/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/components/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/connectivity/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/flow/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/isomorphism/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/link_analysis/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/minors/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/node_classification/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/operators/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/shortest_paths/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/traversal/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/algorithms/tree/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/classes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/drawing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/generators/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/linalg/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/readwrite/json_graph/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/readwrite/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/readwrite/json_graph/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/testing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/networkx/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_config/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_config/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_libs/tslibs/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_libs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_libs/tslibs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_libs/window/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/_testing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/api/extensions/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/api/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/api/extensions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/api/indexers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/api/interchange/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/api/types/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/api/typing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/arrays/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/arrays/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/arrays/arrow/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/arrays/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/arrays/arrow/dtype.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/arrays/sparse/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/computation/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/dtypes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/groupby/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/indexes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/interchange/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/ops/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/reshape/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/sparse/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/tools/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/util/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/core/window/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/errors/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/clipboard/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/clipboard/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/excel/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/formats/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/json/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/parsers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/io/sas/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/plotting/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/tseries/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/util/version/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/util/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/pandas/util/version/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/_shared/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/_shared/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/data/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/draw/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/feature/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/future/graph/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/future/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/future/graph/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/measure/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/morphology/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/scripts/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/skimage/transform/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/__check_build/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/__check_build/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/_build_utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/_loss/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/cluster/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/compose/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/covariance/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/cross_decomposition/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/datasets/data/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/datasets/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/datasets/data/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/datasets/descr/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/datasets/images/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/decomposition/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/ensemble/_hist_gradient_boosting/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/ensemble/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/ensemble/_hist_gradient_boosting/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/experimental/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/externals/_packaging/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/externals/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/externals/_packaging/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/feature_extraction/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/feature_selection/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/gaussian_process/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/impute/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/inspection/_plot/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/inspection/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/inspection/_plot/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/linear_model/_glm/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/linear_model/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/linear_model/_glm/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/manifold/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/metrics/_pairwise_distances_reduction/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/metrics/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/metrics/_pairwise_distances_reduction/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/metrics/_plot/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/metrics/cluster/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/mixture/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/model_selection/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/neighbors/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/neural_network/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/preprocessing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/semi_supervised/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/svm/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/tests/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/tree/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sklearn/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/algebras/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/algebras/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/assumptions/handlers/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/assumptions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/assumptions/handlers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/assumptions/predicates/common.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/assumptions/relation/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/calculus/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/categories/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/codegen/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/combinatorics/coset_table.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/concrete/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/crypto/crypto.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/diffgeom/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/discrete/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/external/importtools.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/functions/combinatorial/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/functions/combinatorial/factorials.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/functions/elementary/complexes.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/functions/special/bessel.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/geometry/curve.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/holonomic/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/integrals/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/interactive/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/liealgebras/cartan_type.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/logic/algorithms/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/logic/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/logic/algorithms/dpll.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/matrices/expressions/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/matrices/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/matrices/expressions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/multipledispatch/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/ntheory/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/parsing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/control/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/control/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/units/definitions/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/units/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/physics/units/definitions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/plotting/intervalmath/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/plotting/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/plotting/intervalmath/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/plotting/pygletplot/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/polys/agca/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/polys/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/polys/agca/extensions.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/polys/domains/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/polys/matrices/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/polys/numberfields/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/printing/pretty/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/printing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/printing/pretty/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/series/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/sets/handlers/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/sets/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/sets/handlers/add.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/simplify/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/solvers/diophantine/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/solvers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/solvers/diophantine/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/solvers/ode/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/stats/sampling/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/stats/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/stats/sampling/sample_numpy.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/strategies/branch/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/strategies/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/strategies/branch/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/tensor/array/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/tensor/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/tensor/array/expressions/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/tensor/array/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/tensor/array/expressions/array_expressions.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/testing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/unify/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/utilities/mathml/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/utilities/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/utilities/mathml/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/sympy-stubs/vector/basisdependent.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/transformers-stubs/models/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/transformers-stubs/py.typed'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/transformers-stubs/models/auto/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/transformers-stubs/models/auto/auto_factory.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/app/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/app/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/color/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/ext/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/geometry/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/gloo/gl/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/gloo/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/gloo/gl/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/glsl/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/io/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/plot/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/scene/cameras/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/scene/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/scene/cameras/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/scene/widgets/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/testing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/util/dpi/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/util/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/util/dpi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/util/fonts/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/util/svg/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/collections/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/collections/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/filters/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/glsl/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/graphs/layouts/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/graphs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/graphs/layouts/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/line/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/shaders/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/text/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/stubs/vispy/visuals/transforms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/bundled/wasm/tree-sitter-rst.wasm'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/schemas/pyrightconfig.schema.json'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/stub-generation/README.md'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/commit.txt'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/_typeshed/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/__future__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/_typeshed/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/asyncio/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/collections/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/concurrent/futures/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/concurrent/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/concurrent/futures/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/ctypes/macholib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/ctypes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/ctypes/macholib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/curses/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/dbm/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/distutils/command/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/distutils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/distutils/command/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/email/mime/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/email/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/email/mime/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/encodings/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/ensurepip/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/html/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/http/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/importlib/metadata/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/importlib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/importlib/metadata/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/importlib/resources/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/json/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/lib2to3/fixes/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/lib2to3/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/lib2to3/fixes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/lib2to3/pgen2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/logging/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/msilib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/multiprocessing/dummy/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/multiprocessing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/multiprocessing/dummy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/os/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/pydoc_data/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/pyexpat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/sqlite3/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/sys/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/tkinter/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/unittest/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/urllib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/venv/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/wsgiref/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/dom/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/dom/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/etree/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/parsers/expat/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/parsers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/parsers/expat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xml/sax/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/xmlrpc/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/zipfile/_path/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/zipfile/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/zipfile/_path/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stdlib/zoneinfo/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/aiofiles/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/aiofiles/tempfile/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/aiofiles/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/aiofiles/tempfile/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aiofiles/aiofiles/threadpool/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/atn/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/atn/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/dfa/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/error/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/tree/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/antlr4-python3-runtime/antlr4/xpath/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/assertpy/assertpy/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/assertpy/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/assertpy/assertpy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/atheris/atheris/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/atheris/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/atheris/atheris/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/common/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/common/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/integrations/base_client/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/integrations/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/integrations/base_client/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/drafts/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/drafts/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/rfc7515/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/rfc7516/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/rfc7517/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/rfc7518/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/rfc7519/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/jose/rfc8037/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth1/rfc5849/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth1/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth1/rfc5849/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc6749/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc6749/grants/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc6749/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc6749/grants/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc6750/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc7009/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc7521/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc7523/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc7591/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc7592/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc7636/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc7662/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc8414/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc8628/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc8693/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oauth2/rfc9068/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/core/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/core/grants/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/core/grants/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Authlib/authlib/oidc/discovery/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/emitters/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/emitters/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/exceptions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/models/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/plugins/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/sampling/local/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/sampling/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/sampling/local/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/streaming/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/aws-xray-sdk/aws_xray_sdk/core/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/beautifulsoup4/bs4/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/beautifulsoup4/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/beautifulsoup4/bs4/builder/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/beautifulsoup4/bs4/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/beautifulsoup4/bs4/builder/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/bleach/bleach/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/bleach/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/bleach/bleach/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/boltons/boltons/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/boltons/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/boltons/boltons/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/dispute_details/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/dispute_details/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/exceptions/http/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/exceptions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/exceptions/http/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/graphql/enums/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/graphql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/graphql/enums/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/graphql/inputs/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/graphql/types/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/graphql/unions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/merchant_account/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/test/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/braintree/braintree/util/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cachetools/cachetools/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cachetools/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cachetools/cachetools/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/caldav/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/caldav/elements/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/caldav/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/caldav/elements/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/caldav/caldav/lib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/capturer/capturer.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cffi/cffi/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cffi/_cffi_backend.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/cffi/cffi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/chevron/chevron/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/chevron/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/chevron/chevron/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-default-group/click_default_group.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-log/click_log/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-log/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-log/click_log/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-spinner/click_spinner/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-spinner/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-spinner/click_spinner/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-web/click_web/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-web/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-web/click_web/resources/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-web/click_web/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/click-web/click_web/resources/cmd_exec.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/colorama/colorama/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/colorama/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/colorama/colorama/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/commonmark/commonmark/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/commonmark/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/commonmark/commonmark/render/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/commonmark/commonmark/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/commonmark/commonmark/render/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/format/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/format/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/items/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/console-menu/consolemenu/validators/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/sources/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/sources/taiga/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/sources/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/sources/taiga/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/corus/corus/third/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/croniter/croniter/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/croniter/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/croniter/croniter/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/calendars/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/calendars/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/custom_language_detection/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/data/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/languages/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/search/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dateparser/dateparser_data/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/decorator/decorator.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/defusedxml/defusedxml/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/defusedxml/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/defusedxml/defusedxml/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Deprecated/deprecated/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Deprecated/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Deprecated/deprecated/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/import_export/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/import_export/formats/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/import_export/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/import_export/formats/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/import_export/templatetags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/management/commands/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/management/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/django-import-export/management/commands/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/api/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/api/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/context/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/credentials/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/models/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/transport/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/types/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docker/docker/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dockerfile-parse/dockerfile_parse/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dockerfile-parse/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/dockerfile-parse/dockerfile_parse/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/languages/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/languages/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/parsers/rst/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/parsers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/parsers/rst/directives/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/parsers/rst/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/parsers/rst/directives/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/readers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/transforms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/docutils/docutils/writers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/editdistance/editdistance/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/editdistance/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/editdistance/editdistance/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/entrypoints/entrypoints.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/tags/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/tags/makernote/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/tags/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ExifRead/exifread/tags/makernote/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fanstatic/fanstatic/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fanstatic/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fanstatic/fanstatic/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/first/first.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/api/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/api/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/formatting/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/main/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/options/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8/flake8/plugins/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-bugbear/bugbear.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-builtins/flake8_builtins.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-docstrings/flake8_docstrings.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-rst-docstrings/flake8_rst_docstrings.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-simplify/flake8_simplify/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-simplify/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-simplify/flake8_simplify/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/flake8-typing-imports/flake8_typing_imports.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Cors/flask_cors/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Cors/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Cors/flask_cors/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Migrate/flask_migrate/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Migrate/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-Migrate/flask_migrate/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-SocketIO/flask_socketio/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-SocketIO/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Flask-SocketIO/flask_socketio/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fpdf2/fpdf/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fpdf2/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/fpdf2/fpdf/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gdb/gdb/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gdb/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gdb/gdb/dap/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gdb/gdb/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gdb/gdb/dap/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/geopandas/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/geopandas/io/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/geopandas/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/geopandas/io/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/geopandas/geopandas/tools/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/_ffi/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/_ffi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/libev/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/libuv/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/monkey/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/gevent/gevent/resolver/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/cloud/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/cloud/ndb/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/google-cloud-ndb/google/cloud/ndb/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/greenlet/greenlet/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/greenlet/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/greenlet/greenlet/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hdbcli/hdbcli/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hdbcli/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hdbcli/hdbcli/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hnswlib/hnswlib.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/_trie/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/_trie/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/filters/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/treeadapters/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/treebuilders/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/html5lib/html5lib/treewalkers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/httplib2/httplib2/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/httplib2/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/httplib2/httplib2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/humanfriendly/humanfriendly/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/humanfriendly/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/humanfriendly/humanfriendly/terminal/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/humanfriendly/humanfriendly/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/humanfriendly/humanfriendly/terminal/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/auth_methods/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/auth_methods/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/secrets_engines/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/api/system_backend/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/constants/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/hvac/hvac/v1/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ibm-db/ibm_db.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/icalendar/icalendar/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/icalendar/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/icalendar/icalendar/timezone/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/icalendar/icalendar/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/icalendar/icalendar/timezone/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/_async/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/_async/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/_sync/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/client/util/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/client/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/client/util/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/client/write/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/domain/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/influxdb-client/influxdb_client/service/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/inifile/inifile.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/JACK-Client/jack/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/JACK-Client/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/JACK-Client/jack/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Jetson.GPIO/Jetson/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Jetson.GPIO/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Jetson.GPIO/Jetson/GPIO/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Jetson.GPIO/Jetson/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Jetson.GPIO/Jetson/GPIO/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jmespath/jmespath/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jmespath/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jmespath/jmespath/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jsonschema/jsonschema/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jsonschema/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jsonschema/jsonschema/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jwcrypto/jwcrypto/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jwcrypto/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/jwcrypto/jwcrypto/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/keyboard/keyboard/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/keyboard/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/keyboard/keyboard/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/abstract/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/abstract/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/extend/microsoft/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/extend/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/extend/microsoft/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/extend/novell/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/extend/standard/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/operation/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/protocol/formatters/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/protocol/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/protocol/formatters/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/protocol/sasl/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/protocol/schemas/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/strategy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ldap3/ldap3/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/libsass/sassutils/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/libsass/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/libsass/sassutils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lupa/lupa/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lupa/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lupa/lupa/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lzstring/lzstring/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lzstring/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/lzstring/lzstring/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/m3u8/m3u8/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/m3u8/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/m3u8/m3u8/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Markdown/markdown/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Markdown/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Markdown/markdown/extensions/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Markdown/markdown/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Markdown/markdown/extensions/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mock/mock/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mock/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mock/mock/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mypy-extensions/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mysqlclient/MySQLdb/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mysqlclient/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mysqlclient/MySQLdb/constants/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mysqlclient/MySQLdb/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/mysqlclient/MySQLdb/constants/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoid/nanoid/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoid/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoid/nanoid/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoleafapi/nanoleafapi/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoleafapi/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/nanoleafapi/nanoleafapi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/contrib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/contrib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/eui/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/ip/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netaddr/netaddr/strategy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/netifaces/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/approximation/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/approximation/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/assortativity/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/bipartite/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/centrality/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/coloring/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/community/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/components/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/connectivity/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/flow/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/isomorphism/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/link_analysis/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/minors/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/operators/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/shortest_paths/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/traversal/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/algorithms/tree/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/classes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/drawing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/generators/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/linalg/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/readwrite/json_graph/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/readwrite/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/readwrite/json_graph/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/networkx/networkx/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/rfc5849/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/rfc5849/endpoints/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/rfc5849/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth1/rfc5849/endpoints/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/clients/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/clients/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/endpoints/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/oauth2/rfc6749/grant_types/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/core/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/core/endpoints/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/core/endpoints/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/oauthlib/oauthlib/openid/connect/core/grant_types/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/objgraph/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/olefile/olefile/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/olefile/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/olefile/olefile/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/cell/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/cell/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/chart/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/chartsheet/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/comments/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/compat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/descriptors/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/drawing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/formatting/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/formula/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/packaging/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/pivot/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/reader/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/styles/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/workbook/external_link/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/workbook/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/workbook/external_link/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/worksheet/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/writer/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/openpyxl/openpyxl/xml/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/ext/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/ext/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/harness/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/mocktracer/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/opentracing/opentracing/scope_managers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/paramiko/paramiko/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/paramiko/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/paramiko/paramiko/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/parsimonious/parsimonious/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/parsimonious/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/parsimonious/parsimonious/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/crypto/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/crypto/_blowfish/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/crypto/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/crypto/_blowfish/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/crypto/scrypt/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/ext/django/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/ext/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/ext/django/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/handlers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/utils/compat/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passlib/passlib/utils/compat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passpy/passpy/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passpy/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/passpy/passpy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/peewee/playhouse/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/peewee/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/peewee/playhouse/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pep8-naming/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pexpect/pexpect/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pexpect/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pexpect/pexpect/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/adapters/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/adapters/utils/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/adapters/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pika/pika/adapters/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/polib/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/portpicker/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/compiler/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/compiler/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/internal/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/protobuf/google/protobuf/util/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psutil/psutil/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psutil/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psutil/psutil/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psycopg2/psycopg2/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psycopg2/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/psycopg2/psycopg2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/ber/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/ber/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/cer/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/der/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/codec/native/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/compat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyasn1/pyasn1/type/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyaudio/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyAutoGUI/pyautogui/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyAutoGUI/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyAutoGUI/pyautogui/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pycocotools/pycocotools/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pycocotools/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pycocotools/pycocotools/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pycurl/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyfarmhash/farmhash.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyflakes/pyflakes/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyflakes/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyflakes/pyflakes/scripts/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyflakes/pyflakes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyflakes/pyflakes/scripts/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pygit2/pygit2/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pygit2/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pygit2/pygit2/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/filters/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/filters/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/formatters/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/lexers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Pygments/pygments/styles/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/pyi_splash/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/pyi_splash/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/building/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/building/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/depend/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/isolated/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/lib/modulegraph/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/lib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/lib/modulegraph/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/utils/hooks/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/utils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/utils/hooks/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyinstaller/PyInstaller/utils/win32/versioninfo.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyjks/jks/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyjks/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyjks/jks/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyMySQL/pymysql/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyMySQL/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyMySQL/pymysql/constants/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyMySQL/pymysql/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyMySQL/pymysql/constants/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/pynput/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/pynput/keyboard/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/pynput/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/pynput/keyboard/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pynput/pynput/mouse/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyperclip/pyperclip/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyperclip/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyperclip/pyperclip/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyRFC3339/pyrfc3339/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyRFC3339/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyRFC3339/pyrfc3339/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyScreeze/pyscreeze/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyScreeze/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyScreeze/pyscreeze/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/threaded/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/threaded/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/tools/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyserial/serial/urlhandler/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pysftp/pysftp/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pysftp/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pysftp/pysftp/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pytest-lazy-fixture/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-crontab/cronlog.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-datemath/datemath/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-datemath/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-datemath/datemath/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/parser/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/parser/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/tz/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-dateutil/dateutil/zoneinfo/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-http-client/python_http_client/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-http-client/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-http-client/python_http_client/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jenkins/jenkins/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jenkins/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jenkins/jenkins/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jose/jose/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jose/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jose/jose/backends/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jose/jose/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-jose/jose/backends/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-nmap/nmap/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-nmap/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-nmap/nmap/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/ext/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/ext/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/keysymdef/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/protocol/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/support/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/python-xlib/Xlib/xobject/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pytz/pytz/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pytz/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pytz/pytz/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/isapi/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/_win32typing.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/isapi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/pythonwin/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32/lib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32/lib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/adsi/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/adsi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/authorization/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/axcontrol/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/axdebug/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/axscript/client/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/axscript/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/axscript/client/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/axscript/server/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/bits/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/client/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/directsound/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/gen_py/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/ifilter/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/internet/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/mapi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/propsys/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/server/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/shell/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32com/taskscheduler/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/adsi/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/adsi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/authorization/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/axcontrol/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/axdebug/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/axscript/client/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/axscript/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/axscript/client/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/axscript/server/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/bits/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/directsound/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/ifilter/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/internet/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/mapi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/propsys/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/shell/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pywin32/win32comext/taskscheduler/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyxdg/xdg/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyxdg/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/pyxdg/xdg/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyYAML/yaml/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyYAML/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/PyYAML/yaml/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrbill/qrbill/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrbill/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrbill/qrbill/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/styles/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/styles/moduledrawers/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/styles/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/qrcode/qrcode/image/styles/moduledrawers/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/regex/regex/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/regex/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/regex/regex/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/barcode/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/barcode/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/charts/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/samples/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/graphics/widgets/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/lib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/pdfbase/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/pdfgen/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/reportlab/reportlab/platypus/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests/requests/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests/requests/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests-oauthlib/requests_oauthlib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests-oauthlib/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests-oauthlib/requests_oauthlib/compliance_fixes/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests-oauthlib/requests_oauthlib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/requests-oauthlib/requests_oauthlib/compliance_fixes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/retry/retry/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/retry/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/retry/retry/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/RPi.GPIO/RPi/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/RPi.GPIO/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/RPi.GPIO/RPi/GPIO/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/RPi.GPIO/RPi/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/RPi.GPIO/RPi/GPIO/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/s2clientprotocol/s2clientprotocol/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/s2clientprotocol/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/s2clientprotocol/s2clientprotocol/build.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/_core/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/_core/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/_marks/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/_stats/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/colors/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/seaborn/seaborn/external/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Send2Trash/send2trash/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Send2Trash/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/Send2Trash/send2trash/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/distutils/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/distutils/command/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/distutils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/distutils/command/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/distutils/compat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/pkg_resources/_vendored_packaging/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/pkg_resources/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/pkg_resources/_vendored_packaging/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/_distutils/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/_distutils/command/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/_distutils/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/_distutils/command/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/_distutils/compat/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/command/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/setuptools/setuptools/config/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/algorithms/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/algorithms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/geometry/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/shapely/shapely/vectorized/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/simplejson/simplejson/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/simplejson/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/simplejson/simplejson/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/singledispatch/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/moves/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/moves/urllib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/moves/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/six/six/moves/urllib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/slumber/slumber/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/slumber/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/slumber/slumber/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/str2bool/str2bool/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/str2bool/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/str2bool/str2bool/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tabulate/tabulate/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tabulate/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tabulate/tabulate/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/TgCrypto/tgcrypto/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/TgCrypto/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/TgCrypto/tgcrypto/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/toml/toml/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/toml/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/toml/toml/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/toposort/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tqdm/tqdm/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tqdm/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tqdm/tqdm/contrib/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tqdm/tqdm/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tqdm/tqdm/contrib/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/translationstring/translationstring/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/translationstring/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/translationstring/translationstring/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tree-sitter-languages/tree_sitter_languages/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tree-sitter-languages/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/tree-sitter-languages/tree_sitter_languages/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ttkthemes/ttkthemes/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ttkthemes/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ttkthemes/ttkthemes/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/ujson/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/unidiff/unidiff/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/unidiff/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/unidiff/unidiff/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/untangle/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/usersettings/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/uWSGI/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/vobject/@tests/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/vobject/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/vobject/@tests/stubtest_allowlist.txt'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/vobject/vobject/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/waitress/waitress/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/waitress/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/waitress/waitress/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WebOb/webob/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WebOb/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WebOb/webob/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/whatthepatch/whatthepatch/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/whatthepatch/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/whatthepatch/whatthepatch/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/africa/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/africa/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/america/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/asia/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/europe/scotland/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/europe/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/europe/scotland/mixins/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/europe/scotland/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/europe/scotland/mixins/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/oceania/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/workalendar/workalendar/usa/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/csrf/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/csrf/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/fields/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/WTForms/wtforms/widgets/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/wurlitzer/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/xdgenvpy/xdgenvpy/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/xdgenvpy/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/xdgenvpy/xdgenvpy/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/xmltodict/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/zstd/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/zxcvbn/zxcvbn/'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/zxcvbn/METADATA.toml'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/dist/typeshed-fallback/stubs/zxcvbn/zxcvbn/__init__.pyi'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/images/icon.png'

'vscode/exe/data/extensions/ms-python.vscode-pylance-2025.3.2/typings/string.d.ts'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/dist/'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/.vsixmanifest'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/dist/browser/'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/dist/browser/extension.js'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/dist/node/460.js'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/l10n/bundle.l10n.cs.json'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/resources/walkthrough/'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/resources/remote-wsl-nightly.png'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/resources/walkthrough/create-project.md'

'vscode/exe/data/extensions/ms-vscode-remote.remote-wsl-0.88.5/scripts/wslCode.sh'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/.changeset/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/.dockerignore'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/.changeset/changelog-config.js'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/assets/docs/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/assets/docs/demo.gif'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/assets/icons/icon.png'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/assets/images/openrouter.png'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/audio/celebration.wav'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/extension.js'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/ca/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/ca/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/de/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/en/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/es/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/fr/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/hi/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/it/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/ja/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/ko/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/pl/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/pt-BR/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/tr/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/vi/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/zh-CN/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/dist/i18n/locales/zh-TW/common.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/codicons/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/codicons/dist/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/node_modules/@vscode/codicons/dist/codicon.css'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/scripts/find-missing-i18n-key.js'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/src/integrations/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/src/integrations/theme/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/src/integrations/theme/default-themes/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/src/integrations/theme/default-themes/dark_modern.json'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/webview-ui/.storybook/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/webview-ui/.npmrc'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/webview-ui/.storybook/vscode.css'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/webview-ui/build/assets/'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/webview-ui/build/.gitkeep'

'vscode/exe/data/extensions/rooveterinaryinc.roo-cline-3.11.3/webview-ui/build/assets/_basePickBy.js'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.changeset/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.changie.yaml'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.changeset/config.json'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.github/ISSUE_TEMPLATE/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.github/CODEOWNERS'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.github/ISSUE_TEMPLATE/bug_report.yml'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.github/scripts/overwrite_changeset_changelog.py'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.github/workflows/changeset-converter.yml'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/.husky/pre-commit'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/assets/docs/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/assets/docs/demo.gif'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/assets/icons/icon.png'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/dist/extension.js'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/ar-sa/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/ar-sa/CODE_OF_CONDUCT.md'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/de/CODE_OF_CONDUCT.md'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/es/CODE_OF_CONDUCT.md'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/ja/CODE_OF_CONDUCT.md'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/ko/CODE_OF_CONDUCT.md'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/pt-BR/CODE_OF_CONDUCT.md'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/zh-cn/CODE_OF_CONDUCT.md'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/locales/zh-tw/CODE_OF_CONDUCT.md'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/codicons/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/codicons/dist/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/node_modules/@vscode/codicons/dist/codicon.css'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/scripts/test-ci.js'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/src/integrations/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/src/integrations/theme/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/src/integrations/theme/default-themes/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/src/integrations/theme/default-themes/dark_modern.json'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/webview-ui/build/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/webview-ui/eslint.config.js'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/webview-ui/build/assets/'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/webview-ui/build/index.html'

'vscode/exe/data/extensions/saoudrizwan.claude-dev-3.8.5/webview-ui/build/assets/codicon.ttf'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/.github/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/.vsixmanifest'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/.github/ISSUE_TEMPLATE/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/.github/FUNDING.yml'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/.github/ISSUE_TEMPLATE/bug_report.md'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/.github/workflows/release.yml'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/electron-webview/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/README.md'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/electron-webview/preload.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/font-awesome/css/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/font-awesome/css/all.min.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/font-awesome/webfonts/fa-brands-400.ttf'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/fonts/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/katex.min.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/fonts/KaTeX_AMS-Regular.ttf'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/mermaid/mermaid.min.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/remarkable/remarkable.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/css/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/css/print/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/css/reset.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/css/print/paper.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/css/theme/beige.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/js/reveal.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/css/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/css/zenburn.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/font/league-gothic/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/font/league-gothic/league-gothic.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/font/source-sans-pro/LICENSE'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/lib/js/classList.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/multiplex/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/multiplex/client.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/notes/notes.html'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/notes-server/client.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/print-pdf/print-pdf.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/search/plugin.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/zoom/plugin.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/reveal/plugin/zoom-js/zoom.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/vega/vega.min.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/vega-embed/vega-embed.min.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/vega-lite/vega-lite.min.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/wavedrom/skins/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/wavedrom/wavedrom.min.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/wavedrom/skins/default.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/styles/preview_theme/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/styles/markdown-it-admonition.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/styles/preview_theme/atom-dark.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/styles/prism_theme/atom-dark.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/webview/backlinks.css'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/media/mpe.png'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/out/native/'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/out/native/extension.js'

'vscode/exe/data/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/out/web/extension.js'

'vscode/exe/data/user-data/Backups/'

'vscode/exe/data/user-data/languagepacks.json'

'vscode/exe/data/user-data/blob_storage/e98145f6-de79-4362-a698-318916381ab6/'

'vscode/exe/data/user-data/Cache/Cache_Data/'

'vscode/exe/data/user-data/Cache/Cache_Data/data_0'

'vscode/exe/data/user-data/CachedData/ddc367ed5c8936efe395cffeec279b04ffd7db78/'

'vscode/exe/data/user-data/CachedData/ddc367ed5c8936efe395cffeec279b04ffd7db78/chrome/'

'vscode/exe/data/user-data/CachedData/ddc367ed5c8936efe395cffeec279b04ffd7db78/chrome/js/'

'vscode/exe/data/user-data/CachedData/ddc367ed5c8936efe395cffeec279b04ffd7db78/chrome/js/index-dir/'

'vscode/exe/data/user-data/CachedData/ddc367ed5c8936efe395cffeec279b04ffd7db78/chrome/js/3fc548982e503c7b_0'

'vscode/exe/data/user-data/CachedData/ddc367ed5c8936efe395cffeec279b04ffd7db78/chrome/js/index-dir/the-real-index'

'vscode/exe/data/user-data/CachedData/ddc367ed5c8936efe395cffeec279b04ffd7db78/chrome/wasm/index-dir/'

'vscode/exe/data/user-data/CachedData/ddc367ed5c8936efe395cffeec279b04ffd7db78/chrome/wasm/index'

'vscode/exe/data/user-data/CachedData/ddc367ed5c8936efe395cffeec279b04ffd7db78/chrome/wasm/index-dir/the-real-index'

'vscode/exe/data/user-data/CachedExtensionVSIXs/.trash/'

'vscode/exe/data/user-data/CachedExtensionVSIXs/ms-python.debugpy-2025.4.1-win32-x64'

'vscode/exe/data/user-data/CachedExtensionVSIXs/.trash/ms-python.debugpy-2025.4.1-win32-x64.sigzip'

'vscode/exe/data/user-data/CachedProfilesData/__default__profile__/'

'vscode/exe/data/user-data/CachedProfilesData/__default__profile__/extensions.builtin.cache'

'vscode/exe/data/user-data/Code Cache/js/'

'vscode/exe/data/user-data/Code Cache/js/index-dir/'

'vscode/exe/data/user-data/Code Cache/js/index'

'vscode/exe/data/user-data/Code Cache/js/index-dir/the-real-index'

'vscode/exe/data/user-data/Code Cache/wasm/index-dir/'

'vscode/exe/data/user-data/Code Cache/wasm/index'

'vscode/exe/data/user-data/Code Cache/wasm/index-dir/the-real-index'

'vscode/exe/data/user-data/Crashpad/attachments/'

'vscode/exe/data/user-data/Crashpad/metadata'

'vscode/exe/data/user-data/DawnGraphiteCache/data_0'

'vscode/exe/data/user-data/DawnWebGPUCache/data_0'

'vscode/exe/data/user-data/GPUCache/data_0'

'vscode/exe/data/user-data/Local Storage/leveldb/'

'vscode/exe/data/user-data/Local Storage/leveldb/000003.log'

'vscode/exe/data/user-data/logs/20250401T212213/'

'vscode/exe/data/user-data/logs/20250401T212213/window1/'

'vscode/exe/data/user-data/logs/20250401T212213/editSessions.log'

'vscode/exe/data/user-data/logs/20250401T212213/window1/exthost/'

'vscode/exe/data/user-data/logs/20250401T212213/window1/network.log'

'vscode/exe/data/user-data/logs/20250401T212213/window1/exthost/ms-python.python/'

'vscode/exe/data/user-data/logs/20250401T212213/window1/exthost/exthost.log'

'vscode/exe/data/user-data/logs/20250401T212213/window1/exthost/ms-python.python/Python Language Server.log'

'vscode/exe/data/user-data/logs/20250401T212213/window1/exthost/output_logging_20250401T212216/1-Roo-Code.log'

'vscode/exe/data/user-data/logs/20250401T212213/window1/exthost/vscode.git/Git.log'

'vscode/exe/data/user-data/logs/20250401T212213/window1/exthost/vscode.github/GitHub.log'

'vscode/exe/data/user-data/logs/20250401T212213/window1/exthost/vscode.github-authentication/GitHub Authentication.log'

'vscode/exe/data/user-data/logs/20250401T212213/window1/output_20250401T212215/tasks.log'

'vscode/exe/data/user-data/Network/Cookies'

'vscode/exe/data/user-data/Service Worker/Database/'

'vscode/exe/data/user-data/Service Worker/Database/000003.log'

'vscode/exe/data/user-data/Service Worker/ScriptCache/index-dir/'

'vscode/exe/data/user-data/Service Worker/ScriptCache/2cc80dabc69f58b6_0'

'vscode/exe/data/user-data/Service Worker/ScriptCache/index-dir/the-real-index'

'vscode/exe/data/user-data/Session Storage/000003.log'

'vscode/exe/data/user-data/Shared Dictionary/cache/'

'vscode/exe/data/user-data/Shared Dictionary/db'

'vscode/exe/data/user-data/Shared Dictionary/cache/index-dir/'

'vscode/exe/data/user-data/Shared Dictionary/cache/index'

'vscode/exe/data/user-data/Shared Dictionary/cache/index-dir/the-real-index'

'vscode/exe/data/user-data/User/globalStorage/'

'vscode/exe/data/user-data/User/keybindings.json'

'vscode/exe/data/user-data/User/globalStorage/rooveterinaryinc.roo-cline/'

'vscode/exe/data/user-data/User/globalStorage/state.vscdb'

'vscode/exe/data/user-data/User/globalStorage/rooveterinaryinc.roo-cline/cache/'

'vscode/exe/data/user-data/User/globalStorage/rooveterinaryinc.roo-cline/cache/glama_models.json'

'vscode/exe/data/user-data/User/globalStorage/rooveterinaryinc.roo-cline/settings/custom_modes.json'

'vscode/exe/data/user-data/User/globalStorage/saoudrizwan.claude-dev/cache/'

'vscode/exe/data/user-data/User/globalStorage/saoudrizwan.claude-dev/cache/openrouter_models.json'

'vscode/exe/data/user-data/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json'

'vscode/exe/data/user-data/User/workspaceStorage/1743535334455/'

'vscode/exe/data/user-data/User/workspaceStorage/1743535334455/state.vscdb'

'vscode/exe/data/user-data/WebStorage/1/'

'vscode/exe/data/user-data/WebStorage/QuotaManager'

'vscode/exe/data/user-data/WebStorage/1/CacheStorage/'

'vscode/exe/data/user-data/WebStorage/1/CacheStorage/0422043a-f99a-41bc-8b83-fffae76a94e4/'

'vscode/exe/data/user-data/WebStorage/1/CacheStorage/index.txt'

'vscode/exe/data/user-data/WebStorage/1/CacheStorage/0422043a-f99a-41bc-8b83-fffae76a94e4/index-dir/'

'vscode/exe/data/user-data/WebStorage/1/CacheStorage/0422043a-f99a-41bc-8b83-fffae76a94e4/1541e8f43c6d42a2_0'

'vscode/exe/data/user-data/WebStorage/1/CacheStorage/0422043a-f99a-41bc-8b83-fffae76a94e4/index-dir/the-real-index'

'vscode/exe/data/user-data/WebStorage/1/IndexedDB/indexeddb.leveldb/'

'vscode/exe/data/user-data/WebStorage/1/IndexedDB/indexeddb.leveldb/000004.log'

'vscode/exe/data/user-data/WebStorage/2/CacheStorage/'

'vscode/exe/data/user-data/WebStorage/2/CacheStorage/ffa99c5e-2f62-4af7-8239-aa1d50580a13/'

'vscode/exe/data/user-data/WebStorage/2/CacheStorage/index.txt'

'vscode/exe/data/user-data/WebStorage/2/CacheStorage/ffa99c5e-2f62-4af7-8239-aa1d50580a13/index-dir/'

'vscode/exe/data/user-data/WebStorage/2/CacheStorage/ffa99c5e-2f62-4af7-8239-aa1d50580a13/0c2188cb98928538_0'

'vscode/exe/data/user-data/WebStorage/2/CacheStorage/ffa99c5e-2f62-4af7-8239-aa1d50580a13/index-dir/the-real-index'

## Message 5

**Mapping key exclusions**

Crafting a concise .gitignore file, I’m including typical ephemeral or generated files like node_modules, dist, build, and various log, cache, and temp directories.

**Identifying exclusions**

I’m noting the inclusion of ephemeral directories like dist, build, and node_modules, along with various log, cache, and temp directories, to ensure a concise .gitignore file.

**Focusing on key inclusions**

I'm listing ephemeral system artifacts like node_modules, build files, logs, and caches, while noting the minimal and essential exclusions for a streamlined .gitignore file.

## Message 6

Here’s a **short, generalized** `.gitignore` that captures the usual categories of generated or user-specific files you might see in a portable VS Code directory. Adapt or refine as needed:

```gitignore
# Build artifacts and distribution bundles
dist/
build/
out/
bundled/

# Common ephemeral folders
node_modules/
.cache/
logs/
.history/
tmp/
coverage/
env/
venv/
__pycache__/

# Typical system or temp files
*.log
*.tmp
*.lock
*.pyc
.DS_Store

# Dev configuration or meta
.husky/
.changeset/
.github/
.vscode/
.dockerignore
.flake8
*.env

# VSCode user data, backups, and caches
**/user-data/
**/Backups/
**/Service\ Worker/
**/blob_storage/
**/GPUCache/
**/Crashpad/
**/Code\ Cache/
**/Local\ Storage/
**/IndexedDB/
**/Network/
**/Shared\ Dictionary/
**/workspaceStorage/
```

> **Tip:** Place this at the **root of your project** (or wherever your Git repo starts) so it will **ignore** these patterns across the entire portable VS Code hierarchy. Adjust folder names/patterns to match your exact layout or tooling needs.

## Message 7

please consolidate this version:



# Concise .gitignore for vscode/exe/data/extensions/ (Portable VSCode)

# Place this file in vscode/exe/data/



# Core Dependencies (Very common & large)

**/node_modules/

**/bundled/

**/libs/           # Common name for libraries

**/lib/            # Often contains bundled Python libs (e.g., under python_files/)

**/_vendored/

**/vendor/



# Build & Distribution Output

**/dist/

**/out/

**/build/



# Python Specific Cache/Compiled

**/__pycache__/

**/*.pyc

**/*.pyo

**/*.pyd



# General Caches & Logs

**/*Cache/

**/CachedData/

**/logs/

**/*.log



# Development/Repo Artifacts (Not needed for installed state)

**/.vscode/

**/.github/

**/.git/          # Just in case an extension includes its history



# Metadata (Often regenerated or install-specific)

**/.vsixmanifest

**/*.dist-info/



# OS Specific

**/.DS_Store

**/Thumbs.db





# General ignore for extension caches, history, and generated data

**/.changeset/

**/dist/

**/out/

**/node_modules/

**/.github/

**/.config/

**/.specstory/

**/bundled/

**/resources/

**/themes/

**/webview-ui/build/



# Ignore Python specific compiled files and environments

**/__pycache__/

**/*.pyc

**/*.pyo

**/*.pyd

**/python-env-tools/bin/



# Ignore temporary, testing, and example data

**/tests/

**/@tests/



# Ignore locale and documentation assets

**/locales/

**/assets/docs/



# Ignore specific large bundled resources (libs, vendored dependencies)

**/libs/

**/_vendored/

**/third_party/

**/typeshed-fallback/

**/native-stubs/



# Ignore additional extension-specific directories

**/python_files/.vscode/

**/python_files/deactivate/

**/python_files/lib/

**/python_files/vscode_datascience_helpers/



# Specific ignore patterns for common extensions

ms-python.python-*/python_files/

ms-python.debugpy-*/bundled/

ms-python.vscode-pylance-*/dist/

ms-vscode-remote.remote-wsl-*/dist/

shd101wyy.markdown-preview-enhanced-*/crossnote/dependencies/



# --- Optional Additions (Uncomment if needed) ---

# Add these if you find specific patterns causing noise or large diffs.



# Type Stubs (Can be large)

# extensions/**/typeshed-fallback/

# extensions/**/native-stubs/

# extensions/**/*-stubs/

# extensions/**/*.pyi



# Localization / Assets / Themes (Ignore if defaults are fine)

# extensions/**/l10n/

# extensions/**/locales/

# extensions/**/i18n/

# extensions/**/resources/  # CAUTION: Might contain essential UI elements

# extensions/**/assets/     # CAUTION: Might contain essential UI elements

# extensions/**/themes/



# Other Specific Tooling

# extensions/**/.changeset/

# extensions/**/.config/    # Can sometimes hold important config, but often cache

# extensions/**/tests/

# extensions/**/@tests/

# extensions/**/.env

## Message 8

**Sifting through lines**

I’m noting the repeated and partial duplicate lines in .gitignore. Carefully merging all records into a simpler set will streamline this process.

**Merging patterns**

I'm focusing on unifying repeated .gitignore patterns while keeping them relevant. Ensuring the final list is organized, consistent, and streamlined for easy updates and
