# Modular PDF Design

Conversation ID: 685d127c-88d0-8008-aaf4-13d1a2025f06

## Message 1

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-Fg5NmU2cuG4YjasCvzifb1', 'size_bytes': 136543, 'width': 869, 'height': 1238, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-66g8bpciUu2yoBgva22CTc', 'size_bytes': 130872, 'width': 885, 'height': 924, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
What would be a cleaner way to programmatically design this pdf such that i can control the design of the pdf through as few chronologically ordered "groups" of settings as possible? Instead of having to scroll (up/down and) through different settings in different files (or linenumber) to change the design, it would be better if we classified each section by "type" first (see attached image) and then designed the controllability in a hierarchical and logically ordered fashion (that corresponds with our classifications). This would make it possible to craft the design of the pdf in a "section-by-section" incremental manner. As an example the header should be controlled in a separate "group" as the content below it, same goes for the signing at the bottom. And for the content the same principles should be used, think of each section as a "group". Here's my current design (only for context), your goal is to enhance the *prompt* itself:





    import React from 'react';

    import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';

    import { ContractFormData } from '@/lib/meta/types';



    interface ContractData {

      companyName: string;

      companyOrgNr: string;

      companyAddress: string;

      employeeName: string;

      employeeBirthDate: string;

      employeeAddress: string;

      position: string;

      positionDescription: string;

      startDate: string;

      employmentType: string;

      probationPeriod: string;

      workHours: string;

      breakTime: string;

      hourlyRate: string;

      overtimeRate: string;

      paymentDate: string;

      travelAllowance: string;

      vacationDays: string;

      vacationPay: string;

      sickPay: string;

      noticePeriod: string;

      terminationRules: string;

      pensionProvider: string;

      workInsurance: string;

      tariffAgreement: string;

      competenceDevelopment: string;

      legalReference: string;

      contractDate: string;

    }



    interface ContractPDFProps {

      formData: ContractFormData;

    }



    const styles = StyleSheet.create({

      page: {

        flexDirection: 'column',

        backgroundColor: '#ffffff',

        padding: 30,

        fontSize: 11,

        lineHeight: 1.4,

      },

      header: {

        textAlign: 'center',

        marginBottom: 24,

        paddingBottom: 16,

        borderBottomWidth: 2,

        borderBottomColor: '#059669',

      },

      companyName: {

        fontSize: 18,

        fontWeight: 'bold',

        color: '#059669',

        marginBottom: 5,

      },

      title: {

        fontSize: 16,

        fontWeight: 'bold',

        color: '#374151',

        marginTop: 15,

      },

      section: {

        marginBottom: 16,

        breakInside: 'avoid',

      },

      sectionTitle: {

        fontSize: 13,

        fontWeight: 'bold',

        color: '#059669',

        marginBottom: 10,

        paddingBottom: 3,

        borderBottomWidth: 1,

        borderBottomColor: '#d1d5db',

        breakAfter: 'avoid',

      },

      row: {

        flexDirection: 'row',

        justifyContent: 'space-between',

        marginBottom: 15,

        breakInside: 'avoid',

      },

      column: {

        flex: 1,

        paddingRight: 20,

      },

      label: {

        fontWeight: 'bold',

        marginBottom: 2,

      },

      text: {

        marginBottom: 5,

      },

      legalText: {

        fontSize: 9,

        color: '#6b7280',

        marginBottom: 20,

      },

      signatureSection: {

        flexDirection: 'row',

        justifyContent: 'space-between',

        marginTop: 40,

        paddingTop: 20,

        borderTopWidth: 1,

        borderTopColor: '#d1d5db',

      },

      signatureBox: {

        width: '45%',

        textAlign: 'center',

      },

      signatureLine: {

        borderTopWidth: 1,

        borderTopColor: '#9ca3af',

        paddingTop: 5,

        marginBottom: 10,

        height: 30,

      },

      keepTogether: {

        breakInside: 'avoid',

        orphans: 3,

        widows: 3,

      },

    });



    // Helper function to transform form data to contract data

    const transformFormData = (formData: ContractFormData): ContractData => {

      const formatDate = (dateString: string) => {

        if (!dateString) return '__.__.__';

        const date = new Date(dateString);

        return date.toLocaleDateString('no-NO', {

          day: '2-digit',

          month: '2-digit',

          year: 'numeric'

        });

      };



      return {

        companyName: formData.companyName || 'Ringerike Landskap AS',

        companyOrgNr: formData.companyOrgNumber || '***********',

        companyAddress: formData.companyAddress || 'Birchs vei 7, 3530 Røyse',

        employeeName: formData.employeeName || '________________________________',

        employeeBirthDate: formatDate(formData.employeeBirthDate),

        employeeAddress: formData.employeeAddress || '________________________________',

        position: formData.position || '________________________________',

        positionDescription: 'Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten',

        startDate: formatDate(formData.startDate),

        employmentType: formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse',

        probationPeriod: formData.probationPeriod ? `${formData.probationMonths || 6} måneder med 14 dagers gjensidig oppsigelsesfrist` : 'Ingen prøvetid',

        workHours: `${formData.workingHoursPerWeek || 37.5} timer per uke, normalt ${formData.workingTime || '07:00-15:00'}`,

        breakTime: formData.breakTime || 'Minst 30 min. ubetalt pause ved arbeidsdag >5,5 t',

        hourlyRate: `kr ${formData.hourlyRate || '___'},-`,

        overtimeRate: `${formData.overtimeRate || 40}% av timelønn`,

        paymentDate: `Den ${formData.paymentDay || 5}. hver måned til kontonummer ${formData.accountNumber || '____.____.__.____'}`,

        travelAllowance: formData.travelAllowance || 'Statens gjeldende satser (pt. 3,50 kr/km)',

        vacationDays: '5 uker per år i henhold til ferieloven',

        vacationPay: '12% av feriepengegrunnlaget',

        sickPay: 'Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom',

        noticePeriod: formData.noticePeriod || '1 måned gjensidig etter prøvetid',

        terminationRules: formData.notificationRules || 'Endringer varsles minimum 2 uker i forveien der mulig',

        pensionProvider: `${formData.pensionProvider || 'Storebrand'} (org.nr ${formData.pensionOrgNumber || '958 995 369'})`,

        workInsurance: `${formData.insuranceProvider || 'Gjensidige Forsikring ASA'} (org.nr ${formData.insuranceOrgNumber || '995 568 217'})`,

        tariffAgreement: 'Ingen tariffavtale er gjeldende per dags dato',

        competenceDevelopment: 'Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen',

        legalReference: `Denne kontrakten er utarbeidet i henhold til Arbeidsmiljøloven § 14-6 og oppfyller alle juridiske krav per ${new Date().toLocaleDateString('no-NO')}.`,

        contractDate: new Date().toLocaleDateString('no-NO'),

      };

    };



    const ContractPDF: React.FC<ContractPDFProps> = ({ formData }) => {

      const data = transformFormData(formData);



      return (

        <Document>

          <Page size="A4" style={styles.page}>

            {/* Header */}

            <View style={styles.header}>

              <Text style={styles.companyName}>{data.companyName}</Text>

              <Text>Org.nr: {data.companyOrgNr}</Text>

              <Text>{data.companyAddress}</Text>

              <Text style={styles.title}>ARBEIDSKONTRAKT</Text>

            </View>



            {/* Section 1: Party Identity */}

            <View style={styles.section}>

              <Text style={styles.sectionTitle}>1. PARTENES IDENTITET</Text>

              <View style={styles.row}>

                <View style={styles.column}>

                  <Text style={styles.label}>Arbeidsgiver:</Text>

                  <Text style={styles.text}>{data.companyName}</Text>

                  <Text style={styles.label}>Org.nr:</Text>

                  <Text style={styles.text}>{data.companyOrgNr}</Text>

                  <Text style={styles.label}>Adresse:</Text>

                  <Text style={styles.text}>{data.companyAddress}</Text>

                </View>

                <View style={styles.column}>

                  <Text style={styles.label}>Arbeidstaker:</Text>

                  <Text style={styles.text}>{data.employeeName}</Text>

                  <Text style={styles.label}>Fødselsdato:</Text>

                  <Text style={styles.text}>{data.employeeBirthDate}</Text>

                  <Text style={styles.label}>Adresse:</Text>

                  <Text style={styles.text}>{data.employeeAddress}</Text>

                </View>

              </View>

            </View>



            {/* Section 2: Work Location and Tasks */}

            <View style={styles.section}>

              <Text style={styles.sectionTitle}>2. ARBEIDSSTED OG ARBEIDSOPPGAVER</Text>

              <Text style={styles.label}>Arbeidssted:</Text>

              <Text style={styles.text}>Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt</Text>

              <Text style={styles.label}>Stillingsbetegnelse:</Text>

              <Text style={styles.text}>{data.position}</Text>

              <Text style={styles.label}>Arbeidsoppgaver:</Text>

              <Text style={styles.text}>{data.positionDescription}</Text>

            </View>



            {/* Section 3: Employment Terms */}

            <View style={styles.section}>

              <Text style={styles.sectionTitle}>3. ANSETTELSESFORHOLD</Text>

              <Text style={styles.text}><Text style={styles.label}>Tiltredelsesdato:</Text> {data.startDate}</Text>

              <Text style={styles.text}><Text style={styles.label}>Ansettelsestype:</Text> {data.employmentType}</Text>

              <Text style={styles.text}><Text style={styles.label}>Prøvetid:</Text> {data.probationPeriod}</Text>

            </View>



            {/* Section 4: Work Time and Salary */}

            <View style={styles.section}>

              <Text style={styles.sectionTitle}>4. ARBEIDSTID OG LØNN</Text>

              <Text style={styles.text}><Text style={styles.label}>Arbeidstid:</Text> {data.workHours}</Text>

              <Text style={styles.text}><Text style={styles.label}>Pauser:</Text> {data.breakTime}</Text>

              <Text style={styles.text}><Text style={styles.label}>Timelønn:</Text> {data.hourlyRate}</Text>

              <Text style={styles.text}><Text style={styles.label}>Overtidstillegg:</Text> {data.overtimeRate}</Text>

              <Text style={styles.text}><Text style={styles.label}>Utbetaling:</Text> {data.paymentDate}</Text>

              <Text style={styles.text}><Text style={styles.label}>Kjøregodtgjørelse:</Text> {data.travelAllowance}</Text>

            </View>



            {/* Section 5: Vacation and Leave */}

            <View style={[styles.section, styles.keepTogether]}>

              <Text style={styles.sectionTitle}>5. FERIE OG PERMISJON</Text>

              <Text style={styles.text}><Text style={styles.label}>Ferie:</Text> {data.vacationDays}</Text>

              <Text style={styles.text}><Text style={styles.label}>Feriepenger:</Text> {data.vacationPay}</Text>

              <Text style={styles.text}><Text style={styles.label}>Sykepenger:</Text> {data.sickPay}</Text>

            </View>



            {/* Section 6: Notice and Termination */}

            <View style={[styles.section, styles.keepTogether]}>

              <Text style={styles.sectionTitle}>6. OPPSIGELSE OG ENDRINGER</Text>

              <Text style={styles.text}><Text style={styles.label}>Oppsigelsesfrister:</Text> {data.noticePeriod}</Text>

              <Text style={styles.text}><Text style={styles.label}>Varslingsregler:</Text> {data.terminationRules}</Text>

              <Text style={styles.text}><Text style={styles.label}>Formkrav:</Text> Oppsigelse skal være skriftlig</Text>

            </View>



            {/* Section 7: Pension and Insurance */}

            <View style={[styles.section, styles.keepTogether]}>

              <Text style={styles.sectionTitle}>7. PENSJON OG FORSIKRING</Text>

              <Text style={styles.text}><Text style={styles.label}>Pensjon:</Text> {data.pensionProvider}</Text>

              <Text style={styles.text}><Text style={styles.label}>Yrkesskadeforsikring:</Text> {data.workInsurance}</Text>

            </View>



            {/* Section 8: Other Terms */}

            <View style={[styles.section, styles.keepTogether]}>

              <Text style={styles.sectionTitle}>8. ØVRIGE BESTEMMELSER</Text>

              <Text style={styles.text}><Text style={styles.label}>Tariffavtale:</Text> {data.tariffAgreement}</Text>

              <Text style={styles.text}><Text style={styles.label}>Kompetanseutvikling:</Text> {data.competenceDevelopment}</Text>

            </View>



            {/* Legal Reference */}

            <Text style={styles.legalText}>{data.legalReference}</Text>



            {/* Signature Section */}

            <View style={[styles.signatureSection, styles.keepTogether]}>

              <View style={styles.signatureBox}>

                <View style={styles.signatureLine}></View>

                <Text>Dato: {data.contractDate}</Text>

                <Text style={styles.label}>Arbeidsgiver</Text>

                <Text>{data.companyName}</Text>

              </View>

              <View style={styles.signatureBox}>

                <View style={styles.signatureLine}></View>

                <Text>Dato: {data.contractDate}</Text>

                <Text style={styles.label}>Arbeidstaker</Text>

                <Text>{data.employeeName}</Text>

              </View>

            </View>

          </Page>

        </Document>

      );

    };



    export default ContractPDF;

## Message 2

### 0 ▸ Role Acknowledgment  
_As your Synthesist, I’ll reorganise the “prompt” (i.e. the code that generates the PDF) into a **dimensional, modular hierarchy** so you can tweak each visual layer without endless scrolling.  I’ll walk from the top (global tokens) down to each concrete section type._

---

## 1 ▸ Three-Layer Design Model  

| Layer | What it holds | Edited *once* | Consumed by |
|-------|---------------|---------------|-------------|
| **A. Design Tokens** | Colours, fonts, spacing, border radii, break rules | `designSystem.ts` | every component |
| **B. Section Schemas** | Pure configuration describing _what a “Header”, “StandardSection”, “SignatureBlock” etc._ look like (margins, grid, typography) | `sectionSchemas.ts` | generic `<Section>` renderer |
| **C. Document Map** | _Chronological_ array that says **which** schema renders **which** data slice | `ContractPDF.tsx` | PDF runtime |

This means: **one vertical pass** through the file list lets you style everything, top-to-bottom:

```
A  →  B  →  C
tokens   schemas   instance-order
```

---

## 2 ▸ Code Sketch (concise but complete)

### 2.1  A – designSystem.ts  (only primitives)

```ts
// A. DESIGN TOKENS  ---------------------------------
export const tokens = {
  color: {
    primary:  '#059669',
    gray700:  '#374151',
    gray500:  '#6b7280',
    border:   '#d1d5db',
  },
  fontSize: {
    xs: 9, sm: 11, base: 13, lg: 16, xl: 18,
  },
  space:    (n: number) => n * 4,   // 4-pt grid
  border:   { thin: 1, thick: 2 },
  break:    { keepTogether: { breakInside: 'avoid', orphans:3, widows:3 } },
} as const;
```

### 2.2  B – sectionSchemas.ts  (still no JSX)

```ts
import { tokens as t } from './designSystem'

export type SectionType = 'header' | 'identity' | 'standard' | 'signature';

export const schemas = {
  header: {
    container: { textAlign:'center', mb: t.space(6), pb: t.space(4),
                 borderBottomWidth: t.border.thick, borderBottomColor: t.color.primary },
    title:     { fontSize: t.fontSize.xl, fontWeight:'bold', color:t.color.primary, mb:t.space(1) },
    sub:       { fontSize: t.fontSize.base, color:t.color.gray700 },
  },

  standard: {
    container: { mb: t.space(4), ...t.break.keepTogether },
    heading:   { fontSize: t.fontSize.base, fontWeight:'bold',
                 color:t.color.primary, mb:t.space(2),
                 pb:t.space(1), borderBottomWidth:t.border.thin, borderBottomColor:t.color.border },
    label:     { fontWeight:'bold', mb:t.space(0.5) },
    text:      { mb: t.space(1) },
  },

  identity: {          // specialised 2-column grid
    ...this.standard,
    row:      { flexDirection:'row', justifyContent:'space-between', mb:t.space(3) },
    col:      { flex:1, pr:t.space(5) },
  },

  signature: {
    container: { flexDirection:'row', justifyContent:'space-between',
                 mt:t.space(10), pt:t.space(5), borderTopWidth:t.border.thin, borderTopColor:t.color.border,
                 ...t.break.keepTogether },
    box:       { width:'45%', textAlign:'center' },
    line:      { height:30, borderTopWidth:t.border.thin, borderTopColor:t.color.gray500, pt:t.space(1), mb:t.space(2) },
  },
} as const;
```

### 2.3  C – ContractPDF.tsx  (order only)

```tsx
/* eslint-disable react/no-children-prop */
import React from 'react';
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
import { tokens as t }          from './designSystem';
import { schemas, SectionType } from './sectionSchemas';
import { ContractFormData }     from '@/lib/meta/types';

// ------------ 1. Generic Section renderer ----------------
const Section = (
  { type, title, rows, children }:
  { type: SectionType; title?: React.ReactNode; rows?: any[]; children?: React.ReactNode }
) => {
  const s = schemas[type] as any;
  return (
    <View style={s.container}>
      {title && <Text style={s.heading}>{title}</Text>}

      {/* Identity uses special grid */}
      {type === 'identity' && rows?.map((row, i) => (
        <View key={i} style={s.row}>
          {row.map((col: any, j: number) => (
            <View key={j} style={s.col}>
              <Text style={s.label}>{col.label}</Text>
              <Text style={s.text}>{col.value}</Text>
            </View>
          ))}
        </View>
      ))}

      {/* Standard free-form content */}
      {children}
    </View>
  );
};

// ------------ 2. Main Document ---------------------------
export const ContractPDF: React.FC<{ formData: ContractFormData }> = ({ formData }) => {
  const d = transformFormData(formData);   // keep your helper untouched

  /* 2a. Chronological “map” from section-type → content */
  const content = [
    {
      type:'header', children:
        <>
          <Text style={schemas.header.title}>{d.companyName}</Text>
          <Text style={schemas.header.sub}>Org.nr: {d.companyOrgNr}</Text>
          <Text style={schemas.header.sub}>{d.companyAddress}</Text>
          <Text style={[schemas.header.sub, {marginTop:t.space(4)}]}>ARBEIDSKONTRAKT</Text>
        </>
    },
    {
      type:'identity', title:'1. PARTENES IDENTITET', rows:[
        [
          { label:'Arbeidsgiver:',   value:d.companyName },
          { label:'Org.nr:',         value:d.companyOrgNr },
          { label:'Adresse:',        value:d.companyAddress },
        ],
        [
          { label:'Arbeidstaker:',   value:d.employeeName },
          { label:'Fødselsdato:',    value:d.employeeBirthDate },
          { label:'Adresse:',        value:d.employeeAddress },
        ],
      ]
    },
    {
      type:'standard', title:'2. ARBEIDSSTED OG ARBEIDSOPPGAVER', children:
        <>
          <Text><Text style={schemas.standard.label}>Arbeidssted: </Text>
                Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt</Text>
          <Text><Text style={schemas.standard.label}>Stillingsbetegnelse: </Text>{d.position}</Text>
          <Text><Text style={schemas.standard.label}>Arbeidsoppgaver: </Text>{d.positionDescription}</Text>
        </>
    },
    /* …repeat for the remaining numbered sections… */
    {
      type:'standard', children:
        <Text style={{fontSize:t.fontSize.xs, color:t.color.gray500}}>{d.legalReference}</Text>
    },
    {
      type:'signature', rows:[], children:   // using children here for brevity
        <>
          {['Arbeidsgiver', 'Arbeidstaker'].map((role,i) => (
            <View key={role} style={schemas.signature.box}>
              <View style={schemas.signature.line}/>
              <Text>Dato: {d.contractDate}</Text>
              <Text style={schemas.standard.label}>{role}</Text>
              <Text>{i === 0 ? d.companyName : d.employeeName}</Text>
            </View>
          ))}
        </>
    },
  ] as const;

  /* 2b. Render -------------------- */
  const pageStyles = StyleSheet.create({ page:{ padding:t.space(8), fontSize:t.fontSize.sm, lineHeight:1.4 }});

  return (
    <Document>
      <Page size="A4" style={pageStyles.page}>
        {content.map((sec, idx) => (
          <Section key={idx} {...sec}/>
        ))}
      </Page>
    </Document>
  );
};
```

---

## 3 ▸ Why this is cleaner

1. **Single-source styling** Tokens live in _one_ place.  
2. **Section-first editing** Open `sectionSchemas.ts`, tweak `standard.heading`, and every numbered section updates.  
3. **Chronological map** In `ContractPDF.tsx` you only scan *downward* to see the contract flow—no ping-ponging between files.  
4. **Extensible** Need a “Schedule A” appendix?  Create a new schema type and drop it into the content array.  
5. **Designer hand-off** A non-dev can read `schemas` without JSX noise and see exactly what “Header” or “Signature” means.

---

## 4 ▸ Next Iteration Ideas  
* **Theme switching** – export multiple token sets (e.g. dark / light, per-client branding).  
* **External JSON** – move the `content` array to CMS or database so the React layer is _pure rendering_.  
* **Dynamic page breaks** – give each schema a `canBreak` property and let a pagination helper decide optimal splits.

---

I’ve kept the example minimal to spotlight the hierarchy; adapt the typing / helper functions as needed.  This layout should let you refine each *visual stratum* in isolation, exactly as you described.
