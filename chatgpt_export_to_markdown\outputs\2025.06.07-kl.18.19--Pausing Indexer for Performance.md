# Pausing Indexer for Performance

Conversation ID: 6844669b-a5c4-8008-bebb-d31cda719f6a

## Message 1

Does it make sense to pause the file indexer on my synology nas when copying and moving large folders?

## Message 2

Yes, **pausing the file indexer** on your Synology NAS when copying or moving large folders **does make sense** in many situations, especially if you're concerned with performance or want to avoid redundant processing.

### Here's why:

#### ✅ Benefits of Pausing the Indexer:
1. **Improved Performance:**
   - Indexing consumes CPU and I/O. During large file operations, this can cause slowdowns or bottlenecks.
   - Pausing the indexer frees up system resources for faster copying/moving.

2. **Avoids Redundant Indexing:**
   - If you're moving files between indexed folders, the system might re-index them unnecessarily at the new location.
   - Pausing prevents double work until everything is in place.

3. **Less Disk Thrashing:**
   - Indexer reads metadata while you’re writing/moving files, potentially causing fragmented I/O and more drive wear.

---

#### ⚠️ When Not to Pause:
- If real-time search availability is critical (e.g., for Moments, Audio Station, Synology Drive), pausing the indexer will delay search updates.
- If you're copying over a long period and forget to resume indexing, the library stays outdated.

---

### 🔧 How to Pause:
- **DSM UI**: `Control Panel > Indexing Service` → Temporarily pause.
- **SSH/Terminal** (advanced): stop `synoindexd` or related services—only if you know what you’re doing.

---

### 🧠 Recommendation:
Pause indexing **before large operations**, especially when:
- Moving between folders that are both indexed
- Copying massive media libraries or archives
- You notice high CPU usage from `synoindexd`

Resume once done. This avoids unnecessary strain and redundant indexing events.

Let me know if you want a quick script or shortcut to toggle it on/off.
