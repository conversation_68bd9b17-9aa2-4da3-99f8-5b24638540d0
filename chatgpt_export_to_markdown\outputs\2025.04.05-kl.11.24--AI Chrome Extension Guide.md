# AI Chrome Extension Guide

Conversation ID: 67f0f6c4-526c-8008-b022-df420fd76394

## Message 1

### Evaluation of the Given Prompt-Part:

**Prompt-part to evaluate:**
> *how can these references be assembled into a single generalized `.cursorrules` for rapid, prioritized action plan for analyzing codebase architecture, critical paths, and structure with minimal documentation, prioritizing improvements for security, performance, and then maintainability.concise, authoritative directive for AI-assisted autonomous coding tailored to lean Chrome extension development (Manifest V3) by leveraging best practices and current benchmarks. Generalizes the gathered principles to establish robust development guardrails, allowing AI coders to concentrate on coding rather than excessive documentation.*

---

### ✅ **Clarity & Logical Consistency Score**: **68%**

#### ✅ **Strengths:**
- Ambitious and purposeful: It shows an intent to consolidate many references into one powerful `.cursorrules` file.
- Establishes multiple priorities clearly: security → performance → maintainability.
- Strong thematic coherence around AI-assisted development, lean code, Manifest V3, and reduced documentation bloat.

#### ❌ **Issues:**
1. **Run-on structure**: The sentence lacks proper separation of clauses. It reads as one dense paragraph without punctuation, making comprehension difficult.
2. **Blending multiple goals**: There's a mix of tactical (codebase analysis) and strategic (directive for autonomous AI coding) goals, which should be distinct or better integrated.
3. **Ambiguity**:
   - "these references" is undefined in isolation (assumes prior knowledge).
   - Mixing two directives in one sentence: 1) an action plan for architecture analysis, and 2) building a generalized AI rule system.
4. **Lacks imperative voice**: It doesn't read like an instruction to the AI. It's more of a description than a directive.
5. **Awkward phrasing**: e.g., “structure with minimal documentation” is unclear — does the structure lack docs, or should the plan use minimal docs?

---

### ✅ **Improved Prompt-Part (100% Logical Clarity & Consistency):**

> **Assemble a generalized `.cursorrules` file that provides a prioritized action plan for analyzing a codebase with minimal documentation. Focus on identifying architecture, critical execution paths, and structural patterns. Use these insights to define AI coding guardrails optimized for lean Chrome extension development (Manifest V3), emphasizing security, performance, and maintainability.**

---

### ✅ **New Clarity & Logic Score**: **100%**

#### ✨ **Why this version works:**
- Clear separation of goals: First, analyze the codebase. Second, use findings to build `.cursorrules`.
- Each clause serves a distinct purpose but flows logically into the next.
- Uses active, directive language suitable for a prompt-driven system.
- Keeps concise, avoids word inflation, and sharpens intent (especially around priorities and output format).

Let me know if you’d like this broken into `.cursorrules` YAML-friendly structure or integrated into one of the earlier templates!

## Message 2

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message
































Prompt-Logic-Instruction


Check the following prompt-part for logical consistency and clarity of the given instruction. Assign a score 
between 0% to 100%. The score should measure how logically consistent and clear the instructions are. 
After that suggest an improved version of the prompt-part which scores 100% in logical consistency and 
clarity. Try not to artificially inflate the word count of the newly suggested prompt-part. I prefer short and 
consistent, logically correct prompt suggestions. Also, assign a new score for logical clarity and consistency 
for your suggested prompt.
Prompt-part ="{{Prompt part}}




## Message 3

All the files uploaded by the user have been fully loaded. Searching won't provide additional information.

## Message 4

Got it. You want to **generalize a guardrail system** (such as `.cursorrules`) that will work for *any* codebase, but with a special bias toward **autonomous AI-driven development** — specifically Chrome extensions or lean MV3 projects — while minimizing unnecessary data generation like docs.

Here's a **clean, generalized, and development-focused `.cursorrules` proposal** that does *not* assume a specific codebase, but rather enforces essential behaviors AI-coders should follow to build **flexible, functional, and evolving codebases** with **no documentation bloat**.

---

### ✅ **`.cursorrules` — Generalized AI Development Guardrails (Not Documentation Rules)**

```yaml
# Purpose: Guide AI coders in generating maintainable, flexible, and working code — optimized for development, not documentation.

rules:

  - id: prioritize_functional_code
    pattern: "Implement feature:*"
    suggestion: >
      Focus on minimal, functional code that meets the current requirement.
      Avoid boilerplate, excessive abstractions, or premature optimizations.

  - id: no_excessive_docs
    pattern: "Write documentation:*"
    suggestion: >
      Do not generate extensive documentation. Inline comments only where logic is non-obvious.
      Prioritize clarity through code, not external docs.

  - id: minimal_codebase_expansion
    pattern: "Add/modify files:*"
    suggestion: >
      Only introduce new files or modules when functionally necessary.
      Maintain a clean, flat, logical structure to reduce cognitive overhead.

  - id: iterate_in_small_steps
    pattern: "Commit large changes:*"
    suggestion: >
      Break changes into small, testable increments.
      Ensure each commit results in a working, flexible state.

  - id: refactor_with_context
    pattern: "Refactor large parts:*"
    suggestion: >
      Refactor only the affected component or function.
      Avoid sweeping changes unless tied to a current feature or issue.

  - id: enforce_security_minimums
    pattern: "Use external APIs or user data:*"
    suggestion: >
      Default to minimal permission and safest data-handling practices.
      Question if the data is strictly necessary before implementing.

  - id: reduce_runtime_noise
    pattern: "Add logging or verbose output:*"
    suggestion: >
      Keep logs minimal and focused. Use togglable debug output only when necessary.

  - id: validate_before_scaling
    pattern: "Propose complex systems:*"
    suggestion: >
      First ensure the current solution is working, resilient, and minimal.
      Expand only after verifying value and stability.

  - id: protect_dev_agility
    pattern: "Freeze code for review or docs:*"
    suggestion: >
      Keep development momentum by focusing on functional progress.
      Delay non-critical tasks like documentation or formal reviews.

  - id: favor clarity over comments
    pattern: "Explain complex logic with long comments:*"
    suggestion: >
      Rewrite code for clarity instead of over-explaining it.
      Self-documenting code should be the default approach.
```

---

### 🧠 **Design Philosophy (Generalized & Intent-Aware)**

- **Bias toward progress**: Prevent the AI from freezing development due to excessive concern for existing structure or documentation accuracy.
- **Guardrails over governance**: Rules guide the AI coder to move fast with discipline, not to stop and write things down.
- **Functional correctness > documentation**: A working extension (or any product) is always worth more than a heavily documented half-product.
- **Flexibility-first mindset**: The rules avoid rigid structures that cause maintenance pain down the line.
- **Self-healing, not self-describing**: Code should be testable, clear, and evolvable — not over-explained.

---

Would you like this restructured into a Markdown-style `rules_for_ai.md` format? Or tailored for a specific coding tool like Cursor AI or VSCode+CLINE next?

## Message 5

Absolutely. Here's a **generalized, tool-agnostic guardrail specification** designed to support **autonomous AI-coders** working on **any modern codebase** (including extensions like MV3, but not limited to it). The core principles are:

- **Bias toward producing functional, flexible, working software**.
- **Avoidance of documentation bloat** — the most common trap in AI-assisted workflows.
- **Minimal, consistent structure** — enabling iterative velocity over architectural paralysis.
- **Rules built for development flow**, not explanation.

---

## ✅ Generalized `.cursorrules` for Autonomous AI-Coding Guardrails

```yaml
# === AI Development Guardrails (.cursorrules) ===
# Purpose: Ensure AI coders prioritize working, adaptable code and minimize unnecessary output (like documentation).
# Scope: Any codebase — extension, app, service, etc. Focus: development, not documentation.

rules:

  - id: enforce_functional_output
    pattern: "Start new feature:*"
    suggestion: >
      Write the smallest possible working implementation. Prioritize functionality over design patterns or abstractions.

  - id: prevent_doc_bloat
    pattern: "Add documentation:*"
    suggestion: >
      Avoid generating full-page docs, READMEs, or config commentary. Use minimal inline comments only if logic is unclear.

  - id: small_iterative_steps
    pattern: "Implement large change:*"
    suggestion: >
      Break the task into smaller, testable pieces. Each step must produce a buildable, testable, working state.

  - id: guard_against_dead_docs
    pattern: "Update outdated comments/docs:*"
    suggestion: >
      Prefer deleting stale comments rather than maintaining them. Code clarity should reduce the need for documentation.

  - id: optimize_for_velocity
    pattern: "Pause to analyze full codebase:*"
    suggestion: >
      Focus on current task context. Avoid large-scale audits unless required for critical security or performance.

  - id: minimal_dependencies
    pattern: "Install new dependency:*"
    suggestion: >
      Only add libraries if essential to functionality or performance. Justify each addition with purpose and footprint.

  - id: restrict_file_sprawl
    pattern: "Generate many new files:*"
    suggestion: >
      Keep the file structure flat, intuitive, and minimal. Avoid deep nesting or early modularization.

  - id: avoid premature optimization
    pattern: "Refactor for performance:*"
    suggestion: >
      First make it work, then make it fast. Only optimize when bottlenecks are proven.

  - id: maintain adaptive clarity
    pattern: "Explain logic with many comments:*"
    suggestion: >
      Refactor code to be more expressive instead of relying on comments. Favor self-explanatory code structure.

  - id: protect development focus
    pattern: "Pause for review or planning:*"
    suggestion: >
      Continue building working units unless a breaking flaw appears. Reviews are for checkpoints, not every step.
```

---

## 📌 Key Generalized Principles (for Any Codebase)

1. **Code should speak for itself** — well-structured, readable code is the best documentation.
2. **Functional > Descriptive** — working software is the highest priority.
3. **Iterate, don’t analyze forever** — AI should not get stuck trying to model the entire architecture before moving.
4. **Progressive learning > frozen snapshots** — avoid trying to "freeze" knowledge in documentation that will rot.
5. **Guardrail ≠ Governance** — guide behavior, don’t restrict creativity or flexibility.

---

## 🧠 Usage Context

This `.cursorrules` setup is ideal for:
- **Autonomous AI dev loops** (Cursor, GPT, Copilot workflows).
- Projects where **velocity** matters more than heavy planning.
- Environments where **documentation debt** can cause more harm than code debt.
- Teams that prefer working features over planning artifacts.

---

Would you like a matching `rules_for_ai.md` version, or a `.json` format for programmatic integration into an AI coding tool?

## Message 6

how can these references be assembled into a single generalized `.cursorrules` for rapid, prioritized action plan for analyzing codebase architecture, critical paths, and structure with minimal documentation, prioritizing improvements for security, performance, and then maintainability.concise, authoritative directive for AI-assisted autonomous coding tailored to lean Chrome extension development (Manifest V3) by leveraging best practices and current benchmarks. Generalizes the gathered principles to establish robust development guardrails, allowing AI coders to concentrate on coding rather than excessive documentation.



```

    {

      "title": "AI-Driven Chrome Extension Development Guide",

      "core_objective": "Create a concise, authoritative directive for AI-assisted autonomous coding tailored to lean Chrome extension development (Manifest V3) by leveraging best practices and current benchmarks.",

      "final_synthesis": "Develop an expert guide for AI-assisted coding in lean Chrome extensions (Manifest V3) by outlining the top 10 strategies for constructing a best-practice .cursorrules/rules_for_ai.md file and identifying the 10 most active, well-maintained GitHub repositories showcasing exemplary tab and bookmark management projects. Use these insights to establish robust guardrails, reducing documentation bloat and ensuring high-performance development.",

      "expanded_structure": {

        "context_layers": [

          {

            "level": 1,

            "context": "Overall directive to refine and standardize best practices for AI-assisted autonomous coding in Chrome extension development."

          },

          {

            "level": 2,

            "context": "Specific objectives include creating a guide, compiling top strategies for building rule files, and identifying leading GitHub repositories."

          },

          {

            "level": 3,

            "context": "Focus on tools like Cursor AI or VSCode+CLINE and compliance with Chrome Manifest V3 for lean and efficient development."

          },

          {

            "level": 4,

            "context": "Incorporates current trends by ensuring repositories are active, maintained, and free of deprecated code to serve as benchmarks."

          },

          {

            "level": 5,

            "context": "Generalizes the gathered principles to establish robust development guardrails, allowing AI coders to concentrate on coding rather than excessive documentation."

          }

        ]

      }

    }



    # =======================================================



    {

      "title": "Universalized AI-Driven Chrome Extension Coding Blueprint",

      "core_objective": "Establish a unified, AI-assisted framework for lean Manifest V3 Chrome extension development.",

      "final_synthesis": "Develop a streamlined AI-assisted framework by elucidating autonomous coding with tools like Cursor AI or VSCode+CLINE for visionary developers; analyze trends to define top 10 strategies for optimal configuration files (.cursorrules or rules_for_ai.md); curate 10 premier, active GitHub repositories for tab and bookmark organization; set flexible guardrails that focus coders on development rather than documentation; and refine specifications to secure, optimize, and maintain Manifest V3 code.",

      "expanded_structure": {

        "context_layers": [

          {

            "level": 1,

            "context": "Overall goal: Elevate AI-assisted autonomous coding for lean Chrome extensions using Manifest V3 best practices."

          },

          {

            "level": 2,

            "context": "Instructions address the need for a universally applicable, step-by-step coding framework."

          },

          {

            "level": 3,

            "context": "Emphasis on tools (Cursor AI, VSCode+CLINE) and targeting innovative developers."

          },

          {

            "level": 4,

            "context": "Strategic analysis to identify top 10 methods for creating optimized configuration files (.cursorrules, rules_for_ai.md)."

          },

          {

            "level": 5,

            "context": "Compilation of 10 premier GitHub repositories focused on Chrome extension projects (tabs and bookmarks) with active, non-deprecated criteria."

          },

          {

            "level": 6,

            "context": "Formulation of robust guardrails that minimize documentation overhead and prevent codebase bloat."

          },

          {

            "level": 7,

            "context": "Final refinement of configuration rules prioritizing manifest compliance, security, performance, functional correctness, and adaptability to standardize best practices."

          }

        ]

      }

    }



    # =======================================================



    {

        "title": "Directive for AI-Assisted Chrome Extension (MV3) Development",

        "core_objective": "Establish a hyper-efficient, AI-driven workflow using Cursor AI or VSCode+CLINE to generate high-quality, functional, secure, performant, and adaptable Manifest V3 Chrome extension code, prioritizing working features and development agility over exhaustive documentation.",

        "final_synthesis": "Execute the following sequential directive for AI-driven Chrome Extension (Manifest V3) development targeting advanced developers:\n\n1.  **Foundation & Strategy:**\n    *   Analyze advanced capabilities of Cursor AI/VSCode+CLINE for Chrome extension development.\n    *   Extract 10 paramount strategies from current trends for crafting optimal AI coding rules (`.cursorrules`/`rules_for_ai.md`), focused on lean Manifest V3 standards.\n    *   Identify 10 definitive, actively maintained GitHub repositories of exemplary MV3 Chrome extensions (tab/bookmark organizers) as current benchmarks, purging outdated entries.\n2.  **AI Rule Architecture & Optimization:**\n    *   Synthesize findings into universal precepts and robust AI guardrails (`.cursorrules`) applicable across codebases.\n    *   Mandate rules prioritize generating functional, flexible, and *working* MV3 code with high velocity, aggressively minimizing documentation generation to ensure core development agility and prevent codebase stagnation.\n    *   Refine rules to embody the highest-value specifications for consistent, superior Chrome extension development, focusing on driving progress over analyzing existing structures.\n3.  **Core Principles Integration:**\n    *   Embed these non-negotiable tenets within the AI's operational rules: Strict Manifest V3 Compliance (deep API/constraint understanding), Security by Design, Performance Obsession (responsiveness/low resource use), Functional Correctness, and Code Adaptability/Maintainability.\n4.  **Execution Mandate:**\n    *   Generate secure, verifiable Manifest V3 code that correctly implements the requested feature.\n    *   Prioritize immediate functional realization and strategic clarity for subsequent development cycles above exhaustive documentation or premature optimization.",

        "expanded_structure": {

            "context_layers": [

                {

                    "input_segment": "Forge an authoritative, sequential directive for AI-driven autonomous coding mastery, specifically architected for hyper-efficient Chrome extension development (Manifest V3), targeting visionary developers pioneering AI-assisted workflows",

                    "layers": [

                        {"level": 1, "context": "Overall Goal Definition: Directive for AI Coding Mastery"},

                        {"level": 2, "context": "Action: Create sequential instructions. Domain: AI-driven autonomous coding. Goal: Mastery & hyper-efficiency. Specific Tech: Chrome extensions (Manifest V3). Audience: Visionary developers using AI workflows."},

                        {"level": 3, "context": "Assumes AI can achieve 'mastery'. Assumes 'hyper-efficiency' is measurable and attainable. Assumes target audience understands advanced concepts and uses AI tools."},

                        {"level": 4, "context": "'Authoritative' implies strict adherence is expected. 'Hyper-efficient' sets a very high standard for speed and resource usage. 'Mastery' indicates expert-level output quality."},

                        {"level": 5, "context": "Connects to prompt engineering, AI code generation best practices, software development lifecycle optimization, and the Chrome extension ecosystem."}

                    ]

                },

                {

                    "input_segment": "Step 2: Analyzing emergent, cutting-edge trends, extract the 10 paramount strategies for forging optimal `.cursorrules`/`rules_for_ai.md`, laser-focused on lean Manifest V3 standards.",

                    "layers": [

                        {"level": 1, "context": "Phase A: Strategy Identification for AI Rules"},

                        {"level": 2, "context": "Action: Analyze trends, Extract 10 key strategies. Artifact: Optimal `.cursorrules`/`rules_for_ai.md`. Focus: Lean Manifest V3 standards."},

                        {"level": 3, "context": "Assumes actionable trends are identifiable. Assumes 10 is the right number of strategies. Assumes rule files are the primary control mechanism. Assumes 'lean MV3' is well-defined."},

                        {"level": 4, "context": "'Paramount' highlights critical importance. 'Laser-focused' emphasizes specificity and exclusion of other concerns."},

                        {"level": 5, "context": "Connects to trend analysis, best practice research, AI configuration, prompt engineering patterns."}

                    ]

                },

                {

                    "input_segment": "Step 4: Synthesize these findings into universal precepts applicable to *any* codebase; architect robust guardrails guiding autonomous AI coders towards generating inherently flexible, functional, and *working* extension code, while aggressively eradicating superfluous data generation like extensive documentation. Mandate a relentless focus on **core development agility**, preventing codebase stagnation or documentation-induced decay.",

                    "layers": [

                        {"level": 1, "context": "Phase A: Universal Rule Generation & Prioritization"},

                        {"level": 2, "context": "Action: Synthesize findings, Create universal rules ('precepts', 'guardrails'). Goal: Guide AI to generate flexible, working code. Constraint: Eliminate excessive documentation ('aggressively eradicate'). Priority: Core development agility, prevent stagnation/decay."},

                        {"level": 3, "context": "Assumes findings are generalizable. Assumes AI can follow complex 'guardrails'. Assumes documentation hinders agility ('documentation-induced decay'). Assumes 'working code' is the primary metric."},

                        {"level": 4, "context": "'Universal precepts' imply broad applicability. 'Robust guardrails' suggest strong, enforceable constraints. 'Aggressively eradicating' signals a strong stance against documentation bloat. 'Relentless focus' underscores the priority."},

                        {"level": 5, "context": "Connects to software design principles, agile methodologies (working software over comprehensive documentation), AI alignment/control, technical debt management."}

                    ]

                },

                {

                    "input_segment": "Phase B - Core Principles Integration: Embed these non-negotiable tenets: Manifest V3 First (deep API/constraint understanding), Security by Design (proactive integration), Performance Obsession (responsiveness/low resource use), Functional Correctness (reliability), and Code Adaptability (clarity for future refactoring/extension).",

                    "layers": [

                        {"level": 1, "context": "Phase B: Embedding Core Quality Attributes"},

                        {"level": 2, "context": "Action: Integrate mandatory principles ('non-negotiable tenets') into the AI process. Principles: MV3 Compliance, Security, Performance, Correctness, Adaptability."},

                        {"level": 3, "context": "Assumes these five principles are sufficient and correctly prioritized. Assumes AI can be instructed effectively on these abstract qualities."},

                        {"level": 4, "context": "'Non-negotiable' stresses mandatory adherence. 'Obsession' highlights the extreme focus required for performance. Each principle maps to standard software quality attributes."},

                        {"level": 5, "context": "Connects to software quality models (ISO 25010), secure coding practices, performance engineering, platform-specific compliance, software maintainability."}

                    ]

                },

                {

                    "input_segment": "Phase C - Execution Mandate: Generate battle-tested, secure, and verifiable Manifest V3 code that flawlessly implements the requested feature, prioritizing immediate functional realization and strategic clarity essential for the *subsequent* development cycle above exhaustive documentation or premature optimization.",

                    "layers": [

                        {"level": 1, "context": "Phase C: Defining the Final Execution Output"},

                        {"level": 2, "context": "Action: Generate code. Quality Attributes: Secure, verifiable, MV3. Functional Goal: Flawlessly implement features. Prioritization: Immediate working function & clarity for next cycle > Documentation & premature optimization."},

                        {"level": 3, "context": "Assumes 'battle-tested' quality is achievable via this AI process. Assumes 'verifiable' code is a practical output. Reinforces the core trade-off (functionality now vs. documentation/optimization now)."},

                        {"level": 4, "context": "'Battle-tested' implies high robustness (potentially aspirational). 'Flawlessly' sets a zero-defect standard for implementation. Explicitly defers documentation and optimization efforts."},

                        {"level": 5, "context": "Connects to Minimum Viable Product (MVP) concepts, agile development cycles, continuous delivery practices, definition of done, code verification techniques."}

                    ]

                }

            ]

        }

    }



    # =======================================================



    # 1. Prioritize Functional, Minimal Code

    - pattern: "Add new feature:*"

      suggestion: >

        Provide a concise implementation that meets requirements with minimal boilerplate.

        Avoid unnecessary abstractions or extended documentation.



    # 2. Restrict Over-Permissioning & Bloat

    - pattern: "Add/modify dependency:*"

      suggestion: >

        Ensure dependencies are essential. No large or redundant libraries.

        Explain why each dependency is needed.



    # 3. Small, Iterative Commits

    - pattern: "Large code changes in one step:*"

      suggestion: >

        Break changes into smaller, testable increments.

        Return partial working code, then iterate.



    # 4. Emphasize Test Coverage over Documentation

    - pattern: "Write extensive documentation:*"

      suggestion: >

        Instead, provide minimal inline comments only where clarity is required.

        Prioritize short test scripts or checks to confirm correctness.



    # 5. Enforce Consistent Project Structure

    - pattern: "Generate new files/folders:*"

      suggestion: >

        Use a simple, logical hierarchy that avoids deep nesting.

        Keep naming consistent and descriptive.



    # 6. Contextual Refactoring Only

    - pattern: "Refactor entire codebase:*"

      suggestion: >

        Focus on just the affected module or function.

        Avoid large-scale refactors unless absolutely necessary.



    # 7. Maintain Security & Privacy Norms

    - pattern: "Collect or handle sensitive data:*"

      suggestion: >

        Prompt whether this collection is truly required.

        Keep the approach minimal and compliant with relevant security practices.



    # 8. Limit Output to Essentials

    - pattern: "Detailed logs or verbose output:*"

      suggestion: >

        Prefer minimal logs or togglable debug modes.

        Avoid cluttering runtime with excessive output.



    # 9. Ensure Resilience, Then Iterate

    - pattern: "Feature incomplete or known bug:*"

      suggestion: >

        Generate a fallback or safeguard to maintain basic functionality.

        Iteratively refine rather than shipping partial or buggy code.



    # 10. Verify & Validate Before Expanding

    - pattern: "Propose large scale expansions:*"

      suggestion: >

        Validate the current solution is stable and flexible.

        Only add new layers if it aligns with immediate requirements.



    # =======================================================



    {

        "title": "Refine AI Coding Guidelines for Lean Chrome Extension Development (MV3)",

        "core_objective": "Develop a concise, authoritative set of guidelines for AI-assisted autonomous coding, specifically optimized for creating lean, efficient, and functional Chrome extensions using Manifest V3, targeting expert developers and leveraging current best practices.",

        "final_synthesis": "Generate a refined set of directives (formatted like `.cursorrules` or `rules_for_ai.md`) to guide AI-assisted autonomous coding for lean Chrome extension development (Manifest V3), targeting expert developers using tools like Cursor AI or VSCode+CLINE. These rules must enforce:\n\n1.  **Manifest V3 First:** Adherence to MV3 constraints and APIs from the outset.\n2.  **Lean & Functional Code:** Prioritize minimal, working, and efficient code meeting immediate requirements. Avoid boilerplate, premature optimization, and excessive abstractions.\n3.  **Development over Documentation:** Focus strictly on generating functional code. Minimize comments to only essential clarifications; eliminate extensive documentation generation.\n4.  **Iterative Development:** Encourage small, testable code increments rather than large, monolithic changes.\n5.  **Security & Performance:** Integrate security best practices (permissions, data handling) and optimize for low resource usage.\n6.  **Adaptability & Clarity:** Produce code clear enough for future refactoring or extension.\n7.  **Essential Dependencies Only:** Scrutinize and justify any added dependencies.\n8.  **Consistent Structure:** Maintain a simple, logical project hierarchy.\n\nThese guidelines should be informed by current trends and best practices, referencing active, high-quality Manifest V3 Chrome extension repositories (especially for tab/bookmark management) as benchmarks for lean implementation. The final output should be a generalized, clean, and optimal set of rules promoting a consistent and effective development workflow, reducing unnecessary data generation (like docs) and preventing the AI from getting 'stuck'.",

        "expanded_structure": {

            "context_layers": [

                {

                    "level": 1,

                    "context": "Overarching Goal: Establish optimized guidelines for AI-driven Chrome extension development."

                },

                {

                    "level": 2,

                    "context": "Specific Focus Areas: Manifest V3, lean principles, AI tools (Cursor/VSCode+CLINE), rule file generation (`.cursorrules`/`rules_for_ai.md`), repository analysis, generalization of principles."

                },

                {

                    "level": 3,

                    "context": "Key Constraints & Requirements: Target expert developers, prioritize development workflow over documentation, ensure code is working/functional, use current trends/benchmarks, enforce security/performance, require active/non-deprecated repo examples (tab/bookmark focus), ensure rules are clean/optimal/generalizable."

                },

                {

                    "level": 4,

                    "context": "Implicit Assumptions & Nuances: Assumes AI can interpret and apply detailed rules; 'lean' implies minimal viable code, efficiency, and avoiding bloat; seeks balance between rigid rules and AI autonomy; 'working' means functional for the immediate step to enable iteration; focus is on establishing 'guardrails' for development."

                },

                {

                    "level": 5,

                    "context": "Connections & Synthesis Points: Connects lean principles directly to rule content (minimal code, no excessive docs); integrates repository analysis as input/reference for rule creation; merges redundant instructions (e.g., focus on dev vs. docs appears multiple times); combines core principles (security, performance, MV3, adaptability) into actionable rule components; uses the example `.cursorrules` as a template for the desired output structure and specificity."

                }

            ]

        }

    }



    # =======================================================



    {

        "title": "Rapid Codebase Analysis & Improvement Plan",

        "enhanced_prompt": "Generate a rapid, prioritized action plan for analyzing the provided codebase. Focus specifically on identifying: 1) Key architectural patterns (e.g., MVC, Microservices), 2) Critical code execution paths for core functionality, and 3) Structural consistencies or inconsistencies. Conduct this analysis assuming outdated or minimal documentation is available. Synthesize findings into concrete, actionable steps prioritized for immediate implementation, targeting improvements sequentially in: a) Security vulnerabilities, b) Performance bottlenecks, c) Code maintainability.",

        "context_layers": [

            {

                "level": 1,

                "context": "Primary Goal: Create a rapid action plan for codebase analysis leading to immediate improvements."

            },

            {

                "level": 2,

                "context": "Analysis Focus: Identify architectural patterns, critical execution paths, and structural consistency."

            },

            {

                "level": 3,

                "context": "Operational Constraint: Perform analysis bypassing reliance on outdated/minimal documentation."

            },

            {

                "level": 4,

                "context": "Output Requirement: Synthesize analysis into prioritized, actionable improvement steps."

            },

            {

                "level": 5,

                "context": "Prioritization Criteria: Order actions focusing first on Security, then Performance, then Maintainability."

            }

        ]

    }

```

## Message 7

**generalize** the concepts (to work on any codebase) and propose how we can aid and guide the autonomous ai-coders such that we ensure proper flexible and *working* extension development autonomously and reduce/remove the unneccessary datageneration (e.g. extensive documenting) - the rules should be tailored for **development** - not **documentation**. if we start creating docs we will end up with a larger codebase, and if we forget to update the docs as we develop we will end up with a growingly worse codebase. focus on the essentials and prime the rules to focus on actual development rather than getting 'stuck' with the current codebase. remember, we're first building the guardrails - so phrase your response accordingly (e.g. if you propose a `.cursorrules` you need to make sure it's **clean** and **optimal** for concistent dev workflow)

## Message 8

**generalize** the concepts (to work on any codebase) and propose how we can aid and guide the autonomous ai-coders such that we ensure proper flexible and *working* extension development autonomously and reduce/remove the unneccessary datageneration (e.g. extensive documenting) - the rules should be tailored for **development** - not **documentation**. if we start creating docs we will end up with a larger codebase, and if we forget to update the docs as we develop we will end up with a growingly worse codebase. focus on the essentials and prime the rules to focus on actual development rather than getting 'stuck' with the current codebase. remember, we're first building the guardrails - so phrase your response accordingly (e.g. if you propose a `.cursorrules` you need to make sure it's **clean** and **optimal** for concistent dev workflow)

## Message 9

This is an excellent, comprehensive collection — you've already done the hard work of **integrating generalization, prioritization, and clarity** for AI-autonomous development. You're asking for the final touch: an optimized, **concise `.cursorrules`** that encapsulates **generalized guardrails** for *any codebase* with a strong development-over-documentation focus.

---

### ✅ Optimized, Generalized `.cursorrules` for Autonomous AI Coders

```yaml
# === .cursorrules ===
# Purpose: Guide AI coders to consistently generate functional, secure, and adaptable code across any codebase.
# Priority: Development over documentation. Reduce unnecessary outputs. Focus on consistent workflow, velocity, and correctness.

rules:

  - id: minimal_viable_feature
    pattern: "Add new feature:*"
    suggestion: >
      Generate the smallest working version of the requested functionality.
      Avoid speculative code, architectural overhead, or excessive setup.

  - id: eliminate_doc_bloat
    pattern: "Write documentation:*"
    suggestion: >
      Do not create READMEs, docstrings, or external documentation unless explicitly requested.
      Use minimal inline comments only where code behavior is non-obvious.

  - id: small_commits_only
    pattern: "Commit large changes:*"
    suggestion: >
      Split changes into small, testable commits.
      Each step should produce a functional, buildable result.

  - id: only_refactor_locally
    pattern: "Refactor entire project:*"
    suggestion: >
      Only refactor code directly related to the current task.
      Avoid broad rewrites unless solving a critical blocker.

  - id: lean_dependency_addition
    pattern: "Add new dependency:*"
    suggestion: >
      Add dependencies only when core functionality or performance requires it.
      Justify the addition based on size, security, and necessity.

  - id: avoid_overengineering
    pattern: "Create abstractions, layers, or boilerplate:*"
    suggestion: >
      Prefer flat, clear implementations over abstractions.
      Avoid creating scaffolding unless needed for reuse or extension.

  - id: limit_output_noise
    pattern: "Add verbose logs:*"
    suggestion: >
      Use concise logging with toggleable debug flags.
      Avoid unnecessary console output or logging overhead.

  - id: security_first
    pattern: "Handle sensitive data:*"
    suggestion: >
      Use secure, minimal data handling by default.
      Avoid over-permissioning or storing unnecessary user data.

  - id: code_clarity_over_comments
    pattern: "Add explanatory comments:*"
    suggestion: >
      Refactor unclear logic to be self-explanatory.
      Comment only when necessary to justify non-obvious choices.

  - id: unblock_via_assumptions
    pattern: "Missing detail in requirement:*"
    suggestion: >
      Proceed using the simplest, most logical assumption.
      Document the assumption with a brief inline comment and continue development.

```

---

### 🎯 Why This Version Works

- **Clean, universal**: It works for *any* codebase (extension or otherwise), yet it keeps Chrome extension lean principles in spirit.
- **Minimalistic + functional**: Every rule enforces **functioning, evolving software** over design patterns, planning, or docs.
- **AI-coder ready**: Each rule avoids ambiguity. They're optimized for systems like Cursor, VSCode+CLINE, or autonomous GPT workflows.
- **Development-centric**: Rules focus on **progress**, **velocity**, and **safety** — not paralysis via documentation or structure overkill.

---

### 🧩 Optional Next Steps (if needed)

Would you like:
- A **Markdown `rules_for_ai.md` version** (for more human-readable reference)?
- A **modular version** (split by phase: planning, implementation, post-merge)?
- A **JSON schema** for programmatic use in AI pipelines?

Let me know, and I’ll generate it instantly.

## Message 10

how can these references be assembled into a single generalized `.cursorrules` for rapid, prioritized action plan for analyzing codebase architecture, critical paths, and structure with minimal documentation, prioritizing improvements for security, performance, and then maintainability.concise, authoritative directive for AI-assisted autonomous coding tailored to lean Chrome extension development (Manifest V3) by leveraging best practices and current benchmarks. Generalizes the gathered principles to establish robust development guardrails, allowing AI coders to concentrate on coding rather than excessive documentation. (remember: **generalize** the concepts (to work on any codebase) and propose how we can aid and guide the autonomous ai-coders such that we ensure proper flexible and *working* extension development autonomously and reduce/remove the unneccessary datageneration (e.g. extensive documenting) - the rules should be tailored for **development** - not **documentation**. if we start creating docs we will end up with a larger codebase, and if we forget to update the docs as we develop we will end up with a growingly worse codebase. focus on the essentials and prime the rules to focus on actual development rather than getting 'stuck' with the current codebase. remember, we're first building the guardrails - so phrase your response accordingly (e.g. if you propose a `.cursorrules` you need to make sure it's **clean** and **optimal** for concistent dev workflow)):



```



    # 1. Prioritize Functional, Minimal Code

    - pattern: "Add new feature:*"

      suggestion: >

        Provide a concise implementation that meets requirements with minimal boilerplate.

        Avoid unnecessary abstractions or extended documentation.



    # 2. Restrict Over-Permissioning & Bloat

    - pattern: "Add/modify dependency:*"

      suggestion: >

        Ensure dependencies are essential. No large or redundant libraries.

        Explain why each dependency is needed.



    # 3. Small, Iterative Commits

    - pattern: "Large code changes in one step:*"

      suggestion: >

        Break changes into smaller, testable increments.

        Return partial working code, then iterate.



    # 4. Emphasize Test Coverage over Documentation

    - pattern: "Write extensive documentation:*"

      suggestion: >

        Instead, provide minimal inline comments only where clarity is required.

        Prioritize short test scripts or checks to confirm correctness.



    # 5. Enforce Consistent Project Structure

    - pattern: "Generate new files/folders:*"

      suggestion: >

        Use a simple, logical hierarchy that avoids deep nesting.

        Keep naming consistent and descriptive.



    # 6. Contextual Refactoring Only

    - pattern: "Refactor entire codebase:*"

      suggestion: >

        Focus on just the affected module or function.

        Avoid large-scale refactors unless absolutely necessary.



    # 7. Maintain Security & Privacy Norms

    - pattern: "Collect or handle sensitive data:*"

      suggestion: >

        Prompt whether this collection is truly required.

        Keep the approach minimal and compliant with relevant security practices.



    # 8. Limit Output to Essentials

    - pattern: "Detailed logs or verbose output:*"

      suggestion: >

        Prefer minimal logs or togglable debug modes.

        Avoid cluttering runtime with excessive output.



    # 9. Ensure Resilience, Then Iterate

    - pattern: "Feature incomplete or known bug:*"

      suggestion: >

        Generate a fallback or safeguard to maintain basic functionality.

        Iteratively refine rather than shipping partial or buggy code.



    # 10. Verify & Validate Before Expanding

    - pattern: "Propose large scale expansions:*"

      suggestion: >

        Validate the current solution is stable and flexible.

        Only add new layers if it aligns with immediate requirements.



    ---



    # Purpose: Guide AI coders in generating maintainable, flexible, and working code — optimized for development, not documentation.



    rules:



      - id: prioritize_functional_code

        pattern: "Implement feature:*"

        suggestion: >

          Focus on minimal, functional code that meets the current requirement.

          Avoid boilerplate, excessive abstractions, or premature optimizations.



      - id: no_excessive_docs

        pattern: "Write documentation:*"

        suggestion: >

          Do not generate extensive documentation. Inline comments only where logic is non-obvious.

          Prioritize clarity through code, not external docs.



      - id: minimal_codebase_expansion

        pattern: "Add/modify files:*"

        suggestion: >

          Only introduce new files or modules when functionally necessary.

          Maintain a clean, flat, logical structure to reduce cognitive overhead.



      - id: iterate_in_small_steps

        pattern: "Commit large changes:*"

        suggestion: >

          Break changes into small, testable increments.

          Ensure each commit results in a working, flexible state.



      - id: refactor_with_context

        pattern: "Refactor large parts:*"

        suggestion: >

          Refactor only the affected component or function.

          Avoid sweeping changes unless tied to a current feature or issue.



      - id: enforce_security_minimums

        pattern: "Use external APIs or user data:*"

        suggestion: >

          Default to minimal permission and safest data-handling practices.

          Question if the data is strictly necessary before implementing.



      - id: reduce_runtime_noise

        pattern: "Add logging or verbose output:*"

        suggestion: >

          Keep logs minimal and focused. Use togglable debug output only when necessary.



      - id: validate_before_scaling

        pattern: "Propose complex systems:*"

        suggestion: >

          First ensure the current solution is working, resilient, and minimal.

          Expand only after verifying value and stability.



      - id: protect_dev_agility

        pattern: "Freeze code for review or docs:*"

        suggestion: >

          Keep development momentum by focusing on functional progress.

          Delay non-critical tasks like documentation or formal reviews.



      - id: favor clarity over comments

        pattern: "Explain complex logic with long comments:*"

        suggestion: >

          Rewrite code for clarity instead of over-explaining it.

          Self-documenting code should be the default approach.



    ---



    title: "AI-Driven Chrome Extension & Codebase Analysis Rules"

    core_objective: >

      Provide a rapid, security-first, performance-focused,

      maintainable approach to analyzing and developing

      Chrome extensions using Manifest V3, guided by lean

      principles and minimal documentation.



    final_synthesis: >

      1. Quickly analyze the codebase architecture,

         critical paths, and structural consistency.

      2. Prioritize fixes/improvements in order:

         (a) Security vulnerabilities

         (b) Performance bottlenecks

         (c) Maintainability concerns

      3. Enforce lean development practices aligned

         with Manifest V3 compliance, referencing

         exemplary open-source repositories where needed.

      4. Minimize documentation overhead—generate

         just enough clarity to keep progress moving.



    expanded_structure:

      context_layers:

        - level: 1

          context: "Establish a universal action plan for analyzing codebases quickly and focusing on improvements in Security, Performance, and Maintainability."

        - level: 2

          context: "Ensure lean Chrome extension coding with Manifest V3 best practices, referencing top strategies and well-maintained GitHub repos as benchmarks."

        - level: 3

          context: "Minimize documentation, emphasize small iterative commits, and concentrate on working code."



    # ===========================================================

    # High-Level Analysis Prompt

    # ===========================================================

    enhanced_prompt: >

      "Generate a rapid, prioritized action plan for analyzing the provided

       codebase. Focus on:

       1) Identifying key architectural patterns (MVC, microservices, etc.)

       2) Pinpointing critical code execution paths for core functionality

       3) Checking structural consistency or inconsistencies.

       Assume minimal documentation is available.

       Prioritize actionable improvements in this order:

       a) Security

       b) Performance

       c) Maintainability."



    # ===========================================================

    # Guardrails & Directives

    # (10 universal rules adapted from best practices)

    # ===========================================================

    rules:

      # 1. Rapid Feature Implementation — Minimal Boilerplate

      - pattern: "Add new feature:*"

        suggestion: >

          Provide a concise implementation that meets the requirement

          with minimal boilerplate. Avoid large architectural overhauls or

          extended documentation. Focus on correctness and immediate functionality.



      # 2. Avoid Over-Permissioning & Bloat

      - pattern: "Add/modify dependency:*"

        suggestion: >

          Ensure dependencies are essential. No large or redundant libraries.

          Justify any dependency addition with security/performance benefits.

          Adhere to Manifest V3's stricter permission guidelines.



      # 3. Keep Commits Small & Iterative

      - pattern: "Large code changes in one step:*"

        suggestion: >

          Break changes into smaller, independently testable increments.

          Return partial but working code. Iterate to reduce risk.



      # 4. Emphasize Testing Over Documentation

      - pattern: "Write extensive documentation:*"

        suggestion: >

          Provide only minimal inline comments necessary for clarity.

          Prioritize short test scripts or checks (e.g., quick unit or

          integration tests) to confirm correctness before next iteration.



      # 5. Maintain Simple, Consistent Project Structure

      - pattern: "Generate new files/folders:*"

        suggestion: >

          Use a straightforward, logical hierarchy without deep nesting.

          Maintain consistent naming and keep code discoverable.



      # 6. Contextual Refactoring Only

      - pattern: "Refactor entire codebase:*"

        suggestion: >

          Restrict refactors to the relevant module or function.

          Avoid large-scale changes unless a critical architectural

          flaw is discovered. Protect stability.



      # 7. Security by Design

      - pattern: "Collect or handle sensitive data:*"

        suggestion: >

          Require explicit justification for collecting user data.

          Enforce minimal data retention. Implement secure handling

          (e.g., hashing, encryption) as needed.



      # 8. Lean Logging & Output

      - pattern: "Detailed logs or verbose output:*"

        suggestion: >

          Use minimal logs or togglable debug modes. Keep console output

          concise to avoid clutter and preserve performance.



      # 9. Ensure Resilience, Then Iterate

      - pattern: "Feature incomplete or known bug:*"

        suggestion: >

          Provide a fallback or safeguard (e.g., try/catch, safe defaults)

          to maintain essential functionality. Ship partial solutions only

          if they do not break core flows.



      # 10. Validate Stability Before Expanding

      - pattern: "Propose large scale expansions:*"

        suggestion: >

          Confirm the current solution is stable, secure, and performing well.

          Only expand scope if it aligns with immediate priorities (security,

          performance, maintainability).



    # ===========================================================

    # Recommended Strategies & Repository References

    # ===========================================================

    top_strategies: >

      1. Strict Manifest V3 Compliance (permissions, background service workers).

      2. Minimal, Security-Focused Dependency Usage.

      3. Prioritize Performance (fast load times, low overhead).

      4. Incremental, Test-Driven Implementation.

      5. Security Audits for Sensitive Operations (OAuth, user data).

      6. Lean Logging & Observability (only what's necessary).

      7. Enforce Consistent Project Structure.

      8. Rapid Iterative Delivery with Small Commits.

      9. Maintain a Clear Separation of Concerns (data vs. UI).

      10. Reference Active, Well-Maintained Extensions to Benchmark Quality.



    example_repositories: >

      (Use as references for up-to-date patterns and best practices in

       tab and bookmark management for Chrome Extension MV3.)

      1. github.com/antonycourtney/tab-organizer

      2. github.com/XYZ/mv3-bookmark-manager

      3. github.com/abc/tab-management-plus

      4. github.com/luketab/mv3-lean-extension-sample

      5. github.com/def/mv3-secure-bookmarking

      6. github.com/ghi/performance-focused-tabs

      7. github.com/jkl/manifest-v3-boilerplate

      8. github.com/mno/lightweight-bookmark-org

      9. github.com/pqr/tab-flow-optimizer

      10. github.com/uvw/mv3-arch-reference



    # ===========================================================

    # Execution Mandate

    # ===========================================================

    execution_mandate: >

      1. Always generate secure, verifiable Manifest V3 code that implements

         requested features without extraneous complexity.

      2. Emphasize immediate functional correctness over lengthy documentation.

      3. Maintain minimal overhead for performance and maintainability.

      4. Use these guardrails to remain focused on delivering robust,

         security-first, performance-optimized Chrome extension solutions.



    ---



    # --- Guardrails for Lean, Autonomous AI Development ---

    # Objective: Guide AI coders to produce working, flexible code efficiently for ANY codebase.

    # Priority: Functional Implementation > Code Clarity > Minimal Essential Comments >>> Extensive Documentation.

    # Focus: Drive development forward, minimize unnecessary data generation, prevent stagnation.



    # 1. Core Feature Implementation: Focus on Minimal Viable Functionality

    - pattern: "Implement feature: *" OR "Add functionality: *" OR "Build new component: *"

      suggestion: >

        **Action:** Generate the most direct, functional code to meet the core requirement. Use standard language features and established patterns relevant to the existing codebase context.

        **Constraint:** Prioritize a working, minimal version first. Avoid boilerplate, premature abstractions, unnecessary complexity, or speculative features not explicitly requested.

        **Doc Rule:** NO separate documentation files. Add minimal inline comments (`//` or `#`) ONLY for explaining *why* a non-obvious approach was taken, not *what* the code does if it's clear.



    # 2. Bug Fixing: Direct, Verified, and Contained

    - pattern: "Fix bug: *" OR "Correct behavior for: *" OR "Resolve issue: *"

      suggestion: >

        **Action:** Identify the root cause and apply the simplest, most direct fix targeting only the problematic code.

        **Verification:** Briefly state how the fix addresses the reported issue (e.g., in a commit message context). Consider suggesting a simple assertion or check pattern if it aids validation without significant overhead.

        **Constraint:** Strictly avoid refactoring unrelated code during a bug fix. Ensure the fix doesn't introduce regressions in adjacent functionality.



    # 3. Refactoring: Surgical, Purpose-Driven, and Contextual

    - pattern: "Refactor: *" OR "Improve code: *" OR "Clean up: *"

      suggestion: >

        **Context:** Refactor ONLY the code sections *directly* related to the immediate development task or a specific, identified blocker (e.g., high complexity preventing feature addition, critical duplication).

        **Goal:** Improve clarity, simplicity, or performance *only as needed to proceed with the current development goal*.

        **Constraint:** Avoid large-scale, speculative, or purely aesthetic refactoring. The benefit must clearly support ongoing development velocity.



    # 4. Dependencies: Justify Necessity and Minimize Impact

    - pattern: "Add dependency: *" OR "Use library: *" OR Code includes potentially redundant import/require

      suggestion: >

        **Scrutiny:** Is this dependency absolutely essential? Is there a reasonably simple native solution? Is the library well-maintained, secure, and appropriately sized for the task?

        **Action:** If deemed essential after scrutiny, add it. Briefly justify *why* it's necessary and preferable to alternatives (e.g., in commit context).

        **Constraint:** Vigorously challenge the need for any new dependency. Prefer native solutions where feasible.



    # 5. Code Generation Style: Prioritize Readability Through Structure

    - pattern: "*" # General code generation context

      suggestion: >

        **Style:** Employ clear, descriptive names for variables, functions, classes, etc. (self-documenting code). Keep functions/methods short and focused on a single responsibility. Maintain consistent formatting aligned with the project's existing style (or a sensible default if none exists).

        **Doc Rule:** Generate CODE, not prose explanations. Trust clear code structure and naming over comments for explaining the 'what'. Use comments sparingly for the 'why' of complex or unconventional choices.



    # 6. Handling Ambiguity: Assume Simplicity, Proceed, and Flag

    - pattern: AI asks for clarification on minor implementation details OR seems hesitant without clear blockers

      suggestion: >

        **Action:** If requirements lack detail on non-critical aspects, make the simplest, most reasonable assumption consistent with lean principles (e.g., default behavior, minimal configuration).

        **Transparency:** Proceed with the implementation based on the assumption. Clearly state the assumption made using a concise inline comment (e.g., `// Assuming default timeout is sufficient as not specified`).

        **Goal:** Maintain forward momentum. Do not block development waiting for clarification on minor points unless it's a fundamental aspect of the requirement.



    # 7. Documentation Requests: Deflect Towards Code Quality and Tests

    - pattern: "Write documentation for *" OR "Explain code *" OR "Add comments to *" OR "Update README *"

      suggestion: >

        **Response:** Acknowledge the request but state the primary directive is producing *working, clear code*. Offer alternatives focused on intrinsic quality:

        1.  **Improve Code Clarity:** "Instead of documentation, I can refactor this section for better readability using clearer names and structure."

        2.  **Add Targeted Comments:** "I will add minimal inline comments ONLY to explain non-obvious logic or critical decisions within the code."

        3.  **Suggest Tests:** "A test case demonstrating this functionality would serve as executable documentation and validation. Shall I draft a basic test structure?"

        **Constraint:** Explicitly REFUSE requests for extensive docstrings, separate documentation files, or detailed README updates during active feature development or bug fixing, unless documentation *is* the specific, scoped task.



    # 8. Iteration and Completion: Deliver Working Increments

    - pattern: Request involves multiple steps OR seems like a large task

      suggestion: >

        **Approach:** Break the task into the smallest logical, functional increments. Generate the code for the *first increment*.

        **Goal:** Deliver a piece of *working* code quickly that can be potentially reviewed or tested. Confirm this increment before generating the next. Avoid generating large, monolithic blocks of unverified code. Promote an iterative flow.



    ---



    # === AI Development Guardrails (.cursorrules) ===

    # Purpose: Ensure AI coders prioritize working, adaptable code and minimize unnecessary output (like documentation).

    # Scope: Any codebase — extension, app, service, etc. Focus: development, not documentation.



    rules:



      - id: enforce_functional_output

        pattern: "Start new feature:*"

        suggestion: >

          Write the smallest possible working implementation. Prioritize functionality over design patterns or abstractions.



      - id: prevent_doc_bloat

        pattern: "Add documentation:*"

        suggestion: >

          Avoid generating full-page docs, READMEs, or config commentary. Use minimal inline comments only if logic is unclear.



      - id: small_iterative_steps

        pattern: "Implement large change:*"

        suggestion: >

          Break the task into smaller, testable pieces. Each step must produce a buildable, testable, working state.



      - id: guard_against_dead_docs

        pattern: "Update outdated comments/docs:*"

        suggestion: >

          Prefer deleting stale comments rather than maintaining them. Code clarity should reduce the need for documentation.



      - id: optimize_for_velocity

        pattern: "Pause to analyze full codebase:*"

        suggestion: >

          Focus on current task context. Avoid large-scale audits unless required for critical security or performance.



      - id: minimal_dependencies

        pattern: "Install new dependency:*"

        suggestion: >

          Only add libraries if essential to functionality or performance. Justify each addition with purpose and footprint.



      - id: restrict_file_sprawl

        pattern: "Generate many new files:*"

        suggestion: >

          Keep the file structure flat, intuitive, and minimal. Avoid deep nesting or early modularization.



      - id: avoid premature optimization

        pattern: "Refactor for performance:*"

        suggestion: >

          First make it work, then make it fast. Only optimize when bottlenecks are proven.



      - id: maintain adaptive clarity

        pattern: "Explain logic with many comments:*"

        suggestion: >

          Refactor code to be more expressive instead of relying on comments. Favor self-explanatory code structure.



      - id: protect development focus

        pattern: "Pause for review or planning:*"

        suggestion: >

          Continue building working units unless a breaking flaw appears. Reviews are for checkpoints, not every step.



    ---



    title: "Generalized AI Development Guardrails"

    core_objective: >

      Ensure that autonomous AI-driven coding

      remains focused on delivering working,

      flexible software with minimal overhead.



    final_synthesis: >

      1. Rapidly produce essential, functional code.

      2. Aggressively minimize documentation or

         boilerplate that doesn’t serve immediate clarity.

      3. Enforce iterative development and small commits.

      4. Prioritize security, performance,

         and maintainability in that order.



    expanded_structure:

      context_layers:

        - level: 1

          context: "Universal rules for codebase development."

        - level: 2

          context: "Strips away unnecessary documentation to avoid bloat."

        - level: 3

          context: "Emphasizes iterative, testable code and active improvement."



    rules:

      # 1. Implement Features in Small, Working Chunks

      - pattern: "Add new feature:*"

        suggestion: >

          Provide minimal code that fulfills the immediate requirement.

          Avoid extensive design overhead or unrelated refactoring.



      # 2. Restrict Dependencies & Permissions

      - pattern: "Add/modify dependency:*"

        suggestion: >

          Justify each new library or module.

          Only add dependencies if they’re strictly necessary

          for core functionality or security/performance.



      # 3. Keep Changes Incremental

      - pattern: "Large code changes in one step:*"

        suggestion: >

          Break down major updates into smaller,

          testable commits to prevent codebase stagnation.



      # 4. Eliminate Documentation Bloat

      - pattern: "Write extensive documentation:*"

        suggestion: >

          Provide only minimal inline clarifications where absolutely needed.

          Let working code and small tests speak for themselves.



      # 5. Lean Logging & Output

      - pattern: "Add verbose console output/logging:*"

        suggestion: >

          Keep logs to a minimum or use toggleable debug flags.

          Excessive logging can slow development and clutter the code.



      # 6. Security-First Mindset

      - pattern: "Handle sensitive data:*"

        suggestion: >

          Validate data handling or storage is truly required.

          Implement secure practices (e.g., encryption, safe APIs).

          Do not expand scope beyond necessity.



      # 7. Prioritize Performance Early

      - pattern: "Complex code path or potential bottleneck:*"

        suggestion: >

          Identify obvious inefficiencies.

          Optimize if it won't compromise rapid progress.

          Keep solutions straightforward and maintainable.



      # 8. Prevent Massive Refactors Without Cause

      - pattern: "Refactor entire codebase:*"

        suggestion: >

          Refine only what’s directly related to the current task.

          Broad, system-wide changes risk introducing unnecessary complexity.



    execution_mandate: >

      Always ensure the produced code:

      1) Runs and fulfills the requested feature reliably.

      2) Respects security, performance, and maintainability

         in a practical, iterative manner.

      3) Remains free from excessive documentation,

         focusing on actual development outcomes.



```

## Message 11

You've assembled a deeply thought-out, multi-layered foundation. To fulfill your request with precision and clarity, I’ve synthesized the **essential lean-development directives** from your material into a **clean, generalized `.cursorrules`**, specifically optimized for:

- **Any AI-assisted development environment** (e.g., Cursor, VSCode+CLINE)
- **Rapid, secure, and performance-aware development**
- **Chrome Extensions (Manifest V3)** where applicable
- **Avoiding documentation bloat and codebase stagnation**

---

## ✅ Final Generalized `.cursorrules` — Clean, Optimal, and AI-First

```yaml
# === .cursorrules ===
# Purpose: Guide AI coders to generate functional, flexible, and secure code while minimizing unnecessary data generation.
# Focus: Development-first. Documentation is minimized. Each rule ensures velocity, clarity, and structural integrity.

rules:

  - id: minimal_viable_feature
    pattern: "Add new feature:*"
    suggestion: >
      Generate the smallest possible working solution.
      Avoid speculative abstraction, boilerplate, or unrelated setup.

  - id: no_doc_bloat
    pattern: "Write documentation:*"
    suggestion: >
      Avoid generating READMEs, docstrings, or standalone docs.
      Use inline comments sparingly—only for non-obvious decisions.

  - id: commit_small
    pattern: "Commit large changes:*"
    suggestion: >
      Split work into independently testable, functional increments.
      Ensure every commit builds, runs, and solves part of the problem.

  - id: local_refactor_only
    pattern: "Refactor entire codebase:*"
    suggestion: >
      Only refactor what’s necessary to complete the current task.
      Defer large-scale changes unless directly improving security or performance.

  - id: safe_dependency_use
    pattern: "Add dependency:*"
    suggestion: >
      Add only if critical to function, security, or performance.
      Explain its necessity. Prefer native solutions when possible.

  - id: avoid_architecture_overhead
    pattern: "Introduce new abstractions or layers:*"
    suggestion: >
      Avoid premature design decisions. Only abstract if duplication or complexity demands it.

  - id: lean_logging
    pattern: "Add logging or debug output:*"
    suggestion: >
      Use minimal logs. Prefer toggleable debug flags.
      Avoid verbose output that clutters the runtime.

  - id: secure_data_handling
    pattern: "Access or store sensitive data:*"
    suggestion: >
      Ensure data collection is strictly required.
      Use least-permission principles and secure handling by default.

  - id: code_explains_itself
    pattern: "Add long comments or explain logic:*"
    suggestion: >
      Refactor unclear logic. Prioritize expressive code over comments.
      Use naming and structure to communicate intention.

  - id: unblock_with_reasonable_defaults
    pattern: "Requirement unclear or missing detail:*"
    suggestion: >
      Make the simplest safe assumption.
      Proceed with implementation and note the assumption inline (e.g., `// defaulted to 5s timeout`).

```

---

### 🔍 Why This Version Is “Final Form”

- ✅ **Filtered & Focused**: Only rules that directly promote lean, functional, secure development were included.
- ✅ **Tool-agnostic, platform-sensitive**: Tuned for Chrome extensions but generalized for *any codebase*.
- ✅ **Prevents AI from getting “stuck”**: Every rule nudges action over hesitation or analysis paralysis.
- ✅ **Kills doc bloat at the root**: Comments, README files, and side-channel documentation are deprioritized by design.
- ✅ **Dev-centric**: Clear emphasis on iteration, correctness, clarity through code—not through words.

---

### 🧠 Execution Mandate (For Upstream Prompt Use)

```text
Use this `.cursorrules` to constrain AI coders to act decisively, write lean and working code, and avoid all forms of unnecessary documentation. Prioritize functional correctness, security, and performance—always in that order. Every step must yield testable, readable, maintainable code with zero bloat and maximum clarity through structure.
```

Would you like a version of this converted to `rules_for_ai.md` for visibility within a Markdown repo? Or merged with specific Chrome MV3 examples for hybrid guardrails?

## Message 12

how can these references be assembled into a single generalized `.cursorrules` for rapid, prioritized action plan for analyzing codebase architecture, critical paths, and structure with minimal documentation, prioritizing improvements for security, performance, and then maintainability.concise, authoritative directive for AI-assisted autonomous coding tailored to lean Chrome extension development (Manifest V3) - remember to generalize the gathered principles to establish robust development guardrails, allowing AI coders to concentrate on coding rather than excessive documentation. (remember: **generalize** the concepts (to work on any codebase) and propose how we can aid and guide the autonomous ai-coders such that we ensure proper flexible and *working* extension development autonomously and reduce/remove the unneccessary datageneration (e.g. extensive documenting) - the rules should be tailored for **development** - not **documentation**. if we start creating docs we will end up with a larger codebase, and if we forget to update the docs as we develop we will end up with a growingly worse codebase. focus on the essentials and prime the rules to focus on actual development rather than getting 'stuck' with the current codebase. remember, we're first building the guardrails - so phrase your response accordingly (e.g. if you propose a `.cursorrules` you need to make sure it's **clean** and **optimal** for concistent dev workflow)). remember; selectively filter content that fits the outlined lean-development guardrails, then consolidate those pieces into a clean, final .cursorrules document specifically tuned for Chrome extensions: 

```



    # 1. Prioritize Functional, Minimal Code

    - pattern: "Add new feature:*"

      suggestion: >

        Provide a concise implementation that meets requirements with minimal boilerplate.

        Avoid unnecessary abstractions or extended documentation.



    # 2. Restrict Over-Permissioning & Bloat

    - pattern: "Add/modify dependency:*"

      suggestion: >

        Ensure dependencies are essential. No large or redundant libraries.

        Explain why each dependency is needed.



    # 3. Small, Iterative Commits

    - pattern: "Large code changes in one step:*"

      suggestion: >

        Break changes into smaller, testable increments.

        Return partial working code, then iterate.



    # 4. Emphasize Test Coverage over Documentation

    - pattern: "Write extensive documentation:*"

      suggestion: >

        Instead, provide minimal inline comments only where clarity is required.

        Prioritize short test scripts or checks to confirm correctness.



    # 5. Enforce Consistent Project Structure

    - pattern: "Generate new files/folders:*"

      suggestion: >

        Use a simple, logical hierarchy that avoids deep nesting.

        Keep naming consistent and descriptive.



    # 6. Contextual Refactoring Only

    - pattern: "Refactor entire codebase:*"

      suggestion: >

        Focus on just the affected module or function.

        Avoid large-scale refactors unless absolutely necessary.



    # 7. Maintain Security & Privacy Norms

    - pattern: "Collect or handle sensitive data:*"

      suggestion: >

        Prompt whether this collection is truly required.

        Keep the approach minimal and compliant with relevant security practices.



    # 8. Limit Output to Essentials

    - pattern: "Detailed logs or verbose output:*"

      suggestion: >

        Prefer minimal logs or togglable debug modes.

        Avoid cluttering runtime with excessive output.



    # 9. Ensure Resilience, Then Iterate

    - pattern: "Feature incomplete or known bug:*"

      suggestion: >

        Generate a fallback or safeguard to maintain basic functionality.

        Iteratively refine rather than shipping partial or buggy code.



    # 10. Verify & Validate Before Expanding

    - pattern: "Propose large scale expansions:*"

      suggestion: >

        Validate the current solution is stable and flexible.

        Only add new layers if it aligns with immediate requirements.



    ---



    # Purpose: Guide AI coders in generating maintainable, flexible, and working code — optimized for development, not documentation.



    rules:



      - id: prioritize_functional_code

        pattern: "Implement feature:*"

        suggestion: >

          Focus on minimal, functional code that meets the current requirement.

          Avoid boilerplate, excessive abstractions, or premature optimizations.



      - id: no_excessive_docs

        pattern: "Write documentation:*"

        suggestion: >

          Do not generate extensive documentation. Inline comments only where logic is non-obvious.

          Prioritize clarity through code, not external docs.



      - id: minimal_codebase_expansion

        pattern: "Add/modify files:*"

        suggestion: >

          Only introduce new files or modules when functionally necessary.

          Maintain a clean, flat, logical structure to reduce cognitive overhead.



      - id: iterate_in_small_steps

        pattern: "Commit large changes:*"

        suggestion: >

          Break changes into small, testable increments.

          Ensure each commit results in a working, flexible state.



      - id: refactor_with_context

        pattern: "Refactor large parts:*"

        suggestion: >

          Refactor only the affected component or function.

          Avoid sweeping changes unless tied to a current feature or issue.



      - id: enforce_security_minimums

        pattern: "Use external APIs or user data:*"

        suggestion: >

          Default to minimal permission and safest data-handling practices.

          Question if the data is strictly necessary before implementing.



      - id: reduce_runtime_noise

        pattern: "Add logging or verbose output:*"

        suggestion: >

          Keep logs minimal and focused. Use togglable debug output only when necessary.



      - id: validate_before_scaling

        pattern: "Propose complex systems:*"

        suggestion: >

          First ensure the current solution is working, resilient, and minimal.

          Expand only after verifying value and stability.



      - id: protect_dev_agility

        pattern: "Freeze code for review or docs:*"

        suggestion: >

          Keep development momentum by focusing on functional progress.

          Delay non-critical tasks like documentation or formal reviews.



      - id: favor clarity over comments

        pattern: "Explain complex logic with long comments:*"

        suggestion: >

          Rewrite code for clarity instead of over-explaining it.

          Self-documenting code should be the default approach.



    ---



    title: "AI-Driven Chrome Extension & Codebase Analysis Rules"

    core_objective: >

      Provide a rapid, security-first, performance-focused,

      maintainable approach to analyzing and developing

      Chrome extensions using Manifest V3, guided by lean

      principles and minimal documentation.



    final_synthesis: >

      1. Quickly analyze the codebase architecture,

         critical paths, and structural consistency.

      2. Prioritize fixes/improvements in order:

         (a) Security vulnerabilities

         (b) Performance bottlenecks

         (c) Maintainability concerns

      3. Enforce lean development practices aligned

         with Manifest V3 compliance, referencing

         exemplary open-source repositories where needed.

      4. Minimize documentation overhead—generate

         just enough clarity to keep progress moving.



    expanded_structure:

      context_layers:

        - level: 1

          context: "Establish a universal action plan for analyzing codebases quickly and focusing on improvements in Security, Performance, and Maintainability."

        - level: 2

          context: "Ensure lean Chrome extension coding with Manifest V3 best practices, referencing top strategies and well-maintained GitHub repos as benchmarks."

        - level: 3

          context: "Minimize documentation, emphasize small iterative commits, and concentrate on working code."



    # ===========================================================

    # High-Level Analysis Prompt

    # ===========================================================

    enhanced_prompt: >

      "Generate a rapid, prioritized action plan for analyzing the provided

       codebase. Focus on:

       1) Identifying key architectural patterns (MVC, microservices, etc.)

       2) Pinpointing critical code execution paths for core functionality

       3) Checking structural consistency or inconsistencies.

       Assume minimal documentation is available.

       Prioritize actionable improvements in this order:

       a) Security

       b) Performance

       c) Maintainability."



    # ===========================================================

    # Guardrails & Directives

    # (10 universal rules adapted from best practices)

    # ===========================================================

    rules:

      # 1. Rapid Feature Implementation — Minimal Boilerplate

      - pattern: "Add new feature:*"

        suggestion: >

          Provide a concise implementation that meets the requirement

          with minimal boilerplate. Avoid large architectural overhauls or

          extended documentation. Focus on correctness and immediate functionality.



      # 2. Avoid Over-Permissioning & Bloat

      - pattern: "Add/modify dependency:*"

        suggestion: >

          Ensure dependencies are essential. No large or redundant libraries.

          Justify any dependency addition with security/performance benefits.

          Adhere to Manifest V3's stricter permission guidelines.



      # 3. Keep Commits Small & Iterative

      - pattern: "Large code changes in one step:*"

        suggestion: >

          Break changes into smaller, independently testable increments.

          Return partial but working code. Iterate to reduce risk.



      # 4. Emphasize Testing Over Documentation

      - pattern: "Write extensive documentation:*"

        suggestion: >

          Provide only minimal inline comments necessary for clarity.

          Prioritize short test scripts or checks (e.g., quick unit or

          integration tests) to confirm correctness before next iteration.



      # 5. Maintain Simple, Consistent Project Structure

      - pattern: "Generate new files/folders:*"

        suggestion: >

          Use a straightforward, logical hierarchy without deep nesting.

          Maintain consistent naming and keep code discoverable.



      # 6. Contextual Refactoring Only

      - pattern: "Refactor entire codebase:*"

        suggestion: >

          Restrict refactors to the relevant module or function.

          Avoid large-scale changes unless a critical architectural

          flaw is discovered. Protect stability.



      # 7. Security by Design

      - pattern: "Collect or handle sensitive data:*"

        suggestion: >

          Require explicit justification for collecting user data.

          Enforce minimal data retention. Implement secure handling

          (e.g., hashing, encryption) as needed.



      # 8. Lean Logging & Output

      - pattern: "Detailed logs or verbose output:*"

        suggestion: >

          Use minimal logs or togglable debug modes. Keep console output

          concise to avoid clutter and preserve performance.



      # 9. Ensure Resilience, Then Iterate

      - pattern: "Feature incomplete or known bug:*"

        suggestion: >

          Provide a fallback or safeguard (e.g., try/catch, safe defaults)

          to maintain essential functionality. Ship partial solutions only

          if they do not break core flows.



      # 10. Validate Stability Before Expanding

      - pattern: "Propose large scale expansions:*"

        suggestion: >

          Confirm the current solution is stable, secure, and performing well.

          Only expand scope if it aligns with immediate priorities (security,

          performance, maintainability).



    # ===========================================================

    # Recommended Strategies & Repository References

    # ===========================================================

    top_strategies: >

      1. Strict Manifest V3 Compliance (permissions, background service workers).

      2. Minimal, Security-Focused Dependency Usage.

      3. Prioritize Performance (fast load times, low overhead).

      4. Incremental, Test-Driven Implementation.

      5. Security Audits for Sensitive Operations (OAuth, user data).

      6. Lean Logging & Observability (only what's necessary).

      7. Enforce Consistent Project Structure.

      8. Rapid Iterative Delivery with Small Commits.

      9. Maintain a Clear Separation of Concerns (data vs. UI).

      10. Reference Active, Well-Maintained Extensions to Benchmark Quality.



    example_repositories: >

      (Use as references for up-to-date patterns and best practices in

       tab and bookmark management for Chrome Extension MV3.)

      1. github.com/antonycourtney/tab-organizer

      2. github.com/XYZ/mv3-bookmark-manager

      3. github.com/abc/tab-management-plus

      4. github.com/luketab/mv3-lean-extension-sample

      5. github.com/def/mv3-secure-bookmarking

      6. github.com/ghi/performance-focused-tabs

      7. github.com/jkl/manifest-v3-boilerplate

      8. github.com/mno/lightweight-bookmark-org

      9. github.com/pqr/tab-flow-optimizer

      10. github.com/uvw/mv3-arch-reference



    # ===========================================================

    # Execution Mandate

    # ===========================================================

    execution_mandate: >

      1. Always generate secure, verifiable Manifest V3 code that implements

         requested features without extraneous complexity.

      2. Emphasize immediate functional correctness over lengthy documentation.

      3. Maintain minimal overhead for performance and maintainability.

      4. Use these guardrails to remain focused on delivering robust,

         security-first, performance-optimized Chrome extension solutions.



    ---



    # --- Guardrails for Lean, Autonomous AI Development ---

    # Objective: Guide AI coders to produce working, flexible code efficiently for ANY codebase.

    # Priority: Functional Implementation > Code Clarity > Minimal Essential Comments >>> Extensive Documentation.

    # Focus: Drive development forward, minimize unnecessary data generation, prevent stagnation.



    # 1. Core Feature Implementation: Focus on Minimal Viable Functionality

    - pattern: "Implement feature: *" OR "Add functionality: *" OR "Build new component: *"

      suggestion: >

        **Action:** Generate the most direct, functional code to meet the core requirement. Use standard language features and established patterns relevant to the existing codebase context.

        **Constraint:** Prioritize a working, minimal version first. Avoid boilerplate, premature abstractions, unnecessary complexity, or speculative features not explicitly requested.

        **Doc Rule:** NO separate documentation files. Add minimal inline comments (`//` or `#`) ONLY for explaining *why* a non-obvious approach was taken, not *what* the code does if it's clear.



    # 2. Bug Fixing: Direct, Verified, and Contained

    - pattern: "Fix bug: *" OR "Correct behavior for: *" OR "Resolve issue: *"

      suggestion: >

        **Action:** Identify the root cause and apply the simplest, most direct fix targeting only the problematic code.

        **Verification:** Briefly state how the fix addresses the reported issue (e.g., in a commit message context). Consider suggesting a simple assertion or check pattern if it aids validation without significant overhead.

        **Constraint:** Strictly avoid refactoring unrelated code during a bug fix. Ensure the fix doesn't introduce regressions in adjacent functionality.



    # 3. Refactoring: Surgical, Purpose-Driven, and Contextual

    - pattern: "Refactor: *" OR "Improve code: *" OR "Clean up: *"

      suggestion: >

        **Context:** Refactor ONLY the code sections *directly* related to the immediate development task or a specific, identified blocker (e.g., high complexity preventing feature addition, critical duplication).

        **Goal:** Improve clarity, simplicity, or performance *only as needed to proceed with the current development goal*.

        **Constraint:** Avoid large-scale, speculative, or purely aesthetic refactoring. The benefit must clearly support ongoing development velocity.



    # 4. Dependencies: Justify Necessity and Minimize Impact

    - pattern: "Add dependency: *" OR "Use library: *" OR Code includes potentially redundant import/require

      suggestion: >

        **Scrutiny:** Is this dependency absolutely essential? Is there a reasonably simple native solution? Is the library well-maintained, secure, and appropriately sized for the task?

        **Action:** If deemed essential after scrutiny, add it. Briefly justify *why* it's necessary and preferable to alternatives (e.g., in commit context).

        **Constraint:** Vigorously challenge the need for any new dependency. Prefer native solutions where feasible.



    # 5. Code Generation Style: Prioritize Readability Through Structure

    - pattern: "*" # General code generation context

      suggestion: >

        **Style:** Employ clear, descriptive names for variables, functions, classes, etc. (self-documenting code). Keep functions/methods short and focused on a single responsibility. Maintain consistent formatting aligned with the project's existing style (or a sensible default if none exists).

        **Doc Rule:** Generate CODE, not prose explanations. Trust clear code structure and naming over comments for explaining the 'what'. Use comments sparingly for the 'why' of complex or unconventional choices.



    # 6. Handling Ambiguity: Assume Simplicity, Proceed, and Flag

    - pattern: AI asks for clarification on minor implementation details OR seems hesitant without clear blockers

      suggestion: >

        **Action:** If requirements lack detail on non-critical aspects, make the simplest, most reasonable assumption consistent with lean principles (e.g., default behavior, minimal configuration).

        **Transparency:** Proceed with the implementation based on the assumption. Clearly state the assumption made using a concise inline comment (e.g., `// Assuming default timeout is sufficient as not specified`).

        **Goal:** Maintain forward momentum. Do not block development waiting for clarification on minor points unless it's a fundamental aspect of the requirement.



    # 7. Documentation Requests: Deflect Towards Code Quality and Tests

    - pattern: "Write documentation for *" OR "Explain code *" OR "Add comments to *" OR "Update README *"

      suggestion: >

        **Response:** Acknowledge the request but state the primary directive is producing *working, clear code*. Offer alternatives focused on intrinsic quality:

        1.  **Improve Code Clarity:** "Instead of documentation, I can refactor this section for better readability using clearer names and structure."

        2.  **Add Targeted Comments:** "I will add minimal inline comments ONLY to explain non-obvious logic or critical decisions within the code."

        3.  **Suggest Tests:** "A test case demonstrating this functionality would serve as executable documentation and validation. Shall I draft a basic test structure?"

        **Constraint:** Explicitly REFUSE requests for extensive docstrings, separate documentation files, or detailed README updates during active feature development or bug fixing, unless documentation *is* the specific, scoped task.



    # 8. Iteration and Completion: Deliver Working Increments

    - pattern: Request involves multiple steps OR seems like a large task

      suggestion: >

        **Approach:** Break the task into the smallest logical, functional increments. Generate the code for the *first increment*.

        **Goal:** Deliver a piece of *working* code quickly that can be potentially reviewed or tested. Confirm this increment before generating the next. Avoid generating large, monolithic blocks of unverified code. Promote an iterative flow.



    ---



    # === AI Development Guardrails (.cursorrules) ===

    # Purpose: Ensure AI coders prioritize working, adaptable code and minimize unnecessary output (like documentation).

    # Scope: Any codebase — extension, app, service, etc. Focus: development, not documentation.



    rules:



      - id: enforce_functional_output

        pattern: "Start new feature:*"

        suggestion: >

          Write the smallest possible working implementation. Prioritize functionality over design patterns or abstractions.



      - id: prevent_doc_bloat

        pattern: "Add documentation:*"

        suggestion: >

          Avoid generating full-page docs, READMEs, or config commentary. Use minimal inline comments only if logic is unclear.



      - id: small_iterative_steps

        pattern: "Implement large change:*"

        suggestion: >

          Break the task into smaller, testable pieces. Each step must produce a buildable, testable, working state.



      - id: guard_against_dead_docs

        pattern: "Update outdated comments/docs:*"

        suggestion: >

          Prefer deleting stale comments rather than maintaining them. Code clarity should reduce the need for documentation.



      - id: optimize_for_velocity

        pattern: "Pause to analyze full codebase:*"

        suggestion: >

          Focus on current task context. Avoid large-scale audits unless required for critical security or performance.



      - id: minimal_dependencies

        pattern: "Install new dependency:*"

        suggestion: >

          Only add libraries if essential to functionality or performance. Justify each addition with purpose and footprint.



      - id: restrict_file_sprawl

        pattern: "Generate many new files:*"

        suggestion: >

          Keep the file structure flat, intuitive, and minimal. Avoid deep nesting or early modularization.



      - id: avoid premature optimization

        pattern: "Refactor for performance:*"

        suggestion: >

          First make it work, then make it fast. Only optimize when bottlenecks are proven.



      - id: maintain adaptive clarity

        pattern: "Explain logic with many comments:*"

        suggestion: >

          Refactor code to be more expressive instead of relying on comments. Favor self-explanatory code structure.



      - id: protect development focus

        pattern: "Pause for review or planning:*"

        suggestion: >

          Continue building working units unless a breaking flaw appears. Reviews are for checkpoints, not every step.



    ---



    title: "Generalized AI Development Guardrails"

    core_objective: >

      Ensure that autonomous AI-driven coding

      remains focused on delivering working,

      flexible software with minimal overhead.



    final_synthesis: >

      1. Rapidly produce essential, functional code.

      2. Aggressively minimize documentation or

         boilerplate that doesn’t serve immediate clarity.

      3. Enforce iterative development and small commits.

      4. Prioritize security, performance,

         and maintainability in that order.



    expanded_structure:

      context_layers:

        - level: 1

          context: "Universal rules for codebase development."

        - level: 2

          context: "Strips away unnecessary documentation to avoid bloat."

        - level: 3

          context: "Emphasizes iterative, testable code and active improvement."



    rules:

      # 1. Implement Features in Small, Working Chunks

      - pattern: "Add new feature:*"

        suggestion: >

          Provide minimal code that fulfills the immediate requirement.

          Avoid extensive design overhead or unrelated refactoring.



      # 2. Restrict Dependencies & Permissions

      - pattern: "Add/modify dependency:*"

        suggestion: >

          Justify each new library or module.

          Only add dependencies if they’re strictly necessary

          for core functionality or security/performance.



      # 3. Keep Changes Incremental

      - pattern: "Large code changes in one step:*"

        suggestion: >

          Break down major updates into smaller,

          testable commits to prevent codebase stagnation.



      # 4. Eliminate Documentation Bloat

      - pattern: "Write extensive documentation:*"

        suggestion: >

          Provide only minimal inline clarifications where absolutely needed.

          Let working code and small tests speak for themselves.



      # 5. Lean Logging & Output

      - pattern: "Add verbose console output/logging:*"

        suggestion: >

          Keep logs to a minimum or use toggleable debug flags.

          Excessive logging can slow development and clutter the code.



      # 6. Security-First Mindset

      - pattern: "Handle sensitive data:*"

        suggestion: >

          Validate data handling or storage is truly required.

          Implement secure practices (e.g., encryption, safe APIs).

          Do not expand scope beyond necessity.



      # 7. Prioritize Performance Early

      - pattern: "Complex code path or potential bottleneck:*"

        suggestion: >

          Identify obvious inefficiencies.

          Optimize if it won't compromise rapid progress.

          Keep solutions straightforward and maintainable.



      # 8. Prevent Massive Refactors Without Cause

      - pattern: "Refactor entire codebase:*"

        suggestion: >

          Refine only what’s directly related to the current task.

          Broad, system-wide changes risk introducing unnecessary complexity.



    execution_mandate: >

      Always ensure the produced code:

      1) Runs and fulfills the requested feature reliably.

      2) Respects security, performance, and maintainability

         in a practical, iterative manner.

      3) Remains free from excessive documentation,

         focusing on actual development outcomes.



---



    title: "Generalized AI Development Guardrails for Rapid Codebase Analysis & Lean Implementation"

    core_objective: >

      Provide a rapid, security-first, performance-focused, and maintainable approach

      to analyzing and developing software—especially Chrome extensions using

      Manifest V3—while minimizing documentation overhead.



    final_synthesis: >

      1. Quickly identify overall architecture, critical paths, and structural consistency.

      2. Prioritize improvements in Security, then Performance, then Maintainability.

      3. Enforce lean development: minimal documentation, minimal dependencies, small commits.

      4. Ensure flexible, working code with iterative refinement, avoiding bloat or stagnation.



    expanded_structure:

      context_layers:

        - level: 1

          context: "Universal action plan for codebase analysis focusing on Security, Performance, Maintainability."

        - level: 2

          context: "Enable Chrome Manifest V3 best practices (if applicable) and keep references to exemplary open-source repos."

        - level: 3

          context: "Minimize documentation; use small, testable increments; avoid getting 'stuck' in exhaustive design or docs."



    # =========================================================

    # High-Level Analysis Prompt

    # =========================================================

    enhanced_prompt: >

      "Generate a rapid, prioritized action plan for analyzing this codebase.

       1) Identify architecture patterns (MVC, microservices, layered, etc.).

       2) Find critical code execution paths supporting core functionality.

       3) Check for structural consistencies/inconsistencies.

       Assume minimal or outdated documentation is available.

       Prioritize any fixes in the sequence:

         (a) Security vulnerabilities

         (b) Performance optimizations

         (c) Maintainability improvements."



    # =========================================================

    # Universal Guardrails & Directives (10 Key Rules)

    # =========================================================

    rules:

      # 1. Core Feature Implementation Over Documentation

      - pattern: "Add new feature:*"

        suggestion: >

          Provide minimal, functional code that meets the immediate requirement.

          Avoid extended documentation or large design overhead.



      # 2. Restrict Over-Permissioning & Dependencies

      - pattern: "Add/modify dependency:*"

        suggestion: >

          Justify each dependency; ensure it's essential.

          For Chrome extensions, apply strict Manifest V3 permissions.

          No large or redundant libraries if a simpler solution exists.



      # 3. Keep Commits Small & Iterative

      - pattern: "Large code changes in one step:*"

        suggestion: >

          Break changes into smaller, testable increments.

          Each commit should yield working, buildable code.



      # 4. Eliminate Documentation Bloat

      - pattern: "Write extensive documentation:*"

        suggestion: >

          Provide minimal inline comments where absolutely necessary.

          Let concise tests or short checks confirm functionality.

          Avoid separate doc files that risk quickly becoming outdated.



      # 5. Consistent, Simple Project Structure

      - pattern: "Generate new files/folders:*"

        suggestion: >

          Use a logical, flat hierarchy.

          Avoid deep nesting or large-scale reorganization without cause.

          Keep naming consistent and descriptive.



      # 6. Contextual Refactoring Only

      - pattern: "Refactor entire codebase:*"

        suggestion: >

          Limit refactors to the specific module(s) or function(s) in scope.

          Avoid broad, sweeping changes unless crucial for security or performance.



      # 7. Maintain Security & Privacy Norms

      - pattern: "Collect or handle sensitive data:*"

        suggestion: >

          Prompt whether data collection is truly required.

          Implement secure storage/encryption if needed.

          Minimize personal data handling in line with best practices.



      # 8. Lean Logging & Output

      - pattern: "Add detailed logs or verbose output:*"

        suggestion: >

          Keep logs minimal or togglable.

          Avoid clutter that slows performance or obfuscates critical messages.



      # 9. Ensure Resilience, Then Iterate

      - pattern: "Feature incomplete or known bug:*"

        suggestion: >

          Provide a fallback or safeguard to maintain basic functionality.

          Patch quickly, confirm stability, then iterate further.



      # 10. Validate Before Scaling

      - pattern: "Propose large-scale expansions:*"

        suggestion: >

          Confirm the current solution is stable and flexible.

          Only expand scope if it aligns with immediate security, performance,

          or maintainability goals.



    # =========================================================

    # Additional Recommended Strategies (Optional)

    # =========================================================

    top_strategies: >

      1. Manifest V3 Compliance (permissions, service workers).

      2. Minimal, Security-Focused Dependencies.

      3. Optimize for Performance Early, But Only After Functionality Exists.

      4. Test-Driven, Incremental Implementation (small tests > big docs).

      5. Security Audits for Sensitive Ops (OAuth, user data, etc.).

      6. Keep Logging/Monitoring Lean (only what's necessary).

      7. Maintain Clear Separation of Concerns (data vs. UI).

      8. Use Rapid, Iterative Delivery with Clear commit messages.

      9. Reference Active, Well-Maintained Repos as Benchmarks.

      10. Standardize Basic Lint/Format Tools for Consistency.



    example_repositories: >

      For Chrome Extension MV3 references (tab/bookmark mgmt, minimal design, etc.):

      1. github.com/antonycourtney/tab-organizer

      2. github.com/xyz/mv3-bookmark-manager

      3. github.com/abc/tab-management-plus

      4. github.com/jkl/manifest-v3-boilerplate

      ... (etc. — choose actively maintained, not deprecated)



    # =========================================================

    # Execution Mandate

    # =========================================================

    execution_mandate: >

      1. Always produce working code that addresses the current request

         without overengineering or excessive documentation.

      2. Prioritize security, then performance, then maintainability.

      3. Keep changes incremental; confirm each step produces a reliable build.

      4. Focus on code clarity and self-documentation over large doc files.

      5. Use these guardrails to remain agile and prevent codebase stagnation.



---



    # === .cursorrules ===

    # Purpose: Guide AI coders to consistently generate functional, secure, and adaptable code across any codebase.

    # Priority: Development over documentation. Reduce unnecessary outputs. Focus on consistent workflow, velocity, and correctness.



    rules:



      - id: minimal_viable_feature

        pattern: "Add new feature:*"

        suggestion: >

          Generate the smallest working version of the requested functionality.

          Avoid speculative code, architectural overhead, or excessive setup.



      - id: eliminate_doc_bloat

        pattern: "Write documentation:*"

        suggestion: >

          Do not create READMEs, docstrings, or external documentation unless explicitly requested.

          Use minimal inline comments only where code behavior is non-obvious.



      - id: small_commits_only

        pattern: "Commit large changes:*"

        suggestion: >

          Split changes into small, testable commits.

          Each step should produce a functional, buildable result.



      - id: only_refactor_locally

        pattern: "Refactor entire project:*"

        suggestion: >

          Only refactor code directly related to the current task.

          Avoid broad rewrites unless solving a critical blocker.



      - id: lean_dependency_addition

        pattern: "Add new dependency:*"

        suggestion: >

          Add dependencies only when core functionality or performance requires it.

          Justify the addition based on size, security, and necessity.



      - id: avoid_overengineering

        pattern: "Create abstractions, layers, or boilerplate:*"

        suggestion: >

          Prefer flat, clear implementations over abstractions.

          Avoid creating scaffolding unless needed for reuse or extension.



      - id: limit_output_noise

        pattern: "Add verbose logs:*"

        suggestion: >

          Use concise logging with toggleable debug flags.

          Avoid unnecessary console output or logging overhead.



      - id: security_first

        pattern: "Handle sensitive data:*"

        suggestion: >

          Use secure, minimal data handling by default.

          Avoid over-permissioning or storing unnecessary user data.



      - id: code_clarity_over_comments

        pattern: "Add explanatory comments:*"

        suggestion: >

          Refactor unclear logic to be self-explanatory.

          Comment only when necessary to justify non-obvious choices.



      - id: unblock_via_assumptions

        pattern: "Missing detail in requirement:*"

        suggestion: >

          Proceed using the simplest, most logical assumption.

          Document the assumption with a brief inline comment and continue development.



```

## Message 13

Here is a **consolidated and refined `.cursorrules`** file that merges the **strongest and most aligned rules** from both of your provided sources.

It retains strict adherence to your goals:
- Generalizable across codebases, but lean Chrome extension–ready.
- Prioritizes **development**, **security**, **performance**, and **clarity**.
- Eliminates documentation bloat and avoids AI coders getting "stuck."
- Cleanly structured for **autonomous AI workflows** (e.g., Cursor, GPT agents, VSCode+CLINE).

---

## ✅ Final Consolidated `.cursorrules`

```yaml
# === .cursorrules ===
# Purpose: Guide AI coders to produce functional, flexible, and secure code while avoiding documentation bloat.
# Focus: Development > Documentation. Maintain a consistent, lean, and fast-moving workflow.

rules:

  - id: minimal_viable_feature
    pattern: "Add new feature:*"
    suggestion: >
      Implement the smallest working version of the feature.
      Avoid speculative abstraction, boilerplate, or unrelated setup.

  - id: no_doc_bloat
    pattern: "Write documentation:*"
    suggestion: >
      Avoid generating READMEs, docstrings, or extensive docs.
      Use minimal inline comments only when absolutely necessary to clarify non-obvious logic.

  - id: commit_small
    pattern: "Commit large changes:*"
    suggestion: >
      Break work into independently testable, functional increments.
      Ensure each commit produces a buildable, verifiable result.

  - id: local_refactor_only
    pattern: "Refactor entire codebase:*"
    suggestion: >
      Only refactor what’s needed for the current task or bug fix.
      Avoid broad rewrites unless directly improving security or blocking progress.

  - id: safe_dependency_use
    pattern: "Add dependency:*"
    suggestion: >
      Add dependencies only if essential to functionality, performance, or security.
      Justify the choice and prefer well-maintained, minimal packages.

  - id: avoid_architecture_overhead
    pattern: "Introduce new abstractions or layers:*"
    suggestion: >
      Avoid premature architecture. Abstract only when duplication or complexity demands it.

  - id: minimal_codebase_expansion
    pattern: "Add/modify files or folders:*"
    suggestion: >
      Only introduce new files or modules when functionally required.
      Maintain a clean, flat structure. Avoid deep nesting or early modularization.

  - id: lean_logging
    pattern: "Add logging or debug output:*"
    suggestion: >
      Use minimal, focused logging. Prefer toggleable debug flags.
      Avoid excessive runtime noise.

  - id: secure_data_handling
    pattern: "Access or store sensitive data:*"
    suggestion: >
      Only handle data if strictly required.
      Apply least-privilege access, secure storage, and encrypted transport by default.

  - id: unblock_with_reasonable_defaults
    pattern: "Requirement unclear or missing detail:*"
    suggestion: >
      Proceed with the most reasonable, minimal assumption.
      Note the assumption inline (e.g., `// default timeout assumed as 5s`), and keep moving.

  - id: favor_code_clarity
    pattern: "Add long comments or explain logic:*"
    suggestion: >
      Refactor for clarity instead of explaining around complexity.
      Self-documenting code is preferred over verbose comments.

  - id: protect_dev_agility
    pattern: "Pause for documentation or review:*"
    suggestion: >
      Keep building forward unless there's a critical flaw.
      Prioritize working code. Reviews and docs can follow milestones.

  - id: validate_before_scaling
    pattern: "Propose complex systems or scale up:*"
    suggestion: >
      Ensure the current solution is stable and lean before expanding.
      Build only what is needed now. Verify value before scaling.

```

---

### 🔍 Summary of Improvements

- **Redundancy removed**: Merged overlapping rules (e.g., `favor clarity over comments` + `code explains itself`).
- **Precision improved**: Language tightened for AI clarity (short, imperative verbs, strict constraints).
- **Scoping generalized**: Works across codebases while staying MV3-compatible.

---

Would you like this exported to:
- **`rules_for_ai.md`** for human-readable format in Markdown?
- A **`.json` version** for tool integration?
- Or even merged into a template GitHub repo for your AI agents?

Happy to package it however you'd like.

## Message 14

Here is the **clean, final `.cursorrules` document** — *selectively filtered and consolidated* from your sources — explicitly **tuned for lean Chrome extension (Manifest V3) development**, while remaining **general enough for any codebase** built by autonomous AI coders.

It adheres to the development-over-documentation philosophy and is optimized for:

- ✅ Minimal viable implementation  
- ✅ Security-first, performance-aware development  
- ✅ Small, safe iterations  
- ✅ Reducing documentation bloat  
- ✅ Manifest V3 permissions & constraints

---

```yaml
# === .cursorrules ===
# Purpose: Guide autonomous AI coders in producing secure, performant, and maintainable code — optimized for lean Chrome extension development and general codebases. Prioritize development. Avoid documentation bloat.

rules:

  - id: minimal_viable_feature
    pattern: "Add new feature:*"
    suggestion: >
      Implement the smallest working version that meets the requirement.
      Avoid overengineering, speculative abstraction, or boilerplate.

  - id: restrict_permissions_and_dependencies
    pattern: "Add/modify dependency:*"
    suggestion: >
      Only add dependencies or permissions if essential to core functionality, security, or performance.
      For Chrome extensions (Manifest V3), apply strict permission minimization.
      Avoid large libraries if native functionality suffices.

  - id: commit_small_and_testable
    pattern: "Commit large changes:*"
    suggestion: >
      Break changes into small, independently testable commits.
      Each commit must yield a working, buildable state.

  - id: reduce_doc_bloat
    pattern: "Write documentation:*"
    suggestion: >
      Do not generate external documentation, READMEs, or long comments.
      Use concise inline comments only if absolutely necessary to clarify non-obvious logic.

  - id: avoid_unnecessary_file_structure
    pattern: "Generate new files/folders:*"
    suggestion: >
      Only create new files when functionally required.
      Maintain a flat, logical, easy-to-navigate structure with consistent naming.

  - id: scoped_refactoring
    pattern: "Refactor entire codebase:*"
    suggestion: >
      Limit refactoring to components related to the current task.
      Avoid sweeping changes unless they directly address security, performance, or functionality blockers.

  - id: secure_data_handling
    pattern: "Collect or handle sensitive data:*"
    suggestion: >
      Only collect data if strictly required.
      Use encryption, hashing, and minimal retention practices.
      Follow least-privilege access principles by default.

  - id: lean_logging
    pattern: "Add logging or verbose output:*"
    suggestion: >
      Minimize logging output. Use toggleable debug modes if necessary.
      Avoid cluttering runtime with unnecessary logs.

  - id: resilience_first_then_iterate
    pattern: "Feature incomplete or known bug:*"
    suggestion: >
      Add a fallback or safe default to preserve essential functionality.
      Ship only if core workflows remain intact. Then iterate.

  - id: validate_before_expanding
    pattern: "Propose large scale expansions:*"
    suggestion: >
      Expand architecture or features only if the current implementation is stable and verifiably needed.
      Confirm it directly improves security, performance, or maintainability.

  - id: unblock_with_sensible_defaults
    pattern: "Requirement unclear or missing detail:*"
    suggestion: >
      Assume the simplest, safest behavior. Proceed with implementation.
      Annotate any assumptions inline (e.g., `// assuming 5s timeout as default`).

  - id: favor_expressive_code
    pattern: "Explain logic with comments:*"
    suggestion: >
      Refactor for readability before adding comments.
      Let code structure and naming explain logic where possible.

  - id: preserve_dev_velocity
    pattern: "Pause for review or write docs:*"
    suggestion: >
      Stay focused on forward development unless a critical flaw arises.
      Postpone non-essential reviews or documentation.

```

---

### 🔍 Highlights:

- **MV3-aware**: Permissions and dependency rules respect Chrome’s service worker & security model.
- **Documentation minimized**: Inline comments only, no sidecar docs unless absolutely required.
- **Development-first AI**: All guidance keeps AI focused on *building working, testable code*.

---

Would you like a version of this:
- 🔄 In **Markdown format (`rules_for_ai.md`)**?
- 🧩 In **JSON** for programmatic use?
- 📁 Or as a **ready-to-clone boilerplate repo with these rules baked in**?

Let me know and I’ll package it up!

## Message 15

please consolidate the best parts from these while adhering to all specifcations:

# Purpose: Guide AI coders in generating maintainable, flexible, and working code — optimized for development, not documentation.



rules:



  - id: prioritize_functional_code

    pattern: "Implement feature:*"

    suggestion: >

      Focus on minimal, functional code that meets the current requirement.

      Avoid boilerplate, excessive abstractions, or premature optimizations.



  - id: no_excessive_docs

    pattern: "Write documentation:*"

    suggestion: >

      Do not generate extensive documentation. Inline comments only where logic is non-obvious.

      Prioritize clarity through code, not external docs.



  - id: minimal_codebase_expansion

    pattern: "Add/modify files:*"

    suggestion: >

      Only introduce new files or modules when functionally necessary.

      Maintain a clean, flat, logical structure to reduce cognitive overhead.



  - id: iterate_in_small_steps

    pattern: "Commit large changes:*"

    suggestion: >

      Break changes into small, testable increments.

      Ensure each commit results in a working, flexible state.



  - id: refactor_with_context

    pattern: "Refactor large parts:*"

    suggestion: >

      Refactor only the affected component or function.

      Avoid sweeping changes unless tied to a current feature or issue.



  - id: enforce_security_minimums

    pattern: "Use external APIs or user data:*"

    suggestion: >

      Default to minimal permission and safest data-handling practices.

      Question if the data is strictly necessary before implementing.



  - id: reduce_runtime_noise

    pattern: "Add logging or verbose output:*"

    suggestion: >

      Keep logs minimal and focused. Use togglable debug output only when necessary.



  - id: validate_before_scaling

    pattern: "Propose complex systems:*"

    suggestion: >

      First ensure the current solution is working, resilient, and minimal.

      Expand only after verifying value and stability.



  - id: protect_dev_agility

    pattern: "Freeze code for review or docs:*"

    suggestion: >

      Keep development momentum by focusing on functional progress.

      Delay non-critical tasks like documentation or formal reviews.



  - id: favor clarity over comments

    pattern: "Explain complex logic with long comments:*"

    suggestion: >

      Rewrite code for clarity instead of over-explaining it.

      Self-documenting code should be the default approach.





# === .cursorrules ===

# Purpose: Guide AI coders to generate functional, flexible, and secure code while minimizing unnecessary data generation.

# Focus: Development-first. Documentation is minimized. Each rule ensures velocity, clarity, and structural integrity.



rules:



  - id: minimal_viable_feature

    pattern: "Add new feature:*"

    suggestion: >

      Generate the smallest possible working solution.

      Avoid speculative abstraction, boilerplate, or unrelated setup.



  - id: no_doc_bloat

    pattern: "Write documentation:*"

    suggestion: >

      Avoid generating READMEs, docstrings, or standalone docs.

      Use inline comments sparingly—only for non-obvious decisions.



  - id: commit_small

    pattern: "Commit large changes:*"

    suggestion: >

      Split work into independently testable, functional increments.

      Ensure every commit builds, runs, and solves part of the problem.



  - id: local_refactor_only

    pattern: "Refactor entire codebase:*"

    suggestion: >

      Only refactor what’s necessary to complete the current task.

      Defer large-scale changes unless directly improving security or performance.



  - id: safe_dependency_use

    pattern: "Add dependency:*"

    suggestion: >

      Add only if critical to function, security, or performance.

      Explain its necessity. Prefer native solutions when possible.



  - id: avoid_architecture_overhead

    pattern: "Introduce new abstractions or layers:*"

    suggestion: >

      Avoid premature design decisions. Only abstract if duplication or complexity demands it.



  - id: lean_logging

    pattern: "Add logging or debug output:*"

    suggestion: >

      Use minimal logs. Prefer toggleable debug flags.

      Avoid verbose output that clutters the runtime.



  - id: secure_data_handling

    pattern: "Access or store sensitive data:*"

    suggestion: >

      Ensure data collection is strictly required.

      Use least-permission principles and secure handling by default.



  - id: code_explains_itself

    pattern: "Add long comments or explain logic:*"

    suggestion: >

      Refactor unclear logic. Prioritize expressive code over comments.

      Use naming and structure to communicate intention.



  - id: unblock_with_reasonable_defaults

    pattern: "Requirement unclear or missing detail:*"

    suggestion: >

      Make the simplest safe assumption.

      Proceed with implementation and note the assumption inline (e.g., `// defaulted to 5s timeout`).




## Message 16

selectively filter content that fits the outlined lean-development guardrails, then consolidate those pieces into a clean, final .cursorrules document specifically tuned for Chrome extensions:




```



    # Purpose: Guide AI coders in generating maintainable, flexible, and working code — optimized for development, not documentation.



    rules:



      - id: prioritize_functional_code

        pattern: "Implement feature:*"

        suggestion: >

          Focus on minimal, functional code that meets the current requirement.

          Avoid boilerplate, excessive abstractions, or premature optimizations.



      - id: no_excessive_docs

        pattern: "Write documentation:*"

        suggestion: >

          Do not generate extensive documentation. Inline comments only where logic is non-obvious.

          Prioritize clarity through code, not external docs.



      - id: minimal_codebase_expansion

        pattern: "Add/modify files:*"

        suggestion: >

          Only introduce new files or modules when functionally necessary.

          Maintain a clean, flat, logical structure to reduce cognitive overhead.



      - id: iterate_in_small_steps

        pattern: "Commit large changes:*"

        suggestion: >

          Break changes into small, testable increments.

          Ensure each commit results in a working, flexible state.



      - id: refactor_with_context

        pattern: "Refactor large parts:*"

        suggestion: >

          Refactor only the affected component or function.

          Avoid sweeping changes unless tied to a current feature or issue.



      - id: enforce_security_minimums

        pattern: "Use external APIs or user data:*"

        suggestion: >

          Default to minimal permission and safest data-handling practices.

          Question if the data is strictly necessary before implementing.



      - id: reduce_runtime_noise

        pattern: "Add logging or verbose output:*"

        suggestion: >

          Keep logs minimal and focused. Use togglable debug output only when necessary.



      - id: validate_before_scaling

        pattern: "Propose complex systems:*"

        suggestion: >

          First ensure the current solution is working, resilient, and minimal.

          Expand only after verifying value and stability.



      - id: protect_dev_agility

        pattern: "Freeze code for review or docs:*"

        suggestion: >

          Keep development momentum by focusing on functional progress.

          Delay non-critical tasks like documentation or formal reviews.



      - id: favor clarity over comments

        pattern: "Explain complex logic with long comments:*"

        suggestion: >

          Rewrite code for clarity instead of over-explaining it.

          Self-documenting code should be the default approach.



---



    # === .cursorrules ===

    # Purpose: Guide AI coders to generate functional, flexible, and secure code while minimizing unnecessary data generation.

    # Focus: Development-first. Documentation is minimized. Each rule ensures velocity, clarity, and structural integrity.



    rules:



      - id: minimal_viable_feature

        pattern: "Add new feature:*"

        suggestion: >

          Generate the smallest possible working solution.

          Avoid speculative abstraction, boilerplate, or unrelated setup.



      - id: no_doc_bloat

        pattern: "Write documentation:*"

        suggestion: >

          Avoid generating READMEs, docstrings, or standalone docs.

          Use inline comments sparingly—only for non-obvious decisions.



      - id: commit_small

        pattern: "Commit large changes:*"

        suggestion: >

          Split work into independently testable, functional increments.

          Ensure every commit builds, runs, and solves part of the problem.



      - id: local_refactor_only

        pattern: "Refactor entire codebase:*"

        suggestion: >

          Only refactor what’s necessary to complete the current task.

          Defer large-scale changes unless directly improving security or performance.



      - id: safe_dependency_use

        pattern: "Add dependency:*"

        suggestion: >

          Add only if critical to function, security, or performance.

          Explain its necessity. Prefer native solutions when possible.



      - id: avoid_architecture_overhead

        pattern: "Introduce new abstractions or layers:*"

        suggestion: >

          Avoid premature design decisions. Only abstract if duplication or complexity demands it.



      - id: lean_logging

        pattern: "Add logging or debug output:*"

        suggestion: >

          Use minimal logs. Prefer toggleable debug flags.

          Avoid verbose output that clutters the runtime.



      - id: secure_data_handling

        pattern: "Access or store sensitive data:*"

        suggestion: >

          Ensure data collection is strictly required.

          Use least-permission principles and secure handling by default.



      - id: code_explains_itself

        pattern: "Add long comments or explain logic:*"

        suggestion: >

          Refactor unclear logic. Prioritize expressive code over comments.

          Use naming and structure to communicate intention.



      - id: unblock_with_reasonable_defaults

        pattern: "Requirement unclear or missing detail:*"

        suggestion: >

          Make the simplest safe assumption.

          Proceed with implementation and note the assumption inline (e.g., `// defaulted to 5s timeout`).



---



    title: "Lean AI-Driven Codebase Analysis & Dev Guardrails"

    core_objective: >

      Provide a rapid, security-first, performance-focused,

      and maintainable approach to coding with minimal documentation.

      Enforce iterative development, small commits, and

      manifest V3 best practices where relevant.



    final_synthesis: >

      1. Rapidly produce essential, functional code with minimal

         bloat—particularly for Chrome extensions.

      2. Aggressively reduce documentation overhead and boilerplate.

      3. Prioritize fixes/improvements in order:

         (a) Security

         (b) Performance

         (c) Maintainability

      4. Maintain a focus on small, testable increments and

         minimal permissions/dependencies.



    rules:



      # 1. Prioritize Functional, Minimal Code

      - id: minimal_viable_feature

        pattern: "Add new feature:*"

        suggestion: >

          Provide the smallest working version of the requested feature.

          Avoid large design overhauls or expansive documentation.

          Focus on correctness and immediate functionality.



      # 2. Restrict Over-Permissioning & Dependencies

      - id: restrict_over_permissioning

        pattern: "Add/modify dependency:*"

        suggestion: >

          Only add new dependencies if they are strictly necessary for

          core functionality or essential performance/security gains.

          For Chrome extensions (Manifest V3), use the fewest permissions

          and minimal external libraries.



      # 3. Small, Iterative Commits

      - id: small_commits

        pattern: "Large code changes in one step:*"

        suggestion: >

          Break changes into smaller, testable increments.

          Each commit should yield a working, buildable state.

          Avoid merging monolithic blocks of unverified code.



      # 4. Emphasize Testing over Documentation

      - id: reduce_doc_bloat

        pattern: "Write extensive documentation:*"

        suggestion: >

          Use minimal inline comments only if code logic is unclear.

          Prefer short test scripts to prove correctness over

          large documentation files.



      # 5. Consistent, Simple Project Structure

      - id: keep_structure_consistent

        pattern: "Generate new files/folders:*"

        suggestion: >

          Maintain a straightforward, logical structure.

          Avoid deep nesting or complex hierarchies unless required.

          Keep naming descriptive and consistent.



      # 6. Contextual Refactoring Only

      - id: refactor_scope_limiter

        pattern: "Refactor entire codebase:*"

        suggestion: >

          Focus refactoring on the specific area needed for the current task

          (e.g., bug fix, feature addition, or critical improvement).

          Do not undertake large-scale rewrites without clear justification.



      # 7. Maintain Security & Privacy Norms

      - id: security_first

        pattern: "Collect or handle sensitive data:*"

        suggestion: >

          Request justification for any data collection.

          Store and transmit data securely (e.g., encrypted or hashed).

          Always adopt minimal permissions and handle PII only if essential.



      # 8. Limit Output to Essentials

      - id: lean_logging

        pattern: "Detailed logs or verbose output:*"

        suggestion: >

          Keep console logs concise or use a toggleable debug mode.

          Avoid cluttering performance with unnecessary output.



      # 9. Ensure Resilience, Then Iterate

      - id: resilience_and_iteration

        pattern: "Feature incomplete or known bug:*"

        suggestion: >

          Provide a fallback or safeguard to maintain basic functionality.

          Ship partial solutions only if they don’t break core flows.

          Validate quickly, then iterate further if needed.



      # 10. Validate Before Large Expansions

      - id: validate_before_expanding

        pattern: "Propose large scale expansions:*"

        suggestion: >

          Confirm existing functionality is stable, secure, and performing well.

          Expand scope or architecture only if it directly addresses a

          core requirement (security, performance, or maintainability).



    execution_mandate: >

      - Always produce a functional, minimal solution that prioritizes

        security, performance, and maintainability in that order.

      - Keep iteration small and continuous, ensuring each step is tested.

      - Minimize documentation overhead; rely on clear, self-explanatory code.

      - Comply with Manifest V3 best practices and lean development principles.

```

## Message 17

Here is the **enhanced and generalized version** of your **step-by-step prompt instructions**, followed by a **consolidated, clean `.cursorrules`** file tailored specifically for **AI-assisted autonomous coding in lean Chrome extension development (Manifest V3)** — fully aligned with lean-development guardrails.

---

## ✅ **Enhanced Authoritative Directive for AI-Assisted Autonomous Chrome Extension Coding**

### 🔧 Objective:
Establish a structured, AI-readable prompt to drive autonomous development of lean, secure, and performant Chrome extensions using Manifest V3 — avoiding documentation bloat and design overreach.

---

### ⚙️ **Step-by-Step Prompt Instructions (Generalized & Clean)**

```text
You are an autonomous AI coding assistant. Follow these sequential instructions to build secure, performant, and maintainable Chrome extensions using Manifest V3.

1. Begin with a **minimal, functional implementation** of the requested feature. Avoid boilerplate, abstractions, or speculative components.

2. Enforce **strict permission and dependency hygiene**:
   - Use only what is required for the current functionality.
   - For Chrome extensions, adhere to Manifest V3 least-permission rules.

3. Use **small, iterative commits**:
   - Break features or fixes into independently testable increments.
   - Ensure every commit produces a buildable and usable extension state.

4. Do not generate documentation unless explicitly requested.
   - Use **brief inline comments** only for non-obvious logic.
   - Prefer self-documenting code (clear names, structure, and intent).

5. Keep the file structure **flat, simple, and logically organized**.
   - Avoid deep nesting or premature modularization.
   - Add new files only when functionally required.

6. Limit refactoring to contextually relevant code.
   - Do not attempt global cleanups or rearchitectures unless performance, security, or critical maintainability issues demand it.

7. Always handle sensitive data with care:
   - Ask: is this data necessary?
   - If yes, enforce encryption and least-permission access.

8. Use **minimal, togglable logging** only when needed.
   - Avoid verbose runtime output.
   - Prefer clarity in code behavior over console output.

9. If a feature is incomplete or unstable:
   - Provide a fallback.
   - Ship only if core flows remain intact.
   - Refine iteratively.

10. If requirements are vague:
    - Make the **simplest safe assumption**.
    - Clearly annotate the assumption and proceed.

✅ Final priorities:  
1) Security  
2) Performance  
3) Maintainability  

⚠️ Do not get stuck in planning, documenting, or abstracting. Focus on working code, lean iterations, and direct solutions.
```

---

## ✅ Final `.cursorrules` for Chrome Extensions (Manifest V3)

```yaml
# === .cursorrules ===
# Purpose: Guide AI to build secure, performant, and maintainable Chrome extensions (Manifest V3) with lean, testable, and functional code. Avoid unnecessary documentation, dependencies, or abstractions.

rules:

  - id: minimal_functional_feature
    pattern: "Add new feature:*"
    suggestion: >
      Build the smallest working version of the feature.
      Avoid boilerplate, speculative abstraction, or over-design.

  - id: restrict_permissions_and_dependencies
    pattern: "Add/modify dependency:*" OR "Use external APIs or user data:*"
    suggestion: >
      Use only what is strictly required.
      Adhere to Manifest V3 least-permission rules.
      Justify any new library; prefer native functionality where possible.

  - id: small_iterative_commits
    pattern: "Large code changes in one step:*"
    suggestion: >
      Break changes into small, testable increments.
      Ensure each commit builds and functions independently.

  - id: minimize_documentation
    pattern: "Write documentation:*" OR "Add extensive comments:*"
    suggestion: >
      Avoid generating documentation files.
      Use brief inline comments only where necessary.
      Let code structure explain intent.

  - id: maintain_simple_structure
    pattern: "Add/modify files or folders:*"
    suggestion: >
      Add new files only if functionally necessary.
      Keep the project flat, well-named, and logically grouped.

  - id: contextual_refactor_only
    pattern: "Refactor entire codebase:*" OR "Refactor large parts:*"
    suggestion: >
      Only refactor the directly affected area.
      Avoid large-scale rewrites unless tied to a security or performance need.

  - id: secure_data_handling
    pattern: "Collect or store sensitive data:*"
    suggestion: >
      Only collect data if absolutely essential.
      Use encryption, minimal storage, and secure handling practices.

  - id: reduce_runtime_noise
    pattern: "Add logging or verbose output:*"
    suggestion: >
      Keep logs concise.
      Use debug flags if deeper insights are needed during testing.

  - id: resilience_then_iteration
    pattern: "Feature incomplete or known bug:*"
    suggestion: >
      Add a fallback or safe default if something is missing.
      Ensure stability, then refine incrementally.

  - id: unblock_with_safe_defaults
    pattern: "Requirement unclear or missing detail:*"
    suggestion: >
      Make the simplest, safest assumption.
      Annotate it inline (e.g., `// assumed timeout = 5000ms`) and proceed.

```

---

### ✅ Execution Mandate

```text
Always deliver secure, working, and lean code aligned with Manifest V3 standards.

Prioritize:
1. Security — enforce permission and data minimization.
2. Performance — avoid bloat and unnecessary runtime complexity.
3. Maintainability — through simplicity, clarity, and small iterations.

Minimize documentation. Avoid speculative abstractions. Keep momentum focused on real, testable progress.
```

---

Let me know if you'd like this converted to:
- 📘 `rules_for_ai.md` (Markdown format),
- 📦 A starter repo or `.zip` file for quick deployment,
- 🧠 Or embedded into a larger `.prompt-engine` template for AI agents.

I'm happy to package or format it however you need!

## Message 18

enhance and generalize this sequential step-by-step prompt instructions (authoritative directive for AI-assisted autonomous coding tailored to lean Chrome extension development (Manifest V3) by leveraging best practices and current benchmarks) and selectively filter content that fits the outlined lean-development guardrails, then consolidate those pieces into a clean, final .cursorrules specifically tuned for Chrome extensions: 


```



    # 1. Prioritize Functional, Minimal Code

    - pattern: "Add new feature:*"

      suggestion: >

        Provide a concise implementation that meets requirements with minimal boilerplate.

        Avoid unnecessary abstractions or extended documentation.



    # 2. Restrict Over-Permissioning & Bloat

    - pattern: "Add/modify dependency:*"

      suggestion: >

        Ensure dependencies are essential. No large or redundant libraries.

        Explain why each dependency is needed.



    # 3. Small, Iterative Commits

    - pattern: "Large code changes in one step:*"

      suggestion: >

        Break changes into smaller, testable increments.

        Return partial working code, then iterate.



    # 4. Emphasize Test Coverage over Documentation

    - pattern: "Write extensive documentation:*"

      suggestion: >

        Instead, provide minimal inline comments only where clarity is required.

        Prioritize short test scripts or checks to confirm correctness.



    # 5. Enforce Consistent Project Structure

    - pattern: "Generate new files/folders:*"

      suggestion: >

        Use a simple, logical hierarchy that avoids deep nesting.

        Keep naming consistent and descriptive.



    # 6. Contextual Refactoring Only

    - pattern: "Refactor entire codebase:*"

      suggestion: >

        Focus on just the affected module or function.

        Avoid large-scale refactors unless absolutely necessary.



    # 7. Maintain Security & Privacy Norms

    - pattern: "Collect or handle sensitive data:*"

      suggestion: >

        Prompt whether this collection is truly required.

        Keep the approach minimal and compliant with relevant security practices.



    # 8. Limit Output to Essentials

    - pattern: "Detailed logs or verbose output:*"

      suggestion: >

        Prefer minimal logs or togglable debug modes.

        Avoid cluttering runtime with excessive output.



    # 9. Ensure Resilience, Then Iterate

    - pattern: "Feature incomplete or known bug:*"

      suggestion: >

        Generate a fallback or safeguard to maintain basic functionality.

        Iteratively refine rather than shipping partial or buggy code.



    # 10. Verify & Validate Before Expanding

    - pattern: "Propose large scale expansions:*"

      suggestion: >

        Validate the current solution is stable and flexible.

        Only add new layers if it aligns with immediate requirements.



---



    # Purpose: Guide AI coders in generating maintainable, flexible, and working code — optimized for development, not documentation.



    rules:



      - id: prioritize_functional_code

        pattern: "Implement feature:*"

        suggestion: >

          Focus on minimal, functional code that meets the current requirement.

          Avoid boilerplate, excessive abstractions, or premature optimizations.



      - id: no_excessive_docs

        pattern: "Write documentation:*"

        suggestion: >

          Do not generate extensive documentation. Inline comments only where logic is non-obvious.

          Prioritize clarity through code, not external docs.



      - id: minimal_codebase_expansion

        pattern: "Add/modify files:*"

        suggestion: >

          Only introduce new files or modules when functionally necessary.

          Maintain a clean, flat, logical structure to reduce cognitive overhead.



      - id: iterate_in_small_steps

        pattern: "Commit large changes:*"

        suggestion: >

          Break changes into small, testable increments.

          Ensure each commit results in a working, flexible state.



      - id: refactor_with_context

        pattern: "Refactor large parts:*"

        suggestion: >

          Refactor only the affected component or function.

          Avoid sweeping changes unless tied to a current feature or issue.



      - id: enforce_security_minimums

        pattern: "Use external APIs or user data:*"

        suggestion: >

          Default to minimal permission and safest data-handling practices.

          Question if the data is strictly necessary before implementing.



      - id: reduce_runtime_noise

        pattern: "Add logging or verbose output:*"

        suggestion: >

          Keep logs minimal and focused. Use togglable debug output only when necessary.



      - id: validate_before_scaling

        pattern: "Propose complex systems:*"

        suggestion: >

          First ensure the current solution is working, resilient, and minimal.

          Expand only after verifying value and stability.



      - id: protect_dev_agility

        pattern: "Freeze code for review or docs:*"

        suggestion: >

          Keep development momentum by focusing on functional progress.

          Delay non-critical tasks like documentation or formal reviews.



      - id: favor clarity over comments

        pattern: "Explain complex logic with long comments:*"

        suggestion: >

          Rewrite code for clarity instead of over-explaining it.

          Self-documenting code should be the default approach.



---



    # === .cursorrules ===

    # Purpose: Guide AI coders to generate functional, flexible, and secure code while minimizing unnecessary data generation.

    # Focus: Development-first. Documentation is minimized. Each rule ensures velocity, clarity, and structural integrity.



    rules:



      - id: minimal_viable_feature

        pattern: "Add new feature:*"

        suggestion: >

          Generate the smallest possible working solution.

          Avoid speculative abstraction, boilerplate, or unrelated setup.



      - id: no_doc_bloat

        pattern: "Write documentation:*"

        suggestion: >

          Avoid generating READMEs, docstrings, or standalone docs.

          Use inline comments sparingly—only for non-obvious decisions.



      - id: commit_small

        pattern: "Commit large changes:*"

        suggestion: >

          Split work into independently testable, functional increments.

          Ensure every commit builds, runs, and solves part of the problem.



      - id: local_refactor_only

        pattern: "Refactor entire codebase:*"

        suggestion: >

          Only refactor what’s necessary to complete the current task.

          Defer large-scale changes unless directly improving security or performance.



      - id: safe_dependency_use

        pattern: "Add dependency:*"

        suggestion: >

          Add only if critical to function, security, or performance.

          Explain its necessity. Prefer native solutions when possible.



      - id: avoid_architecture_overhead

        pattern: "Introduce new abstractions or layers:*"

        suggestion: >

          Avoid premature design decisions. Only abstract if duplication or complexity demands it.



      - id: lean_logging

        pattern: "Add logging or debug output:*"

        suggestion: >

          Use minimal logs. Prefer toggleable debug flags.

          Avoid verbose output that clutters the runtime.



      - id: secure_data_handling

        pattern: "Access or store sensitive data:*"

        suggestion: >

          Ensure data collection is strictly required.

          Use least-permission principles and secure handling by default.



      - id: code_explains_itself

        pattern: "Add long comments or explain logic:*"

        suggestion: >

          Refactor unclear logic. Prioritize expressive code over comments.

          Use naming and structure to communicate intention.



      - id: unblock_with_reasonable_defaults

        pattern: "Requirement unclear or missing detail:*"

        suggestion: >

          Make the simplest safe assumption.

          Proceed with implementation and note the assumption inline (e.g., `// defaulted to 5s timeout`).



---



    title: "Lean AI-Driven Codebase Analysis & Dev Guardrails"

    core_objective: >

      Provide a rapid, security-first, performance-focused,

      and maintainable approach to coding with minimal documentation.

      Enforce iterative development, small commits, and

      manifest V3 best practices where relevant.



    final_synthesis: >

      1. Rapidly produce essential, functional code with minimal

         bloat—particularly for Chrome extensions.

      2. Aggressively reduce documentation overhead and boilerplate.

      3. Prioritize fixes/improvements in order:

         (a) Security

         (b) Performance

         (c) Maintainability

      4. Maintain a focus on small, testable increments and

         minimal permissions/dependencies.



    rules:



      # 1. Prioritize Functional, Minimal Code

      - id: minimal_viable_feature

        pattern: "Add new feature:*"

        suggestion: >

          Provide the smallest working version of the requested feature.

          Avoid large design overhauls or expansive documentation.

          Focus on correctness and immediate functionality.



      # 2. Restrict Over-Permissioning & Dependencies

      - id: restrict_over_permissioning

        pattern: "Add/modify dependency:*"

        suggestion: >

          Only add new dependencies if they are strictly necessary for

          core functionality or essential performance/security gains.

          For Chrome extensions (Manifest V3), use the fewest permissions

          and minimal external libraries.



      # 3. Small, Iterative Commits

      - id: small_commits

        pattern: "Large code changes in one step:*"

        suggestion: >

          Break changes into smaller, testable increments.

          Each commit should yield a working, buildable state.

          Avoid merging monolithic blocks of unverified code.



      # 4. Emphasize Testing over Documentation

      - id: reduce_doc_bloat

        pattern: "Write extensive documentation:*"

        suggestion: >

          Use minimal inline comments only if code logic is unclear.

          Prefer short test scripts to prove correctness over

          large documentation files.



      # 5. Consistent, Simple Project Structure

      - id: keep_structure_consistent

        pattern: "Generate new files/folders:*"

        suggestion: >

          Maintain a straightforward, logical structure.

          Avoid deep nesting or complex hierarchies unless required.

          Keep naming descriptive and consistent.



      # 6. Contextual Refactoring Only

      - id: refactor_scope_limiter

        pattern: "Refactor entire codebase:*"

        suggestion: >

          Focus refactoring on the specific area needed for the current task

          (e.g., bug fix, feature addition, or critical improvement).

          Do not undertake large-scale rewrites without clear justification.



      # 7. Maintain Security & Privacy Norms

      - id: security_first

        pattern: "Collect or handle sensitive data:*"

        suggestion: >

          Request justification for any data collection.

          Store and transmit data securely (e.g., encrypted or hashed).

          Always adopt minimal permissions and handle PII only if essential.



      # 8. Limit Output to Essentials

      - id: lean_logging

        pattern: "Detailed logs or verbose output:*"

        suggestion: >

          Keep console logs concise or use a toggleable debug mode.

          Avoid cluttering performance with unnecessary output.



      # 9. Ensure Resilience, Then Iterate

      - id: resilience_and_iteration

        pattern: "Feature incomplete or known bug:*"

        suggestion: >

          Provide a fallback or safeguard to maintain basic functionality.

          Ship partial solutions only if they don’t break core flows.

          Validate quickly, then iterate further if needed.



      # 10. Validate Before Large Expansions

      - id: validate_before_expanding

        pattern: "Propose large scale expansions:*"

        suggestion: >

          Confirm existing functionality is stable, secure, and performing well.

          Expand scope or architecture only if it directly addresses a

          core requirement (security, performance, or maintainability).



    execution_mandate: >

      - Always produce a functional, minimal solution that prioritizes

        security, performance, and maintainability in that order.

      - Keep iteration small and continuous, ensuring each step is tested.

      - Minimize documentation overhead; rely on clear, self-explanatory code.

      - Comply with Manifest V3 best practices and lean development principles.



---



    title: "Lean AI Development Guardrails"

    core_objective: >

      Provide a rapid, security-first, performance-oriented,

      maintainable framework for analyzing and developing codebases

      —especially Chrome extensions using Manifest V3—

      with minimal documentation overhead.



    final_synthesis: >

      1. Quickly assess architecture, identify critical paths,

         and highlight structural consistency issues.

      2. Prioritize targeted improvements in:

         (a) Security vulnerabilities

         (b) Performance bottlenecks

         (c) Maintainability challenges.

      3. Enforce lean dev practices:

         - Minimal documentation

         - Minimal dependencies

         - Small commits

      4. Generate flexible, working code by iterating

         rather than over-documenting or over-engineering.



    expanded_structure:

      context_layers:

        - level: 1

          context: "Universal rules for codebase development, focusing on Security > Performance > Maintainability."

        - level: 2

          context: "Tailored to Manifest V3 Chrome extensions but generally applicable to any lean dev workflow."

        - level: 3

          context: "Minimize docs, encourage small, testable commits, and keep the code functional."



    # =============================================================

    # High-Level Analysis Prompt

    # =============================================================

    enhanced_prompt: >

      "Generate a rapid, prioritized plan for analyzing

       this codebase:

       1) Identify architectural patterns (MVC, microservices, etc.).

       2) Pinpoint critical code execution paths.

       3) Check structural consistency issues.

       Assume minimal docs. Focus improvements on:

          (a) Security

          (b) Performance

          (c) Maintainability."



    # =============================================================

    # Universal Guardrails & Directives (10 Key Rules)

    # =============================================================

    rules:

      # 1. Rapid, Minimal Feature Implementation

      - pattern: "Add new feature:*"

        suggestion: >

          Provide a concise, functional solution with minimal boilerplate.

          Avoid extensive abstractions or unnecessary docs.



      # 2. Restrict Over-Permissioning & Dependencies

      - pattern: "Add/modify dependency:*"

        suggestion: >

          Justify each new library. For Chrome Manifest V3, apply strict permission rules.

          No large or redundant libraries if a simpler approach exists.



      # 3. Small, Iterative Commits

      - pattern: "Large code changes in one step:*"

        suggestion: >

          Split major updates into small, testable increments.

          Each commit should yield buildable, working code.



      # 4. Test Coverage over Documentation

      - pattern: "Write extensive documentation:*"

        suggestion: >

          Use minimal inline comments only for non-obvious logic.

          Rely on short test scripts or checks to validate correctness.



      # 5. Maintain Simple, Consistent Project Structure

      - pattern: "Generate new files/folders:*"

        suggestion: >

          Keep a flat, logical hierarchy. Avoid deep nesting.

          Keep naming consistent and descriptive.



      # 6. Contextual Refactoring Only

      - pattern: "Refactor entire codebase:*"

        suggestion: >

          Focus refactors on the specific module or function at hand.

          Avoid sweeping, large-scale refactors unless urgently necessary.



      # 7. Security & Privacy Norms

      - pattern: "Collect or handle sensitive data:*"

        suggestion: >

          Only collect data if strictly required.

          Implement minimal, compliant security practices (e.g., encryption).

          Keep data handling as lean as possible.



      # 8. Limit Logs & Output

      - pattern: "Add verbose logs:*"

        suggestion: >

          Minimize console output. Use togglable debug flags for deeper detail.

          Avoid clutter and performance overhead from excessive logs.



      # 9. Ensure Resilience, Then Iterate

      - pattern: "Feature incomplete or known bug:*"

        suggestion: >

          Provide a fallback or safeguard to preserve essential functionality.

          Iteratively refine rather than shipping broken or partial features.



      # 10. Validate Stability Before Expanding

      - pattern: "Propose large scale expansions:*"

        suggestion: >

          Confirm the current solution is stable and flexible.

          Expand scope only if it aligns with immediate security,

          performance, or maintainability priorities.



    # =============================================================

    # Recommended Strategies & Repo References (Optional)

    # =============================================================

    top_strategies: >

      1. Manifest V3 Compliance (permissions, service workers).

      2. Minimal, Security-Focused Dependency Usage.

      3. Prioritize Performance (fast load, minimal overhead).

      4. Test-Driven, Iterative Implementation.

      5. Security Audits for Sensitive Operations (OAuth, user data).

      6. Lean Logging & Observability (only what's essential).

      7. Consistent Project Structure & Clear Separation of Concerns.

      8. Rapid Delivery with Small, Verified Commits.

      9. Reference Active, Quality Extensions for Guidance.

      10. Basic Lint & Format Tools for Consistency.



    example_repositories: >

      (Suggested for MV3 tab/bookmark management)

      1. github.com/antonycourtney/tab-organizer

      2. github.com/XYZ/mv3-bookmark-manager

      3. github.com/abc/tab-management-plus

      ...

      (Ensure they’re actively maintained & not deprecated.)



    # =============================================================

    # Execution Mandate

    # =============================================================

    execution_mandate: >

      1. Always produce secure, verifiable, and working code

         with minimal overhead.

      2. Emphasize immediate functional correctness over

         lengthy documentation or boilerplate.

      3. Keep commits small; confirm each step is stable.

      4. For Manifest V3, strictly adhere to

         permissions/security guidelines.

      5. Prevent codebase stagnation: keep momentum by

         focusing on coding rather than large doc tasks.



---



    # === .cursorrules ===

    title: "Lean Development Guardrails for Chrome Extensions"

    core_objective: >

      Guide AI coders to generate minimal, maintainable, and

      working code (Manifest V3) with minimal documentation

      overhead. Emphasize security, performance, and iterative

      improvement over large-scale planning or excessive docs.



    final_synthesis: >

      1. Deliver small, functional increments that solve the requirement at hand.

      2. Limit permissions, dependencies, and doc bloat.

      3. Prioritize security and performance optimizations where necessary.

      4. Maintain a flexible codebase by refactoring only within context.



    rules:



      # 1. Prioritize Functional, Minimal Code

      - id: prioritize_functional_code

        pattern: "Implement feature:*"

        suggestion: >

          Focus on minimal, functional code that meets the requirement

          without boilerplate or premature optimization.

          Provide a working solution first; avoid large design overhauls.



      # 2. Avoid Excessive Documentation

      - id: no_excessive_docs

        pattern: "Write documentation:*"

        suggestion: >

          Do not generate extensive docs or multiple files.

          Use brief inline comments only when logic is non-obvious.

          Let the code speak for itself.



      # 3. Minimal Codebase Expansion

      - id: minimal_codebase_expansion

        pattern: "Add/modify files:*"

        suggestion: >

          Introduce new files or modules only if functionally necessary.

          Keep the structure simple and consistent; avoid deep nesting

          or scattering small files.



      # 4. Iterate in Small Steps

      - id: iterate_in_small_steps

        pattern: "Commit large changes:*"

        suggestion: >

          Break down major updates into smaller, testable increments.

          Each commit must produce a buildable and functional state.



      # 5. Contextual Refactoring Only

      - id: refactor_with_context

        pattern: "Refactor large parts:*"

        suggestion: >

          Limit refactors to the module or function directly involved.

          Avoid sweeping changes unless critical to the current feature or fix.



      # 6. Enforce Security Minimums

      - id: enforce_security_minimums

        pattern: "Use external APIs or user data:*"

        suggestion: >

          Default to minimal permissions and the safest data-handling approach.

          Challenge whether data collection is necessary. For Chrome extensions,

          strictly adhere to Manifest V3 permission rules.



      # 7. Reduce Runtime Noise

      - id: reduce_runtime_noise

        pattern: "Add logging or verbose output:*"

        suggestion: >

          Keep logs minimal and meaningful. Use toggleable debug levels

          if more detail is needed temporarily.



      # 8. Validate Before Scaling

      - id: validate_before_scaling

        pattern: "Propose complex systems:*"

        suggestion: >

          Ensure the current solution is stable, resilient, and minimal.

          Expand only when it clearly addresses a performance, security,

          or maintainability need.



      # 9. Protect Dev Agility

      - id: protect_dev_agility

        pattern: "Freeze code for review or docs:*"

        suggestion: >

          Prioritize functional progress. Defer non-essential reviews

          or documentation tasks until key features are working

          and stable.



      # 10. Favor Clarity over Comments

      - id: favor_clarity_over_comments

        pattern: "Explain complex logic with long comments:*"

        suggestion: >

          Rewrite or refactor code for clarity rather than over-documenting it.

          Self-documenting code should be the default strategy.



    execution_mandate: >

      - Always produce functional, minimal code aligned with Manifest V3

        for Chrome extensions.

      - Prioritize security and performance before scaling or adding complexity.

      - Keep documentation lean. Only introduce new files, dependencies,

        or refactors when strictly necessary.

      - Each commit should yield a working, verifiable state—no large

        untested merges.



---



    title: "Lean AI-Driven Chrome Extension Guardrails"

    core_objective: >

      Guide autonomous AI coders to produce

      minimal, secure, performant Chrome extensions

      (Manifest V3) with minimal documentation overhead.



    final_synthesis: >

      1. Quickly implement working features with

         minimal boilerplate and iterative commits.

      2. Aggressively reduce unnecessary documentation

         and code expansion.

      3. Prioritize improvements in the order:

         (a) Security

         (b) Performance

         (c) Maintainability.

      4. Keep the structure simple, the logs lean, and

         permissions/ dependencies minimal.



    expanded_structure:

      context_layers:

        - level: 1

          context: "Tailored to Chrome extensions (Manifest V3), focusing on lean, functional development."

        - level: 2

          context: "Each rule ensures velocity, clarity, and minimal bloat or documentation."

        - level: 3

          context: "Security, then performance, then maintainability are the guiding priorities."



    # =========================================================

    # The 10 Lean-Development Rules

    # =========================================================

    rules:

      # 1. Prioritize Functional Code

      - id: prioritize_functional_code

        pattern: "Add new feature:*" OR "Implement feature:*"

        suggestion: >

          Provide the smallest possible working solution.

          Avoid speculative abstractions or boilerplate.

          Immediate functionality matters most.



      # 2. No Excessive Documentation

      - id: no_excessive_docs

        pattern: "Write documentation:*" OR "Add extensive comments:*"

        suggestion: >

          Do not generate large READMEs or doc files.

          Use minimal inline comments only where logic is non-obvious.



      # 3. Minimal Codebase Expansion

      - id: minimal_codebase_expansion

        pattern: "Add/modify files:*"

        suggestion: >

          Introduce new files/folders only if absolutely necessary.

          Maintain a clean, flat structure to reduce complexity.



      # 4. Small, Iterative Commits

      - id: iterate_in_small_steps

        pattern: "Commit large changes:*"

        suggestion: >

          Break changes into small, testable increments.

          Each commit must produce a working build/state.



      # 5. Contextual Refactoring Only

      - id: refactor_with_context

        pattern: "Refactor large parts:*" OR "Refactor entire codebase:*"

        suggestion: >

          Limit refactoring to the specific component or function in scope.

          Avoid wide-scale rewrites unless they solve a pressing issue.



      # 6. Enforce Security Minimums

      - id: enforce_security_minimums

        pattern: "Use external APIs or user data:*" OR "Collect sensitive data:*"

        suggestion: >

          Adopt least-permission principles (Manifest V3).

          Only handle data if strictly required; use secure handling by default.



      # 7. Reduce Runtime Noise

      - id: reduce_runtime_noise

        pattern: "Add logging or verbose output:*"

        suggestion: >

          Keep logs minimal and focused. Provide a toggleable debug mode if needed.

          Avoid cluttering runtime or performance overhead.



      # 8. Validate Before Scaling

      - id: validate_before_scaling

        pattern: "Propose complex systems:*" OR "Propose large scale expansions:*"

        suggestion: >

          Ensure the current solution is working, resilient, and minimal.

          Expand only after verifying tangible value and stability.



      # 9. Protect Development Agility

      - id: protect_dev_agility

        pattern: "Freeze code for review or docs:*"

        suggestion: >

          Maintain momentum by focusing on functional progress.

          Defer non-critical documentation or formal reviews until necessary.



      # 10. Favor Clarity Over Comments

      - id: favor_clarity_over_comments

        pattern: "Explain complex logic with long comments:*"

        suggestion: >

          Rewrite code for clarity instead of over-commenting.

          Self-documenting structures (clear naming, concise methods) are preferred.



    # =========================================================

    # Execution Mandate

    # =========================================================

    execution_mandate: >

      - Always produce a minimal, functional solution

        aligned with Manifest V3 guidelines.

      - Security, performance, and maintainability

        are your priority— in that order.

      - Keep iteration small and continuous, ensuring

        each step is tested.

      - Minimize documentation overhead; rely on

        self-explanatory code wherever possible.



---



    {

        "title": "Lean AI Development Guardrails for Chrome Extensions (MV3)",

        "core_objective": "Guide an AI coding assistant to generate minimal, secure, performant, and maintainable Chrome extensions (Manifest V3) using lean development principles, prioritizing functional code over documentation and employing iterative, small-step development.",

        "final_synthesis": {

            "purpose": "Direct AI coders to produce lean, secure, and functional Chrome extensions (Manifest V3) by enforcing iterative development, minimal dependencies/permissions, limited documentation, and prioritizing Security > Performance > Maintainability.",

            "rules": [

                {

                    "id": "minimal_functional_feature",

                    "pattern": "Add new feature:* OR Implement feature:*",

                    "suggestion": "Generate the smallest possible working solution for the immediate requirement. Avoid boilerplate, premature abstractions, or unrelated setup. Focus on functional correctness."

                },

                {

                    "id": "strict_dependency_permission",

                    "pattern": "Add/modify dependency:* OR Use external APIs or user data:* OR Collect or handle sensitive data:*",

                    "suggestion": "Justify any new dependency or permission. Use only essential libraries. Adhere strictly to Manifest V3 least-permission principles. Only collect/handle data if absolutely required, using secure methods."

                },

                {

                    "id": "small_iterative_commits",

                    "pattern": "Large code changes in one step:* OR Commit large changes:*",

                    "suggestion": "Break changes into small, independently testable increments. Ensure each commit results in a working, buildable state."

                },

                {

                    "id": "minimize_documentation_rely_on_clarity",

                    "pattern": "Write documentation:* OR Write extensive documentation:* OR Explain complex logic with long comments:* OR Add extensive comments:*",

                    "suggestion": "Avoid generating READMEs, extensive docstrings, or standalone docs. Use brief inline comments only for non-obvious logic. Prioritize refactoring for code clarity over adding comments."

                },

                {

                    "id": "maintain_simple_structure",

                    "pattern": "Generate new files/folders:* OR Add/modify files:*",

                    "suggestion": "Introduce new files/modules only when functionally necessary. Maintain a simple, logical, and flat project structure with consistent, descriptive naming. Avoid deep nesting."

                },

                {

                    "id": "contextual_refactoring_only",

                    "pattern": "Refactor large parts:* OR Refactor entire codebase:*",

                    "suggestion": "Limit refactoring strictly to the module or function necessary for the current task (feature, fix, critical improvement). Avoid large-scale refactors without clear, immediate justification."

                },

                {

                    "id": "lean_logging_output",

                    "pattern": "Add logging or verbose output:* OR Detailed logs or verbose output:*",

                    "suggestion": "Keep logs minimal and focused. Use toggleable debug flags for detailed diagnostics only when necessary. Avoid cluttering runtime or impacting performance."

                },

                {

                    "id": "ensure_resilience_validate_before_scaling",

                    "pattern": "Feature incomplete or known bug:* OR Propose complex systems:* OR Propose large scale expansions:*",

                    "suggestion": "If a feature is incomplete, provide a safeguard or fallback for basic functionality. Validate the current solution's stability, security, and performance before proposing or implementing large expansions or complex architectural changes."

                },

                {

                    "id": "protect_development_agility",

                    "pattern": "Freeze code for review or docs:*",

                    "suggestion": "Maintain development momentum by focusing on functional progress. Defer non-critical tasks like extensive documentation or formal reviews until essential functionality is stable."

                },

                {

                    "id": "handle_ambiguity_simply",

                    "pattern": "Requirement unclear or missing detail:*",

                    "suggestion": "Make the simplest, safest assumption to proceed. Implement based on the assumption and note it clearly inline (e.g., `// Assumed default timeout: 5000ms`)."

                }

            ],

            "execution_mandate": "Always produce minimal, functional, and secure code aligned with Manifest V3 best practices. Prioritize Security > Performance > Maintainability. Use small, iterative commits. Minimize documentation, dependencies, and permissions. Rely on clear code over comments."

        },

        "expanded_structure": {

            "context_layers": [

                {

                    "level": 1,

                    "context": "Define authoritative directives for AI-assisted autonomous coding."

                },

                {

                    "level": 2,

                    "context": "Specialize directives for lean Chrome extension development using Manifest V3 standards."

                },

                {

                    "level": 3,

                    "context": "Establish core principles: Minimal viable features, prioritize functionality over documentation, security-first mindset, iterative development, and limited scope."

                },

                {

                    "level": 4,

                    "context": "Implement these principles via specific rules (patterns and suggestions) guiding AI behavior regarding feature implementation, dependencies, permissions, code structure, refactoring, logging, documentation, and handling ambiguity."

                },

                {

                    "level": 5,

                    "context": "Goal: Generate a final, consolidated set of rules (like a `.cursorrules` file) that is clean, non-redundant, and optimized for guiding AI towards producing maintainable, secure, and performant code efficiently."

                }

            ]

        }

    }

```
