# Update Blender Portable Safely

Conversation ID: 681ef343-54a8-8008-b003-3db126533b36

## Message 1

How can i update blender portable without losing my setting and preferences?

## Message 2

To update **Blender Portable** without losing your settings and preferences, follow this safe and manual process:

---

### **1. Locate Your Current Settings Folder**
In Blender Portable, your settings are stored locally (not in the system appdata). Usually, you’ll find:

```
Blender\
├── 4.1\
│   └── config\
│       └── userpref.blend
│       └── startup.blend
│       └── bookmarks.txt
```

Also check for:

- `scripts\addons\` (for installed add-ons)
- `scripts\modules\` (for custom modules, if any)

---

### **2. Download the New Blender Portable Version**

Go to [Blender.org](https://www.blender.org/download/) and download the **.zip (portable)** version of the new release.

Unzip it to a new directory (e.g., `Blender_4.2/`) to keep things clean.

---

### **3. Copy Settings and Preferences**

From the *old version* (e.g., `Blender_4.1/`), copy the following **into** the same relative path in the *new version*:

- `config/` folder: includes your user preferences and startup files.
- `scripts/addons/`: custom or manually installed add-ons.
- `scripts/modules/`: if you have any custom Python modules.

> Note: Be careful when copying addons — check compatibility with the new version. You may need to reinstall or update some of them.

---

### **4. Launch the New Version and Verify**

Run the new Blender executable from the new folder, and check that:

- Your preferences are intact
- Add-ons are enabled and working
- Themes, layouts, and hotkeys are preserved

---

### **5. Cleanup (Optional)**

Once you're sure everything works, you can delete the old version or keep it as a backup.

---

Would you like a simple script to automate the migration process for future updates?
