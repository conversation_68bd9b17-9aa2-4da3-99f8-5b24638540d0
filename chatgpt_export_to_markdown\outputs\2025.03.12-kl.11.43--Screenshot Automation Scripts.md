# Screenshot Automation Scripts

Conversation ID: 67d16549-b464-8008-b8a3-8335f94261ea

## Message 1

The attached scripts reside within the `script` folder of a React/TypeScript project, you should famialize yourself with it to such degree that you can explain *exactly* what it is and *exactly* what purpose it serve:



    # Dir `scripts`



    ### File Structure



    ```

    ├── auto-snapshot.js

    ├── dev-with-snapshots.js

    ├── refresh-screenshots.js

    ├── screenshot-manager.js

    ├── tidy-screenshots.js

    ├── config

    │   └── screenshot-config.js

    └── utils

        └── screenshot-utils.js

    ```





    #### `auto-snapshot.js`



    ```javascript

    import { exec } from "child_process";

    import { promisify } from "util";

    import fs from "fs";

    import path from "path";

    import { fileURLToPath } from "url";

    import puppeteer from "puppeteer";



    const execAsync = promisify(exec);



    // Get current directory

    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "..");



    // Configuration

    const CONFIG = {

        // Development server port

        port: 5173,



        // Delay before capturing screenshots (ms)

        captureDelay: 2000,



        // AI analysis directory

        aiDir: path.join(projectRoot, ".ai-analysis"),



        // Latest screenshots directory

        latestDir: path.join(projectRoot, ".ai-analysis", "latest"),



        // Maximum number of snapshots to keep

        maxSnapshots: 10,



        // Pages to capture

        pages: [

            { path: "/", name: "home" },

            { path: "/hvem-er-vi", name: "about" },

            { path: "/hva-vi-gjor", name: "services" },

            { path: "/prosjekter", name: "projects" },

            { path: "/kontakt", name: "contact" },

        ],



        // Viewport to use for AI analysis

        viewport: { width: 1440, height: 900, name: "desktop" },

    };



    /**

     * Ensures the AI analysis directory exists

     */

    function setupAiDirectory() {

        // Create main AI directory if it doesn't exist

        if (!fs.existsSync(CONFIG.aiDir)) {

            fs.mkdirSync(CONFIG.aiDir, { recursive: true });

        }



        // Create latest directory if it doesn't exist

        if (!fs.existsSync(CONFIG.latestDir)) {

            fs.mkdirSync(CONFIG.latestDir, { recursive: true });

        }



        // Create a metadata.json file if it doesn't exist

        const metadataPath = path.join(CONFIG.aiDir, "metadata.json");

        if (!fs.existsSync(metadataPath)) {

            fs.writeFileSync(

                metadataPath,

                JSON.stringify(

                    {

                        snapshots: [],

                        lastSnapshot: null,

                    },

                    null,

                    2

                )

            );

        }



        return metadataPath;

    }



    /**

     * Updates the metadata file with snapshot information

     */

    function updateMetadata(metadataPath, snapshotId) {

        const metadata = JSON.parse(fs.readFileSync(metadataPath, "utf8"));



        // Add new snapshot

        metadata.snapshots.push({

            id: snapshotId,

            timestamp: new Date().toISOString(),

            pages: CONFIG.pages.map((page) => page.name),

        });



        // Keep only the most recent snapshots

        if (metadata.snapshots.length > CONFIG.maxSnapshots) {

            const removedSnapshots = metadata.snapshots.slice(

                0,

                metadata.snapshots.length - CONFIG.maxSnapshots

            );

            metadata.snapshots = metadata.snapshots.slice(

                metadata.snapshots.length - CONFIG.maxSnapshots

            );



            // Remove old snapshot directories

            removedSnapshots.forEach((snapshot) => {

                const snapshotDir = path.join(CONFIG.aiDir, snapshot.id);

                if (fs.existsSync(snapshotDir)) {

                    fs.rmSync(snapshotDir, { recursive: true, force: true });

                }

            });

        }



        // Update last snapshot

        metadata.lastSnapshot = snapshotId;



        // Write updated metadata

        fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));



        return metadata;

    }



    /**

     * Captures a screenshot of a page

     */

    async function capturePage(page, snapshotDir) {

        const url = `http://localhost:${CONFIG.port}${page.path}`;

        const outputPath = path.join(snapshotDir, `${page.name}.png`);

        const latestOutputPath = path.join(CONFIG.latestDir, `${page.name}.png`);



        try {

            // Use puppeteer directly instead of via a temporary script

            const browser = await puppeteer.launch();

            const browserPage = await browser.newPage();



            await browserPage.setViewport({

                width: CONFIG.viewport.width,

                height: CONFIG.viewport.height,

            });



            await browserPage.goto(url, {

                waitUntil: "networkidle2",

                timeout: 10000,

            });



            // Wait for any animations

            await new Promise((resolve) =>

                setTimeout(resolve, CONFIG.captureDelay)

            );



            // Take the screenshot

            await browserPage.screenshot({

                path: outputPath,

                fullPage: true,

            });



            // Copy to latest directory

            fs.copyFileSync(outputPath, latestOutputPath);



            await browser.close();



            console.log(`Captured ${page.name} page`);

            return { outputPath, latestOutputPath };

        } catch (error) {

            console.error(`Error capturing ${page.name} page:`, error.message);

            return null;

        }

    }



    /**

     * Generates an AI-readable summary of the snapshot

     */

    function generateAiSummary(snapshotDir, metadata) {

        const summaryPath = path.join(snapshotDir, "ai-summary.md");

        const latestSummaryPath = path.join(CONFIG.latestDir, "ai-summary.md");



        const summary = `# Website Snapshot for AI Analysis



    ## Snapshot Information

    - **ID**: ${metadata.lastSnapshot}

    - **Timestamp**: ${new Date().toISOString()}

    - **Pages Captured**: ${CONFIG.pages.map((page) => page.name).join(", ")}



    ## Screenshot Paths

    ${CONFIG.pages

        .map(

            (page) =>

                `- **${page.name}**: \`${path.join(

                    snapshotDir,

                    `${page.name}.png`

                )}\``

        )

        .join("\n")}



    ## Latest Screenshot Paths (Always Current)

    ${CONFIG.pages

        .map(

            (page) =>

                `- **${page.name}**: \`${path.join(

                    CONFIG.latestDir,

                    `${page.name}.png`

                )}\``

        )

        .join("\n")}



    ## Viewport Information

    - **Width**: ${CONFIG.viewport.width}px

    - **Height**: ${CONFIG.viewport.height}px

    - **Name**: ${CONFIG.viewport.name}



    ## Usage Instructions

    These screenshots can be used by AI assistants to understand the current state of the website.

    When making code changes, reference these images to understand the visual impact of your changes.



    ## Access Paths

    To reference the specific snapshot in conversations, use:

    \`\`\`

    /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/${

            metadata.lastSnapshot

        }

    \`\`\`



    To always reference the latest screenshots, use:

    \`\`\`

    /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest

    \`\`\`

    `;



        // Write to snapshot directory

        fs.writeFileSync(summaryPath, summary);



        // Write to latest directory

        fs.writeFileSync(latestSummaryPath, summary);



        return { summaryPath, latestSummaryPath };

    }



    /**

     * Main function to capture snapshots for AI analysis

     */

    async function captureForAiAnalysis() {

        try {

            console.log("Capturing website snapshots for AI analysis...");



            // Setup AI directory

            const metadataPath = setupAiDirectory();



            // Create a unique ID for this snapshot

            const snapshotId = `snapshot-${Date.now()}`;

            const snapshotDir = path.join(CONFIG.aiDir, snapshotId);

            fs.mkdirSync(snapshotDir, { recursive: true });



            // Capture screenshots for each page

            for (const page of CONFIG.pages) {

                await capturePage(page, snapshotDir);

            }



            // Update metadata

            const metadata = updateMetadata(metadataPath, snapshotId);



            // Generate AI summary

            const { summaryPath, latestSummaryPath } = generateAiSummary(

                snapshotDir,

                metadata

            );



            console.log(`\nSnapshot captured successfully: ${snapshotId}`);

            console.log(`AI summary generated at: ${summaryPath}`);

            console.log(`Latest summary updated at: ${latestSummaryPath}`);



            console.log(

                `\nTo reference the specific snapshot in conversations, use:`

            );

            console.log(

                `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/${metadata.lastSnapshot}`

            );



            console.log(`\nTo always reference the latest screenshots, use:`);

            console.log(

                `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`

            );



            return {

                success: true,

                snapshotId,

                snapshotDir,

                latestDir: CONFIG.latestDir,

                summaryPath,

                latestSummaryPath,

            };

        } catch (error) {

            console.error("Error capturing snapshots for AI analysis:", error);

            return {

                success: false,

                error: error.message,

            };

        }

    }



    // Run the capture process

    captureForAiAnalysis();

    ```





    #### `dev-with-snapshots.js`



    ```javascript

    import { exec, spawn } from "child_process";

    import { promisify } from "util";

    import fs from "fs";

    import path from "path";

    import { fileURLToPath } from "url";

    import { createServer } from "http";



    const execAsync = promisify(exec);



    // Get current directory

    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "..");



    // Configuration

    const CONFIG = {

        // Minimum time between snapshots (ms)

        snapshotCooldown: 30000, // 30 seconds



        // Port for the snapshot trigger server

        triggerPort: 5174,



        // Development server port

        devPort: 5173,



        // Latest screenshots directory

        latestDir: path.join(projectRoot, ".ai-analysis", "latest"),

    };



    // Track the last snapshot time

    let lastSnapshotTime = 0;



    /**

     * Starts the development server

     */

    function startDevServer() {

        console.log("Starting development server...");



        const devProcess = spawn("npm", ["run", "dev"], {

            cwd: projectRoot,

            stdio: "inherit",

            shell: true,

        });



        devProcess.on("error", (error) => {

            console.error("Error starting development server:", error);

        });



        return devProcess;

    }



    /**

     * Takes a snapshot if enough time has passed since the last one

     */

    async function takeSnapshotIfNeeded() {

        const now = Date.now();



        // Check if enough time has passed since the last snapshot

        if (now - lastSnapshotTime < CONFIG.snapshotCooldown) {

            console.log("Snapshot cooldown active, skipping...");

            return false;

        }



        // Update the last snapshot time

        lastSnapshotTime = now;



        try {

            console.log("Taking snapshot for AI analysis...");

            await execAsync(`npm run ai:snapshot`, { cwd: projectRoot });



            // Display the latest directory path for easy reference

            console.log("\n=== LATEST SCREENSHOTS READY FOR AI REFERENCE ===");

            console.log(

                `Path: /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`

            );

            console.log("=================================================\n");



            return true;

        } catch (error) {

            console.error("Error taking snapshot:", error.message);

            return false;

        }

    }



    /**

     * Creates a simple HTTP server to trigger snapshots

     */

    function createTriggerServer() {

        const server = createServer(async (req, res) => {

            // Set CORS headers

            res.setHeader("Access-Control-Allow-Origin", "*");

            res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");

            res.setHeader("Access-Control-Allow-Headers", "Content-Type");



            // Handle preflight requests

            if (req.method === "OPTIONS") {

                res.writeHead(204);

                res.end();

                return;

            }



            // Handle snapshot trigger

            if (req.url === "/trigger-snapshot") {

                console.log("Snapshot triggered via HTTP request");



                const success = await takeSnapshotIfNeeded();



                res.writeHead(200, { "Content-Type": "application/json" });

                res.end(

                    JSON.stringify({

                        success,

                        message: success

                            ? "Snapshot taken successfully"

                            : "Snapshot skipped (cooldown active)",

                        latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,

                    })

                );

                return;

            }



            // Handle status check

            if (req.url === "/status") {

                res.writeHead(200, { "Content-Type": "application/json" });

                res.end(

                    JSON.stringify({

                        status: "running",

                        lastSnapshot: new Date(lastSnapshotTime).toISOString(),

                        cooldownActive:

                            Date.now() - lastSnapshotTime < CONFIG.snapshotCooldown,

                        latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,

                    })

                );

                return;

            }



            // Handle latest path request

            if (req.url === "/latest-path") {

                res.writeHead(200, { "Content-Type": "application/json" });

                res.end(

                    JSON.stringify({

                        latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,

                    })

                );

                return;

            }



            // Default response

            res.writeHead(404, { "Content-Type": "application/json" });

            res.end(JSON.stringify({ error: "Not found" }));

        });



        server.listen(CONFIG.triggerPort, () => {

            console.log(

                `Snapshot trigger server running at http://localhost:${CONFIG.triggerPort}`

            );

            console.log(

                `- To trigger a snapshot: http://localhost:${CONFIG.triggerPort}/trigger-snapshot`

            );

            console.log(

                `- To check status: http://localhost:${CONFIG.triggerPort}/status`

            );

            console.log(

                `- To get latest path: http://localhost:${CONFIG.triggerPort}/latest-path`

            );

        });



        return server;

    }



    /**

     * Creates a file watcher to trigger snapshots on changes

     */

    function createFileWatcher() {

        // Directories to watch

        const watchDirs = [path.join(projectRoot, "src")];



        // Extensions to watch

        const watchExtensions = [

            ".ts",

            ".tsx",

            ".js",

            ".jsx",

            ".css",

            ".scss",

            ".html",

        ];



        // Create watchers for each directory

        const watchers = watchDirs.map((dir) => {

            console.log(`Watching directory for changes: ${dir}`);



            return fs.watch(

                dir,

                { recursive: true },

                async (eventType, filename) => {

                    if (!filename) return;



                    // Check if the file extension should trigger a snapshot

                    const ext = path.extname(filename).toLowerCase();

                    if (!watchExtensions.includes(ext)) return;



                    console.log(`File changed: ${filename}`);

                    await takeSnapshotIfNeeded();

                }

            );

        });



        return watchers;

    }



    /**

     * Main function to run development with automatic snapshots

     */

    async function devWithSnapshots() {

        try {

            console.log("Starting development with automatic snapshots...");



            // Start the development server

            const devProcess = startDevServer();



            // Wait for the development server to start

            console.log("Waiting for development server to start...");

            await new Promise((resolve) => setTimeout(resolve, 5000));



            // Create the trigger server

            const triggerServer = createTriggerServer();



            // Create file watchers

            const watchers = createFileWatcher();



            // Take an initial snapshot

            console.log("Taking initial snapshot...");

            await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait for dev server to be fully ready

            await takeSnapshotIfNeeded();



            console.log("\nDevelopment with automatic snapshots is running!");

            console.log("- Development server: http://localhost:5173");

            console.log(

                "- Snapshot trigger: http://localhost:5174/trigger-snapshot"

            );

            console.log(

                "- File changes in src/ will automatically trigger snapshots"

            );

            console.log("- Latest screenshots are always available at:");

            console.log(

                `  /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`

            );

            console.log("- Press Ctrl+C to stop");



            // Handle process termination

            process.on("SIGINT", () => {

                console.log("Shutting down...");



                // Close file watchers

                watchers.forEach((watcher) => watcher.close());



                // Close trigger server

                triggerServer.close();



                // Kill development server

                devProcess.kill();



                process.exit(0);

            });

        } catch (error) {

            console.error("Error starting development with snapshots:", error);

        }

    }



    // Run the main function

    devWithSnapshots();

    ```





    #### `refresh-screenshots.js`



    ```javascript

    import { exec } from "child_process";

    import { promisify } from "util";

    import path from "path";

    import { fileURLToPath } from "url";



    const execAsync = promisify(exec);



    // Get current directory

    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "..");



    /**

     * Runs a screenshot manager command

     * @param {string} command The command to run

     * @param {string[]} args Additional arguments

     */

    async function runScreenshotCommand(command, args = []) {

        const commandString = `node ${path.join(

            projectRoot,

            "scripts",

            "screenshot-manager.js"

        )} ${command} ${args.join(" ")}`;

        console.log(`Running: ${commandString}`);



        try {

            const { stdout, stderr } = await execAsync(commandString);

            if (stdout) console.log(stdout);

            if (stderr) console.error(stderr);

        } catch (error) {

            console.error(`Error running ${command}:`, error.message);

            throw error;

        }

    }



    /**

     * Refreshes screenshots by capturing, tidying, and cleaning up

     */

    async function refreshScreenshots() {

        try {

            console.log("Starting screenshot refresh process...");



            // Step 1: Capture new screenshots

            console.log("\n=== Step 1: Capturing new screenshots ===");

            await runScreenshotCommand("capture");



            // Step 2: Tidy up the screenshots directory

            console.log("\n=== Step 2: Tidying up screenshots directory ===");

            await runScreenshotCommand("tidy");



            // Step 3: Clean up old screenshots (keep last 7 days)

            console.log("\n=== Step 3: Cleaning up old screenshots ===");

            await runScreenshotCommand("clean", ["7"]);



            console.log("\nScreenshot refresh process completed successfully!");

            console.log(

                "You can view the latest screenshots in the screenshot-report.html file."

            );

        } catch (error) {

            console.error("Screenshot refresh process failed:", error.message);

        }

    }



    // Run the refresh process

    refreshScreenshots();

    ```





    #### `screenshot-manager.js`



    ```javascript

    import { exec } from "child_process";

    import { promisify } from "util";

    import fs from "fs";

    import path from "path";

    import { CONFIG, COMMANDS } from "./config/screenshot-config.js";

    import {

        captureScreenshots,

        cleanOldScreenshots,

        tidyScreenshots,

        ensureDirectories,

    } from "./utils/screenshot-utils.js";



    const execAsync = promisify(exec);



    /**

     * Displays help information

     */

    function showHelp() {

        console.log(`

    Screenshot Manager - Utility for managing website screenshots



    Usage:

      node screenshot-manager.js [command] [options]



    Commands:

      capture                 Capture screenshots now

      clean [days]           Clean screenshots older than specified days (default: ${CONFIG.defaultRetention})

      compare [dir1] [dir2]  Compare two screenshot directories

      schedule [interval]    Schedule automatic captures (interval in hours, default: 24)

      unschedule            Remove scheduled captures

      tidy                  Tidy up the screenshots directory

      help                  Show this help message



    Examples:

      node screenshot-manager.js capture

      node screenshot-manager.js clean 3

      node screenshot-manager.js schedule 12

      node screenshot-manager.js tidy

    `);

    }



    /**

     * Schedules automatic screenshot captures

     * @param {number} interval Interval in hours

     */

    async function scheduleCaptures(interval = 24) {

        try {

            const isWindows = process.platform === "win32";



            if (isWindows) {

                const scriptPath = path.join(

                    CONFIG.projectRoot,

                    "scripts",

                    "run-capture.bat"

                );

                const batchContent = `@echo off\ncd ${CONFIG.projectRoot}\nnode ${CONFIG.captureScript}\n`;

                fs.writeFileSync(scriptPath, batchContent);



                await execAsync(

                    `schtasks /create /tn "${CONFIG.windowsTaskName}" /tr "${scriptPath}" /sc HOURLY /mo ${interval} /f`

                );

                console.log(

                    `Scheduled screenshot capture every ${interval} hours using Windows Task Scheduler`

                );

            } else {

                const cronCommand = `0 */${interval} * * * cd ${CONFIG.projectRoot} && node ${CONFIG.captureScript}`;

                const { stdout: currentCrontab } = await execAsync(

                    'crontab -l 2>/dev/null || echo ""'

                );



                if (currentCrontab.includes(CONFIG.captureScript)) {

                    console.log("Screenshot capture is already scheduled");

                    return;

                }



                const newCrontab =

                    currentCrontab +

                    `\n# Ringerike Landskap Screenshot Capture\n${cronCommand}\n`;

                const tempFile = path.join(CONFIG.projectRoot, "temp-crontab");

                fs.writeFileSync(tempFile, newCrontab);

                await execAsync(`crontab ${tempFile}`);

                fs.unlinkSync(tempFile);



                console.log(

                    `Scheduled screenshot capture every ${interval} hours using crontab`

                );

            }

        } catch (error) {

            console.error("Error scheduling captures:", error.message);

        }

    }



    /**

     * Removes scheduled screenshot captures

     */

    async function unscheduleCaptures() {

        try {

            const isWindows = process.platform === "win32";



            if (isWindows) {

                try {

                    await execAsync(

                        `schtasks /delete /tn "${CONFIG.windowsTaskName}" /f`

                    );

                    console.log(

                        "Scheduled screenshot capture removed from Windows Task Scheduler"

                    );

                } catch (error) {

                    console.log("No scheduled task found");

                }

            } else {

                const { stdout: currentCrontab } = await execAsync(

                    'crontab -l 2>/dev/null || echo ""'

                );



                if (!currentCrontab.includes(CONFIG.captureScript)) {

                    console.log("No scheduled screenshot capture found");

                    return;

                }



                const newCrontab = currentCrontab

                    .split("\n")

                    .filter(

                        (line) =>

                            !line.includes(CONFIG.captureScript) &&

                            !line.includes(

                                "# Ringerike Landskap Screenshot Capture"

                            )

                    )

                    .join("\n");



                const tempFile = path.join(CONFIG.projectRoot, "temp-crontab");

                fs.writeFileSync(tempFile, newCrontab);

                await execAsync(`crontab ${tempFile}`);

                fs.unlinkSync(tempFile);



                console.log("Scheduled screenshot capture removed from crontab");

            }

        } catch (error) {

            console.error("Error removing scheduled captures:", error.message);

        }

    }



    /**

     * Main function to handle commands

     */

    async function main() {

        // Ensure directories exist

        ensureDirectories();



        const [command = COMMANDS.help, ...args] = process.argv.slice(2);



        switch (command) {

            case COMMANDS.capture:

                await captureScreenshots();

                break;



            case COMMANDS.clean:

                const days = parseInt(args[0]) || CONFIG.defaultRetention;

                await cleanOldScreenshots(days);

                break;



            case COMMANDS.schedule:

                const interval = parseInt(args[0]) || 24;

                await scheduleCaptures(interval);

                break;



            case COMMANDS.unschedule:

                await unscheduleCaptures();

                break;



            case COMMANDS.tidy:

                await tidyScreenshots();

                break;



            case COMMANDS.help:

            default:

                showHelp();

                break;

        }

    }



    main().catch((error) => {

        console.error("Error:", error);

        process.exit(1);

    });

    ```





    #### `tidy-screenshots.js`



    ```javascript

    import fs from "fs";

    import path from "path";

    import { fileURLToPath } from "url";



    // Get current directory

    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "..");



    // Screenshot directory

    const screenshotsDir = path.join(projectRoot, "screenshots");



    /**

     * Creates a .gitignore file in the screenshots directory

     */

    function createGitignore() {

        const gitignorePath = path.join(screenshotsDir, ".gitignore");

        const gitignoreContent = `# Ignore all files in this directory except README.md and .gitignore

    *

    !README.md

    !.gitignore

    !.gitkeep



    # Allow the latest directory but ignore its contents

    !latest/

    latest/*

    !latest/.gitkeep



    # Allow the directory structure but not the actual screenshots

    !*/

    */*/

    `;



        fs.writeFileSync(gitignorePath, gitignoreContent);

        console.log("Created .gitignore file in screenshots directory");

    }



    /**

     * Creates .gitkeep files to preserve directory structure

     */

    function createGitkeepFiles() {

        // Create .gitkeep in the screenshots directory

        fs.writeFileSync(path.join(screenshotsDir, ".gitkeep"), "");



        // Create .gitkeep in the latest directory

        const latestDir = path.join(screenshotsDir, "latest");

        if (fs.existsSync(latestDir)) {

            fs.writeFileSync(path.join(latestDir, ".gitkeep"), "");



            // Create .gitkeep in viewport subdirectories

            ["mobile", "tablet", "desktop"].forEach((viewport) => {

                const viewportDir = path.join(latestDir, viewport);

                if (fs.existsSync(viewportDir)) {

                    fs.writeFileSync(path.join(viewportDir, ".gitkeep"), "");

                }

            });

        }



        console.log("Created .gitkeep files to preserve directory structure");

    }



    /**

     * Removes the legacy backup directory

     */

    function removeLegacyBackup() {

        const legacyBackupDir = path.join(screenshotsDir, "legacy-backup");



        if (fs.existsSync(legacyBackupDir)) {

            try {

                fs.rmSync(legacyBackupDir, { recursive: true, force: true });

                console.log("Removed legacy-backup directory");

            } catch (error) {

                console.error(

                    "Error removing legacy-backup directory:",

                    error.message

                );

            }

        } else {

            console.log("No legacy-backup directory found");

        }

    }



    /**

     * Removes any loose files in the screenshots directory

     */

    function removeLooseFiles() {

        const files = fs.readdirSync(screenshotsDir);



        const looseFiles = files.filter((file) => {

            const filePath = path.join(screenshotsDir, file);

            const isDirectory = fs.statSync(filePath).isDirectory();



            // Keep directories with timestamp format or 'latest'

            if (isDirectory) {

                return !(

                    file === "latest" ||

                    /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(file)

                );

            }



            // Keep README.md, .gitignore, and .gitkeep

            if (

                [

                    "README.md",

                    ".gitignore",

                    ".gitkeep",

                    "screenshot-report.html",

                ].includes(file)

            ) {

                return false;

            }



            // All other files should be removed

            return true;

        });



        if (looseFiles.length === 0) {

            console.log("No loose files found in screenshots directory");

            return;

        }



        console.log(

            `Found ${looseFiles.length} loose files in screenshots directory`

        );



        looseFiles.forEach((file) => {

            const filePath = path.join(screenshotsDir, file);

            try {

                fs.rmSync(filePath, { force: true });

                console.log(`Removed loose file: ${file}`);

            } catch (error) {

                console.error(`Error removing ${file}:`, error.message);

            }

        });

    }



    /**

     * Optimizes the screenshot report by reducing image sizes

     */

    function optimizeScreenshotReport() {

        const reportPath = path.join(screenshotsDir, "screenshot-report.html");



        if (!fs.existsSync(reportPath)) {

            console.log("Screenshot report not found");

            return;

        }



        try {

            let reportContent = fs.readFileSync(reportPath, "utf8");



            // Add lazy loading to images

            reportContent = reportContent.replace(

                /<img src="([^"]+)"/g,

                '<img src="$1" loading="lazy"'

            );



            // Add viewport meta tag for better mobile display if not already present

            if (!reportContent.includes("viewport")) {

                reportContent = reportContent.replace(

                    "<head>",

                    '<head>\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">'

                );

            }



            // Add a note about the tidied directory

            reportContent = reportContent.replace(

                '<p class="timestamp">Generated',

                '<p class="note">Screenshots directory has been tidied up for better organization.</p>\n        <p class="timestamp">Generated'

            );



            fs.writeFileSync(reportPath, reportContent);

            console.log("Optimized screenshot report");

        } catch (error) {

            console.error("Error optimizing screenshot report:", error.message);

        }

    }



    /**

     * Main function to tidy up the screenshots directory

     */

    function tidyScreenshotsDirectory() {

        console.log("Tidying up screenshots directory...");



        // Create .gitignore file

        createGitignore();



        // Create .gitkeep files

        createGitkeepFiles();



        // Remove legacy backup

        removeLegacyBackup();



        // Remove loose files

        removeLooseFiles();



        // Optimize screenshot report

        optimizeScreenshotReport();



        console.log("Screenshots directory tidied up successfully");

    }



    // Run the tidy function

    tidyScreenshotsDirectory();

    ```





    #### `config\screenshot-config.js`



    ```javascript

    import path from "path";

    import { fileURLToPath } from "url";



    const __filename = fileURLToPath(import.meta.url);

    const __dirname = path.dirname(__filename);

    const projectRoot = path.resolve(__dirname, "../..");



    export const CONFIG = {

        // Core paths

        projectRoot,

        screenshotsDir: path.join(projectRoot, "screenshots"),

        aiAnalysisDir: path.join(projectRoot, ".ai-analysis"),



        // Latest directories

        latestScreenshotsDir: path.join(projectRoot, "screenshots", "latest"),

        latestAiDir: path.join(projectRoot, ".ai-analysis", "latest"),



        // Scripts

        captureScript: path.join(projectRoot, "capture-website.js"),

        tidyScript: path.join(projectRoot, "scripts", "tidy-screenshots.js"),



        // Development settings

        devPort: 5173,

        triggerPort: 5174,

        snapshotCooldown: 30000, // 30 seconds



        // Screenshot settings

        defaultRetention: 7,

        watchExtensions: [".ts", ".tsx", ".js", ".jsx", ".css", ".scss", ".html"],



        // Task scheduling

        windowsTaskName: "RingerikeLandskapScreenshots",

        cronJobLabel: "# Ringerike Landskap Screenshot Capture",

    };



    export const COMMANDS = {

        capture: "capture",

        clean: "clean",

        compare: "compare",

        help: "help",

        schedule: "schedule",

        unschedule: "unschedule",

        tidy: "tidy",

    };

    ```





    #### `utils\screenshot-utils.js`



    ```javascript

    import { exec } from "child_process";

    import { promisify } from "util";

    import fs from "fs";

    import path from "path";

    import { CONFIG } from "../config/screenshot-config.js";



    const execAsync = promisify(exec);



    /**

     * Captures screenshots using the capture script

     */

    export async function captureScreenshots() {

        try {

            console.log("Capturing screenshots...");

            await execAsync(`node ${CONFIG.captureScript}`);

            console.log("Screenshots captured successfully");

            return true;

        } catch (error) {

            console.error("Error capturing screenshots:", error.message);

            return false;

        }

    }



    /**

     * Cleans up screenshots older than the specified number of days

     * @param {number} days Number of days to keep screenshots

     */

    export async function cleanOldScreenshots(days = CONFIG.defaultRetention) {

        try {

            const baseDir = CONFIG.screenshotsDir;

            if (!fs.existsSync(baseDir)) {

                console.log("Screenshots directory does not exist");

                return false;

            }



            const now = new Date().getTime();

            const maxAge = days * 24 * 60 * 60 * 1000;



            const dirs = fs

                .readdirSync(baseDir)

                .filter((name) => {

                    const fullPath = path.join(baseDir, name);

                    return (

                        fs.statSync(fullPath).isDirectory() &&

                        name !== "latest" &&

                        /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(name)

                    );

                })

                .map((name) => ({

                    name,

                    path: path.join(baseDir, name),

                    time: fs.statSync(path.join(baseDir, name)).mtime.getTime(),

                }))

                .filter((dir) => now - dir.time > maxAge);



            if (dirs.length === 0) {

                console.log(`No screenshots older than ${days} days found`);

                return true;

            }



            console.log(

                `Found ${dirs.length} screenshot directories older than ${days} days`

            );



            dirs.forEach((dir) => {

                console.log(`Removing old snapshot: ${dir.name}`);

                fs.rmSync(dir.path, { recursive: true, force: true });

            });



            console.log(`Cleaned up ${dirs.length} old screenshot directories`);

            return true;

        } catch (error) {

            console.error("Error cleaning old screenshots:", error);

            return false;

        }

    }



    /**

     * Runs the tidy script to organize screenshots

     */

    export async function tidyScreenshots() {

        try {

            console.log("Tidying screenshots...");

            await execAsync(`node ${CONFIG.tidyScript}`);

            console.log("Screenshots tidied successfully");

            return true;

        } catch (error) {

            console.error("Error tidying screenshots:", error.message);

            return false;

        }

    }



    /**

     * Ensures screenshot directories exist

     */

    export function ensureDirectories() {

        const dirs = [

            CONFIG.screenshotsDir,

            CONFIG.aiAnalysisDir,

            CONFIG.latestScreenshotsDir,

            CONFIG.latestAiDir,

        ];



        dirs.forEach((dir) => {

            if (!fs.existsSync(dir)) {

                fs.mkdirSync(dir, { recursive: true });

            }

        });

    }

    ```


## Message 2

**Explaining script roles**

I’m detailing the functions of five scripts and two directories. For instance, `auto-snapshot.js` captures screenshots for AI analysis, while `dev-with-snapshots.js` runs the dev server and triggers snapshots upon code changes.

**Setting up the server**

I'm noting that `dev-with-snapshots.js` configures a server on port 5174, allowing manual snapshot triggering and status checks via web URLs.

**Orchestrating screenshot tasks**

`refresh-screenshots.js` manages screenshot capture, cleanup, and organization, while `screenshot-manager.js` offers a CLI tool for various screenshot-related commands, including scheduling and comparison.

**Organizing and cleaning**

`tidy-screenshots.js` refines the screenshots directory by removing outdated directories and establishing .gitignore and .gitkeep files.

**Detailing valid configurations**

The `config/screenshot-config.js` file encapsulates screenshot capturing configurations and links to relevant directories.

**Implementing utilities**

`utils/screenshot-utils.js` provides the mechanics for screenshot capture, cleaning, and organization, enhancing the functionality of the entire script suite.

## Message 3

Below is a structured explanation of each file in the `scripts` folder—what it does, how it fits into the bigger picture, and the exact purposes it serves. Overall, these scripts revolve around **capturing and managing screenshots** for automated visual checks, AI analysis, and development convenience.

---

## 1. High-Level Overview

1. **Purpose**  
   - This entire set of scripts orchestrates **automated screenshot captures**, keeps track of snapshots over time, and helps maintain a clean directory structure.
   - They integrate with a React/TypeScript project’s development workflow so that screenshots can be captured on demand, on file changes, or on a schedule.
   - There is also logic to tidy older screenshots, remove stale data, and provide references for an “always latest” set of images.

2. **Key Concepts**  
   - **`.ai-analysis` Directory**: A special folder that stores snapshots for AI usage, along with metadata.
   - **`screenshots` Directory**: Another place where general screenshot captures (and housekeeping) occur.
   - **`latest` Folders**: Mirror copies of the most recent screenshots, so you can always reference a stable path (without needing a specific timestamp).
   - **Scheduling & Triggering**: Scripts allow scheduling captures via Windows Task Scheduler or cron jobs, or triggering them on-the-fly.

---

## 2. Script Summaries

Below are each script’s specifics and how they work. (They often reference each other or share config settings through `screenshot-config.js`.)

### 2.1 `auto-snapshot.js`
1. **What It Does**  
   - Runs a one-time snapshot process using [Puppeteer](https://github.com/puppeteer/puppeteer) to **capture screenshots** of predefined pages (`/`, `/hvem-er-vi`, etc.).
   - Creates a unique folder for each “snapshot run” inside `.ai-analysis` named `snapshot-<timestamp>`.
   - Maintains a `metadata.json` that tracks all snapshots and keeps only the most recent N snapshots (defined by `maxSnapshots`).

2. **Detailed Purpose**  
   1. **Setup Directories**: Ensures `.ai-analysis` and a `latest` subfolder exist. If `metadata.json` is missing, it creates one.
   2. **Capture Logic**: For each page in `CONFIG.pages`, it does:
      - Launches Puppeteer.
      - Navigates to `http://localhost:<port>` plus the page’s path.
      - Waits for a short delay (`captureDelay`) to let the page finish loading or animating.
      - Takes the screenshot, saves it in the timestamped folder *and* copies it to `.ai-analysis/latest`.
   3. **Metadata Update**: Appends info about the new snapshot to `metadata.json`, removes old snapshots if above `maxSnapshots`, and sets `lastSnapshot`.
   4. **Generate AI Summary**: Writes a Markdown summary (`ai-summary.md`) about the pages captured and how to reference them. Copies that summary to both the snapshot folder and the `latest` folder.

> **Big Picture**: `auto-snapshot.js` is the standalone script if you just want to run a snapshot once. It’s also invoked by other scripts (e.g., `dev-with-snapshots.js` or `refresh-screenshots.js`) to automate the capturing step.

---

### 2.2 `dev-with-snapshots.js`
1. **What It Does**  
   - Launches the Vite/React dev server (`npm run dev`) **and** sets up watchers/HTTP endpoints to trigger new snapshots on file changes or HTTP calls.
   - Enforces a cooldown (`snapshotCooldown`) so that if you change multiple files in quick succession, it won’t keep snapping repeatedly.

2. **Detailed Purpose**  
   1. **Start Dev Server**: Spawns `npm run dev` in the background.
   2. **HTTP Trigger Server**: Listens on port 5174 with endpoints:
      - `GET /trigger-snapshot`: Force a new snapshot if cooldown has passed.
      - `GET /status`: Check last snapshot time and cooldown status.
      - `GET /latest-path`: Returns the “latest” screenshots path.
   3. **File Watchers**: Watches your `src` directory (and certain file extensions) for changes. If a matching file changes, it attempts to run a new snapshot (again respecting the cooldown).
   4. **Initial Snapshot**: After the dev server is up, it does an immediate snapshot so you start with a fresh set of images.

> **Big Picture**: If you run this script instead of a normal `npm run dev`, you get the dev server plus continuous screenshot captures. Good for quick feedback loops or letting an AI reference the updated visuals.

---

### 2.3 `refresh-screenshots.js`
1. **What It Does**  
   - Provides a **three-step “refresh”** pipeline:
     1. Capture new screenshots.
     2. Tidy the screenshots directory.
     3. Clean out old screenshots based on a retention period.

2. **Detailed Purpose**  
   - It calls `runScreenshotCommand("capture")`, `runScreenshotCommand("tidy")`, and `runScreenshotCommand("clean", ["7"])` in sequence.  
   - Those commands are processed by the separate `screenshot-manager.js` script, meaning it’s basically a convenience script that does a full cycle of:
     1. Generating new captures.
     2. Organizing them (via tidying).
     3. Removing older-than-7-days content.

> **Big Picture**: Use this script when you want a single command that updates all screenshots, cleans, and reorganizes them neatly.

---

### 2.4 `screenshot-manager.js`
1. **What It Does**  
   - Acts as a **CLI entry point** for screenshot-related tasks: `capture`, `clean`, `compare`, `schedule`, `unschedule`, `tidy`, and `help`.
   - Pulls its configuration from `config/screenshot-config.js`.
   - Dispatches to relevant utility functions in `utils/screenshot-utils.js`.

2. **Detailed Purpose**  
   1. **Parsing Commands**: Reads the first CLI argument to decide which operation to run. e.g.:
      - `node screenshot-manager.js capture`
      - `node screenshot-manager.js clean 3`
      - `node screenshot-manager.js schedule 12`
      - etc.
   2. **Scheduling**:  
      - On Windows, it uses `schtasks` to create or remove scheduled tasks for captures.
      - On non-Windows (Linux/macOS), it modifies the crontab.
   3. **Tidying & Cleaning**: Calls the underlying utility scripts for removing older captures, or systematically tidying the screenshots folder.
   4. **Compare** (listed but not shown in detail): Potentially used for comparing two sets of screenshots, though the snippet doesn't show its implementation.  

> **Big Picture**: `screenshot-manager.js` is **the** general command dispatcher for your entire screenshot workflow, wrapping up all the standard tasks in one place.

---

### 2.5 `tidy-screenshots.js`
1. **What It Does**  
   - **Cleans and organizes** your `screenshots` directory so that extraneous files are removed, `.gitignore` is properly set, and `.gitkeep` placeholders exist to preserve empty directories in version control.

2. **Detailed Purpose**  
   1. **`.gitignore` Creation**: Ensures the `screenshots` folder has a `.gitignore` that ignores everything except `README.md`, `.gitignore`, `.gitkeep`, and `latest/` (but not the contents within `latest/`).
   2. **`.gitkeep` Files**: Creates `.gitkeep` in relevant subdirectories so that empty folders can be tracked in Git.
   3. **Removing Legacy Directories**: If there is a `legacy-backup` folder, it is deleted.
   4. **Removing Loose Files**: Anything not recognized (like random stray images) is removed if it’s not part of the standard structure.  
   5. **Optimizing `screenshot-report.html`**: Adds lazy loading to `<img>` tags and a viewport meta tag for better mobile display.

> **Big Picture**: This script keeps your screenshot folder tree neat, version-control-friendly, and lighter weight.

---

### 2.6 `config/screenshot-config.js`
1. **What It Does**  
   - Centralized **configuration** for all screenshot operations.
   - Holds directory paths (e.g. `screenshotsDir`, `aiAnalysisDir`), script references (`captureScript`, `tidyScript`), and default values (like `defaultRetention: 7`).

2. **Detailed Purpose**  
   - `projectRoot` is set by working out the path relative to itself.
   - `COMMANDS` is an export that enumerates valid command keywords (`capture`, `clean`, etc.), used by `screenshot-manager.js` to parse CLI actions.
   - Values like `snapshotCooldown`, `watchExtensions`, and `windowsTaskName` are stored so multiple scripts can share the same config without duplicating them.

> **Big Picture**: A single place to change default screenshot retention or folder names, ensuring consistency across scripts.

---

### 2.7 `utils/screenshot-utils.js`
1. **What It Does**  
   - A set of **helper functions** that screenshot-manager and others rely on for:
     - Capturing screenshots (`captureScreenshots`)
     - Removing old screenshots (`cleanOldScreenshots`)
     - Running the “tidy” script (`tidyScreenshots`)
     - Ensuring certain directories exist (`ensureDirectories`)

2. **Detailed Purpose**  
   1. **`captureScreenshots()`**: Invokes `CONFIG.captureScript` (which is defined as `capture-website.js`, though not shown above) via `node ...`.
   2. **`cleanOldScreenshots(days)`**: Reads the `screenshots` folder, checks each timestamped directory, and deletes those older than `X` days. Skips the `latest` folder or anything that doesn’t match the naming pattern.
   3. **`tidyScreenshots()`**: Calls the `tidyScript` via `node ...`, effectively delegating to the logic in `tidy-screenshots.js`.
   4. **`ensureDirectories()`**: Makes sure core directories (`screenshotsDir`, `aiAnalysisDir`, etc.) are in place if they don’t already exist.

> **Big Picture**: This is the functional backbone beneath `screenshot-manager.js`, performing the actual file operations and script calls.

---

## 3. Overall Purpose and Usage

1. **Primary Goal**:  
   - To **capture** and **maintain** up-to-date visual snapshots for the project. These can help with:
     - Automated testing and AI-based analysis,
     - Visual regression checks,
     - Quick reference for design or layout changes.

2. **Integration Points**:  
   - **Development**: Run `dev-with-snapshots.js` instead of a normal dev script to have auto-captures on code changes.
   - **Automation**: Use `screenshot-manager.js` commands to schedule regular captures (e.g., daily or hourly).
   - **Housekeeping**: Periodically run `refresh-screenshots.js` to ensure everything is tidy and old images are removed.
   - **Manual Snapshot**: If you just want a single capture at any time, run `npm run ai:snapshot` or `auto-snapshot.js`.

3. **Key Takeaways**:  
   - The scripts revolve around **two main directories**:
     - **`.ai-analysis`**: Where snapshots for AI analysis are stored with metadata and a “latest” pointer.
     - **`screenshots`**: A more generic screenshot folder for general usage and housekeeping.
   - They rely on **Puppeteer** to automate browser actions and **Node.js** child processes to schedule tasks, watch files, and execute commands.
   - **Config-driven**: Almost all paths and policies are adjustable in `config/screenshot-config.js`.

---

### Final Summary

- **Exact Function**: These scripts collectively manage the lifecycle of website screenshots—capturing them automatically or on demand, storing them in a well-structured format, updating metadata, and cleaning up old resources.
- **Exact Purpose**: They provide a robust workflow for developers (or AI assistants) to continuously check the visual state of the application, either to confirm UI changes or feed updated imagery into AI-based analysis. This is especially helpful if you’re iterating quickly and want to see if or how your code changes affect the UI.

That is the essence and purpose of each file in the `scripts` folder.

## Message 4

The attached filestructure represents the particular project they belong to, please propose an optimal way of consolidating the screenshot utility (to execute only on demand) into a single `website-snapshot.js`:



    ```

    ├── .gitignore

    ├── .gitkeep

    ├── capture-website.js

    ├── eslint.config.js

    ├── index.html

    ├── package-lock.json

    ├── package.json

    ├── postcss.config.js

    ├── rl-website-initial-notes.md

    ├── tailwind.config.js

    ├── tsconfig.app.json

    ├── tsconfig.json

    ├── tsconfig.node.json

    ├── vite.config.ts

    ├── public

    │   ├── robots.txt

    │   ├── site.webmanifest

    │   ├── sitemap.xml

    │   └── images

    │       ├── metadata.json

    │       ├── categorized

    │       │   ├── belegg

    │       │   ├── ferdigplen

    │       │   ├── hekk

    │       │   ├── kantstein

    │       │   ├── platting

    │       │   ├── stål

    │       │   ├── støttemur

    │       │   └── trapp-repo

    │       ├── site

    │       └── team

    ├── screenshots

    │   ├── .gitignore

    │   ├── .gitkeep

    │   ├── README.md

    │   ├── screenshot-report.html

    │   └── latest

    │       ├── .gitkeep

    │       ├── desktop

    │       │   ├── about-desktop.html

    │       │   ├── contact-desktop.html

    │       │   ├── home-desktop.html

    │       │   ├── projects-desktop.html

    │       │   └── services-desktop.html

    │       ├── mobile

    │       │   ├── about-mobile.html

    │       │   ├── contact-mobile.html

    │       │   ├── home-mobile.html

    │       │   ├── projects-mobile.html

    │       │   └── services-mobile.html

    │       └── tablet

    │           ├── about-tablet.html

    │           ├── contact-tablet.html

    │           ├── home-tablet.html

    │           ├── projects-tablet.html

    │           └── services-tablet.html

    ├── scripts

    │   ├── auto-snapshot.js

    │   ├── dev-with-snapshots.js

    │   ├── refresh-screenshots.js

    │   ├── screenshot-manager.js

    │   ├── tidy-screenshots.js

    │   ├── config

    │   │   └── screenshot-config.js

    │   └── utils

    │       └── screenshot-utils.js

    └── src

        ├── App.tsx

        ├── index.css

        ├── index.html

        ├── main.tsx

        ├── vite-env.d.ts

        ├── components

        │   ├── Navbar.tsx

        │   ├── ServiceCard.tsx

        │   ├── index.ts

        │   ├── common

        │   │   ├── Button.tsx

        │   │   ├── Container.tsx

        │   │   ├── Hero.tsx

        │   │   ├── ImageGallery.tsx

        │   │   ├── LocalExpertise.tsx

        │   │   ├── LocalServiceArea.tsx

        │   │   ├── Logo.tsx

        │   │   ├── SeasonalCTA.tsx

        │   │   ├── ServiceAreaList.tsx

        │   │   ├── WeatherAdaptedServices.tsx

        │   │   └── index.ts

        │   ├── contact

        │   │   └── ContactForm.tsx

        │   ├── layout

        │   │   ├── Footer.tsx

        │   │   ├── Header.tsx

        │   │   ├── Hero.tsx

        │   │   ├── Layout.tsx

        │   │   ├── Meta.tsx

        │   │   ├── Navbar.tsx

        │   │   └── index.ts

        │   ├── local

        │   │   ├── SeasonalGuide.tsx

        │   │   ├── ServiceAreaMap.tsx

        │   │   └── WeatherNotice.tsx

        │   ├── projects

        │   │   ├── ProjectCard.tsx

        │   │   ├── ProjectFilter.tsx

        │   │   ├── ProjectGallery.tsx

        │   │   └── ProjectGrid.tsx

        │   ├── seo

        │   │   └── TestimonialsSchema.tsx

        │   ├── services

        │   │   ├── Gallery.tsx

        │   │   └── ServiceCard.tsx

        │   ├── shared

        │   │   ├── ErrorBoundary.tsx

        │   │   ├── Elements

        │   │   │   ├── Card.tsx

        │   │   │   ├── Icon.tsx

        │   │   │   ├── Image.tsx

        │   │   │   ├── Link.tsx

        │   │   │   ├── Loading.tsx

        │   │   │   ├── index.ts

        │   │   │   └── Form

        │   │   │       ├── Input.tsx

        │   │   │       ├── Select.tsx

        │   │   │       ├── Textarea.tsx

        │   │   │       └── index.ts

        │   │   └── Layout

        │   │       ├── Layout.tsx

        │   │       └── index.ts

        │   └── ui

        │       ├── Button.tsx

        │       ├── Container.tsx

        │       ├── Hero.tsx

        │       ├── Intersection.tsx

        │       ├── Logo.tsx

        │       ├── Notifications.tsx

        │       ├── SeasonalCTA.tsx

        │       ├── ServiceAreaList.tsx

        │       ├── Skeleton.tsx

        │       ├── Transition.tsx

        │       └── index.ts

        ├── config

        │   ├── images.ts

        │   ├── routes.ts

        │   └── site.ts

        ├── content

        │   ├── index.ts

        │   ├── services

        │   │   └── index.ts

        │   ├── team

        │   │   └── index.ts

        │   └── testimonials

        │       └── index.ts

        ├── data

        │   ├── projects.ts

        │   ├── services.ts

        │   └── testimonials.ts

        ├── features

        │   ├── home.tsx

        │   ├── projects.tsx

        │   ├── services.tsx

        │   ├── team.tsx

        │   ├── testimonials.tsx

        │   ├── home

        │   │   ├── FilteredServicesSection.tsx

        │   │   ├── SeasonalProjectsCarousel.tsx

        │   │   ├── SeasonalServicesSection.tsx

        │   │   └── index.ts

        │   ├── projects

        │   │   ├── ProjectCard.tsx

        │   │   ├── ProjectFilter.tsx

        │   │   ├── ProjectGallery.tsx

        │   │   ├── ProjectGrid.tsx

        │   │   ├── ProjectsCarousel.tsx

        │   │   └── index.ts

        │   ├── services

        │   │   ├── ServiceCard.tsx

        │   │   ├── ServiceFeature.tsx

        │   │   ├── ServiceGrid.tsx

        │   │   ├── data.ts

        │   │   └── index.ts

        │   └── testimonials

        │       ├── AverageRating.tsx

        │       ├── Testimonial.tsx

        │       ├── TestimonialFilter.tsx

        │       ├── TestimonialSlider.tsx

        │       ├── TestimonialsSection.tsx

        │       ├── data.ts

        │       └── index.ts

        ├── hooks

        │   └── useData.ts

        ├── lib

        │   ├── constants.ts

        │   ├── content.ts

        │   ├── hooks.ts

        │   ├── index.ts

        │   ├── types.ts

        │   ├── utils.ts

        │   ├── api

        │   │   └── index.ts

        │   ├── config

        │   │   ├── images.ts

        │   │   ├── index.ts

        │   │   ├── paths.ts

        │   │   └── site.ts

        │   ├── context

        │   │   └── AppContext.tsx

        │   ├── hooks

        │   │   ├── images.ts

        │   │   ├── index.ts

        │   │   ├── useAnalytics.ts

        │   │   ├── useDebounce.ts

        │   │   ├── useEventListener.ts

        │   │   ├── useForm.ts

        │   │   ├── useFormField.ts

        │   │   ├── useIntersectionObserver.ts

        │   │   ├── useLocalStorage.ts

        │   │   └── useMediaQuery.ts

        │   ├── types

        │   │   ├── common.ts

        │   │   ├── components.ts

        │   │   ├── content.ts

        │   │   └── index.ts

        │   └── utils

        │       ├── analytics.ts

        │       ├── date.ts

        │       ├── images.ts

        │       ├── index.ts

        │       ├── seo.ts

        │       └── validation.ts

        ├── pages

        │   ├── ProjectDetail.tsx

        │   ├── Projects.tsx

        │   ├── ServiceDetail.tsx

        │   ├── Services.tsx

        │   ├── TestimonialsPage.tsx

        │   ├── about

        │   │   └── index.tsx

        │   ├── contact

        │   │   └── index.tsx

        │   ├── home

        │   │   └── index.tsx

        │   ├── projects

        │   │   ├── detail.tsx

        │   │   └── index.tsx

        │   ├── services

        │   │   ├── detail.tsx

        │   │   └── index.tsx

        │   └── testimonials

        │       └── index.tsx

        ├── styles

        │   ├── animations.css

        │   ├── base.css

        │   └── utilities.css

        ├── types

        │   ├── content.ts

        │   └── index.ts

        └── utils

            └── imageLoader.ts

    ```


## Message 5

**Consolidating scripts**

OK, let me see. Organizing all screenshot functionalities into one script, "website-snapshot.js", to streamline management and eliminate redundant scripts. Progressing towards an efficient solution.

**Crafting the script**

I'm pulling together "website-snapshot.js" to:

- Start Puppeteer
- Capture and store screenshots
- Embed or read config
- Create a main function and usage instructions
- Outline steps for managing and tidying up screenshots.

**Piecing together the script**

Integrating auto-snapshot logic and config references, "website-snapshot.js" works on demand without scheduling. It features a `captureSnapshots` function utilizing Puppeteer for efficient snapshot capture.

**Piecing together metadata**

I'm crafting "website-snapshot.js" to retain metadata from auto-snapshot. It captures snapshots on demand, stores them, and provides usage instructions for running the capture command.

## Message 6

Below is one possible approach to **consolidate everything** into a single script called `website-snapshot.js` that:

1. **Captures screenshots on demand** using Puppeteer (no watchers or schedules).  
2. **Handles multiple pages** and multiple viewports (optional).  
3. **Stores snapshots** in time-stamped folders and keeps a `latest` reference if desired.  

This approach assumes:
- You **already have a running server** on some port (e.g. `npm run dev` in a separate terminal).  
- You **don’t need** auto-watchers, dev-server triggers, or scheduling tasks—just **on-demand** captures.  
- You’d like to **keep** a “latest” folder in `screenshots/latest`, plus any number of older snapshot folders named by timestamp.  
- You’re comfortable having all logic self-contained in a single file so that you can remove `scripts/` if you wish.

> **Tip**: If you want to start/stop the dev server automatically inside the script, you could adapt the code. But below is the simplest “on-demand” approach: **start your server separately**, then run `node website-snapshot.js`.

---

## 1. Proposed `website-snapshot.js`

Create a new file at the project root (same level as `package.json`) named **`website-snapshot.js`**:

```js
/**
 * website-snapshot.js
 *
 * A single on-demand script to capture website screenshots via Puppeteer.
 * Run: node website-snapshot.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import puppeteer from 'puppeteer';

/* ------------------------------------------------------------------
   1. Setup Paths & Configuration
   ------------------------------------------------------------------ */
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = __dirname; // or path.resolve(__dirname, '..') if desired

// Where to save screenshots
const SCREENSHOTS_DIR = path.join(projectRoot, 'screenshots');
const LATEST_DIR = path.join(SCREENSHOTS_DIR, 'latest');

// Pages you want to capture
const PAGES = [
  { path: '/', name: 'home' },
  { path: '/hvem-er-vi', name: 'about' },
  { path: '/hva-vi-gjor', name: 'services' },
  { path: '/prosjekter', name: 'projects' },
  { path: '/kontakt', name: 'contact' },
];

// Optional: multiple viewports if you want different device sizes
const VIEWPORTS = [
  { width: 390, height: 844, name: 'mobile' },
  { width: 834, height: 1112, name: 'tablet' },
  { width: 1440, height: 900, name: 'desktop' },
];

// The port where your dev server is running.
// Make sure "npm run dev" (or similar) is started separately.
const DEV_PORT = 5173;

// If you want to wait a bit after loading each page for animations, etc.
const CAPTURE_DELAY_MS = 2000;

/* ------------------------------------------------------------------
   2. Directory Setup
   ------------------------------------------------------------------ */
function ensureDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Ensure base screenshot directories exist
ensureDirectory(SCREENSHOTS_DIR);
ensureDirectory(LATEST_DIR);

// Create a timestamp-based folder for each run
function createTimestampedDir() {
  const now = new Date();
  const timestamp = now
    .toISOString()
    .replace(/T/, '_')
    .replace(/\..+/, '')
    .replace(/:/g, '-'); 
  // e.g. "2025-03-12_12-34-56"

  const snapshotDir = path.join(SCREENSHOTS_DIR, timestamp);
  ensureDirectory(snapshotDir);
  return snapshotDir;
}

/* ------------------------------------------------------------------
   3. Main Capture Logic
   ------------------------------------------------------------------ */
async function capturePage(browser, snapshotDir, pagePath, pageName, viewport) {
  const url = `http://localhost:${DEV_PORT}${pagePath}`;
  const fileSuffix = `${pageName}-${viewport.name}.png`;

  try {
    // New page in the browser
    const page = await browser.newPage();
    await page.setViewport({ width: viewport.width, height: viewport.height });
    await page.goto(url, { waitUntil: 'networkidle2' });
    
    // Wait for any animations or dynamic content
    await new Promise((resolve) => setTimeout(resolve, CAPTURE_DELAY_MS));

    // Build final file paths
    const viewportDir = path.join(snapshotDir, viewport.name);
    ensureDirectory(viewportDir);

    const screenshotPath = path.join(viewportDir, fileSuffix);
    await page.screenshot({ path: screenshotPath, fullPage: true });

    // Also copy to "latest/<viewport>"
    const latestViewportDir = path.join(LATEST_DIR, viewport.name);
    ensureDirectory(latestViewportDir);
    const latestScreenshotPath = path.join(latestViewportDir, fileSuffix);
    fs.copyFileSync(screenshotPath, latestScreenshotPath);

    await page.close();
    console.log(`Captured: ${pageName} (${viewport.name})`);
  } catch (error) {
    console.error(`Error capturing ${pageName} (${viewport.name}):`, error);
  }
}

async function captureAllPages(snapshotDir) {
  // Launch Puppeteer once
  const browser = await puppeteer.launch();

  // For each viewport, for each page
  for (const vp of VIEWPORTS) {
    for (const pg of PAGES) {
      await capturePage(browser, snapshotDir, pg.path, pg.name, vp);
    }
  }

  await browser.close();
}

/* ------------------------------------------------------------------
   4. Run the Script
   ------------------------------------------------------------------ */
async function main() {
  try {
    console.log('Starting screenshot capture...');
    const snapshotDir = createTimestampedDir();
    await captureAllPages(snapshotDir);
    console.log('\nAll screenshots captured successfully!\n');
    console.log(`- Timestamped folder: ${snapshotDir}`);
    console.log(`- Latest folder:       ${LATEST_DIR}`);
  } catch (err) {
    console.error('Snapshot process failed:', err);
    process.exit(1);
  }
}

// Invoke main if called directly (node website-snapshot.js)
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
```

### How it Works
1. **Configuration Block**  
   - Defines which pages to capture (`PAGES`), which viewports to use (`VIEWPORTS`), and the port to connect to your local dev server.
2. **Directory Setup**  
   - Creates (if not exists) a `screenshots` folder in your project root, plus a `latest` subfolder.
   - Each run gets its own time-stamped subfolder, e.g. `screenshots/2025-03-12_12-34-56`.
3. **Capture Loop**  
   - Puppeteer is launched once.  
   - For each viewport (mobile, tablet, desktop, etc.) and each page, it navigates to `http://localhost:<DEV_PORT><PAGE_PATH>`, waits, and saves a screenshot:
     - **Time-stamped folder** for permanent record (e.g. `screenshots/2025-03-12_12-34-56/mobile/home-mobile.png`).
     - **Copied to** `screenshots/latest/mobile/home-mobile.png` (the “latest” reference).
4. **Final Output**  
   - Logs the location of both your new time-stamped folder and the “latest” folder.

> **Note**: We removed watchers, refresh steps, scheduling, metadata/AI analysis references, etc., to keep it **purely** on-demand. If you need those features, you can embed or adapt the relevant parts.

---

## 2. How to Use

1. **Install Puppeteer** (if not already):  
   ```bash
   npm install puppeteer
   ```
   
2. **Ensure your dev server is running**  
   - For example: `npm run dev` (assuming it starts on port 5173, matching `DEV_PORT` in the script).  

3. **Run the script**  
   ```bash
   node website-snapshot.js
   ```

4. **See the results**  
   - A new folder `screenshots/<timestamp>` is created, containing subfolders for each viewport (mobile/tablet/desktop).  
   - The script also updates `screenshots/latest/<viewport>/*` so you can always reference the most recent images.

---

## 3. Optional Tweaks

- **Add to `package.json`**:  
  ```json
  "scripts": {
    "snapshot": "node website-snapshot.js"
  }
  ```
  Then run with `npm run snapshot`.

- **Cleaning old snapshots**:  
  - If you still want to remove older time-stamped folders occasionally, you could add a small function in the same file that checks for directories older than X days and deletes them.  

- **Single vs. Multiple Viewports**:  
  - If you only need one viewport, remove the `VIEWPORTS` array and just set a single `width/height`.

- **Targeting Different Servers**:  
  - If you have a staging or production URL, simply change the `url` to point externally (and remove or adjust `DEV_PORT`).

- **Screen or DOM Interactions**:  
  - If you need to click something or wait for specific elements, you can expand `capturePage()` with more Puppeteer logic (e.g., `await page.waitForSelector('.my-element')`).

---

## 4. Final Takeaway

By placing all screenshot capture code into **one `website-snapshot.js`** script, you:

- Eliminate the overhead of watchers, dev triggers, scheduling, or AI-oriented metadata.  
- Simplify the process to just **“run a single command when you need screenshots.”**  
- Maintain a straightforward, minimal maintenance approach.

That’s it—**one file** controlling everything you need for quick, on-demand website snapshots.
