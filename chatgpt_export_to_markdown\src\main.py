#!/usr/bin/env python3
"""
Robust ChatGPT Export Converter
-------------------------------
A more robust version of the convoviz utility that handles errors gracefully.
"""

import argparse
import json
import os
import sys
import traceback
from pathlib import Path
from typing import Dict, List, Union

# Import third-party libraries
try:
    from tqdm import tqdm
except ImportError:
    # Define a simple tqdm replacement if not available
    def tqdm(iterable, *args, **kwargs):
        disable = kwargs.get('disable', False)
        if not disable:
            total = len(iterable) if hasattr(iterable, '__len__') else None
            print(f"Processing {total} items...")
        return iterable

# Import the robust conversation set
try:
    from .conversation_set import RobustConversationSet
    from .utils import make_long_path_safe
except ImportError:
    # Handle direct execution
    from conversation_set import RobustConversationSet
    from utils import make_long_path_safe

# Import convoviz
import convoviz





def load_conversations(json_path: Union[str, Path]) -> List[Dict]:
    """Load conversations from a JSON file."""
    json_path = Path(json_path)
    safe_path = make_long_path_safe(json_path)

    try:
        with safe_path.open(encoding="utf-8") as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        sys.exit(1)


def convert_zip_to_markdown(
    zip_file: Union[str, Path],
    output_dir: Union[str, Path],
    verbose: bool = False
) -> None:
    """Convert a ChatGPT export zip file to markdown files."""
    zip_path = Path(zip_file)
    output_dir = Path(output_dir)
    safe_output_dir = make_long_path_safe(output_dir)

    print(f"\nStarting ChatGPT export conversion...")

    # Extract the zip file
    print("\nLoading data...")

    try:
        # Use the robust conversation set to load the zip file
        conversation_set = RobustConversationSet.from_zip(zip_path, verbose=verbose)

        total_conversations = len(conversation_set.array) + len(conversation_set.skipped_conversations)
        print(f"Found {total_conversations} conversations in the export file.")

        if conversation_set.skipped_conversations:
            print(f"\nWarning: {len(conversation_set.skipped_conversations)} conversations could not be processed due to errors.")

            # Save skipped conversations for later analysis
            skipped_path = safe_output_dir.parent / "skipped_conversations.json"
            conversation_set.save_skipped(skipped_path)

        # Create output directory
        safe_output_dir.mkdir(parents=True, exist_ok=True)

        # Save the conversations
        if conversation_set.array:
            print(f"\nSuccessfully processed {len(conversation_set.array)} conversations.")

            # Try to save conversations
            try:
                conversation_set.save(safe_output_dir, progress_bar=True)
            except Exception as e:
                print(f"\nError during saving: {e}")
                if verbose:
                    traceback.print_exc()

                # If the save method fails, try a more basic approach
                print("\nAttempting to save conversations with basic method...")
                save_conversations_basic(conversation_set.array, output_dir, progress_bar=True)

            print(f"\nDone! Markdown files saved to: {output_dir}")
        else:
            print("No valid conversations found. Nothing to save.")
            if not verbose:
                print("Try running with --verbose to see detailed error information.")
            sys.exit(1)

    except Exception as e:
        print(f"Error: {e}")
        if not verbose:
            print("Try running with --verbose to see detailed error information.")
        else:
            traceback.print_exc()
        sys.exit(1)


def save_conversations_basic(conversations, output_dir: Path, progress_bar: bool = False) -> None:
    """A very basic method to save conversations as markdown files."""
    # tqdm is already imported at the top of the file

    successful = 0
    failed = 0

    for conversation in tqdm(conversations, "Writing Markdown files", disable=not progress_bar):
        try:
            # Create a safe filename
            title = getattr(conversation, 'title', 'Untitled')
            safe_title = "".join(c if c.isalnum() or c in " -_" else "_" for c in title)
            filepath = output_dir / f"{safe_title}.md"
            safe_filepath = make_long_path_safe(filepath)

            with safe_filepath.open("w", encoding="utf-8") as f:
                f.write(f"# {title}\n\n")

                # Add conversation ID if available
                conversation_id = getattr(conversation, 'conversation_id', None)
                if conversation_id:
                    f.write(f"Conversation ID: {conversation_id}\n\n")

                # Add messages if available
                messages = getattr(conversation, 'messages', [])
                for i, msg in enumerate(messages):
                    f.write(f"\n## Message {i+1}\n\n")

                    # Try different ways to get content
                    content = None
                    if hasattr(msg, 'content'):
                        content = msg.content

                    if content:
                        if isinstance(content, str):
                            f.write(f"{content}\n")
                        else:
                            f.write(f"{str(content)}\n")
                    else:
                        msg_id = getattr(msg, 'id', 'unknown')
                        f.write(f"[No content available for message ID: {msg_id}]\n")

            successful += 1
        except Exception as e:
            print(f"Warning: Failed to save conversation '{title}': {e}")
            failed += 1

    print(f"\nBasic save method: Saved {successful} conversations successfully. Failed to save {failed} conversations.")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Convert ChatGPT export zip files to markdown format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    parser.add_argument(
        "--zip-file",
        type=str,
        help="Path to the ChatGPT export zip file"
    )

    parser.add_argument(
        "--output-dir",
        type=str,
        default="outputs",
        help="Directory to save the markdown files (default: ./outputs)"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Show detailed error information"
    )

    args = parser.parse_args()

    # Print header
    print("───────── ChatGPT Export Converter ─────────")
    print("Convert ChatGPT export zip files to markdown format")
    print("───────────────────────────────────────────\n")

    # Get zip file path if not provided
    zip_file = args.zip_file
    if not zip_file:
        print("--zip-file argument is missing.")
        zip_file = input("Please enter the path to the ChatGPT export zip file: ")

    # Convert the zip file
    convert_zip_to_markdown(zip_file, args.output_dir, args.verbose)


if __name__ == "__main__":
    main()
