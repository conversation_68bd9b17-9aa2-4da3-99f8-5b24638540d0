# Python script for 3dsmax

Conversation ID: 67a5c963-0db0-8008-9e2d-ff5aa7b35913

## Message 1

are you capable of writing python script for 3dsmax that will execute correctly in 3dsmax 2025?

## Message 2

**Considering 3dsMax 2025 Python scripting**

The user asked if I can write a Python script for 3dsMax 2025. Yes, I can write Python scripts for 3dsMax, and they are typically executed using MAXScript. Wait, I should check: 3dsMax 2025 supports Python. Generally, 3dsMax integrates Python scripting, and I think it supports Python 3.9 as of 2023? But I’m not entirely sure about the exact version in 2025. I'll check further, just to be sure.

**Confirming Python scripting for 3dsMax**

Yes, I can write Python scripts for 3dsMax 2025! However, I'll need more specifics to create exactly what you need. For instance, do you have a particular functionality in mind? I’ll be using 3dsMax's Python environment, which usually involves pymxs or MaxPlus. As of 3dsMax 2021, pymxs is the standard, and I expect the same for 2025. I can provide examples once I understand what you need more clearly!

## Message 3

Yes, I can help write Python scripts for Autodesk 3ds Max that run correctly in the 2025 version. In recent versions of 3ds Max, Python is typically used through the `pymxs` module, which provides access to the 3ds Max API in a Pythonic way.

Could you let me know what specific functionality or task you need the script to perform? That way, I can tailor the code to your requirements.

## Message 4

please explain exactly what this is and what it's for:

import sys

import os

import subprocess

import json

import fnmatch

from PySide6 import QtCore, QtGui, QtWidgets

from PySide6.QtCore import Qt, QDateTime, QSize

from PySide6.QtGui import QAction, QKeySequence

from pymxs import runtime as rt

import qtmax



PRINT_DEBUG_INFO = False



class Config:

    STYLESHEET_TEMPLATE = """

        QTreeWidget {{

            background-color: {tree_bg};

            color: {tree_fg};

            border: none;

            padding: {pad}px;

        }}

        QTreeWidget::item {{

            padding: {pad}px;

            font-size: {item_font_size}px;

        }}

        QTreeWidget::item:hover {{

            background-color: {hover_bg};

        }}

        QTreeWidget::item:selected {{

            background-color: {sel_bg};

        }}

        QTreeWidget::item:pressed {{

            background-color: {press_bg};

        }}

        QHeaderView::section {{

            background-color: {hdr_bg};

            color: {hdr_fg};

            padding: {pad}px;

            font-size: {hdr_font_size}px;

            border: 1px solid {hdr_border};

        }}

    """



    class UI:

        MIN_DOCK_WIDTH = 300

        MIN_DOCK_HEIGHT = 400

        SPLITTER_RATIO = [1, 2]

        ICON_SIZE = QSize(20, 20)

        TREE_INDENT = 20

        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}

        SEARCH_DEBOUNCE_MS = 150

        SEARCH_MIN_CHARS = 2

        AUTO_EXPAND_RESULTS = True

        PRESERVE_EXPANSION = False

        FOLDERS_EXPANDABLE = True

        DOUBLE_CLICK_ACTION = "run"

        BUTTON_PADDING_RATIO = 0.002

        HEADER_FONT_RATIO = 0.007

        ITEM_FONT_RATIO = 0.005



    class Style:

        FAVORITES_BG = "#1a1a1a"

        TREE_BG = "#2b2b2b"

        FAVORITES_FG = "#f0f0f0"

        TREE_FG = "#f0f0f0"

        PIN_COLOR = "#79ff1c"

        HDR_BG = "#2b2b2b"

        HDR_FG = "#f0f0f0"

        HDR_BORDER = "#3c3c3c"

        HOVER_BG = "#3c3c3c"

        SEL_BG = "#1e90ff"

        PRESS_BG = "#104e8b"

        TINT_RULES = [

            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},

            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},

            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},

            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},

        ]



    class Paths:

        SUPPORTED = ('.ms', '.mse', '.json')

        MAX_DEPTH = 8



        @classmethod

        def favorites_file(cls):

            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")



        @classmethod

        def run_counts_file(cls):

            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")



    class Menu:

        CONTEXT = {

            'file': [

                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},

                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},

                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},

                {'text': '-'},

                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+P', 'icon': 'SP_DialogYesButton'},

                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}

            ],

            'folder': [

                {'text': 'Expand All', 'shortcut': 'Ctrl+E', 'icon': 'SP_FileDialogDetailedView'},

                {'text': 'Collapse All', 'shortcut': 'Ctrl+C', 'icon': 'SP_FileDialogListView'},

                {'text': '-'},

                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}

            ],

            'favorites': [

                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},

                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},

                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},

                {'text': '-'},

                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}

            ]

        }



    def stylesheet(self):

        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()

        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)

        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))

        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))

        return self.STYLESHEET_TEMPLATE.format(

            tree_bg=Config.Style.TREE_BG,

            tree_fg=Config.Style.TREE_FG,

            hdr_bg=Config.Style.HDR_BG,

            hdr_fg=Config.Style.HDR_FG,

            hdr_border=Config.Style.HDR_BORDER,

            hover_bg=Config.Style.HOVER_BG,

            sel_bg=Config.Style.SEL_BG,

            press_bg=Config.Style.PRESS_BG,

            pad=pad,

            hdr_font_size=hdr_fs,

            item_font_size=item_fs

        )



def create_menu(menu_items, parent):

    m = QtWidgets.QMenu(parent)

    for it in menu_items:

        if it['text'] == '-':

            m.addSeparator()

            continue

        a = QAction(it['text'], parent)

        if 'shortcut' in it:

            a.setShortcut(QKeySequence(it['shortcut']))

        if 'icon' in it:

            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))

            a.setIcon(icon)

        m.addAction(a)

    return m



def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):

    if depth > max_depth:

        return {}

    structure = {}

    try:

        with os.scandir(path) as it:

            dirs, files = [], []

            for e in it:

                if e.is_dir():

                    dirs.append(e)

                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):

                    files.append(e)

            dirs.sort(key=lambda x: x.name.lower())

            files.sort(key=lambda x: x.name.lower())

            for d in dirs:

                sub = scan_dir(d.path, max_depth, depth+1)

                if sub:

                    structure[d.name] = sub

            for f in files:

                structure[f.name] = f.path

    except PermissionError:

        pass

    return structure



def blend(base_hex, overlay_hex, alpha=0.3):

    base = QtGui.QColor(base_hex)

    over = QtGui.QColor(overlay_hex)

    r = int(base.red()*(1-alpha) + over.red()*alpha)

    g = int(base.green()*(1-alpha) + over.green()*alpha)

    b = int(base.blue()*(1-alpha) + over.blue()*alpha)

    c = QtGui.QColor(r, g, b).name()

    if PRINT_DEBUG_INFO:

        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")

    return c



class ScriptItem(QtWidgets.QTreeWidgetItem):

    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):

        super().__init__([name, "" if folder else str(count)])

        self.original_name = name

        self.script_path = path

        self.is_folder = folder

        self.is_pinned = pinned

        self.run_count = count

        self.setData(0, Qt.UserRole, path)

        self.setData(0, Qt.UserRole+1, folder)

        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)

        self.set_icon()

        self.defer_tooltip()

        if pinned:

            self.setData(0, Qt.UserRole+2, True)

            self.setData(1, Qt.UserRole+2, True)

            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)

            self.setForeground(0, QtGui.QBrush(pcolor))

            self.setForeground(1, QtGui.QBrush(pcolor))

        if not folder:

            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)

        self.tint_background(parents)



    def set_icon(self):

        style = QtWidgets.QApplication.style()

        if self.is_pinned:

            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)

        elif self.is_folder:

            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)

        else:

            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)

        self.setIcon(0, ic)



    def defer_tooltip(self):

        if not self.is_folder and self.script_path:

            self.setToolTip(0, "Loading...")

            QtCore.QTimer.singleShot(100, self.load_tooltip)



    def load_tooltip(self):

        if self.script_path and os.path.exists(self.script_path):

            try:

                st = os.stat(self.script_path)

                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)

                sz = st.st_size

                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")

            except Exception as e:

                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")



    def update_run_count(self, n):

        self.run_count = n

        if not self.is_folder:

            self.setText(1, str(n))



    def tint_background(self, parents):

        tinted = False

        if parents:

            for rule in Config.Style.TINT_RULES:

                pat, c, a = rule['pattern'], rule['color'], rule['alpha']

                for f in parents:

                    if fnmatch.fnmatch(f.lower(), pat):

                        blended = blend(Config.Style.TREE_BG, c, a)

                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))

                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))

                        tinted = True

                        break

                if tinted:

                    break

        if not tinted:

            bg = QtGui.QColor(Config.Style.TREE_BG)

            self.setBackground(0, QtGui.QBrush(bg))

            self.setBackground(1, QtGui.QBrush(bg))



class FavoriteItem(QtWidgets.QListWidgetItem):

    def __init__(self, name, path, count=0):

        super().__init__(name)

        self.script_path = path

        self.run_count = count

        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})

        self.setToolTip(f"Path: {path}\nRun Count: {count}")



    def update_run_count(self, n):

        self.run_count = n

        d = self.data(Qt.UserRole)

        d['run_count'] = n

        self.setData(Qt.UserRole, d)

        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")



class Dock(QtWidgets.QDockWidget):

    def __init__(self, parent=None, scripts_dir=None):

        super().__init__(parent)

        self.setWindowTitle("Jorn Tools: Widget")

        self.setAllowedAreas(Qt.AllDockWidgetAreas)

        self.setObjectName("jornWidget_treeView")

        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)



        self.scripts_directory = scripts_dir

        self.favorites = {}

        self.run_counts = {}

        self._search_index = {}

        self._last_search = ""



        self.init_ui()

        self.load_run_counts()

        self.load_favorites()

        if self.scripts_directory:

            self.build_tree()

        self.init_file_watcher()

        self.init_shortcuts()



    def init_ui(self):

        c = QtWidgets.QWidget()

        self.setWidget(c)

        lay = QtWidgets.QVBoxLayout(c)

        split = QtWidgets.QSplitter(Qt.Vertical)

        lay.addWidget(split)



        fav_cont = QtWidgets.QWidget()

        fav_lay = QtWidgets.QVBoxLayout(fav_cont)

        fav_lay.setContentsMargins(0, 0, 0, 0)

        fav_label = QtWidgets.QLabel("Favorites")

        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")

        fav_lay.addWidget(fav_label)



        self.favorites_widget = QtWidgets.QListWidget()

        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)

        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)

        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)

        self.favorites_widget.setDropIndicatorShown(True)

        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)

        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)

        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)

        fav_lay.addWidget(self.favorites_widget)

        split.addWidget(fav_cont)



        h = QtWidgets.QApplication.primaryScreen().geometry().height()

        p = int(h * Config.UI.BUTTON_PADDING_RATIO)

        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))

        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))

        style = Config().stylesheet()

        self.favorites_widget.setStyleSheet(style)

        self.favorites_widget.setIconSize(QSize(20, 20))

        ff = self.favorites_widget.font()

        ff.setPointSize(it_fs)

        self.favorites_widget.setFont(ff)



        main_cont = QtWidgets.QWidget()

        main_lay = QtWidgets.QVBoxLayout(main_cont)

        main_lay.setContentsMargins(0, 0, 0, 0)

        main_label = QtWidgets.QLabel("Scripts")

        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")

        main_lay.addWidget(main_label)



        self.tree_widget = QtWidgets.QTreeWidget()

        self.tree_widget.setHeaderLabels(["Name", "Run Count"])

        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)

        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)

        self.tree_widget.setColumnWidth(1, 80)

        self.tree_widget.setHeaderHidden(False)

        hi = self.tree_widget.headerItem()

        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)

        self.tree_widget.setStyleSheet(style)

        self.tree_widget.setIconSize(QSize(20, 20))

        self.tree_widget.itemDoubleClicked.connect(self.run_script)

        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)

        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)

        self.tree_widget.setDragEnabled(True)

        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)

        main_lay.addWidget(self.tree_widget)



        self.search_bar = QtWidgets.QLineEdit()

        self.search_bar.setPlaceholderText("Search...")

        self.search_bar.setFocusPolicy(Qt.ClickFocus)

        self.search_bar.textChanged.connect(self.debounced_filter)

        main_lay.addWidget(self.search_bar)

        split.addWidget(main_cont)



        split.setStretchFactor(0, 0)

        split.setStretchFactor(1, 1)

        split.setSizes([300, 600])



        tf = self.tree_widget.font()

        tf.setPointSize(it_fs)

        self.tree_widget.setFont(tf)



    def init_shortcuts(self):

        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+P"), self)

        pin_sc.activated.connect(self.pin_selected)

        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)

        unpin_sc.activated.connect(self.unpin_selected)

        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)

        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)

        enter_sc.activated.connect(self.run_selected_if_focused)



    def init_file_watcher(self):

        self.fs_watcher = QtCore.QFileSystemWatcher()

        if self.scripts_directory:

            self.fs_watcher.addPath(self.scripts_directory)

            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)



    def load_favorites(self):

        fp = Config.Paths.favorites_file()

        if os.path.exists(fp):

            try:

                with open(fp, 'r') as f:

                    self.favorites = json.load(f)

                for n, pth in self.favorites.items():

                    if os.path.exists(pth):

                        c = self.run_counts.get(pth, 0)

                        it = FavoriteItem(n, pth, c)

                        self.favorites_widget.addItem(it)

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")

                self.favorites = {}

        else:

            self.save_favorites()



    def save_favorites(self):

        try:

            with open(Config.Paths.favorites_file(), 'w') as f:

                json.dump(self.favorites, f, indent=4)

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")



    def load_run_counts(self):

        rp = Config.Paths.run_counts_file()

        if os.path.exists(rp):

            try:

                with open(rp, 'r') as f:

                    self.run_counts = json.load(f)

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")

                self.run_counts = {}

        else:

            self.save_run_counts()



    def save_run_counts(self):

        try:

            with open(Config.Paths.run_counts_file(), 'w') as f:

                json.dump(self.run_counts, f, indent=4)

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")



    def build_tree(self):

        self.tree_widget.setUpdatesEnabled(False)

        self._search_index.clear()

        self.tree_widget.clear()

        self.folder_structure = scan_dir(self.scripts_directory)

        self.populate_tree(self.tree_widget, self.folder_structure, [])

        self.tree_widget.setUpdatesEnabled(True)



    def rebuild_tree(self, _path):

        self.build_tree()



    def populate_tree(self, parent, structure, parents):

        for name, val in structure.items():

            if isinstance(val, dict):

                it = ScriptItem(name, folder=True, parents=parents)

                if isinstance(parent, QtWidgets.QTreeWidget):

                    parent.addTopLevelItem(it)

                else:

                    parent.addChild(it)

                self.index_item(it)

                self.populate_tree(it, val, parents+[name])

            else:

                it = ScriptItem(name, path=val, parents=parents)

                if isinstance(parent, QtWidgets.QTreeWidget):

                    parent.addTopLevelItem(it)

                else:

                    parent.addChild(it)

                self.index_item(it)



    def refresh_item(self, it):

        if getattr(it, 'is_folder', False):

            fpath = it.script_path

            if not fpath or not os.path.isdir(fpath):

                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")

                return

            pf = self.parent_names(it)

            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)

            it.takeChildren()

            self.populate_tree(it, st, pf+[it.text(0)])

            it.setExpanded(False)

        else:

            p = getattr(it, 'script_path', None)

            if p and os.path.exists(p):

                c = self.run_counts.get(p, 0)

                it.update_run_count(c)

                it.defer_tooltip()

            else:

                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")



    def debounced_filter(self, text):

        if hasattr(self, '_filter_timer'):

            self._filter_timer.stop()

        self._filter_timer = QtCore.QTimer(singleShot=True)

        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))

        self._filter_timer.start(150)



    def apply_filter(self, t):

        self.tree_widget.setUpdatesEnabled(False)

        t = t.strip().lower()

        if not t:

            self.show_all(collapse=True)

        else:

            self.hide_all()

            self.show_matches(t)

        self.tree_widget.setUpdatesEnabled(True)



    def show_all(self, collapse=False):

        for items in self._search_index.values():

            for it in items:

                it.setHidden(False)

                p = it.parent()

                while p and p != self.tree_widget.invisibleRootItem():

                    p.setHidden(False)

                    if collapse:

                        p.setExpanded(False)

                    p = p.parent()



    def hide_all(self):

        for items in self._search_index.values():

            for it in items:

                it.setHidden(True)



    def show_matches(self, txt):

        found = set()

        for n, items in self._search_index.items():

            if txt in n:

                for it in items:

                    found.add(it)

                    p = it.parent()

                    while p and p != self.tree_widget.invisibleRootItem():

                        found.add(p)

                        p = p.parent()

        for it in found:

            it.setHidden(False)

            if it.childCount() > 0:

                it.setExpanded(True)



    def display_context_menu(self, pos):

        w = self.sender()

        it = w.itemAt(pos)

        if not it:

            return



        if w == self.favorites_widget:

            # Favorites always use the 'favorites' menu

            menu_items = Config.Menu.CONTEXT['favorites']

        else:

            # For the tree_widget, decide if it's a folder or file

            is_folder = getattr(it, 'is_folder', False)

            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']



        menu = create_menu(menu_items, w)

        chosen = menu.exec(w.viewport().mapToGlobal(pos))

        if chosen:

            self.execute_menu_action(chosen.text(), it)



    def execute_menu_action(self, text, it):

        sp = getattr(it, 'script_path', None)

        from_fav = (self.sender() == self.favorites_widget)

        acts = {

            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,

            'Open Script':        lambda: self.open_script_file(sp) if sp else None,

            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,

            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,

            'Unpin from Favorites':lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,

            'Refresh':            lambda: self.refresh_item(it),

            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,

            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,

            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None

        }

        a = acts.get(text)

        if a:

            a()



    def pin_item(self, it):

        n, p = it.original_name, it.script_path

        if not p:

            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")

            return

        self.favorites[n] = p

        self.save_favorites()

        c = self.run_counts.get(p, 0)

        fav = FavoriteItem(n, p, c)

        self.favorites_widget.addItem(fav)

        it.is_pinned = True

        it.set_icon()



    def unpin_item(self, item, from_favorites=False):

        d = item.data(Qt.UserRole)

        n, p = d['name'], d['script_path']

        if n in self.favorites and self.favorites[n] == p:

            del self.favorites[n]

            self.save_favorites()

            r = self.favorites_widget.row(item)

            if r != -1:

                self.favorites_widget.takeItem(r)

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)



    def pin_selected(self):

        sel = self.tree_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No script to pin.", 5000)

            return

        for it in sel:

            if not it.is_folder:

                self.pin_item(it)



    def unpin_selected(self):

        sel = self.favorites_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No favorite to unpin.", 5000)

            return

        for it in sel:

            self.unpin_item(it, True)



    def run_script(self, it, col):

        p = it.script_path

        if p and not it.is_folder:

            try:

                rt.filein(p)

                mw = self.parent()

                if isinstance(mw, QtWidgets.QMainWindow):

                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)

                c = self.run_counts.get(p, 0) + 1

                self.run_counts[p] = c

                self.save_run_counts()

                it.update_run_count(c)

                if it.is_pinned:

                    fi = self.find_fav_by_path(p)

                    if fi:

                        fi.update_run_count(c)

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")



    def run_script_by_path(self, p):

        if p and os.path.exists(p):

            try:

                rt.filein(p)

                mw = self.parent()

                if isinstance(mw, QtWidgets.QMainWindow):

                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)

                c = self.run_counts.get(p, 0) + 1

                self.run_counts[p] = c

                self.save_run_counts()

                return c

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

        else:

            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")

        return None



    def run_script_from_favorites(self, it):

        d = it.data(Qt.UserRole)

        p = d['script_path']

        if p and os.path.exists(p):

            self.run_script_by_path(p)

            c = self.run_counts.get(p, 0)

            it.update_run_count(c)



    def run_selected_if_focused(self):

        if self.tree_widget.hasFocus() or self.favorites_widget.hasFocus():

            self.run_selected()



    def run_selected(self):

        sel = self.tree_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No scripts selected.", 5000)

            return

        for it in sel:

            if not it.is_folder and it.script_path:

                self.run_script(it, 0)



    def open_in_explorer(self, p):

        try:

            if os.name == 'nt':

                subprocess.Popen(['explorer', '/select,', p])

            elif os.name == 'posix':

                subprocess.Popen(['open', '-R', p])

            else:

                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")



    def open_script_file(self, p):

        try:

            if os.name == 'nt':

                os.startfile(p)

            elif os.name == 'posix':

                subprocess.Popen(['open', p])

            else:

                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")



    def dragEnterEvent(self, ev):

        if ev.mimeData().hasUrls():

            ev.acceptProposedAction()

        else:

            ev.ignore()



    def dropEvent(self, ev):

        if ev.mimeData().hasUrls():

            for url in ev.mimeData().urls():

                p = url.toLocalFile()

                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):

                    n = os.path.basename(p)

                    it = self.find_by_path(self.tree_widget, p)

                    if it and not it.is_pinned:

                        self.pin_item(it)

            ev.acceptProposedAction()

        else:

            ev.ignore()



    def find_by_path(self, parent, path):

        for i in range(parent.childCount()):

            c = parent.child(i)

            if c.script_path == path:

                return c

            if c.is_folder:

                r = self.find_by_path(c, path)

                if r:

                    return r

        return None



    def find_fav_by_path(self, path):

        for i in range(self.favorites_widget.count()):

            fav = self.favorites_widget.item(i)

            d = fav.data(Qt.UserRole)

            if d and d['script_path'] == path:

                return fav

        return None



    def parent_names(self, it):

        names = []

        p = it.parent()

        while p:

            names.insert(0, p.text(0))

            p = p.parent()

        return names



    def index_item(self, it):

        n = it.original_name.lower()

        if n not in self._search_index:

            self._search_index[n] = []

        self._search_index[n].append(it)



    def expand_all(self, it):

        it.setExpanded(True)

        for i in range(it.childCount()):

            self.expand_all(it.child(i))



    def collapse_all(self, it):

        it.setExpanded(False)

        for i in range(it.childCount()):

            self.collapse_all(it.child(i))



    def mouseDoubleClickEvent(self, ev):

        super().mouseDoubleClickEvent(ev)



def create_dock_widget(scripts_directory):

    mw = qtmax.GetQMaxMainWindow()



    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")

    if existing:

        mw.removeDockWidget(existing)

        existing.deleteLater()



    dw = Dock(parent=mw, scripts_dir=scripts_directory)

    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)

    dw.setFloating(False)

    dw.show()



def main():



    # Determine the scripts directory, prioritizing 'novMaxscriptsPath' if available.

    scripts_dir = ""

    try:

        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):

            scripts_dir = rt.novMaxscriptsPath

    except AttributeError: # Fallback to default usermacros directory

        scripts_dir = rt.getDir(rt.Name('userMacros'))



    # Initialize widget

    mw = qtmax.GetQMaxMainWindow()

    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")

    if existing:

        mw.removeDockWidget(existing)

        existing.deleteLater()

    create_dock_widget(scripts_dir)



if __name__ == "__main__":

    main()








## Message 5

**Explaining Python code functionality**

The user wants an explanation of a Python script. This code creates a GUI using PySide6, designed to integrate into Autodesk 3ds Max. It's essentially a dockable widget that displays a file tree and a list of favorite scripts. The widget scans a specific folder for script files (such as `.ms`, `.mse`, or `.json` extensions). It allows users to interact by double-clicking to run scripts, opening them in explorer, or pinning them to a favorites list for easy access later.

**Explaining Python script imports and setup**

The script for Autodesk 3ds Max 2025 creates a dockable GUI widget using PySide6 and pymxs for MaxScript. It imports several packages like `sys`, `os`, `json`, and `fnmatch`. The PySide6 modules help build the UI, while `pymxs.runtime` connects to 3ds Max. It also imports "qtmax," likely for getting the main window from Max. There's a `Config` class that holds UI constants, style settings, and paths, along with a `STYLESHEET_TEMPLATE` for customizing the widget’s appearance.

**Explaining the main widget setup**

The `Config` class holds constants for UI dimensions, colors, and paths. It defines styles for backgrounds, hover states, and tinting rules for matching patterns. The `Paths` nested class specifies supported script extensions and path retrieval methods. The `Menu` class manages context menu items for files, folders, and favorites. The main `Dock` widget class sets up 3ds Max’s interface, featuring a favorites list, script tree, and search bar. It also includes methods for UI initialization, directory scanning, and updating item data like run counts and tooltips.

## Message 6

This script is a complete Python application designed to integrate with Autodesk 3ds Max (specifically, version 2025). In essence, it builds a dockable graphical user interface (GUI) widget inside 3ds Max that lets you easily browse, run, and manage custom scripts. Here’s a breakdown of what it is and what it does:

---

## 1. **Overall Purpose**

- **Integration in 3ds Max:**  
  The script uses 3ds Max’s Python API (via the `pymxs` module) and the Qt framework (via PySide6) to add a new dockable panel to the 3ds Max interface. This panel is designed to help users manage their scripts more effectively.

- **Script Management:**  
  It scans a specified directory for script files (with extensions like `.ms`, `.mse`, or `.json`), organizes them into a tree view, and provides functionality to run these scripts directly within 3ds Max. It also supports marking scripts as favorites, tracking how many times each script is run, and even filtering/searching through the available scripts.

---

## 2. **Key Components and Their Roles**

### **A. Configuration (The `Config` Class)**

- **Styles and UI Constants:**  
  - **Stylesheet Template:** A multi-line string defines how widgets (like the script tree view) are styled—controlling background colors, text colors, padding, and font sizes.
  - **UI Constants:** Settings such as minimum dock size, icon sizes, column widths, and search debounce intervals are defined to standardize the widget’s look and behavior.
  
- **Color and Style Settings:**  
  The nested `Style` class holds specific color values for various UI elements (e.g., background colors for folders and favorites, hover effects, selection colors) and even rules for “tinting” items based on their names.

- **Paths and File Management:**  
  The `Paths` nested class specifies which file types are supported and where to save/load configuration files (like favorites and run counts). It uses 3ds Max’s userMacros directory (obtained via `rt.getDir(rt.Name("userMacros"))`) to store these files.

- **Context Menus:**  
  The `Menu` nested class defines the structure of context menus (right-click menus) for different items (files, folders, and favorites). Each menu item includes a label, an optional keyboard shortcut, and an icon.

### **B. GUI Components and Custom Items**

- **ScriptItem (Tree View Item):**  
  - Represents an individual script file or folder within the tree view.
  - Displays the script’s name, an icon (which changes if it’s a folder, a file, or “pinned”), and a run count (the number of times the script has been executed).
  - Automatically loads additional file details (like last modified time and file size) into a tooltip.
  - Applies a “tint” to its background based on naming rules so that similar categories of scripts can be visually grouped.

- **FavoriteItem (List Widget Item):**  
  - Represents a favorite script in a separate list.
  - Stores the same information as a ScriptItem but is presented in a flat list for quick access.
  - Updates its display to show how many times the script has been run.

### **C. The Main Dock Widget (`Dock` Class)**

- **UI Layout:**  
  - **Favorites Section:** A list widget at the top displays favorite scripts.
  - **Scripts Tree View:** Below the favorites is a tree view that displays the directory structure of your scripts. This view is searchable via a search bar.
  - **Splitter Layout:** A vertical splitter divides the favorites from the main script listing, allowing dynamic resizing.

- **Core Functionalities:**  
  - **Directory Scanning:** Uses the `scan_dir` function to recursively traverse a given directory and build a nested dictionary representing folders and script files.
  - **Context Menus:** Right-clicking on items (in either the tree view or favorites list) brings up context menus that allow you to run, open, or pin scripts, among other actions.
  - **Script Execution:** When a script is double-clicked (or run via a shortcut), the widget uses `rt.filein` (from `pymxs`) to execute the script within 3ds Max. It also updates the run count both in the UI and in a stored JSON file.
  - **Favorites Management:** You can pin scripts from the tree view to the favorites list (or unpin them) so that frequently used scripts are always at hand.
  - **Search and Filtering:** A debounced search bar filters the tree view items as you type, showing only matching scripts and their parent folders.
  - **File System Watching:** The widget monitors the scripts directory for changes. If files are added or removed, it automatically rebuilds the tree view to reflect the current state.
  - **Drag-and-Drop Support:** You can drag supported files into the widget to pin them as favorites, enhancing usability.

### **D. Integration Helpers**

- **qtmax Module:**  
  The script imports a module called `qtmax` to obtain the main 3ds Max window (`qtmax.GetQMaxMainWindow()`). This ensures that the dock widget is correctly parented within the 3ds Max application.

- **File and Process Utilities:**  
  The script also uses standard modules such as `os`, `subprocess`, and `json` to handle file operations (like opening files in the system explorer) and to read/write configuration data.

---

## 3. **How It All Comes Together**

- **Initialization:**  
  When the script is executed (via the `main()` function), it first determines the appropriate scripts directory (checking for a custom path via `rt.novMaxscriptsPath` and falling back to the userMacros directory if needed).

- **Creating the Dock Widget:**  
  The `create_dock_widget` function is called, which:
  - Retrieves the main 3ds Max window.
  - Removes any existing widget with the same identifier to avoid duplicates.
  - Instantiates the `Dock` widget with the determined scripts directory.
  - Adds the widget to the left side of the main window.

- **Runtime Behavior:**  
  Once active, the widget continuously monitors the scripts directory. Users can:
  - Browse scripts in a hierarchically organized tree.
  - Filter scripts via the search bar.
  - Right-click for additional actions (such as running a script, opening its location in Explorer, or pinning it to favorites).
  - Drag and drop files to quickly add them as favorites.
  - Double-click to execute a script within 3ds Max.

---

## 4. **In Summary**

This Python script is essentially a script management tool for 3ds Max. It leverages PySide6 to create a polished, interactive GUI dock widget that:
- **Organizes** your custom scripts into an easily navigable tree.
- **Tracks** how often each script is run.
- **Facilitates** running, editing, or exploring scripts via context menus and shortcuts.
- **Enhances** workflow by allowing you to pin your favorite scripts for rapid access.

By integrating with 3ds Max’s runtime (using `pymxs`) and the Qt framework, it creates a seamless experience for users who rely on custom scripts to extend the capabilities of 3ds Max.

If you have any further questions about specific parts of the code or how to modify it, feel free to ask!

## Message 7

in the favorites widget i can remove items by clicking the delete key on the keyboard, but if i try to do it through the contextmenu (clicking "Unpin from Favorites" nothing happens. are you able to pinpoint the exact reason for this?

## Message 8

**Investigating "Unpin from Favorites" issue**

Okay, it sounds like the "Unpin from Favorites" option isn't working when clicked from the context menu, but the delete key works. I'll need to troubleshoot the code provided, especially the function `display_context_menu(self, pos)` in the `Dock` class. That seems to be key to handling the context menu actions. Let me check how that part of the code is interacting with the event and what might be missing or causing the issue when the context menu is triggered.

**Analyzing "Unpin from Favorites" Issue**

The context menu calls `execute_menu_action` for the "Unpin from Favorites" option, which executes the `unpin_item` function if the item is pinned. But there's a key issue—the `FavoriteItem` doesn't seem to have an `is_pinned` attribute defined when it's created. Since the "Unpin from Favorites" action depends on this attribute being `True`, that's likely why it's not working. I need to make sure the `is_pinned` attribute is properly set when favorites items are created.

## Message 9

Yes. The issue is that the context menu’s "Unpin from Favorites" action only triggers if the item has an attribute `is_pinned` set to a truthy value. In the `execute_menu_action` method, the lambda for "Unpin from Favorites" is defined as follows:

```python
'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
```

This means that for the unpin action to run, the item must have an attribute `is_pinned` that evaluates to `True`.

However, items in the favorites widget are created using the `FavoriteItem` class, and this class does not set an `is_pinned` attribute anywhere. As a result, when you right-click on a favorite and choose "Unpin from Favorites", the check `getattr(it, 'is_pinned', False)` returns `False` (using the default value), and the lambda does nothing. 

In contrast, when you press the Delete key, a different code path (the `unpin_selected` method) is executed that correctly handles the removal of favorite items without relying on the `is_pinned` attribute.

To fix this issue, you could modify the lambda condition in `execute_menu_action` for favorites to not rely on the `is_pinned` attribute, or ensure that `FavoriteItem` instances have an `is_pinned` attribute set to `True` when they are created.

## Message 10

please show the solution

## Message 11

**Providing a solution**

The user wants to see the corrected code. I believe a fix could be to modify the lambda in the `execute_menu_action()` function to check if the item is of type `FavoriteItem`, or alternatively, add an attribute to `FavoriteItem` indicating if it’s pinned.

The solution might look something like this:

```python
# If checking the type
lambda item: isinstance(item, FavoriteItem) and getattr(item, 'is_pinned', False)

# Or add 'is_pinned' attribute to FavoriteItem and modify accordingly
``` 

I'll offer this fixed version to solve the issue.

**Proposing a fix**

Okay, so the ScriptItem has the `is_pinned` attribute, but the FavoriteItem class doesn’t. One option is to modify the `FavoriteItem` class to include `self.is_pinned = True` in its `__init__` method.

Alternatively, we could adjust the lambda in `execute_menu_action()` to check if the item is an instance of `FavoriteItem`. 

This way, we ensure that the "Unpin from Favorites" action works by properly managing the `is_pinned` attribute. Let me update that!

## Message 12

Below is one straightforward solution: modify the `FavoriteItem` class so that each favorite item has an `is_pinned` attribute set to `True`. This way, when the context menu’s lambda checks for `getattr(it, 'is_pinned', False)`, it will return `True` for a favorite item and execute the unpin action.

### **Modified `FavoriteItem` Class**

Replace the existing `FavoriteItem` class definition with the following code:

```python
class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        self.is_pinned = True  # Added attribute to indicate the item is pinned.
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")
```

### **How This Fixes the Issue**

- **Before the Fix:**  
  The context menu action for "Unpin from Favorites" uses this lambda:
  
  ```python
  'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
  ```
  
  Since the original `FavoriteItem` class did not define an `is_pinned` attribute, `getattr(it, 'is_pinned', False)` would return `False`, and nothing would happen when you clicked "Unpin from Favorites".

- **After the Fix:**  
  With the addition of `self.is_pinned = True` in the `FavoriteItem` constructor, the lambda condition now evaluates as `True` for items in the favorites widget. As a result, clicking "Unpin from Favorites" correctly calls the `unpin_item` method and removes the item.

This change aligns the behavior of the context menu with the keyboard (Delete key) action, ensuring both methods work as expected.

## Message 13

can you identify any other similar issues?

## Message 14

**Looking at similar issues**

The user asked if there are other similar issues in the code. They mentioned a fix for the "unpin favorites via context menu" issue earlier. Now, I'm looking at the code, particularly two classes: `ScriptItem` (from `QTreeWidgetItem`) and `FavoriteItem` (from `QListWidgetItem`). In the `ScriptItem` constructor, the handling of properties like `pinned` and `count` seems fine. So, I'll keep checking the rest of the code to spot any inconsistencies or other similar issues related to the handling of favorites.

**Checking unpinning logic**

Alright, the `execute_menu_action` lambda for "Pin to Favorites" looks fine, as it correctly checks pinned attributes for ScriptItem objects. For "Unpin from Favorites," the method depends on the `is_pinned` attribute, but it should only apply to FavoriteItems or ScriptItems with `is_pinned`. That seems fine for now, but I wonder if the context menu using `is_pinned` works safely for tree widget items too. Now, I need to review the `unpin_item` method and ensure that `item.data(Qt.UserRole)` provides the necessary script path, especially when the item comes from the tree widget.

**Checking unpin and pin logic**

In `ScriptItem`, we're setting data in `Qt.UserRole` like path and folder, but not a dictionary. So, when calling `unpin_item` on a tree widget item, it returns a path, which wouldn't work since `unpin_item` expects a dictionary. This is inconsistent. However, I found that the menu for the tree widget doesn't have an "unpin" item, so no issues arise there. Only in the favorites context menu does this matter, and we've already fixed it by ensuring `FavoriteItem` handles its data correctly. Now, I'll check for run count updates and shortcut behaviors.

**Reviewing potential issues**

The `unpin_selected` method looks good, and the filesystem watcher setup seems fine too. There are minor inconsistencies like icon handling in `set_icon` (which isn't used in `FavoriteItem`), and a slight issue with handling duplicates in the favorites list when pinning. The "Pin to Favorites" context menu works well, but there could be a UX issue: users might expect to unpin directly from the tree view context menu when the item is pinned. Additionally, the search filtering might miss items not in `_search_index`, but that's unlikely. Otherwise, things look solid.

## Message 15

Below is a list of related issues you might run into because of inconsistencies in how the two item types store and expose their data:

---

### 1. **Inconsistent Data Stored in Qt.UserRole**

- **What’s Happening:**  
  The **ScriptItem** stores a simple value (the script’s path) in `Qt.UserRole` (and an extra flag in `Qt.UserRole+1`), while the **FavoriteItem** stores a dictionary (with keys like `'script_path'`, `'run_count'`, and `'name'`) in its `Qt.UserRole`.

- **Potential Issue:**  
  When context menu actions (such as “Open Script” or “Show in Explorer”) retrieve data via `item.data(Qt.UserRole)`, they assume a consistent type. For a FavoriteItem this works fine because it gets a dictionary. However, if you ever (for any reason) pass a ScriptItem into a routine that expects a dictionary (for example, if you decide to add an “Unpin” option to the tree’s context menu later), this inconsistency can lead to errors.

- **How to Fix It:**  
  Decide on one consistent format. For example, you could modify ScriptItem’s constructor to store a dictionary as well:

  ```python
  self.setData(0, Qt.UserRole, {'script_path': path, 'is_folder': folder, 'name': name})
  ```
  
  And then update all the methods that access this data accordingly.

---

### 2. **Missing Attributes in FavoriteItem**

- **What’s Happening:**  
  We already addressed that **FavoriteItem** didn’t have an `is_pinned` attribute, which caused the “Unpin from Favorites” context menu action to do nothing. In addition, some routines check for the `is_folder` attribute using `getattr(item, 'is_folder', False)`.

- **Potential Issue:**  
  While it’s true that favorites are always files (so `is_folder` would default to `False`), it can be confusing or lead to future bugs if you later extend functionality that needs to know the item type. It’s often better to explicitly set these attributes.

- **How to Fix It:**  
  In addition to setting `self.is_pinned = True` (as shown in the previous fix), you might also set:
  
  ```python
  self.is_folder = False  # In FavoriteItem __init__
  ```
  
  This way, any checks on these attributes (whether in context menu lambdas or elsewhere) will reliably return the correct values.

---

### 3. **Context Menu Options Across Widgets**

- **What’s Happening:**  
  The tree view’s context menu (for ScriptItem) shows a “Pin to Favorites” option only, whereas the favorites widget shows an “Unpin from Favorites” option.  
   
- **Potential Issue:**  
  This difference in available options might be confusing if a user expects a similar set of actions regardless of where the script is listed. For instance, if a user sees a pinned script in the tree view, they might assume they can unpin it there as well—but the code currently only allows unpinning from the favorites widget.

- **How to Fix It:**  
  If you’d like consistent behavior, consider adding an “Unpin from Favorites” option to the tree widget’s context menu when an item is already pinned. This would require a check similar to:
  
  ```python
  if getattr(it, 'is_pinned', False):
      # Offer an option to unpin as well as the regular actions.
  ```
  
  And then have that option trigger the same unpinning method.

---

### 4. **Duplicate Favorites**

- **What’s Happening:**  
  The current code in `pin_item` does not check if an item is already pinned (beyond checking `is_pinned` on the ScriptItem).

- **Potential Issue:**  
  This might lead to duplicate entries in the favorites list if the same script is pinned multiple times from different parts of the UI (or if some inconsistency occurs between the two lists).

- **How to Fix It:**  
  Enhance the `pin_item` method so that before adding a new favorite, it checks if the script’s unique identifier (for example, its path or name) already exists in the favorites dictionary. If it does, you might simply bring that item into focus or update its run count rather than adding a duplicate.

---

### In Summary

The main issues revolve around inconsistent data and attribute expectations between the two item classes and the actions that operate on them. Addressing these by:

1. **Standardizing the Data Format:** Make sure both ScriptItem and FavoriteItem store similar information (preferably as dictionaries) in `Qt.UserRole`.
2. **Explicitly Setting Expected Attributes:** For example, add both `is_pinned` and `is_folder` attributes to FavoriteItem.
3. **Harmonizing Context Menus:** Provide consistent options whether a script is accessed from the tree view or the favorites widget.
4. **Preventing Duplicates:** Check for and prevent duplicate entries when pinning items.

By making these changes, you reduce the chance of unexpected behavior when invoking actions through context menus or keyboard shortcuts.

## Message 16

please show the full updated code with a proper implementation of your proposed solution for this scenario and context, do this while retaining the existing functionality and creating an in-place replacement

## Message 17

**Updating code implementation**

The user asks for an updated code that retains functionality while applying specific changes. We need to add the `is_pinned` attribute to `FavoriteItem` (already done), and update the use of `Qt.UserRole` for consistency. There’s an issue of inconsistencies with `ScriptItem` and `FavoriteItem`, like missing attributes and context menu options.

I’ll need to update both classes. Specifically: 
1. In `FavoriteItem`, I'll add `self.is_pinned = True` and `self.is_folder = False`. I'll look for any additional role inconsistencies too.

**Considering consistent data structure**

Alright, for ScriptItem, it seems like using a consistent dictionary in `Qt.UserRole` is a good idea, but we must retain existing functionality, so I need to be careful here. 

Currently, ScriptItem and FavoriteItem have inconsistent implementations. In ScriptItem, I’m storing data using `setData` with different roles, while in FavoriteItem I’m using a dictionary. To make them consistent, I could use a dictionary in both, but that might break existing code. A safer option could be updating only FavoriteItem by adding missing attributes like `is_pinned` and `is_folder`.

For the context menu, I’ll need to decide if I should update `execute_menu_action` to handle these changes for the Favorites widget. Plus, modifying FavoriteItem seems like the better approach to avoid affecting ScriptItem too much while still fostering consistency.

**Updating handling for pinning and unpinning**

I need to modify several parts of the code to handle pinning and unpinning consistently. First, I’ll ensure that both `ScriptItem` and `FavoriteItem` check for files already being pinned by updating `pin_item` to prevent duplicates using the `self.favorites` dictionary. Additionally, I’ll incorporate `is_pinned` and `is_folder` attributes to `FavoriteItem`.

For `execute_menu_action`, I’ll adjust it so it works seamlessly with both `FavoriteItem` and `ScriptItem` by checking the `is_pinned` attribute. Then, I'll update `pin_item`, `unpin_item`, and drag-and-drop actions accordingly. The goal is to implement these changes without breaking any existing functionality.

## Message 18

Below is the full updated code with the proposed changes integrated in place. In this version:

1. **FavoriteItem** now explicitly sets both the `is_pinned` and `is_folder` attributes so that context‐menu actions work consistently.  
2. **pin_item** has been updated to check for duplicate entries (by comparing the script path) so that a script isn’t pinned more than once.  
3. In addition, comments have been added to mark the changes while keeping the rest of the functionality unchanged.

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+C', 'icon': 'SP_FileDialogListView'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

# --- Updated FavoriteItem with explicit attributes for consistency ---
class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        # Explicitly mark this as a pinned, non-folder item.
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                it = ScriptItem(name, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents+[name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf+[it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return

        if w == self.favorites_widget:
            # Favorites always use the 'favorites' menu
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            # For the tree_widget, decide if it's a folder or file
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']

        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites':lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    # --- Updated pin_item to prevent duplicates ---
    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        # Check if this script path is already pinned in favorites.
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        # For FavoriteItem, data is stored as a dictionary.
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus() or self.favorites_widget.hasFocus():
            self.run_selected()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()

    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()

    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    # Determine the scripts directory, prioritizing 'novMaxscriptsPath' if available.
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError: # Fallback to default usermacros directory
        scripts_dir = rt.getDir(rt.Name('userMacros'))

    # Initialize widget
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation

- **FavoriteItem:**  
  The constructor now sets `self.is_pinned = True` and `self.is_folder = False` so that context-menu actions (like “Unpin from Favorites”) detect the item as a favorite.

- **pin_item:**  
  Before adding a new favorite, the method checks if any favorite in the stored dictionary already has the same script path. If so, it shows a status message and does not add a duplicate.

- **Other Areas:**  
  The rest of the code remains largely unchanged to retain existing functionality while now using more consistent data attributes.

This updated code should now allow removal from favorites via the context menu as well as by pressing the Delete key, and it prevents duplicate entries while ensuring consistency between different parts of the interface.

## Message 19

when i right-click a folder and select "Refresh Folder" i get this error:
---------------------------

Refresh Failed

---------------------------

Invalid folder path 'None'

---------------------------

OK   

---------------------------


## Message 20

**Debugging folder refresh issue**

The error happens because the folder path (`fpath`) is `None`, as indicated by the message "Invalid folder path 'None'." To address this, we can look at the `refresh_item` function.

In the code, it's checking if the path exists and is valid using `os.path.isdir(fpath)` before proceeding. If the `fpath` is `None`, the error message will trigger. So, the issue lies in how the path is being retrieved or initialized for the folder. Let’s make sure `it.script_path` is providing a valid value for directories.

**Addressing folder path issue**

The `ScriptItem` for a folder is created without a path, so its `script_path` is initialized as `None`. In the `refresh_item` function, it expects a valid directory path, which is why the refresh fails.

To fix this, we could assign the folder's actual path when constructing the folder item. However, since `populate_tree` doesn’t pass a path for folders, we might need to adjust the function to also store and pass the folder's full path when creating folder items. That could allow the refresh functionality to work properly.

**Improving folder path handling**

The `scan_dir` function currently doesn’t store folder paths, only folder names and substructures. This causes issues in `refresh_item` where it expects to get a folder's actual path.

Two solutions come to mind:
1. Modify `scan_dir` to store the folder path along with its substructure, but this might complicate the current code.
2. A simpler approach: modify `populate_tree` to compute the folder path by passing a `base_path`. This way, we can easily compute full folder paths using `os.path.join`.

The second option retains the existing structure without breaking anything.

## Message 21

Below is the full updated code with an in‐place fix for the “Refresh Folder” issue. In this update, the folder items now have their actual folder path stored (in the `script_path` attribute) by computing it based on the scripts directory and the parent folder names. This change ensures that when you refresh a folder, the folder’s path is valid rather than `None`.

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+C', 'icon': 'SP_FileDialogListView'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

# --- Updated FavoriteItem with explicit attributes for consistency ---
class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        # Explicitly mark this as a pinned, non-folder item.
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    # --- Updated populate_tree: folders now receive their full folder path ---
    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                # Compute full folder path using the scripts_directory and current parent names.
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return

        if w == self.favorites_widget:
            # Favorites always use the 'favorites' menu
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            # For the tree_widget, decide if it's a folder or file
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']

        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    # --- Updated pin_item to prevent duplicates ---
    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        # Check if this script path is already pinned in favorites.
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        # For FavoriteItem, data is stored as a dictionary.
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus() or self.favorites_widget.hasFocus():
            self.run_selected()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()

    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()

    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    # Determine the scripts directory, prioritizing 'novMaxscriptsPath' if available.
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:  # Fallback to default usermacros directory
        scripts_dir = rt.getDir(rt.Name('userMacros'))

    # Initialize widget
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation of the Fix

- **Problem:**  
  When a folder item was created in the original code, its `script_path` attribute was left as `None` (because no path was provided). Consequently, when you refreshed a folder (by right-clicking and choosing “Refresh Folder”), the code checked for a valid folder path and failed.

- **Solution:**  
  In the updated `populate_tree` method, when iterating over folder entries (i.e. when `val` is a dictionary), we now compute the full folder path using:  
  ```python
  folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
  ```  
  This computed path is then passed into the `ScriptItem` constructor so that folder items now have a valid `script_path`.

With this update, refreshing a folder will correctly use its full path instead of `None` and no longer throw the "Invalid folder path" error.

If you have further questions or need additional adjustments, let me know!

## Message 22

please add a contextmenu for folders also that is called "Show in Explorer"

## Message 23

**Adding option to context menu**

I’m checking the current context menu for folders, which is defined under `Config.Menu.CONTEXT['folder']`. It currently includes "Expand All," "Collapse All," and "Refresh Folder." Now, we need to add the "Show in Explorer" option for folders.

It seems like the simplest solution might be to add this new option, perhaps before the separator or after "Collapse All." I’ll just add it where it makes the most sense in the list.

**Modifying folder context menu**

I’m thinking about how to add the "Show in Explorer" option for the folder context menu. The "Show in Explorer" option already exists in the file context menu, so we should ensure the folder context menu gets the same option.

Looking at the existing structure, I could add it after the separator or perhaps before "Refresh Folder." Adding it as the last item might be the simplest. It seems like I could call `self.open_in_explorer(it.script_path)` to implement it. I'll go ahead and modify `Config.Menu.CONTEXT['folder']`.

**Finalizing folder context menu update**

I’m considering the folder context menu and how to add the "Show in Explorer" option. For consistency with the file context menu, I'll be adding the option with the shortcut “Ctrl+Shift+E” and the icon “SP_DirIcon.”

To prevent a conflict with "Expand All" (already using “Ctrl+E”), “Ctrl+Shift+E” seems like a good choice. The execute_menu_action mapping already includes the action for "Show in Explorer," and it will work for both folders and files as expected.

I’ll update `Config.Menu.CONTEXT['folder']` with these changes in the code.

## Message 24

Below is the full updated code with an added "Show in Explorer" option in the folder context menu. In the updated code, the folder menu now includes an extra entry (with a non-conflicting shortcut) so that right‐clicking a folder brings up an option to open its location in Explorer.

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # Note the update in the 'folder' menu: a new entry for "Show in Explorer" has been added.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+C', 'icon': 'SP_FileDialogListView'},
                # Added "Show in Explorer" for folders.
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

# --- Updated FavoriteItem with explicit attributes for consistency ---
class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        # Explicitly mark this as a pinned, non-folder item.
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    # --- Updated populate_tree: folders now receive their full folder path ---
    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                # Compute full folder path using the scripts_directory and current parent names.
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return

        if w == self.favorites_widget:
            # Favorites always use the 'favorites' menu
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            # For the tree_widget, decide if it's a folder or file
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']

        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    # --- Updated pin_item to prevent duplicates ---
    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        # Check if this script path is already pinned in favorites.
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        # For FavoriteItem, data is stored as a dictionary.
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus() or self.favorites_widget.hasFocus():
            self.run_selected()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()

    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()

    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    # Determine the scripts directory, prioritizing 'novMaxscriptsPath' if available.
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:  # Fallback to default usermacros directory
        scripts_dir = rt.getDir(rt.Name('userMacros'))

    # Initialize widget
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation

- **Menu Update:**  
  In the `Config.Menu.CONTEXT['folder']` list, a new entry for "Show in Explorer" has been added with the shortcut `"Ctrl+Shift+E"` (to avoid conflicting with the existing `"Ctrl+E"` for "Expand All") and the icon `SP_DirIcon`.

- **Unified Action Mapping:**  
  The `execute_menu_action` method already contains a mapping for `"Show in Explorer"`, so when this option is selected (whether from a file or folder context menu), it calls the same `open_in_explorer` function.

- **Folder Items' Paths:**  
  As before, folder items now have their full folder path computed and stored in `script_path` (via the updated `populate_tree` method). This ensures that the "Show in Explorer" action correctly opens the folder location.

This complete, in-place replacement retains all the existing functionality while adding the requested context menu for folders.

## Message 25

for some reason the keybinding in the "Scripts" widget doesn't work, if i e.g. select a folder and press ctrl+shift+e nothing happens

## Message 26

**Investigating shortcut issue**

The user is saying the "Ctrl+Shift+E" shortcut isn't working, even though it's assigned to "Show in Explorer." I think the issue could be that there’s no explicit shortcut for it in the `init_shortcuts` function. The shortcuts for other actions like "Ctrl+P" and "Delete" are defined, but not for "Ctrl+Shift+E." The problem could also be happening because the widget isn't focused or the QShortcut isn't attached properly to the action. I'll check the code to confirm!

**Understanding shortcut behavior**

The issue arises because the "Ctrl+Shift+E" shortcut is defined for the context menu, but it's context-based, meaning it only works within the menu. It's not a global shortcut. Also, pressing "Ctrl+Shift+E" while a folder is selected won't automatically show the context menu. The shortcut needs to be globally mapped. One possible solution is to add a QShortcut for "Ctrl+Shift+E" in the `init_shortcuts` method, ensuring it works when the tree widget is focused and the selected item is a folder.

**Implementing shortcut for folder**

To solve the issue, we can map "Ctrl+Shift+E" to a global QShortcut in the `init_shortcuts` method inside the `Dock` class, specifically for the tree widget. The shortcut will trigger a function that checks if the tree widget has focus and if the selected item is a folder, then open it in Explorer. The code could look like this:

```python
explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
explorer_sc.activated.connect(self.handle_explorer_shortcut)

def handle_explorer_shortcut(self):
    if self.tree_widget.hasFocus():
        sel = self.tree_widget.selectedItems()
        for item in sel:
            if item.is_folder and item.script_path:
                self.open_in_explorer(item.script_path)
```

This should work to activate the shortcut only when the tree widget is focused.

## Message 27

Below is the full updated code with an added global shortcut (attached to the tree widget) that listens for **Ctrl+Shift+E**. When pressed while a folder is selected in the "Scripts" (tree) widget, it will trigger the "Show in Explorer" action. Notice that while the menu entry in the context menu already has a key sequence defined, that key sequence is only active when the menu is open. To make the shortcut work without opening the menu, we add an explicit QShortcut.

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # Updated menus include the "Show in Explorer" entry for folders.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+C', 'icon': 'SP_FileDialogListView'},
                # Added "Show in Explorer" for folders.
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

# --- Updated FavoriteItem with explicit attributes for consistency ---
class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        # Explicitly mark this as a pinned, non-folder item.
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)
        # Add a global shortcut for "Show in Explorer" for the tree widget
        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
        explorer_sc.activated.connect(self.handle_explorer_shortcut)

    def handle_explorer_shortcut(self):
        # Check if the tree widget has focus and a folder item is selected.
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.is_folder and item.script_path:
                    self.open_in_explorer(item.script_path)
                    break

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    # --- Updated populate_tree: folders now receive their full folder path ---
    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                # Compute full folder path using the scripts_directory and current parent names.
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return

        if w == self.favorites_widget:
            # Favorites always use the 'favorites' menu
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            # For the tree_widget, decide if it's a folder or file
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']

        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    # --- Updated pin_item to prevent duplicates ---
    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        # Check if this script path is already pinned in favorites.
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        # For FavoriteItem, data is stored as a dictionary.
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus() or self.favorites_widget.hasFocus():
            self.run_selected()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()

    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()

    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    # Determine the scripts directory, prioritizing 'novMaxscriptsPath' if available.
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:  # Fallback to default usermacros directory
        scripts_dir = rt.getDir(rt.Name('userMacros'))

    # Initialize widget
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation

1. **Global Shortcut Addition:**  
   In the `init_shortcuts` method, a new QShortcut is added for **Ctrl+Shift+E** (attached to `self.tree_widget`).  
   ```python
   explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
   explorer_sc.activated.connect(self.handle_explorer_shortcut)
   ```
2. **Shortcut Handler:**  
   The method `handle_explorer_shortcut` checks if the tree widget has focus and then retrieves the selected items. If a folder is selected (with a valid `script_path`), it calls the `open_in_explorer` function to open the folder in the system file explorer.
3. **Existing Functionality Retained:**  
   All other functionality (context menus, pinning, filtering, etc.) remains the same.

With this update, pressing **Ctrl+Shift+E** in the "Scripts" widget when a folder is selected will now trigger the "Show in Explorer" action.

## Message 28

please see the updated keybindings and go through and ensure all of them works correctly:

import sys

import os

import subprocess

import json

import fnmatch

from PySide6 import QtCore, QtGui, QtWidgets

from PySide6.QtCore import Qt, QDateTime, QSize

from PySide6.QtGui import QAction, QKeySequence

from pymxs import runtime as rt

import qtmax



PRINT_DEBUG_INFO = False



class Config:

    STYLESHEET_TEMPLATE = """

        QTreeWidget {{

            background-color: {tree_bg};

            color: {tree_fg};

            border: none;

            padding: {pad}px;

        }}

        QTreeWidget::item {{

            padding: {pad}px;

            font-size: {item_font_size}px;

        }}

        QTreeWidget::item:hover {{

            background-color: {hover_bg};

        }}

        QTreeWidget::item:selected {{

            background-color: {sel_bg};

        }}

        QTreeWidget::item:pressed {{

            background-color: {press_bg};

        }}

        QHeaderView::section {{

            background-color: {hdr_bg};

            color: {hdr_fg};

            padding: {pad}px;

            font-size: {hdr_font_size}px;

            border: 1px solid {hdr_border};

        }}

    """



    class UI:

        MIN_DOCK_WIDTH = 300

        MIN_DOCK_HEIGHT = 400

        SPLITTER_RATIO = [1, 2]

        ICON_SIZE = QSize(20, 20)

        TREE_INDENT = 20

        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}

        SEARCH_DEBOUNCE_MS = 150

        SEARCH_MIN_CHARS = 2

        AUTO_EXPAND_RESULTS = True

        PRESERVE_EXPANSION = False

        FOLDERS_EXPANDABLE = True

        DOUBLE_CLICK_ACTION = "run"

        BUTTON_PADDING_RATIO = 0.002

        HEADER_FONT_RATIO = 0.007

        ITEM_FONT_RATIO = 0.005



    class Style:

        FAVORITES_BG = "#1a1a1a"

        TREE_BG = "#2b2b2b"

        FAVORITES_FG = "#f0f0f0"

        TREE_FG = "#f0f0f0"

        PIN_COLOR = "#79ff1c"

        HDR_BG = "#2b2b2b"

        HDR_FG = "#f0f0f0"

        HDR_BORDER = "#3c3c3c"

        HOVER_BG = "#3c3c3c"

        SEL_BG = "#1e90ff"

        PRESS_BG = "#104e8b"

        TINT_RULES = [

            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},

            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},

            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},

            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},

        ]



    class Paths:

        SUPPORTED = ('.ms', '.mse', '.json')

        MAX_DEPTH = 8



        @classmethod

        def favorites_file(cls):

            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")



        @classmethod

        def run_counts_file(cls):

            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")



    class Menu:

        # Updated menus include the "Show in Explorer" entry for folders.

        CONTEXT = {

            'file': [

                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},

                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},

                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},

                {'text': '-'},

                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},

                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}

            ],

            'folder': [

                {'text': 'Expand All', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_FileDialogDetailedView'},

                {'text': 'Collapse All', 'shortcut': 'Ctrl+Shift+C', 'icon': 'SP_FileDialogListView'},

                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_DirIcon'},

                {'text': '-'},

                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}

            ],

            'favorites': [

                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},

                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},

                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},

                {'text': '-'},

                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}

            ]

        }



    def stylesheet(self):

        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()

        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)

        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))

        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))

        return self.STYLESHEET_TEMPLATE.format(

            tree_bg=Config.Style.TREE_BG,

            tree_fg=Config.Style.TREE_FG,

            hdr_bg=Config.Style.HDR_BG,

            hdr_fg=Config.Style.HDR_FG,

            hdr_border=Config.Style.HDR_BORDER,

            hover_bg=Config.Style.HOVER_BG,

            sel_bg=Config.Style.SEL_BG,

            press_bg=Config.Style.PRESS_BG,

            pad=pad,

            hdr_font_size=hdr_fs,

            item_font_size=item_fs

        )



def create_menu(menu_items, parent):

    m = QtWidgets.QMenu(parent)

    for it in menu_items:

        if it['text'] == '-':

            m.addSeparator()

            continue

        a = QAction(it['text'], parent)

        if 'shortcut' in it:

            a.setShortcut(QKeySequence(it['shortcut']))

        if 'icon' in it:

            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))

            a.setIcon(icon)

        m.addAction(a)

    return m



def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):

    if depth > max_depth:

        return {}

    structure = {}

    try:

        with os.scandir(path) as it:

            dirs, files = [], []

            for e in it:

                if e.is_dir():

                    dirs.append(e)

                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):

                    files.append(e)

            dirs.sort(key=lambda x: x.name.lower())

            files.sort(key=lambda x: x.name.lower())

            for d in dirs:

                sub = scan_dir(d.path, max_depth, depth+1)

                if sub:

                    structure[d.name] = sub

            for f in files:

                structure[f.name] = f.path

    except PermissionError:

        pass

    return structure



def blend(base_hex, overlay_hex, alpha=0.3):

    base = QtGui.QColor(base_hex)

    over = QtGui.QColor(overlay_hex)

    r = int(base.red()*(1-alpha) + over.red()*alpha)

    g = int(base.green()*(1-alpha) + over.green()*alpha)

    b = int(base.blue()*(1-alpha) + over.blue()*alpha)

    c = QtGui.QColor(r, g, b).name()

    if PRINT_DEBUG_INFO:

        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")

    return c



class ScriptItem(QtWidgets.QTreeWidgetItem):

    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):

        super().__init__([name, "" if folder else str(count)])

        self.original_name = name

        self.script_path = path

        self.is_folder = folder

        self.is_pinned = pinned

        self.run_count = count

        self.setData(0, Qt.UserRole, path)

        self.setData(0, Qt.UserRole+1, folder)

        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)

        self.set_icon()

        self.defer_tooltip()

        if pinned:

            self.setData(0, Qt.UserRole+2, True)

            self.setData(1, Qt.UserRole+2, True)

            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)

            self.setForeground(0, QtGui.QBrush(pcolor))

            self.setForeground(1, QtGui.QBrush(pcolor))

        if not folder:

            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)

        self.tint_background(parents)



    def set_icon(self):

        style = QtWidgets.QApplication.style()

        if self.is_pinned:

            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)

        elif self.is_folder:

            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)

        else:

            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)

        self.setIcon(0, ic)



    def defer_tooltip(self):

        if not self.is_folder and self.script_path:

            self.setToolTip(0, "Loading...")

            QtCore.QTimer.singleShot(100, self.load_tooltip)



    def load_tooltip(self):

        if self.script_path and os.path.exists(self.script_path):

            try:

                st = os.stat(self.script_path)

                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)

                sz = st.st_size

                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")

            except Exception as e:

                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")



    def update_run_count(self, n):

        self.run_count = n

        if not self.is_folder:

            self.setText(1, str(n))



    def tint_background(self, parents):

        tinted = False

        if parents:

            for rule in Config.Style.TINT_RULES:

                pat, c, a = rule['pattern'], rule['color'], rule['alpha']

                for f in parents:

                    if fnmatch.fnmatch(f.lower(), pat):

                        blended = blend(Config.Style.TREE_BG, c, a)

                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))

                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))

                        tinted = True

                        break

                if tinted:

                    break

        if not tinted:

            bg = QtGui.QColor(Config.Style.TREE_BG)

            self.setBackground(0, QtGui.QBrush(bg))

            self.setBackground(1, QtGui.QBrush(bg))



# --- Updated FavoriteItem with explicit attributes for consistency ---

class FavoriteItem(QtWidgets.QListWidgetItem):

    def __init__(self, name, path, count=0):

        super().__init__(name)

        self.script_path = path

        self.run_count = count

        # Explicitly mark this as a pinned, non-folder item.

        self.is_pinned = True

        self.is_folder = False

        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})

        self.setToolTip(f"Path: {path}\nRun Count: {count}")



    def update_run_count(self, n):

        self.run_count = n

        d = self.data(Qt.UserRole)

        d['run_count'] = n

        self.setData(Qt.UserRole, d)

        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")



class Dock(QtWidgets.QDockWidget):

    def __init__(self, parent=None, scripts_dir=None):

        super().__init__(parent)

        self.setWindowTitle("Jorn Tools: Widget")

        self.setAllowedAreas(Qt.AllDockWidgetAreas)

        self.setObjectName("jornWidget_treeView")

        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)



        self.scripts_directory = scripts_dir

        self.favorites = {}

        self.run_counts = {}

        self._search_index = {}

        self._last_search = ""



        self.init_ui()

        self.load_run_counts()

        self.load_favorites()

        if self.scripts_directory:

            self.build_tree()

        self.init_file_watcher()

        self.init_shortcuts()



    def init_ui(self):

        c = QtWidgets.QWidget()

        self.setWidget(c)

        lay = QtWidgets.QVBoxLayout(c)

        split = QtWidgets.QSplitter(Qt.Vertical)

        lay.addWidget(split)



        fav_cont = QtWidgets.QWidget()

        fav_lay = QtWidgets.QVBoxLayout(fav_cont)

        fav_lay.setContentsMargins(0, 0, 0, 0)

        fav_label = QtWidgets.QLabel("Favorites")

        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")

        fav_lay.addWidget(fav_label)



        self.favorites_widget = QtWidgets.QListWidget()

        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)

        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)

        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)

        self.favorites_widget.setDropIndicatorShown(True)

        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)

        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)

        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)

        fav_lay.addWidget(self.favorites_widget)

        split.addWidget(fav_cont)



        h = QtWidgets.QApplication.primaryScreen().geometry().height()

        p = int(h * Config.UI.BUTTON_PADDING_RATIO)

        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))

        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))

        style = Config().stylesheet()

        self.favorites_widget.setStyleSheet(style)

        self.favorites_widget.setIconSize(QSize(20, 20))

        ff = self.favorites_widget.font()

        ff.setPointSize(it_fs)

        self.favorites_widget.setFont(ff)



        main_cont = QtWidgets.QWidget()

        main_lay = QtWidgets.QVBoxLayout(main_cont)

        main_lay.setContentsMargins(0, 0, 0, 0)

        main_label = QtWidgets.QLabel("Scripts")

        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")

        main_lay.addWidget(main_label)



        self.tree_widget = QtWidgets.QTreeWidget()

        self.tree_widget.setHeaderLabels(["Name", "Run Count"])

        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)

        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)

        self.tree_widget.setColumnWidth(1, 80)

        self.tree_widget.setHeaderHidden(False)

        hi = self.tree_widget.headerItem()

        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)

        self.tree_widget.setStyleSheet(style)

        self.tree_widget.setIconSize(QSize(20, 20))

        self.tree_widget.itemDoubleClicked.connect(self.run_script)

        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)

        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)

        self.tree_widget.setDragEnabled(True)

        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)

        main_lay.addWidget(self.tree_widget)



        self.search_bar = QtWidgets.QLineEdit()

        self.search_bar.setPlaceholderText("Search...")

        self.search_bar.setFocusPolicy(Qt.ClickFocus)

        self.search_bar.textChanged.connect(self.debounced_filter)

        main_lay.addWidget(self.search_bar)

        split.addWidget(main_cont)



        split.setStretchFactor(0, 0)

        split.setStretchFactor(1, 1)

        split.setSizes([300, 600])



        tf = self.tree_widget.font()

        tf.setPointSize(it_fs)

        self.tree_widget.setFont(tf)



    def init_shortcuts(self):

        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)

        pin_sc.activated.connect(self.pin_selected)

        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)

        unpin_sc.activated.connect(self.unpin_selected)

        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)

        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)

        enter_sc.activated.connect(self.run_selected_if_focused)

        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)

        explorer_sc.activated.connect(self.handle_explorer_shortcut)



    def handle_explorer_shortcut(self):

        # Check if the tree widget has focus and a folder item is selected.

        if self.tree_widget.hasFocus():

            sel = self.tree_widget.selectedItems()

            if not sel:

                return

            for item in sel:

                if item.is_folder and item.script_path:

                    self.open_in_explorer(item.script_path)

                    break



    def init_file_watcher(self):

        self.fs_watcher = QtCore.QFileSystemWatcher()

        if self.scripts_directory:

            self.fs_watcher.addPath(self.scripts_directory)

            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)



    def load_favorites(self):

        fp = Config.Paths.favorites_file()

        if os.path.exists(fp):

            try:

                with open(fp, 'r') as f:

                    self.favorites = json.load(f)

                for n, pth in self.favorites.items():

                    if os.path.exists(pth):

                        c = self.run_counts.get(pth, 0)

                        it = FavoriteItem(n, pth, c)

                        self.favorites_widget.addItem(it)

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")

                self.favorites = {}

        else:

            self.save_favorites()



    def save_favorites(self):

        try:

            with open(Config.Paths.favorites_file(), 'w') as f:

                json.dump(self.favorites, f, indent=4)

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")



    def load_run_counts(self):

        rp = Config.Paths.run_counts_file()

        if os.path.exists(rp):

            try:

                with open(rp, 'r') as f:

                    self.run_counts = json.load(f)

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")

                self.run_counts = {}

        else:

            self.save_run_counts()



    def save_run_counts(self):

        try:

            with open(Config.Paths.run_counts_file(), 'w') as f:

                json.dump(self.run_counts, f, indent=4)

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")



    def build_tree(self):

        self.tree_widget.setUpdatesEnabled(False)

        self._search_index.clear()

        self.tree_widget.clear()

        self.folder_structure = scan_dir(self.scripts_directory)

        self.populate_tree(self.tree_widget, self.folder_structure, [])

        self.tree_widget.setUpdatesEnabled(True)



    def rebuild_tree(self, _path):

        self.build_tree()



    # --- Updated populate_tree: folders now receive their full folder path ---

    def populate_tree(self, parent, structure, parents):

        for name, val in structure.items():

            if isinstance(val, dict):

                # Compute full folder path using the scripts_directory and current parent names.

                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))

                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)

                if isinstance(parent, QtWidgets.QTreeWidget):

                    parent.addTopLevelItem(it)

                else:

                    parent.addChild(it)

                self.index_item(it)

                self.populate_tree(it, val, parents + [name])

            else:

                it = ScriptItem(name, path=val, parents=parents)

                if isinstance(parent, QtWidgets.QTreeWidget):

                    parent.addTopLevelItem(it)

                else:

                    parent.addChild(it)

                self.index_item(it)



    def refresh_item(self, it):

        if getattr(it, 'is_folder', False):

            fpath = it.script_path

            if not fpath or not os.path.isdir(fpath):

                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")

                return

            pf = self.parent_names(it)

            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)

            it.takeChildren()

            self.populate_tree(it, st, pf + [it.text(0)])

            it.setExpanded(False)

        else:

            p = getattr(it, 'script_path', None)

            if p and os.path.exists(p):

                c = self.run_counts.get(p, 0)

                it.update_run_count(c)

                it.defer_tooltip()

            else:

                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")



    def debounced_filter(self, text):

        if hasattr(self, '_filter_timer'):

            self._filter_timer.stop()

        self._filter_timer = QtCore.QTimer(singleShot=True)

        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))

        self._filter_timer.start(150)



    def apply_filter(self, t):

        self.tree_widget.setUpdatesEnabled(False)

        t = t.strip().lower()

        if not t:

            self.show_all(collapse=True)

        else:

            self.hide_all()

            self.show_matches(t)

        self.tree_widget.setUpdatesEnabled(True)



    def show_all(self, collapse=False):

        for items in self._search_index.values():

            for it in items:

                it.setHidden(False)

                p = it.parent()

                while p and p != self.tree_widget.invisibleRootItem():

                    p.setHidden(False)

                    if collapse:

                        p.setExpanded(False)

                    p = p.parent()



    def hide_all(self):

        for items in self._search_index.values():

            for it in items:

                it.setHidden(True)



    def show_matches(self, txt):

        found = set()

        for n, items in self._search_index.items():

            if txt in n:

                for it in items:

                    found.add(it)

                    p = it.parent()

                    while p and p != self.tree_widget.invisibleRootItem():

                        found.add(p)

                        p = p.parent()

        for it in found:

            it.setHidden(False)

            if it.childCount() > 0:

                it.setExpanded(True)



    def display_context_menu(self, pos):

        w = self.sender()

        it = w.itemAt(pos)

        if not it:

            return



        if w == self.favorites_widget:

            # Favorites always use the 'favorites' menu

            menu_items = Config.Menu.CONTEXT['favorites']

        else:

            # For the tree_widget, decide if it's a folder or file

            is_folder = getattr(it, 'is_folder', False)

            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']



        menu = create_menu(menu_items, w)

        chosen = menu.exec(w.viewport().mapToGlobal(pos))

        if chosen:

            self.execute_menu_action(chosen.text(), it)



    def execute_menu_action(self, text, it):

        sp = getattr(it, 'script_path', None)

        from_fav = (self.sender() == self.favorites_widget)

        acts = {

            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,

            'Open Script':        lambda: self.open_script_file(sp) if sp else None,

            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,

            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,

            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,

            'Refresh':            lambda: self.refresh_item(it),

            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,

            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,

            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None

        }

        a = acts.get(text)

        if a:

            a()



    # --- Updated pin_item to prevent duplicates ---

    def pin_item(self, it):

        n, p = it.original_name, it.script_path

        if not p:

            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")

            return

        # Check if this script path is already pinned in favorites.

        if any(existing == p for existing in self.favorites.values()):

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)

            return

        self.favorites[n] = p

        self.save_favorites()

        c = self.run_counts.get(p, 0)

        fav = FavoriteItem(n, p, c)

        self.favorites_widget.addItem(fav)

        it.is_pinned = True

        it.set_icon()



    def unpin_item(self, item, from_favorites=False):

        # For FavoriteItem, data is stored as a dictionary.

        if hasattr(item, 'is_pinned') and item.is_pinned:

            if from_favorites:

                d = item.data(Qt.UserRole)

                n, p = d['name'], d['script_path']

            else:

                n, p = item.original_name, item.script_path

            if n in self.favorites and self.favorites[n] == p:

                del self.favorites[n]

                self.save_favorites()

                if from_favorites:

                    r = self.favorites_widget.row(item)

                    if r != -1:

                        self.favorites_widget.takeItem(r)

                mw = self.parent()

                if isinstance(mw, QtWidgets.QMainWindow):

                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)



    def pin_selected(self):

        sel = self.tree_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No script to pin.", 5000)

            return

        for it in sel:

            if not it.is_folder:

                self.pin_item(it)



    def unpin_selected(self):

        sel = self.favorites_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No favorite to unpin.", 5000)

            return

        for it in sel:

            self.unpin_item(it, True)



    def run_script(self, it, col):

        p = it.script_path

        if p and not it.is_folder:

            try:

                rt.filein(p)

                mw = self.parent()

                if isinstance(mw, QtWidgets.QMainWindow):

                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)

                c = self.run_counts.get(p, 0) + 1

                self.run_counts[p] = c

                self.save_run_counts()

                it.update_run_count(c)

                if it.is_pinned:

                    fi = self.find_fav_by_path(p)

                    if fi:

                        fi.update_run_count(c)

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")



    def run_script_by_path(self, p):

        if p and os.path.exists(p):

            try:

                rt.filein(p)

                mw = self.parent()

                if isinstance(mw, QtWidgets.QMainWindow):

                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)

                c = self.run_counts.get(p, 0) + 1

                self.run_counts[p] = c

                self.save_run_counts()

                return c

            except Exception as e:

                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

        else:

            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")

        return None



    def run_script_from_favorites(self, it):

        d = it.data(Qt.UserRole)

        p = d['script_path']

        if p and os.path.exists(p):

            self.run_script_by_path(p)

            c = self.run_counts.get(p, 0)

            it.update_run_count(c)



    def run_selected_if_focused(self):

        if self.tree_widget.hasFocus() or self.favorites_widget.hasFocus():

            self.run_selected()



    def run_selected(self):

        sel = self.tree_widget.selectedItems()

        if not sel:

            mw = self.parent()

            if isinstance(mw, QtWidgets.QMainWindow):

                mw.statusBar().showMessage("No scripts selected.", 5000)

            return

        for it in sel:

            if not it.is_folder and it.script_path:

                self.run_script(it, 0)



    def open_in_explorer(self, p):

        try:

            if os.name == 'nt':

                subprocess.Popen(['explorer', '/select,', p])

            elif os.name == 'posix':

                subprocess.Popen(['open', '-R', p])

            else:

                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")



    def open_script_file(self, p):

        try:

            if os.name == 'nt':

                os.startfile(p)

            elif os.name == 'posix':

                subprocess.Popen(['open', p])

            else:

                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")

        except Exception as e:

            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")



    def dragEnterEvent(self, ev):

        if ev.mimeData().hasUrls():

            ev.acceptProposedAction()

        else:

            ev.ignore()



    def dropEvent(self, ev):

        if ev.mimeData().hasUrls():

            for url in ev.mimeData().urls():

                p = url.toLocalFile()

                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):

                    n = os.path.basename(p)

                    it = self.find_by_path(self.tree_widget, p)

                    if it and not it.is_pinned:

                        self.pin_item(it)

            ev.acceptProposedAction()

        else:

            ev.ignore()



    def find_by_path(self, parent, path):

        for i in range(parent.childCount()):

            c = parent.child(i)

            if c.script_path == path:

                return c

            if c.is_folder:

                r = self.find_by_path(c, path)

                if r:

                    return r

        return None



    def find_fav_by_path(self, path):

        for i in range(self.favorites_widget.count()):

            fav = self.favorites_widget.item(i)

            d = fav.data(Qt.UserRole)

            if d and d['script_path'] == path:

                return fav

        return None



    def parent_names(self, it):

        names = []

        p = it.parent()

        while p:

            names.insert(0, p.text(0))

            p = p.parent()

        return names



    def index_item(self, it):

        n = it.original_name.lower()

        if n not in self._search_index:

            self._search_index[n] = []

        self._search_index[n].append(it)



    def expand_all(self, it):

        it.setExpanded(True)

        for i in range(it.childCount()):

            self.expand_all(it.child(i))



    def collapse_all(self, it):

        it.setExpanded(False)

        for i in range(it.childCount()):

            self.collapse_all(it.child(i))



    def mouseDoubleClickEvent(self, ev):

        super().mouseDoubleClickEvent(ev)



def create_dock_widget(scripts_directory):

    mw = qtmax.GetQMaxMainWindow()



    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")

    if existing:

        mw.removeDockWidget(existing)

        existing.deleteLater()



    dw = Dock(parent=mw, scripts_dir=scripts_directory)

    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)

    dw.setFloating(False)

    dw.show()



def main():

    # Determine the scripts directory, prioritizing 'novMaxscriptsPath' if available.

    scripts_dir = ""

    try:

        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):

            scripts_dir = rt.novMaxscriptsPath

    except AttributeError:  # Fallback to default usermacros directory

        scripts_dir = rt.getDir(rt.Name('userMacros'))



    # Initialize widget

    mw = qtmax.GetQMaxMainWindow()

    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")

    if existing:

        mw.removeDockWidget(existing)

        existing.deleteLater()

    create_dock_widget(scripts_dir)



if __name__ == "__main__":

    main()


## Message 29

**Evaluating keybindings**

I see I'm being asked to check updated keybindings and make sure that they all work correctly. Alright, I'll need to go through the code and compare each keybinding listed. I'll look for any potential issues, like conflicting commands or misconfigured keys, and test their functionality. I’m probably checking for syntax issues too, just to make sure everything’s set up properly. Okay, let's figure out the specifics and ensure there aren't any glitches!

**Verifying keybindings**

I see the keybindings in the Config.Menu block, and I need to check if they match the expected functionalities. 

For example, the "Run Script" shortcut is "Return". It's also assigned in the code, specifically for the tree widget, but it looks like it’s meant for running the script for selected files if not a folder — seems right.

For "Open Script", it doesn't have a global shortcut, and that's okay because it’s used when the context menu is open.

I’ll keep checking these to ensure everything matches up.

**Resolving shortcut conflicts**

There’s a bit of inconsistency between file, folder, and favorites keybindings. For example, "Show in Explorer" differs between file/favorites (Ctrl+Shift+E) and folder (Ctrl+Shift+O), which could cause confusion. I noticed that "Expand All" in folder uses Ctrl+Shift+E, conflicting with file's "Show in Explorer." One option could be renaming folder’s "Expand All" to Ctrl+Alt+E and "Collapse All" to Ctrl+Alt+C to avoid overlap. I'll work on re-aligning the shortcuts to make them consistent across all views.

## Message 30

Below is the full updated code. In this revision we’ve adjusted the folder context menu keybindings so that they no longer conflict with the commands for file items and favorites. In our new setup:

- **For file items and favorites (the “file” and “favorites” menus):**
  - **Run Script:** Return  
  - **Open Script:** Ctrl+Shift+O  
  - **Show in Explorer:** Ctrl+Shift+E  
  - **(For file only) Pin to Favorites:** Ctrl+Shift+P  
  - **Refresh / (for favorites there is also “Unpin from Favorites”):** F5 and Delete respectively

- **For folder items (the “folder” menu):**
  - **Expand All:** Ctrl+Alt+E  
  - **Collapse All:** Ctrl+Alt+C  
  - **Show in Explorer:** Ctrl+Shift+E  
  - **Refresh Folder:** F5

Also, the global shortcuts set in the dock’s `init_shortcuts` remain as follows:

- **Pin Selected:** Ctrl+Shift+P  
- **Unpin Selected:** Delete  
- **Run Selected:** Return  
- **Show in Explorer (for folders in the tree widget):** Ctrl+Shift+E

With these changes the commands for similar actions now share the same shortcuts regardless of where they appear (except for folder‐specific commands like “Expand All” and “Collapse All,” which now use Ctrl+Alt+E and Ctrl+Alt+C, respectively).

---

### Full Updated Code

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # Updated menus with new keybindings.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

# --- Updated FavoriteItem with explicit attributes for consistency ---
class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        # Explicitly mark this as a pinned, non-folder item.
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)
        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
        explorer_sc.activated.connect(self.handle_explorer_shortcut)

    def handle_explorer_shortcut(self):
        # If the tree widget has focus and a folder item is selected, open its folder.
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.is_folder and item.script_path:
                    self.open_in_explorer(item.script_path)
                    break

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    # --- Updated populate_tree: folders now receive their full folder path ---
    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return

        if w == self.favorites_widget:
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']

        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus() or self.favorites_widget.hasFocus():
            self.run_selected()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()

    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()

    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:
        scripts_dir = rt.getDir(rt.Name('userMacros'))

    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Verification of Keybindings

1. **Global Shortcuts (set in `init_shortcuts`):**
   - **Ctrl+Shift+P:** Calls `pin_selected` to pin the selected script.
   - **Delete:** Calls `unpin_selected` to unpin the selected favorite.
   - **Return:** Runs the selected script (via `run_selected_if_focused`).
   - **Ctrl+Shift+E (attached to the tree widget):** When a folder is selected in the Scripts tree, it triggers `handle_explorer_shortcut` which opens the folder in the system explorer.

2. **Context Menu Keybindings (as defined in `Config.Menu`):**
   - **For File Items:**
     - **Run Script:** Return  
     - **Open Script:** Ctrl+Shift+O  
     - **Show in Explorer:** Ctrl+Shift+E  
     - **Pin to Favorites:** Ctrl+Shift+P  
     - **Refresh:** F5
   - **For Folder Items:**
     - **Expand All:** Ctrl+Alt+E  
     - **Collapse All:** Ctrl+Alt+C  
     - **Show in Explorer:** Ctrl+Shift+E  
     - **Refresh Folder:** F5
   - **For Favorites:**
     - **Run Script:** Return  
     - **Open Script:** Ctrl+Shift+O  
     - **Show in Explorer:** Ctrl+Shift+E  
     - **Unpin from Favorites:** Delete

All similar actions now use the same key combination across file items and favorites, and folder-specific actions use distinct keybindings (Ctrl+Alt+E/C) that do not conflict with the global ones.

Feel free to test each keybinding in your 3ds Max environment to ensure they behave as expected.

## Message 31

the keybindings for "Expand All" and "Collapse All" doesn't work

## Message 32

**Considering keybinding issue**

The user mentioned that the keybindings for 'Expand All' and 'Collapse All' aren't working. I see that the code assigns these keybindings inside the folder context menu: `'Expand All': {'shortcut': 'Ctrl+Alt+E'}` and `'Collapse All': {'shortcut': 'Ctrl+Alt+C'}`. However, these keybindings are only activated when the context menu is visible, not globally. If the user wants them to work globally when the folder is selected, the code needs to create QShortcuts for these key combinations. I'll need to verify that.

**Proposing solution for keybindings**

The issue with 'Expand All' and 'Collapse All' is that their keybindings are only useful when the context menu is open. To make them work globally, I need to add QShortcuts that are triggered even when a folder is selected. Currently, only actions like "Show in Explorer" have global QShortcuts, so we need to establish the same for 'Expand All' and 'Collapse All.' I’ll add shortcuts to the `init_shortcuts` function, activated when the tree widget is focused. Functions like `expand_selected` and `collapse_selected` also need to be implemented.

## Message 33

Below is the full updated code. In this revision we add global QShortcuts for expanding and collapsing folder items. These new shortcuts (Ctrl+Alt+E and Ctrl+Alt+C) are attached to the tree widget and call helper methods that operate on all currently selected folder items.

In summary, after these changes the keybindings are as follows:

- **For file items and favorites (via context menu and global shortcuts):**  
  - **Run Script:** Return  
  - **Open Script:** Ctrl+Shift+O  
  - **Show in Explorer:** Ctrl+Shift+E  
  - **(For file only) Pin to Favorites:** Ctrl+Shift+P  
  - **Refresh:** F5 (context menu only)  
  - **Unpin from Favorites:** Delete (for favorites)

- **For folder items:**  
  - **Expand All:** Global shortcut Ctrl+Alt+E (or via context menu)  
  - **Collapse All:** Global shortcut Ctrl+Alt+C (or via context menu)  
  - **Show in Explorer:** Ctrl+Shift+E  
  - **Refresh Folder:** F5 (context menu only)

Below is the complete code with the added shortcuts and helper methods:

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # Updated menus with new keybindings.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

# --- Updated FavoriteItem with explicit attributes for consistency ---
class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)
        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
        explorer_sc.activated.connect(self.handle_explorer_shortcut)
        # NEW: Global shortcuts for expanding/collapsing folder items
        expand_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+E"), self.tree_widget)
        expand_sc.activated.connect(self.expand_selected)
        collapse_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+C"), self.tree_widget)
        collapse_sc.activated.connect(self.collapse_selected)

    def handle_explorer_shortcut(self):
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.is_folder and item.script_path:
                    self.open_in_explorer(item.script_path)
                    break

    def expand_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.expand_all(item)

    def collapse_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.collapse_all(item)

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return

        if w == self.favorites_widget:
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']

        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus() or self.favorites_widget.hasFocus():
            self.run_selected()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:
        scripts_dir = rt.getDir(rt.Name('userMacros'))
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation

1. **Global Shortcuts Added in `init_shortcuts`:**  
   In addition to the previously defined shortcuts, two new QShortcuts are added for expanding and collapsing folder items:
   - **Ctrl+Alt+E** is connected to the new `expand_selected` method.
   - **Ctrl+Alt+C** is connected to the new `collapse_selected` method.

2. **Helper Methods:**  
   The methods `expand_selected` and `collapse_selected` iterate over the currently selected items in the tree widget and, if the item is a folder, call the existing `expand_all` or `collapse_all` methods on that item.

3. **Keybindings Verification:**  
   All keybindings are now explicitly defined either as part of the context menu (which appear when you right-click) or as global shortcuts attached to the relevant widget (e.g. the tree widget). This ensures that when a folder is selected, pressing Ctrl+Alt+E or Ctrl+Alt+C will work correctly.

Test these keybindings in your 3ds Max environment to confirm that all actions work as intended.

## Message 34

please also fix so the keybindings works for the Favorites widget and for files in the Scripts widget

## Message 35

**Fixing keybindings**

The user is asking for keybindings to properly work for both the Favorites widget and files in the Scripts widget. The updated code I have includes keybindings for both, so maybe there's something else going wrong or I overlooked something.

Hmm, the code also includes context menus for files, favorites, and folders — could there be a conflict there? Let me double-check how the keybindings interact with these menus. I’ll see if I can spot any issues.

**Fixing keyboard shortcut issues**

So, the user wants the keybindings to work properly for both the Favorites widget and files in the Scripts widget. Right now, the global shortcuts are applied to the tree_widget, but the Favorites widget might be missing them.

I’m noting that the keybindings for file, folder, and favorites are defined in the Config.Menu class. It seems like I need to add global shortcuts for the Favorites widget, possibly in the `init_shortcuts()` method. Also, keybindings for files in the Scripts widget should be applied alongside their context menus. Let’s see how to attach them properly.

**Adding shortcuts for favorites widget**

So, I need to add keybindings for the Favorites widget too. I can create QShortcuts for each action like "Run Script," "Open Script," and "Show in Explorer" using the `QShortcut` function, and connect them to their respective methods (like `self.run_favorite_selected`).

However, the tricky part is making sure that focus on the Favorites widget will trigger its own set of shortcuts, while the tree_widget shortcuts work when focused on that. I'll need to confirm these behaviors by checking if `run_selected_if_focused` works as intended but for both widgets. Let's update `init_shortcuts`.

## Message 36

Below is the complete updated code. In this version, we make the following improvements so that the keybindings work not only for folders in the Scripts widget but also for files in the Scripts widget and for items in the Favorites widget:

1. **Global Shortcuts for the Scripts (tree) widget:**

   - **Return:** Runs the selected file(s) (if not folder) by calling a new helper (`open_selected_script` for “Open Script” and a modified explorer shortcut that—if a file is selected—opens its containing folder).
   - **Ctrl+Shift+O:** Opens the selected file(s) in an external editor (via `open_selected_script`).
   - **Ctrl+Shift+E:** If the selected item is a folder, opens that folder; if it is a file, opens the file’s containing folder.
   - **Ctrl+Alt+E/C:** Global shortcuts for expanding/collapsing folder items (as before).

2. **Global Shortcuts for the Favorites widget:**

   Attached directly to the Favorites widget, these shortcuts perform similar actions:
   - **Return:** Runs the selected favorite(s) (via `run_selected_favorites`).
   - **Ctrl+Shift+O:** Opens the selected favorite’s script file.
   - **Ctrl+Shift+E:** Opens the folder containing the selected favorite’s script.
   - **Delete:** Unpins the selected favorite(s).

Below is the full code with these changes:

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # Note: keybindings defined here are used in the context menus.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        # Global shortcuts for the Dock:
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)
        # Shortcuts for the Scripts (tree) widget:
        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
        explorer_sc.activated.connect(self.handle_explorer_shortcut)
        file_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.tree_widget)
        file_open_sc.activated.connect(self.open_selected_script)
        expand_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+E"), self.tree_widget)
        expand_sc.activated.connect(self.expand_selected)
        collapse_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+C"), self.tree_widget)
        collapse_sc.activated.connect(self.collapse_selected)
        # Shortcuts for the Favorites widget:
        fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
        fav_run_sc.activated.connect(self.run_selected_favorites)
        fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.favorites_widget)
        fav_open_sc.activated.connect(self.open_selected_favorite_script)
        fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.favorites_widget)
        fav_explorer_sc.activated.connect(self.explorer_selected_favorites)
        fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self.favorites_widget)
        fav_unpin_sc.activated.connect(self.unpin_selected)

    # --- Helper methods for the Scripts widget ---
    def handle_explorer_shortcut(self):
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.script_path:
                    if item.is_folder:
                        self.open_in_explorer(item.script_path)
                    else:
                        folder = os.path.dirname(item.script_path)
                        self.open_in_explorer(folder)
                    break

    def open_selected_script(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.open_script_file(it.script_path)

    # --- Helper methods for expanding/collapsing folders in Scripts widget ---
    def expand_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.expand_all(item)

    def collapse_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.collapse_all(item)

    # --- Helper methods for the Favorites widget ---
    def run_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            self.run_script_from_favorites(item)

    def open_selected_favorite_script(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                self.open_script_file(p)

    def explorer_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                # Open the folder containing the script.
                folder = os.path.dirname(p)
                self.open_in_explorer(folder)
                break

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return
        if w == self.favorites_widget:
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']
        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus():
            self.run_selected()
        elif self.favorites_widget.hasFocus():
            self.run_selected_favorites()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:
        scripts_dir = rt.getDir(rt.Name('userMacros'))
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Summary of Changes

- **For the Scripts (tree) widget:**  
  • A shortcut for **Ctrl+Shift+O** opens the selected file(s) using `open_selected_script`.  
  • The **Ctrl+Shift+E** shortcut now checks if the selected item is a folder or a file; for files it opens the file’s folder.  
  • Global shortcuts **Ctrl+Alt+E** and **Ctrl+Alt+C** expand or collapse selected folder items.

- **For the Favorites widget:**  
  • Added shortcuts for **Return**, **Ctrl+Shift+O**, **Ctrl+Shift+E**, and **Delete** (unpin) by attaching QShortcuts directly to the Favorites widget and defining helper methods (`run_selected_favorites`, `open_selected_favorite_script`, and `explorer_selected_favorites`).

Test all keybindings in your 3ds Max environment to ensure they now work correctly for files in the Scripts widget as well as for items in the Favorites widget.

## Message 37

the keybindings for folders works correctly, but the favorites keybinding no longer work

## Message 38

**Troubleshooting shortcuts issue**

The user's shortcuts for favorites aren't working, even though the code seems okay. I'll check the definitions for favorites shortcuts in `init_shortcuts`. I've noticed that many of the shortcuts are correctly mapped, like activating the favorite script, opening it, and unpinning items.

Hmm, maybe the problem is that the favorites widget isn't in focus or there's some other focus issue. I should recheck if the widget is getting focus when the shortcuts trigger or if there’s a problem there.

**Checking focus and shortcuts**

I’m noticing that the favorites widget might not be receiving the shortcuts, probably because it’s not getting the focus. We can check if `self.favorites_widget` has a proper focus policy. I see we’ve used `setFocusPolicy` for other widgets like `search_bar`, so maybe we need to add `self.favorites_widget.setFocusPolicy(Qt.StrongFocus)` in `init_ui` to make sure it can accept keyboard inputs.

Also, there's a slight chance the global "Delete" shortcut is conflicting with the Favorites' shortcut. I'll check that too.

## Message 39

Below is the full updated code. The changes below ensure that the Favorites widget receives keyboard focus and its shortcuts are set with a context that makes them active when the Favorites widget is focused. In particular, we now set a strong focus policy on the Favorites widget and explicitly set the context of its QShortcuts to `Qt.WidgetShortcut` so that they do not get overridden by the parent’s shortcuts.

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # Keybindings defined here are used in the context menus.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        # Set focus policy so the favorites widget can receive keyboard events.
        self.favorites_widget.setFocusPolicy(Qt.StrongFocus)
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        # Global shortcuts for the Dock:
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)
        # Shortcuts for the Scripts (tree) widget:
        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
        explorer_sc.activated.connect(self.handle_explorer_shortcut)
        file_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.tree_widget)
        file_open_sc.activated.connect(self.open_selected_script)
        expand_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+E"), self.tree_widget)
        expand_sc.activated.connect(self.expand_selected)
        collapse_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+C"), self.tree_widget)
        collapse_sc.activated.connect(self.collapse_selected)
        # Shortcuts for the Favorites widget:
        fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
        fav_run_sc.setContext(Qt.WidgetShortcut)
        fav_run_sc.activated.connect(self.run_selected_favorites)
        fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.favorites_widget)
        fav_open_sc.setContext(Qt.WidgetShortcut)
        fav_open_sc.activated.connect(self.open_selected_favorite_script)
        fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.favorites_widget)
        fav_explorer_sc.setContext(Qt.WidgetShortcut)
        fav_explorer_sc.activated.connect(self.explorer_selected_favorites)
        fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self.favorites_widget)
        fav_unpin_sc.setContext(Qt.WidgetShortcut)
        fav_unpin_sc.activated.connect(self.unpin_selected)

    # --- Helper methods for the Scripts widget ---
    def handle_explorer_shortcut(self):
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.script_path:
                    if item.is_folder:
                        self.open_in_explorer(item.script_path)
                    else:
                        folder = os.path.dirname(item.script_path)
                        self.open_in_explorer(folder)
                    break

    def open_selected_script(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.open_script_file(it.script_path)

    def expand_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.expand_all(item)

    def collapse_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.collapse_all(item)

    # --- Helper methods for the Favorites widget ---
    def run_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            self.run_script_from_favorites(item)

    def open_selected_favorite_script(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                self.open_script_file(p)

    def explorer_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                folder = os.path.dirname(p)
                self.open_in_explorer(folder)
                break

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return
        if w == self.favorites_widget:
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']
        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus():
            self.run_selected()
        elif self.favorites_widget.hasFocus():
            self.run_selected_favorites()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:
        scripts_dir = rt.getDir(rt.Name('userMacros'))
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation of Changes

1. **Favorites Widget Focus:**  
   In `init_ui`, we now call  
   ```python
   self.favorites_widget.setFocusPolicy(Qt.StrongFocus)
   ```  
   to ensure that it can receive keyboard events.

2. **Favorites Shortcuts Context:**  
   In `init_shortcuts`, the QShortcuts created for the Favorites widget have been set with:  
   ```python
   fav_run_sc.setContext(Qt.WidgetShortcut)
   ```  
   (and similarly for the others) so that these shortcuts become active when the Favorites widget is focused.

3. **Helper Methods for Favorites:**  
   We added helper methods (`run_selected_favorites`, `open_selected_favorite_script`, and `explorer_selected_favorites`) that are connected to the favorites-specific shortcuts.

Test these keybindings by clicking into the Favorites widget so that it receives focus, then try pressing Return, Ctrl+Shift+O, Ctrl+Shift+E, and Delete. They should now work as intended.

If you have any further issues or questions, feel free to ask!

## Message 40

now the keybindings work both for files and folders in the Scripts widget, however none of them work in the Favorites widget

## Message 41

**Fixing keybindings in Favorites widget**

I need to ensure the keybindings work in the Favorites widget. We’ve already tried setting a stronger focus policy and setting Qt.WidgetShortcut for the QShortcuts. We've used:

```
self.favorites_widget.setFocusPolicy(Qt.StrongFocus)
```

And for favorites shortcuts:

```
fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
fav_run_sc.setContext(Qt.WidgetShortcut)
fav_run_sc.activated.connect(self.run_selected_favorites)
```

Given that the Favorites widget is a QListWidget, it’s possible that it's not receiving focus, or the context of QShortcut could need tweaking. Let's test that.

**Debugging keybinding focus issues**

I might need to adjust the QShortcuts for the Favorites widget. We’ve already made them children of `favorites_widget` and used `Qt.WidgetShortcut`, but maybe using `Qt.WidgetWithChildrenShortcut` would help.

Another thought: perhaps the `favorites_widget` isn’t getting focus properly. Even though we set the focus policy with `Qt.StrongFocus`, I could try explicitly calling `setFocus()` after it's clicked to make sure it's receiving focus.

Also, there's the option to try setting the context to `Qt.WindowShortcut`, though that could conflict with parent shortcuts. Let's try these changes and see.

## Message 42

Below is the full updated code with changes to the Favorites widget shortcuts. In this version, the QShortcuts for the Favorites widget are now created with a context of `Qt.WidgetWithChildrenShortcut` (instead of `Qt.WidgetShortcut`), ensuring that they work even when a child widget (or an item) inside the Favorites widget has focus.

Please try the following updated code:

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # These are the keybindings used in the context menus.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        # Ensure the Favorites widget can receive keyboard focus.
        self.favorites_widget.setFocusPolicy(Qt.StrongFocus)
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        # Global shortcuts for the Dock:
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)
        # Shortcuts for the Scripts (tree) widget:
        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
        explorer_sc.activated.connect(self.handle_explorer_shortcut)
        file_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.tree_widget)
        file_open_sc.activated.connect(self.open_selected_script)
        expand_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+E"), self.tree_widget)
        expand_sc.activated.connect(self.expand_selected)
        collapse_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+C"), self.tree_widget)
        collapse_sc.activated.connect(self.collapse_selected)
        # Shortcuts for the Favorites widget:
        fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self.favorites_widget)
        fav_run_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_run_sc.activated.connect(self.run_selected_favorites)
        fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.favorites_widget)
        fav_open_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_open_sc.activated.connect(self.open_selected_favorite_script)
        fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.favorites_widget)
        fav_explorer_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_explorer_sc.activated.connect(self.explorer_selected_favorites)
        fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self.favorites_widget)
        fav_unpin_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_unpin_sc.activated.connect(self.unpin_selected)

    # --- Helper methods for the Scripts widget ---
    def handle_explorer_shortcut(self):
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.script_path:
                    if item.is_folder:
                        self.open_in_explorer(item.script_path)
                    else:
                        folder = os.path.dirname(item.script_path)
                        self.open_in_explorer(folder)
                    break

    def open_selected_script(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.open_script_file(it.script_path)

    def expand_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.expand_all(item)

    def collapse_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.collapse_all(item)

    # --- Helper methods for the Favorites widget ---
    def run_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            self.run_script_from_favorites(item)

    def open_selected_favorite_script(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                self.open_script_file(p)

    def explorer_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                folder = os.path.dirname(p)
                self.open_in_explorer(folder)
                break

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return
        if w == self.favorites_widget:
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']
        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus():
            self.run_selected()
        elif self.favorites_widget.hasFocus():
            self.run_selected_favorites()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:
        scripts_dir = rt.getDir(rt.Name('userMacros'))
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### Explanation

1. **Favorites Widget Focus:**  
   We explicitly set the favorites widget’s focus policy to `Qt.StrongFocus` in `init_ui`.

2. **Favorites Shortcuts Context:**  
   When creating the favorites widget shortcuts (for running, opening, exploring, and unpinning), we now call  
   ```python
   fav_run_sc.setContext(Qt.WidgetWithChildrenShortcut)
   ```  
   (and similarly for the others) so that these shortcuts will fire when the favorites widget or any of its children have focus.

3. **Helper Methods for Favorites:**  
   The helper methods (`run_selected_favorites`, `open_selected_favorite_script`, and `explorer_selected_favorites`) remain attached to the favorites widget shortcuts.

Please test the keybindings in the Favorites widget (by clicking inside it so that it receives focus) and verify that pressing Return, Ctrl+Shift+O, Ctrl+Shift+E, and Delete work as expected.

If you continue to experience issues, let me know and we can further adjust the shortcut contexts.

## Message 43

still none of the keybindings work for the "Favorites" widget. in the initial code the "Delete" key worked for Favorites, but now none of them works. all of the keybindings for the "Scripts" widget seems to work okay

## Message 44

**Investigating keybinding issue**

Okay, the user says none of the keybindings work for their Favorites widget, even though the 'Delete' key worked before. The Favorites widget is a QListWidget, and the keybindings are defined as QShortcuts attached to it.

I need to check if QListWidget accepts shortcuts as expected, or if its internal focus policy might be causing issues. Maybe I should attach the QShortcuts to the main window instead. Something's likely interfering here, so I need to dig deeper into this!

**Considering keybinding solutions**

The issue is with keybindings for the Favorites widget — they worked initially, but now don't. We were attaching QShortcuts directly to the Favorites widget, but maybe it already handles keys like Delete differently. Perhaps we need to attach the QShortcuts to the parent window (like the Dock widget) instead. Alternatively, we could use an event filter and check if the Favorites widget has focus. I'll need to experiment with attaching shortcuts to the main window or Dock widget and adjusting focus checks accordingly.

## Message 45

Below is the full updated code. The changes below attach the Favorites‐widget keybindings to the parent (the Dock widget) rather than directly to the QListWidget. In the activated slots we then check if the Favorites widget has focus. This ensures that when you are working inside Favorites the keys (Return, Ctrl+Shift+O, Ctrl+Shift+E, and Delete) will work as intended, while not interfering with the Scripts widget keybindings.

---

```python
import sys
import os
import subprocess
import json
import fnmatch
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QDateTime, QSize
from PySide6.QtGui import QAction, QKeySequence
from pymxs import runtime as rt
import qtmax

PRINT_DEBUG_INFO = False

class Config:
    STYLESHEET_TEMPLATE = """
        QTreeWidget {{
            background-color: {tree_bg};
            color: {tree_fg};
            border: none;
            padding: {pad}px;
        }}
        QTreeWidget::item {{
            padding: {pad}px;
            font-size: {item_font_size}px;
        }}
        QTreeWidget::item:hover {{
            background-color: {hover_bg};
        }}
        QTreeWidget::item:selected {{
            background-color: {sel_bg};
        }}
        QTreeWidget::item:pressed {{
            background-color: {press_bg};
        }}
        QHeaderView::section {{
            background-color: {hdr_bg};
            color: {hdr_fg};
            padding: {pad}px;
            font-size: {hdr_font_size}px;
            border: 1px solid {hdr_border};
        }}
    """

    class UI:
        MIN_DOCK_WIDTH = 300
        MIN_DOCK_HEIGHT = 400
        SPLITTER_RATIO = [1, 2]
        ICON_SIZE = QSize(20, 20)
        TREE_INDENT = 20
        COLUMN_WIDTHS = {"name": "stretch", "run_count": 80}
        SEARCH_DEBOUNCE_MS = 150
        SEARCH_MIN_CHARS = 2
        AUTO_EXPAND_RESULTS = True
        PRESERVE_EXPANSION = False
        FOLDERS_EXPANDABLE = True
        DOUBLE_CLICK_ACTION = "run"
        BUTTON_PADDING_RATIO = 0.002
        HEADER_FONT_RATIO = 0.007
        ITEM_FONT_RATIO = 0.005

    class Style:
        FAVORITES_BG = "#1a1a1a"
        TREE_BG = "#2b2b2b"
        FAVORITES_FG = "#f0f0f0"
        TREE_FG = "#f0f0f0"
        PIN_COLOR = "#79ff1c"
        HDR_BG = "#2b2b2b"
        HDR_FG = "#f0f0f0"
        HDR_BORDER = "#3c3c3c"
        HOVER_BG = "#3c3c3c"
        SEL_BG = "#1e90ff"
        PRESS_BG = "#104e8b"
        TINT_RULES = [
            {'pattern': '*actions*', 'color': '#c65439', 'alpha': 0.15},
            {'pattern': '*hotkeys*', 'color': '#d843bb', 'alpha': 0.15},
            {'pattern': '*utils*', 'color': '#3483b4', 'alpha': 0.15},
            {'pattern': '*todo*', 'color': '#93888e', 'alpha': 0.15},
        ]

    class Paths:
        SUPPORTED = ('.ms', '.mse', '.json')
        MAX_DEPTH = 8

        @classmethod
        def favorites_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "favorites.json")

        @classmethod
        def run_counts_file(cls):
            return os.path.join(rt.getDir(rt.Name("userMacros")), "run_counts.json")

    class Menu:
        # These keybindings appear in the context menus.
        CONTEXT = {
            'file': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Pin to Favorites', 'shortcut': 'Ctrl+Shift+P', 'icon': 'SP_DialogYesButton'},
                {'text': 'Refresh', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'folder': [
                {'text': 'Expand All', 'shortcut': 'Ctrl+Alt+E', 'icon': 'SP_FileDialogDetailedView'},
                {'text': 'Collapse All', 'shortcut': 'Ctrl+Alt+C', 'icon': 'SP_FileDialogListView'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Refresh Folder', 'shortcut': 'F5', 'icon': 'SP_BrowserReload'}
            ],
            'favorites': [
                {'text': 'Run Script', 'shortcut': 'Return', 'icon': 'SP_MediaPlay'},
                {'text': 'Open Script', 'shortcut': 'Ctrl+Shift+O', 'icon': 'SP_FileIcon'},
                {'text': 'Show in Explorer', 'shortcut': 'Ctrl+Shift+E', 'icon': 'SP_DirIcon'},
                {'text': '-'},
                {'text': 'Unpin from Favorites', 'shortcut': 'Delete', 'icon': 'SP_DialogNoButton'}
            ]
        }

    def stylesheet(self):
        screen_h = QtWidgets.QApplication.primaryScreen().geometry().height()
        pad = int(screen_h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(screen_h * Config.UI.HEADER_FONT_RATIO))
        item_fs = max(10, int(screen_h * Config.UI.ITEM_FONT_RATIO))
        return self.STYLESHEET_TEMPLATE.format(
            tree_bg=Config.Style.TREE_BG,
            tree_fg=Config.Style.TREE_FG,
            hdr_bg=Config.Style.HDR_BG,
            hdr_fg=Config.Style.HDR_FG,
            hdr_border=Config.Style.HDR_BORDER,
            hover_bg=Config.Style.HOVER_BG,
            sel_bg=Config.Style.SEL_BG,
            press_bg=Config.Style.PRESS_BG,
            pad=pad,
            hdr_font_size=hdr_fs,
            item_font_size=item_fs
        )

def create_menu(menu_items, parent):
    m = QtWidgets.QMenu(parent)
    for it in menu_items:
        if it['text'] == '-':
            m.addSeparator()
            continue
        a = QAction(it['text'], parent)
        if 'shortcut' in it:
            a.setShortcut(QKeySequence(it['shortcut']))
        if 'icon' in it:
            icon = parent.style().standardIcon(getattr(QtWidgets.QStyle, it['icon']))
            a.setIcon(icon)
        m.addAction(a)
    return m

def scan_dir(path, max_depth=Config.Paths.MAX_DEPTH, depth=0):
    if depth > max_depth:
        return {}
    structure = {}
    try:
        with os.scandir(path) as it:
            dirs, files = [], []
            for e in it:
                if e.is_dir():
                    dirs.append(e)
                elif e.is_file() and e.name.endswith(Config.Paths.SUPPORTED):
                    files.append(e)
            dirs.sort(key=lambda x: x.name.lower())
            files.sort(key=lambda x: x.name.lower())
            for d in dirs:
                sub = scan_dir(d.path, max_depth, depth+1)
                if sub:
                    structure[d.name] = sub
            for f in files:
                structure[f.name] = f.path
    except PermissionError:
        pass
    return structure

def blend(base_hex, overlay_hex, alpha=0.3):
    base = QtGui.QColor(base_hex)
    over = QtGui.QColor(overlay_hex)
    r = int(base.red()*(1-alpha) + over.red()*alpha)
    g = int(base.green()*(1-alpha) + over.green()*alpha)
    b = int(base.blue()*(1-alpha) + over.blue()*alpha)
    c = QtGui.QColor(r, g, b).name()
    if PRINT_DEBUG_INFO:
        print(f"Blending '{base_hex}' + '{overlay_hex}' @ {alpha} -> '{c}'")
    return c

class ScriptItem(QtWidgets.QTreeWidgetItem):
    def __init__(self, name, path=None, folder=False, pinned=False, count=0, parents=None):
        super().__init__([name, "" if folder else str(count)])
        self.original_name = name
        self.script_path = path
        self.is_folder = folder
        self.is_pinned = pinned
        self.run_count = count
        self.setData(0, Qt.UserRole, path)
        self.setData(0, Qt.UserRole+1, folder)
        self.setFlags(self.flags() | Qt.ItemIsSelectable | Qt.ItemIsEnabled)
        self.set_icon()
        self.defer_tooltip()
        if pinned:
            self.setData(0, Qt.UserRole+2, True)
            self.setData(1, Qt.UserRole+2, True)
            pcolor = QtGui.QColor(Config.Style.PIN_COLOR)
            self.setForeground(0, QtGui.QBrush(pcolor))
            self.setForeground(1, QtGui.QBrush(pcolor))
        if not folder:
            self.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tint_background(parents)

    def set_icon(self):
        style = QtWidgets.QApplication.style()
        if self.is_pinned:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileDialogListView)
        elif self.is_folder:
            ic = style.standardIcon(QtWidgets.QStyle.SP_DirIcon)
        else:
            ic = style.standardIcon(QtWidgets.QStyle.SP_FileIcon)
        self.setIcon(0, ic)

    def defer_tooltip(self):
        if not self.is_folder and self.script_path:
            self.setToolTip(0, "Loading...")
            QtCore.QTimer.singleShot(100, self.load_tooltip)

    def load_tooltip(self):
        if self.script_path and os.path.exists(self.script_path):
            try:
                st = os.stat(self.script_path)
                lm = QDateTime.fromSecsSinceEpoch(int(st.st_mtime)).toString(Qt.DefaultLocaleShortDate)
                sz = st.st_size
                self.setToolTip(0, f"Path: {self.script_path}\nLast Modified: {lm}\nSize: {sz} bytes")
            except Exception as e:
                self.setToolTip(0, f"Path: {self.script_path}\nError: {e}")

    def update_run_count(self, n):
        self.run_count = n
        if not self.is_folder:
            self.setText(1, str(n))

    def tint_background(self, parents):
        tinted = False
        if parents:
            for rule in Config.Style.TINT_RULES:
                pat, c, a = rule['pattern'], rule['color'], rule['alpha']
                for f in parents:
                    if fnmatch.fnmatch(f.lower(), pat):
                        blended = blend(Config.Style.TREE_BG, c, a)
                        self.setBackground(0, QtGui.QBrush(QtGui.QColor(blended)))
                        self.setBackground(1, QtGui.QBrush(QtGui.QColor(blended)))
                        tinted = True
                        break
                if tinted:
                    break
        if not tinted:
            bg = QtGui.QColor(Config.Style.TREE_BG)
            self.setBackground(0, QtGui.QBrush(bg))
            self.setBackground(1, QtGui.QBrush(bg))

class FavoriteItem(QtWidgets.QListWidgetItem):
    def __init__(self, name, path, count=0):
        super().__init__(name)
        self.script_path = path
        self.run_count = count
        self.is_pinned = True
        self.is_folder = False
        self.setData(Qt.UserRole, {'script_path': path, 'run_count': count, 'name': name})
        self.setToolTip(f"Path: {path}\nRun Count: {count}")

    def update_run_count(self, n):
        self.run_count = n
        d = self.data(Qt.UserRole)
        d['run_count'] = n
        self.setData(Qt.UserRole, d)
        self.setToolTip(f"Path: {d['script_path']}\nRun Count: {n}")

class Dock(QtWidgets.QDockWidget):
    def __init__(self, parent=None, scripts_dir=None):
        super().__init__(parent)
        self.setWindowTitle("Jorn Tools: Widget")
        self.setAllowedAreas(Qt.AllDockWidgetAreas)
        self.setObjectName("jornWidget_treeView")
        self.setAttribute(QtCore.Qt.WA_DeleteOnClose)

        self.scripts_directory = scripts_dir
        self.favorites = {}
        self.run_counts = {}
        self._search_index = {}
        self._last_search = ""

        self.init_ui()
        self.load_run_counts()
        self.load_favorites()
        if self.scripts_directory:
            self.build_tree()
        self.init_file_watcher()
        self.init_shortcuts()

    def init_ui(self):
        c = QtWidgets.QWidget()
        self.setWidget(c)
        lay = QtWidgets.QVBoxLayout(c)
        split = QtWidgets.QSplitter(Qt.Vertical)
        lay.addWidget(split)

        fav_cont = QtWidgets.QWidget()
        fav_lay = QtWidgets.QVBoxLayout(fav_cont)
        fav_lay.setContentsMargins(0, 0, 0, 0)
        fav_label = QtWidgets.QLabel("Favorites")
        fav_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        fav_lay.addWidget(fav_label)

        self.favorites_widget = QtWidgets.QListWidget()
        # Set the Favorites widget to receive focus
        self.favorites_widget.setFocusPolicy(Qt.StrongFocus)
        self.favorites_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.favorites_widget.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)
        self.favorites_widget.setDefaultDropAction(Qt.MoveAction)
        self.favorites_widget.setDropIndicatorShown(True)
        self.favorites_widget.itemDoubleClicked.connect(self.run_script_from_favorites)
        self.favorites_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.favorites_widget.customContextMenuRequested.connect(self.display_context_menu)
        fav_lay.addWidget(self.favorites_widget)
        split.addWidget(fav_cont)

        h = QtWidgets.QApplication.primaryScreen().geometry().height()
        p = int(h * Config.UI.BUTTON_PADDING_RATIO)
        hdr_fs = max(10, int(h * Config.UI.HEADER_FONT_RATIO))
        it_fs = max(10, int(h * Config.UI.ITEM_FONT_RATIO))
        style = Config().stylesheet()
        self.favorites_widget.setStyleSheet(style)
        self.favorites_widget.setIconSize(QSize(20, 20))
        ff = self.favorites_widget.font()
        ff.setPointSize(it_fs)
        self.favorites_widget.setFont(ff)

        main_cont = QtWidgets.QWidget()
        main_lay = QtWidgets.QVBoxLayout(main_cont)
        main_lay.setContentsMargins(0, 0, 0, 0)
        main_label = QtWidgets.QLabel("Scripts")
        main_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        main_lay.addWidget(main_label)

        self.tree_widget = QtWidgets.QTreeWidget()
        self.tree_widget.setHeaderLabels(["Name", "Run Count"])
        self.tree_widget.header().setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        self.tree_widget.header().setSectionResizeMode(1, QtWidgets.QHeaderView.Fixed)
        self.tree_widget.setColumnWidth(1, 80)
        self.tree_widget.setHeaderHidden(False)
        hi = self.tree_widget.headerItem()
        hi.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
        self.tree_widget.setStyleSheet(style)
        self.tree_widget.setIconSize(QSize(20, 20))
        self.tree_widget.itemDoubleClicked.connect(self.run_script)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.display_context_menu)
        self.tree_widget.setDragEnabled(True)
        self.tree_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_lay.addWidget(self.tree_widget)

        self.search_bar = QtWidgets.QLineEdit()
        self.search_bar.setPlaceholderText("Search...")
        self.search_bar.setFocusPolicy(Qt.ClickFocus)
        self.search_bar.textChanged.connect(self.debounced_filter)
        main_lay.addWidget(self.search_bar)
        split.addWidget(main_cont)

        split.setStretchFactor(0, 0)
        split.setStretchFactor(1, 1)
        split.setSizes([300, 600])

        tf = self.tree_widget.font()
        tf.setPointSize(it_fs)
        self.tree_widget.setFont(tf)

    def init_shortcuts(self):
        # Global shortcuts for the Dock:
        pin_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        pin_sc.activated.connect(self.pin_selected)
        unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        unpin_sc.activated.connect(self.unpin_selected)
        enter_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        enter_sc.setContext(Qt.WidgetWithChildrenShortcut)
        enter_sc.activated.connect(self.run_selected_if_focused)
        # Shortcuts for the Scripts (tree) widget:
        explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self.tree_widget)
        explorer_sc.activated.connect(self.handle_explorer_shortcut)
        file_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self.tree_widget)
        file_open_sc.activated.connect(self.open_selected_script)
        expand_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+E"), self.tree_widget)
        expand_sc.activated.connect(self.expand_selected)
        collapse_sc = QtGui.QShortcut(QKeySequence("Ctrl+Alt+C"), self.tree_widget)
        collapse_sc.activated.connect(self.collapse_selected)
        # Instead of attaching directly to the Favorites widget,
        # attach favorites shortcuts to the Dock and check if favorites_widget is focused.
        fav_run_sc = QtGui.QShortcut(QKeySequence("Return"), self)
        fav_run_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_run_sc.activated.connect(self.handle_fav_run)
        fav_open_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+O"), self)
        fav_open_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_open_sc.activated.connect(self.handle_fav_open)
        fav_explorer_sc = QtGui.QShortcut(QKeySequence("Ctrl+Shift+E"), self)
        fav_explorer_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_explorer_sc.activated.connect(self.handle_fav_explorer)
        fav_unpin_sc = QtGui.QShortcut(QKeySequence("Delete"), self)
        fav_unpin_sc.setContext(Qt.WidgetWithChildrenShortcut)
        fav_unpin_sc.activated.connect(self.handle_fav_unpin)

    # --- Helper methods for the Scripts widget ---
    def handle_explorer_shortcut(self):
        if self.tree_widget.hasFocus():
            sel = self.tree_widget.selectedItems()
            if not sel:
                return
            for item in sel:
                if item.script_path:
                    if item.is_folder:
                        self.open_in_explorer(item.script_path)
                    else:
                        folder = os.path.dirname(item.script_path)
                        self.open_in_explorer(folder)
                    break

    def open_selected_script(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.open_script_file(it.script_path)

    def expand_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.expand_all(item)

    def collapse_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            return
        for item in sel:
            if item.is_folder:
                self.collapse_all(item)

    # --- Helper methods for the Favorites widget (via the Dock) ---
    def handle_fav_run(self):
        # Only trigger if the favorites widget is focused.
        if self.favorites_widget.hasFocus():
            self.run_selected_favorites()

    def handle_fav_open(self):
        if self.favorites_widget.hasFocus():
            self.open_selected_favorite_script()

    def handle_fav_explorer(self):
        if self.favorites_widget.hasFocus():
            self.explorer_selected_favorites()

    def handle_fav_unpin(self):
        if self.favorites_widget.hasFocus():
            self.unpin_selected()

    def run_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            self.run_script_from_favorites(item)

    def open_selected_favorite_script(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                self.open_script_file(p)

    def explorer_selected_favorites(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite selected.", 5000)
            return
        for item in sel:
            d = item.data(Qt.UserRole)
            p = d.get("script_path", None)
            if p:
                folder = os.path.dirname(p)
                self.open_in_explorer(folder)
                break

    def init_file_watcher(self):
        self.fs_watcher = QtCore.QFileSystemWatcher()
        if self.scripts_directory:
            self.fs_watcher.addPath(self.scripts_directory)
            self.fs_watcher.directoryChanged.connect(self.rebuild_tree)

    def load_run_counts(self):
        rp = Config.Paths.run_counts_file()
        if os.path.exists(rp):
            try:
                with open(rp, 'r') as f:
                    self.run_counts = json.load(f)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load run counts:\n{e}")
                self.run_counts = {}
        else:
            self.save_run_counts()

    def save_run_counts(self):
        try:
            with open(Config.Paths.run_counts_file(), 'w') as f:
                json.dump(self.run_counts, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save run counts:\n{e}")

    def load_favorites(self):
        fp = Config.Paths.favorites_file()
        if os.path.exists(fp):
            try:
                with open(fp, 'r') as f:
                    self.favorites = json.load(f)
                for n, pth in self.favorites.items():
                    if os.path.exists(pth):
                        c = self.run_counts.get(pth, 0)
                        it = FavoriteItem(n, pth, c)
                        self.favorites_widget.addItem(it)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to load favorites:\n{e}")
                self.favorites = {}
        else:
            self.save_favorites()

    def save_favorites(self):
        try:
            with open(Config.Paths.favorites_file(), 'w') as f:
                json.dump(self.favorites, f, indent=4)
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed to save favorites:\n{e}")

    def build_tree(self):
        self.tree_widget.setUpdatesEnabled(False)
        self._search_index.clear()
        self.tree_widget.clear()
        self.folder_structure = scan_dir(self.scripts_directory)
        self.populate_tree(self.tree_widget, self.folder_structure, [])
        self.tree_widget.setUpdatesEnabled(True)

    def rebuild_tree(self, _path):
        self.build_tree()

    def populate_tree(self, parent, structure, parents):
        for name, val in structure.items():
            if isinstance(val, dict):
                folder_path = os.path.join(self.scripts_directory, *(parents + [name]))
                it = ScriptItem(name, path=folder_path, folder=True, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)
                self.populate_tree(it, val, parents + [name])
            else:
                it = ScriptItem(name, path=val, parents=parents)
                if isinstance(parent, QtWidgets.QTreeWidget):
                    parent.addTopLevelItem(it)
                else:
                    parent.addChild(it)
                self.index_item(it)

    def refresh_item(self, it):
        if getattr(it, 'is_folder', False):
            fpath = it.script_path
            if not fpath or not os.path.isdir(fpath):
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid folder path '{fpath}'")
                return
            pf = self.parent_names(it)
            st = scan_dir(fpath, Config.Paths.MAX_DEPTH, 0)
            it.takeChildren()
            self.populate_tree(it, st, pf + [it.text(0)])
            it.setExpanded(False)
        else:
            p = getattr(it, 'script_path', None)
            if p and os.path.exists(p):
                c = self.run_counts.get(p, 0)
                it.update_run_count(c)
                it.defer_tooltip()
            else:
                QtWidgets.QMessageBox.warning(self, "Refresh Failed", f"Invalid file '{p}'")

    def debounced_filter(self, text):
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        self._filter_timer = QtCore.QTimer(singleShot=True)
        self._filter_timer.timeout.connect(lambda: self.apply_filter(text))
        self._filter_timer.start(150)

    def apply_filter(self, t):
        self.tree_widget.setUpdatesEnabled(False)
        t = t.strip().lower()
        if not t:
            self.show_all(collapse=True)
        else:
            self.hide_all()
            self.show_matches(t)
        self.tree_widget.setUpdatesEnabled(True)

    def show_all(self, collapse=False):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(False)
                p = it.parent()
                while p and p != self.tree_widget.invisibleRootItem():
                    p.setHidden(False)
                    if collapse:
                        p.setExpanded(False)
                    p = p.parent()

    def hide_all(self):
        for items in self._search_index.values():
            for it in items:
                it.setHidden(True)

    def show_matches(self, txt):
        found = set()
        for n, items in self._search_index.items():
            if txt in n:
                for it in items:
                    found.add(it)
                    p = it.parent()
                    while p and p != self.tree_widget.invisibleRootItem():
                        found.add(p)
                        p = p.parent()
        for it in found:
            it.setHidden(False)
            if it.childCount() > 0:
                it.setExpanded(True)

    def display_context_menu(self, pos):
        w = self.sender()
        it = w.itemAt(pos)
        if not it:
            return
        if w == self.favorites_widget:
            menu_items = Config.Menu.CONTEXT['favorites']
        else:
            is_folder = getattr(it, 'is_folder', False)
            menu_items = Config.Menu.CONTEXT['folder'] if is_folder else Config.Menu.CONTEXT['file']
        menu = create_menu(menu_items, w)
        chosen = menu.exec(w.viewport().mapToGlobal(pos))
        if chosen:
            self.execute_menu_action(chosen.text(), it)

    def execute_menu_action(self, text, it):
        sp = getattr(it, 'script_path', None)
        from_fav = (self.sender() == self.favorites_widget)
        acts = {
            'Run Script':         lambda: self.run_script(it, 0) if sp and not getattr(it, 'is_folder', False) else None,
            'Open Script':        lambda: self.open_script_file(sp) if sp else None,
            'Show in Explorer':   lambda: self.open_in_explorer(sp) if sp else None,
            'Pin to Favorites':   lambda: self.pin_item(it) if sp and not getattr(it, 'is_pinned', False) else None,
            'Unpin from Favorites': lambda: self.unpin_item(it, from_favorites=from_fav) if getattr(it, 'is_pinned', False) else None,
            'Refresh':            lambda: self.refresh_item(it),
            'Expand All':         lambda: self.expand_all(it) if getattr(it, 'is_folder', False) else None,
            'Collapse All':       lambda: self.collapse_all(it) if getattr(it, 'is_folder', False) else None,
            'Refresh Folder':     lambda: self.refresh_item(it) if getattr(it, 'is_folder', False) else None
        }
        a = acts.get(text)
        if a:
            a()

    def pin_item(self, it):
        n, p = it.original_name, it.script_path
        if not p:
            QtWidgets.QMessageBox.warning(self, "Pin Failed", "No script path.")
            return
        if any(existing == p for existing in self.favorites.values()):
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage(f"'{n}' is already pinned.", 5000)
            return
        self.favorites[n] = p
        self.save_favorites()
        c = self.run_counts.get(p, 0)
        fav = FavoriteItem(n, p, c)
        self.favorites_widget.addItem(fav)
        it.is_pinned = True
        it.set_icon()

    def unpin_item(self, item, from_favorites=False):
        if hasattr(item, 'is_pinned') and item.is_pinned:
            if from_favorites:
                d = item.data(Qt.UserRole)
                n, p = d['name'], d['script_path']
            else:
                n, p = item.original_name, item.script_path
            if n in self.favorites and self.favorites[n] == p:
                del self.favorites[n]
                self.save_favorites()
                if from_favorites:
                    r = self.favorites_widget.row(item)
                    if r != -1:
                        self.favorites_widget.takeItem(r)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"'{n}' removed from Favorites.", 5000)

    def pin_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No script to pin.", 5000)
            return
        for it in sel:
            if not it.is_folder:
                self.pin_item(it)

    def unpin_selected(self):
        sel = self.favorites_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No favorite to unpin.", 5000)
            return
        for it in sel:
            self.unpin_item(it, True)

    def run_script(self, it, col):
        p = it.script_path
        if p and not it.is_folder:
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                it.update_run_count(c)
                if it.is_pinned:
                    fi = self.find_fav_by_path(p)
                    if fi:
                        fi.update_run_count(c)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")

    def run_script_by_path(self, p):
        if p and os.path.exists(p):
            try:
                rt.filein(p)
                mw = self.parent()
                if isinstance(mw, QtWidgets.QMainWindow):
                    mw.statusBar().showMessage(f"Executing '{os.path.basename(p)}'...", 5000)
                c = self.run_counts.get(p, 0) + 1
                self.run_counts[p] = c
                self.save_run_counts()
                return c
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "Error", f"Failed to run {os.path.basename(p)}:\n{e}")
        else:
            QtWidgets.QMessageBox.warning(self, "Run Script", "Invalid path.")
        return None

    def run_script_from_favorites(self, it):
        d = it.data(Qt.UserRole)
        p = d['script_path']
        if p and os.path.exists(p):
            self.run_script_by_path(p)
            c = self.run_counts.get(p, 0)
            it.update_run_count(c)

    def run_selected_if_focused(self):
        if self.tree_widget.hasFocus():
            self.run_selected()
        elif self.favorites_widget.hasFocus():
            self.run_selected_favorites()

    def run_selected(self):
        sel = self.tree_widget.selectedItems()
        if not sel:
            mw = self.parent()
            if isinstance(mw, QtWidgets.QMainWindow):
                mw.statusBar().showMessage("No scripts selected.", 5000)
            return
        for it in sel:
            if not it.is_folder and it.script_path:
                self.run_script(it, 0)

    def open_in_explorer(self, p):
        try:
            if os.name == 'nt':
                subprocess.Popen(['explorer', '/select,', p])
            elif os.name == 'posix':
                subprocess.Popen(['open', '-R', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open explorer.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def open_script_file(self, p):
        try:
            if os.name == 'nt':
                os.startfile(p)
            elif os.name == 'posix':
                subprocess.Popen(['open', p])
            else:
                QtWidgets.QMessageBox.warning(self, "OS Unsupported", "Cannot open file.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Failed:\n{e}")

    def dragEnterEvent(self, ev):
        if ev.mimeData().hasUrls():
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def dropEvent(self, ev):
        if ev.mimeData().hasUrls():
            for url in ev.mimeData().urls():
                p = url.toLocalFile()
                if os.path.isfile(p) and p.endswith(Config.Paths.SUPPORTED):
                    n = os.path.basename(p)
                    it = self.find_by_path(self.tree_widget, p)
                    if it and not it.is_pinned:
                        self.pin_item(it)
            ev.acceptProposedAction()
        else:
            ev.ignore()

    def find_by_path(self, parent, path):
        for i in range(parent.childCount()):
            c = parent.child(i)
            if c.script_path == path:
                return c
            if c.is_folder:
                r = self.find_by_path(c, path)
                if r:
                    return r
        return None

    def find_fav_by_path(self, path):
        for i in range(self.favorites_widget.count()):
            fav = self.favorites_widget.item(i)
            d = fav.data(Qt.UserRole)
            if d and d['script_path'] == path:
                return fav
        return None

    def parent_names(self, it):
        names = []
        p = it.parent()
        while p:
            names.insert(0, p.text(0))
            p = p.parent()
        return names

    def index_item(self, it):
        n = it.original_name.lower()
        if n not in self._search_index:
            self._search_index[n] = []
        self._search_index[n].append(it)

    def expand_all(self, it):
        it.setExpanded(True)
        for i in range(it.childCount()):
            self.expand_all(it.child(i))

    def collapse_all(self, it):
        it.setExpanded(False)
        for i in range(it.childCount()):
            self.collapse_all(it.child(i))

    def mouseDoubleClickEvent(self, ev):
        super().mouseDoubleClickEvent(ev)

def create_dock_widget(scripts_directory):
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    dw = Dock(parent=mw, scripts_dir=scripts_directory)
    mw.addDockWidget(Qt.LeftDockWidgetArea, dw)
    dw.setFloating(False)
    dw.show()

def main():
    scripts_dir = ""
    try:
        if hasattr(rt, 'novMaxscriptsPath') and os.path.isdir(rt.novMaxscriptsPath):
            scripts_dir = rt.novMaxscriptsPath
    except AttributeError:
        scripts_dir = rt.getDir(rt.Name('userMacros'))
    mw = qtmax.GetQMaxMainWindow()
    existing = mw.findChild(QtWidgets.QDockWidget, "jornWidget_treeView")
    if existing:
        mw.removeDockWidget(existing)
        existing.deleteLater()
    create_dock_widget(scripts_dir)

if __name__ == "__main__":
    main()
```

---

### How It Works

1. **Favorites Widget Shortcuts Now Attached to the Dock:**  
   In the `init_shortcuts()` method, the favorites shortcuts (Return, Ctrl+Shift+O, Ctrl+Shift+E, and Delete) are now created on the Dock (the parent widget) rather than directly on the favorites widget. Their context is set to `Qt.WidgetWithChildrenShortcut` so that they fire when any child (or the favorites widget itself) is focused.

2. **Handler Methods Check Favorites Focus:**  
   Each favorites shortcut’s handler (e.g. `handle_fav_run()`, `handle_fav_open()`, etc.) first checks that the Favorites widget currently has focus before calling the corresponding helper method (like `run_selected_favorites()`). This ensures the keys work only when you’re actively in Favorites.

3. **No Interference with Scripts Widget:**  
   Since the shortcuts are attached to the Dock and check which widget is focused, the Scripts widget shortcuts remain unaffected.

Please test the Favorites widget now—click inside the Favorites list so it has focus, then press Return, Ctrl+Shift+O, Ctrl+Shift+E, and Delete. They should now work as expected.

If you have any further issues, please let me know!
