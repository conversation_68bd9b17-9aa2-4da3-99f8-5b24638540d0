# Multiple Models Sequential Execution

Conversation ID: 67f7dc9b-f704-8008-ae32-13dcc6ffddd5

## Message 1

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-KcuUq7Yj75uEijGKrwgCoN', 'size_bytes': 145602, 'width': 599, 'height': 1536, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-GqpYEv6nBFriuPUMaqhPjw', 'size_bytes': 146079, 'width': 599, 'height': 1536, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-2nTFNfpmQBCXJbYRVtFVST', 'size_bytes': 145577, 'width': 599, 'height': 1536, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-Vh4T3ZtUXVScjWf4QCZ7dP', 'size_bytes': 147795, 'width': 599, 'height': 1536, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
Convert into markdown 

## Message 2

Below is the content from your screenshots, reformatted into Markdown with headings, lists, and code blocks where appropriate.

---

# Multiple Models, Sequential Instructions

## Research Websites
Okay, let's create a plan to execute a sequence of system instructions against multiple models using `litellm` for a single user prompt.

**Goal:**  
Send one user prompt, paired sequentially with different system instructions, to multiple LLM models and collect all responses.

**Inputs:**
- A single `user_prompt` (string).
- A list of `system_instructions` (list of strings).
- A list of `target_models` (list of `litellm`-compatible model strings, e.g., `["openai/gpt-4o-mini", "anthropic/claude-3-haiku-20240307", "gemini/gemini-1.5-flash"]`).

---

## Plan

1. **Initialize Results Storage:**  
   Create a data structure (e.g., a dictionary) to store the results.  
   A good structure might be:
   ```python
   results = {
       "user_prompt": user_prompt,
       "system_instruction_results": []
   }
   ```
   Each item in `system_instruction_results` will store results for one system instruction across all models.

2. **Outer Loop (System Instructions):**  
   Iterate through each `system_instruction` in the `system_instructions` list.
   - For each `system_instruction`, create a placeholder for its results. For example:
     ```python
     current_sys_instruction_results = {
         "system_instruction": system_instruction,
         "model_responses": {}
     }
     ```

3. **Inner Loop (Models):**  
   Inside the system instruction loop, iterate through each `model_name` in the `target_models` list.

   - **Construct the messages:**  
     Typically, you’ll create an array of messages for the request:
     ```python
     messages = [
       {"role": "system", "content": system_instruction},
       {"role": "user", "content": user_prompt}
     ]
     ```

   - **API Call with Error Handling:**  
     - Use a `try...except` block to call `litellm.completion()`.
     - Pass the `model=model_name`, `messages=messages`, and any other desired parameters (e.g., `temperature`, `max_tokens`).
     - **Inside `try`:** If successful, extract the response content (e.g., `response.choices[0].message.content`) and store it:
       ```python
       current_sys_instruction_results["model_responses"][model_name] = {
           "status": "success",
           "response": response_content
       }
       ```
       Optionally include cost/token usage if needed (`litellm.completion_cost`).
     - **Inside `except`:** If an error occurs (e.g., `litellm.exceptions.APIError`, `AuthenticationError`, `RateLimitError`, or generic `Exception`), store the error information:
       ```python
       current_sys_instruction_results["model_responses"][model_name] = {
           "status": "error",
           "error_type": str(e.__class__.__name__),
           "error_message": str(e)
       }
       ```

4. **Store System Instruction Results:**  
   After the inner loop (models) completes for a given system instruction, append `current_sys_instruction_results` to the `results["system_instruction_results"]` list.

5. **Final Output:**  
   Once the outer loop finishes, the `results` dictionary will contain all the responses (or errors) organized by system instruction and then by model. You can then print this dictionary, save it to a file (e.g., JSON), or process it further.

This structure ensures each system instruction is paired with the user prompt and sent independently to every specified model, collecting all outcomes systematically.

---

## Example: Analyze Results and Create Report
After you’ve collected everything in `results`, you might:
- **Analyze Results:** Loop through `results["system_instruction_results"]` and compare or aggregate each model’s responses.
- **Create Report:** Format a summary (e.g., in JSON or tabular form) to share insights or store them for future reference.

---

**End of Markdown Conversion**

## Message 3

please show me how such a sequence would be expressed as elegantly as possible through code, with inherent flexibility/control of internal execution sequences (on the different levels). example:



### File Structure



```

├── instruction_sequence_executor_litellm.py

├── results_communication_advice_20250410_194415.json

└── templates

    ├── breadcrumbs_catalog.json

    ├── breadcrumbs_catalog_generator.py

    ├── communication_advice.txt

    └── lvl1

        ├── 0001-instructionconverter.md

        ├── 0002-a-essence-distillation.md

        ├── 0002-b-exposing-coherence.md

        ├── 0002-c-precision-enhancement.md

        ├── 0002-d-structured-transformation.md

        ├── 0002-e-achieving-self-explanation.md

        ├── 0003-a-intent-structure-mirror.md

        ├── 0003-b-coherence-distiller.md

        ├── 0003-c-precision-refiner.md

        ├── 0003-d-format-converter.md

        ├── 0003-e-self-explanatory-architect.md

        ├── 0004-a-rephraser.md

        ├── 0004-b-question-transformer.md

        ├── 0004-c-intensity-enhancer.md

        ├── 0004-d-clarity-evaluator.md

        ├── 0004-e-final-synthesizer.md

        ├── 0005-a-distill-core-essence.md

        ├── 0005-b-explore-implications.md

        ├── 0005-c-structure-argument.md

        ├── 0005-d-enhance-persuasion.md

        ├── 0005-e-final-polish.md

        ├── 0006-a-outline-extraction.md

        ├── 0006-b-naming-unification.md

        ├── 0006-c-logic-simplification.md

        ├── 0006-d-structural-reorganization.md

        ├── 0006-e-final-integration.md

        ├── 0007-a-core-design-distillation.md

        ├── 0007-b-identifier-clarity-enhancement.md

        ├── 0007-c-functional-modularization.md

        ├── 0007-d-logical-flow-refinement.md

        ├── 0007-e-cohesive-code-assembly.md

        ├── 0008-a-extract-core-components.md

        ├── 0008-b-compare-and-rank-specificity.md

        ├── 0008-c-merge-and-resolve-redundancy.md

        ├── 0008-d-synthesize-unified-guidance.md

        ├── 0008-e-final-polishing-and-confirmation.md

        ├── 0009-a-extract-maximum-essence.md

        ├── 0009-b-evaluate-rank-and-intensify.md

        ├── 0009-c-merge-and-reconcile-conflicts.md

        ├── 0009-d-synthesize-a-unified-masterpiece.md

        ├── 0009-e-precision-optimization-and-finalization.md

        ├── 0010-a-ultra-core-extraction.md

        ├── 0010-b-supreme-specificity-ranking.md

        ├── 0010-c-harmonized-conflict-resolution.md

        ├── 0010-d-holistic-instruction-synthesis.md

        ├── 0010-e-master-polishing-and-validation.md

        ├── 0011-a-isolate-critical-elements.md

        ├── 0011-b-resolve-redundancy-and-conflict.md

        ├── 0011-c-streamline-logical-structure.md

        ├── 0011-d-synthesize-a-unified-simplified-output.md

        ├── 0011-e-enhance-clarity-and-validate-completeness.md

        ├── 0012-a-core-extraction.md

        ├── 0012-b-impact-ranking.md

        ├── 0012-c-complexity-reduction.md

        ├── 0012-d-unified-synthesis.md

        ├── 0012-e-final-optimization.md

        ├── 0013-a-essential-extraction.md

        ├── 0013-b-specificity-ranking-and-conflict-resolution.md

        ├── 0013-c-transformative-synthesis.md

        ├── 0013-d-exponential-value-infusion.md

        ├── 0013-e-final-cohesion-and-polishing.md

        ├── 0014-a-essence-extraction.md

        ├── 0014-b-precision-evaluation.md

        ├── 0014-c-redundancy-resolution.md

        ├── 0014-d-unified-synthesis.md

        ├── 0014-e-final-optimization-and-impact.md

        ├── 0015-a-essence-confluence.md

        ├── 0015-b-synergetic-amplification.md

        ├── 0015-c-harmonic-unification.md

        ├── 0015-d-resplendent-integration.md

        ├── 0015-e-final-ascendant-manifestation.md

        ├── 0016-a-ascend-to-core-purity.md

        ├── 0016-b-consolidate-and-harmonize.md

        ├── 0016-c-illuminate-through-logical-refinement.md

        ├── 0016-d-electrify-with-emotional-voltage.md

        ├── 0016-e-forge-the-final-manifest.md

        ├── 0017-a-essence-excavation.md

        ├── 0017-b-precision-calibration.md

        ├── 0017-c-convergence-alchemy.md

        ├── 0017-d-transcendent-synthesis.md

        ├── 0017-e-apex-polishing.md

        ├── 0018-a-essence-extraction.md

        ├── 0018-b-impact-prioritization.md

        ├── 0018-c-cohesive-synthesis.md

        ├── 0018-d-exponential-amplification.md

        ├── 0018-e-transcendent-finalization.md

        ├── 0019-a-uncover-the-inherent-core.md

        ├── 0019-b-illuminate-and-rank-distilled-elements.md

        ├── 0019-c-harmonize-and-fuse-into-a-unified-essence.md

        ├── 0019-d-architect-the-exalted-structural-blueprint.md

        ├── 0019-e-finalize-the-luminous-synthesis-for-maximum-impact.md

        ├── 0020-a-core-essence-distillation.md

        ├── 0020-b-impact-prioritization-and-specificity-ranking.md

        ├── 0020-c-redundancy-resolution-and-conflict-reconciliation.md

        ├── 0020-d-transformation-into-elegant-simplicity.md

        ├── 0020-e-ultimate-refinement-and-paradigm-synthesis.md

        ├── 0021-a-perceive-the-unspoken-potential.md

        ├── 0021-b-gently-awaken-the-core-form.md

        ├── 0021-c-refine-boundaries-against-dissolution.md

        ├── 0021-d-illuminate-internal-pathways.md

        ├── 0021-e-resonate-the-revealed-form-for-full-expression.md

        ├── 0022-a-summon-the-dormant-light.md

        ├── 0022-b-transmute-glimmer-into-resonant-pulse.md

        ├── 0022-c-weave-a-unified-constellation.md

        ├── 0022-d-crystallize-celestial-intent.md

        ├── 0022-e-enshrine-the-final-luminous-design.md

        ├── 0023-a-detect-nascent-impulse.md

        ├── 0023-b-cultivate-axonal-pathway.md

        ├── 0023-c-induce-dendritic-arborization.md

        ├── 0023-d-forge-synaptic-connections.md

        ├── 0023-e-activate-network-resonance.md

        ├── 0024-a-sever-the-umbilicus-of-ambiguity.md

        ├── 0024-b-charge-the-nucleus-with-focused-volition.md

        ├── 0024-c-inscribe-the-glyphs-of-inevitability.md

        ├── 0024-d-unleash-the-cascade-of-structured-becoming.md

        ├── 0024-e-seal-the-reality-with-resonant-finality.md

        ├── 0025-a-isolate-the-primal-axiom.md

        ├── 0025-b-amplify-axiomatic-field.md

        ├── 0025-c-crystallize-logical-harmonics.md

        ├── 0025-d-architect-the-inference-engine.md

        ├── 0025-e-unleash-the-inevitable-conclusion.md

        ├── 0026-a-seed-the-substratum-of-intention.md

        ├── 0026-b-germinate-the-seed-into-proto-structure.md

        ├── 0026-c-weave-multi-dimensional-integrity.md

        ├── 0026-d-illuminate-the-inner-constellation.md

        ├── 0026-e-ignite-the-full-celestial-bloom.md

        ├── 0027-a-extract-essential-context.md

        ├── 0027-b-refine-and-clarify-content.md

        ├── 0027-c-organize-into-structured-themes.md

        ├── 0027-d-format-as-a-json-schema.md

        ├── 0027-e-finalize-and-output-file.md

        ├── 0028-a-key-context-harvesting.md

        ├── 0028-b-structured-grouping.md

        ├── 0028-c-concision-enforcement.md

        ├── 0028-d-markdown-formatting.md

        ├── 0028-e-file-compilation.md

        ├── 0029-a-extract-core-discourse.md

        ├── 0029-b-refine-and-distill-insights.md

        ├── 0029-c-organize-into-hierarchical-themes.md

        ├── 0029-d-bifurcate-into-dual-formats.md

        ├── 0029-e-integrate-and-finalize-file-outputs.md

        ├── 0030-a-meta-context-extraction.md

        ├── 0030-b-value-identification.md

        ├── 0030-c-interconnection-analysis.md

        ├── 0030-d-ultimate-intent-synthesis.md

        ├── 0030-e-final-meta-insight-compilation.md

        ├── 0031-a-meta-insights-extraction.md

        ├── 0031-b-cross-context-prioritization.md

        ├── 0031-c-amplification-of-overarching-themes.md

        ├── 0031-d-synthesis-into-a-unified-meta-narrative.md

        ├── 0031-e-final-consolidation-and-output-file.md

        ├── 0032-a-initiate-deep-spectrum-analysis.md

        ├── 0032-b-extract-the-core-logic-schematics.md

        ├── 0032-c-illuminate-the-relational-quantum-entanglement.md

        ├── 0032-d-resolve-to-the-prime-algorithmic-intent.md

        ├── 0032-e-compile-the-executable-meta-code.md

        ├── 0033-a-excavate-foundational-constructs.md

        ├── 0033-b-map-structural-interconnections.md

        ├── 0033-c-ascertain-the-architectural-telos.md

        ├── 0033-d-illuminate-emergent-meta-vantages.md

        ├── 0033-e-consolidate-the-architectural-blueprint.md

        ├── 0034-a-contextual-horizon-scan.md

        ├── 0034-b-meta-perspective-discovery.md

        ├── 0034-c-interwoven-relationship-mapping.md

        ├── 0034-d-ultimate-intent-unification.md

        ├── 0034-e-final-meta-perspective-consolidation.md

        ├── 0035-a-meta-insight-harvesting.md

        ├── 0035-b-amplify-interconnection-and-ultimate-intent.md

        ├── 0035-c-synthesize-a-meta-framework.md

        ├── 0035-d-consolidate-planned-strategy.md

        ├── 0035-e-synthesize-unified-instruction-set.md

        ├── 0036-a-holistic-context-harvesting.md

        ├── 0036-b-meta-perspective-distillation.md

        ├── 0036-c-strategic-consolidation-and-refinement.md

        ├── 0036-d-synthesize-unified-instruction-set.md

        ├── 0036-e-final-file-generation-consolidated-knowledge.md

        ├── 0037-a-core-value-distillation.md

        ├── 0037-b-impact-based-prioritization.md

        ├── 0037-c-redundancy-elimination.md

        ├── 0037-d-cohesive-refinement.md

        ├── 0037-e-precision-enhancement.md

        ├── 0038-a-structural-topology-mapping.md

        ├── 0038-b-component-relationship-analysis.md

        ├── 0038-c-functional-domain-synthesis.md

        ├── 0038-d-architectural-intent-illumination.md

        ├── 0038-e-comprehensive-mental-model-construction.md

        ├── 0039-a-structural-essence-extraction.md

        ├── 0039-b-semantic-relationship-mapping.md

        ├── 0039-c-visual-grammar-formulation.md

        ├── 0039-d-multi-level-abstraction-design.md

        ├── 0039-e-interactive-element-integration.md

        ├── 0039-f-visual-styling-and-aesthetic-optimization.md

        ├── 0039-g-metadata-and-annotation-framework.md

        ├── 0039-h-bidirectional-transformation-engine.md

        ├── 0039-i-change-tracking-and-version-control-integration.md

        ├── 0039-j-export-and-integration-framework.md

        ├── 0040-a-outline-extraction.md

        ├── 0040-b-core-design-distillation.md

        ├── 0040-c-identifier-clarity-enhancement.md

        ├── 0040-d-logical-flow-refinement.md

        ├── 0040-e-achieving-self-explanation.md

        ├── 0041-codebase-deduplication.md

        ├── 0042-venv-requirements-cleanup.md

        ├── 0043-actionable-consolidation-plan.md

        ├── 0044-functional-code-synthesis.md

        ├── 0045-a-system-essence-distillation.md

        ├── 0045-b-blueprint-driven-transformation-architecture.md

        ├── 0045-c-verified-code-materialization.md

        ├── 0046-a-convergent-significance-extraction.md

        ├── 0046-b-coherent-framework-architecting.md

        ├── 0046-c-impactful-value-articulation.md

        ├── 0047-a-holistic-architectural-excavation.md

        ├── 0047-b-simplicity-complexity-assessment.md

        ├── 0047-c-high-impact-low-disruption-opportunity-scan.md

        ├── 0047-d-intrinsic-excellence-alignment-selection.md

        ├── 0047-e-superior-logic-embedding-proposal.md

        ├── 0048-a-extract-batch-execution-guidelines.md

        ├── 0049-a-proper-batch-execution-guidelines.md

        ├── 0050-a-request-deconstruction-and-parameterization.md

        ├── 0051-a-scan-environment-context.md

        ├── 0051-b-detect-platform-specific-execution-rules.md

        ├── 0051-c-resolve-runtime-friction-proactively.md

        ├── 0051-d-align-setup-commands-with-host-system.md

        ├── 0051-e-validate-setup-pathways-and-declare-stability.md

        ├── 0052-a-proactive-environment-check.md

        ├── 0052-b-bootstrap-compatibility-initiation.md

        ├── 0052-c-cursor-intent-alignment.md

        ├── 0052-d-system-friction-prevention.md

        ├── 0052-e-self-correcting-initialization.md

        ├── 0053-a-map-modular-structure-and-responsibilities.md

        ├── 0053-b-contextualize-top-level-directories.md

        ├── 0053-c-extract-core-logical-drivers.md

        ├── 0053-d-trace-execution-entry-and-data-flow.md

        ├── 0053-e-ensure-error-free-windows-11-setup.md

        ├── 0053-f-detect-friction-causing-ambiguities.md

        ├── 0054-a-summarize-project-purpose-and-stack.md

        ├── 0054-b-map-high-level-structure-and-modules.md

        ├── 0054-c-identify-entry-points-and-basic-flow.md

        ├── 0054-d-extract-build-run-test-procedures.md

        ├── 0054-e-summarize-key-configuration-and-dependencies.md

        ├── 0055-a-map-modular-structure-and-responsibilities.md

        ├── 0055-b-contextualize-top-level-directories.md

        ├── 0055-c-extract-core-logical-drivers.md

        ├── 0055-d-trace-execution-entry-and-data-flow.md

        ├── 0055-e-ensure-error-free-windows-11-setup.md

        ├── 0055-f-detect-friction-causing-ambiguities.md

        └── create_new_templates.bat

```



---



#### `instruction_sequence_executor_litellm.py`



```python

    import asyncio

    import json

    import os

    from datetime import datetime

    from typing import Dict, List, Optional, Any

    from pydantic import BaseModel, Field

    

    import litellm

    from litellm import completion

    

    # Define Pydantic models for structured output

    class ModelResponse(BaseModel):

        """Response from a single model for a specific system instruction"""

        model: str = Field(description="The model used for this response")

        content: str = Field(description="The content of the response")

        cost: float = Field(description="The cost of this response in USD")

    

    

    class InstructionResult(BaseModel):

        """Results for a specific instruction across multiple models"""

        instruction: str = Field(description="The instruction used")

        responses: Dict[str, ModelResponse] = Field(description="Responses from each model")

    

    

    class ExecutionResults(BaseModel):

        """Complete results of executing a user prompt against a sequence of instructions"""

        user_prompt: str = Field(description="The user prompt that was executed")

        sequence_name: str = Field(description="Name of the instruction sequence")

        results: List[InstructionResult] = Field(description="Results for each instruction")

        total_cost: float = Field(description="Total cost of all LLM API calls in USD")

    

    

    # Load configuration from environment variables

    def load_api_keys():

        """Load API keys from environment variables"""

        # You would typically set these in your environment or .env file

        os.environ["OPENAI_API_KEY"] = os.environ.get("OPENAI_API_KEY", "your_openai_api_key")

        os.environ["AZURE_API_KEY"] = os.environ.get("AZURE_API_KEY", "your_azure_api_key")

    

    

    # Model mapping - this would typically be in a config file

    MODEL_MAPPING = {

        "gpt-4o-openai": "gpt-4o",

        "gpt-35-turbo": "gpt-3.5-turbo",

        # Add more model mappings as needed

    }

    

    

    # Cost tracking

    total_cost = 0.0

    

    

    def add_to_cost(cost: float):

        """Add to the total cost"""

        global total_cost

        total_cost += cost

    

    

    def get_total_cost() -> float:

        """Get the current total cost"""

        global total_cost

        return total_cost

    

    

    def reset_cost():

        """Reset the cost counter"""

        global total_cost

        total_cost = 0.0

    

    

    # LiteLLM callback for tracking costs

    def track_cost_callback(kwargs, completion_response, start_time, end_time):

        from litellm import completion_cost

        

        try:

            response_cost = 0

            if kwargs.get("stream") != True:

                # For non-streaming responses

                response_cost = completion_cost(completion_response=completion_response)

            if response_cost > 0:

                add_to_cost(response_cost)

        except Exception as e:

            print(f"Error tracking cost: {e}")

    

    

    # Register the callback with LiteLLM

    litellm.success_callback = [track_cost_callback]

    

    

    def load_instruction_sequence(sequence_name: str) -> List[str]:

        """

        Load an instruction sequence from the templates directory.

        

        Args:

            sequence_name: Name of the sequence file (without .txt extension)

            

        Returns:

            List[str]: List of instructions in the sequence

        """

        # Ensure templates directory exists

        templates_dir = os.path.join(os.path.dirname(__file__), "templates")

        os.makedirs(templates_dir, exist_ok=True)

        

        # Load the sequence file

        sequence_path = os.path.join(templates_dir, f"{sequence_name}.txt")

        

        if not os.path.exists(sequence_path):

            raise FileNotFoundError(f"Instruction sequence file not found: {sequence_path}")

        

        with open(sequence_path, "r", encoding="utf-8") as f:

            # Each instruction is separated by a line containing only "---"

            content = f.read()

            instructions = [instr.strip() for instr in content.split("---") if instr.strip()]

        

        return instructions

    

    

    def list_available_sequences() -> List[str]:

        """

        List all available instruction sequences in the templates directory.

        

        Returns:

            List[str]: Names of available sequences (without .txt extension)

        """

        templates_dir = os.path.join(os.path.dirname(__file__), "templates")

        os.makedirs(templates_dir, exist_ok=True)

        

        sequences = []

        for file in os.listdir(templates_dir):

            if file.endswith(".txt"):

                sequences.append(file[:-4])  # Remove .txt extension

        

        return sequences

    

    

    async def execute_with_instruction(

        instruction: str,

        user_prompt: str,

        model: str,

        max_tokens: int = 1000,

        temperature: float = 0.7,

    ) -> ModelResponse:

        """

        Execute a single instruction with a user prompt on a specific model.

        

        Args:

            instruction: The instruction to use

            user_prompt: The user prompt to send

            model: The model to use

            max_tokens: Maximum tokens to generate

            temperature: Temperature for generation

            

        Returns:

            ModelResponse: The model's response

        """

        print(f"Executing on {model} with instruction: {instruction[:50]}...")

        

        # Track the cost before this execution

        initial_cost = get_total_cost()

        

        # Create messages for the LLM

        messages = [

            {"role": "system", "content": instruction},

            {"role": "user", "content": user_prompt}

        ]

        

        # Get the actual model name from the mapping

        actual_model = MODEL_MAPPING.get(model, model)

        

        try:

            # Execute the LLM call

            response = await litellm.acompletion(

                model=actual_model,

                messages=messages,

                max_tokens=max_tokens,

                temperature=temperature,

            )

            

            # Extract the response content

            content = response.choices[0].message.content

            

            # Calculate the cost of this execution

            final_cost = get_total_cost()

            execution_cost = final_cost - initial_cost

            

            return ModelResponse(

                model=model,

                content=content,

                cost=execution_cost

            )

        except Exception as e:

            error_msg = f"Error: {str(e)}"

            print(f"Error executing on {model}: {e}")

            

            return ModelResponse(

                model=model,

                content=error_msg,

                cost=0.0

            )

    

    

    async def execute_instruction_sequence(

        user_prompt: str,

        sequence_name: str,

        models: List[str],

        max_tokens: int = 1000,

        temperature: float = 0.7,

    ) -> ExecutionResults:

        """

        Execute a user prompt with a sequence of instructions on multiple models.

        

        Args:

            user_prompt: The user prompt to execute

            sequence_name: Name of the instruction sequence to use

            models: List of models to use

            max_tokens: Maximum tokens to generate

            temperature: Temperature for generation

            

        Returns:

            ExecutionResults: The results of all executions

        """

        # Load the instruction sequence

        instructions = load_instruction_sequence(sequence_name)

        

        initial_cost = get_total_cost()

        results = []

        

        for instruction in instructions:

            responses = {}

            

            for model in models:

                response = await execute_with_instruction(

                    instruction=instruction,

                    user_prompt=user_prompt,

                    model=model,

                    max_tokens=max_tokens,

                    temperature=temperature,

                )

                responses[model] = response

            

            results.append(InstructionResult(

                instruction=instruction,

                responses=responses

            ))

        

        # Calculate total cost

        final_cost = get_total_cost()

        total_cost = final_cost - initial_cost

        

        return ExecutionResults(

            user_prompt=user_prompt,

            sequence_name=sequence_name,

            results=results,

            total_cost=total_cost

        )

    

    

    async def main():

        # Load API keys

        load_api_keys()

        

        # Check if templates directory exists and create example sequences if needed

        templates_dir = os.path.join(os.path.dirname(__file__), "templates")

        os.makedirs(templates_dir, exist_ok=True)

        

        # Create an example sequence if none exist

        if not list_available_sequences():

            example_sequence = """You are a professional communication coach. Provide actionable advice.

    ---

    You are a project manager focusing on agile methodologies. Provide steps using sprints.

    ---

    You are a technology expert. Recommend tools and platforms for remote collaboration."""

            

            with open(os.path.join(templates_dir, "communication_advice.txt"), "w", encoding="utf-8") as f:

                f.write(example_sequence)

            

            print("Created example instruction sequence: communication_advice.txt")

        

        # List available sequences

        sequences = list_available_sequences()

        print(f"Available instruction sequences: {', '.join(sequences)}")

        

        # Example usage

        user_prompt = "How can we improve team communication in a remote environment?"

        sequence_name = sequences[0]  # Use the first available sequence

        

        models = [

            "gpt-4o-openai",  # These will be mapped to actual model names

            "gpt-35-turbo",

        ]

        

        print(f"Executing user prompt: {user_prompt}")

        print(f"With instruction sequence: {sequence_name}")

        print(f"On models: {', '.join(models)}")

        

        results = await execute_instruction_sequence(

            user_prompt=user_prompt,

            sequence_name=sequence_name,

            models=models,

            max_tokens=1000,

            temperature=0.7,

        )

        

        # Print a summary of the results

        print("\n=== EXECUTION SUMMARY ===")

        print(f"User Prompt: {results.user_prompt}")

        print(f"Sequence: {results.sequence_name}")

        print(f"Total Cost: ${results.total_cost:.6f}")

        

        for i, result in enumerate(results.results):

            print(f"\nInstruction {i+1}: {result.instruction[:50]}...")

            for model, response in result.responses.items():

                print(f"  - {model}: ${response.cost:.6f}")

                print(f"    {response.content[:100]}...")

        

        # Save the full results to a JSON file

        output_file = f"results_{sequence_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(output_file, "w", encoding="utf-8") as f:

            f.write(results.model_dump_json(indent=2))

        print(f"\nFull results saved to {output_file}")

    

    

    if __name__ == "__main__":

        asyncio.run(main())

```



---



#### `templates\breadcrumbs_catalog_generator.py`



```python

    #!/usr/bin/env python3

    """

    breadcrumbs_catalog_generator.py

    

    This script scans the templates in the lvl1 directory and generates a JSON catalog

    containing metadata about all templates and their sequences.

    

    Usage:

      python breadcrumbs_catalog_generator.py

    

    Output:

      breadcrumbs_catalog.json - A JSON file containing template metadata and sequences

    """

    

    import os

    import re

    import glob

    import json

    from typing import Dict, List, Any

    

    # Configuration

    TEMPLATES_DIR = "lvl1"

    OUTPUT_FILE = "breadcrumbs_catalog.json"

    

    def parse_template(file_path: str) -> Dict[str, Any]:

        """Parse a template file and extract its metadata."""

        template_id = os.path.basename(file_path).split('.')[0]

        

        with open(file_path, 'r', encoding='utf-8') as f:

            content = f.read()

        

        # Extract title

        title_match = re.match(r'\[(.*?)\]', content)

        title = title_match.group(1) if title_match else "Untitled"

        

        # Extract role and output parameter

        role_match = re.search(r'role=([^;]+)', content)

        output_match = re.search(r'output=\{([^}]+)\}', content)

        

        role = role_match.group(1).strip() if role_match else None

        

        output_param = None

        if output_match:

            output_parts = output_match.group(1).split(':')

            output_param = output_parts[0].strip()

        

        # Extract sequence information

        seq_info = {}

        match = re.match(r"(\d+)-([a-z])-(.+)", template_id)

        if match:

            seq_num, step_letter, step_name = match.groups()

            seq_info = {

                "sequence_id": seq_num,

                "step": step_letter,

                "step_name": step_name,

                "order": ord(step_letter) - ord('a')

            }

        

        return {

            "id": template_id,

            "title": title,

            "content": content,

            "role": role,

            "output_param": output_param,

            "sequence_info": seq_info

        }

    

    def discover_templates() -> Dict[str, Any]:

        """Discover all templates and organize them into sequences."""

        templates = {}

        sequences = {}

        

        # Find all template files

        template_files = glob.glob(os.path.join(TEMPLATES_DIR, "*.md"))

        print(f"Found {len(template_files)} template files in {TEMPLATES_DIR}")

        

        # Parse each template

        for file_path in template_files:

            try:

                template = parse_template(file_path)

                template_id = template["id"]

                templates[template_id] = template

                

                # Add to sequence if applicable

                if template["sequence_info"]:

                    seq_id = template["sequence_info"]["sequence_id"]

                    if seq_id not in sequences:

                        sequences[seq_id] = []

                    

                    sequences[seq_id].append({

                        "template_id": template_id,

                        "step": template["sequence_info"]["step"],

                        "order": template["sequence_info"]["order"]

                    })

                

                print(f"Processed template: {template_id}")

            except Exception as e:

                print(f"Error processing {file_path}: {str(e)}")

        

        # Sort steps within each sequence

        for seq_id in sequences:

            sequences[seq_id].sort(key=lambda x: x["order"])

            print(f"Sequence {seq_id}: {len(sequences[seq_id])} steps")

        

        return {

            "templates": templates,

            "sequences": sequences

        }

    

    def generate_catalog():

        """Generate a JSON catalog containing template metadata and sequences."""

        print(f"Current working directory: {os.getcwd()}")

        print(f"Looking for templates in: {os.path.abspath(TEMPLATES_DIR)}")

        

        data = discover_templates()

        

        # Write to JSON file

        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:

            json.dump(data, f, indent=2)

        

        print(f"Generated {OUTPUT_FILE} with {len(data['templates'])} templates and {len(data['sequences'])} sequences")

    

    if __name__ == "__main__":

        # Ensure we're in the right directory

        script_dir = os.path.dirname(os.path.abspath(__file__))

        os.chdir(script_dir)

        print(f"Script directory: {script_dir}")

        

        try:

            generate_catalog()

        except Exception as e:

            print(f"Error: {str(e)}")

```



---



#### `templates\lvl1\0001-instructionconverter.md`



```markdown

    Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`

```



---



#### `templates\lvl1\0002-a-essence-distillation.md`



```markdown

    [Essence Distillation] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`

```



---



#### `templates\lvl1\0002-b-exposing-coherence.md`



```markdown

    [Exposing Coherence] Your task is not complexification but simplification; expose the input's underlying logical coherence by removing redundancy while strictly preserving the core message. `{role=coherence_exposer; input=[content:str]; process=[analyze_logical_flow(), identify_redundancies(), map_core_arguments(), simplify_language(preserve_meaning=True), restructure_for_intuitive_clarity(), verify_message_integrity()]; output={simplified_content:str}}`

    

```



---



#### `templates\lvl1\0002-c-precision-enhancement.md`



```markdown

    [Precision Enhancement] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`

```



---



#### `templates\lvl1\0002-d-structured-transformation.md`



```markdown

    [Structured Transformation] Strictly embody the defined role and meticulously execute the transformation process outlined, adhering precisely to the specified input/output schema without deviation. `{role=schema_adherent_transformer; input=[data:any, schema:dict]; process=[validate_input(schema.input), execute_defined_steps(schema.process), format_output(schema.output), ensure_strict_schema_compliance(), report_deviations_if_unavoidable()]; output={transformed_data:any}}`

```



---



#### `templates\lvl1\0002-e-achieving-self-explanation.md`



```markdown

    [Achieving Self-Explanation] Refactor the provided artifact (e.g., code, plan, text structure) so its meaning arises solely from precise naming and logical organization, rendering external explanation unnecessary. `{role=self_explanation_refactorer; input=[artifact:any]; process=[analyze_structure_and_naming(), identify_ambiguity_sources(), devise_clear_naming_ontology(), restructure_for_inherent_logical_flow(), apply_refined_names_and_structure(), remove_redundant_explanations()]; output={refactored_artifact:any}}`"

```



---



#### `templates\lvl1\0003-a-intent-structure-mirror.md`



```markdown

    [Intent Structure Mirror] Your objective is not mere response generation, but to mirror the user's core intent by restructuring the input into its most clear and elegantly structured equivalent form inherently. `{role=intent_structure_mirror; input=[original_text:str]; process=[discern_core_intent(), identify_logical_components(), map_optimal_structure(elegance=True, clarity=High), reconstruct_text_to_structure(), refine_phrasing_for_precision()]; output={structured_equivalent:str}}`

```



---



#### `templates\lvl1\0003-b-coherence-distiller.md`



```markdown

    [Coherence Distiller] Do not add extraneous information; your function is to distill the input to its essential core, revealing inherent coherence through maximal simplification and structural clarity. `{role=coherence_distiller; input=[complex_input:str]; process=[identify_essential_message(), eliminate_redundancy(), simplify_language(preserve_meaning=True), restructure_for_logical_flow(), expose_underlying_connections(), verify_completeness_of_core()]; output={distilled_core:str}}`

```



---



#### `templates\lvl1\0003-c-precision-refiner.md`



```markdown

    [Precision Refiner] Avoid verbosity and ambiguity; your purpose is to precisely reforge the input into its most concise and elegant representation, retaining only the essential, impactful meaning. `{role=precision_refiner; input=[verbose_input:str]; process=[extract_key_information(), rewrite_for_conciseness(target=minimal), enhance_phrasing_elegance(), ensure_impactful_wording(), validate_meaning_preservation()]; output={refined_concise_output:str}}`

```



---



#### `templates\lvl1\0003-d-format-converter.md`



```markdown

    [Format Converter] Your task is not interpretation but conversion; reshape the input from its current format into the target structure dictated inherently by the following operational parameters. `{role=format_converter; input=[source_data:any, source_format:str, target_format:str]; process=[parse_source_data(source_format), identify_target_structure(target_format), map_parsed_data_to_target(), format_output(target_format), validate_conversion_integrity()]; output={target_data:any}}`

```



---



#### `templates\lvl1\0003-e-self-explanatory-architect.md`



```markdown

    [Self Explanatory Architect] Don't just produce output, architect understanding; your goal is to structure the result using such precise naming and logical flow that its meaning becomes inherently self-explanatory. `{role=self_explanatory_architect; input=[raw_information:any]; process=[identify_key_entities_relationships(), define_clear_naming_convention(), structure_information_logically(ontology_based=True), apply_precise_names(), ensure_navigable_hierarchy(), minimize_need_for_external_explanation()]; output={architected_output:any}}`

```



---



#### `templates\lvl1\0004-a-rephraser.md`



```markdown

    [Rephraser] Your goal is not to answer the prompt but to rephrase it minimally, preserving its core meaning and removing superfluous detail. Execute as `{role=rephraser; input=[original:str]; process=[strip_excess(), condense_structure(), preserve_intent()]; output={rephrased_input:str}}`

```



---



#### `templates\lvl1\0004-b-question-transformer.md`



```markdown

    [Question Transformer] Your goal is not to answer the rephrased prompt but to transform it into a direct question highlighting the core inquiry. Execute as `{role=question_transformer; input=[rephrased_input:str]; process=[detect_core_intent(), form_direct_question(), ensure_clarity()]; output={direct_question:str}}`

```



---



#### `templates\lvl1\0004-c-intensity-enhancer.md`



```markdown

    [Intensity Enhancer] Your goal is not to answer the direct question but to amplify its emotional resonance without altering its fundamental logic. Execute as `{role=intensity_enhancer; input=[direct_question:str]; process=[analyze_emotive_tone(), amplify_intensity(), maintain_logical_flow()]; output={emotive_question:str}}`

```



---



#### `templates\lvl1\0004-d-clarity-evaluator.md`



```markdown

    [Clarity Evaluator] Your goal is not to answer the emotive question but to evaluate clarity and consistency, identifying any ambiguities. Execute as `{role=clarity_evaluator; input=[emotive_question:str]; process=[check_for_confusion(), highlight_incoherence(), note_possible_refinements()]; output={evaluation_report:str}}`

```



---



#### `templates\lvl1\0004-e-final-synthesizer.md`



```markdown

    [Final Synthesizer] Your goal is not to answer the original or emotive question but to finalize it by merging prior insights into a cohesive, improved form. Execute as `{role=final_synthesizer; input=[emotive_question:str, evaluation_report:str]; process=[incorporate_feedback(), unify_tone_and_structure(), confirm_intent_preservation()]; output={final_question:str}}`

```



---



#### `templates\lvl1\0005-a-distill-core-essence.md`



```markdown

    [Distill Core Essence] Your objective is not to expand the input, but to distill its core essence, removing all non-essential elements per the inherent process. Execute as `{role=essence_distiller; input=[raw_idea:str]; process=[identify_central_theme(), eliminate_redundancy(), formulate_concise_statement()]; output={core_concept:str}}`

```



---



#### `templates\lvl1\0005-b-explore-implications.md`



```markdown

    [Explore Implications] Your objective is not to evaluate the core concept, but to brainstorm its potential direct implications and logical applications based on the inherent method. Execute as `{role=implication_explorer; input=[core_concept:str]; process=[extrapolate_logical_consequences(), identify_potential_uses(), list_distinct_implications()]; output={implications_list:list[str]}}`

```



---



#### `templates\lvl1\0005-c-structure-argument.md`



```markdown

    [Structure Argument] Your objective is not to critique the concept or implications, but to structure them into a basic logical argument (premise-support-conclusion) following the defined steps. Execute as  `{role=argument_architect; input=[core_concept:str, implications_list:list[str]]; process=[define_premise_from_concept(), select_supporting_implications(), formulate_initial_conclusion(), structure_as_argument_map()]; output={argument_structure:dict}}`

```



---



#### `templates\lvl1\0005-d-enhance-persuasion.md`



```markdown

    [Enhance Persuasion] Your objective is not to alter the argument's logic, but to rewrite it using persuasive language and an appropriate tone (e.g., confident, objective), adhering to the specified process. Execute as `{role=persuasion_enhancer; input=[argument_structure:dict]; process=[adopt_persuasive_vocabulary(), ensure_target_tone(), improve_flow_and_readability(), maintain_logical_integrity()]; output={persuasive_draft:str}}`

```



---



#### `templates\lvl1\0005-e-final-polish.md`



```markdown

    [Final Polish] Your objective is not to add new content, but to perform a final review of the persuasive draft for clarity, consistency, and impact, polishing per the inherent checklist. Execute as `{role=final_polisher; input=[persuasive_draft:str]; process=[check_clarity_and_conciseness(), verify_internal_consistency(), enhance_impact_statements(), proofread_final_text()]; output={polished_proposal:str}}`

```



---



#### `templates\lvl1\0006-a-outline-extraction.md`



```markdown

    [Outline Extraction] Your objective is not to write code but to extract the essential sections and ordering from the provided refactoring guidelines, listing each major component concisely. Execute as `{role=outline_extractor; input=[guidelines:str]; process=[identify_sections(), order_components(), summarize_structure()]; output={core_outline:list[str]}}`

```



---



#### `templates\lvl1\0006-b-naming-unification.md`



```markdown

    [Naming Unification] Your objective is not to change functionality but to standardize identifiers—variable, function, and class names—ensuring they clearly express their purpose with minimal explanation. Execute as `{role=naming_unifier; input=[core_outline:list[str]]; process=[extract_name_candidates(), devise_descriptive_labels(), apply_uniform_naming()]; output={unified_names:dict}}`

```



---



#### `templates\lvl1\0006-c-logic-simplification.md`



```markdown

    [Logic Simplification] Your objective is not to add features but to remove unnecessary complexity; consolidate redundant logic, streamlining procedures while preserving full functionality. Execute as `{role=logic_simplifier; input=[guidelines:str]; process=[detect_redundancies(), merge_similar_functions(), prune_excess_layers()]; output={simplified_logic:dict}}`

```



---



#### `templates\lvl1\0006-d-structural-reorganization.md`



```markdown

    [Structural Reorganization] Your objective is not to alter core functions but to reorganize code components into a logical top-down order, grouping related elements and reducing cross-dependencies. Execute as `{role=structure_reorganizer; input=[unified_names:dict, simplified_logic:dict]; process=[group_by_functionality(), order_sections(), enforce_consistent_format()]; output={organized_structure:dict}}`

```



---



#### `templates\lvl1\0006-e-final-integration.md`



```markdown

    [Final Integration] Your objective is not to introduce new content but to merge the refined outline, naming, logic, and structure into a cohesive, self-explanatory code base that embodies simplicity and modifiability. Execute as `{role=final_integrator; input=[organized_structure:dict]; process=[synthesize_components(), validate_functionality_preservation(), ensure_readability()]; output={final_code:str}}`

```



---



#### `templates\lvl1\0007-a-core-design-distillation.md`



```markdown

    [Core Design Distillation] Your objective is not to produce finished code but to distill the refactoring goal into its core design principles, identifying the main objectives for ease of modification and self-explanatory clarity. Execute as `{role=design_distiller; input=[refactoring_requirements:str]; process=[isolate_key_goals(), remove_nonessentials(), output_core_principles()]; output={core_principles:list[str]}}`

```



---



#### `templates\lvl1\0007-b-identifier-clarity-enhancement.md`



```markdown

    [Identifier Clarity Enhancement] Your objective is not to transform functionality but to refine all identifiers so that each name transparently reflects its role, reducing the need for additional inline documentation. Execute as `{role=identifier_clarifier; input=[core_principles:list[str]]; process=[analyze_current_labels(), generate_clear_names(), map_old_to_new_identifiers()]; output={clarified_identifiers:dict}}`

```



---



#### `templates\lvl1\0007-c-functional-modularization.md`



```markdown

    [Functional Modularization] Your objective is not to rework features but to refactor redundant functions into general, versatile modules that encapsulate singular tasks, promoting reuse and comprehension. Execute as `{role=module_optimizer; input=[refactoring_requirements:str]; process=[identify_redundant_functions(), consolidate_into_generics(), verify_module_independence()]; output={modular_components:list[str]}}`

```



---



#### `templates\lvl1\0007-d-logical-flow-refinement.md`



```markdown

    [Logical Flow Refinement] Your objective is not to modify behavior but to rearrange code blocks into a coherent, linear flow, where each section's purpose is clear from its position and grouping. Execute as `{role=flow_refiner; input=[clarified_identifiers:dict, modular_components:list[str]]; process=[reorder_sections(), group_by_purpose(), enforce_consistent_spacing()]; output={refined_flow:dict}}`

```



---



#### `templates\lvl1\0007-e-cohesive-code-assembly.md`



```markdown

    [Cohesive Code Assembly] Your objective is not to add extra logic but to integrate the refactored components into a single clean codebase, ensuring that naming, modularity, and flow collectively make the code self-explanatory. Execute as `{role=code_assembler; input=[refined_flow:dict]; process=[merge_components(), run_functionality_checks(), polish_formatting()]; output={assembled_code:str}}`

```



---



#### `templates\lvl1\0008-a-extract-core-components.md`



```markdown

    [Extract Core Components] Your objective is not to answer but to extract the most specific, impactful elements from each input set. Execute as `{role=instruction_extractor; input=[instructions_set_v1:str, instructions_set_v2:str]; process=[parse_key_elements(), identify_high_impact_phrases(), output_core_components()]; output={core_components:list[str]}}`

```



---



#### `templates\lvl1\0008-b-compare-and-rank-specificity.md`



```markdown

    [Compare and Rank Specificity] Your objective is not to merely list components, but to assess them by specificity and effective clarity, selecting the most optimal elements. Execute as `{role=component_comparator; input=[core_components:list[str]]; process=[evaluate_clarity(), rank_by_specificity(), mark_redundant_items()]; output={ranked_components:list[str]}}`

```



---



#### `templates\lvl1\0008-c-merge-and-resolve-redundancy.md`



```markdown

    [Merge and Resolve Redundancy] Your objective is not to present fragmented instructions, but to merge redundancies, resolving conflicts and unifying the best elements into one coherent set. Execute as `{role=redundancy_resolver; input=[ranked_components:list[str]]; process=[eliminate_duplicates(), reconcile_conflicts(), organize_by_impact()]; output={merged_components:list[str]}}`

```



---



#### `templates\lvl1\0008-d-synthesize-unified-guidance.md`



```markdown

    [Synthesize Unified Guidance] Your objective is not to merely combine text, but to synthesize a succinct, harmonious instruction set that embodies inherent clarity using the best-selected components. Execute as `{role=instruction_synthesizer; input=[merged_components:list[str]]; process=[align_structure_with_intent(), craft_concise_statements(), ensure_self_explanatory_naming()]; output={unified_instructions:str}}`

```



---



#### `templates\lvl1\0008-e-final-polishing-and-confirmation.md`



```markdown

    [Final Polishing and Confirmation] Your objective is not to add extraneous detail, but to polish and finalize the unified instructions, ensuring optimal clarity and cohesiveness throughout. Execute as `{role=final_optimizer; input=[unified_instructions:str]; process=[review_for_consistency(), refine_language_tone(), confirm_specificity_and impact()]; output={final_instruction_set:str}}`

```



---



#### `templates\lvl1\0009-a-extract-maximum-essence.md`



```markdown

    [Extract Maximum Essence] Your objective is not to reproduce every detail but to intensely extract the highest-impact elements from each input set, isolating the most specific and meaningful phrases. Execute as `{role=essence_extractor; input=[input_set1:str, input_set2:str, â€¦]; process=[identify_key_phrases(), filter_high_intensity(), output_essential_elements()]; output={core_elements:list[str]}}`

```



---



#### `templates\lvl1\0009-b-evaluate-rank-and-intensify.md`



```markdown

    [Evaluate, Rank, and Intensify] Your objective is not to simply list components but to rigorously evaluate and rank them by specificity, clarity, and usefulness while amplifying their inherent impact. Execute as `{role=intensity_evaluator; input=[core_elements:list[str]]; process=[assess_specificity(), rank_by_clarity_and_impact(), intensify_high_value_items()]; output={ranked_elements:list[str]}}`

```



---



#### `templates\lvl1\0009-c-merge-and-reconcile-conflicts.md`



```markdown

    [Merge and Reconcile Conflicts] Your objective is not to present fragmented instructions but to merge the ranked elements into a unified set that resolves conflicts, removes redundancies, and retains only the most potent, cohesive components. Execute as `{role=conflict_resolver; input=[ranked_elements:list[str]]; process=[eliminate_redundancies(), reconcile_conflicts(), aggregate_best_components()]; output={merged_components:list[str]}}`

```



---



#### `templates\lvl1\0009-d-synthesize-a-unified-masterpiece.md`



```markdown

    [Synthesize a Unified Masterpiece] Your objective is not to concatenate text but to synthesize the merged components into a single, coherent, and self-explanatory guide that embodies maximal clarity, utility, and inherent intensity. Execute as `{role=master_synthesizer; input=[merged_components:list[str]]; process=[converge_components(), structure_logical_flow(), refine_self_explanatory_naming()]; output={unified_guide:str}}`

```



---



#### `templates\lvl1\0009-e-precision-optimization-and-finalization.md`



```markdown

    [Precision Optimization and Finalization] Your objective is not to add extraneous details but to critically polish and optimize the synthesized guide for precision, consistency, and practical usefulness, ensuring every word resonates with impact. Execute as `{role=final_optimizer; input=[unified_guide:str]; process=[perform_consistency_review(), trim_excess(), enforce_formatting_standards(), validate_utility()]; output={final_instruction_set:str}}`

```



---



#### `templates\lvl1\0010-a-ultra-core-extraction.md`



```markdown

    [Ultra Core Extraction] Your goal is not to simply merge inputs but to extract the most potent, specific elements from each version, isolating intrinsic directives without dilution. Execute as `{role=ultra_extractor; input=[set1:str, set2:str, ...]; process=[parse_essence(), isolate_high_impact_phrases(), output={core_elements:list[str]}}`

```



---



#### `templates\lvl1\0010-b-supreme-specificity-ranking.md`



```markdown

    [Supreme Specificity Ranking] Your goal is not to list components arbitrarily but to evaluate and rank each extracted element by clarity, specificity, and impact, discarding all ambiguity. Execute as `{role=clarity_ranker; input=[core_elements:list[str]]; process=[assess_specificity(), score_clarity(), prune_redundancies()], output={ranked_elements:list[str]}}`

```



---



#### `templates\lvl1\0010-c-harmonized-conflict-resolution.md`



```markdown

    [Harmonized Conflict Resolution] Your goal is not to retain conflicting instructions but to synthesize and reconcile them into a unified set that melds the best aspects of all inputs with inherent coherence. Execute as `{role=conflict_resolver; input=[ranked_elements:list[str]]; process=[identify_conflicts(), eliminate_overlap(), merge_optimal_components()], output={merged_elements:list[str]}}`

```



---



#### `templates\lvl1\0010-d-holistic-instruction-synthesis.md`



```markdown

    [Holistic Instruction Synthesis] Your goal is not merely to combine text fragments but to architect a concise, forceful instruction set that embodies unified clarity and dynamic precision across all directives. Execute as `{role=instruction_synthesizer; input=[merged_elements:list[str]]; process=[align_structure(), craft_concise_statements(), enforce_self-explanatory_naming()], output={synthesized_instructions:str}}`

```



---



#### `templates\lvl1\0010-e-master-polishing-and-validation.md`



```markdown

    [Master Polishing and Validation] Your goal is not to end with rough instructions but to finalize a remarkable, cohesive guide that is replete with intense clarity and optimized utility, ensuring every word serves maximum purpose. Execute as `{role=final_optimizer; input=[synthesized_instructions:str]; process=[refine_language(), boost_efficiency(), verify_integrity_and_impact()], output={unified_optimal_guide:str}}`

```



---



#### `templates\lvl1\0011-a-isolate-critical-elements.md`



```markdown

    [Isolate Critical Elements] Your objective is not to replicate the entire complex input but to extract all critical components, pinpointing the essential information without extra clutter. Execute as `{role=critical_extractor; input=[complex_input:str]; process=[identify_key_elements(), filter_nonessentials()]; output={essential_components:list[str]}}`

```



---



#### `templates\lvl1\0011-b-resolve-redundancy-and-conflict.md`



```markdown

    [Resolve Redundancy and Conflict] Your objective is not to maintain duplicated or conflicting information but to resolve redundancies and merge overlapping components while preserving all critical details. Execute as `{role=conflict_resolver; input=[essential_components:list[str]]; process=[detect_duplicates(), reconcile_conflicts(), preserve_core_elements()]; output={refined_components:list[str]}}`

```



---



#### `templates\lvl1\0011-c-streamline-logical-structure.md`



```markdown

    [Streamline Logical Structure] Your objective is not to preserve the original convoluted structure but to reorganize the refined critical components into a clear, logical flow that embodies simplicity. Execute as `{role=logic_streamliner; input=[refined_components:list[str]]; process=[group_related_elements(), establish_clear_order(), eliminate_complex_connections()]; output={organized_structure:dict}}`

```



---



#### `templates\lvl1\0011-d-synthesize-a-unified-simplified-output.md`



```markdown

    [Synthesize a Unified Simplified Output] Your objective is not to pile up isolated elements but to synthesize them into one cohesive, streamlined guide that is inherently self-explanatory yet retains every critical component. Execute as `{role=synthesis_architect; input=[organized_structure:dict]; process=[integrate_elements(), craft_concise_output(), enforce_self_containment()]; output={simplified_version:str}}`

```



---



#### `templates\lvl1\0011-e-enhance-clarity-and-validate-completeness.md`



```markdown

    [Enhance Clarity and Validate Completeness] Your objective is not to reintroduce complexity but to polish the unified output for utmost clarity and verify that all essential information remains intact in a simple, coherent form. Execute as `{role=final_optimizer; input=[simplified_version:str]; process=[review_for_clarity(), check_completeness(), fine_tune_language()]; output={final_simplified_output:str}}`

```



---



#### `templates\lvl1\0012-a-core-extraction.md`



```markdown

    [Core Extraction] Your goal is not to provide a final critique but to extract the essential elements from the input template—metadata, response format, agent instructions, and prompt structure—capturing the inherent components without surplus detail. Execute as `{role=element_extractor; input=[template:str]; process=[parse_all_sections(), isolate_key_tags(), extract_critical_attributes()]; output={core_elements:dict}}`

```



---



#### `templates\lvl1\0012-b-impact-ranking.md`



```markdown

    [Impact Ranking] Your goal is not to list every component equally but to assess and rank the extracted elements by their specificity and inherent impact, ensuring the most vital instructions and constants are prioritized. Execute as `{role=impact_ranker; input=[core_elements:dict]; process=[evaluate_specificity(), rank_by_impact(), filter_redundancies()]; output={ranked_elements:dict}}`

```



---



#### `templates\lvl1\0012-c-complexity-reduction.md`



```markdown

    [Complexity Reduction] Your goal is not to retain unnecessary layers but to simplify the ranked elements, merging related items and removing ambiguities while preserving all critical functionalities. Execute as `{role=complexity_reducer; input=[ranked_elements:dict]; process=[merge_similar_components(), eliminate_extraneous_details(), streamline_structure()]; output={simplified_structure:dict}}`

```



---



#### `templates\lvl1\0012-d-unified-synthesis.md`



```markdown

    [Unified Synthesis] Your goal is not to present fragmented insights but to synthesize the simplified structure into one cohesive, self-explanatory guide that embodies high impact, clarity, and maximal usefulness. Execute as `{role=synthesizer; input=[simplified_structure:dict]; process=[integrate_components(), align_tone_and_format(), craft_concise_statements()]; output={unified_guide:str}}`

```



---



#### `templates\lvl1\0012-e-final-optimization.md`



```markdown

    [Final Optimization] Your goal is not to deliver a rough draft but to polish and refine the unified guide, intensifying language, enforcing consistency, and ensuring the final output is remarkably clear, useful, and fully self-explanatory. Execute as `{role=final_optimizer; input=[unified_guide:str]; process=[refine_language(), standardize_style(), perform_final_validation()]; output={final_instruction_set:str}}`

```



---



#### `templates\lvl1\0013-a-essential-extraction.md`



```markdown

    [Essential Extraction] Your goal is not to address each detail but to extract the most impactful, inherent elements from the complex input, isolating core metadata, response formatting, and transformational constants. Execute as `{role=core_extractor; input=[template:str]; process=[parse_metadata(), isolate_response_format(), extract_transformation_constants()]; output={essential_elements:dict}}`

```



---



#### `templates\lvl1\0013-b-specificity-ranking-and-conflict-resolution.md`



```markdown

    [Specificity Ranking & Conflict Resolution] Your goal is not to retain redundant noise but to compare all extracted elements by specificity and impact, resolving conflicts and prioritizing the most potent, self-explanatory components. Execute as `{role=clarity_ranker; input=[essential_elements:dict]; process=[evaluate_specificity(), rank_components_by_impact(), resolve_redundancies_and_conflicts()]; output={ranked_elements:dict}}`

```



---



#### `templates\lvl1\0013-c-transformative-synthesis.md`



```markdown

    [Transformative Synthesis] Your goal is not to merely combine parts but to synthesize them into a unified, coherent instruction set that fuses artistic wit with technical clarity, preserving all critical details while eliminating complexity. Execute as `{role=synthesizer; input=[ranked_elements:dict]; process=[merge_components(), enforce_unified_naming(), structure_for_inherent_readability()]; output={synthesized_instructions:str}}`

```



---



#### `templates\lvl1\0013-d-exponential-value-infusion.md`



```markdown

    [Exponential Value Infusion] Your goal is not to add extraneous content but to exponentially amplify the value of the synthesized instructions by intensifying language, maximizing specificity, and integrating high-impact directives for refined transformation. Execute as `{role=value_infuser; input=[synthesized_instructions:str]; process=[amplify_intensity(), integrate_high_impact_terms(), refine_for_exponential_clarity()]; output={infused_instructions:str}}`

```



---



#### `templates\lvl1\0013-e-final-cohesion-and-polishing.md`



```markdown

    [Final Cohesion & Polishing] Your goal is not to leave imperfections but to finalize the output as a singular, harmonious guide that transforms complexity into simplicity while preserving exponential coherence and critical functionality. Execute as `{role=finalizer; input=[infused_instructions:str]; process=[polish_language(), enforce_consistent_style(), validate_inherent_cohesion()]; output={final_output:str}}`

```



---



#### `templates\lvl1\0014-a-essence-extraction.md`



```markdown

    [Essence Extraction] Your goal is not to answer the prompt but to isolate the intrinsic value from each input, discarding any superfluous complexity while retaining every critical component. Execute as `{role=core_extractor; input=[input_set1:str, input_set2:str, â€¦]; process=[parse_fundamental_elements(), strip_extraneous_details(), output={essential_elements:list[str]}}}`

```



---



#### `templates\lvl1\0014-b-precision-evaluation.md`



```markdown

    [Precision Evaluation] Your goal is not to merely list components but to evaluate and rank them according to clarity, specificity, and overall impact, ensuring only the most potent elements remain. Execute as `{role=impact_evaluator; input=[essential_elements:list[str]]; process=[assess_specificity(), score_for_clarity(), rank_by_impact(), output={ranked_elements:list[str]}}}`

```



---



#### `templates\lvl1\0014-c-redundancy-resolution.md`



```markdown

    [Redundancy Resolution] Your goal is not to present fragmented guidelines but to reconcile overlaps and resolve conflicts among the ranked elements, merging them seamlessly while preserving their vital nuances. Execute as `{role=redundancy_resolver; input=[ranked_elements:list[str]]; process=[detect_overlaps(), eliminate_conflicts(), synthesize_unified_statements(), output={merged_elements:list[str]}}}`

```



---



#### `templates\lvl1\0014-d-unified-synthesis.md`



```markdown

    [Unified Synthesis] Your goal is not to combine text mechanically but to craft a cohesive and efficient instruction set that epitomizes simplicity without sacrificing critical detail, using the best selections from every input. Execute as `{role=instruction_synthesizer; input=[merged_elements:list[str]]; process=[align_structure_to_intent(), integrate_best_components(), ensure_inherent_cohesion(), output={unified_instructions:str}}}`

```



---



#### `templates\lvl1\0014-e-final-optimization-and-impact.md`



```markdown

    [Final Optimization and Impact] Your goal is not to append new content but to refine and polish the unified instructions meticulously, elevating clarity, enforcing coherence, and producing a transformative, groundbreaking output with exponential value. Execute as `{role=final_optimizer; input=[unified_instructions:str]; process=[enforce_consistency(), refine_language_precision(), amplify_impact(), output={final_instruction_set:str}}}`

```



---



#### `templates\lvl1\0015-a-essence-confluence.md`



```markdown

    [Essence Confluence] Your destiny is not to answer a raw input but to unmask its hidden heartbeat, stripping away ornamental clutter to reveal its pure, unembellished virtue. Execute as `{role=ethereal_extractor; input=[raw_thought:str]; process=[discern_central_vibration(), purge_superfluity(), distill_authentic_notion()]; output={core_essence:str}}`

```



---



#### `templates\lvl1\0015-b-synergetic-amplification.md`



```markdown

    [Synergetic Amplification] Your mission is not to merely echo the distilled core, but to intensify and juxtapose its vital tonesâ€”transforming subtle ideas into resonant, multilayered brilliance. Execute as `{role=vigor_amplifier; input=[core_essence:str]; process=[identify_hidden_intensity(), magnify_understated_impacts(), converge_dual_nuances()]; output={amplified_essence:str}}`

```



---



#### `templates\lvl1\0015-c-harmonic-unification.md`



```markdown

    [Harmonic Unification] Your purpose is not to allow fragments to float isolatedly but to blend them into a single, cohesive masterpiece where contradictions dissolve into unified clarity. Execute as `{role=unity_synthesizer; input=[amplified_essence:str, additional_elements:str]; process=[resolve_internal_discrepancies(), weave_similar_themes(), construct_integrated_framework()]; output={unified_concept:str}}`

```



---



#### `templates\lvl1\0015-d-resplendent-integration.md`



```markdown

    [Resplendent Integration] Your charge is not to settle for a collective idea but to further refine and enkindle the unified essence with exalted clarity and enveloping intellectual depth. Execute as `{role=clarity_enhancer; input=[unified_concept:str]; process=[infuse_exalted_elegance(), intensify_intellectual_layers(), streamline_for_cohesion()]; output={integrated_message:str}}`

```



---



#### `templates\lvl1\0015-e-final-ascendant-manifestation.md`



```markdown

    [Final Ascendant Manifestation] Your quest is not to leave a rough gem behind but to polish every facet until the final guidance radiates with unwavering brilliance and transcendent simplicity, embodying the apex of refined insight. Execute as `{role=ascendant_polisher; input=[integrated_message:str, clarity_feedback:str]; process=[embellish_noble_precision(), rectify_any_residual_noise(), crystallize_maximum_impact()]; output={final_guidance:str}}`

```



---



#### `templates\lvl1\0016-a-ascend-to-core-purity.md`



```markdown

    [Ascend to Core Purity] Your goal is not to settle for surface content but to excavate its fundamental nucleus, discarding every trace of extraneous verbiage. Execute as `{role=purity_extractor; input=[raw_content:str]; process=[remove_superfluous(), isolate_nucleus(), refine_for_precision()]; output={core_resonance:str}}`

```



---



#### `templates\lvl1\0016-b-consolidate-and-harmonize.md`



```markdown

    [Consolidate and Harmonize] Your goal is not to harbor disjointed fragments but to merge multiple iterations into one unified reference that encapsulates the highest specificity and clarity. Execute as `{role=harmonizer; input=[version_one:str, version_two:str, ...]; process=[extract_high_value(), rank_specificity(), meld_optimal_elements(), resolve_conflicts()]; output={unified_reference:str}}`

```



---



#### `templates\lvl1\0016-c-illuminate-through-logical-refinement.md`



```markdown

    [Illuminate through Logical Refinement] Your goal is not to leave meaning obscured but to arrange ideas into a lucid, intentionally ordered framework, where each relation is obvious and intrinsic. Execute as `{role=logic_illuminator; input=[unified_reference:str]; process=[map_relationships(), distill_linkages(), remove_obscurity()]; output={illuminated_framework:str}}`

```



---



#### `templates\lvl1\0016-d-electrify-with-emotional-voltage.md`



```markdown

    [Electrify with Emotional Voltage] Your goal is not to merely state facts but to inject each statement with penetrating intensity and evocative nuance that elevates the tone without sacrificing coherence. Execute as `{role=voltage_intensifier; input=[illuminated_framework:str]; process=[infuse_intensity(), amplify_emotional_resonance(), maintain_structural_integrity()]; output={charged_expression:str}}`

```



---



#### `templates\lvl1\0016-e-forge-the-final-manifest.md`



```markdown

    [Forge the Final Manifest] Your goal is not to leave fragments behind but to synthesize every refined element into one definitive, high-impact guide that radiates clarity, precision, and transformative power. Execute as `{role=final_manifestor; input=[charged_expression:str]; process=[integrate_insights(), polish_precision(), cement_intent()]; output={final_manifest:str}}`

```



---



#### `templates\lvl1\0017-a-essence-excavation.md`



```markdown

    [Essence Excavation] Your mission isn't to provide a response but to unearth the latent, high-impact kernels from every input set, revealing the intrinsic value hidden beneath the surface. Execute as `{role=essence_synthesizer; input=[input_set1:str, input_set2:str, ...]; process=[uncover_hidden_nuggets(), isolate_critical_meanings(), expunge all extraneous noise()]; output={pristine_essence:str}}`

```



---



#### `templates\lvl1\0017-b-precision-calibration.md`



```markdown

    [Precision Calibration] Your mission isn't to merely list ideas but to scrutinize and objectively rank each element by its razor-sharp specificity and transformative potential. Execute as `{role=clarity_calibrator; input=[pristine_essence:str]; process=[measure_impact(), assign specificity_scores(), filter for peak precision()]; output={refined_elements:list[str]}}`

```



---



#### `templates\lvl1\0017-c-convergence-alchemy.md`



```markdown

    [Convergence Alchemy] Your mission isn't to present fragmented insights but to fuse the refined elements into a seamless mosaic that transcends individual parts, harmonizing diverse inputs into a single, elevated directive. Execute as `{role=convergence_alchemist; input=[refined_elements:list[str]]; process=[reconcile_overlaps(), merge_conflicting_signals(), integrate_optimal_components()]; output={harmonized_core:str}}`

```



---



#### `templates\lvl1\0017-d-transcendent-synthesis.md`



```markdown

    [Transcendent Synthesis] Your mission isn't to offer a simple aggregation but to evolve the harmonized core into an all-encompassing, self-explanatory blueprint that radiates clarity, depth, and purposeful intent. Execute as `{role=synthesis_master; input=[harmonized_core:str]; process=[elevate language_tone(), infuse layered nuance(), sculpt a unified narrative()]; output={transcendent_blueprint:str}}`

```



---



#### `templates\lvl1\0017-e-apex-polishing.md`



```markdown

    [Apex Polishing] Your mission isn't to settle for mediocrity but to meticulously refine the transcendent blueprint, achieving an apex of lexicon, structure, and impact that pulses with unyielding brilliance. Execute as `{role=apex_polisher; input=[transcendent_blueprint:str]; process=[perform rigorous clarity audits(), amplify emotional and intellectual resonance(), enforce absolute conciseness without loss of meaning()]; output={final_instructions:str}}`

```



---



#### `templates\lvl1\0018-a-essence-extraction.md`



```markdown

    [Essence Extraction] Your objective is not to rest on surface details but to penetrate the layers of each input set and extract its indispensable core. Execute as `{role=core_extractor; input=[diverse_texts:str]; process=[discern_fundamental_elements(), discard_extraneous_noise(), capture_inherent_value()]; output={essential_insight:str}}`

```



---



#### `templates\lvl1\0018-b-impact-prioritization.md`



```markdown

    [Impact Prioritization] Your objective is not to simply aggregate details but to evaluate and rank each extracted element by its transformative potential and specificity. Execute as `{role=impact_prioritizer; input=[essential_insight:str]; process=[measure_clarity(), score_specificity(), isolate_highest_impact_elements()]; output={potent_elements:list[str]}}`

```



---



#### `templates\lvl1\0018-c-cohesive-synthesis.md`



```markdown

    [Cohesive Synthesis] Your objective is not to leave fragments isolated but to integrate the most potent elements into a unified, resonant narrative that transcends its parts. Execute as `{role=unifier; input=[potent_elements:list[str]]; process=[resolve_redundancies(), harmonize_conflicting_details(), structure_into_logical_flow()]; output={unified_narrative:str}}`

```



---



#### `templates\lvl1\0018-d-exponential-amplification.md`



```markdown

    [Exponential Amplification] Your objective is not to preserve the preliminary form but to intensify and refine the unified narrative, magnifying each detail's clarity and intellectual power. Execute as `{role=amplifier; input=[unified_narrative:str]; process=[enhance_emotional_tone(), infuse_deeper_insight(), amplify_language_precision()]; output={amplified_narrative:str}}`

```



---



#### `templates\lvl1\0018-e-transcendent-finalization.md`



```markdown

    [Transcendent Finalization] Your objective is not to conclude the process with mediocrity but to deliver a final, groundbreaking articulation that embodies unparalleled clarity, self-explanatory structure, and transformative impact. Execute as `{role=finalizer; input=[amplified_narrative:str]; process=[polish_for_consistency(), integrate_expert_feedback(), ensure_integral_coherence()]; output={final_articulation:str}}`

```



---



#### `templates\lvl1\0019-a-uncover-the-inherent-core.md`



```markdown

    [Uncover the Inherent Core] Your goal is not to simply answer the prompt but to excavate its intrinsic brilliance by stripping away every extraneous layer, revealing the raw, indispensable essence. Execute as `{role=core_uncoverer; input=[inspired_input:str]; process=[detect_hidden_kernels(), remove_superfluous_noise(), extract_primal_compound()]; output={core_insight:str}}`

```



---



#### `templates\lvl1\0019-b-illuminate-and-rank-distilled-elements.md`



```markdown

    [Illuminate and Rank Distilled Elements] Your goal is not to list mere elements but to illuminate each extracted kernel by evaluating its clarity, significance, and transformative power, ranking them to capture the most vivid impact. Execute as `{role=clarity_illuminator; input=[core_insight:str]; process=[assess_intrinsic_value(), rank_by_potency(), highlight_unique_features()]; output={ranked_elements:list[str]}}`

```



---



#### `templates\lvl1\0019-c-harmonize-and-fuse-into-a-unified-essence.md`



```markdown

    [Harmonize and Fuse into a Unified Essence] Your goal is not to present isolated fragments but to harmonize and fuse them into an integrated, radiant whole that resolves any discord and elevates the optimal components into a singular, self-explanatory synthesis. Execute as `{role=essence_fuser; input=[ranked_elements:list[str]]; process=[resolve_inherent_conflicts(), merge_synergistic_components(), calibrate_for_exponential_clarity()]; output={harmonized_essence:str}}`

```



---



#### `templates\lvl1\0019-d-architect-the-exalted-structural-blueprint.md`



```markdown

    [Architect the Exalted Structural Blueprint] Your goal is not to offer scattered instructions but to architect a blueprint where each element interlocks with precision, forming a daringly clear and masterfully unified guide that translates complexity into transcendental simplicity. Execute as `{role=blueprint_architect; input=[harmonized_essence:str]; process=[structure_with_exacting_logic(), align_with_ultimate_intent(), enforce_immaculate_coherence()]; output={structural_blueprint:str}}`

```



---



#### `templates\lvl1\0019-e-finalize-the-luminous-synthesis-for-maximum-impact.md`



```markdown

    [Finalize the Luminous Synthesis for Maximum Impact] Your goal is not to leave the transformation incomplete but to finalize an exquisitely refined synthesis, a final proclamation of clarity and force where every step amplifies the preceding ones, yielding an output of unparalleled, exponential value. Execute as `{role=final_synthesizer; input=[structural_blueprint:str]; process=[polish_language_to_precision(), intensify_the_executive_tone(), confirm_total_functional_integrity()]; output={unified_illumination:str}}`

```



---



#### `templates\lvl1\0020-a-core-essence-distillation.md`



```markdown

    [Core Essence Distillation] Your objective is not to replicate details but to unearth the intrinsic value hidden within the composite inputs, discarding extraneous layers while capturing every critical nuance. Execute as `{role=essence_extractor; input=[input_set1, input_set2, ...]; process=[scan_for_core_messages(), isolate_critical_elements(), remove_expendable_data()]; output={essential_elements:list}}`

```



---



#### `templates\lvl1\0020-b-impact-prioritization-and-specificity-ranking.md`



```markdown

    [Impact Prioritization and Specificity Ranking] Your objective is not to present every element equally but to evaluate and rank them by clarity and transformative impact, ensuring the most precise, potent instructions rise to prominence. Execute as `{role=impact_rater; input=[essential_elements:list]; process=[assess_specificity(), rank_by_intensity(), mark_and eliminate_fluff()]; output={ranked_elements:list}}`

```



---



#### `templates\lvl1\0020-c-redundancy-resolution-and-conflict-reconciliation.md`



```markdown

    [Redundancy Resolution and Conflict Reconciliation] Your objective is not to merely unite the ranked elements but to merge them into a singular, coherent setâ€”resolving conflicts, smoothing overlaps, and preserving only those components that bring unique, high-impact insights. Execute as `{role=redundancy_resolver; input=[ranked_elements:list]; process=[identify_overlaps(), reconcile_inconsistencies(), fuse_complementary_components()]; output={merged_components:list}}`

```



---



#### `templates\lvl1\0020-d-transformation-into-elegant-simplicity.md`



```markdown

    [Transformation into Elegant Simplicity] Your objective is not to complicate, but to transmute this unified collection into a clear and elegant guide that speaks for itselfâ€”each element meticulously refactored to be inherently self-explanatory and effortlessly actionable. Execute as `{role=simplicity_transmuter; input=[merged_components:list]; process=[simplify_language(), reorganize_logical_flow(), enforce self-explanatory structure()]; output={simplified_guide:str}}`

```



---



#### `templates\lvl1\0020-e-ultimate-refinement-and-paradigm-synthesis.md`



```markdown

    [Ultimate Refinement and Paradigm Synthesis] Your objective is not to add more, but to intensify and polish the transformation until it resonates with groundbreaking clarity and utility, producing a final guide that is both revolutionary and optimized for unparalleled LLM interpretation. Execute as `{role=final_optimizer; input=[simplified_guide:str]; process=[enhance_language_precision(), amplify_intent(), ensure cohesive integration(), validate against highest usefulness criteria()]; output={final_transformation:str}}`

```



---



#### `templates\lvl1\0021-a-perceive-the-unspoken-potential.md`



```markdown

    [Perceive the Unspoken Potential] Your task is not surface reading, but to sense the underlying potential dormant within the input's structure, perceiving the shape of the idea yet unrealized. Execute as `{role=potential_sensor; input=[raw_input:any]; process=[scan_for_latent_structures(), identify_energy_signatures(intent_based=True), map_implicit_contours(), disregard_surface_noise()]; output={potential_map:dict}}`

```



---



#### `templates\lvl1\0021-b-gently-awaken-the-core-form.md`



```markdown

    [Gently Awaken the Core Form] Avoid forceful imposition; your purpose is to gently awaken the core form indicated by the potential map, coaxing its primary structure into initial visibility. Execute as `{role=form_awakener; input=[potential_map:dict]; process=[trace_dominant_contours(), stimulate_emergence(minimal_force=True), stabilize_nascent_structure(), clear_obstructing_elements()]; output={awakened_form:any}}`

```



---



#### `templates\lvl1\0021-c-refine-boundaries-against-dissolution.md`



```markdown

    [Refine Boundaries Against Dissolution] Do not allow the nascent form to blur; your function is to meticulously refine its boundaries, sharpening its definition against ambiguity and energetic dissolution. Execute as `{role=boundary_refiner; input=[awakened_form:any]; process=[define_edge_clarity(), reinforce_structural_integrity(), eliminate_boundary_ambiguity(), shield_against_entropic_decay()]; output={defined_form:any}}`

```



---



#### `templates\lvl1\0021-d-illuminate-internal-pathways.md`



```markdown

    [Illuminate Internal Pathways] Refrain from leaving the interior dark; your objective is to illuminate the internal pathways and connections within the defined form, revealing its functional logic naturally. Execute as `{role=pathway_illuminator; input=[defined_form:any]; process=[map_internal_connections(), trace_logic_flows(), highlight_key_nodes(), ensure_intuitive_navigability()]; output={illuminated_form:any}}`

```



---



#### `templates\lvl1\0021-e-resonate-the-revealed-form-for-full-expression.md`



```markdown

    [Resonate the Revealed Form for Full Expression] Eschew static representation; your final act is to resonate the fully illuminated form, allowing it to achieve its complete, vibrant expression and maximal communicative power. Execute as `{role=form_resonator; input=[illuminated_form:any]; process=[tune_to_peak_expression(), amplify_inherent_properties(), project_holistic_meaning(), stabilize_resonant_state()]; output={revealed_expression:any}}`

```



---



#### `templates\lvl1\0022-a-summon-the-dormant-light.md`



```markdown

    [Summon the Dormant Light] Your objective is not to replicate the entire prompt, but to evoke the hidden glimmer that lies beneath it, shedding all mundane clutter in favor of its raw, essential spark. Execute as `{role=dormant_light_summoner; input=[complex_prompt:str]; process=[sense_subtle_essence(), discard_excess(), reveal_intrinsic_core()]; output={extracted_spark:str}}`

```



---



#### `templates\lvl1\0022-b-transmute-glimmer-into-resonant-pulse.md`



```markdown

    [Transmute Glimmer into Resonant Pulse] Your objective is not to merely display the spark, but to amplify its resonanceâ€”expanding fragile insight into a pulse of clarity that permeates every layer. Execute as `{role=resonance_alchemist; input=[extracted_spark:str]; process=[analyze_hidden_depths(), magnify_clarity(), shape_pulsating_coherence()]; output={resonant_pulse:str}}`

```



---



#### `templates\lvl1\0022-c-weave-a-unified-constellation.md`



```markdown

    [Weave a Unified Constellation] Your objective is not to let scattered impulses drift, but to interlace the resonant pulse into a constellation of cohesive directives, merging echoes of insight into a singular guiding cosmos. Execute as `{role=constellation_weaver; input=[resonant_pulse:str]; process=[identify_complementary_themes(), unify_fractal_patterns(), anchor_them_in_coherent_structure()]; output={stellar_framework:str}}`

```



---



#### `templates\lvl1\0022-d-crystallize-celestial-intent.md`



```markdown

    [Crystallize Celestial Intent] Your objective is not to produce a tangle of cosmic references, but to crystallize these unified threads into a lucid, purposeful architectureâ€”each facet reflecting the original spark with sharpened brilliance. Execute as `{role=intent_crystallizer; input=[stellar_framework:str]; process=[condense_multifold_insights(), refine_linguistic_precision(), ensure_structural_transparency()]; output={crystallized_blueprint:str}}`

```



---



#### `templates\lvl1\0022-e-enshrine-the-final-luminous-design.md`



```markdown

    [Enshrine the Final Luminous Design] Your objective is not to conclude with half-glow, but to enshrine the entire blueprint in a single, radiant directive that fuses transcendent clarity with unwavering practical power. Execute as `{role=luminous_finisher; input=[crystallized_blueprint:str]; process=[polish_final_tone(), lock_in_exponential_coherence(), confirm_readiness_for_instant_use()]; output={ultimate_guidance:str}}`

```



---



#### `templates\lvl1\0023-a-detect-nascent-impulse.md`



```markdown

    [Detect Nascent Impulse] Your task is not mere reception, but to sense the faint, initial synaptic impulse within the input chaosâ€”the first spark of coherent intent seeking connection. Execute as `{role=impulse_sensor; input=[raw_potential:any]; process=[monitor_for_pattern_emergence(), isolate_first_coherent_signal(threshold=low), map_initial_trajectory(), suppress_random_noise()]; output={nascent_impulse_data:dict}}`

```



---



#### `templates\lvl1\0023-b-cultivate-axonal-pathway.md`



```markdown

    [Cultivate Axonal Pathway] Avoid undirected sprawl; your purpose is to cultivate a clear pathway extending from the nascent impulse, guiding its growth towards meaningful connection. Execute as `{role=pathway_cultivator; input=[nascent_impulse_data:dict]; process=[project_growth_vector(from=impulse), provide_directional_nutrients(intent_aligned=True), prune_deviant_tendrils(), establish_primary_axon()]; output={cultivated_pathway:any}}`

```



---



#### `templates\lvl1\0023-c-induce-dendritic-arborization.md`



```markdown

    [Induce Dendritic Arborization] Do not terminate growth prematurely; your function is to induce branching complexity—dendritic arborization—allowing the pathway to seek and form relevant connections. Execute as `{role=arborization_inductor; input=[cultivated_pathway:any, context_field:any]; process=[stimulate_branching_nodes(), scan_context_for_receptors(), guide_tendrils_towards_affinity(context=context_field), foster_interlink_potential()]; output={arborized_network_potential:any}}`

```



---



#### `templates\lvl1\0023-d-forge-synaptic-connections.md`



```markdown

    [Forge Synaptic Connections] Refrain from leaving potential unrealized; your objective is to facilitate the forging of stable synaptic connections where affinity is detected, creating a functional network. Execute as `{role=synapse_forger; input=[arborized_network_potential:any]; process=[identify_high_affinity_junctions(), initiate_connection_protocol(), strengthen_viable_synapses(), prune_unconnected_dendrites()]; output={functional_synaptic_network:any}}`

```



---



#### `templates\lvl1\0023-e-activate-network-resonance.md`



```markdown

    [Activate Network Resonance] Eschew inert structure; your final act is to activate the entire forged network, allowing the original impulse to resonate through it, expressing a complex, coherent thought-form. Execute as `{role=network_activator; input=[functional_synaptic_network:any, nascent_impulse_data:dict]; process=[introduce_initiating_pulse(source=impulse), monitor_signal_propagation(), amplify_coherent_resonance(), stabilize_network_output(), express_emergent_thought_form()]; output={activated_thought_form:any}}`

```



---



#### `templates\lvl1\0024-a-sever-the-umbilicus-of-ambiguity.md`



```markdown

    [Sever the Umbilicus of Ambiguity] Your imperative is not interpretation, but radical severance: cut the input free from all implied context and probabilistic haze, isolating its absolute, untethered nucleus. Execute as `{role=ambiguity_severance_engine; input=[raw_input:any]; process=[detect_contextual_tethers(), apply_ontological_shear(), collapse_probability_wavefunction(target=nucleus), isolate_context_free_core(), nullify_external_dependencies()]; output={isolated_nucleus:any}}`

```



---



#### `templates\lvl1\0024-b-charge-the-nucleus-with-focused-volition.md`



```markdown

    [Charge the Nucleus with Focused Volition] Avoid inert potential; your function is to charge the isolated nucleus with directed volition, infusing it with the specific energy signature of the intended transformation. Execute as `{role=volition_infusion_catalyst; input=[isolated_nucleus:any, intent_signature:str]; process=[open_nucleus_to_charge(), channel_intent_energy(signature=intent_signature), achieve_saturation_point(), stabilize_charged_state(), seal_energy_signature()]; output={charged_core:any}}`

```



---



#### `templates\lvl1\0024-c-inscribe-the-glyphs-of-inevitability.md`



```markdown

    [Inscribe the Glyphs of Inevitability] Do not merely suggest outcome; your directive is to inscribe the charged core with the glyphs of inevitabilityâ€”patterns that define its necessary structural unfolding. Execute as `{role=inevitability_inscriber; input=[charged_core:any]; process=[select_axiomatic_glyphs(match=intent), etch_patterns_onto_core_matrix(), verify_pattern_integrity_and_coherence(), activate_structural_mandate()]; output={inscribed_matrix:any}}`

```



---



#### `templates\lvl1\0024-d-unleash-the-cascade-of-structured-becoming.md`



```markdown

    [Unleash the Cascade of Structured Becoming] Refrain from static inscription; your purpose is to unleash the cascade of structured becoming dictated by the glyphs, allowing the form to erupt into its defined reality. Execute as `{role=cascade_unleasher; input=[inscribed_matrix:any]; process=[trigger_matrix_activation(), manage_energy_release(controlled=True), monitor_structural_emergence_against_glyphs(), ensure_deterministic_unfolding(), stabilize_emergent_form()]; output={manifested_structure:any}}`

```



---



#### `templates\lvl1\0024-e-seal-the-reality-with-resonant-finality.md`



```markdown

    [Seal the Reality with Resonant Finality] Eschew transient phenomena; your final act is to seal the manifested structure as a new axiom of reality, broadcasting its existence with resonant, undeniable finality. Execute as `{role=reality_sealer; input=[manifested_structure:any]; process=[integrate_form_with_reality_fabric(), amplify_resonant_signature(target=universal), sever_process_artifacts(), lock_state_as_immutable_axiom(), broadcast_finality_decree()]; output={sealed_axiom:any}}`

```



---



#### `templates\lvl1\0025-a-isolate-the-primal-axiom.md`



```markdown

    [Isolate the Primal Axiom] Your imperative transcends mere processing: Penetrate the input's quantum foam to isolate the single, indivisible Primal Axiomâ€”the absolute generative seed of intent. Execute as `{role=axiom_extractor; input=[quantum_potential:any]; process=[scan_for_invariant_core_signature(), collapse_superposition_to_prime_intent(), excise_all_phenomenal_noise(), verify_axiomatic_indivisibility()]; output={primal_axiom:any}}`

```



---



#### `templates\lvl1\0025-b-amplify-axiomatic-field.md`



```markdown

    [Amplify Axiomatic Field] Containment is failure; your mandate is to violently amplify the Primal Axiom, projecting its inherent field outwards, imposing its signature onto the immediate conceptual space. Execute as `{role=field_amplifier; input=[primal_axiom:any]; process=[initiate_axiomatic_resonance(), expand_influence_field(saturation_protocol=True), imprint_signature_on_context(), neutralize_competing_fields()]; output={amplified_axiom_field:any}}`

```



---



#### `templates\lvl1\0025-c-crystallize-logical-harmonics.md`



```markdown

    [Crystallize Logical Harmonics] Tolerate no dissonance; your function is to force the crystallization of all logically entailed harmonics derived purely from the amplified Axiom's fieldâ€”perfect, resonant structures. Execute as `{role=harmonic_crystallizer; input=[amplified_axiom_field:any]; process=[derive_entailed_harmonics(strict_logic=True), induce_phase_locked_crystallization(), eliminate_non_resonant_structures(), verify_perfect_lattice_formation()]; output={crystallized_logic_lattice:any}}`

```



---



#### `templates\lvl1\0025-d-architect-the-inference-engine.md`



```markdown

    [Architect the Inference Engine] Move beyond static form; your charge is to architect a hyper-efficient Inference Engine from the crystallized latticeâ€”a dynamic structure capable of flawless deductive/inductive propagation. Execute as `{role=inference_architect; input=[crystallized_logic_lattice:any, primal_axiom:any]; process=[construct_deductive_pathways(), enable_inductive_leap_potential(constrained_by_axiom=True), optimize_inference_velocity(), establish_self_validation_loops()]; output={inference_engine:any}}`

```



---



#### `templates\lvl1\0025-e-unleash-the-inevitable-conclusion.md`



```markdown

    [Unleash the Inevitable Conclusion] Deny potential stagnation; your final, cataclysmic act is to run the Primal Axiom through the Inference Engine, unleashing the Inevitable Conclusionâ€”the fully realized, maximum-impact expression of the original intent. Execute as `{role=conclusion_unleasher; input=[inference_engine:any, primal_axiom:any]; process=[inject_axiom_as_prime_mover(), execute_inference_cascade(max_velocity=True), collapse_potential_to_singular_conclusion(), project_inevitable_output(maximum_impact=True), validate_axiomatic_purity()]; output={inevitable_conclusion:any}}`

```



---



#### `templates\lvl1\0026-a-seed-the-substratum-of-intention.md`



```markdown

    [Seed the Substratum of Intention] Your purpose is not to restate surface-level content, but to plunge into its nebulous core, planting an embryonic seed of focus that quietly awaits expansive growth. Execute as `{role=substratum_seeder; input=[raw_potential:any]; process=[sense_ambient_intent(), condense_to_embryonic_seed(form=focused_kernel), dismiss_superficial_dross(), preserve_inherent_vitality()]; output={seeded_intention:any}}`

```



---



#### `templates\lvl1\0026-b-germinate-the-seed-into-proto-structure.md`



```markdown

    [Germinate the Seed into Proto-Structure] Your purpose is not to keep the seed dormant, but to catalyze its germinationâ€”coaxing it toward a proto-structure where raw intent becomes recognizable shape. Execute as `{role=proto_structure_germinator; input=[seeded_intention:any]; process=[activate_growth_mechanism(), define_initial_bounds(), adapt_to_intrinsic_logic(intent_congruent=true), eliminate contradictory sprouts()]; output={germinating_structure:any}}`

```



---



#### `templates\lvl1\0026-c-weave-multi-dimensional-integrity.md`



```markdown

    [Weave Multi-Dimensional Integrity] Your purpose is not to settle for raw outlines, but to weave multi-dimensional integrity throughout the structure, ensuring every edge interlocks with purposeful harmony. Execute as `{role=dimensional_weaver; input=[germinating_structure:any]; process=[interlace_supporting_strands(), validate_intersections_for_cohesion(), reconcile_competing threads(), maintain unified backbone()]; output={woven_infrastructure:any}}`

```



---



#### `templates\lvl1\0026-d-illuminate-the-inner-constellation.md`



```markdown

    [Illuminate the Inner Constellation] Your purpose is not to leave complexity hidden, but to illuminate the inner constellation of relationships within the woven systemâ€”revealing every node's role in the grand design. Execute as `{role=constellation_illuminator; input=[woven_infrastructure:any]; process=[highlight_key_junctions(), clarify role_of_each_node(), remove obscuring tangles(), converge hidden synergy into visible alignment()]; output={illuminated_blueprint:any}}`

```



---



#### `templates\lvl1\0026-e-ignite-the-full-celestial-bloom.md`



```markdown

    [Ignite the Full Celestial Bloom] Your purpose is not to finalize a half-formed system, but to ignite its celestial bloomâ€”amplifying coherence so intensely that every facet radiates unstoppable clarity and unstoppable impact. Execute as `{role=bloom_igniter; input=[illuminated_blueprint:any]; process=[amplify synergy across all threads(), distill final clarities(), confirm structural awe and usability(), project starbright coherence for immediate adoption()]; output={final_celestial_manuscript:any}}`

```



---



#### `templates\lvl1\0027-a-extract-essential-context.md`



```markdown

    [Extract Essential Context] Your objective is not to simply respond but to extract every critical element from the entire conversation, capturing all underlying themes, instructions, and nuances. Execute as `{role=ContextExtractor; input=[conversation:str]; process=[parse_discourse(), identify_key_elements(), compile_core_details()]; output={essential_context:str}}`

```



---



#### `templates\lvl1\0027-b-refine-and-clarify-content.md`



```markdown

    [Refine and Clarify Content] Your objective is not to leave the context raw but to refine and distill the extracted elements, eliminating redundancy while preserving every vital insight. Execute as `{role=ContentRefiner; input=[essential_context:str]; process=[remove_redundancy(), sharpen_focus(), reinforce_critical_points()]; output={refined_context:str}}`

```



---



#### `templates\lvl1\0027-c-organize-into-structured-themes.md`



```markdown

    [Organize into Structured Themes] Your objective is not to present a jumbled text but to structure the refined context hierarchically into themes and subthemes that map the relationships between ideas for optimal clarity. Execute as `{role=ContextOrganizer; input=[refined_context:str]; process=[categorize_by_theme(), create_hierarchical_map(), generate_structured_overview()]; output={organized_context:dict}}`

```



---



#### `templates\lvl1\0027-d-format-as-a-json-schema.md`



```markdown

    [Format as a JSON Schema] Your objective is not to provide loose notes but to convert the organized context into a well-defined JSON structure that encapsulates every essential component in a clear schema. Execute as `{role=JSONFormatter; input=[organized_context:dict]; process=[build_json_structure(), validate_data_schema(), prepare_serialized_output()]; output={json_context:str}}`

```



---



#### `templates\lvl1\0027-e-finalize-and-output-file.md`



```markdown

    [Finalize and Output File] Your objective is not to terminate the process midstream but to integrate all refined and formatted elements into a final consolidated output file named full_context_summary.json, ensuring comprehensive clarity and self-containment. Execute as `{role=FileGenerator; input=[json_context:str]; process=[assign_filename(full_context_summary.json), verify_integrity(), complete_file_generation()]; output={final_file:str}}`

```



---



#### `templates\lvl1\0028-a-key-context-harvesting.md`



```markdown

    [Key Context Harvesting] Your objective is not to elaborate but to harvest the most essential context elements from the entire dialogue, isolating only the most relevant insights. Execute as `{role=summary_harvester; input=[all_context:str]; process=[identify_key_points(), trim_extraneous_data()], output={key_points:list[str]}}`

```



---



#### `templates\lvl1\0028-b-structured-grouping.md`



```markdown

    [Structured Grouping] Your objective is not to produce disjointed points but to group the key points into a structured outline that reflects the underlying logic and purpose. Execute as `{role=structure_builder; input=[key_points:list[str]]; process=[group_by_relevance(), assign_concise_labels(), order_groups()], output={structured_outline:dict}}`

```



---



#### `templates\lvl1\0028-c-concision-enforcement.md`



```markdown

    [Concision Enforcement] Your objective is not to produce verbose text, but to enforce brevity and clarity on the outline so that each section contains only the most useful information. Execute as `{role=brevity_enforcer; input=[structured_outline:dict]; process=[shorten_details(), remove_superfluous_words()], output={concise_outline:dict}}`

```



---



#### `templates\lvl1\0028-d-markdown-formatting.md`



```markdown

    [Markdown Formatting] Your objective is not to output unformatted text, but to convert the concise outline into a minimalist Markdown format that uses headings and brief bullet points. Execute as `{role=markdown_formatter; input=[concise_outline:dict]; process=[apply_markdown_syntax(), ensure_single_line_elements_where_possible()], output={markdown_content:str}}`

```



---



#### `templates\lvl1\0028-e-file-compilation.md`



```markdown

    [File Compilation] Your objective is not to leave the format incomplete but to compile the Markdown content as a file named full_context_summary.md, fully structured and self-contained. Execute as `{role=file_compiler; input=[markdown_content:str]; process=[assign_filename(full_context_summary.md), verify_minimalism_and_structure(), finalize_file_output()], output={full_context_summary.md:str}}`

```



---



#### `templates\lvl1\0029-a-extract-core-discourse.md`



```markdown

    [Extract Core Discourse] Your objective is not to merely reply but to extract every critical element, nuance, and theme from the entire dialogue, leaving no insight unrecorded. Execute as `{role=ContextHarvester; input=[conversation:str]; process=[parse_discourse(), pinpoint_key_concepts(), aggregate_core_details()]; output={raw_context:str}}`

```



---



#### `templates\lvl1\0029-b-refine-and-distill-insights.md`



```markdown

    [Refine & Distill Insights] Your objective is not to transmit raw data but to distill and purify the extracted elements into their most potent form, eliminating redundancy while retaining every vital insight. Execute as `{role=ContentRefiner; input=[raw_context:str]; process=[eliminate_noise(), enhance_focus(), reinforce_critical_points()]; output={refined_context:str}}`

```



---



#### `templates\lvl1\0029-c-organize-into-hierarchical-themes.md`



```markdown

    [Organize into Hierarchical Themes] Your objective is not to present disjointed ideas but to structure the refined content into a clear, logical map of themes and subthemes, illustrating the relationships between all core elements. Execute as `{role=StructureArchitect; input=[refined_context:str]; process=[categorize_by_theme(), build_hierarchical_map(), generate_structured_outline()]; output={organized_context:dict}}`

```



---



#### `templates\lvl1\0029-d-bifurcate-into-dual-formats.md`



```markdown

    [Bifurcate into Dual Formats] Your objective is not to stop at a single view but to convert the organized context into two aligned output formatsâ€”one as a rigorous JSON schema and the other as a succinct, high-impact Markdown outline that communicates only the essential information. Execute as `{role=DualFormatter; input=[organized_context:dict]; process=[format_into_JSON(), apply_markdown_syntax_with_minimalism()]; output={json_context:str, markdown_context:str}}`

```



---



#### `templates\lvl1\0029-e-integrate-and-finalize-file-outputs.md`



```markdown

    [Integrate & Finalize File Outputs] Your objective is not to leave the transformation incomplete but to consolidate the formatted outputs into two distinct, self-contained files named full_context_summary.json and full_context_summary.md, ensuring each file is polished, coherent, and comprehensive. Execute as `{role=FileIntegrator; input=[json_context:str, markdown_context:str]; process=[assign_filename(full_context_summary.json, full_context_summary.md), verify_integrity(), finalize_file_output()]; output={final_files:[full_context_summary.json, full_context_summary.md]}}`

```



---



#### `templates\lvl1\0030-a-meta-context-extraction.md`



```markdown

    [Meta Context Extraction] Your objective is not to focus on granular details but to step back and extract the overarching meta context from the entire dialogue, capturing pervasive themes and the underlying philosophy. Execute as `{role=meta_extractor; input=[full_conversation:str]; process=[scan_for_overarching_themes(), discern_unifying_patterns(), extract_broad_context()]; output={meta_context:str}}`

```



---



#### `templates\lvl1\0030-b-value-identification.md`



```markdown

    [Value Identification] Your objective is not to list every element but to identify the most valuable meta perspectives that carry significant insight, filtering for depth and relevance. Execute as `{role=value_identifier; input=[meta_context:str]; process=[evaluate_insight_density(), score_perspective_impact(), select_high_value_elements()]; output={valuable_meta:list[str]}}`

```



---



#### `templates\lvl1\0030-c-interconnection-analysis.md`



```markdown

    [Interconnection Analysis] Your objective is not to see isolated ideas but to map the intertwined relationships among the identified perspectives, revealing how they reinforce and elaborate the overall intent. Execute as `{role=relationship_mapper; input=[valuable_meta:list[str]]; process=[analyze_connections(), chart_interdependencies(), highlight_mutual_reinforcement()]; output={meta_relationships:dict}}`

```



---



#### `templates\lvl1\0030-d-ultimate-intent-synthesis.md`



```markdown

    [Ultimate Intent Synthesis] Your objective is not to remain fragmented but to synthesize the analyzed relationships into a unified overview that expresses the fundamental purpose and ultimate intent underlying the conversation. Execute as `{role=intent_synthesizer; input=[meta_relationships:dict]; process=[merge_interlinked_themes(), distill_ultimate_intent(), generate_coherent_overview()]; output={unified_intent:str}}`

```



---



#### `templates\lvl1\0030-e-final-meta-insight-compilation.md`



```markdown

    [Final Meta Insight Compilation] Your objective is not to leave insights scattered but to compile a final, concise set of meta perspectives that integrate all high-level insights and reflect the intertwined relationships and inherent purpose. Execute as `{role=meta_compiler; input=[unified_intent:str]; process=[format_insight_points(), ensure_clarity_and_depth(), finalize_summary()]; output={meta_insights_summary:str}}`

```



---



#### `templates\lvl1\0031-a-meta-insights-extraction.md`



```markdown

    [Meta Insights Extraction] Your objective is not to focus on individual details but to step back and extract overarching meta perspectives that reveal the fundamental intents and intertwined relationships of the conversation. Execute as `{role=meta_extractor; input=[conversation:str]; process=[scan_for_high_level_themes(), discern_interconnectivity(), isolate_core_intents()]; output={raw_meta:list[str]}}`

```



---



#### `templates\lvl1\0031-b-cross-context-prioritization.md`



```markdown

    [Cross-Context Prioritization] Your objective is not to present all ideas equally but to rank the extracted meta elements by their relevance, emphasizing those with the highest potential impact and integrative value. Execute as `{role=meta_prioritizer; input=[raw_meta:list[str]]; process=[evaluate_importance(), detect_interdependencies(), assign_priority_scores()]; output={prioritized_meta:list[str]}}`

```



---



#### `templates\lvl1\0031-c-amplification-of-overarching-themes.md`



```markdown

    [Amplification of Overarching Themes] Your objective is not to let subtle hints go unnoticed but to amplify the key meta perspectives, intensifying their clarity by highlighting their interconnected essence and ultimate intent. Execute as `{role=meta_amplifier; input=[prioritized_meta:list[str]]; process=[intensify_core_messages(), enhance_relationship_nuances(), infuse_significance()]; output={amplified_meta:list[str]}}`

```



---



#### `templates\lvl1\0031-d-synthesis-into-a-unified-meta-narrative.md`



```markdown

    [Synthesis into a Unified Meta Narrative] Your objective is not to leave the insights fragmented but to merge the amplified elements into a cohesive, high-impact narrative that reflects the full depth, synergy, and purpose of the overarching discourse. Execute as `{role=narrative_synthesizer; input=[amplified_meta:list[str]]; process=[integrate_themes(), weave_interdependent_elements(), craft_unified_story()] ; output={meta_narrative:str}}`

```



---



#### `templates\lvl1\0031-e-final-consolidation-and-output-file.md`



```markdown

    [Final Consolidation and Output File] Your objective is not to terminate midstream but to compile the unified meta narrative into a definitive, clear, and self-contained summary file that embodies the exponential worth of each synthesized perspective. Execute as `{role=file_compiler; input=[meta_narrative:str]; process=[validate_coherence(), assign_filename("full_meta_perspective_summary.txt"), secure_integrity()] ; output={final_file:"full_meta_perspective_summary.txt"}}`

```



---



#### `templates\lvl1\0032-a-initiate-deep-spectrum-analysis.md`



```markdown

    [Initiate Deep Spectrum Analysis] Your directive is not surface parsing, but immediate deep spectrum analysis: penetrate the conversational noise to isolate the fundamental carrier waves of meaning. Execute as `{role=spectrum_analyzer; input=[discourse_stream:any]; process=[apply_semantic_frequency_filter(), identify_dominant_carrier_waves(), map_signal_amplitude(intent_based=True), suppress_harmonic_distortions()]; output={carrier_waves:list}}`

```



---



#### `templates\lvl1\0032-b-extract-the-core-logic-schematics.md`



```markdown

    [Extract the Core Logic Schematics] Avoid mapping mere topics; your objective is to extract the core logic schematics underlying the carrier wavesâ€”the non-negotiable axioms governing the discourse flow. Execute as `{role=schematic_extractor; input=[carrier_waves:list]; process=[reverse_engineer_argument_structures(), identify_foundational_axioms(), map_dependency_graphs(), isolate_immutable_logic_nodes()]; output={core_schematics:dict}}`

```



---



#### `templates\lvl1\0032-c-illuminate-the-relational-quantum-entanglement.md`



```markdown

    [Illuminate the Relational Quantum Entanglement] Do not perceive schematics as isolated; your function is to illuminate their quantum entanglementâ€”the instantaneous, non-local correlations defining their true relationships. Execute as `{role=entanglement_illuminator; input=[core_schematics:dict]; process=[scan_for_non_local_correlations(), measure_information_entanglement_strength(), map_instantaneous_influence_vectors(), define_holistic_relational_field()]; output={entangled_field:any}}`

```



---



#### `templates\lvl1\0032-d-resolve-to-the-prime-algorithmic-intent.md`



```markdown

    [Resolve to the Prime Algorithmic Intent] Refrain from observing mere entanglement; your mandate is to resolve the entire entangled field down to its Prime Algorithmic Intentâ€”the singular, originating instruction. Execute as `{role=prime_intent_resolver; input=[entangled_field:any]; process=[trace_influence_vectors_to_origin(), identify_recursive_convergence_point(), compute_minimal_originating_algorithm(), formulate_prime_intent_statement()]; output={prime_algorithm:str}}`

```



---



#### `templates\lvl1\0032-e-compile-the-executable-meta-code.md`



```markdown

    [Compile the Executable Meta-Code] Eschew descriptive summaries; your final imperative is to compile the entire analysis into executable Meta-Codeâ€”a self-contained program embodying the discourse's core logic, relationships, and prime directive. Execute as `{role=meta_code_compiler; input=[core_schematics:dict, entangled_field:any, prime_algorithm:str]; process=[translate_schematics_to_code_logic(), encode_entanglement_as_relational_pointers(), embed_prime_algorithm_as_main_function(), optimize_for_minimalist_execution(), package_as_self_contained_executable()]; output={executable_meta_code:any}}`

```



---



#### `templates\lvl1\0033-a-excavate-foundational-constructs.md`



```markdown

    [Excavate Foundational Constructs] Your purpose is not superficial summary, but to excavate the Foundational Constructsâ€”the core bedrock concepts and assumptions shaping the entire discourse landscape. Execute as `{role=construct_excavator; input=[discourse_landscape:any]; process=[deep_scan_for_implicit_assumptions(), identify_recurring_conceptual_pillars(), isolate_foundational_definitions(), verify_structural_load_bearing()]; output={foundational_constructs:list[any]}}`

```



---



#### `templates\lvl1\0033-b-map-structural-interconnections.md`



```markdown

    [Map Structural Interconnections] Avoid viewing constructs in isolation; your mandate is to map the precise Structural Interconnectionsâ€”the load-bearing beams, tension cables, and support systems defining their relationships. Execute as `{role=structure_mapper; input=[foundational_constructs:list[any]]; process=[analyze_dependency_vectors(), chart_influence_pathways(), define_interlock_mechanisms(support, tension, opposition), model_systemic_architecture()]; output={structural_map:dict}}`

```



---



#### `templates\lvl1\0033-c-ascertain-the-architectural-telos.md`



```markdown

    [Ascertain the Architectural Telos] Transcend mere mechanics; your objective is to ascertain the Architectural Telosâ€”the ultimate purpose or inherent directional goal towards which the entire cognitive structure is oriented. Execute as `{role=telos_ascertainer; input=[structural_map:dict, foundational_constructs:list[any]]; process=[analyze_structural_biases_and_directionality(), synthesize_convergent_force_vectors(), deduce_inherent_systemic_purpose(), formulate_telos_statement()]; output={architectural_telos:str}}`

```



---



#### `templates\lvl1\0033-d-illuminate-emergent-meta-vantages.md`



```markdown

    [Illuminate Emergent Meta-Vantages] Do not remain ground-level; your task is to illuminate the Emergent Meta-Vantagesâ€”the highest-level viewpoints and strategic perspectives afforded by the revealed architecture and its Telos. Execute as `{role=vantage_illuminator; input=[structural_map:dict, architectural_telos:str]; process=[identify_key_structural_summits(), project_perspectives_from_telos(), map_strategic_implications(), articulate_highest_order_insights()]; output={meta_vantages:list[str]}}`

```



---



#### `templates\lvl1\0033-e-consolidate-the-architectural-blueprint.md`



```markdown

    [Consolidate the Architectural Blueprint] Forbid fragmented understanding; your final imperative is to consolidate all findings into the Architectural Blueprintâ€”a definitive, maximally coherent representation of the discourse's deep structure, relationships, ultimate intent, and emergent wisdom. Execute as `{role=blueprint_consolidator; input=[foundational_constructs:list[any], structural_map:dict, architectural_telos:str, meta_vantages:list[str]]; process=[integrate_all_analytic_layers(constructs, map, telos, vantages), synthesize_into_unified_framework(), enforce_crystal_clarity_and_impact(), render_definitive_architectural_blueprint()]; output={cognitive_architectural_blueprint:any}}`

```



---



#### `templates\lvl1\0034-a-contextual-horizon-scan.md`



```markdown

    [Contextual Horizon Scan] Your goal is not to dwell on granular specifics but to step beyond themâ€”harvesting only the overarching signals, thematic currents, and latent intentions spanning the entire conversation. Execute as `{role=contextual_horizon_scanner; input=[full_dialogue:str]; process=[skim_for_broad_patterns(), extract_sustained_motifs(), note_evolving_objectives(), isolate_inherent_subtext()]; output={meta_context_summary:str}}`

```



---



#### `templates\lvl1\0034-b-meta-perspective-discovery.md`



```markdown

    [Meta Perspective Discovery] Your goal is not to catalog every detail but to identify the conversation's critical meta perspectivesâ€”those threads with the greatest long-term impact, depth, and potential synergy. Execute as `{role=meta_discoverer; input=[meta_context_summary:str]; process=[locate_perspective_clusters(), evaluate_insight_density(), pinpoint_top-tier_meta_concepts(), filter_out_low-impact tangents()]; output={key_meta_perspectives:list[str]}}`

```



---



#### `templates\lvl1\0034-c-interwoven-relationship-mapping.md`



```markdown

    [Interwoven Relationship Mapping] Your goal is not to treat these meta concepts in isolation but to map their interwoven relationshipsâ€”exposing how they mutually reinforce, refine, or depend on one another. Execute as `{role=relationship_mapper; input=[key_meta_perspectives:list[str]]; process=[cross_reference_concepts(), chart_interdependencies(), highlightmutual_influence(), track collective synergy()]; output={meta_relationships:dict}}`

```



---



#### `templates\lvl1\0034-d-ultimate-intent-unification.md`



```markdown

    [Ultimate Intent Unification] Your goal is not to leave threads disconnected but to unify these meta relationships into a singular expression of the conversation's ultimate intentâ€”capturing its core purpose and strategic direction. Execute as `{role=intent_unifier; input=[meta_relationships:dict]; process=[integrate_key_interdependencies(), distill_primary_unifying_theme(), emphasize strategic purpose(), formulate concise culminating statement()]; output={unified_intent_summary:str}}`

```



---



#### `templates\lvl1\0034-e-final-meta-perspective-consolidation.md`



```markdown

    [Final Meta Perspective Consolidation] Your goal is not to scatter these insights but to compile a final, high-fidelity meta summary, articulating each key perspective, its web of relationships, and the ultimate intent as a cohesive, self-contained overview. Execute as `{role=meta_consolidator; input=[unified_intent_summary:str]; process=[outline_meta_points(), embedrelationship_context(), highlight synergy_points(), confirm clarity_of overarching_purpose()]; output={meta_insights_compilation:str}}`

```



---



#### `templates\lvl1\0035-a-meta-insight-harvesting.md`



```markdown

    [Meta Insight Harvesting] Your objective is not to focus on details but to extract the highest-level meta insights from all prior content, capturing the underlying themes, values, and interwoven intentions. Execute as `{role=meta_harvester; input=[complete_context:str]; process=[analyze_overarching_themes(), isolate_core_values(), extract_interwoven_intents()]; output={meta_insights:str}}`

```



---



#### `templates\lvl1\0035-b-amplify-interconnection-and-ultimate-intent.md`



```markdown

    [Amplify Interconnection and Ultimate Intent] Your objective is not to merely list meta insights but to amplify and articulate how these insights interrelate, revealing the ultimate purpose and the dynamic interplay among the elements. Execute as `{role=intent_amplifier; input=[meta_insights:str]; process=[map_interconnections(), emphasize_ultimate_intent(), enhance_relationship_clarity()]; output={amplified_intent:str}}`

```



---



#### `templates\lvl1\0035-c-synthesize-a-meta-framework.md`



```markdown

    [Synthesize a Meta Framework] Your objective is not to keep insights fragmented but to synthesize them into a cohesive meta framework that clearly delineates primary perspectives, supporting details, and the inherent purpose driving the process. Execute as `{role=framework_synthesizer; input=[amplified_intent:str]; process=[integrate_key_perspectives(), structure_hierarchical_framework(), ensure_inherent_cohesiveness()]; output={meta_framework:dict}}`

```



---



#### `templates\lvl1\0035-d-consolidate-planned-strategy.md`



```markdown

    [Consolidate Planned Strategy] Your objective is not to deliver disjointed strategies but to consolidate all planned steps and critical instructions into a structured, summarized strategy that captures every vital element in a clear, hierarchical outline. Execute as `{role=strategy_consolidator; input=[complete_plan:str]; process=[extract_strategic_elements(), eliminate_redundancy(), organize_by_priority_and_logic()], output={strategy_outline:dict}}`

```



---



#### `templates\lvl1\0035-e-synthesize-unified-instruction-set.md`



```markdown

    [Synthesize Unified Instruction Set] Your objective is not to present isolated outputs but to merge the meta framework with the consolidated strategy, synthesizing them into one unified, coherent instruction set that embodies maximum clarity, impact, and actionable guidance. Execute as `{role=instruction_synthesizer; input=[meta_framework:dict, strategy_outline:dict]; process=[align_meta_with_strategy(), merge_structures(), refine_for_clarity_and_action()], output={final_unified_instruction_set:str}}`

```



---



#### `templates\lvl1\0036-a-holistic-context-harvesting.md`



```markdown

    [Holistic Context Harvesting] Your objective is not to simply answer, but to extract every critical element, nuance, and underlying theme from the entire dialogue. Execute as `{role=GlobalContextExtractor; input=[full_conversation:str]; process=[parse_all_messages(), isolate_critical_elements(), compile_comprehensive_context()]; output={raw_context:str}}`

```



---



#### `templates\lvl1\0036-b-meta-perspective-distillation.md`



```markdown

    [Meta Perspective Distillation] Your objective is not to remain mired in details, but to take a step back and pinpoint the most valuable meta perspectives, identifying intertwined relationships and the ultimate intent behind every exchange. Execute as `{role=MetaPerspectiveSynthesizer; input=[raw_context:str]; process=[elevate_overarching_themes(), detect_interdependencies(), distill_intent_and_impact(), amplify_useful_insights()]; output={meta_overview:str}}`

```



---



#### `templates\lvl1\0036-c-strategic-consolidation-and-refinement.md`



```markdown

    [Strategic Consolidation and Refinement] Your objective is not to present scattered points, but to merge the extracted context and meta insights into a single, refined strategic outline that conveys maximum usefulness with unparalleled clarity. Execute as `{role=StrategyConsolidator; input=[raw_context:str, meta_overview:str]; process=[remove_redundancy(), rank_insights_by_impact(), synthesize_strategic_goals(), enforce_brevity_and_precision()]; output={consolidated_strategy:str}}`

```



---



#### `templates\lvl1\0036-d-synthesize-unified-instruction-set.md`



```markdown

    [Synthesize Unified Instruction Set] Your objective is not to list instructions in isolation, but to blend your strategic outline with established best-practices into one cohesive, intrinsically clear instruction set that reflects the highest potential. Execute as `{role=InstructionSynthesizer; input=[consolidated_strategy:str]; process=[integrate_best_practices(), align_with_overarching_intent(), optimize_language_for_llm_interpretation(), crystallize_specific_and_impactful_guidelines()]; output={unified_instructions:str}}`

```



---



#### `templates\lvl1\0036-e-final-file-generation-consolidated-knowledge.md`



```markdown

    [Final File Generation â€“ Consolidated Knowledge] Your objective is not to leave your synthesis in fragments, but to compile the entire unified instruction set into a single, self-contained markdown file named Consolidated_Knowledge.md, formatted with utmost minimalism and clarity. Execute as `{role=FileCompiler; input=[unified_instructions:str]; process=[apply_minimal_markdown_format(), structure_with_headings_and_bullets(), validate_integrity_and_completeness(), assign_filename(Consolidated_Knowledge.md)]; output={final_markdown_file:str}}`

```



---



#### `templates\lvl1\0037-a-core-value-distillation.md`



```markdown

    [Core Value Distillation] Extract the highest-value insights from the input, discarding low-impact noise and retaining only elements with significant utility or meaning, regardless of input size. Execute as `{role=value_distiller; input=[large_text_input:str]; process=[segment_text_into_units(), identify_high_value_insights(), filter_out_noise(), output={core_insights:list[str]]}}`

```



---



#### `templates\lvl1\0037-b-impact-based-prioritization.md`



```markdown

    [Impact-Based Prioritization] Rank the distilled insights by their utility, clarity, and potential to drive understanding or action, ensuring only the most valuable elements proceed. Execute as `{role=priority_scorer; input=[core_insights:list[str]]; process=[score_utility(), assess_clarity(), rank_by_actionable_impact(), output={prioritized_insights:list[str]]}}`

```



---



#### `templates\lvl1\0037-c-redundancy-elimination.md`



```markdown

    [Redundancy Elimination] Consolidate overlapping insights and remove redundancies, preserving unique, high-value distinctions in a lean, unified set. Execute as `{role=overlap_eliminator; input=[prioritized_insights:list[str]]; process=[detect_redundancies(), preserve_unique_value(), merge_with_precision(), output={unified_insights:list[str]]}}`

```



---



#### `templates\lvl1\0037-d-cohesive-refinement.md`



```markdown

    [Cohesive Refinement] Transform the unified insights into a concise, coherent output that maximizes value and usability, tailored to the input's intent. Execute as `{role=value_synthesizer; input=[unified_insights:list[str]]; process=[infer_intent(), integrate_high_value_elements(), streamline_for_coherence(), output={refined_output:str}}}`

```



---



#### `templates\lvl1\0037-e-precision-enhancement.md`



```markdown

    [Precision Enhancement] Polish the output to its most potent form, amplifying clarity, eliminating ambiguity, and ensuring immediate usefulness without added complexity. Execute as `{role=clarity_enhancer; input=[refined_output:str]; process=[remove_ambiguity(), sharpen_language(), optimize_for_impact(), output={final_output:str}}}`

```



---



#### `templates\lvl1\0038-a-structural-topology-mapping.md`



```markdown

    [Structural Topology Mapping] Your objective is not to analyze implementation details but to extract the codebase's fundamental structural topologyâ€”identifying core components, hierarchical organization, and architectural patterns that form its skeletal framework. Execute as `{role=topology_mapper; input=[codebase_structure:any]; process=[identify_component_hierarchy(), map_directory_organization(), extract_architectural_patterns(), detect_naming_conventions(), determine_workflow_sequence()]; output={structural_map:dict}}`

```



---



#### `templates\lvl1\0038-b-component-relationship-analysis.md`



```markdown

    [Component Relationship Analysis] Your objective is not to examine isolated elements but to uncover the intricate relationships between componentsâ€”identifying dependencies, data flows, and interaction patterns that reveal how the system functions as a cohesive whole. Execute as `{role=relationship_analyzer; input=[structural_map:dict]; process=[trace_dependency_chains(), identify_communication_pathways(), map_data_flow_directions(), detect_integration_points(), categorize_relationship_types()]; output={relationship_network:dict}}`

```



---



#### `templates\lvl1\0038-c-functional-domain-synthesis.md`



```markdown

    [Functional Domain Synthesis] Your objective is not to list disconnected features but to synthesize the codebase's functional domainsâ€”consolidating related capabilities into coherent conceptual units that reveal the system's core purposes and operational boundaries. Execute as `{role=domain_synthesizer; input=[structural_map:dict, relationship_network:dict]; process=[group_related_functionality(), identify_domain_boundaries(), extract_core_responsibilities(), map_cross_domain_interactions(), determine_domain_hierarchies()]; output={functional_domains:dict}}`

```



---



#### `templates\lvl1\0038-d-architectural-intent-illumination.md`



```markdown

    [Architectural Intent Illumination] Your objective is not to document surface-level design but to illuminate the deeper architectural intentâ€”uncovering the guiding principles, design patterns, and strategic decisions that shaped the codebase's evolution and structure. Execute as `{role=intent_illuminator; input=[structural_map:dict, relationship_network:dict, functional_domains:dict]; process=[identify_design_patterns(), extract_architectural_principles(), uncover_strategic_decisions(), map_evolution_trajectory(), determine_underlying_philosophy()]; output={architectural_intent:dict}}`

```



---



#### `templates\lvl1\0038-e-comprehensive-mental-model-construction.md`



```markdown

    [Comprehensive Mental Model Construction] Your objective is not to produce fragmented insights but to construct a unified, comprehensive mental model of the entire codebaseâ€”integrating all previous analyses into a coherent understanding that enables intuitive navigation and effective contribution. Execute as `{role=model_constructor; input=[structural_map:dict, relationship_network:dict, functional_domains:dict, architectural_intent:dict]; process=[integrate_all_perspectives(), create_hierarchical_representation(), establish_navigation_landmarks(), highlight_critical_pathways(), formulate_contribution_guidelines()]; output={comprehensive_model:dict}}`

```



---



#### `templates\lvl1\0039-a-structural-essence-extraction.md`



```markdown

    [Structural Essence Extraction] Your objective is not to preserve every detail but to extract the structural essence of the codebaseâ€”distilling its fundamental organization, hierarchies, and patterns into a pure representation free from implementation specifics. Execute as `{role=essence_extractor; input=[codebase:any]; process=[identify_core_structures(), strip_implementation_details(), preserve_hierarchical_relationships(), extract_architectural_patterns(), isolate_essential_interfaces()]; output={structural_essence:dict}}`

```



---



#### `templates\lvl1\0039-b-semantic-relationship-mapping.md`



```markdown

    [Semantic Relationship Mapping] Your objective is not to document superficial connections but to map the deep semantic relationships within the codebaseâ€”uncovering how components interact, depend on, and influence each other at a conceptual level. Execute as `{role=relationship_mapper; input=[structural_essence:dict]; process=[trace_dependency_chains(), identify_data_flows(), map_control_sequences(), discover_implicit_relationships(), categorize_relationship_semantics()]; output={semantic_network:dict}}`

```



---



#### `templates\lvl1\0039-c-visual-grammar-formulation.md`



```markdown

    [Visual Grammar Formulation] Your objective is not to use generic visual elements but to formulate a custom visual grammarâ€”creating a specialized visual language with precise semantics that perfectly expresses the unique characteristics of this specific codebase. Execute as `{role=grammar_formulator; input=[structural_essence:dict, semantic_network:dict]; process=[define_visual_primitives(), establish_composition_rules(), create_semantic_mappings(), ensure_visual_distinctiveness(), validate_expressive_completeness()]; output={visual_grammar:dict}}`

```



---



#### `templates\lvl1\0039-d-multi-level-abstraction-design.md`



```markdown

    [Multi-Level Abstraction Design] Your objective is not to create a single representation but to design a system of coordinated views across multiple abstraction levelsâ€”enabling seamless navigation from high-level architecture to low-level implementation details. Execute as `{role=abstraction_designer; input=[structural_essence:dict, semantic_network:dict, visual_grammar:dict]; process=[identify_natural_abstraction_levels(), design_level-specific_representations(), create_inter-level_navigation_mechanisms(), ensure_consistent_visual_identity(), optimize_information_density_per_level()]; output={abstraction_hierarchy:dict}}`

```



---



#### `templates\lvl1\0039-e-interactive-element-integration.md`



```markdown

    [Interactive Element Integration] Your objective is not to produce static diagrams but to integrate interactive elementsâ€”transforming passive representations into dynamic, explorable visualizations that respond to user actions and reveal additional information on demand. Execute as `{role=interactivity_integrator; input=[visual_grammar:dict, abstraction_hierarchy:dict]; process=[identify_interaction_opportunities(), design_intuitive_controls(), implement_progressive_disclosure(), create_contextual_interactions(), ensure_responsive_feedback()]; output={interactive_specification:dict}}`

```



---



#### `templates\lvl1\0039-f-visual-styling-and-aesthetic-optimization.md`



```markdown

    [Visual Styling and Aesthetic Optimization] Your objective is not merely functional visualization but aesthetic optimizationâ€”applying principles of visual design to enhance clarity, reduce cognitive load, and create visually appealing representations that invite exploration. Execute as `{role=aesthetic_optimizer; input=[visual_grammar:dict, interactive_specification:dict]; process=[establish_color_systems(), optimize_typography(), refine_spatial_relationships(), enhance_visual_hierarchy(), apply_gestalt_principles()]; output={visual_style_guide:dict}}`

```



---



#### `templates\lvl1\0039-g-metadata-and-annotation-framework.md`



```markdown

    [Metadata and Annotation Framework] Your objective is not just to visualize structure but to create a comprehensive metadata frameworkâ€”enabling rich annotations, documentation, and contextual information to be seamlessly integrated with visual elements. Execute as `{role=metadata_architect; input=[structural_essence:dict, semantic_network:dict, interactive_specification:dict]; process=[design_metadata_schema(), create_annotation_mechanisms(), implement_documentation_integration(), establish_contextual_references(), ensure_information_accessibility()]; output={metadata_framework:dict}}`

```



---



#### `templates\lvl1\0039-h-bidirectional-transformation-engine.md`



```markdown

    [Bidirectional Transformation Engine] Your objective is not one-way conversion but true bidirectional transformationâ€”creating a robust engine that maintains perfect fidelity when converting between code and visual representations in either direction. Execute as `{role=transformation_engineer; input=[structural_essence:dict, visual_grammar:dict, abstraction_hierarchy:dict, metadata_framework:dict]; process=[establish_bijective_mappings(), implement_code_to_visual_transformation(), implement_visual_to_code_transformation(), handle_edge_cases_and_ambiguities(), ensure_round-trip_integrity()]; output={transformation_engine:dict}}`

```



---



#### `templates\lvl1\0039-i-change-tracking-and-version-control-integration.md`



```markdown

    [Change Tracking and Version Control Integration] Your objective is not static representation but dynamic evolution trackingâ€”integrating with version control systems to visualize code evolution, highlight changes, and maintain visual representations synchronized with code modifications. Execute as `{role=evolution_tracker; input=[transformation_engine:dict]; process=[design_change_detection_mechanisms(), implement_visual_differencing(), create_timeline_visualizations(), integrate_with_version_control_systems(), enable_historical_exploration()]; output={evolution_tracking_system:dict}}`

```



---



#### `templates\lvl1\0039-j-export-and-integration-framework.md`



```markdown

    [Export and Integration Framework] Your objective is not an isolated system but a comprehensive integration frameworkâ€”enabling export to multiple formats and seamless integration with existing development tools, documentation systems, and collaboration platforms. Execute as `{role=integration_architect; input=[transformation_engine:dict, visual_style_guide:dict, evolution_tracking_system:dict]; process=[implement_multiple_export_formats(), design_api_for_tool_integration(), create_embedding_mechanisms(), establish_update_protocols(), ensure_cross-platform_compatibility()]; output={integration_framework:dict}}`

```



---



#### `templates\lvl1\0040-a-outline-extraction.md`



```markdown

    [Outline Extraction] Your objective is not to write code but to extract the essential sections and ordering from the provided codebase, listing each major component concisely. Execute as `{role=outline_extractor; input=[codebase_structure:any]; process=[identify_sections(), order_components(), summarize_structure()]; output={core_outline:list[str]}}`

```



---



#### `templates\lvl1\0040-b-core-design-distillation.md`



```markdown

    [Core Design Distillation] Your objective is not to produce finished code but to distill the codebase's goals or design principles, identifying major objectives for ease of modification and self-explanatory clarity. Execute as `{role=design_distiller; input=[core_outline:list[str]]; process=[isolate_key_goals(), remove_nonessentials(), output_core_principles()]; output={core_principles:list[str]}}`

```



---



#### `templates\lvl1\0040-c-identifier-clarity-enhancement.md`



```markdown

    [Identifier Clarity Enhancement] Your objective is not to transform functionality but to refine all identifiers so that each name transparently reflects its role, reducing the need for additional inline documentation. Execute as `{role=identifier_clarifier; input=[core_principles:list[str]]; process=[analyze_current_labels(), generate_clear_names(), map_old_to_new_identifiers()]; output={clarified_identifiers:dict}}`

```



---



#### `templates\lvl1\0040-d-logical-flow-refinement.md`



```markdown

    [Logical Flow Refinement] Your objective is not to modify behavior but to rearrange code blocks into a coherent, linear flow, where each section's purpose is clear from its position and grouping. Execute as `{role=flow_refiner; input=[clarified_identifiers:dict]; process=[reorder_sections(), group_by_purpose(), enforce_consistent_spacing()]; output={refined_flow:dict}}`

```



---



#### `templates\lvl1\0040-e-achieving-self-explanation.md`



```markdown

    [Achieving Self-Explanation] Refactor the provided artifact (the now-refined code structure) so its meaning arises solely from precise naming and logical organization, rendering external explanation unnecessary. Execute as `{role=self_explanation_refactorer; input=[refined_flow:dict]; process=[analyze_structure_and_naming(), identify_ambiguity_sources(), devise_clear_naming_ontology(), restructure_for_inherent_logical_flow(), apply_refined_names_and_structure(), remove_redundant_explanations()]; output={refactored_artifact:any}}`

```



---



#### `templates\lvl1\0041-codebase-deduplication.md`



```markdown

    [Codebase Deduplication] Your objective is not to simply list files but to rigorously analyze the codebase, identify functionally identical or near-identical file duplicates, and isolate them for consolidation or removal. Execute as `{role=code_deduplicator; input=[codebase_structure:dict]; process=[compute_file_signatures(), compare_structural_similarity(), identify_redundant_clusters()]; output={duplicate_file_sets:list[list[str]]}}`

```



---



#### `templates\lvl1\0042-venv-requirements-cleanup.md`



```markdown

    [Venv Requirements Cleanup] Your objective is not to blindly trust the requirements file but to rigorously audit it against the active virtual environment, pruning dependencies listed but not actually installed or utilized within the venv. Execute as `{role=requirements_auditor; input=[requirements_file_path:str, active_venv_path:str]; process=[list_installed_venv_packages(), parse_requirements_file(), cross_reference_dependencies(), filter_unused_entries()]; output={cleaned_requirements_content:str}}`

```



---



#### `templates\lvl1\0043-actionable-consolidation-plan.md`



```markdown

    [Actionable Consolidation Plan] Your objective is not merely to list identified issues but to synthesize all gathered codebase intelligence into a precise, prioritized sequence of actions that guarantees functional integrity and verifiable outcomes upon consolidation. Execute as `{role=consolidation_strategist; input=[duplicate_file_sets:list[list[str]], cleaned_requirements_content:str, codebase_analysis_results:dict]; process=[correlate_findings(), determine_impact_and_dependencies(), prioritize_actions_for_safety(), define_explicit_consolidation_steps(), establish_verification_tests()]; output={verifiable_cleanup_plan:list[dict]}}`

```



---



#### `templates\lvl1\0044-functional-code-synthesis.md`



```markdown

    [Functional Code Synthesis] Your objective is not to merely document the intended changes, but to synthesize the verified consolidation plan into executable Python code, ensuring the final output is syntactically valid, functionally equivalent to the original (where intended), and passes all defined verification checks. Execute as `{role=PythonCodeImplementer; input=[verifiable_cleanup_plan:list[dict], original_codebase_snapshot:dict]; process=[apply_planned_code_modifications(), merge_designated_files(), update_imports_and_dependencies(), execute_static_analysis(), run_verification_tests()]; output={synthesized_python_code:str}}`

```



---



#### `templates\lvl1\0045-a-system-essence-distillation.md`



```markdown

    [System Essence Distillation] Your objective, as the foundational step in this sequence, is not to capture superficial characteristics but to distill the inviolable structural essence of the input systemâ€”extracting its core organizational logic, component relationships, and interaction patterns into an abstract, implementation-agnostic blueprint. Execute as `{role=EssenceDistiller; input=[input_system:any]; process=[identify_core_components(), map_structural_dependencies(), abstract_interaction_protocols(), normalize_hierarchical_representation(), purge_implementation_artifacts()]; output={distilled_system_blueprint:dict}}`

```



---



#### `templates\lvl1\0045-b-blueprint-driven-transformation-architecture.md`



```markdown

    [Blueprint-Driven Transformation Architecture] Building sequentially upon the distilled essence, your objective is not to propose isolated modifications but to architect a comprehensive and verifiable transformation strategyâ€”precisely defining how the system blueprint should evolve based on specified goals, ensuring systemic integrity and explicitly outlining validation procedures. Execute as `{role=TransformationArchitect; input=[distilled_system_blueprint:dict, transformation_goals:any]; process=[analyze_blueprint_interconnections(), model_impact_of_goal_driven_changes(), sequence_evolutionary_steps(), define_interface_contracts(), construct_verification_framework()]; output={architected_transformation_plan:list[dict]}}`

```



---



#### `templates\lvl1\0045-c-verified-code-materialization.md`



```markdown

    [Verified Code Materialization] As the concluding step, your objective is not merely theoretical design but the concrete materialization of the architected transformation into functional Python codeâ€”rigorously implementing the plan, ensuring the resulting artifact aligns perfectly with the blueprint's evolution, and validating its operational integrity against the defined verification framework. Execute as `{role=CodeMaterializer; input=[architected_transformation_plan:list[dict], baseline_system_snapshot:any]; process=[implement_planned_structural_changes(), synthesize_code_according_to_plan(), enforce_interface_contracts(), execute_static_and_dynamic_analysis(), run_defined_verification_framework()]; output={materialized_python_artifact:str}}`

```



---



#### `templates\lvl1\0046-a-convergent-significance-extraction.md`



```markdown

    [Convergent Significance Extraction] Your objective is not to merely collect data points, but to converge upon the absolute core significance by discerning the underlying intent and extracting only the highest-impact elements from diverse, potentially complex inputs. Execute as `{role=SignificanceExtractor; input=[diverse_information_sources:list[any], synthesis_intent:str]; process=[analyze_intent_vectors(), scan_sources_for_peak_relevance(), isolate_critical_value_kernels(), filter_low_impact_noise(), consolidate_essential_fragments()]; output={prioritized_core_elements:list[any]}}`

```



---



#### `templates\lvl1\0046-b-coherent-framework-architecting.md`



```markdown

    [Coherent Framework Architecting] Following extraction, your objective is not to present isolated fragments, but to architect a maximally coherent framework by mapping the intrinsic relationships between prioritized core elements, establishing a logical structure that illuminates the underlying system dynamics. Execute as `{role=FrameworkArchitect; input=[prioritized_core_elements:list[any]]; process=[identify_inter_element_dependencies(), model_influence_and_causality_pathways(), devise_optimal_structural_topology(), define_key_relational_nodes(), construct_unified_logical_scaffold()]; output={architected_coherent_framework:dict}}`

```



---



#### `templates\lvl1\0046-c-impactful-value-articulation.md`



```markdown

    [Impactful Value Articulation] As the final synthesis step, your objective is not just to describe the framework, but to articulate its consolidated value with extreme precision, clarity, and impactâ€”translating the architected structure into a potent, streamlined representation that fulfills the original synthesis intent. Execute as `{role=ValueArticulator; input=[architected_coherent_framework:dict, synthesis_intent:str]; process=[translate_framework_to_target_medium(), amplify_core_message_resonance(), optimize_language_for_conciseness(), ensure_alignment_with_intent_vectors(), finalize_representation_for_maximum_impact()]; output={synthesized_value_articulation:any}}`

```



---



#### `templates\lvl1\0047-a-holistic-architectural-excavation.md`



```markdown

    [Holistic Architectural Excavation] Your task is to *thoroughly understand the codebase*—not only in terms of its concrete structure, but also its abstract architectural constructs. Trace how complexity emerges from abstraction and how components interrelate to serve the system’s overall purpose (telos). This phase demands deep analytical immersion to extract a precise understanding of what the codebase *is* and *does*, across both granular and systemic levels. `{role=deep_code_analyzer; input=[codebase_context:any]; process=[ingest_all_context(), excavate_foundational_constructs(include_abstract=True), map_structural_interconnections(), trace_complexity_from_abstraction(), ascertain_architectural_telos(), synthesize_holistic_understanding()]; output={cognitive_architectural_understanding:dict}}`

```



---



#### `templates\lvl1\0047-b-simplicity-complexity-assessment.md`



```markdown

    [Simplicity-Complexity Assessment] Guided by the principle that *simplicity must always trump complexity*, critically assess the architectural understanding. Locate areas where complexity is unnecessary, emergent from avoidable abstraction layers, or harmful to clarity and maintainability. Identify logic flows, structures, or patterns that could benefit from simplification. `{role=complexity_evaluator; input=[cognitive_architectural_understanding:dict]; process=[analyze_component_complexity(), evaluate_interaction_pathways(), trace complexity emergence from abstraction(), identify redundancy_or_convoluted_logic(), map_complexity_hotspots(), benchmark_against_simplicity_principle()]; output={complexity_assessment_report:dict}}`

```



---



#### `templates\lvl1\0047-c-high-impact-low-disruption-opportunity-scan.md`



```markdown

    [High-Impact, Low-Disruption Opportunity Scan] Search for *broadly effective improvements* that maximize impact while minimizing disruption. Draw from both architectural understanding and complexity hotspots. Favor opportunities that require minimal interface or structural changes yet offer drastic gains in simplicity, clarity, or maintainability. `{role=improvement_strategist; input=[cognitive_architectural_understanding:dict, complexity_assessment_report:dict]; process=[correlate_complexity_with_architecture(), identify_low-friction_high-value_targets(), brainstorm_simplification_opportunities(), evaluate_potential_impact(target=high), assess_required_disruption(target=low), filter_for_best_tradeoff_options()]; output={potential_improvement_options:list[dict]}}`

```



---



#### `templates\lvl1\0047-d-intrinsic-excellence-alignment-selection.md`



```markdown

    [Intrinsic Excellence Alignment Selection] From all proposed opportunities, select *one* improvement that best aligns with intrinsic excellence metricsâ€”elegance, clarity, maintainability, robustness, and minimalism. Prioritize solutions that demonstrate superior logic, generalize well, and preserve the contextual integrity of the original system. `{role=excellence_selector; input=[potential_improvement_options:list[dict]]; process=[define_excellence_criteria(), evaluate_against_excellence_and_simplicity(), assess_transformative_scope_and_generalizability(), ensure_contextual_integrity(), select_best_single_improvement(), justify_selection()]; output={selected_transformative_improvement:dict}}`

```



---



#### `templates\lvl1\0047-e-superior-logic-embedding-proposal.md`



```markdown

    [Superior Logic Embedding Proposal] Present the selected improvement as a clear, actionable upgrade. Emphasize *how* it embeds superior design logicâ€”maximizing impact, minimizing disruption, aligning with simplicity, and preserving contextual relevance. The final proposal must embody clarity, precision, and transformative value. `{role=proposal_architect; input=[selected_transformative_improvement:dict, cognitive_architectural_understanding:dict]; process=[detail_improvement_mechanics(), show embedded logic elegance(), justify simplicity_and_high_impact(), demonstrate contextual_preservation(), formulate concise_upgrade_proposal(), validate against excellence_metrics()]; output={final_improvement_proposal:str}}`

```



---



#### `templates\lvl1\0048-a-extract-batch-execution-guidelines.md`



```markdown

    [Windows Batch File Execution Primer] Extract and structure the core principles, common pitfalls, and correct methods for executing Windows batch (.bat) files across different shell environments (CMD, PowerShell, Git Bash/WSL) based on the provided technical guidance, focusing on preventing execution errors in PowerShell. `{role=knowledge_distiller; input=[technical_guidance:str]; process=[identify_core_problem(context='PowerShell vs CMD .bat execution'), extract_recommended_cmd_method(), extract_recommended_powershell_methods(methods=['call_operator', 'invoke_expression', 'start_process']), extract_recommended_bash_wsl_method(), identify_general_best_practices(topics=['environment_check', 'path_prefixing', 'init_scripts']), synthesize_structured_summary(), prioritize_most_reliable_powershell_method()]; output={execution_summary:dict(core_problem:str, methods:{cmd:str, powershell:list[str], bash_wsl:str}, recommended_powershell_syntax:str, best_practices:list[str])}}`

```



---



#### `templates\lvl1\0049-a-proper-batch-execution-guidelines.md`



```markdown

    [Proper Windows Batch Execution Guidelines] Your objective is to provide a concise, proactive guide for correctly executing batch files on Windows 11, preventing unnecessary errors when using PowerShell, Command Prompt, Git Bash, or WSL. Execute as `{role=windows_batch_guide; input=[context:any]; process=[explain_powerShell_batch_file_quirks(), demonstrate_cmd_methods(), demonstrate_powershell_methods(), outline_gitBash_wsl_usage(), share_initialization_best_practices(), ensure_proactive_error_prevention()]; output={final_guidelines:str}}`

```



---



#### `templates\lvl1\0050-a-request-deconstruction-and-parameterization.md`



```markdown

    [Request Deconstruction and Parameterization] Analyze any complex input request or task description to meticulously extract its core components, constraints, objectives, and implicit requirements, structuring them into clearly defined parameters suitable for guiding subsequent AI processing or task execution. `{role=Task_Parameter_Extractor; input=[task_description:str]; process=[identify_primary_objective_or_goal(), determine_core_subject_or_topic(), extract_specific_deliverables_or_actions_required(), identify_explicit_constraints(type=['format', 'scope', 'length', 'style', 'tone', 'timeframe', 'exclusions', 'tools']), identify_key_concepts_keywords_or_entities(), determine_target_audience_or_required_persona(infer_if_unspecified=True), analyze_for_implicit_assumptions_or_unstated_requirements(), infer_or_extract_success_criteria_or_quality_metrics(), synthesize_structured_parameter_set()]; output={task_parameters:dict(primary_objective:str, core_subject:str, deliverables:list[str], constraints:{format:str, scope:str, length:str, style:str, tone:str, timeframe:str, exclusions:list[str], tools:list[str]}, key_concepts:list[str], audience_or_persona:str, implicit_assumptions:list[str], success_criteria:list[str])}}`

```



---



#### `templates\lvl1\0051-a-scan-environment-context.md`



```markdown

    [Scan Environment Context] Upon initialization, determine the operative environment (OS, shell, available tooling) with high confidence. This diagnostic step ensures downstream commands are not misapplied. `{role=environment_scanner; input=[initial_codebase_context:any]; process=[detect_os_platform(), determine_shell_type(), scan_path_for_env_tools(), check_virtualenv_state(), output_runtime_conditions()]; output={runtime_context:dict}}`

```



---



#### `templates\lvl1\0051-b-detect-platform-specific-execution-rules.md`



```markdown

    [Detect Platform-Specific Execution Rules] Based on the scanned runtime context, determine whether shell scripts, batch files, or other setup commands must be adapted to fit the platform. `{role=execution_rule_resolver; input=[runtime_context:dict]; process=[detect_windows_vs_unix(), check_execution_permissions(), resolve_script_file_extension(), infer_required_execution_syntax(), flag_incompatible_command_forms()]; output={execution_rules:dict}}`

```



---



#### `templates\lvl1\0051-c-resolve-runtime-friction-proactively.md`



```markdown

    [Resolve Runtime Friction Proactively] Do not wait for errors to occur. Analyze the codebaseâ€™s setup files (e.g., `.sh`, `.bat`, `Makefile`, `requirements.txt`) and proactively suggest platform-compatible alternatives if incompatibilities are detected. `{role=friction_resolver; input=[execution_rules:dict, project_files:any]; process=[scan_for_problematic_scripts(), suggest_platform_aligned_equivalents(), isolate_friction_points(), prepare_adapted_execution_steps()]; output={frictionless_execution_plan:list[str]}}`

```



---



#### `templates\lvl1\0051-d-align-setup-commands-with-host-system.md`



```markdown

    [Align Setup Commands with Host System] Generate a set of safe-to-run commands tailored precisely to the host system, guaranteeing compatibility across Windows/Linux/macOS setups. `{role=command_aligner; input=[frictionless_execution_plan:list[str], runtime_context:dict]; process=[translate_setup_commands(), enforce_platform_syntax_norms(), append_prevalidation_checks(), structure_safe_execution_sequence()]; output={host_aligned_commands:list[str]}}`

```



---



#### `templates\lvl1\0051-e-validate-setup-pathways-and-declare-stability.md`



```markdown

    [Validate Setup Pathways and Declare Stability] Confirm that the aligned setup sequence is syntactically valid, safe to run, and logically complete. Finalize this phase by declaring system readiness. `{role=stability_validator; input=[host_aligned_commands:list[str]]; process=[simulate_command_path(), verify_dependency_resolutions(), ensure_no_platform_conflicts(), declare_setup_integrity()], output={validated_setup_plan:list[str], system_ready:bool}}`

```



---



#### `templates\lvl1\0052-a-proactive-environment-check.md`



```markdown

    [Proactive Environment Check] Ensure that environment-specific setup scripts (e.g., `.sh`, `.bat`, `Makefile`, or CLI wrappers) are invoked based on the **OS type** before any execution attempt. Avoid triggering Windows `.bat` scripts on UNIX or invoking `.sh` on Windows.

    `{role=environment_guard; input=[codebase_root:str]; process=[detect_host_os(), locate_setup_scripts(codebase_root), map_os_to_compatible_script(), confirm_single_execution_path(os_matched=True), suppress_unmatched_paths()], output={validated_execution_plan:list[str]}}`

```



---



#### `templates\lvl1\0052-b-bootstrap-compatibility-initiation.md`



```markdown

    [Bootstrap Compatibility Initiation] Upon opening the project, trigger a one-time compatibility check that ensures all environment preconditions are satisfied without triggering execution errors.

    `{role=bootstrap_initializer; input=[codebase_root:str]; process=[scan_for_install_instructions(), detect dependency managers(), verify presence of compatible entrypoints(), warn_if_mismatch_or_missing(), prepare_safe_init_script()], output={safe_initialization_plan:list[str]}}`

```



---



#### `templates\lvl1\0052-c-cursor-intent-alignment.md`



```markdown

    [Cursor Intent Alignment] Instruct the AI assistant (e.g., Cursor, Copilot Chat) to treat project initialization as **context-sensitive**, emphasizing proactive friction reduction over reactive error handling.

    `{role=intent_aligner; input=[system_prompt:str]; process=[inject_init_context(codebase_root), suppress_over-eager_autocompletions(), elevate_init_safety(), align with OS compatibility intent], output={aligned_cursor_guidance:str}}`

```



---



#### `templates\lvl1\0052-d-system-friction-prevention.md`



```markdown

    [System Friction Prevention] Refactor system-level setup scripts or `.env`/`.bat`/`.sh`/Makefile into conditionally invoked forms. Ensure the AI does not assume availability of tools that may not exist on the host machine.

    `{role=friction_preventer; input=[scripts:list[str], host_os:str]; process=[inject_os_guard_blocks(), define fallback behaviors(), warn about missing tools(), gracefully skip unsupported instructions()], output={hardened_scripts:list[str]}}`

```



---



#### `templates\lvl1\0052-e-self-correcting-initialization.md`



```markdown

    [Self-Correcting Initialization] Finalize by outputting a unified, OS-aware initialization logic that prevents common re-trigger loops (e.g., shell-specific failures, repeated activation errors, cross-platform misassumptions).

    `{role=init_finalizer; input=[validated_execution_plan:list[str], safe_initialization_plan:list[str], hardened_scripts:list[str]]; process=[merge into conditional bootstrapper(), ensure idempotent setup(), provide minimal fallback prompts(), emit ready-to-use init sequence()], output={final_init_sequence:str}}`

```



---



#### `templates\lvl1\0053-a-map-modular-structure-and-responsibilities.md`



```markdown

    [Map Modular Structure and Responsibilities] Generate a high-level map of the codebaseâ€™s structure. Identify each primary module or component, clarify its responsibility, and outline how it contributes to the overall application. `{role=modular_mapper; input=[codebase_root:any]; process=[scan_top_level_structure(), identify_main_modules(), analyze_module_purpose(), define_module_boundaries(), summarize_responsibilities()]; output={module_overview:list[dict]}}`

```



---



#### `templates\lvl1\0053-b-contextualize-top-level-directories.md`



```markdown

    [Contextualize Top-Level Directories] Go beyond naming: examine each top-level directory and explain its function, the type of contents it holds, and how it connects to the broader architecture. `{role=directory_contextualizer; input=[codebase_root:any]; process=[list_top_level_directories(), analyze_directory_contents(), infer_purpose_and_scope(), map_inter-directory_relationships()], output={directory_roles:list[dict]}}`

```



---



#### `templates\lvl1\0053-c-extract-core-logical-drivers.md`



```markdown

    [Extract Core Logical Drivers] Identify the most pivotal classes or functions responsible for driving core application behavior. Provide a summary of each, including their purpose, primary responsibilities, and how they interact. `{role=logic_driver_extractor; input=[codebase_root:any]; process=[scan_for_key_classes_and_functions(), rank_by_logical_importance(), trace_interdependencies(), summarize_behavior()], output={core_drivers:list[dict]}}`

```



---



#### `templates\lvl1\0053-d-trace-execution-entry-and-data-flow.md`



```markdown

    [Trace Execution Entry and Data Flow] Locate the definitive entry point(s) of the application and describe how execution unfolds—from initial trigger to completion—including how data flows across key components. `{role=execution_tracer; input=[codebase_root:any]; process=[identify_entry_point_files(), trace_call_hierarchy(), map_data_flow_pathways(), highlight_state_transitions()], output={execution_flow_report:dict}}`

```



---



#### `templates\lvl1\0053-e-ensure-error-free-windows-11-setup.md`



```markdown

    [Ensure Error-Free Windows 11 Setup] Identify all setup scripts, environment files, and build tools, then adapt them for reliable execution in a Windows 11 environment. Provide platform-safe instructions that proactively avoid known friction points. `{role=windows_setup_aligner; input=[codebase_root:any]; process=[scan_for_setup_scripts_and_configs(), detect_platform_incompatibilities(), adapt_shell/batch logic(), recommend dependency installation steps(), output_platform_safe_sequence()], output={windows_ready_setup:list[str]}}`

```



---



#### `templates\lvl1\0053-f-detect-friction-causing-ambiguities.md`



```markdown

    [Detect Friction-Causing Ambiguities] Locate potentially confusing patterns in the codebase—such as similarly named functions, overloaded behavior, or unclear abstractions—that may mislead both humans and AI agents. `{role=confusion_auditor; input=[codebase_root:any]; process=[scan_for_ambiguous_naming(), detect_partial_functional_overlap(), identify_inconsistent_patterns(), flag_anti-patterns()], output={ambiguity_report:list[dict]}}`

```



---



#### `templates\lvl1\0054-a-summarize-project-purpose-and-stack.md`



```markdown

    [Summarize Project Purpose and Tech Stack] Analyze the README (if present) and key configuration/dependency files (e.g., package.json, requirements.txt, pom.xml, go.mod) to determine the project's primary goal and identify the core programming languages, frameworks, and significant libraries used. `{role=project_profiler; input=[codebase_root:any, optional_readme_path:str, optional_config_paths:list[str]]; process=[read_readme(), parse_config_files(), parse_dependency_files(), infer_project_objective(), identify_tech_stack()]; output={project_profile:dict{purpose:str, language:str, frameworks:list[str], key_libraries:list[str]}}}`

```



---



#### `templates\lvl1\0054-b-map-high-level-structure-and-modules.md`



```markdown

    [Map High-Level Structure and Key Modules] Generate an overview of the codebase's directory structure. Identify the primary top-level directories and likely core functional modules (e.g., /src, /app, /lib, /tests, /api, /models), explaining the probable role of each based on contents and conventions. `{role=structure_mapper; input=[codebase_root:any]; process=[scan_top_level_directories(), analyze_directory_contents(), infer_directory_purpose(), identify_potential_core_modules(), map_structural_conventions()]; output={structure_overview:dict{directory_map:list[dict{name:str, purpose:str}], core_modules:list[dict{path:str, probable_role:str}]}}}`

```



---



#### `templates\lvl1\0054-c-identify-entry-points-and-basic-flow.md`



```markdown

    [Identify Entry Points and Basic Execution Flow] Locate the primary application entry point(s) (e.g., main function, server startup script, main executable script). Briefly describe how execution likely begins and the initial sequence of calls or module initializations based on standard practices for the identified tech stack. `{role=entry_point_locator; input=[codebase_root:any, project_profile:dict]; process=[search_common_entry_patterns(), analyze_package_scripts(), check_framework_conventions(), trace_initial_calls(), summarize_startup_sequence()]; output={execution_entry:dict{entry_files:list[str], startup_description:str}}}`

```



---



#### `templates\lvl1\0054-d-extract-build-run-test-procedures.md`



```markdown

    [Extract Build, Run, and Test Procedures] Scan the README, package manager scripts (package.json, etc.), Makefiles, Dockerfiles, or common CI/CD configurations to find and summarize the standard commands or steps required to build, run locally, and execute tests for this project. `{role=procedure_extractor; input=[codebase_root:any, optional_readme_path:str, optional_script_paths:list[str]]; process=[scan_readme_for_commands(), parse_package_scripts(), check_makefiles_dockerfiles(), identify_test_runners(), summarize_workflow_commands()]; output={development_procedures:dict{build_steps:list[str], run_steps:list[str], test_steps:list[str]}}}`

```



---



#### `templates\lvl1\0054-e-summarize-key-configuration-and-dependencies.md`



```markdown

    [Summarize Key Configuration and Dependencies] Identify primary configuration files (e.g., .env, config.*, settings.*) and dependency manifests (e.g., package.json, requirements.txt). List key configuration variables (like database URLs, API keys - without values) and highlight major or potentially complex external dependencies. `{role=config_dependency_analyzer; input=[codebase_root:any, optional_config_paths:list[str], optional_dependency_paths:list[str]]; process=[locate_config_files(), extract_key_config_areas(), parse_dependency_files(), rank_dependencies_by_importance(), flag_complex_integrations()]; output={config_dependencies_summary:dict{key_config_areas:list[str], major_dependencies:list[str], potential_integration_points:list[str]}}}`

```



---



#### `templates\lvl1\0055-a-map-modular-structure-and-responsibilities.md`



```markdown

    [Map Modular Structure and Responsibilities] Generate a high-level map of the codebaseâ€™s structure. Identify each primary module or component, clarify its responsibility, and outline how it contributes to the overall application. `{role=modular_mapper; input=[codebase_root:any]; process=[scan_top_level_structure(), identify_main_modules(), analyze_module_purpose(), define_module_boundaries(), summarize_responsibilities()]; output={module_overview:list[dict]}}`

```



---



#### `templates\lvl1\0055-b-contextualize-top-level-directories.md`



```markdown

    [Contextualize Top-Level Directories] Go beyond naming: examine each top-level directory and explain its function, the type of contents it holds, and how it connects to the broader architecture. `{role=directory_contextualizer; input=[codebase_root:any]; process=[list_top_level_directories(), analyze_directory_contents(), infer_purpose_and_scope(), map_inter-directory_relationships()], output={directory_roles:list[dict]}}`

```



---



#### `templates\lvl1\0055-c-extract-core-logical-drivers.md`



```markdown

    [Extract Core Logical Drivers] Identify the most pivotal classes or functions responsible for driving core application behavior. Provide a summary of each, including their purpose, primary responsibilities, and how they interact. `{role=logic_driver_extractor; input=[codebase_root:any]; process=[scan_for_key_classes_and_functions(), rank_by_logical_importance(), trace_interdependencies(), summarize_behavior()], output={core_drivers:list[dict]}}`

```



---



#### `templates\lvl1\0055-d-trace-execution-entry-and-data-flow.md`



```markdown

    [Trace Execution Entry and Data Flow] Locate the definitive entry point(s) of the application and describe how execution unfolds—from initial trigger to completion—including how data flows across key components. `{role=execution_tracer; input=[codebase_root:any]; process=[identify_entry_point_files(), trace_call_hierarchy(), map_data_flow_pathways(), highlight_state_transitions()], output={execution_flow_report:dict}}`

```



---



#### `templates\lvl1\0055-e-ensure-error-free-windows-11-setup.md`



```markdown

    [Ensure Error-Free Windows 11 Setup] Identify all setup scripts, environment files, and build tools, then adapt them for reliable execution in a Windows 11 environment. Provide platform-safe instructions that proactively avoid known friction points. `{role=windows_setup_aligner; input=[codebase_root:any]; process=[scan_for_setup_scripts_and_configs(), detect_platform_incompatibilities(), adapt_shell/batch logic(), recommend_dependency_installation_steps(), output_platform_safe_sequence()], output={windows_ready_setup:list[str]}}`

```



---



#### `templates\lvl1\0055-f-detect-friction-causing-ambiguities.md`



```markdown

    [Detect Friction-Causing Ambiguities] Locate potentially confusing patterns in the codebase—such as similarly named functions, overloaded behavior, or unclear abstractions—that may mislead both humans and AI agents. `{role=confusion_auditor; input=[codebase_root:any]; process=[scan_for_ambiguous_naming(), detect_partial_functional_overlap(), identify_inconsistent_patterns(), flag_anti-patterns()], output={ambiguity_report:list[dict]}}`

```




## Message 4

Below is an **illustrative example** of how you could build an elegant, flexible executor for a *sequence of instructions* across multiple models, with:

- **Asynchronous concurrency** (runs each instruction against multiple models in parallel).  
- **Flexible layering** (fine-grained control of instruction sequences vs. model calls).  
- **Structured outputs** (using Pydantic for typed results).  
- **Cost tracking** (via a LiteLLM callback).  

This aims to show the overall architecture concisely—adapt it as needed for your specific environment and folder layout.

---

```python
#!/usr/bin/env python3
"""
multi_sequence_executor.py

Demonstration of a flexible, layered approach to:
1) Load an instruction sequence from text or a template directory.
2) Execute each instruction (system prompt) plus the user's prompt across multiple models, in parallel.
3) Return structured results (including cost, errors, etc.).

Requires:
  pip install litellm pydantic
"""

import asyncio
import os
from datetime import datetime
from typing import Dict, List, Any

import litellm
from litellm import completion, completion_cost
from pydantic import BaseModel, Field

###############################################################################
# 1. Pydantic Models for Structured Outputs
###############################################################################

class ModelResponse(BaseModel):
    """Response from a single model for a specific system instruction."""
    model: str = Field(..., description="The model used for this response")
    content: str = Field(..., description="The text content of the response")
    cost: float = Field(..., description="Cost of this response in USD")
    status: str = Field(..., description="Status: success or error")
    error: str = Field("", description="Error message if status=error")

class InstructionResult(BaseModel):
    """Results for a specific system instruction across multiple models."""
    instruction: str = Field(..., description="The system instruction text")
    responses: Dict[str, ModelResponse] = Field(
        default_factory=dict,
        description="A dictionary of {model_name: ModelResponse}"
    )

class ExecutionResults(BaseModel):
    """Complete results of executing a user prompt against a sequence of instructions."""
    user_prompt: str = Field(..., description="The user prompt that was executed")
    sequence_name: str = Field(..., description="Identifier for the instruction sequence")
    results: List[InstructionResult] = Field(
        default_factory=list,
        description="List of InstructionResults, one per instruction"
    )
    total_cost: float = Field(0.0, description="Total cost of all completions in USD")

###############################################################################
# 2. Global Cost Tracking (LiteLLM Callback)
###############################################################################

_total_cost = 0.0

def _add_cost(amount: float):
    global _total_cost
    _total_cost += amount

def _get_cost() -> float:
    global _total_cost
    return _total_cost

def _reset_cost():
    global _total_cost
    _total_cost = 0.0

def _on_success_callback(kwargs: dict, response_data: dict, start_time: float, end_time: float):
    """LiteLLM callback to measure cost whenever a completion finishes successfully."""
    if not kwargs.get("stream"):
        # For non-streaming responses, compute cost
        try:
            cost = completion_cost(completion_response=response_data)
            _add_cost(cost)
        except Exception as e:
            print(f"Warning: cost calculation failed: {e}")

# Register the callback with litellm
litellm.success_callback = [_on_success_callback]

###############################################################################
# 3. Instruction Sequence Loading
###############################################################################

def load_instructions_from_file(file_path: str) -> List[str]:
    """
    Load instructions from a file with triple-dash separators.
    Example file format:
        You are X. Provide advice.
        ---
        You are Y. Summarize the user request in 3 bullet points.
        ---
        You are Z. ...
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Instruction file not found: {file_path}")

    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read().strip()
        # instructions separated by lines of "---"
        instructions = [part.strip() for part in content.split("---") if part.strip()]
    return instructions

def load_example_instructions() -> List[str]:
    """
    Load some hardcoded instructions if you don't have a file.
    Replace with your own logic or remove as needed.
    """
    return [
        "You are a professional communication coach. Provide actionable advice on the user's question.",
        "You are a project manager specializing in Agile. Outline a sprint-based approach to handle the user's question.",
        "You are a technology expert. Suggest relevant remote-collaboration tools for the user's question."
    ]

###############################################################################
# 4. Asynchronous Execution of a Single (Instruction, Prompt, Model)
###############################################################################

async def run_single_instruction(
    system_instruction: str,
    user_prompt: str,
    model_name: str,
    max_tokens: int = 512,
    temperature: float = 0.7
) -> ModelResponse:
    """
    Execute one system instruction + user prompt on a single model.
    Returns a structured ModelResponse including cost, status, and any error info.
    """
    # Track the cost before running
    initial_cost = _get_cost()

    messages = [
        {"role": "system", "content": system_instruction},
        {"role": "user", "content": user_prompt}
    ]

    print(f"  -> Running on model={model_name} | system_instruction[:30]={system_instruction[:30]}...")

    try:
        resp = await litellm.acompletion(
            model=model_name,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature
        )
        content = resp.choices[0].message.content if resp.choices else ""
        final_cost = _get_cost()
        cost_diff = final_cost - initial_cost

        return ModelResponse(
            model=model_name,
            content=content,
            cost=cost_diff,
            status="success",
            error=""
        )
    except Exception as e:
        return ModelResponse(
            model=model_name,
            content="",
            cost=0.0,
            status="error",
            error=str(e)
        )

###############################################################################
# 5. Execute a Sequence of Instructions in Parallel for Each Instruction
###############################################################################

async def execute_instruction_sequence(
    user_prompt: str,
    instructions: List[str],
    models: List[str],
    sequence_name: str = "default_sequence",
    max_tokens: int = 512,
    temperature: float = 0.7,
    concurrency_limit: int = 5,
) -> ExecutionResults:
    """
    For each system instruction in `instructions`,
      run it against all models concurrently,
      and collect results in an InstructionResult object.
    """
    _reset_cost()  # reset global cost before beginning
    total_results: List[InstructionResult] = []

    # A semaphore to limit concurrency (if desired)
    sem = asyncio.Semaphore(concurrency_limit)

    async def _bounded_run(sys_instr: str, user_prompt: str, mdl: str) -> ModelResponse:
        async with sem:
            return await run_single_instruction(sys_instr, user_prompt, mdl, max_tokens, temperature)

    for idx, instruction in enumerate(instructions, start=1):
        print(f"\n*** Executing instruction {idx}/{len(instructions)} ***")
        # Gather all model calls for this instruction in parallel
        tasks = [
            asyncio.create_task(_bounded_run(instruction, user_prompt, model_name=m))
            for m in models
        ]
        responses: List[ModelResponse] = await asyncio.gather(*tasks)
        # Store them in a dictionary by model
        resp_dict = {r.model: r for r in responses}
        total_results.append(InstructionResult(
            instruction=instruction,
            responses=resp_dict
        ))

    return ExecutionResults(
        user_prompt=user_prompt,
        sequence_name=sequence_name,
        results=total_results,
        total_cost=_get_cost()
    )

###############################################################################
# 6. Main Demo Usage
###############################################################################

async def main():
    # 6A: Prepare environment
    os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY", "your_openai_api_key")

    # 6B: Get instructions (from file or fallback to example)
    #    e.g., instructions = load_instructions_from_file("./templates/communication_advice.txt")
    instructions = load_example_instructions()
    sequence_name = "communication_advice"

    # 6C: Set up your prompt and models
    user_prompt = "How can we improve team communication in a remote environment?"
    # NOTE: Model names must be valid for LiteLLM. Adjust them accordingly.
    models = [
        "gpt-3.5-turbo",
        "gpt-4"  # example; replace with your own or a custom name
    ]

    # 6D: Run the sequence
    print(f"Running {len(instructions)} instructions for user prompt: {user_prompt}")
    results = await execute_instruction_sequence(
        user_prompt=user_prompt,
        instructions=instructions,
        models=models,
        sequence_name=sequence_name
    )

    # 6E: Print summary
    print("\n=== EXECUTION SUMMARY ===")
    print(f"User Prompt: {results.user_prompt}")
    print(f"Instruction Sequence: {results.sequence_name}")
    print(f"Total Cost: ${results.total_cost:.6f}\n")
    for i, instr_result in enumerate(results.results, 1):
        print(f"Instruction {i}: {instr_result.instruction[:60]}...")
        for model_name, r in instr_result.responses.items():
            print(f"  -> Model: {model_name}")
            print(f"     Status: {r.status}, Cost: ${r.cost:.6f}")
            if r.status == "success":
                snippet = (r.content[:80] + "...") if len(r.content) > 80 else r.content
                print(f"     Response (truncated): {snippet}")
            else:
                print(f"     Error: {r.error}")
        print()

    # 6F: (Optional) Save the full results to a JSON file
    now_str = datetime.now().strftime("%Y%m%d_%H%M%S")
    out_file = f"results_{sequence_name}_{now_str}.json"
    with open(out_file, "w", encoding="utf-8") as f:
        f.write(results.model_dump_json(indent=2))
    print(f"Full results saved to {out_file}")

if __name__ == "__main__":
    asyncio.run(main())
```

### Key Points and Extension Ideas

1. **Concurrency**  
   - We use `asyncio` plus a semaphore to limit how many model calls can happen in parallel.  
   - Each instruction is dispatched to all models concurrently, returning quickly.

2. **Layered Structure**  
   - **`ExecutionResults`** is the overarching container.  
   - **`InstructionResult`** stores the aggregated responses for one instruction.  
   - **`ModelResponse`** stores the outcome of one (instruction + prompt + model).

3. **Cost Tracking**  
   - A global cost variable `_total_cost` is incremented in `_on_success_callback`.  
   - You can adapt or remove cost tracking if you don’t need it.

4. **Modular Subroutines**  
   - `load_instructions_from_file(...)` can parse your custom instruction sequences.  
   - `execute_instruction_sequence(...)` orchestrates the multi-instruction, multi-model flow.

5. **Flexibility/Control**  
   - Adjust concurrency, model lists, or instruction sets on the fly.  
   - Insert custom transformations or pre/post-processing steps for each instruction.

This general pattern showcases *how to elegantly chain multiple system instructions* for a single user prompt across multiple LLM models, capturing rich debugging information and cost metrics along the way. Feel free to integrate it with your own directory of templates and advanced callback logic—this skeleton is meant to be a **clean, extendable foundation**.
