# Medarbeidersamtale oppsummering

Conversation ID: 682da669-2bb0-8008-8435-77e1b9d10f3e

## Message 1



# SCENARIO



Du jobber som ansatt i NOV (National Oilwell Varco) og skal ha en medarbeidersamtale med en avdelingsleder om 10 minutter, her er notatene som avdelingsleder skrev fra forrige samtale:



Expectations:

  > What is the employee going to do?(Work goals, priorities & metrics for success) How is the employee going to do it? (competencies, behaviors & resources necessary for success, team culture)

  - Jobber med alt som går innunder kategorien 3D for simulatorer (modellering, reduksjon, teksturering, rigging og animasjon), med spesialisering innenfor rigging (oppsett av intern logikk for enklere animasjon av maskiner) og scripting (for automatisering av arbeidsoppgaver). - Jobber med et kontinuerlig mål om å konstant optimere arbeidsflyt og metodikk (for raskere og bedre produksjon). Er opptatt av at teamet som jobber med 3D har god kommunikasjon, forholder seg til identiske standarder, og at alle på teamet er oppdatert på nye teknikker og kunnskap (ved å lage og dele scripts, dokumentasjoner og videoleksjoner). - Har et mål om å ytterligere forbedre pipeline for produksjon (utviklingsprosjekt ift. templatesystem), samt forbedringer i simulerings-plugin for Mud-simulator (kommende utviklingsprosjekt).



Feedback:

  > Performance (accomplishments, improvement areas, and impact).Well-being (How is the employee doing? What’s working? What’s not working?)

  - Viktig at Jørn gir beskjed om det er for mye å gjøre. Vi har god kapasitet i avdelingen og det er enkelt å sette en eller flere til å jobbe med leveransen(e). Jørn lager en plan for når han skal være på kontoret. Begynner med en dag i uken. Liker utfordringer, jobber selvstendig og strukturert med omfattende og komplekse arbeidsoppgaver, er flink til å forbedre/forenkle prosesser/arbeidoppgaver og å sette dette i system. Trives godt i jobben og er fornøyd med kollegaer. - Jørn har fått hev/senk kontorpult på jobb for å kunne variere arbeidsstilling. Flyttes til Dvergsnes ved behov.



Growth:

  > Career growth (employee interests and aspirations for current & future roles). Learning and development opportunities for current or future roles: • What? (competency/skill/experience) • How? (new assignments, education, coaching, exposure)

  - Utvikle verktøy for å forberede og samle inn prosjektdata, tegninger og nummer, med mer. Jørn og Tore koordinerer dett og setter av tid for å kunne gjennomføre det. Holder seg oppdatert med selvstudie (nettforumer, youtube, artikler, med mer). - Mål om å fortsatt spesialisere seg innenfor eget fagfelt. Gir beskjed hvis det er kurs av interesse som Jørn ønsker å gjennomføre. Jørn tar kontakt med Stig Atle med hensyn på spesifikt kurs i Unreal.



---



# GROWTH



Her er informasjon om verktøyet som jeg laget siden forrige medarbeidersamtale (work in progress):



Initial concept:

```

## Konsept/Prosess



Verktøy for "automatisk" innhenting av dokumentasjon via. https://rigdoc.nov.com (prosjektinfo, tegninger, osv).



### 1. Jobbe utifra "R4511 - Internkontroll (119299318-Rev.0).xlsx"



    `"C:\Vault\Norway Simulation\Rigs\R4511-R5385-StenaIceMAX\docs\R4511-Internkontroll(119299318-Rev.0).xlsx"`

    ├── ---

    └── `C:\Vault\Norway Simulation\`

        └── `Rigs\R4511 - R5385 - Stena IceMAX\Documents`

            └── `IMPORTANT - Internal Audit - Qualification Record for Simulator 3D Development`

                │

                └── `R4511 - Internkontroll (119299318-Rev.0).xlsx`



### 2. Samler inn essensiell info



| Case No           | Equipment                                   | GA drawing             | GA rev.   | Verified by  | Comment        |

| ----------------- | ------------------------------------------- | ---------------------- | --------- | ------------ | -------------- |

| EQ-28209-104A     | Cylinder Hoisting Rig, 1250st, 48m          | 19129153-GAD           | 01        |              | 29273-106      |

| EQ-28209-104A     | Sheave Cluster Cylinder Rig 1250            | 19129152-GAD           | 02        |              | 29273-106      |

| EQ-28209-106A     | Top Drive, 1250t AC                         | 19140396-GAD           | 01        |              | 29273-106      |

| EQ-28209-120A     | Power Slip 1500 ton                         | DD-10141101-605        | 02        |              | 29273-106      |

| V6056             | Iron Roughneck-Hydratong MPT-200            | V6056-D1100-G0001      | 5         |              | 29273-106      |

| V6051             | Tubular Chute, Main                         | V6051-D1195-G0002      | 2         |              | 29273-107      |

| V6045             | Fingerboards                                | V6045-D1202-G0001      | 03A       |              | 29273-107      |

| V6042             | Hydraracker IV, Main                        | V6042-D1213-G0001      | 0         |              | 29273-107      |

| V6054             | Pipe guide, main under drillfloor           | V6054-D1194-G0002      | 3         |              | 29273-107      |

| EQ-28209-103A     | Elevated Backup Tong; EBT-150               | 1906283-GAD            | 03        |              | 29273-107      |



### 3. RigOffice søkestrenger



    `"search_urls"`

    ├── ---

    ├── `https://rigdoc.nov.com/search/advancedsearch?q=`

    │   │

    │   └── "EQ-28209-104A" -> `&CaseNo=EQ-28209-104A&DocTypeId=66`

    │

    └── `https://rigdoc.nov.com/search?q=`

        │

        ├── "19129153-GAD" -> `19129153-GAD&CaseType=Equipment`

        ├── "19129152-GAD" -> `19129152-GAD&CaseType=Equipment`

        ├── "19140396-GAD" -> `19140396-GAD&CaseType=Equipment`

        ├── "DD-10141101-605" -> `DD-10141101-605&CaseType=Equipment`

        ├── "V6056-D1100-G0001" -> `V6056-D1100-G0001&CaseType=Equipment`

        ├── "V6051-D1195-G0002" -> `V6051-D1195-G0002&CaseType=Equipment`

        ├── "V6045-D1202-G0001" -> `V6045-D1202-G0001&CaseType=Equipment`

        ├── "V6042-D1213-G0001" -> `V6042-D1213-G0001&CaseType=Equipment`

        ├── "V6054-D1194-G0002" -> `V6054-D1194-G0002&CaseType=Equipment`

        ├── "19066283-GAD" -> `19066283-GAD&CaseType=Equipment`

        └── "29273-106" -> `29273-106&CaseType=Task / Deliverable`

```



Intro:

```

### Primary Problem

Engineers waste significant time navigating poorly functioning applications to gather documents before work can begin. The actual 3D work isn't the bottleneck—it's the manual preparation process.



### Solution Approach

A Python-based utility that:

1. Automatically scrapes document metadata from RigOffice

2. Extracts file information from those documents

3. Downloads and organizes selected files based on user criteria



### Current State

Functional working prototype that:

- Uses a 3-step workflow (document metadata → file metadata → download)

- Stores intermediate results in JSON format

- Allows user intervention between steps

- Provides progress feedback

```



About:

```

# RigOfficeDownloader



Gather documentation from RigOffice without the tedious manual process of searching, selecting, and downloading files one by one.



---



## Purpose



Automated tool for downloading documents from RigOffice:



* Automate document retrieval from RigDoc with Python and Selenium.

* Prevent workflow interruptions and repetitive navigation steps.

* Minimize manual errors and standardize retrieval processes.

* Ensure employees focus on core tasks instead of busywork.

* Streamline data collection with consistent file naming and organization.



---



## Problem



Manual document retrieval in RigOffice is inefficient and error-prone, leading to:



* Repetitive tasks and workflow interruptions hinder focus and reduce productivity.

* Repeated time consumption for each project diverts attention from other tasks.

* Manual retrieval is inherently slow, tedious, and prone to errors.

* Engineers waste time on low-value tasks, delaying real work.



---



## Solution



Leverage the webinterface (https://rigdoc.nov.com) while using Python and Selenium to:



1. Fetch document metadata (`<rig>-a-docs.json`)

2. Export docs to Markdown (`<rig>-a-docs.md`): mark `item_include=true`

3. Import updated doc data back into JSON

4. Fetch file metadata for included docs

5. Export files to Markdown (`<rig>-b-files.md`): mark `item_download=true`

6. Import updated file data

7. Download files with subfolder paths derived from the generated names



---



## Result



* Significantly reduce the time spent on manual document retrieval.

* Eliminate repetitive navigation, clicking, and downloading tasks.

* Reduce manual effort, time, and errors in document retrieval.

* Eliminate repetitive navigation and download tasks.

* Maintain workflow continuity and minimize interruptions.



---



## Workflow



Documents → Files → Downloads:



1. Change / Configure

   - Input your rig number and search URLs.

   - Set up or modify filters (e.g., auto-include `*DRILL*FLOOR*` docs, exclude `*VOID*` docs).

2. Fetch Documents

   - Scrape document metadata from RigDoc URLs.

   - Data saved to JSON (`<rig>-a-docs.json`), all set to `item_include=false`.

3. Export & Review Docs

   - Creates `<rig>-a-docs.md`.

   - Manually set `item_include=true` for relevant docs.

4. Import Updated Docs

   - Reads user changes back into JSON.

5. Fetch File Metadata

   - For each included doc, scrapes associated files (saved in `<rig>-b-files.json`).

   - `item_download=false` by default.

6. Export & Review Files

   - Creates `<rig>-b-files.md`.

   - Set `item_download=true` for desired files.

7. Import Updated Files

   - Sync file selections back into JSON.

8. Download Files

   - Retrieves selected files, applying `item_generated_name` for naming.

   - `'/'` in `item_generated_name` spawns subfolders in `outputs/downloads/<rig>`.



---



## Setup



1. Run `py_venv_init.bat` to create Python environment

2. Run `RigOfficeDownloader.bat` to start the utility



---



## Usage



┌─────────────────────────────────────────────────────────────────────────┐

│ Interactive Menu Options:                                               │

│ [0] Change search parameters (rig number, URLs)                         │

│ [1] Configure filter chain                                              │

│ [2] Fetch docs (scrape initial data)                                    │

│ [3] Export docs (to Markdown for editing)                               │

│ [4] Import updated doc data                                             │

│ [5] Fetch files (prepare files for download)                            │

│ [6] Export files (to Markdown for editing)                              │

│ [7] Import updated file data                                            │

│ [8] Download files                                                      │

│                                                                         │

│ Run Modes:                                                              │

│ • `RigOfficeDownloader.bat --auto` (full automation)                    │

│ • `RigOfficeDownloader.bat --interactive` (menu-driven)                 │

│ • `RigOfficeDownloader.bat --config` (edit filters)                     │

└─────────────────────────────────────────────────────────────────────────┘



---



## Process



1. Fetch Documents

- The utility starts by scraping document metadata from predefined search URLs

- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory

- Each document entry includes metadata like title, document number, revision, etc.

- All documents are initially marked with item_include=False

- Each document gets an item_generated_name for better identification



2. Export Documents to Markdown

- The JSON data is exported to a Markdown table: <rig>-a-docs.md

- This allows the user to easily review and edit which documents to include

- The user is expected to edit the Markdown file and set item_include=true for desired documents



3. Import Updated Document Data

- After the user edits the Markdown file, the utility imports the changes back to the JSON file

- This updates which documents are marked for file retrieval



4. Fetch Files for Selected Documents

- For each document with item_include=true, the utility scrapes file metadata

- File data is saved to <rig>-b-files.json

- Each file is initially marked with item_download=False

- Files inherit the document's item_generated_name with additional identifiers



5. Export Files to Markdown

- The file data is exported to a Markdown table: <rig>-b-files.md

- The user reviews and edits which files to download by setting item_download=true



6. Import Updated File Data

- After editing, the utility imports the changes back to the JSON file

- This updates which files are marked for download



7. Download Selected Files

- Files with item_download=true are downloaded

- Files are named according to their item_generated_name + extension

- The utility supports creating subfolders based on '/' in the item_generated_name

- Files are saved to the outputs/downloads/<rig> directory



Interactive Menu

- The utility provides an interactive menu where the user can choose which steps to execute

- This allows for flexibility in the workflow, enabling the user to run specific steps as needed

- The user can also update the rig number and search URLs through this menu



---



### RigOfficeDownloader Utility Workflow



This utility is designed to automate the process of retrieving and downloading documents and files from the NOV RigDoc system (https://rigdoc.nov.com). The workflow is clearly defined in the docstring at the beginning of the main script and follows these steps:



1. Fetch Documents

- The utility starts by scraping document metadata from predefined search URLs

- Documents are saved to a JSON file: <rig>-a-docs.json in the outputs/data directory

- Each document entry includes metadata like title, document number, revision, etc.

- All documents are initially marked with item_include=False

- Each document gets an item_generated_name for better identification



2. Export Documents to Markdown

- The JSON data is exported to a Markdown table: <rig>-a-docs.md

- This allows the user to easily review and edit which documents to include

- The user is expected to edit the Markdown file and set item_include=true for desired documents



3. Import Updated Document Data

- After the user edits the Markdown file, the utility imports the changes back to the JSON file

- This updates which documents are marked for file retrieval



4. Fetch Files for Selected Documents

- For each document with item_include=true, the utility scrapes file metadata

- File data is saved to <rig>-b-files.json

- Each file is initially marked with item_download=False

- Files inherit the document's item_generated_name with additional identifiers



5. Export Files to Markdown

- The file data is exported to a Markdown table: <rig>-b-files.md

- The user reviews and edits which files to download by setting item_download=true



6. Import Updated File Data

- After editing, the utility imports the changes back to the JSON file

- This updates which files are marked for download



7. Download Selected Files

- Files with item_download=true are downloaded

- Files are named according to their item_generated_name + extension

- The utility supports creating subfolders based on '/' in the item_generated_name

- Files are saved to the outputs/downloads/<rig> directory



Interactive Menu

- The utility provides an interactive menu where the user can choose which steps to execute

- This allows for flexibility in the workflow, enabling the user to run specific steps as needed

- The user can also update the rig number and search URLs through this menu



Key Features

- Automatic document and file metadata scraping

- User-friendly Markdown editing interface

- Customizable file naming with item_generated_name

- Support for subfolder organization in downloads

- Deduplication of documents and files

- Configurable field ordering for JSON and Markdown exports



Technical Implementation

- Uses Selenium with Chrome WebDriver for web scraping

- Implements smart waiting strategies for page loading

- Handles browser sessions with proper cleanup

- Provides progress feedback during operations

- Sanitizes filenames for valid paths

```



---



# VERKTØY



```python

#!/usr/bin/env python

"""

Single-run utility for rigdoc scraping, now with:

 + "item_generated_name" for both docs and files,

 + custom column ordering for JSON & Markdown,

 + interpretation of '/' as subfolders in download paths.



Workflow (in one run):

 1) Fetch docs => <rig>-a-docs.json (with item_include=False, item_generated_name set).

 2) Export docs => <rig>-a-docs.md; prompt user to edit 'item_include'=true for desired docs, press Enter.

 3) Import updated docs from Markdown.

 4) Fetch files for docs => <rig>-b-files.json (with item_download=False, item_generated_name set).

 5) Export files => <rig>-b-files.md; prompt user to edit 'item_download'=true for desired files, press Enter.

 6) Import updated files from Markdown.

 7) Download any files with item_download=True, naming them from "item_generated_name" + extension,

    and placing them into subfolders based on '/' in "item_generated_name".

"""



# ======================

# Templates

# - List of prebuilt search URLs for targeted, rapid retrieval of documents

# - Filtered by equipment type, function, and rig number

# - Quick, targeted search URLs for simulator-related project documents

# ======================

# PROJECT_RIG_ID = "R5385"

PROJECT_RIG_ID = "R5225"

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId=R5225&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*G00*",



# PROJECTINFO_GAD: Main equipment drawings

PROJECTINFO_GAD = [

    # > Main Equipment Drawings

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*G00*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocNumber=*GAD-00*",

    # > Derrick/Drillfloor

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DRILL*FLOOR*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DERRICK*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EQUIPMENT*LAYOUT*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATION*VIEW*",

    # > Topdrive

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TOP*DRIVE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TRAVELLINGBLOCK*",

    # > Roughneck

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROUGHNECK*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRATONG*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MUDBUCKET*",

    # > Hydraracker

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*HYDRARACKER*",

    # > Fingerboard/Bellyboard/Rackingboard

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*BELLY*BOARD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*FINGER*BOARD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*BOARD*",

    # > Catwalk

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATWALK*",

    # > Mousehole

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MOUSE*HOLE*",

    # > Misc

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CATHEAD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SPIDER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ROTARY*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SLIPS*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*AHC*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*CMC*",

    #

    # > Guidearm

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*GUIDE*ARM*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*EBT*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*ELEVATED*BACKUP*TONG*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STANDBUILDING*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*TUBULAR*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RTX*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*GUIDE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*LIFTING*CYLINDER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*STAR*RACKER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*UTILITY*HANDLING*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RACKING*SYSTEM*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RAISED*SYSTEM*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*DFMA*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MPMA*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MANUPILATOR*ARM*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*PIPE*CHUTE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*RISER*CHUTE*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*SERVICE*BASKET*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=66&DocTitle=*MAINTENANCE*BASKET*",

]



# PROJECTINFO_ASM: Main assembly drawings

PROJECTINFO_ASM = [

    # > Equipment Main Drawings

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocNumber=*A000*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocNumber=*ASM-00*",

    # > Derrick/Drillfloor

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*DRILL*FLOOR*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*DERRICK*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*EQUIPMENT*LAYOUT*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*ELEVATION*VIEW*",

    # > Topdrive

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*TOP*DRIVE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*TRAVELLINGBLOCK*",

    # > Catwalk

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*CATWALK*",

    # > Roughneck

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*ROUGHNECK*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MUDBUCKET*",

    # > Hydraracker

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*HYDRARACKER*",

    # > Mousehole

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MOUSE*HOLE*",

    # > Guidearm

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*GUIDE*ARM*",

    # > Fingerboard/Bellyboard/Rackingboard

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*FINGER*BOARD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*BELLY*BOARD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RACKING*BOARD*",

    # > Misc

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*CATHEAD*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*SPIDER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*ROTARY*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*SLIPS*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*AHC*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*CMC*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*EBT*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*ELEVATED*BACKUP*TONG*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*STANDBUILDING*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*TUBULAR*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RTX*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*PIPE*GUIDE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*LIFTING*CYLINDER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*UTILITY*HANDLING*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*STAR*RACKER*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RACKING*SYSTEM*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RAISED*SYSTEM*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*DFMA*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MPMA*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MANUPILATOR*ARM*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*PIPE*CHUTE*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*RISER*CHUTE*",

    #

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*SERVICE*BASKET*",

    f"https://rigdoc.nov.com/search/rigsearch?q=&RigId={PROJECT_RIG_ID}&ViewTypeId=1&DocTypeGroupId=DWG&DocTypeId=279&DocTitle=*MAINTENANCE*BASKET*",

]



# List of general inclusion patterns for Nov 3D Simulator Projects

FILTERS_SIMULATOR_PATTERNS_INCLUDE = [

    "*DRILL*FLOOR*",

    "*ELEVATION*VIEW*",

    "*EQUIPMENT*LAYOUT*",

    "*G000*",

    "*GAD-00*",

    "*A000*",

    "*ASM-00*",

]



# List of general exclusion patterns for Nov 3D Simulator Projects

FILTERS_SIMULATOR_PATTERNS_SKIP = [

    "*Agitator*",

    "*BOP *",

    "*Bulk*Systems*",

    "*Cabinet*",

    "*Centrifuge*",

    "*Compressor*",

    "*Crane*",

    "*Cuttings*",

    "*Cyberbase*",

    "*DCDA *",

    "*DCI*",

    "*Deadline*",

    "*Gantry*Crane*",

    "*HPU*",

    "*HVAC*",

    "*Knuckle*Crane*",

    "*LMRP*",

    "*Manrider*",

    "*Mixer*",

    "*Mixing*",

    "*Mud *",

    "*Pumps*",

    "*Sdi*",

    "*Standby*",

    "*Station*",

    "*Tank *",

    "*Valve*",

    "*VOID*",

    "*Winch*",

    "*X-tree*",

    "*Yoke*",

]



# ======================

# Configuration

# ======================

CONFIG = {

    "rig_number": f"{PROJECT_RIG_ID}.020",

    # "search_urls": PROJECTINFO_GAD,

    "search_urls": PROJECTINFO_GAD,

    "show_progress": True,

    # Sequential filter chain - applied in order, later filters can override earlier ones

    "filters": [

        # [+] Docs:item_generated_name

        {

            "type": "docs",

            "enabled": True,

            "pattern": FILTERS_SIMULATOR_PATTERNS_INCLUDE,

            "match_field": "item_generated_name",

            "field": "item_include",

            "value": True,

            "comment": "",

        },

        # [-] Docs:item_generated_name

        {

            "type": "docs",

            "enabled": True,

            "pattern": FILTERS_SIMULATOR_PATTERNS_SKIP,

            "match_field": "item_generated_name",

            "field": "item_include",

            "value": False,

            "comment": "",

        },

        # [-] Docs:item_case_description

        {

            "type": "docs",

            "enabled": True,

            "pattern": FILTERS_SIMULATOR_PATTERNS_SKIP,

            "match_field": "item_case_description",

            "field": "item_include",

            "value": False,

            "comment": "",

        },

        # [-] Docs:item_status

        {

            "type": "docs",

            "enabled": True,

            "pattern": ["(VOID)"],

            "match_field": "item_case_description",

            "field": "item_include",

            "value": False,

            "comment": "",

        },

        # [+] Files:item_file_ext

        {

            "type": "files",

            "enabled": True,

            "pattern": ["*.pdf"],

            "match_field": "item_file_ext",

            "field": "item_download",

            "value": True,

            "comment": "",

        },

        # [-] File:item_case_description

        {

            "type": "files",

            "enabled": True,

            "pattern": FILTERS_SIMULATOR_PATTERNS_SKIP,

            "match_field": "item_case_description",

            "field": "item_download",

            "value": False,

            "comment": "",

        },

        # [-] Files:item_generated_name

        {

            "type": "files",

            "enabled": True,

            "pattern": ["*Partslist*"],

            "match_field": "item_generated_name",

            "field": "item_download",

            "value": False,

            "comment": "",

        },

    ],

}



# ======================

# Imports

# ======================

# Standard library imports

import json

import os

import re

import shutil

import time

import timeit

from contextlib import contextmanager

from urllib.parse import urljoin



# Third-party library imports

import colorama

import dateutil.parser

from ansimarkup import ansiprint

from bs4 import BeautifulSoup

from selenium import webdriver

from selenium.webdriver.chrome.options import Options

from selenium.webdriver.chrome.service import Service

from selenium.webdriver.support.ui import WebDriverWait



# (ADDED imports for smarter wait)

from selenium.webdriver.common.by import By

from selenium.webdriver.support import expected_conditions as EC

from selenium.common.exceptions import TimeoutException



from webdriver_manager.chrome import ChromeDriverManager



colorama.init()



# ======================

# Folders

# ======================

BASE_OUTPUT = os.path.abspath(os.path.join(os.getcwd(), "outputs"))

DATA_DIR    = os.path.join(BASE_OUTPUT, "data")

DL_DIR      = os.path.join(BASE_OUTPUT, "downloads")

for d in (BASE_OUTPUT, DATA_DIR, DL_DIR):

    os.makedirs(d, exist_ok=True)



# ======================

# Field Orders

# ======================

DEFAULT_DOC_FIELD_ORDER = [

    "item_include",

    "item_generated_name",

    "item_type",

    "item_case_no",

    "item_drawing_type",

    "item_doc_no",

    "item_date",

    "item_title",

    "item_case_description",

    "item_status",

    "item_revision",

    "item_responsible",

    "item_url",

]



DEFAULT_FILE_FIELD_ORDER = [

    "item_type",

    "item_generated_name",

    "item_case_no",

    "item_drawing_type",

    "item_title",

    "item_doc_no",

    "item_file_ext",

    "item_download",

    "item_file_size",

    "item_file_id",

    "item_date",

    "item_revision",

    "item_case_description",

    "item_status",

    "item_responsible",

    "item_url",

]



# ======================

# Logging / Helper

# ======================

# Colored console output functions

def cprint(x, color='#FFFFFF'):

    ansiprint(f'<fg {color}>{x}')



print_err  = lambda x: cprint(x, '#C92B65')  # Error - red

print_warn = lambda x: cprint(x, '#E6992B')  # Warning - orange

print_ok   = lambda x: cprint(x, '#1FCE46')  # Success - green

print_inf  = lambda x: cprint(x, '#FFFFFF')  # Info - white

print_sub  = lambda x: cprint(x, '#74705D')  # Subdued - gray



class SleepCondition:

    # Callable class for WebDriverWait that sleeps for specified seconds then returns True

    def __init__(self, s):

        self.s = s

    def __call__(self, _):

        # Parameter is required by WebDriverWait but not used

        time.sleep(self.s)

        return True



def reformat_date(s):

    # Convert date string to YYYY.MM.DD format or return original if not a valid date

    try:

        dt = dateutil.parser.parse(s)

        return dt.strftime('%Y.%m.%d')

    except:

        return s



def match_pattern(text, pattern):

    # Match text against glob pattern(s) (*=any chars, ?=single char), case-insensitive

    # Pattern can be a single string or a list of patterns (match any)

    if isinstance(pattern, list):

        # If any pattern in the list matches, return True

        return any(match_pattern(text, p) for p in pattern)



    # Single pattern matching

    regex_pattern = pattern.replace(".", "\\.").replace("*", ".*").replace("?", ".")

    return bool(re.search(f"^{regex_pattern}$", text, re.IGNORECASE))



def sanitize_filename(s):

    # Clean string for valid filename: replace invalid chars, preserve paths, handle quotes

    replacements = {

        "'": "", '"': "", '`': "",  # Remove quotes and backticks

        '\\': "_", '|': "_", '?': "_", '*': "_", '<': "_", '>': "_", ':': "_"  # Replace with underscore

    }



    # Apply replacements and clean up

    result = s

    for char, replacement in replacements.items():

        result = result.replace(char, replacement)



    result = ''.join(c for c in result if c.isprintable() or c == '/')  # Keep printable chars and path separators

    result = re.sub(r'\s+', ' ', result)  # Normalize spaces



    return result.strip()



def reorder_dict_keys(data_dict, field_order):

    # Return dict with keys ordered according to field_order if they exist in data_dict

    reordered = {}

    for key in field_order:

        if key in data_dict:

            reordered[key] = data_dict[key]

    return reordered



def remove_duplicates(data, exclude_keys=None, priority_key='item_download', priority_vals=None):

    # Deduplicate items with priority-based conflict resolution

    if not exclude_keys:

        exclude_keys=[]

    if not priority_vals:

        priority_vals=[]

    exset = set(exclude_keys)

    pmap  = {v:i for i,v in enumerate(priority_vals)}

    out   = {}

    for d in data:

        # Build hashable key ignoring exclude_keys

        filt = {k:v for k,v in d.items() if k not in exset}

        hkey = frozenset(filt.items())

        if hkey not in out:

            out[hkey] = d

        else:

            # pick the item with better priority

            if priority_key and priority_vals:

                newv = d.get(priority_key)

                oldv = out[hkey].get(priority_key)

                if (newv in pmap) and (oldv in pmap) and pmap[newv] < pmap[oldv]:

                    out[hkey] = d

                elif newv in pmap and oldv not in pmap:

                    out[hkey] = d

    return list(out.values())



def dedupe_and_save(new_items, path, field_order=None):

    # Merge items with existing JSON, deduplicate, reorder fields, and save

    if os.path.exists(path):

        try:

            with open(path,'r',encoding='utf-8') as f:

                old = json.load(f)

        except:

            old = []

    else:

        old = []

    merged = old + new_items

    merged = remove_duplicates(

        merged,

        exclude_keys=['item_download','item_generated_path'],

        priority_key='item_download',

        priority_vals=[False,True]

    )



    # If field_order is provided, reorder each item

    if field_order:

        merged = [reorder_dict_keys(d, field_order) for d in merged]



    tmp = path + '.temp'

    with open(tmp,'w',encoding='utf-8') as f:

        json.dump(merged,f,indent=2)

    os.replace(tmp,path)



    # Get relative path for display

    rel_path = os.path.relpath(path, os.getcwd())

    print_inf(f"Saved {len(merged)} items => {rel_path}")



def append_items(new_items, path, field_order=None):

    # Convenience wrapper for dedupe_and_save that checks for empty items

    if new_items:

        dedupe_and_save(new_items, path, field_order=field_order)



# ======================

# JSON <-> Markdown

# ======================

def json_to_md_table(json_path, md_path, field_order=None, filter_chain=None):

    # Export JSON to MD table with optional field ordering and sequential filter chain

    if not os.path.exists(json_path):

        print_err(f"No JSON found: {json_path}")

        return

    with open(json_path,'r',encoding='utf-8') as f:

        data = json.load(f)

    if not data:

        print_warn(f"No data in {json_path}")

        return



    # Determine item type from filename

    item_type = "docs" if "-a-docs." in json_path else "files"



    # Apply sequential filter chain

    if filter_chain and isinstance(filter_chain, list):

        for filter_idx, filter_config in enumerate(filter_chain):

            # Skip if filter is disabled or doesn't match the current item type

            if not filter_config.get("enabled", False) or filter_config.get("type") != item_type:

                continue



            pattern = filter_config.get("pattern", "")

            field = filter_config.get("field", "")

            value = filter_config.get("value", True)

            comment = filter_config.get("comment", "")

            match_field = filter_config.get("match_field", "item_generated_name")



            if pattern and field:

                filtered_count = 0

                for d in data:

                    field_value = d.get(match_field, "")

                    if field_value and match_pattern(field_value, pattern):

                        if d.get(field) != value:  # Only update if value is different

                            d[field] = value

                            filtered_count += 1



                if filtered_count > 0:

                    action = "Set" if value else "Cleared"



                    # Format pattern for display

                    if isinstance(pattern, list):

                        pattern_str = f"[{', '.join(pattern)}]"

                    else:

                        pattern_str = f"'{pattern}'"



                    # Show match field if not the default

                    match_field_str = f" in {match_field}" if match_field != "item_generated_name" else ""



                    print_ok(f"Filter #{filter_idx+1}: {action} {field}={value} for {filtered_count} items matching {pattern_str}{match_field_str}")

                    if comment:

                        print_sub(f"  → {comment}")



    # Determine field order if not provided

    if not field_order:

        colset = set()

        for d in data:

            colset.update(d.keys())

        field_order = sorted(colset)



    # Build Markdown table

    lines = []

    lines.append("| " + " | ".join(field_order) + " |")

    lines.append("|" + "|".join(["---"]*len(field_order)) + "|")



    for d in data:

        row = []

        for c in field_order:

            val = d.get(c,"")

            if isinstance(val,bool):

                val = str(val).lower()

            val = str(val).replace("|","\\|")  # Escape pipe chars

            row.append(val)

        lines.append("| " + " | ".join(row) + " |")



    with open(md_path,'w',encoding='utf-8') as f:

        f.write("\n".join(lines))

    # Get relative path for display

    rel_md_path = os.path.relpath(md_path, os.getcwd())

    print_ok(f"Exported {len(data)} items => {rel_md_path}")



def md_table_to_json(md_path, json_path, field_order=None):

    # Import data from Markdown table to JSON with optional field reordering

    if not os.path.exists(md_path):

        print_err(f"No Markdown file found: {md_path}")

        return

    with open(md_path,'r',encoding='utf-8') as f:

        lines=f.read().splitlines()

    table_lines=[ln.strip() for ln in lines if ln.strip().startswith("|")]

    if len(table_lines)<2:

        print_err("No valid table rows found in the Markdown.")

        return



    header=table_lines[0]

    data_lines=table_lines[2:]

    cols=[x.strip() for x in header.strip('|').split('|')]



    new_data=[]

    for ln in data_lines:

        rowcols=[x.strip() for x in ln.strip('|').split('|')]

        if len(rowcols)!=len(cols):

            print_warn(f"Skipping mismatch line:\n{ln}")

            continue

        d={}

        for i,c in enumerate(cols):

            val=rowcols[i]

            if val.lower()=="true":

                val=True

            elif val.lower()=="false":

                val=False

            d[c]=val

        new_data.append(d)



    # Optionally reorder each dict according to field_order

    if field_order:

        new_data = [reorder_dict_keys(item, field_order) for item in new_data]



    with open(json_path,'w',encoding='utf-8') as f:

        json.dump(new_data,f,indent=2)

    # Get relative path for display

    rel_json_path = os.path.relpath(json_path, os.getcwd())

    print_ok(f"Imported {len(new_data)} items => {rel_json_path}")



# ======================

# RigDocScraper

# ======================

class RigDocScraper:

    def __init__(self, rig, show_progress=False):

        self.rig=rig

        self.show=show_progress

        self.driver=None

        self.docs_path = os.path.join(DATA_DIR, f'{rig}-a-docs.json')

        self.files_path= os.path.join(DATA_DIR, f'{rig}-b-files.json')



    @contextmanager

    def browser_session(self, download_mode=False):

        # Context manager for browser sessions with consistent configuration

        profdir = None

        try:

            # Setup browser options

            opts = Options()

            opts.add_argument("--log-level=3")



            # Configure for downloads if needed

            if download_mode:

                appdata = os.environ.get("APPDATA", os.getcwd())

                profdir = os.path.join(appdata, "chromedriver_profile")

                os.makedirs(profdir, exist_ok=True)

                opts.add_argument(f"--user-data-dir={profdir}")



                dl_dir = os.path.join(DL_DIR, self.rig)

                os.makedirs(dl_dir, exist_ok=True)



                prefs = {

                    "download.default_directory": dl_dir,

                    "download.prompt_for_download": False,

                    "download.directory_upgrade": True,

                    "safebrowsing.enabled": True

                }

                opts.add_experimental_option("prefs", prefs)



            # Initialize and configure browser

            svc = Service(ChromeDriverManager().install())

            self.driver = webdriver.Chrome(service=svc, options=opts)



            wait_time = 2 if download_mode else 5

            WebDriverWait(self.driver, wait_time).until(SleepCondition(wait_time))

            self.driver.implicitly_wait(5 if download_mode else 10)



            if self.show:

                print_sub(f"[Browser] Started {'download-enabled ' if download_mode else ''}session")



            yield self.driver



        finally:

            # Clean up resources

            if self.driver:

                if self.show:

                    print_sub("[Browser] Closing session")

                self.driver.quit()

                self.driver = None



                if download_mode and profdir:

                    try:

                        shutil.rmtree(profdir)

                    except Exception as e:

                        if self.show:

                            print_warn(f"[Browser] Failed to clean up profile: {e}")



    def fetch_docs(self, urls):

        # Scrape docs to JSON with item_include=False and item_generated_name

        if not os.path.exists(self.docs_path):

            with open(self.docs_path,'w',encoding='utf-8') as f:

                json.dump([],f)



        with self.browser_session() as driver:

            for url in urls:

                if self.show:

                    print_sub(f"[DocScrape] => {url}")



                driver.get(url)



                # Decide how long to wait (max) for initial results to load

                is_rig = ("rigsearch" in url.lower()) or ("advancedsearch" in url.lower())

                max_wait = 10 if is_rig else 5  # "upper bound" wait

                wait_scroll = 5 if is_rig else 2



                # Instead of blindly sleeping, explicitly wait for search results

                try:

                    WebDriverWait(driver, max_wait).until(

                        EC.presence_of_element_located((By.CSS_SELECTOR, "a.search-result-link"))

                    )

                except TimeoutException:

                    print_warn(f"Waited up to {max_wait}s, but no 'search-result-link' found. Proceeding anyway...")



                # Now we do the scroll loop, same as before:

                inc = 0

                prev = driver.execute_script("return document.body.scrollHeight")

                while inc < 150:

                    inc += 1

                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

                    time.sleep(wait_scroll)

                    newh = driver.execute_script("return document.body.scrollHeight")

                    if self.show:

                        print_sub(f" -> Scroll {inc}, h={newh}")

                    if newh == prev:

                        break

                    prev = newh



                # Parse the page

                soup = BeautifulSoup(driver.page_source, "html.parser")

                row_elems = soup.find_all("a", class_="search-result-link")

                rows = [(td.find_parent("tr")).find_all("td") for td in row_elems]

                if self.show:

                    print_inf(f"Found {len(rows)} doc rows.")



                new_docs = []

                for row_cells in rows:

                    doc_title   = re.sub(r'[\n\r]+',' ', row_cells[1].a.text.strip())  # doc_title

                    sub_type    = row_cells[2].get_text(strip=True)                    # doc_sub_type

                    doc_no      = row_cells[3].get_text(strip=True)                    # doc_doc_no

                    responsible = row_cells[4].get_text(strip=True)                    # doc_responsible

                    case_no     = row_cells[5].get_text(strip=True)                    # doc_case_no

                    case_desc   = row_cells[6].get_text(strip=True)                    # doc_case_description

                    revision    = row_cells[7].get_text(strip=True)                    # doc_revision

                    status      = row_cells[8].get_text(strip=True)                    # doc_status

                    dt_         = reformat_date(row_cells[9].get_text(strip=True))     # doc_date

                    doc_url     = urljoin("https://rigdoc.nov.com/", row_cells[1].a.get("href"))



                    # handle void

                    if "document-void" in (row_cells[1].a.get("class", [])) or "*VOID*" in doc_title.upper():

                        status = "(VOID)"



                    # build item_generated_name

                    doc_generated_name = (

                        f"{doc_no.upper()}_REV{revision.upper()}--{case_no.upper()}-{doc_title.title()}"

                    ).replace("  ", " ")

                    doc_generated_name_sanitized = sanitize_filename(doc_generated_name)



                    new_docs.append({

                        "item_type": "doc",

                        "item_title": doc_title,

                        "item_drawing_type": sub_type,

                        "item_doc_no": doc_no,

                        "item_responsible": responsible,

                        "item_case_no": case_no,

                        "item_case_description": case_desc,

                        "item_revision": revision,

                        "item_status": status,

                        "item_date": dt_,

                        "item_url": doc_url,

                        "item_include": False,

                        "item_generated_name": doc_generated_name_sanitized

                    })



                # Save new_docs, reorder using DEFAULT_DOC_FIELD_ORDER

                append_items(new_docs, self.docs_path, field_order=DEFAULT_DOC_FIELD_ORDER)



        # Get relative path for display

        rel_docs_path = os.path.relpath(self.docs_path, os.getcwd())

        print_ok(f"Docs => {rel_docs_path}")



    def fetch_files(self):

        # For docs with item_include=True, fetch files with item_download=False

        if not os.path.exists(self.docs_path):

            print_err(f"No docs found: {self.docs_path}")

            return

        with open(self.docs_path,'r',encoding='utf-8') as f:

            all_docs=json.load(f)



        docs_for_files=[d for d in all_docs if d.get("item_type")=="doc" and d.get("item_include")==True]

        if not docs_for_files:

            print_warn("No docs with item_include==True => No files to fetch.")

            return



        if not os.path.exists(self.files_path):

            with open(self.files_path,'w',encoding='utf-8') as f:

                json.dump([],f)



        with self.browser_session() as driver:

            for i,doc in enumerate(docs_for_files,1):

                if self.show:

                    print_sub(f"[FileScrape] doc {i}/{len(docs_for_files)} => {doc.get('item_doc_no')}")

                doc_url = doc.get("item_url","")

                if not doc_url:

                    continue

                wp_url = doc_url.replace("nov.com/documents/","nov.com/wp/documents/")

                if "nov.com/wp/documents/" not in wp_url:

                    continue



                driver.get(wp_url)

                WebDriverWait(driver,5).until(SleepCondition(5))



                soup=BeautifulSoup(driver.page_source,"html.parser")

                frows=soup.select("div.revision-panel-body div.file-detail-row")

                flinks=[]

                for row in frows:

                    lk=row.select_one("div.file-detail a")

                    if lk and lk.get("href"):

                        flinks.append(urljoin("https://rigdoc.nov.com/", lk["href"]))



                doc_generated_name = doc.get("item_generated_name","DOC_UNKNOWN")

                new_files=[]

                for idx,flink in enumerate(flinks,1):

                    fapi=flink.replace("/_download","")

                    driver.get(fapi)

                    WebDriverWait(driver,0.5).until(SleepCondition(0.5))

                    raw=BeautifulSoup(driver.page_source,"html.parser").get_text(strip=True)

                    info=json.loads(raw)



                    fext=info.get("extension","")

                    fsz =info.get("filesize",0)

                    fsmb=f"{(fsz/(1024*1024)):.2f} MB"

                    ftitle=info.get("title","").strip()



                    # build item_generated_name

                    file_generated_name = (f"{doc_generated_name}.id.{idx}-{len(flinks)}.{ftitle.title()}").replace("  "," ")

                    if fext and not file_generated_name.lower().endswith(f".{fext.lower()}"):

                        file_generated_name = f"{file_generated_name}{fext.lower()}"

                    file_generated_name_sanitized = sanitize_filename(file_generated_name)



                    new_files.append({

                        "item_type": "file",

                        "item_title": ftitle,

                        "item_file_id": f"{len(flinks)}->({idx}/{len(flinks)}) FID:{info.get('fileId','-')}",

                        "item_file_ext": f"{fext.lower()}" if fext else "",

                        "item_file_size": fsmb,

                        "item_date": reformat_date(info.get("insertedDate","")),

                        "item_revision": doc.get("item_revision",""),

                        "item_responsible": info.get("insertedBy",""),

                        "item_status": info.get("lastChangedDate") or "-",

                        "item_case_no": doc.get("item_case_no",""),

                        "item_doc_no": doc.get("item_doc_no",""),

                        "item_drawing_type": doc.get("item_drawing_type",""),

                        "item_case_description": doc.get("item_case_description",""),

                        "item_url": flink,

                        "item_download": False,

                        "item_generated_name": file_generated_name_sanitized

                    })

                # Save new_files, reorder with DEFAULT_FILE_FIELD_ORDER

                append_items(new_files, self.files_path, field_order=DEFAULT_FILE_FIELD_ORDER)



        # Get relative path for display

        rel_files_path = os.path.relpath(self.files_path, os.getcwd())

        print_ok(f"Files => {rel_files_path}")



    def download_files(self):

        # Download files with item_download=True using item_generated_name for paths

        if not os.path.exists(self.files_path):

            print_err(f"No files JSON found at {self.files_path}")

            return

        with open(self.files_path, 'r', encoding='utf-8') as f:

            data = json.load(f)



        to_download = [x for x in data if x.get("item_type") == "file" and x.get("item_download") == True]

        if not to_download:

            print_warn("No files with item_download==True.")

            return



        download_dir = os.path.join(DL_DIR, self.rig)



        print_inf(f"Will download {len(to_download)} files => {download_dir}")



        with self.browser_session(download_mode=True) as driver:

            for i, fitem in enumerate(to_download, 1):

                furl = fitem.get("item_url", "")

                if not furl:

                    continue



                # item_generated_name might contain subfolder paths

                base_name = fitem.get("item_generated_name", f"file_{i}")

                if not base_name:

                    base_name = f"file_{i}"



                # Sanitize the entire base_name while preserving path separators

                base_name = sanitize_filename(base_name)



                parts = base_name.split("/")



                # Handle filename and extension

                if parts:

                    last_part = parts[-1]

                    ext = fitem.get("item_file_ext", "").lower()



                    # Add extension if needed

                    if ext and not last_part.lower().endswith(f"{ext}"):

                        ext = f".{ext}" if not ext.startswith(".") else ext

                        final_filename = last_part + ext

                    else:

                        final_filename = last_part



                    subfolders = parts[:-1]

                else:

                    # Fallback for empty path

                    ext = fitem.get("item_file_ext", "")

                    ext = f".{ext}" if ext and not ext.startswith(".") else ext

                    final_filename = f"file_{i}{ext}"

                    subfolders = []



                # create subfolders

                final_subdir = os.path.join(download_dir, *subfolders)

                os.makedirs(final_subdir, exist_ok=True)

                final_destination = os.path.join(final_subdir, final_filename)



                if os.path.exists(final_destination):

                    print_ok(f'Skipped (already exists): "{final_destination}"')

                    continue



                if self.show:

                    print_sub("-" * 80)

                    print_inf(f'[Download {i}/{len(to_download)}] => "{final_filename}"')



                start = timeit.default_timer()

                existing_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f)))

                driver.get(furl)

                time.sleep(1)

                done = False

                while (timeit.default_timer() - start) < 30:

                    time.sleep(1)

                    new_files = set(f for f in os.listdir(download_dir) if os.path.isfile(os.path.join(download_dir, f))) - existing_files

                    if new_files:

                        candidate = list(new_files)[0]

                        ext2 = os.path.splitext(candidate)[1].lower()

                        # Wait for .tmp or .crdownload to finalize

                        if ext2 not in [".tmp", ".crdownload"]:

                            src = os.path.join(download_dir, candidate)

                            try:

                                os.rename(src, final_destination)

                                done = True

                                print_ok(f'-> Downloaded "{final_destination}"')

                                break

                            except Exception as e:

                                print_warn(f'Failed to rename: {e}')

                if not done:

                    print_warn(f'Timed out: "{final_destination}"')



        print_inf("All file downloads attempted.")



# ======================

# Main workflow

# ======================

def run_script(cfg):

    # Initialize from config

    rig = cfg.get("rig_number", "R9999")

    urls = cfg.get("search_urls", [])

    sp = cfg.get("show_progress", False)

    filters = cfg.get("filters", [])



    scraper = RigDocScraper(rig, show_progress=sp)



    while True:

        doc_json = os.path.join(DATA_DIR, f'{rig}-a-docs.json')

        doc_md = os.path.join(DATA_DIR, f'{rig}-a-docs.md')

        file_json = os.path.join(DATA_DIR, f'{rig}-b-files.json')

        file_md = os.path.join(DATA_DIR, f'{rig}-b-files.md')



        # Function to configure sequential filter chain

        def configure_filters():



            while True:

                # Show current filters

                print_inf("\nCurrent filter chain:")

                if not filters:

                    print_sub("  No filters configured")

                else:

                    for i, f in enumerate(filters):

                        status = "ENABLED" if f.get("enabled", False) else "DISABLED"

                        type_str = f.get("type", "unknown")

                        pattern = f.get("pattern", "")

                        field = f.get("field", "")

                        value = f.get("value", True)

                        comment = f.get("comment", "")



                        # Format pattern for display

                        if isinstance(pattern, list):

                            pattern_str = f"[{', '.join(pattern)}]"

                        else:

                            pattern_str = f"'{pattern}'"



                        # Get match field (defaults to item_generated_name if not specified)

                        match_field = f.get("match_field", "item_generated_name")

                        match_field_str = f" in {match_field}" if match_field != "item_generated_name" else ""



                        color_fn = print_ok if f.get("enabled", False) else print_sub

                        color_fn(f"  [{i+1}] {status} - {type_str}: {pattern_str}{match_field_str} → {field}={value}")

                        if comment:

                            print_sub(f"      {comment}")



                # Menu options

                print_inf("\nFilter options:")

                print_inf("  [a] Add new filter")

                print_inf("  [e] Edit filter")

                print_inf("  [d] Delete filter")

                print_inf("  [t] Toggle filter")

                print_inf("  [m] Move filter")

                print_inf("  [s] Save and return")



                choice = input("\nEnter option: ").strip().lower()



                if choice == 'a':

                    # Add new filter

                    print_inf("\nAdd new filter:")

                    filter_type = input("  Type (docs/files): ").strip().lower()

                    if filter_type not in ['docs', 'files']:

                        print_err("  Invalid type. Must be 'docs' or 'files'")

                        continue



                    pattern_input = input("  Pattern (e.g., *g0001*, *.pdf) or comma-separated list: ").strip()

                    if not pattern_input:

                        print_err("  Pattern cannot be empty")

                        continue



                    # Handle comma-separated list of patterns

                    if "," in pattern_input:

                        pattern = [p.strip() for p in pattern_input.split(",") if p.strip()]

                    else:

                        pattern = pattern_input



                    match_field = input(f"  Field to match against [item_generated_name]: ").strip()

                    if not match_field:

                        match_field = 'item_generated_name'



                    field = input(f"  Field to set ({'item_include' if filter_type == 'docs' else 'item_download'}): ").strip()

                    if not field:

                        field = 'item_include' if filter_type == 'docs' else 'item_download'



                    value_input = input(f"  Value to set (true/false) [true]: ").strip().lower()

                    value = False if value_input in ['false', 'f', 'no', 'n', '0'] else True



                    comment = input("  Comment (optional): ").strip()



                    new_filter = {

                        "type": filter_type,

                        "enabled": True,

                        "pattern": pattern,

                        "match_field": match_field,

                        "field": field,

                        "value": value,

                        "comment": comment

                    }



                    filters.append(new_filter)

                    print_ok("  Filter added")



                elif choice == 'e':

                    # Edit filter

                    if not filters:

                        print_err("  No filters to edit")

                        continue



                    idx_input = input(f"  Filter number to edit (1-{len(filters)}): ").strip()

                    try:

                        idx = int(idx_input) - 1

                        if idx < 0 or idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid filter number")

                        continue



                    f = filters[idx]

                    print_inf(f"\nEditing filter #{idx+1}:")



                    filter_type = input(f"  Type (docs/files) [{f.get('type', '')}]: ").strip().lower()

                    if filter_type and filter_type in ['docs', 'files']:

                        f['type'] = filter_type



                    # Format current pattern for display

                    current_pattern = f.get('pattern', '')

                    if isinstance(current_pattern, list):

                        current_pattern_str = ", ".join(current_pattern)

                    else:

                        current_pattern_str = current_pattern



                    pattern_input = input(f"  Pattern [{current_pattern_str}]: ").strip()

                    if pattern_input:

                        # Handle comma-separated list of patterns

                        if "," in pattern_input:

                            f['pattern'] = [p.strip() for p in pattern_input.split(",") if p.strip()]

                        else:

                            f['pattern'] = pattern_input



                    match_field = input(f"  Field to match against [{f.get('match_field', 'item_generated_name')}]: ").strip()

                    if match_field:

                        f['match_field'] = match_field

                    elif 'match_field' not in f:

                        f['match_field'] = 'item_generated_name'



                    field = input(f"  Field to set [{f.get('field', '')}]: ").strip()

                    if field:

                        f['field'] = field



                    value_input = input(f"  Value (true/false) [{f.get('value', True)}]: ").strip().lower()

                    if value_input:

                        f['value'] = False if value_input in ['false', 'f', 'no', 'n', '0'] else True



                    comment = input(f"  Comment [{f.get('comment', '')}]: ").strip()

                    if comment or comment == '':  # Allow clearing comment

                        f['comment'] = comment



                    print_ok("  Filter updated")



                elif choice == 'd':

                    # Delete filter

                    if not filters:

                        print_err("  No filters to delete")

                        continue



                    idx_input = input(f"  Filter number to delete (1-{len(filters)}): ").strip()

                    try:

                        idx = int(idx_input) - 1

                        if idx < 0 or idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid filter number")

                        continue



                    del filters[idx]

                    print_ok("  Filter deleted")



                elif choice == 't':

                    # Toggle filter

                    if not filters:

                        print_err("  No filters to toggle")

                        continue



                    idx_input = input(f"  Filter number to toggle (1-{len(filters)}): ").strip()

                    try:

                        idx = int(idx_input) - 1

                        if idx < 0 or idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid filter number")

                        continue



                    filters[idx]['enabled'] = not filters[idx].get('enabled', False)

                    status = "enabled" if filters[idx]['enabled'] else "disabled"

                    print_ok(f"  Filter {status}")



                elif choice == 'm':

                    # Move filter

                    if len(filters) < 2:

                        print_err("  Need at least 2 filters to move")

                        continue



                    from_idx_input = input(f"  Filter number to move (1-{len(filters)}): ").strip()

                    try:

                        from_idx = int(from_idx_input) - 1

                        if from_idx < 0 or from_idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid filter number")

                        continue



                    to_idx_input = input(f"  New position (1-{len(filters)}): ").strip()

                    try:

                        to_idx = int(to_idx_input) - 1

                        if to_idx < 0 or to_idx >= len(filters):

                            raise ValueError()

                    except ValueError:

                        print_err("  Invalid position")

                        continue



                    if from_idx == to_idx:

                        print_sub("  No change needed")

                        continue



                    # Move the filter

                    filter_to_move = filters.pop(from_idx)

                    filters.insert(to_idx, filter_to_move)

                    print_ok(f"  Filter moved from position {from_idx+1} to {to_idx+1}")



                elif choice == 's':

                    # Save and return

                    break



                else:

                    print_err("  Invalid option")



            return True



        steps = [

            ("Change search parameters", None),

            ("Configure filter chain", configure_filters),

            ("Fetch docs (scrape initial data)", lambda: scraper.fetch_docs(urls)),

            ("Export docs (to Markdown for editing)", lambda: json_to_md_table(

                doc_json, doc_md, field_order=DEFAULT_DOC_FIELD_ORDER, filter_chain=filters

            )),

            ("Import updated doc data", lambda: md_table_to_json(doc_md, doc_json, field_order=DEFAULT_DOC_FIELD_ORDER)),

            ("Fetch files (prepare files for download)", scraper.fetch_files),

            ("Export files (to Markdown for editing)", lambda: json_to_md_table(

                file_json, file_md, field_order=DEFAULT_FILE_FIELD_ORDER, filter_chain=filters

            )),

            ("Import updated file data", lambda: md_table_to_json(file_md, file_json, field_order=DEFAULT_FILE_FIELD_ORDER)),

            ("Download files", scraper.download_files),

        ]



        print("\nChoose procedure(s) to run (space/comma-separated numbers, or q to quit):")

        for i, (desc, _) in enumerate(steps):

            print(f"  [{i}] {desc}")



        choice = input("Your choice: ").strip()



        if choice.lower() == 'q':

            print("Exiting.")

            break



        try:

            indices = [int(x.strip()) for x in choice.replace(',', ' ').split() if x.strip().isdigit()]

        except (ValueError, IndexError):

            print("Invalid selection. Please try again.")

            continue



        for i in indices:

            if i == 0:

                rig_input = input(f"  rig_number ({rig}): ").strip()

                if rig_input:

                    rig = rig_input



                urls_input = input(f"  search_urls ({urls}): ").strip()

                if urls_input:

                    urls = [u.strip() for u in urls_input.split(',') if u.strip()]



                # Create new scraper with updated configuration

                scraper = RigDocScraper(rig, show_progress=sp)

            elif 0 < i < len(steps):

                desc, action = steps[i]

                print_inf(f"\nRunning: {desc}")

                action()



        print_inf("\nSelected steps completed.\n")



if __name__=="__main__":

    run_script(CONFIG)

```



# OBJECTIVE



Din oppgave er å konsolidere arbeidet som jeg har gjort og skrive en melding som kan brukes som grunnlag for den nye medarbeidersamtalen.



Vennligst oppdater basert på følgende ytterligere kontekst:



Mitt liv:

- har ikke fått kontrol på reflux, så sliter en del med dette

- sliter med å holde hodet over vann

- sliter fortsatt mentalt, det vil jeg alltid gjøre - men akkurat nå er jeg knapt i stand til å ta vare på meg selv. når jeg mister refluksen så er det et symptom på at jeg ikke tar vare på meg selv, og det har ringvirkninger

- en konsekvens av å slite med å holde hodet over vann er at jeg sliter mer med uforutsigbarhet (og konsentrasjon generelt)



Kritikk:

- blir fornærmet og inntar forsvarsposisjon ved konstruktiv kritikk

- initiativ skal ikke stoppe hos deg

- når du som leder ikke ser *faktisk* hva de som gjør jobben gjør, så vil du heller ikke være istand til å forsvare de ansatte

- du er bindeleddet mellom vår avdeling og de som har oversikt over alle andre avdelinger, og når du ikke klarer å stå opp og argumentere for at vi har for lite folk. da er det ikke jeg som er urimelig og klager når det alltid bare forventes at vi skal produsere

- vet at du er en snill person og at du prøver å forstå, men situasjon krever noe annet (som leder over en hel avdeling)

- når avdelingen konstant er overarbeidet og *du* er den som forsvarer å *ikke ansette flere* så dreper det arbeidsmoral, det får en til å føle at du er en leder for de *over* deg fremfor de *under* deg

- hvis det bare handler om at *du* synes det er vanskelig å forsvare flere folk (ovenfor dine ledere), og *du* er den som setter det foten ved denne avgjørelsen



Status:

- verktøyet som jeg nå har laget for å automatisere innhenting av dokumentet er bare *èn* ting, hadde vi hatt flere folk kunne jeg ha brukt tiden på automatisering som firmaet *garantert* hadde vært tjent med. jeg synes det er demotiverende at jeg *viser* hva jeg kan gjøre, hva jeg har *lyst til å gjøre*, og jeg *forteller deg* at store deler av mine arbeidsoppgaver handler om å jobbe med systemer *som kunne ha vært automatisert* - at du *alikevel* står urokkelig ved denne posisjonen; så føler jeg at jeg *sløser bort min egen tid*.

- jeg har lyst til å utvikle meg, jeg har ikke lyst til å jobbe som en robot som bare skal "jobbe for å jobbe"


## Message 2

**Gathering insights**

I’m piecing together the user's achievements, challenges, and frustrations. This includes their progress on specialized tasks, personal mental health hurdles, and the need for more staff and better management to boost their energy and motivation.

**Highlighting key challenges**

Crafting a message in Norwegian, I'll address the user's health struggles, mental battles, staffing needs, automation issues, and unpredictability. Gathering all relevant aspects for a compassionate and comprehensive final message.

## Message 3

**Utkast til melding for medarbeidersamtale**

Hei [Leder],

Takk for at du setter av tid til en ny medarbeidersamtale. Her er en samlet oppsummering av status, fremdrift og utfordringer siden sist:

---

## 1. Arbeidsoppgaver og Leveranser
- Jeg fortsetter å jobbe med 3D-oppgaver knyttet til simulatorsystemer – modellering, reduksjon, teksturering, rigging og animasjon.  
- Jeg har et særskilt fokus på rigging (intern logikk for enklere animasjon av maskiner) og scripting (automatisering).  
- Jeg legger vekt på å optimalisere arbeidsflyt og metodikk i teamet, slik at vi får raskere og bedre produksjon.  
- Jeg sørger også for at teamet har gode standarder og retningslinjer, samt deler ny kunnskap gjennom skript, dokumentasjon og videoleksjoner.  
- Jeg jobber med å forbedre pipeline (templatesystem) og simulator-plugins (som for eksempel Mud-simulator).  

**Nyutviklet verktøy**  
- Siden forrige samtale har jeg utviklet et Python-basert verktøy, RigOfficeDownloader, for å automatisere innhenting av dokumenter fra RigDoc/RigOffice. Det reduserer den tidkrevende, manuelle prosessen med å navigere og laste ned filer én og én.  
- Verktøyet henter metadata, sorterer dokumenter, og laster ned ønskede filer på en organisert måte. Dette bidrar til å spare tid og minimere risiko for feil.

---

## 2. Tilbakemeldinger og Personlig Velvære
- Jeg setter pris på fleksibiliteten rundt å gi beskjed ved for stort arbeidspress. Det er også positivt at vi har kapasitet i avdelingen, og at jeg kan få avlastning når det trengs.  
- Jeg forsøker å komme inn minst én dag i uken på kontoret, men jeg sliter med helsen min. Jeg har fremdeles problemer med reflux, og generelt mentalt har jeg det svært tungt akkurat nå.  
- Når reflux-symptomene blusser opp, er det et signal på at jeg ikke tar vare på meg selv, og at jeg overveldes av situasjonen. Dette gjør at jeg har mindre overskudd til å håndtere uforutsigbarhet og holde konsentrasjonen oppe.
- Jeg har fått hev/senk-pult for bedre ergonomi, og kontorplass kan flyttes til Dvergsnes ved behov.

---

## 3. Kritikk og Bekymringer
- Jeg opplever at jeg ofte blir fornærmet og defensiv når jeg får konstruktiv kritikk. Dette er noe jeg jobber med og trenger at vi kan håndtere på en god og åpen måte.  
- Jeg savner at du som avdelingsleder har mer synlig innsikt i hva vi faktisk gjør. Uten den innsikten blir det vanskelig å forsvare teamet vårt mot stadig nye krav.  
- Jeg trenger at du ikke bare representerer ledelsen over oss, men også står opp for oss når vi sier at vi trenger flere folk for å unngå konstant overarbeid.  
- At avdelingen vår forblir underbemannet og at du forsvarer å ikke ansette flere, gjør at jeg opplever at du beskytter interessene til de over deg mer enn behovene vi har i teamet. Resultatet er at arbeidsmoralen svekkes.

---

## 4. Motivasjon og Ønske om Videreutvikling
- Verktøyet jeg har utviklet for dokumentautomatisering er ett eksempel på hva jeg kan gjøre for å effektivisere. Hadde vi hatt flere ansatte, kunne jeg investert enda mer tid i slike løsninger som potensielt sparer bedriften for store ressurser på sikt.  
- Det er demotiverende å se at jeg demonstrerer hva jeg kan, sier tydelig hva jeg har lyst til å drive med, og påpeker at mange av våre arbeidsoppgaver kunne vært automatisert – men at holdningen om ikke å ansette flere står like fast.  
- Jeg ønsker å utvikle meg videre, fremfor å jobbe som en «robot» der jeg bare utfører oppgaver uten rom for innovasjon. Jeg vil at bedriften skal utnytte kompetansen min og gi meg muligheter til å fortsette å spesialisere meg og lære mer (f.eks. kurs i Unreal).

---

## 5. Mulige Løsningsforslag og Veien Videre
1. **Bedre kapasitetsstyring:** Sørge for at vi faktisk utnytter den kapasiteten vi har i avdelingen, og få reell backing dersom det er behov for mer personell.  
2. **Større fokus på automasjon:** Jeg ønsker å formalisere mer tid til automatisering av oppgaver som i dag er repetitive. Dette vil gi både kortsiktige og langsiktige gevinster.  
3. **Tydelig og åpen kommunikasjon:** Ha flere kortere statusmøter for å avklare belastning, progresjon og behov. Det gjør det enklere for meg å si ifra når jeg har for mye på tallerkenen.  
4. **Tilpasset oppfølging:** Jeg setter pris på fleksibilitet rundt helsesituasjonen min, men jeg trenger mer systematisk støtte og forståelse for at jeg tidvis har svært redusert kapasitet.  
5. **Utvikling og læring:** Opprettholde muligheten til å ta kurs (f.eks. i Unreal) og tilrettelegge for at jeg kan bygge videre på den kompetansen som gagner selskapet.  

---

### Oppsummering
Jeg er motivert for å levere gode resultater og har vist at jeg kan effektivisere deler av arbeidsprosessen med utvikling av verktøy som RigOfficeDownloader. Samtidig står jeg i en utfordrende livssituasjon, noe som gjør belastning og uforutsigbarhet vanskeligere å håndtere. Jeg er avhengig av støtte fra ledelsen for å kunne lykkes – både ved å synliggjøre reelle bemanningsbehov og ved å sikre at vi ikke går konstant over grensen av hva teamet tåler.  

Jeg håper vi kan bruke denne samtalen til å finne gode, bærekraftige løsninger slik at jeg og teamet fortsetter å trives og levere på høyt nivå.

Vennlig hilsen,  
[Jørn]

## Message 4

Beskriv i en enkelt setning en kortfattet beskrivelse av verktøyet jeg har laget (RigOfficeDownloader)

## Message 5

RigOfficeDownloader er et Python-basert automatiseringsverktøy som henter, organiserer og laster ned relevante dokumenter og filer fra RigDoc/RigOffice, og slik effektiviserer en ellers manuell og tidkrevende prosess.
