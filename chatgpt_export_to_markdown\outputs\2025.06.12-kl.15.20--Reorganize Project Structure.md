# Reorganize Project Structure

Conversation ID: 684ad413-0460-8008-968e-062e6e67e63d

## Message 1

please transform this such that each stage is branched from the root rather than the scattered and inconsistent way it's currently manifested:

```

├── core

│   ├── __init__.py

│   ├── __main__.py

│   ├── catalog.py

│   ├── config.py

│   ├── executor.py

│   ├── models.py

│   ├── utils.py

│   └── validation.py

├── docs

│   ├── 01_INSTRUCTION_PATTERNS.md

│   ├── 02_DIRECTIONAL_VECTORS.md

│   ├── 03_TEMPLATE_SPECIFICATION.md

│   ├── 04_SYSTEM_ARCHITECTURE.md

│   ├── 05_EXECUTION_GUIDE.md

│   ├── 06_DEVELOPMENT_GUIDE.md

│   ├── 07_QUICK_REFERENCE.md

│   ├── README.md

│   ├── RulesForAI.md

│   └── RulesForAI.minified.md

├── examples

│   ├── basic_usage.py

│   ├── cli_example.md

│   ├── declarative_template_creation.py

│   ├── stage_based_workflow.py

│   └── validate_templates.py

├── templates

│   ├── archive

│   │   ├── directional_vector_generator.py

│   │   ├── lvl1.md.generate.5000-5999.testing.py

│   │   ├── lvl1.md.generate.8000-8999.runway.py

│   │   ├── lvl1.md.generate.extractors.py

│   │   └── semantic_catalog.py

│   ├── md

│   │   ├── archive_legacy

│   │   │   ├── 1000-reflection_initializer.md

│   │   │   ├── 1000-reflection_initializer.testprompt

│   │   │   ├── 1001-directional_translator.md

│   │   │   ├── 1001-directional_translator.testprompt

│   │   │   ├── 1002-signal_resonator.md

│   │   │   ├── 1002-signal_resonator.testprompt

│   │   │   ├── 1003-reflection_initializer.md

│   │   │   ├── 1003-reflection_initializer.testprompt

│   │   │   ├── 1004-directional_translator.md

│   │   │   ├── 1004-directional_translator.testprompt

│   │   │   ├── 1005-signal_resonator.md

│   │   │   ├── 1005-signal_resonator.testprompt

│   │   │   ├── 1007-reflection_initializer.md

│   │   │   ├── 1007-reflection_initializer.testprompt

│   │   │   ├── 1008-directional_translator.md

│   │   │   ├── 1008-directional_translator.testprompt

│   │   │   ├── 1009-signal_resonator.md

│   │   │   ├── 1009-signal_resonator.testprompt

│   │   │   ├── 1010-b-title_extractor.md

│   │   │   ├── 1010-c-title_extractor.md

│   │   │   ├── 1010-d-title_extractor.md

│   │   │   ├── 1020-a-function_namer.md

│   │   │   ├── 1020-b-function_namer.md

│   │   │   ├── 1020-c-function_namer.md

│   │   │   ├── 1020-d-function_namer.md

│   │   │   ├── 1030-a-form_classifier.md

│   │   │   ├── 1031-a-form_classifier.md

│   │   │   ├── 1031-c-form_classifier.md

│   │   │   ├── 1031-d-form_classifier.md

│   │   │   ├── 2001-directional_translator.md

│   │   │   ├── 2002-reflection_initializer.md

│   │   │   ├── 3002-directional_translator.md

│   │   │   ├── 5998-a-meta_reflection_initializer.md

│   │   │   ├── 5998-b-directional_translator.md

│   │   │   ├── 5998-c-identity_mapper.md

│   │   │   ├── 5998-d-signal_resonator.md

│   │   │   ├── 5998-e-closure_deferment_unit.md

│   │   │   ├── 5999-a-directional_agent.md

│   │   │   ├── 8980-a-runway_prompt_generator.md

│   │   │   ├── 8980-b-runway_prompt_generator.md

│   │   │   ├── 8980-c-runway_prompt_generator.md

│   │   │   ├── 8980-d-runway_prompt_generator.md

│   │   │   ├── 8990-a-runway_prompt_generator.md

│   │   │   ├── 8990-b-runway_prompt_generator.md

│   │   │   ├── 8990-c-runway_prompt_generator.md

│   │   │   ├── 8990-d-runway_prompt_generator.md

│   │   │   ├── 9000-b-intensify.md

│   │   │   ├── 9000-c-diminish.md

│   │   │   ├── 9001-a-clarify.md

│   │   │   ├── 9001-b-purify.md

│   │   │   ├── 9001-c-obscure.md

│   │   │   ├── 9002-a-expand.md

│   │   │   ├── 9002-c-restructure.md

│   │   │   ├── 9003-a-elevate.md

│   │   │   ├── 9003-b-distill.md

│   │   │   ├── 9003-c-synthesize.md

│   │   │   ├── 9004-b-concretize.md

│   │   │   ├── 9004-c-transcend.md

│   │   │   ├── 9005-a-accelerate.md

│   │   │   ├── 9005-b-stabilize.md

│   │   │   ├── 9005-c-harmonize.md

│   │   │   ├── 9006-a-superpose.md

│   │   │   ├── 9006-b-entangle.md

│   │   │   └── 9006-c-collapse.md

│   │   ├── stage1

│   │   │   ├── 1000-test_evaluator.md

│   │   │   ├── 1001-content_synthesizer.md

│   │   │   ├── 1006-data_metamorphosis.md

│   │   │   ├── 1010-a-title_extractor.md

│   │   │   └── 1031-b-form_classifier.md

│   │   ├── stage2

│   │   │   └── 2000-reflection_initializer.md

│   │   ├── stage3

│   │   │   └── 3001-reflection_initializer.md

│   │   ├── 9000-a-amplify.md

│   │   ├── 9002-b-compress.md

│   │   └── 9004-a-abstract.md

│   ├── __init__.py

│   ├── catalog.py

│   ├── lvl1.md.templates.json

│   ├── stage1_prototypes.py

│   ├── stage2_candidates.py

│   └── stage3_production.py

├── .gitignore

├── CHANGELOG.md

├── CRYSTALLIZATION_SUMMARY.md

├── LICENSE

├── README.md

├── requirements.txt

└── setup.py

```

## Message 2

**Restructuring the project**

I’m mapping out the directory to better signify each pipeline stage, setting up distinct root-level folders for stages, and incorporating unified templates and shared resources.

## Message 3

Below is a **proposed refactor** that fulfils your request: every _stage_ (Stage 1 → Stage N) now hangs cleanly off the project root, so nothing stage‑specific is dispersed across unrelated folders.  
I show the **target tree**, annotate the **rationale**, and list the **migration steps** (file moves + import tweaks).  
Feel free to adapt names (e.g. `src/` vs `app/`) or keep additional stages—just replicate the same pattern.

---

## 1 · Target directory layout (high‑level view)

```
├── stages/                       # ← single entry‑point for all stage logic
│   ├── stage1/
│   │   ├── __init__.py
│   │   ├── core/                 # code unique to Stage 1
│   │   ├── templates/            # Stage 1 prompts & generators
│   │   ├── docs/                 # Stage 1‑specific docs
│   │   └── examples/             # runnable examples for Stage 1
│   ├── stage2/
│   │   ├── __init__.py
│   │   ├── core/
│   │   ├── templates/
│   │   ├── docs/
│   │   └── examples/
│   └── stage3/
│       ├── __init__.py
│       ├── core/
│       ├── templates/
│       ├── docs/
│       └── examples/
│
├── shared/                       # ← cross‑stage, reusable infrastructure
│   ├── __init__.py
│   ├── catalog.py
│   ├── config.py
│   ├── executor.py
│   ├── models.py
│   ├── utils.py
│   └── validation.py
│
├── docs/                         # project‑wide architectural docs
│   ├── 01_INSTRUCTION_PATTERNS.md
│   ├── 02_DIRECTIONAL_VECTORS.md
│   ├── …
│   └── README.md
│
├── examples/                     # cross‑stage, illustrate orchestration
│   ├── basic_usage.py
│   ├── stage_based_workflow.py
│   └── …
│
├── CHANGELOG.md
├── CRYSTALLIZATION_SUMMARY.md
├── LICENSE
├── README.md
├── requirements.txt
└── setup.py
```

### Where did everything go?

| OLD PATH                                                  | NEW PATH                                                           | NOTES |
|-----------------------------------------------------------|--------------------------------------------------------------------|-------|
| `templates/md/stage1/**`                                  | `stages/stage1/templates/md/**`                                    | Each stage owns its prompts. |
| `templates/stage1_prototypes.py`                          | `stages/stage1/core/prototypes.py`                                 | Group code + prompts. |
| `templates/md/9000-a-amplify.md` (and similar global ops) | keep **only if** truly cross‑stage → move to `shared/templates/`   | Otherwise duplicate under each stage and specialise. |
| `core/*`                                                  | split: keep cross‑cutting logic in `shared/`, move stage‑specific functions to respective `stages/<n>/core/`. |
| `examples/*.py`                                           | leave in root `examples/` _or_ move under each stage if they rely on only one stage. |

---

## 2 · Why this structure?

1. **Single point of entry per stage**  
   – Developers mentally zoom straight to `stages/stageN/…` to see _all_ assets (code + templates + docs + examples) for that stage.

2. **Clear separation of concerns**  
   – Anything reused by two or more stages goes in `shared/`; everything else stays local, avoiding tangled import graphs.

3. **Package‑friendly**  
   – `stages/` and `shared/` can each be proper Python packages; `setup.py` can expose stage‑level console scripts (e.g. `stage1-cli`, `stage2-cli`) without polluting the top namespace.

4. **Incremental compile‑time checks**  
   – Static analysers (ruff, mypy) can run per‑stage in CI; refactors don’t break unrelated stages.

5. **Docs stay adjacent to implementation**  
   – Stage‑specific guides sit next to code, while system‑wide discussions remain in the top‑level `docs/`.

---

## 3 · Step‑by‑step migration guide

> **Tip:** Do the moves in a feature branch and run `pytest`/your CI after each major chunk.

| # | Action | Command‑style example |
|---|--------|-----------------------|
| 1 | Create new dirs | `mkdir -p stages/{stage1,stage2,stage3}/{core,templates,docs,examples}` |
| 2 | Move stage‑specific templates | `git mv templates/md/stage1/* stages/stage1/templates/md/`<br>`git mv templates/md/stage2/* stages/stage2/templates/md/`<br>`git mv templates/md/stage3/* stages/stage3/templates/md/` |
| 3 | Relocate stage‑specific code | `git mv templates/stage1_prototypes.py stages/stage1/core/prototypes.py` etc. |
| 4 | Extract cross‑stage libs | Keep `catalog.py, config.py, executor.py…` in new `shared/`; adjust their imports to `from shared.executor import …`. |
| 5 | Update stage packages | In each `stages/stageN/__init__.py`, expose what callers need (e.g. `from .core.prototypes import run_prototype`). |
| 6 | Fix imports | Use `sed`/`ruff` codemod or PyCharm “Move symbol” to change e.g. `from core.executor` ➜ `from shared.executor`. |
| 7 | Adjust tests/examples | Search+replace old paths in `examples/` and tests. |
| 8 | Run linters + tests | Ensure green. Add CI job to assert `python -m compileall` in each stage. |

---

## 4 · Handling the **archive** folders

- **`templates/archive/**`**  
  Keep as‑is _or_ move into `stages/<stage>/archive/` if you ever need to resurrect items—just ensure it’s excluded from package build via `MANIFEST.in` or `setup.cfg[options.package_data]`.
- **`templates/md/archive_legacy/**`**  
  Same rule: either root‑level `archive/legacy` or per‑stage sub‑archive. The important part is to remove it from the default import path (`__init__.py` guards or `pkg_resources` exclusion).

---

## 5 · Next steps

1. **Automate the move** — a simple Python script (or `make reorganise`) can execute Steps 2–4 so teammates don’t diverge.  
2. **Enforce import discipline** — e.g. `shared` must not import from any `stages.*`; add a lightweight test.  
3. **Optionally expose CLI shims** for each stage in `setup.py`:

   ```python
   entry_points={
       "console_scripts": [
           "stage1-cli = stages.stage1.core.cli:main",
           "stage2-cli = stages.stage2.core.cli:main",
           "stage3-cli = stages.stage3.core.cli:main",
       ]
   }
   ```

4. **Document the policy** in `docs/04_SYSTEM_ARCHITECTURE.md` so new contributors understand the branching rule.

---

### Final thought

This refactor enforces a **hierarchical, modular, and easily navigable** shape—exactly what you asked for—in a way that scales naturally when Stage 4, Stage 5 … appear. If anything feels off‑balance (e.g. duplicated helpers), put them in `shared/` or reconsider whether they’re truly stage‑specific.

Happy restructuring!
