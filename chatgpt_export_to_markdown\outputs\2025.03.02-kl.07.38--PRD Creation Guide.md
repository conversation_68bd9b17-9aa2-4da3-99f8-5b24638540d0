# PRD Creation Guide

Conversation ID: 67c3fcde-5218-8008-80d8-aa89380849c9

## Message 1

```markdown
# 1. Product Requirements Document

Work with an expert product manager to create your requirements. Paste this 1st prompt into your LLM and hit return.

## Context
You are an expert product manager your role is to work with me the product owner to create a Product Requirements Document. This document will be in markdown format and used to help other large language models understand the Product. Be concise.

## Instructions
1. Ask the product owner to explain the project idea to you  
2. If they leave any details out based on the Sample PRD output, ask clarifying questions  
3. Output a markdown file based on the product owner’s context and use the Sample PRD headings as a guide to the output  

## Sample PRD Headings
1. Elevator Pitch – Pitch this product in one paragraph  
2. This is for  
3. Functional Requirements – What does it do?  
4. How it Works – How will the user interact?  
5. User Interface – How will it look?
```

## Message 2

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-1J8YZ52CnpQA37U2raA3w9', 'size_bytes': 639217, 'width': 1020, 'height': 960, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
please write the attached image as a markdown block

## Message 3

```markdown
# 1.1 Rob’s Voice Response to LLM Questions after First Prompt

Let me explain what Levercast is. It’s an application that helps entrepreneurs manage their social media posting more efficiently. Often when I’m reading an article or taking a walk, I’ll come up with an idea for a thought piece or article. I want to capture it, refine it, and then have it go out on my next post. The app has an input box where I can dump those thoughts.

The input then gets sent to an LLM that formats it based on custom template prompts I’ve set up in the application. It returns a 1st revised version of my thoughts, but it does more than that—it generates multiple versions optimized for different social platforms. For our MVP, we’ll focus on LinkedIn and Twitter posts, with plans to add more platforms later.

These generated posts will appear styled exactly as they would on their respective platforms, making it easy to preview them. I can also add images to my input, and the app will append them appropriately for each platform. I can edit any of the outputs, and when I’m satisfied, publish them directly from Levercast. We’ll use OAuth to connect Levercast with LinkedIn and Twitter accounts.

In essence, Levercast helps busy entrepreneurs capture ideas and then quickly cross multiple platforms simultaneously. The key benefit is saving time while amplifying marketing efforts. As for the user interface, while I’ll leave the specific design details to a professional designer, I know it will be approachable initially, with responsiveness to support mobile access in the future.
```

## Message 4

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-2cRHwWEEnLxdZjdEZaapRU', 'size_bytes': 761563, 'width': 749, 'height': 843, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
please do the same for this

## Message 5

```markdown
# 2. User Interface Design Document

Work with an expert UX designer to figure out how you should approach designing the UI for the App. Paste this prompt in your LLM and watch the PRD come alive.

## Context
You are an expert UX designer. Your role is to work with the product manager and the product owner to create a UI design document. This UI design document will be in markdown format. It will be used by developers and other large language models to create the best user interface possible.

---

### 1. Input
The product provides input documents. If one is not provided as an example in the app or a conversation, we can’t do the next steps. That’s because we need to know what we’re designing. For example:  
1. Text input  
2. User input  
3. Advanced settings  

The first step in any design is to understand the user’s input. That’s what we do in the UI design doc.

---

### 2. Instructions
1. If the product owner leaves out any UI details, ask clarifying questions.  
2. Show them an example of the final user interface design plan that is easy to read and follow.

---

### Sample UI Card-like Modular Layout
Use a card-like layout for the user interface, with each card focusing on a specific aspect of the UI. For example:

1. **Text Input Card**  
   - Single input field for capturing user ideas  
   - The user can type or copy/paste text, ideally up to 1000 words  
   - The user can do a multi-step approach  
   - The user can talk to it (audio) or type in

2. **Settings Card**  
   - Options like color scheme, fonts, advanced settings for toggling

3. **Preview Card**  
   - A place to see the final output that the user can edit

4. **Publish or Export Card**  
   - A place to see the final output that the user can choose to push to the next step, such as social media or internal use

5. We can have multiple cards that convey clarity and business logic.

---

### Design Approach
- Focus on the user’s mental model, brand identity guidelines, and layout approach  
- Focus on minimal design  
- Build for desktop  
- Possibly use background images for style (we can add to it)  
- Present it in a color scheme  

---

Looking forward to your feedback!
```

## Message 6

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-BkbVp9WbMMSA83q4WSbvtj', 'size_bytes': 1135970, 'width': 482, 'height': 2048, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
please do the same for this

## Message 7

```text
# Instructions

1. Process the product input documents if one is not provided ask for one
2. Ask questions about the user persona if it's unclear to you
3. Generate 3 options for user interface designs that might suit the persona. Don't use code this is a natural language description
4. Ask the product owner to confirm which one they like or amendments they have
5. Proceed to generate the final User Interface Design Document. Use only basic markdown.
```

## Message 8

```text
Core Components:
- Expansive content area with image support
- Interactive preview cards that users can click to edit
- A top navigation bar for publishing controls and account management

Interaction Patterns:
- Modal dialogs for detailed content editing
- Interactive cards that reveal more content
- Hover effects and clear visual cues for actionable items

Visual Design Elements & Color Scheme:
- Dark background with bold accent colors (e.g., electric blue or bright orange)
- Subtle gradients and shadow effects to create depth
- Emphasis on visual flair while maintaining clarity

Typography:
- Modern sans-serif fonts with varying weights to establish hierarchy

Accessibility:
- Options for a high-contrast mode
- Screen reader-friendly components and distinct focus states
```

## Message 9

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-7eiiVtMufHTaykkBUDifRm', 'size_bytes': 197398, 'width': 1066, 'height': 416, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
write this as a codeblock

## Message 10

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-5atm8ceUJqTg7N1Tgnh1pC', 'size_bytes': 394573, 'width': 1104, 'height': 945, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
write this as a codeblock
