# Script Exit Delay

Conversation ID: 67fa17c9-5938-8008-b3e8-994e477da61a

## Message 1

Below is an updated version of the script that waits 5 seconds before exiting. The main change is the addition of `time.sleep(5)` after the script finishes closing duplicate windows:

```python
import urllib.parse
import win32com.client
import win32con
import win32gui
import time
from loguru import logger
import sys

class LoggerSetup:
    """Sets up YAML logging using Loguru."""

    @staticmethod
    def setup_yaml_logging(
        log_file: str = "explorer_window_manager.log.yml", level: str = "INFO"
    ):
        """
        Configures Loguru to log in YAML format and to the console.

        Args:
            log_file: Path to the log file.
            level: Minimum logging level (e.g., "DEBUG", "INFO").
        """

        def yaml_sink(message):
            record = message.record
            time_str = f"{record['time'].strftime('%Y-%m-%d %H:%M:%S')}"
            level_str = f"!{record['level'].name}"
            name_str = record["name"]
            funcName_str = f"*{record['function']}"
            lineno_int = record["line"]
            msg = record["message"]

            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                message_str = f"'{msg}'" if ":" in msg else msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {funcName_str}",
                f"  lineno: {lineno_int}",
                f"  message: {message_str}",
                "",
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        logger.remove()  # Remove default handler

        # Add YAML file sink
        logger.add(yaml_sink, level=level, enqueue=True, format="{message}")

        # Add console sink with colorized output
        logger.add(
            sys.stdout,
            level=level,
            enqueue=True,
            colorize=True,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        )

    @staticmethod
    def initialize_logging():
        """Initializes logging."""
        LoggerSetup.setup_yaml_logging()


class ExplorerWindowManager:
    """Manages and closes duplicate Explorer windows."""

    @staticmethod
    def get_explorer_window_info(window):
        """
        Gets information about an Explorer window.

        Args:
            window: A Shell window object.

        Returns:
            A dictionary with window information, or None if not an Explorer window.
        """
        hwnd = window.HWND
        title = win32gui.GetWindowText(hwnd)

        # Use LocationURL if available, otherwise fall back to parsing the title
        try:
            location_url = window.LocationURL
            if location_url:
                path = urllib.parse.unquote(location_url).lstrip("/").replace("\\", "/")
            else:
                path = ExplorerWindowManager.get_special_folder_path(title)
        except Exception:
            path = ExplorerWindowManager.get_special_folder_path(title)

        if not path:
            return None

        placement = win32gui.GetWindowPlacement(hwnd)
        pos = [placement[4][0], placement[4][1]]
        size = [placement[4][2] - pos[0], placement[4][3] - pos[1]]
        class_name = win32gui.GetClassName(hwnd)
        always_on_top = bool(
            win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE) & win32con.WS_EX_TOPMOST
        )

        if not win32gui.IsWindowVisible(hwnd) or size[0] <= 1 or size[1] <= 1:
            return None

        return {
            "hwnd": hwnd,
            "window_state": placement[1],
            "position": pos,
            "size": size,
            "title": title,
            "path": path,
            "class_name": class_name,
            "always_on_top": always_on_top,
            "type": "explorer",
        }

    @staticmethod
    def get_special_folder_path(title):
        """
        Attempts to determine the path of special folders based on their title.

        Args:
            title: The title of the Explorer window.

        Returns:
            A string representing the path, or None if it cannot be determined.
        """
        title_lower = title.lower()

        special_folders = {
            "this pc": "::{20D04FE0-3AEA-1069-A2D8-08002B30309D}",  # This PC
            "quick access": "::{679F85CB-0220-4080-B29B-5540CC05AAB6}",  # Quick Access
            "desktop": "::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}",  # Desktop
            "downloads": "::{374DE290-123F-4565-9164-39C4925E467B}",  # Downloads
            "documents": "::{A8CDFF1C-4878-43be-B5FD-F8091C1C60D0}",  # Documents
            "music": "::{1CF1260C-4DD0-4ebb-811F-33C572699FDE}",  # Music
            "pictures": "::{3ADD1653-EB32-4cb0-BBD7-DFA0ABB5ACCA}",  # Pictures
            "videos": "::{A0953C92-50DC-43bf-BE83-3742FED03C9C}",  # Videos
            "network": "::{F02C1A0D-BE21-4350-88B0-7367FC96EF3C}",  # Network
            "control panel": "::{26EE0668-A00A-44D7-9371-BEB064C98683}",  # Control Panel
            "recycle bin": "::{645FF040-5081-101B-9F08-00AA002F954E}",  # Recycle Bin
        }

        for folder_name, clsid in special_folders.items():
            if folder_name in title_lower:
                return clsid

        # If no specific match, check if it's a drive
        if title_lower.endswith(")") and ":" in title_lower:
            drive_letter = title_lower[title_lower.rfind("(") + 1 : title_lower.rfind(")")]
            if (
                len(drive_letter) == 2
                and drive_letter[0].isalpha()
                and drive_letter[1] == ":"
            ):
                return drive_letter + "\\"

        return None

    @classmethod
    def get_explorer_windows(cls):
        """Retrieves all open Explorer windows."""
        logger.debug("Fetching explorer windows")
        shell = win32com.client.Dispatch("Shell.Application")
        windows = [
            info
            for window in shell.Windows()
            if (info := cls.get_explorer_window_info(window))
        ]
        logger.info(f"Found {len(windows)} explorer windows")
        return windows

    @classmethod
    def close_duplicate_explorer_windows(cls):
        """Closes duplicate Explorer windows."""
        windows = cls.get_explorer_windows()
        total_windows = len(windows)
        paths = {}
        duplicates = []

        for window in windows:
            path = window["path"]
            if path in paths:
                duplicates.append(window)
            else:
                paths[path] = window

        closed_windows_info = []
        for window in duplicates:
            try:
                win32gui.PostMessage(window["hwnd"], win32con.WM_CLOSE, 0, 0)
                closed_windows_info.append(window)
                logger.info(
                    f'Closed duplicate explorer window: {window["title"]} at {window["path"]}'
                )
            except Exception as e:
                logger.error(
                    f'Failed to close window: {window["title"]} at {window["path"]} with error: {e}'
                )

        num_duplicates_closed = len(closed_windows_info)
        logger.info(
            f"From a total of {total_windows} windows: closed {num_duplicates_closed} duplicate(s)."
        )
        return closed_windows_info


if __name__ == "__main__":
    LoggerSetup.initialize_logging()
    try:
        ExplorerWindowManager.close_duplicate_explorer_windows()
    except Exception as e:
        logger.exception(f"An unexpected error occurred: {e}")

    # Wait 5 seconds before exiting
    time.sleep(5)
```

All that was needed was the `time.sleep(5)` call at the end of the main block to delay script termination by 5 seconds.

## Message 2

please update this script to wait 5 seconds before exiting after completion:



#### `main.py`



```python

    import urllib.parse

    import win32com.client

    import win32con

    import win32gui

    import time

    from loguru import logger

    import sys

    

    class LoggerSetup:

        """Sets up YAML logging using Loguru."""

    

        @staticmethod

        def setup_yaml_logging(

            log_file: str = "explorer_window_manager.log.yml", level: str = "INFO"

        ):

            """

            Configures Loguru to log in YAML format and to the console.

    

            Args:

                log_file: Path to the log file.

                level: Minimum logging level (e.g., "DEBUG", "INFO").

            """

    

            def yaml_sink(message):

                record = message.record

                time_str = f"{record['time'].strftime('%Y-%m-%d %H:%M:%S')}"

                level_str = f"!{record['level'].name}"

                name_str = record["name"]

                funcName_str = f"*{record['function']}"

                lineno_int = record["line"]

                msg = record["message"]

    

                if "\n" in msg:

                    lines = msg.split("\n")

                    message_str = "|\n" + "\n".join(f"  {line}" for line in lines)

                else:

                    message_str = f"'{msg}'" if ":" in msg else msg

    

                yaml_lines = [

                    f"- time: {time_str}",

                    f"  level: {level_str}",

                    f"  name: {name_str}",

                    f"  funcName: {funcName_str}",

                    f"  lineno: {lineno_int}",

                    f"  message: {message_str}",

                    "",

                ]

    

                with open(log_file, "a", encoding="utf-8") as f:

                    f.write("\n".join(yaml_lines) + "\n")

    

            logger.remove()  # Remove default handler

    

            # Add YAML file sink

            logger.add(yaml_sink, level=level, enqueue=True, format="{message}")

    

            # Add console sink with colorized output

            logger.add(

                sys.stdout,

                level=level,

                enqueue=True,

                colorize=True,

                format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",

            )

    

        @staticmethod

        def initialize_logging():

            """Initializes logging."""

            LoggerSetup.setup_yaml_logging()

    

    

    class ExplorerWindowManager:

        """Manages and closes duplicate Explorer windows."""

    

        @staticmethod

        def get_explorer_window_info(window):

            """

            Gets information about an Explorer window.

    

            Args:

                window: A Shell window object.

    

            Returns:

                A dictionary with window information, or None if not an Explorer window.

            """

            hwnd = window.HWND

            title = win32gui.GetWindowText(hwnd)

    

            # Use LocationURL if available, otherwise fall back to parsing the title

            try:

                location_url = window.LocationURL

                if location_url:

                    path = urllib.parse.unquote(location_url).lstrip("/").replace("\\", "/")

                else:

                    path = ExplorerWindowManager.get_special_folder_path(title)

            except Exception:

                path = ExplorerWindowManager.get_special_folder_path(title)

    

            if not path:

                return None

    

            placement = win32gui.GetWindowPlacement(hwnd)

            pos = [placement[4][0], placement[4][1]]

            size = [placement[4][2] - pos[0], placement[4][3] - pos[1]]

            class_name = win32gui.GetClassName(hwnd)

            always_on_top = bool(

                win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE) & win32con.WS_EX_TOPMOST

            )

    

            if not win32gui.IsWindowVisible(hwnd) or size[0] <= 1 or size[1] <= 1:

                return None

    

            return {

                "hwnd": hwnd,

                "window_state": placement[1],

                "position": pos,

                "size": size,

                "title": title,

                "path": path,

                "class_name": class_name,

                "always_on_top": always_on_top,

                "type": "explorer",

            }

    

        @staticmethod

        def get_special_folder_path(title):

            """

            Attempts to determine the path of special folders based on their title.

    

            Args:

                title: The title of the Explorer window.

    

            Returns:

                A string representing the path, or None if it cannot be determined.

            """

            title_lower = title.lower()

    

            special_folders = {

                "this pc": "::{20D04FE0-3AEA-1069-A2D8-08002B30309D}",  # This PC

                "quick access": "::{679F85CB-0220-4080-B29B-5540CC05AAB6}",  # Quick Access

                "desktop": "::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}",  # Desktop

                "downloads": "::{374DE290-123F-4565-9164-39C4925E467B}",  # Downloads

                "documents": "::{A8CDFF1C-4878-43be-B5FD-F8091C1C60D0}",  # Documents

                "music": "::{1CF1260C-4DD0-4ebb-811F-33C572699FDE}",  # Music

                "pictures": "::{3ADD1653-EB32-4cb0-BBD7-DFA0ABB5ACCA}",  # Pictures

                "videos": "::{A0953C92-50DC-43bf-BE83-3742FED03C9C}",  # Videos

                "network": "::{F02C1A0D-BE21-4350-88B0-7367FC96EF3C}",  # Network

                "control panel": "::{26EE0668-A00A-44D7-9371-BEB064C98683}",  # Control Panel

                "recycle bin": "::{645FF040-5081-101B-9F08-00AA002F954E}",  # Recycle Bin

            }

    

            for folder_name, clsid in special_folders.items():

                if folder_name in title_lower:

                    return clsid

    

            # If no specific match, check if it's a drive

            if title_lower.endswith(")") and ":" in title_lower:

                drive_letter = title_lower[title_lower.rfind("(") + 1 : title_lower.rfind(")")]

                if (

                    len(drive_letter) == 2

                    and drive_letter[0].isalpha()

                    and drive_letter[1] == ":"

                ):

                    return drive_letter + "\\"

    

            return None

    

        @classmethod

        def get_explorer_windows(cls):

            """Retrieves all open Explorer windows."""

            logger.debug("Fetching explorer windows")

            shell = win32com.client.Dispatch("Shell.Application")

            windows = [

                info

                for window in shell.Windows()

                if (info := cls.get_explorer_window_info(window))

            ]

            logger.info(f"Found {len(windows)} explorer windows")

            return windows

    

        @classmethod

        def close_duplicate_explorer_windows(cls):

            """Closes duplicate Explorer windows."""

            windows = cls.get_explorer_windows()

            total_windows = len(windows)

            paths = {}

            duplicates = []

    

            for window in windows:

                path = window["path"]

                if path in paths:

                    duplicates.append(window)

                else:

                    paths[path] = window

    

            closed_windows_info = []

            for window in duplicates:

                try:

                    win32gui.PostMessage(window["hwnd"], win32con.WM_CLOSE, 0, 0)

                    closed_windows_info.append(window)

                    logger.info(

                        f'Closed duplicate explorer window: {window["title"]} at {window["path"]}'

                    )

                except Exception as e:

                    logger.error(

                        f'Failed to close window: {window["title"]} at {window["path"]} with error: {e}'

                    )

    

            num_duplicates_closed = len(closed_windows_info)

            logger.info(

                f"From a total of {total_windows} windows: closed {num_duplicates_closed} duplicate(s)."

            )

            return closed_windows_info

    

    

    if __name__ == "__main__":

        LoggerSetup.initialize_logging()

        try:

            ExplorerWindowManager.close_duplicate_explorer_windows()

        except Exception as e:

            logger.exception(f"An unexpected error occurred: {e}")

```



