# Codebase Optimization Guidelines

Conversation ID: 68684fea-8490-8008-b392-d8aef3d328c5

## Message 1

Please consolidate and transform these codebase rules into ordered and organized highly efficient llm-optimized **generalized** instructions (for use with codebase interaction through ai-assisted coding assistants such as CursorAI, VSCode+Augment, etc):



```

"Always reuse existing tests before creating new ones.",

"Document only essential decisions in a concise format.",

"Optimize file/module size for comprehension and ease of navigation.",

"Justify and integrate new files/modules within existing organization.",

"Post-edit, audit for and remove all residual clutter or inconsistency.",

"Eliminate anomalies, redundancies, and duplications after every change.",

"Consolidate related functionality; minimize cross-component dependencies.",

"Justify any new file creation; align additions with project organization.",

"Write self-explanatory code; only add brief comments for complex sections.",

"Check for and use current tests before creating new ones for added features.",

"Prohibit loss of actionable detail, overgeneralization, and diluted guidance."

"Require concise, self-explanatory code: minimal comments only where necessary.",

"Prioritize simplicity, elegance, and high-leverage changes with maximal impact.",

"Optimize directory and module organization for clarity and developer ergonomics.",

"After each change, rigorously clean up and check for complete codebase alignment.",

"Enforce total adherence to established code patterns, conventions, and standards.",

"Verify and integrate with pre-existing tests; avoid redundancy when adding tests.",

"Verify and reuse existing tests before adding new ones for any new functionality.",

"Mandate thorough evaluation of codebase for patterns/anti-patterns before changes.",

"Balance file/module granularity for maximum comprehension and developer ergonomics.",

"Review recent modifications to ensure no residual clutter or inconsistency remains.",

"Document only condensed, integral decisions with rationale as structural annotations.",

"Validate no loss of prior functionality or clarity; confirm full alignment pre-change.",

"Every modification must be deliberate, justified, and enhance long-term maintainability."

"Enforce single responsibility for components; composition over inheritance when possible.",

"Implement only simple, high-impact changes that maximize clarity and minimize disruption.",

"Review recent changes for thorough cleanup and conformity with structure and conventions.",

"Refactor or add code only if it strictly matches and adapts to current codebase structure.",

"Favor composition over inheritance; enforce single responsibility and minimize dependencies.",

"Prefer composition, not inheritance; keep each unit single-purpose and dependencies minimal.",

"Preserve existing functionality and clarity; ensure full alignment before initiating changes.",

"Prioritize rigorous code cleanliness, respecting and adapting to existing codebase structure.",

"Before creating new tests, confirm no duplicates exist; integrate with existing test frameworks.",

"Rule enforcing decoupling of unrelated parts. Minimize dependencies between unrelated components",

"Creation of new files or components should be justified and conform with established organization.",

"Uphold integrity, maintainability, and clarity; maximize functionality while minimizing disruption.",

"Enforce code clarity via expressive structure and naming; comments only for clarifying complexities.",

"Favor simplicity, elegance, and concise intent; pursue high-impact improvements with low disruption.",

"Create new files only with documented project-specific justification and strict structural alignment.",

"Never compromise current functionality or clarity; verify absolute alignment before any modification.",

"Ensure code clarity via precise naming and concise logic; add comments only for unavoidable complexity.",

"Continuously reorganize files and modules for maximum navigability and intuitive clarity for developers.",

"Reject any generalization or abstraction that eliminates actionable steps or specific developer guidance."

"After edits, audit all recent code changes for strict adherence to codebase conventions and full cleanup.",

"Document only key architectural decisions in brief; prohibit inconsistencies, redundancies, and anomalies."

"Implement only elegant, simple, high-leverage improvements; reject changes causing unnecessary complexity.",

"Rule for minimal yet sufficient documentation. Document only integral decisions in a highly condensed form",

"Scan recent commits and structure before edits; identify existing tests, patterns, and naming conventions.",

"Segment explicit compliance rules (low-level) from aspirational principles (high-level) for enforceability.",

"Merge and co-locate related functions into unified modules; eliminate unnecessary inter-module dependencies.",

"Guideline for organizing code into logical groupings. Consolidate related functionality into cohesive modules",

"Tag and separate explicit, enforceable compliance rules from broader aspirational best-practices for clarity.",

"Design enforcement: uphold single responsibility principle. Ensure all components have a single responsibility",

"Continuously refine structure: merge related modules, eliminate anti-patterns, optimize directory organization.",

"Only create new files/components when necessary, with clear rationale, and match established project structure.",

"Enforce clear, self-explanatory code via precise naming and structure; use comments exclusively for complex logic.",

"Mandate balancing simplicity with capability. Maintain inherent simplicity while providing powerful functionality.",

"Always pursue inherent clarity, structure, simplicity, and elegant, high-impact solutions that minimize disruption.",

"Enforce consolidation of related code into cohesive, easily navigable modules with minimized redundant dependencies."

"Refactor all code to single-responsibility units; prefer composition strategies over inheritance wherever feasible.",

"Uphold and adapt to existing codebase structure after any changes; cohesively clean up and integrate modifications.",

"Pre-change: Rigorously analyze codebase for structural patterns and anti-patterns; act only upon confirmed insights.",

"Annotate code structure with only the most essential, rationale-focused comments—no verbose or redundant explanations.",

"Mandate comprehensive conformity checks and categorical rejection of non-compliant contributions via automated systems."

"Require all modifications to strictly align with established standards—no anomalies, inconsistencies, or redundancies.",

"After changes, immediately refactor and adapt code to match existing structure; eliminate any integration inconsistencies.",

"Optimize all improvements for minimal intrusion and maximal impact, prioritizing elegance, maintainability, and coherence.",

"Instruction to weigh module/file size versus system clarity. Balance file granularity with overall system comprehensibility",

"Guideline for future-proofing and developer friendliness. Prioritize readability and understandability for future developers",

"Design recommendation: prioritize composition for flexibility and clarity. Favor composition over inheritance when applicable",

"Do not introduce or tolerate anomalies, inconsistencies, redundancies, or deviations from standards in any code modification.",

"Directive for structural codebase assessment. Evaluate the existing codebase structure and identify patterns and anti-patterns",

"Only document essential decisions succinctly; all changes must prevent inconsistencies, redundancies, and structural anomalies."

"Fully justify and harmonize any new file, function, or dependency; require automated preservation of all existing functionality.",

"Project-wide objective emphasizing three guiding traits. Aim for simplicity, clarity, and maintainability in all project aspects",

"Code must be self-explanatory through structure and naming, using minimal and targeted comments only as necessary for complexity.",

"Objective to improve ease of use and comprehension in project structure. Optimize for developer ergonomics and intuitive navigation",

"Mandate absolute alignment with established organizational standards, preserving clarity, intent, and system integrity at all times.",

"Strictly maintain conformity with codebase patterns, conventions, and standards for seamless integration and ongoing maintainability.",

"No change permitted if it compromises codebase integrity, maintainability, or clarity; favor minimally disruptive, functional enhancement.",

"Rigorously analyze the current codebase structure—including recent changes and underlying design rationale—before making any modification.",

"Prescriptive statement: adopt standards to achieve simplicity and maintainability. Coding standards that promote simplicity and maintainability",

"Discern unwavering enforcement of clarity, simplicity, and maintainability through explicit, automatable standards and rigorous structural discipline.",

"Ensure any new functionality or files are fully justified, seamlessly integrated, and explicitly documented in line with existing project organization.",

"Explicitly require that all changes are accompanied by concise, in-context annotations detailing only non-obvious rationale or potential integration concerns.",

"Enforce codebase clarity, inherent simplicity, and maintainability: single responsibility per component, strict organization, zero abstraction/redundancy/ambiguity.",

"Directive for detailed, actionable process documentation. Define explicit adaptation, testing, documentation, and review procedures in codebase policies; avoid vague meta-rules.",

"Provide comprehensive, actionable, and unambiguous rules that enforce maximal maintainability, clarity, alignment, and contextual excellence for autonomous coding agent conduct.",

"Notice potential for clearer sequencing (requirements -> enforcement -> workflows -> code standards -> contributions -> automation) and enhancement of developer-centric language.",

"Mandate that each modification references and links to prior relevant architectural or design decisions when applicable, reinforcing context awareness without adding procedural bloat."

"Call to understand both implementation and reasoning behind architectural choices. Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.",

"Requirement for rationalizing file creation and maintaining organizational consistency. Justify the creation of any new files and ensure all additions align with the existing project organization.",

"Requirement to preserve current behavior and clarity as precondition to edits. Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.",

"Establish a baseline of codebase immutability regarding quality and standards. Absolute prohibition of introducing inconsistencies, anomalies, redundancies, or deviations from codebase standards in any modification.",

"Policy to ensure principles are verifiable through automated tooling. Translate principles into one-to-one, machine-readable mappings, enabling tools (linters, CI, code reviewers) to verify compliance automatically.",

"Prevent arbitrary file proliferation and enforce organizational discipline. Obligation to justify creation of new files, coupled with strict alignment of all additions to the organizational structure of the project.",

"High-level principle enforcement: adhere to specific qualities guiding code and design patterns. Follow patterns derived from the principles of inherent clarity, structure, simplicity, elegance, precision, and intent.",

"Rule for testing: ensure awareness of existing tests before adding new ones. If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.",

"Policy to avoid overgeneralization and maintain strict requirements for reliability. Do not replace actionable directives with abstractions—retain strict, granular requirements to ensure unambiguous, consistent outcomes.",

"Specific guideline to structure directories for clarity and assistive toolability. Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.",

"Emphasis on adherence to standards for smooth integration and long-term code health. Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.",

"Direct testing integration with existing codebase and prevent redundant test cases. Specific rule mandating review of existing tests before creating new ones, ensuring no duplication and investment in comprehensive verification.",

"Mandate for rigorous compliance and avoidance of codebase issues post-changes. Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.",

"Mandate comprehensive analysis as a precursor to all code changes. Demand for post-edit holistic review, including cleanup, adherence to structure, and respect for conventions; maintain project integrity and manageability at all times.",

"Directive to assess and uphold cleanliness, structure, and consistency following changes. Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.",

"Guideline emphasizing automation-readiness and unambiguous mandate articulation. Implement automation-focused rule structures—preserve hierarchy, explicit mandates, and concise justifications, ensuring robust integration and effective enforcement.",

"Mandate for responsibility in editing and maintaining code cleanliness, while respecting project structure. Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.",

"Concrete rules and advice for arranging and organizing codebases. Explicit call to simplify project aspects, prefer composition over inheritance, ensure readability, embrace single-responsibility, and condense documentation solely to critical decisions.",

"Amplified policy: encourage direct, machine-enforceable rule embedding to enable automation. Embed precise, enforceable rules directly in config files (YAML, JSON, or schema) for automated enforcement: prioritize clarity, modularity, and maintainability.",

"Define action-oriented, enforceable coding standards for any codebase modifications. List of non-negotiable operational requirements for modifying a codebase, emphasizing conformity to existing structure and detailed attention to cleanup, organization, and cohesion.",

"Enforce codebase hygiene and organizational rigor after any modification. Prerequisite to investigate and understand code structure and design rationale before modifying anything, ensuring clarity, alignment with existing intent, and retention of system functionality.",

"Ensure clarity is primarily achieved via code structure and naming, not excessive comments. Directive to write code that is self-explanatory, well-structured, and minimalistic in commenting, avoiding long explanations in favor of clarifying complex logic only where absolutely necessary.",

"Translate aspirational best practices into general, actionable development patterns. Procedural guidelines for code organization: analyze structures, identify patterns and anti-patterns, consolidate related functions, minimize cross-dependencies, and optimize for developer navigation and file clarity.",

"Establish aspirational ideals influencing all code and organizational decisions. Enumeration of core guiding principles: inherent clarity, structure, simplicity, elegance, precision, intent; pursue minimal, powerful improvements and seek prevailing solutions that maximize impact with minimal disruption.",

"Inspirational principle motivating impactful, minimally disruptive, elegant change. Embrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.",

"Aspirational design principle: seek unified, contextually integrated solutions with outstanding execution. Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.",

## Message 2

**Transforming codebase rules**

I’m condensing and organizing a vast number of rules into efficient, LLM-optimized directives. This involves categorizing by principles, compliance, workflows, guidelines, and automation, ensuring they are easy to parse for AI-assisted coding.

## Message 3

### Universal Codebase‐Interaction Standards  
*(LLM‑optimized for AI coding assistants such as CursorAI, VS Code + Augment, etc.)*

---

## 1 Aspirational Principles (guide every decision)

| ID | Principle | Essence |
|----|-----------|---------|
| **P‑1** | **Clarity over cleverness** | Structure, naming, and flow must make intent self‑evident. |
| **P‑2** | **Simplicity unlocks power** | Prefer the minimal change that yields the maximal, long‑term benefit. |
| **P‑3** | **Single responsibility** | One purpose per file, module, class, and function; prefer composition to inheritance. |
| **P‑4** | **Structural integrity** | Uphold existing architecture; extend rather than deform it. |
| **P‑5** | **Ever‑green maintainability** | Each edit must increase (or at worst preserve) readability, navigability, and testability. |

*These ideals shape intent but are **not** directly machine‑enforceable.*

---

## 2 Strict Compliance Rules (machine‑verifiable, non‑negotiable)

> **Tag all rules you automate in linters/CI as `CR‑#` to keep enforcement explicit.**

| ID | Rule |
|----|------|
| **CR‑1** | **Reuse tests first.** New tests only when no existing one covers the added behavior. |
| **CR‑2** | **No orphan files.** Every new file/module must: a) be justified in the PR/commit message, b) fit the directory taxonomy, and c) appear in build + test manifests. |
| **CR‑3** | **Zero residual clutter.** A change is “done” only when *all* dead code, redundant imports, TODOs, and style violations introduced by that change are removed. |
| **CR‑4** | **No anomalies / duplicates.** CI must fail if a static check detects duplicated code, circular imports, or style/coverage regressions. |
| **CR‑5** | **Self‑explanatory code.** Comments allowed solely for non‑obvious rationale or unavoidable complexity; all other comments fail lint. |
| **CR‑6** | **Directory budget.** Maximum *N* top‑level directories (configurable); module size ≤ *M* logical units (functions/classes). |
| **CR‑7** | **Immutable standards.** PR is rejected if it violates naming conventions, formatting rules, or lint baselines. |
| **CR‑8** | **Functional parity check.** Existing public contracts (tests & types) must still pass after the change. |
| **CR‑9** | **Change justification.** Commit message must state *why* (impact) and *how* (approach) in ≤ 5 lines. |
| **CR‑10** | **Automated post‑edit audit.** Each merge triggers scripts that: format, lint, de‑duplicate, and regenerate docs. Failures block merge. |

---

## 3 Standard Workflow for Any Modification

1. **Scan & Understand**  
   *Search codebase and tests to locate existing patterns, anti‑patterns, and coverage.*  
   → Abort or refactor instead of adding if reuse is possible.

2. **Design in Place**  
   *Outline the change alongside the current architecture; justify any new artifact (file, dependency, test).*

3. **Implement Minimally**  
   *Code the smallest coherent unit, adhering to* P‑1 – P‑5 *and all CRs.*

4. **Local Verification**  
   *Run full test suite + static analysis. Refactor until zero warnings.*

5. **Cleanup & Align**  
   *Merge related functions, prune dead code, and update any affected docs/diagrams.*

6. **Document the Decision**  
   *Add a concise architectural note (e.g., ADR, inline annotation ≤ 10 lines) only if the rationale is non‑obvious.*

7. **Submit PR with Checklist** (see § 7)  

---

## 4 Code Organization & Structure

- **Granularity balance**   
  Aim for files of ~200–400 logical lines; split only when cohesion drops or comprehension suffers.
- **Cohesive modules**   
  Group related features; prohibit “misc”, “util”, or catch‑all buckets.
- **Dependency hygiene**   
  No cross‑layer imports that break onion/hexagonal boundaries.  
  Prefer interfaces or dependency injection over hard coupling.

---

## 5 Testing Policy

| Action | Requirement |
|--------|-------------|
| **New feature** | Search existing tests ➜ extend if applicable, else create new. |
| **Refactor** | Run existing tests before & after; add regression tests if coverage gaps appear. |
| **Bug fix** | Red test → fix → green → eliminate duplicates. |

> **Redundant or flaky tests are technical debt — remove or stabilize within the same PR.**

---

## 6 Commenting & Documentation

1. **Code comments**:  
   *Only* for algorithmic nuance or domain context that can’t be inferred; max 3 lines each.

2. **Design records**:  
   Use *lightweight* ADRs (`docs/adr/NNN-*`), ≤ 200 words, one decision each.

3. **README / Module docstring**:  
   Explain purpose, public API, and invariants; keep it living and minimal.

---

## 7 PR / Commit Checklist (drop into your template)

- [ ] Reused/ext tests or added new **only if necessary** (CR‑1).  
- [ ] No new files unless justified; aligns with directory schema (CR‑2).  
- [ ] All linters, formatters, type‑checkers, and security scans pass (CR‑3, CR‑4, CR‑7).  
- [ ] Public API & behavior unchanged unless intentionally improved (CR‑8).  
- [ ] Commit message meets **≤ 5‑line rule** (CR‑9).  
- [ ] Post‑edit audit script passes (CR‑10).  
- [ ] PR description links to any newly added ADR.  

---

## 8 Automation & Tooling Hooks

| Stage | Tooling Hook |
|-------|--------------|
| **Pre‑commit** | `black`, `ruff`, `isort`, `detect‑duplicates`, `commit‑lint` |
| **CI test** | `pytest -q` + coverage ≥ baseline |
| **CI quality gate** | `mypy` strict, `bandit`, dependency license scan |
| **Post‑merge** | Docs generation, architecture diagram sync, changelog update |

---

### How to Use with AI Coding Assistants

*Embed sections 1‑8 (or a link) in your project root (e.g., `CONTRIBUTING.md`).*  
LLM agents should:  

1. Parse **§ 2** to enforce hard constraints.  
2. Reference **§ 3** as the canonical edit workflow.  
3. Apply **§ 4‑6** heuristically to propose refactors.  
4. Populate PR templates per **§ 7** automatically.  
5. Trigger or simulate the hooks in **§ 8** before presenting code suggestions.  

By internalizing this hierarchy—**principles → rules → workflow → structure → tooling**—any LLM‑driven assistant can deliver safe, coherent, and maintainable modifications with minimal human friction.

## Message 4

That's not adhering to the input parameters, I'll provide clarification. The final product should be a clean markdown-block with all of the generalized instruction ordered chronologically by "category", example:



```markdown

# Requirements



**BEFORE ANY ACTION: Conduct a structured review of all recent codebase changes as well as their associated design rationales; synthesize key impacts and verify that any contemplated changes conform fully to existing project organization, standards, and conventions.**



- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.

- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.

- Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.

- Justify the creation of any new files and ensure all additions align with the existing project organization.

- Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.

- Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.

- Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.

- Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.



## Core Principles

- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.

- Maintain inherent simplicity while providing powerful functionality.

- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.

- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.



## General Principles

- Aim for simplicity, clarity, and maintainability in all project aspects

- Favor composition over inheritance when applicable

- Prioritize readability and understandability for future developers

- Ensure all components have a single responsibility

- Coding standards that promote simplicity and maintainability

- Document only integral decisions in a highly condensed form



## Code Organization

- Evaluate the existing codebase structure and identify patterns and anti-patterns

- Consolidate related functionality into cohesive modules

- Minimize dependencies between unrelated components

- Optimize for developer ergonomics and intuitive navigation

- Balance file granularity with overall system comprehensibility

- Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.

- Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.```"```'

```

## Message 5

**Organizing guidelines**

I’m compiling instructions by category—requirements, core principles, code organization, testing, documentation, workflow, and compliance. This helps in streamlining the process and ensuring clarity.

## Message 6

```markdown
# Requirements — *Pre‑change Gate*

**BEFORE ANY ACTION:** Conduct a structured review of all recent codebase changes *and* their documented design rationales; ensure any contemplated work **fully conforms** to the existing architecture, standards, and conventions.

- Scan commit history for patterns/anti‑patterns, naming, and directory taxonomy.  
- Verify existing tests already cover (or partially cover) the area you plan to change.  
- Confirm the change preserves current behavior, clarity, and public contracts.  
- Justify—*in advance*—any new file, dependency, or test you intend to add.  
- Abort or refactor instead of adding if equivalent functionality already exists.  

---

## Core Principles

- **Inherent clarity, structure, simplicity, elegance, precision, intent.**  
- **Single responsibility; composition over inheritance.**  
- **Simplicity conquers chaos:** pursue high‑impact improvements with minimal disruption.  
- **Structural integrity first:** extend the architecture—never deform it.  
- **Maintainability is non‑negotiable:** each edit must raise or, at worst, preserve it.  

---

## General Principles

- Optimize for readability and future developer comprehension.  
- Balance module/file granularity for maximum navigability.  
- Favor powerful yet minimal solutions; avoid over‑generalization.  
- Self‑explanatory code via expressive structure and naming; comments only for nuance.  
- Document only integral decisions concisely; avoid verbose or duplicative notes.  

---

## Code Organization

- Consolidate related functionality into cohesive modules; forbid “misc” buckets.  
- Minimize cross‑component dependencies; enforce clear layering.  
- Maintain a clean, intuitive directory structure geared toward code‑assist tools.  
- Set size budgets: split files when comprehension drops or cohesion fractures.  
- Strictly align any new artifact with the existing project taxonomy.  

---

## Implementation & Coding Standards

- Adhere to established naming, formatting, and lint baselines—no exceptions.  
- Enforce single‑purpose units; prefer dependency injection over hard coupling.  
- Prohibit anomalies, redundancies, and dead code from entering the codebase.  
- Integrate new logic where it naturally belongs before creating fresh modules.  
- Provide comments only to clarify unavoidable complexity or domain nuance.  

---

## Testing

- **Reuse before you write:** extend existing tests; add new ones only when gaps remain.  
- Reject duplicate or flaky tests; stabilize or remove them immediately.  
- Keep coverage ≥ baseline; each PR must run fast, deterministic test suites.  
- Tie every bug fix to a failing test first, then commit the green fix.  

---

## Documentation

- Inline comments ≤ 3 lines, confined to non‑obvious rationale.  
- Lightweight ADRs (≤ 200 words) for architectural decisions needing traceability.  
- Module docstrings: purpose, public API, invariants—nothing more.  

---

## Post‑Edit Clean‑up & Verification

- Remove residual TODOs, unused imports, and style violations introduced by the change.  
- Merge related functions, eliminate duplicates, and realign directory structure.  
- Re‑run full test, lint, type, and security scans; ensure zero regressions.  
- Confirm commit message states **why** and **how** in ≤ 5 lines.  

---

## Enforcement & Automation

- Pre‑commit: formatter, linter, import sorter, duplicate detector, commit‑lint.  
- CI: full tests, strict type checks, coverage gate, security/licensing scan.  
- Post‑merge: auto‑format, regenerate docs, update diagrams, verify build artifacts.  
- CI must block any anomaly: duplicated code, circular import, style or coverage drift.  
- Embed machine‑readable rules (YAML/JSON) so tools can enforce all of the above.  
```

## Message 7

Is there anything from these we could use to improve the rules even further?



```

{

  "goal_map": [

    {

      "explicit_asks": [

        "Analyze and understand the entire codebase, including its architecture, patterns, anti-patterns, and guiding principles.",

        "Summarize the purpose and scope of the codebase.",

        "Deep dive into all documentation, especially in docs/, memory bank, and relevant markdown files.",

        "Evaluate file/directory structure, focusing on a 'filestructure first' perspective (dirtree comprehension).",

        "Familiarize with named files (e.g., @rl-website-initial-notes.md, config.json, package.json, VSCode_Portable_Documentation.md, README.md, DEPLOYMENT.md, IMPORT_STANDARDS.md, etc.).",

        "Install dependencies and successfully run/start the codebase/website.",

        "Verify documentation and README accuracy and coherence with current project state.",

        "Identify and document all codebase functionality, focusing on maintainability, redundancy, and component relationships.",

        "Identify and address codebase sprawl, duplication, and lack of clear component relationships.",

        "Consolidate and standardize documentation (e.g., merging READMEs, deduplicating docs).",

        "Perform sensitivity/impact analyses before making any codebase changes.",

        "Consolidate and de-duplicate code and tests, ensuring existing function is preserved.",

        "Ensure autonomous validation of all actions (guardrails, feedback mechanisms, website/UI checks).",

        "Ensure all codebase modifications are globally consistent; avoid piecemeal/local changes.",

        "Map out a detailed, step-by-step, safe and systematic plan for enacting changes and improvements.",

        "Articulate the author’s/architect’s abstract structural intent (meta-, abstract, and 'vehicle/buttons' metaphors).",

        "Illustrate inter-component/module relationships (possibly via diagrams like mermaid or knowledge graphs).",

        "Identify opportunities for impactful, low-risk, high-value consolidation/refactoring.",

        "Recognize and manage SE-related requirements or opportunities (e.g., analytics, SEO, etc.).",

        "Maintain a perpetual focus on clarity, brevity, and cohesion throughout the project.",

        "Conduct a structured review of all recent codebase changes and associated design rationales before making any change.",

        "Verify that any contemplated modification fully conforms to the current organization, standards, and conventions.",

        "Analyze structure and identify patterns, anti-patterns, and directory/module layout.",

        "Locate and examine relevant tests for coverage; avoid duplicate testing.",

        "Justify creation of new files, modules, or dependencies; prevent unnecessary proliferation.",

        "Ensure changes preserve all existing functionality, clarity, and public contracts.",

        "Link any changes to prior architectural/design rationales, or create a new concise rationale when none exists.",

        "Ensure new tests only cover uncovered/edge-case behavior and integrate them with existing test frameworks.",

        "Clean up residual clutter, inconsistencies, anomalies, duplications, dead code, TODOs, and stylistic deviations after any change.",

        "Run and pass all formatting, linting, and full test suites prior to merge.",

        "Ensure all changes enhance or, at minimum, preserve integrity, maintainability, clarity."

      ],

      "hidden_assumptions": [

        "A thorough, end-to-end mental model of the entire codebase and its documentation is required before making any changes.",

        "Project and codebase are complex, modular, and potentially have significant technical and organizational debt.",

        "Safety, reliability, and the preservation of functionality are paramount during all operations.",

        "All introduced documentation and code changes must avoid unnecessary bloat or fragmentation.",

        "Changes must be trackable, reversible, and their impact must be visible (visual validation/operational guardrails).",

        "Team or downstream agents will rely on outputs/analyses/guidelines; instructions must be explicit, reproducible, and systematized.",

        "Any consolidation must balance file granularity, maintainability, and developer ergonomics.",

        "Codebase may be in flux or have inconsistent practices: mapping, reconciliation, and standardizing are necessary before action.",

        "The codebase is not self-explanatory—contexts from memory banks, usage docs, and even UI/UX must supplement code understanding.",

        "Abstract and meta-level viewpoints are required for truly optimal restructuring, not just local code analysis.",

        "Automated tools (dependency-cruiser, d3, manifests, etc.) are part of the workflow and must be understood and leveraged.",

        "‘Leaving the codebase in an optimal state’ includes both technical and documentation excellence.",

        "Comprehensive, retrievable commit history and rationales exist and are accessible.",

        "The codebase has a defined and documented organization and standard conventions.",

        "Naming, granularity, and directory conventions are already formalized.",

        "Test coverage information (what is covered, what is not) is available and up-to-date.",

        "There is an existing mechanism for referencing or recording architectural/design rationales (e.g., ADRs).",

        "Tooling is in place for linters, formatters, coverage, and CI enforcement.",

        "There are criteria for 'high-impact/minimally disruptive' changes and mechanisms to assess such impact.",

        "Change authors can evaluate and prove the absence of content, functionality, or clarity regressions.",

        "There are actionable size/coupling guidelines (e.g., file/class LOC, dependency rules).",

        "Automation rules and enforcement are outlined and machine-verifiable.",

        "Rollbacks/refactorings are possible and processes are known if redundancy/duplication is detected.",

      ],

      "sub_goals": [

        "Inventory and map all current files, directories, and modules. Produce a high-fidelity dirtree.",

        "Read and synthesize all relevant markdown and documentation files (memory bank, lineage, RulesForAI, etc.).",

        "Identify redundant, duplicate, or outdated files, components, and patterns in both code and documentation.",

        "Survey and comprehend existing tests, scripts, and build/deployment instructions.",

        "Summarize cohesion/relationships between website, utilities, plugins (Sublime/VScode, MCPs, etc.), and the core app logic.",

        "Verify all links (internal and external) within markdown and doc files are correct.",

        "Validate the codebase by successfully building and running the project (including dependencies, website, UI checks).",

        "Map out key functional workflows (e.g., folder scanning, manifests, plugin operation, knowledge-graph generation).",

        "Identify and enumerate all meta-patterns, abstract design principles, and overarching coding philosophies.",

        "Develop a safe, repeatable method for de-duplication/consolidation; test with non-breaking changes first.",

        "Plan and implement guardrails for future autonomous changes (validation, self-checks, feedback loops).",

        "Analyze project dependencies (package.json, config.json, etc.) for bloat, duplication, and consolidation options.",

        "Ensure all essential documentation (README, DEPLOYMENT, etc.) is consolidated, accurate, and up to date.",

        "Provide architectural diagrams or visualizations mapping high-level relationships, module interactions, and code/data flow.",

        "Extract and restate the project’s guiding principles, explicit and implicit, for future contributors/LLM agents.",

        "Propose or design general processes for maintenance, validation, and future refactoring with minimal risk.",

        "Assess developer and user ergonomics: navigation, cohesion, and intuitiveness.",

        "Perform a final, comprehensive audit before any commit, ensuring all consolidation/cleanup goals are achieved.",

        "Review and document all changes since the last successful state, surfacing their rationales.",

        "Enumerate and describe current organization: directory layout, module boundaries, grouping principles.",

        "List and rationalize prevailing patterns and anti-patterns within the codebase.",

        "Cross-reference all proposed changes against the existing structure, patterns, and rationale.",

        "Scan for and map all related tests, including test type, domain, and coverage gaps.",

        "Determine if any contemplated change duplicates or degrades existing structure, clarity, or test coverage.",

        "Prepare or update a brief, context-rich rationale for each intended change; link to ADRs if present.",

        "Explicitly justify every new file, dependency, or module, contextualizing it within existing organization.",

        "Confirm changes uphold naming, design, and directory conventions.",

        "Integrate any new test into the correct location in the test structure; check for and prevent duplicates.",

        "Run comprehensive format/lint/test/coverage checks; ensure all pass without warnings or failures.",

        "Audit for residual irregularities: TODOs, unused imports, anomalies, redundancies.",

        "If duplication or overlap is found, refactor to merge or eliminate before progressing.",

        "Block or revise the change if any non-conformity with standards, structure, or rationale is identified.",

        "Configure or augment automation (lint, format, CI) to enforce all non-negotiable rules going forward.",

      ],

      "blockers": [

        "Lack of complete and accurate mapping of codebase and documentation structure.",

        "Insufficient understanding of project workflows, dependencies, and implicit architectural decisions.",

        "Lack of established or enforced standards for file organization, naming, and module boundaries.",

        "Risk of breaking existing functionality due to incomplete end-to-end comprehension before acting.",

        "Potential for introducing bloat, redundancy, or fragmentation via poorly scoped changes or documentation.",

        "Inconsistent or incomplete documentation, making onboarding or validation difficult.",

        "Inadequate validation/guardrails for assessing the impact of changes (both functional and structural).",

        "Difficulty in visually or programmatically verifying the results of codebase modifications.",

        "Resistance to change or ambiguity on what constitutes an ‘optimal’ codebase state.",

        "Lack of automated or systematic means to perform duplications/consolidations safely.",

        "Absence of a finalized, shared philosophy document to guide abstract design decisions.",

        "Unclear or shifting project requirements, especially regarding SEO, analytics, or intended reusability.",

        "Absence, inconsistency, or inaccessibility of recent commit history and rationales.",

        "Unclear or outdated project organization, structural conventions, or naming policies.",

        "Incomplete, missing, or outdated test coverage information.",

        "Lack of formalized or accessible architectural decision records.",

        "Tooling gaps for lint, format, test, or CI enforcement.",

        "Ambiguity in what constitutes high-impact, low-disruption, or justified changes.",

        "Lack of process for justifying and merging new files, modules, or dependencies.",

        "No enforced mapping between changes and their rationales or ADRs.",

        "Automation is not fully enforced—making it possible for anomalies or violations to slip through.",

      ]

    }

  ]

}

```

## Message 8

**Synthesizing categories**

Integrating "Discovery & Understanding" and "Documentation & Mapping" to streamline the rules markdown. Adding "Automation & Guardrails" for practical implementation.

## Message 9

```markdown
# Requirements — *Pre‑change Gate*

**BEFORE ANY ACTION:**  
Conduct a structured review of recent commits **and** their design rationales; build a current, high‑fidelity picture of the codebase, its documentation, and its operational state.

- Inventory all files/directories → produce an up‑to‑date dirtree.
- Read every markdown/ADR in `docs/`, memory bank, and root READMEs; verify they reflect reality.
- Install dependencies and build/run the full system (app, website, CI) to confirm a green baseline.
- Map existing tests to features; identify uncovered areas.
- Summarize purpose, scope, and guiding principles in ≤ 10 lines for team context.
- Abort or refactor—rather than add—if equivalent behaviour already exists.
- Draft a step‑by‑step, low‑risk plan before touching code.

---

## Codebase Comprehension & Analysis

- Diagram module and component relationships (e.g., Mermaid) for shared understanding.
- Locate patterns *and* anti‑patterns in architecture, naming, and layout.
- Identify sprawl, duplication, or unclear boundaries in code **and** docs.
- Review package/config manifests for unused or duplicate dependencies.
- Note SEO/analytics or other cross‑cutting concerns that changes might affect.

---

## Core Principles

- **Inherent clarity, structure, simplicity, elegance, precision, intent.**
- **Single responsibility; composition over inheritance.**
- **Simplicity conquers chaos:** favour minimal, high‑impact improvements.
- **Structural integrity first:** extend architecture—never deform it.
- **Maintainability is non‑negotiable:** every edit must raise (or at worst keep) that bar.

---

## General Principles

- Optimize for future‑developer comprehension and ergonomics.
- Balance file/module granularity for easy navigation.
- Avoid over‑generalisation; keep requirements actionable.
- Code communicates through structure and naming; comment only for nuance.
- Document only *integral* decisions, concisely.

---

## Code Organization

- Consolidate related functionality; forbid ‘misc’ buckets.
- Minimise cross‑layer dependencies; enforce clear layering.
- Keep directory taxonomy intuitive and assistant‑friendly.
- Split files when cohesion drops or LOC grows unwieldy.
- New artefact? Align with existing hierarchy and justify in commit.

---

## Documentation & Knowledge

- Merge or deduplicate READMEs and docs; eliminate stale files.
- Inline comments ≤ 3 lines, only for non‑obvious rationale.
- ADRs ≤ 200 words for decisions needing traceability.
- Keep links, diagrams, and build instructions accurate and tested.

---

## Implementation Standards

- Follow naming, formatting, and lint baselines—no exceptions.
- Enforce single‑purpose units; inject dependencies rather than hard‑wire them.
- Block anomalies, redundancies, dead code at PR time.
- Integrate new logic where it naturally belongs before creating files.
- Provide concise comments for unavoidable complexity only.

---

## Testing

- **Reuse before you write:** extend existing tests first.
- Add tests only for uncovered behaviour or edge cases.
- Tie each bug fix to a failing test first, then commit the green fix.
- Remove or stabilise flaky/duplicate tests immediately.
- Keep coverage ≥ baseline; fast, deterministic suites are mandatory.

---

## Verification & Cleanup

- Remove residual TODOs, unused imports, and style violations.
- Merge duplicates, realign directories, update docs/diagrams.
- Re‑run full test, lint, type, security, and coverage gates.
- Commit message must state **why** and **how** in ≤ 5 lines.
- Link change to existing ADR; create one if missing.

---

## Automation & Tooling

| Stage | Hook |
|-------|------|
| **Pre‑commit** | format, lint, import‑sort, duplicate detector, commit‑lint |
| **CI** | full tests, strict typing, coverage gate, security/licence scan |
| **Post‑merge** | auto‑format, regenerate docs & diagrams, update changelog |
| **Guardrails** | CI blocks circular imports, style/coverage drift, unused deps |
| **Machine rules** | Encode non‑negotiables in YAML/JSON so tooling can enforce |

---

## Change Management & Governance

- Changes must be **globally** consistent; reject piecemeal fixes.
- Perform sensitivity/impact analysis before merging.
- Provide visual or programmatic validation (screenshots, logs, UI checks).
- Ensure rollback path if unforeseen regressions surface.
- Augment automation as new standards emerge; the rule‑set itself must evolve cleanly.

---
```

## Message 10

please show the full updated markdown with **generalized** instructions (so it works on any codebase)

## Message 11

**Integrating guidelines**

I'm crafting a comprehensive markdown block, merging previous rules and goal_map elements, ensuring it applies universally to any codebase.

## Message 12

```markdown
# Requirements — *Pre‑change Gate*  

**BEFORE ANY ACTION:**  
Conduct a structured review of the *entire* codebase and its documentation—recent commits, design rationales, READMEs, ADRs, CI status, and running build—to ensure any contemplated change **fully conforms** to current architecture, standards, and conventions.

- **Inventory & Dirtree** Generate an up‑to‑date file/directory map; note size, purpose, and cohesion of each area.  
- **Documentation Sweep** Read *all* markdown/ADR files; confirm accuracy against reality, consolidate duplicates.  
- **Build Verification** Install dependencies and run the full system (app, website, services) to establish a green baseline.  
- **Test Coverage Map** Locate existing tests, link them to features, and identify uncovered areas.  
- **Purpose Snapshot** Summarize codebase scope, high‑level intent, and guiding principles in ≤ 10 lines for team context.  
- **Change Plan** Draft a low‑risk, step‑by‑step approach; abort or refactor—rather than add—if equivalent behaviour already exists.  

---

## Codebase Comprehension & Analysis

- **Relationship Diagrams** Illustrate component/module interactions (e.g., Mermaid or Graphviz).  
- **Pattern Audit** List architectural patterns and anti‑patterns; highlight sprawl, duplication, or unclear boundaries.  
- **Dependency Review** Scan manifests for unused, outdated, or duplicate packages; schedule removals.  
- **Workflow Mapping** Trace key functional flows (build, deploy, data pipelines, UI paths).  
- **Cross‑Cutting Concerns** Identify SEO, analytics, security, or performance implications of the area in question.  

---

## Core Principles

| Principle | Description |
|-----------|-------------|
| **Clarity** | Code expresses intent through structure and naming—comments are a last resort. |
| **Simplicity** | The minimal change that delivers the maximal lasting benefit wins. |
| **Single Responsibility** | One clear purpose per file, class, and function; prefer composition over inheritance. |
| **Structural Integrity** | Extend architecture without deforming established boundaries. |
| **Ever‑green Maintainability** | Every edit must raise—never lower—readability, testability, and navigability. |

---

## General Principles

- Optimize for future‑developer comprehension and ergonomics.  
- Balance file/module granularity for intuitive navigation.  
- Avoid over‑generalisation; keep directives concrete and enforceable.  
- Document only *integral* decisions, succinctly; no bloat.  
- Maintain a perpetual focus on clarity, brevity, and cohesion.  

---

## Code Organization

- **Cohesive Modules** Group related logic; ban “misc” buckets.  
- **Dependency Hygiene** Minimise cross‑layer imports; enforce clear layering or onion boundaries.  
- **Directory Taxonomy** Keep structure predictable and assistant‑friendly.  
- **Size Budgets** Split files when cohesion drops or LOC grows unwieldy (define thresholds).  
- **Justified Artefacts** Any new file/module must be pre‑justified and aligned with hierarchy.  

---

## Documentation & Knowledge

- **Deduplicate Docs** Merge overlapping READMEs, outdated notes, and memory‑bank entries.  
- **Inline Comments** ≤ 3 lines, reserved for non‑obvious rationale or domain nuance.  
- **Architectural Decisions** ADRs ≤ 200 words; link each change to its ADR or create one.  
- **Living Links & Diagrams** Verify hyperlinks, regenerate diagrams after structural shifts.  

---

## Implementation Standards

- Follow naming, formatting, lint, and type‑check baselines—**no exceptions**.  
- Enforce single‑purpose units; inject dependencies rather than hard‑wire them.  
- Block anomalies, redundancies, dead code at PR time.  
- Integrate logic where it naturally belongs before creating new files.  
- Provide concise comments only where complexity is unavoidable.  

---

## Testing

- **Reuse Before Write** Extend existing tests first; add new ones only for uncovered behaviour.  
- **Bug‑Fix Flow** Write a failing test ➝ fix ➝ watch it pass.  
- **Quality Gate** Remove or stabilise flaky/duplicate tests; maintain coverage ≥ baseline.  
- **Fast & Deterministic** Suites must run quickly and produce consistent results.  

---

## Verification & Cleanup

- Remove residual TODOs, unused imports, circular deps, style violations.  
- Merge duplicates, realign directories, update docs and diagrams.  
- Run full lint, type, security, and coverage gates; CI must stay green.  
- Commit message must state **why** and **how** in ≤ 5 lines; include ticket/ADR link.  

---

## Automation & Tooling

| Stage | Enforced Hooks |
|-------|----------------|
| **Pre‑commit** | Formatter, linter, import sorter, duplicate detector, commit‑lint |
| **CI (Build)** | Full tests, strict typing, coverage gate, security/licence scan |
| **CI (Quality)** | Circular‑import check, unused‑dependency scan, style guard |
| **Post‑merge** | Auto‑format, regenerate docs/diagrams, update changelog |
| **Rule Encoding** | Store non‑negotiables in machine‑readable config (YAML/JSON) so tooling blocks violations |

---

## Change Management & Governance

- Changes must be **globally** consistent—piecem​eal or local fixes are rejected.  
- Perform sensitivity/impact analysis before merging; include UI or API smoke tests.  
- Provide visual or programmatic validation artifacts (screenshots, logs).  
- Ensure rollback path or feature flag if unforeseen regressions emerge.  
- Continuously evolve automation to cover new standards; keep the rule set itself clean and versioned.  
```
