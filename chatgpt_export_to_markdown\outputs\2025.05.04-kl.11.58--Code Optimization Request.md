# Code Optimization Request

Conversation ID: 68173a5e-eb18-8008-bc03-d168229d50df

## Message 1

User is working on a Python utility called "Playlist Generator" that converts JSON playlist definitions into M3U8 files.

## Message 2

It looks like the file wasn’t uploaded yet. To proceed with shortening your script to under 250 lines, please upload the code file (e.g., `main.py`) so I can analyze and refactor it directly. This will help ensure no functionality is lost while optimizing for brevity.

Would you like help with uploading it now?

## Message 3

Model set context updated.

## Message 4

shorten this code to a maximum of 250lines:

#!/usr/bin/env python3

"""

Playlist Generator - Convert JSON playlist definitions to M3U8 files



Usage:

  python main.py [json_file] [-o OUTPUT] [--prompt]

  python main.py --prompt



Examples:

  python main.py input_playlists/my_playlist.json

  python main.py input_playlists/my_playlist.json -o output/custom_name.m3u8

  python main.py --prompt

"""



import json

import os

import pathlib

import argparse

import sys

import glob

from typing import Optional, List, Dict, Any



# Import Rich for better visual output

try:

    from rich.console import Console

    from rich.panel import Panel

    from rich.table import Table

    from rich.prompt import Prompt, Confirm

    from rich.syntax import Syntax

    from rich import print as rprint

    RICH_AVAILABLE = True

except ImportError:

    RICH_AVAILABLE = False



# Global defaults

DEFAULT_PLAYLIST_DIR = "input_playlists"

DEFAULT_JSON_PATH = os.path.join(DEFAULT_PLAYLIST_DIR, "template.json")



# Initialize Rich console if available

console = Console() if RICH_AVAILABLE else None



def convert_json_to_m3u8(json_filepath: str, output_m3u8_filepath: Optional[str] = None) -> bool:

    """

    Reads a JSON file containing a list of relative media paths

    and converts it into an M3U8 playlist file.



    Args:

        json_filepath (str): Path to the input JSON file.

        output_m3u8_filepath (str, optional): Path for the output M3U8 file.

                                             If None, defaults to the same name

                                             as the JSON file but with .m3u8 extension.

    

    Returns:

        bool: True if conversion was successful, False otherwise.

    """

    json_path = pathlib.Path(json_filepath).resolve()  # Get absolute path to JSON

    base_dir = json_path.parent  # Get the directory containing the JSON file



    if not json_path.is_file():

        if RICH_AVAILABLE:

            console.print(f"[bold red]Error:[/] Input JSON file not found at {json_path}")

        else:

            print(f"Error: Input JSON file not found at {json_path}")

        return False



    # Determine output path

    if output_m3u8_filepath:

        output_path = pathlib.Path(output_m3u8_filepath).resolve()

    else:

        # Default output: same directory and name as JSON, but with .m3u8

        output_path = base_dir / f"{json_path.stem}.m3u8"



    if RICH_AVAILABLE:

        console.print(Panel.fit(

            f"[bold]Playlist Generation[/]\n\n"

            f"📄 Reading playlist: [cyan]{json_path}[/]\n"

            f"📁 Media paths relative to: [cyan]{base_dir}[/]\n"

            f"💾 Writing playlist to: [cyan]{output_path}[/]",

            title="Playlist Generator",

            border_style="green"

        ))

    else:

        print(f"Reading playlist definition from: {json_path}")

        print(f"Media paths assumed relative to: {base_dir}")

        print(f"Writing M3U8 playlist to: {output_path}")



    try:

        with open(json_path, 'r', encoding='utf-8') as f:

            data = json.load(f)



        if "playlist" not in data or not isinstance(data["playlist"], list):

            if RICH_AVAILABLE:

                console.print("[bold red]Error:[/] JSON file must contain a key 'playlist' with a list of file paths.")

            else:

                print("Error: JSON file must contain a key 'playlist' with a list of file paths.")

            return False



        relative_paths = data["playlist"]

        valid_media_paths = []

        skipped_count = 0

        

        # Process media files

        if RICH_AVAILABLE:

            # Rich version with progress bar

            from rich.progress import Progress

            with Progress() as progress:

                task = progress.add_task("[cyan]Processing media files...", total=len(relative_paths))

                

                for path in relative_paths:

                    # Convert to Path object

                    path_obj = pathlib.Path(path)

                    

                    # Check if this is an absolute path or a relative path

                    if path_obj.is_absolute():

                        # Use the absolute path directly

                        abs_media_path = path_obj

                    else:

                        # Construct the full path by joining the base directory and the relative path

                        abs_media_path = base_dir / path

                    

                    # Normalize the path (cleans up .. or . parts, fixes slashes)

                    abs_media_path = abs_media_path.resolve()



                    # Check if the file actually exists

                    if abs_media_path.is_file():

                        valid_media_paths.append(abs_media_path)

                    else:

                        skipped_count += 1

                        progress.print(f"[yellow]⚠️ Warning:[/] File not found, skipping: {abs_media_path}")

                    

                    progress.update(task, advance=1)

        else:

            # Standard version without rich formatting

            for path in relative_paths:

                # Convert to Path object

                path_obj = pathlib.Path(path)

                

                # Check if this is an absolute path or a relative path

                if path_obj.is_absolute():

                    # Use the absolute path directly

                    abs_media_path = path_obj

                else:

                    # Construct the full path by joining the base directory and the relative path

                    abs_media_path = base_dir / path

                

                # Normalize the path (cleans up .. or . parts, fixes slashes)

                abs_media_path = abs_media_path.resolve()



                # Check if the file actually exists

                if abs_media_path.is_file():

                    valid_media_paths.append(abs_media_path)

                else:

                    print(f"Warning: File not found, skipping: {abs_media_path}")

                    skipped_count += 1



        if not valid_media_paths:

            if RICH_AVAILABLE:

                console.print("[bold red]Error:[/] No valid media files found based on the JSON list.")

            else:

                print("Error: No valid media files found based on the JSON list.")

            return False



        # Write the M3U8 file

        with open(output_path, 'w', encoding='utf-8') as f_out:

            f_out.write("#EXTM3U\n")  # M3U8 header



            for media_path in valid_media_paths:

                display_title = media_path.name  # Use filename as title

                # Write extended info (duration -1, title)

                f_out.write(f"#EXTINF:-1,{display_title}\n")

                # Write the absolute path to the media file

                f_out.write(str(media_path) + "\n")  # Convert Path object to string



        # Display summary

        if RICH_AVAILABLE:

            table = Table(title="Playlist Generation Results")

            table.add_column("Item", style="cyan")

            table.add_column("Count", style="green")

            

            table.add_row("Total files in JSON", str(len(relative_paths)))

            table.add_row("Files included in playlist", str(len(valid_media_paths)))

            table.add_row("Files skipped (not found)", str(skipped_count))

            

            console.print(table)

            console.print(f"[bold green]Success:[/] Playlist created at [cyan]{output_path}[/]")

        else:

            print(f"\nSuccessfully created playlist '{output_path}'")

            print(f"Included {len(valid_media_paths)} items.")

            if skipped_count > 0:

                print(f"Skipped {skipped_count} items because files were not found.")



        return True



    except json.JSONDecodeError:

        if RICH_AVAILABLE:

            console.print(f"[bold red]Error:[/] Invalid JSON format in {json_path}")

        else:

            print(f"Error: Invalid JSON format in {json_path}")

        return False

    except Exception as e:

        if RICH_AVAILABLE:

            console.print(f"[bold red]Error:[/] An unexpected error occurred: {e}")

        else:

            print(f"An unexpected error occurred: {e}")

        return False



def prompt_for_input(prompt_text: str, default: Optional[str] = None) -> str:

    """

    Prompt the user for input with an optional default value.

    Uses Rich if available.

    

    Args:

        prompt_text (str): The text to display to the user.

        default (str, optional): Default value if user provides no input.

        

    Returns:

        str: The user's input or the default value.

    """

    if RICH_AVAILABLE:

        return Prompt.ask(prompt_text, default=default)

    else:

        if default:

            user_input = input(f"{prompt_text} [{default}]: ").strip()

            return user_input if user_input else default

        else:

            while True:

                user_input = input(f"{prompt_text}: ").strip()

                if user_input:

                    return user_input

                print("Input cannot be empty. Please try again.")



def browse_for_file(prompt_text: str, default_path: Optional[str] = None, file_extension: Optional[str] = None) -> str:

    """

    Prompt the user to enter a file path, with optional extension filtering.

    

    Args:

        prompt_text (str): The text to display to the user.

        default_path (str, optional): Default file path to use.

        file_extension (str, optional): File extension to validate (e.g., ".json").

        

    Returns:

        str: The validated file path.

    """

    if RICH_AVAILABLE:

        console.print(f"[cyan]{prompt_text}[/]")

        if default_path:

            console.print(f"Default: [green]{default_path}[/]")

    

    while True:

        if RICH_AVAILABLE:

            file_path = Prompt.ask("Enter path", default=default_path or "")

        else:

            default_display = f" [{default_path}]" if default_path else ""

            file_path = input(f"{prompt_text}{default_display}: ").strip()

            if not file_path and default_path:

                file_path = default_path

                

        if not file_path:

            if RICH_AVAILABLE:

                console.print("[yellow]Please enter a file path.[/]")

            else:

                print("Please enter a file path.")

            continue

            

        path = pathlib.Path(file_path)

        

        if file_extension and not str(path).lower().endswith(file_extension.lower()):

            if RICH_AVAILABLE:

                console.print(f"[yellow]File must have {file_extension} extension. Please try again.[/]")

            else:

                print(f"File must have {file_extension} extension. Please try again.")

            continue

            

        return file_path



def confirm_action(prompt_text: str, default: bool = True) -> bool:

    """

    Ask for confirmation with yes/no prompt.

    

    Args:

        prompt_text (str): The question to ask.

        default (bool): Default response (True=Yes, False=No).

        

    Returns:

        bool: True if confirmed, False otherwise.

    """

    if RICH_AVAILABLE:

        return Confirm.ask(prompt_text, default=default)

    else:

        default_hint = "[Y/n]" if default else "[y/N]"

        response = input(f"{prompt_text} {default_hint}: ").strip().lower()

        if not response:

            return default

        return response in ["y", "yes"]



def print_json_example() -> None:

    """Display an example of the expected JSON format."""

    example = {

        "playlist": [

            "media/song1.mp3",

            "media/song2.mp3",

            "media/video1.mp4",

            "../other_folder/media_file.mp3"

        ]

    }

    

    json_str = json.dumps(example, indent=2)

    

    if RICH_AVAILABLE:

        syntax = Syntax(json_str, "json", theme="monokai", line_numbers=True)

        console.print(Panel(syntax, title="Example JSON Format", border_style="blue"))

    else:

        print("\nExample JSON Format:")

        print(json_str)

        print()



def interactive_mode() -> None:

    """

    Run the playlist generator in interactive mode, prompting for all inputs.

    Uses defaults and Rich formatting if available.

    """

    if RICH_AVAILABLE:

        console.print(Panel.fit(

            "[bold cyan]Welcome to the Playlist Generator[/]\n\n"

            "This utility converts JSON playlist definitions to M3U8 files for media players.\n"

            "You'll be guided through the process with interactive prompts.",

            title="Interactive Mode",

            border_style="green"

        ))

    else:

        print("\n===== Playlist Generator Interactive Mode =====\n")

        print("This utility converts JSON playlist definitions to M3U8 files.")

        print("Follow the prompts to generate your playlist.")

        print()

    

    # Show an example of the JSON format

    if confirm_action("Would you like to see an example of the required JSON format?", default=False):

        print_json_example()

    

    # Check if default playlist directory exists

    if not os.path.exists(DEFAULT_PLAYLIST_DIR):

        if RICH_AVAILABLE:

            console.print(f"[yellow]Note:[/] Default playlist directory [cyan]{DEFAULT_PLAYLIST_DIR}[/] not found.")

        else:

            print(f"Note: Default playlist directory '{DEFAULT_PLAYLIST_DIR}' not found.")

        

        # Create it if desired

        if confirm_action("Would you like to create the default playlist directory?", default=True):

            os.makedirs(DEFAULT_PLAYLIST_DIR, exist_ok=True)

            if RICH_AVAILABLE:

                console.print(f"[green]Created directory:[/] [cyan]{DEFAULT_PLAYLIST_DIR}[/]")

            else:

                print(f"Created directory: {DEFAULT_PLAYLIST_DIR}")

    

    # Prompt for the JSON file with default

    json_file = browse_for_file(

        "Enter the path to your JSON playlist file",

        default_path=DEFAULT_JSON_PATH,

        file_extension=".json"

    )

    

    # Check if the file doesn't exist but might be in another directory

    if not os.path.exists(json_file):

        # Check if the user possibly meant a file in the playlists directory

        filename = os.path.basename(json_file)

        possible_path = os.path.join(DEFAULT_PLAYLIST_DIR, filename)

        

        if os.path.exists(possible_path):

            # Suggest the correct path

            if RICH_AVAILABLE:

                console.print(f"[yellow]File not found at '{json_file}'[/]")

                console.print(f"[green]Similar file found:[/] [cyan]{possible_path}[/]")

            else:

                print(f"File not found at '{json_file}'")

                print(f"Similar file found: {possible_path}")

                

            if confirm_action(f"Use '{possible_path}' instead?", default=True):

                json_file = possible_path

            

        # If no similar file found or user declined, offer to create template

        elif confirm_action(

            f"JSON file '{json_file}' doesn't exist. Create a template?", 

            default=True

        ):

            # Ensure directory exists

            os.makedirs(os.path.dirname(os.path.abspath(json_file)), exist_ok=True)

            

            # Create template JSON

            template = {

                "playlist": [

                    "media/song1.mp3",

                    "media/song2.mp3",

                    "media/video1.mp4",

                    "../other_folder/media_file.mp3"

                ]

            }

            

            with open(json_file, 'w', encoding='utf-8') as f:

                json.dump(template, f, indent=2)

                

            if RICH_AVAILABLE:

                console.print(f"[green]Created template JSON:[/] [cyan]{json_file}[/]")

                console.print("[yellow]Note:[/] Edit this template with your actual media files before proceeding.")

            else:

                print(f"Created template JSON: {json_file}")

                print("Note: Edit this template with your actual media files before proceeding.")

                

            if not confirm_action("Continue with playlist generation?", default=True):

                if RICH_AVAILABLE:

                    console.print("[yellow]Operation cancelled.[/] Edit the template and run again.")

                else:

                    print("Operation cancelled. Edit the template and run again.")

                return

    

    # Prompt for output file (optional)

    use_custom_output = confirm_action("Do you want to specify a custom output file?", default=False)

    output_file = None

    

    if use_custom_output:

        # Default output path based on input

        default_output = os.path.splitext(json_file)[0] + ".m3u8"

        output_file = browse_for_file(

            "Enter the path for the output M3U8 file",

            default_path=default_output,

            file_extension=".m3u8"

        )

    

    # Confirm settings

    if RICH_AVAILABLE:

        table = Table(title="Playlist Generation Settings")

        table.add_column("Setting", style="cyan")

        table.add_column("Value", style="green")

        

        table.add_row("Input JSON", json_file)

        table.add_row("Output M3U8", output_file or "<auto-generated>")

        

        console.print(table)

    else:

        print("\nPlaylist Generation Settings:")

        print(f"  Input JSON: {json_file}")

        print(f"  Output M3U8: {'<auto-generated>' if output_file is None else output_file}")

    

    if not confirm_action("\nGenerate playlist with these settings?", default=True):

        if RICH_AVAILABLE:

            console.print("[yellow]Operation cancelled.[/]")

        else:

            print("Operation cancelled.")

        return

    

    # Verify media files first

    total_files, existing_files, missing_files = verify_media_files(json_file)

    

    if total_files > 0 and existing_files < total_files:

        # Show warning about missing files

        if RICH_AVAILABLE:

            console.print(f"[bold yellow]⚠️ Warning:[/] {total_files - existing_files} of {total_files} media files not found.")

            

            if confirm_action("Would you like to see details of missing files?", default=False):

                for i, missing in enumerate(missing_files[:10], 1):  # Show first 10 only

                    console.print(f"  {i}. [red]{missing}[/]")

                

                if len(missing_files) > 10:

                    console.print(f"  ... and {len(missing_files) - 10} more")

            

            # Offer to search for files

            if confirm_action("Would you like to search for missing files in common media directories?", default=False):

                found_count = 0

                

                with console.status("[bold green]Searching for files...[/]"):

                    for missing in missing_files:

                        suggestion = suggest_media_location(missing)

                        if suggestion:

                            found_count += 1

                            console.print(f"[green]Found:[/] {os.path.basename(missing)} at [cyan]{suggestion}[/]")

                

                console.print(f"[green]Found {found_count} of {len(missing_files)} missing files[/]")

                

            # Offer to create playlist from a directory instead

            if existing_files == 0 and confirm_action(

                "No valid files found. Would you like to create a playlist from a directory scan instead?", 

                default=True

            ):

                scan_dir = browse_for_file("Enter directory to scan for media files", file_extension=None)

                

                if os.path.isdir(scan_dir):

                    if output_file is None:

                        output_file = os.path.join(os.path.dirname(json_file), f"{os.path.basename(scan_dir)}.m3u8")

                    

                    if create_playlist_from_directory(scan_dir, output_file):

                        if RICH_AVAILABLE:

                            console.print(f"[bold green]Success:[/] Created playlist from directory scan at [cyan]{output_file}[/]")

                        else:

                            print(f"\nSuccess: Created playlist from directory scan at {output_file}")

                        return

                    else:

                        if RICH_AVAILABLE:

                            console.print(f"[bold red]Error:[/] No media files found in [cyan]{scan_dir}[/]")

                        else:

                            print(f"\nError: No media files found in {scan_dir}")

        else:

            print(f"Warning: {total_files - existing_files} of {total_files} media files not found.")

    

    # Confirm if user wants to proceed with missing files

    if total_files > 0 and existing_files < total_files and existing_files > 0:

        if not confirm_action(

            f"Proceed with generating playlist with only {existing_files} of {total_files} files?", 

            default=True

        ):

            if RICH_AVAILABLE:

                console.print("[yellow]Operation cancelled.[/]")

            else:

                print("Operation cancelled.")

            return

    

    # Call the conversion function

    success = convert_json_to_m3u8(json_file, output_file)

    

    # Final message

    if success:

        if RICH_AVAILABLE:

            console.print("[bold green]Playlist generation completed successfully![/]")

        else:

            print("\nPlaylist generation completed successfully!")

    else:

        if RICH_AVAILABLE:

            console.print("[bold red]Playlist generation failed.[/] Please check the errors above.")

        else:

            print("\nPlaylist generation failed. Please check the errors above.")

    

    # Wait for user to read output

    if RICH_AVAILABLE:

        console.input("\n[italic]Press Enter to exit...[/]")

    else:

        input("\nPress Enter to exit...")



def verify_media_files(json_filepath: str) -> tuple:

    """

    Verify the existence of media files in a JSON playlist.

    

    Args:

        json_filepath (str): Path to the JSON playlist file.

        

    Returns:

        tuple: (total_files, existing_files, missing_files)

    """

    try:

        with open(json_filepath, 'r', encoding='utf-8') as f:

            data = json.load(f)

            

        if "playlist" not in data or not isinstance(data["playlist"], list):

            return 0, 0, []

            

        base_dir = os.path.dirname(os.path.abspath(json_filepath))

        total_files = len(data["playlist"])

        existing_files = 0

        missing_files = []

        

        for path in data["playlist"]:

            # Check if absolute or relative path

            path_obj = pathlib.Path(path)

            if path_obj.is_absolute():

                full_path = path_obj

            else:

                full_path = pathlib.Path(base_dir) / path

                

            # Check if file exists

            if full_path.is_file():

                existing_files += 1

            else:

                missing_files.append(str(full_path))

                

        return total_files, existing_files, missing_files

    except:

        return 0, 0, []



def suggest_media_location(missing_file: str) -> str:

    """

    Try to find alternative locations for a missing media file.

    

    Args:

        missing_file (str): Path to the missing file.

        

    Returns:

        str: Path to the found file, or empty string if not found.

    """

    filename = os.path.basename(missing_file)

    

    # Common media directories to search

    search_dirs = [

        os.path.expanduser("~/Videos"),

        os.path.expanduser("~/Music"),

        os.path.expanduser("~/Desktop"),

        os.path.expanduser("~/Downloads")

    ]

    

    # Search in each directory

    for directory in search_dirs:

        if os.path.exists(directory):

            for root, _, files in os.walk(directory):

                if filename in files:

                    return os.path.join(root, filename)

    

    return ""



def create_playlist_from_directory(directory: str, output_file: str, extensions: list = None) -> bool:

    """

    Create a playlist from all media files in a directory.

    

    Args:

        directory (str): Directory to scan for media files.

        output_file (str): Path for the output M3U8 file.

        extensions (list, optional): List of file extensions to include.

        

    Returns:

        bool: True if successful, False otherwise.

    """

    if not os.path.exists(directory):

        return False

    

    if extensions is None:

        extensions = ['.mp3', '.mp4', '.wav', '.flac', '.ogg', '.m4a', '.mov', '.avi', '.mkv']

    

    # Find all media files

    media_files = []

    for ext in extensions:

        pattern = os.path.join(directory, f"**/*{ext}")

        media_files.extend(glob.glob(pattern, recursive=True))

    

    # Sort files alphabetically

    media_files.sort()

    

    if not media_files:

        return False

    

    # Create the playlist

    with open(output_file, 'w', encoding='utf-8') as f:

        f.write("#EXTM3U\n")

        

        for media_file in media_files:

            display_title = os.path.basename(media_file)

            f.write(f"#EXTINF:-1,{display_title}\n")

            f.write(f"{media_file}\n")

    

    return True



if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="Convert a JSON playlist definition to an M3U8 file.")

    parser.add_argument("json_file", nargs='?', help="Path to the input JSON file.")

    parser.add_argument("-o", "--output", help="Path for the output M3U8 file (optional).")

    parser.add_argument("--prompt", action="store_true", help="Run in interactive mode with prompts.")

    parser.add_argument("--verify", action="store_true", help="Verify media files exist before generating playlist.")

    parser.add_argument("--scan-dir", metavar="DIR", help="Create playlist from all media files in directory.")



    args = parser.parse_args()

    

    if args.prompt:

        interactive_mode()

    elif args.scan_dir:

        # Create a playlist from a directory scan

        if not os.path.isdir(args.scan_dir):

            if RICH_AVAILABLE:

                console.print(f"[bold red]Error:[/] Directory not found: [cyan]{args.scan_dir}[/]")

            else:

                print(f"Error: Directory not found: {args.scan_dir}")

            sys.exit(1)

            

        output_path = args.output

        if not output_path:

            # Default output path is the directory name with .m3u8 extension

            output_path = f"{os.path.basename(args.scan_dir)}.m3u8"

            

        if RICH_AVAILABLE:

            console.print(f"[bold]Creating playlist from directory scan:[/] [cyan]{args.scan_dir}[/]")

            console.print(f"Output file: [cyan]{output_path}[/]")

        else:

            print(f"Creating playlist from directory scan: {args.scan_dir}")

            print(f"Output file: {output_path}")

            

        success = create_playlist_from_directory(args.scan_dir, output_path)

        

        if success:

            if RICH_AVAILABLE:

                console.print(f"[bold green]Success:[/] Created playlist at [cyan]{output_path}[/]")

            else:

                print(f"Success: Created playlist at {output_path}")

        else:

            if RICH_AVAILABLE:

                console.print(f"[bold red]Error:[/] No media files found in [cyan]{args.scan_dir}[/]")

            else:

                print(f"Error: No media files found in {args.scan_dir}")

            sys.exit(1)

            

    elif args.json_file:

        # Verify files if requested

        if args.verify:

            if RICH_AVAILABLE:

                console.print(f"[bold]Verifying media files in:[/] [cyan]{args.json_file}[/]")

            else:

                print(f"Verifying media files in: {args.json_file}")

                

            total_files, existing_files, missing_files = verify_media_files(args.json_file)

            

            if RICH_AVAILABLE:

                table = Table(title="Media File Verification Results")

                table.add_column("Item", style="cyan")

                table.add_column("Count", style="green")

                

                table.add_row("Total files in JSON", str(total_files))

                table.add_row("Files found", str(existing_files))

                table.add_row("Files not found", str(total_files - existing_files))

                

                console.print(table)

                

                if total_files > 0 and existing_files < total_files:

                    console.print(f"[bold yellow]⚠️ Warning:[/] {total_files - existing_files} of {total_files} files not found")

                    

                    if confirm_action("Continue with playlist generation anyway?", default=True):

                        convert_json_to_m3u8(args.json_file, args.output)

                    else:

                        console.print("[yellow]Operation cancelled.[/]")

                else:

                    convert_json_to_m3u8(args.json_file, args.output)

            else:

                print(f"Total files in JSON: {total_files}")

                print(f"Files found: {existing_files}")

                print(f"Files not found: {total_files - existing_files}")

                

                if total_files > 0 and existing_files < total_files:

                    print(f"Warning: {total_files - existing_files} of {total_files} files not found")

                    response = input("Continue with playlist generation anyway? [Y/n]: ").strip().lower()

                    if response == "" or response in ["y", "yes"]:

                        convert_json_to_m3u8(args.json_file, args.output)

                    else:

                        print("Operation cancelled.")

                else:

                    convert_json_to_m3u8(args.json_file, args.output)

        else:

            # Just create the playlist without verification

            convert_json_to_m3u8(args.json_file, args.output)

    else:

        if RICH_AVAILABLE:

            console.print("[bold]Playlist Generator[/]")

            console.print("\n[yellow]No action specified.[/] Use one of the following options:")

            console.print("\n[cyan]Example usage:[/]")

            console.print("  python main.py input_playlists/my_playlist.json")

            console.print("  python main.py input_playlists/my_playlist.json --verify")

            console.print("  python main.py --scan-dir C:/Music")

            console.print("  python main.py --prompt")

        else:

            parser.print_help()

        sys.exit(1)

