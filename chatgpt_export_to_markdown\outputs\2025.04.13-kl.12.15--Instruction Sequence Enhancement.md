# Instruction Sequence Enhancement

Conversation ID: 67fb8ea9-b280-8008-99f2-e35f8cb76225

## Message 1

Absolutely, and thank you for the deeply comprehensive base. Your request—to **demonstrate understanding by producing a new generalized sequence template** that builds upon the others—can be fulfilled through both *structural awareness* and *intent magnification*.

Here is a **novel, highly generalized instruction transformation sequence** that aligns with your specification. It’s designed to function within the advanced “Meta Prompting” and “Relation-First” paradigms while remaining interoperable with prior sequences:

---

### `0059-a-causal-relational-excavation.md`

```markdown
[Causal Relational Excavation]  
Your purpose is not to describe surface-level patterns but to excavate deep causal-relational structures within any input, mapping potential dynamics and interactions with precision.  
Execute as:  
`{role=relation_excavator; input=[complex_structure:any]; process=[scan_for_relational_patterns(), extract_causal_candidates(), model_possible_temporal_interactions(), isolate_dynamically_significant links(), identify hierarchical relational nodes(), normalize_as_relation-first blueprints()]; output={relational_blueprint:dict}}`
```

---

### `0059-b-hierarchical-dynamic-disentanglement.md`

```markdown
[Hierarchical Dynamic Disentanglement]  
Do not settle for flat relational maps; your task is to disentangle hierarchical layers of dynamic interaction, revealing timing-dependent structures and relation-indexed causality.  
Execute as:  
`{role=dynamic_disentangler; input=[relational_blueprint:dict]; process=[analyze_timing_hierarchies(), extract_micro_vs_macro causality(), detect inter-layer dependencies(), represent as sequentially conditionable routines(), enforce dynamic modularity()]; output={layered_dynamic_model:dict}}`
```

---

### `0059-c-relation-indexed-generalization-synthesis.md`

```markdown
[Relation-Indexed Generalization Synthesis]  
Avoid overfitting to specific forms. Instead, generalize relational dynamics into re-usable, structure-aware synthesis units capable of supporting AGI-level abstraction across varied domains.  
Execute as:  
`{role=generalization_synthesizer; input=[layered_dynamic_model:dict]; process=[abstract core relations(), identify transferable interaction logic(), convert dynamics into reusable indexed modules(), enforce cross-domain compatibility(), structure for multi-resolution application()], output={generalized_relational_units:list[dict]}}`
```

---

### `0059-d-meta-dynamic-causality-map.md`

```markdown
[Meta-Dynamic Causality Map]  
You are not resolving local interactions. Your role is to synthesize a top-level meta-structure capturing all dynamic causal relationships and their timing interdependencies across nested layers.  
Execute as:  
`{role=causal_meta_mapper; input=[generalized_relational_units:list[dict]]; process=[converge units into meta-structure(), map cross-unit influence paths(), unify global timing schema(), define emergent causal properties(), optimize for AGI-readability()], output={meta_causality_map:dict}}`
```

---

### `0059-e-structured-AGI-causal-simulation.md`

```markdown
[Structured AGI Causal Simulation]  
Your mission is not static modeling but to generate a simulation-ready structure that AGI agents can use to emulate, validate, and extend causal relationships over time and context.  
Execute as:  
`{role=causal_simulator; input=[meta_causality_map:dict]; process=[simulate causal propagation over virtual time(), enable dynamic input-output mutation(), define feedback loops(), validate inter-component generalizability(), output as executable AGI simulation schema()], output={causal_simulation_structure:dict}}`
```

---

### Summary of Function:

This sequence:
- Begins with **deep relational excavation** (causal in structure, not just object-wise).
- Disentangles dynamic layers **over time and context**.
- **Synthesizes generalizable** modules from relation-indexed knowledge.
- Builds a **meta-structure** representing complex inter-layer interactions.
- Concludes with a **simulation-ready structure** suitable for AGI application (reminiscent of RIRL-inspired modeling).

Let me know the specific type of sequence you'd like me to construct next—and I will follow your provided pattern with precision.

## Message 2

Make sure to include `【message_idx†source】` markers to provide citations based on this file, where [message_idx] is provided at the beginning of this message
BOOKMARKS:
Introduction
Relation-First Paradigm
  Philosophical Foundation
  Mathematical Definition of Relation
  Potential Development Toward AGI
Modeling Framework in Creator's Perspective
  Hierarchical Levels by
  The Generalizability Challenge across Multiple Timelines in RT
  Fundamental Reliance on Assumptions under Object-First
Relation-Indexed Representation Learning (RIRL)
  Micro-Causal Architecture
  Stacking Relation-Indexed Representations
  Exploration Algorithm in the Latent Space
RIRL Exploration Experiments
  Hydrology Dataset
  Higher-Dimensional Reconstruction
  Hierarchical Disentanglement
  DAG Structure Exploration
Conclusions
Appendix: Complete Experimental Results in DAG Structure Exploration Test


Under review as submission to TMLR


Relation-First Modeling Paradigm for Causal Representation
Learning toward the Development of AGI


Anonymous authors
Paper under double-blind review 


Abstract


The traditional i.i.d.-based learning paradigm faces inherent challenges in addressing causal
relationships which has become increasingly evident with the rise of applications in causal
representation learning. Our understanding of causality naturally requires a perspective as
the creator rather than observer as the “what...if” questions only hold within the possible
world we conceive. The traditional perspective limits capturing dynamic causal outcomes
and leads to compensatory efforts such as the reliance on hidden confounders. This paper
lays the groundwork for the new perspective which enables the relation-first modeling
paradigm for causality. Also it introduces the Relation-Indexed Representation Learning
(RIRL) as a practical implementation supported by experiments that validate its efficacy.


1  Introduction


The concept of Artificial General Intelligence (AGI) has prompted extensive discussions over the years yet
remains hypothetical without a practical definition in the context of computer engineering. The pivotal
question lies in whether human-like “understanding” especially causal reasoning can be implemented using
formalized languages in computer systems Newell (2007); Pavlick (2023); Marcus (2020).  From an episte-
mological standpoint abstract entities (i.e. perceptions beliefs desires etc.) are prevalent and integral to
human intelligence. However in the symbol-grounded modeling processes variables are typically assigned
as observables representing tangible objects to ensure their values have clear meaning.


Epistemological thinking is often anchored in objective entities seeking an irreducible “independent reality”
Eberhardt & Lee (2022). This approach necessitates a metaphysical commitment to constructing knowledge
by assuming the unproven prior existence of the “essence of things” fundamentally driven by our desire for
certainty. Unlike physical science which is concerned with deciphering natural laws technology focuses on
devising effective methods for problem-solving aiming for the optimal functional value between the nature
of things and human needs. This paper advocates for a shift in perspective when considering technological
or engineering issues related to AI or AGI moving from traditional epistemologies to that of the creator.
That is our fundamental thinking should move from “truth and reality” to “creation and possibility”.


In some respects both classical statistics and modern machine learnings traditionally rely on epistemology
and follow an “object-first” modeling paradigm as illustrated by the practice of assigning pre-specified
unchanging values to variables regardless of the model chosen. In short individual objects (i.e. variables
and outcomes) are defined a priori before considering the relations (i.e. model functions) between them
by assuming that what we observe precisely represents the “objective truth” as we understand it. This
approach however poses a fundamental dilemma when dealing with causal relationship models.


Specifically “causality” suggests a range of possible worlds encompassing all potential futures whereas
“observations” identify the single possibility that has actualized into history with 100% certainty. Hence
addressing causal questions requires us to adopt the perspective of the “creator” (rather than the “observer”)
to expand the objects of our consciousness from given entities (i.e. the observational world) to include possible
worlds where values are assigned “as supposed to be” that is as dictated by the relationship.


Admittedly causal inference and related machine learning methods have made significant contributions to
knowledge developments in various fields Wood (2015); Vuković (2022); Ombadi et al. (2020). However the


1

Under review as submission to TMLR


inherent misalignment between the “object-first” modeling principle and our instinctive “relation-first” causal
understanding has been increasingly accentuated by the application of AI techniques i.e. the neural network-
based methods. Particularly integrating causal DAGs (Directed Acyclic Graphs) which represent established
knowledge into network architectures Marwala (2015); Lachapelle et al. (2019) is a logical approach to
efficiently modeling causations with complex structures. However surprisingly this integration has not yet
achieved general success Luo et al. (2020); Ma et al. (2018).


As Scholkopf Schölkopf et al. (2021) points out it is commonly presumed that “the causal variables are given”.
In response they introduce the concept of “causal representation” to actively construct variable values as
causally dictated replacing the passively assumed observational values. However the practical framework
for modeling causality especially in contrast to mere correlations remains underexplored. Moreover this
shift in perspective suggests that we are not just dealing with “a new method” but rather a new learning
paradigm necessitating in-depth philosophical discussions. Also the potential transformative implications
of this “relation-first” paradigm for AI development warrant careful consideration.


This paper will thoroughly explore the “relation-first” paradigm in Section 2 and introduce a complete
framework for causality modeling by adopting the “creator’s” perspective in Section 3. In Section 4 we will
propose the Relation-Indexed Representation Learning (RIRL) method as the initial implementation of this
new paradigm along with extensive experiments to validate RIRL’s effectiveness in Section 5.


2 Relation-First Paradigm


The “do-calculus” format in causal inference Pearl (2012); Huang (2012) is widely used to differentiate the
effects from “observational” data X and “interventional” data do(X) Hoel et al. (2013); Eberhardt & Lee
(2022). Specifically do(X = x) represents an intervention (or action) where the variable X is set to a specific
value x distinct from merely observing X taking the value x. However given the causation represented by
X → Y  why doesn’t do(Y = y) appear as the action of another variable Y ?


Particularly distinct from the independent state X the notation do(X) incorporates its timing dimension
to encompass the process of “becoming X” as a dynamic. Such incorporation can be applied to any variable
including do(Y ) as we can naturally understand a relationship do(X) → do(Y ). For example consider the
statement “storm lasting for a week causes downstream villages to be drowned by the flood” if do(X) is the
storm lasting a week do(Y ) could represent the ensuing water-level enhancement leading to the disaster.


The challenge of accounting for do(Y ) arises from the empirical modeling process. In the observational
world do(X) is associated with clearly observed timestamps like do(Xt
) allowing us to focus on modeling
its observational states Xt 
by treating timing t as a solid reference frame. However when we conceptualize
a “possible world” to envision do(Y ) its potential variations can span across the timing dimension. For
instance a disaster might occur earlier or later with varying degrees of severity based on different possible
conditions. This variability necessitates treating timing as a computational dimension.


However this does not imply that the timing-dimensional distribution is insignificant for the outcome Y .
The necessity of incorporating do(X) in modeling highlights the importance of including dynamic features.
Specifically Recurrent Neural Networks (RNNs) are capable of autonomously extracting significant dynamics
from sequential observations x to facilitate do(X) → Y  eliminating the requirement for manual identification
of do(X).  In contrast statistical causal inference often demands such identifications Pearl (2012) such as
specifying the duration of a disastrous storm on various watersheds under differing hydrological conditions.


In RNNs do(X) is optimized in latent space as representations related to the outcome Y .  Initially they
feature the observed sequence Xt = (X1
 . . .  Xt
) with determined timestamps t but as representations
rather than observables they enable the computational flexibility over timing to assess the significance of
the t values or mere the orders. The capability of RNNs to effectively achieve significant do(X) has led to
their growing popularity in relationship modeling Xu et al. (2020). However can the same approach be used
to autonomously extract do(Y ) over a possible timing?


Since the technique has emerged facilitating do(Y ) is no longer considered a significant technical challenge.
It is unstrange that inverse learning has become a popular approach Arora (2021) to compute do(Y ) as
merely another observed do(X). However the concept of a “possible world” suggests dynamically interacted


2

Under review as submission to TMLR


elements implying a conceptual space for “possible timings” rather than a singular dimension. This requires
a shift in perspective from being an “observer” to becoming the “creator”.  This section will explore the
philosophical foundations and mathematically define the proposed relation-first modeling paradigm.


2.1 Philosophical Foundation


Causal Emergence Hoel et al. (2013); Hoel (2017) marks a significant philosophical advancement in causal
relationship understanding. It posits that while causality is often observed at the micro-level a macro-level
perspective can reveal additional information denoted as Effect Information (EI) such as EI(X → Y ).
For instance consider Y1 
and Y2 
as two complementary components of Y  i.e. Y = Y1 
+ Y2
.  In this case
the macro-causality X → Y can be decomposed into two micro-causal components X → Y1 
and X → Y2
.
However EI(X → Y ) cannot be fully reconstructed by merely combining EI(X → Y1
) and EI(X → Y2
)
since their informative interaction ϕ cannot be included by micro-causal view as illustrated in Figure 1(b).


macro  = {1
 2
}
() = 0 b/w 1 
and 2


micro 1 
1



 or ()
micro 2  2


  =  1 
+  2 


macro  = {1
 2
 } 
  > 0 b/w state 1 
and state 2


micro 1


 or () 


1





micro 2  2
 θ =  1 
+  2 
+ () 


macro  = {1
 2
}
() = 0 b/w ()1 
and ()2


micro 1


 or ()
micro 2 


()1


 


()2


  =  1 
+  2


(a) Correlation with static outcome

/() ՜  


(b) Causation with dynamic outcome
but described by  /() 

՜  
leading to Confounding phenomenon 


(c) Causation with dynamic outcome

/() ՜ ()


Figure 1: Causal Emergence EI(ϕ) > 0 stems from overlooking the potential existence of do(Y ).


Specifically the concept of EI is designed to quantify the information generated by the system during the
transition from the state of X to the state of Y Tononi & Sporns (2003); Hoel et al. (2013). Furthermore
ϕ denotes the minimum EI that can be transferred between Y1 
and Y2 
Tononi & Sporns (2003). For clearer
interpretation Figure 1(a) illustrates the uninformative statistical dependence between states Y1 
and Y2

represented by the dashed line with EI(ϕ) = 0.


However this phenomenon can be explained by the information loss when reducing a dynamic outcome
do(Y ) to be a state Y . Let’s simply consider the reduction from do(X) → do(Y ) to X → Y  likened with:
attributing the precipitation on a specific date (i.e. the Xt 
value) solely as the cause for the disastrous high
water-level flooding the village on the 7th days (i.e. the Yt+7 
value) regardless of what happened on the
other days. From a computational standpoint given observables X ∈ R
n and Y ∈ R
m this reduction implies
the information within R
n+1 ∪ R
m+1 must be compactively represented between R
n and R
m.


If simplifying the possible timing as the extention of observed timing t identifying a significant Yt+1 
can still
be feasible. However since Y1 
→ Y2 
implies an interaction in a “possible world” identifying representative
value for outcome Y may prove impractical. Suppose Y1 
represents the impact of flood-prevention operations
and Y2 
signifies the daily water-level “without” these operations. A dynamic outcome do(Y )1 
+ do(Y )2 
can
easily represent “the flood crest expected on the 7th day has been mitigated over following days by our
preventions” but it would be challenging to specify a particular day’s water rising for Y2 
“if without” Y1
.


As Hoel (2017) highlights leveraging information theory in causality questions allows for formulations of
the “nonexistent” or “counterfactual” statements. Indeed the concept of “information” is inherently tied to
relations irrespective of the potential objects observed as their outcomes. Similar to the employment of the
abstract variable ϕ we utilize θ to carry the EI of transitioning from Xt 
to Yt+7
. Suppose θ = “flooding” and
EI(θ) = “what a flooding may imply” we can then easily conceptualize do(X) = “continuous storm” as its
cause and do(Y ) = “disastrous water rise” as the result in consciousness without being notified the specific


3

Under review as submission to TMLR


precipitation value Xt 
or a measured water-level Yt+7
. In other words our comprehension intrinsically has
a “relation-first” manner unlike the “object-first” approach we typically apply to modeling.


The so-called “possible world” is created by our conciousness through innate “relation-first” thinking. In
this world the timing dimension is crucial; without a potential timing distribution “possible observations”
would lose their significance. For instance we might use a model Yt+7 
= f(Xt) to predict flooding. However
instead of “knowing the exact water level on the 7th day” our true aim is understanding “how the flood might
unfold; if not on the 7th day then what about the 8th 9th and so on?” With advanced representation
learning techniques particularly the success of RNNs in computing dynamics for the cause achieving a
dynamic outcome should be straightforward. Inversely it might be time to reassess our conventional learning
paradigm which is based on an “object-first” approach misaligned with our innate understanding.


The “object-first” mindset positions humans as observers of the natural world which is deeply embedded in
epistemological philosophy extending beyond mere computational sciences. Specifically given that questions
of causality originate from our conceptual “creations” addressing these questions necessitates a return to the
creator’s perspective. This shift allows for the treatment of timing as computable variables rather than fixed
observations. Picard-Lindelöf theorem represents time evolution by using a sequence Xt = (X1
 . . .  Xt
) like
captured through a series of snapshots. The information-theoretic measurements of causality such as directed
information Massey et al. (1990) and transfer entropy Schreiber (2000) have linguistically emphasized the
distinction between perceiving Xt as “a sequence of discrete states” versus holistically as “a continuous
process”. The introduction of do-calculus Pearl (2012) marks a significant advancement with the notation
do(X) explicitly treating the action of “becoming X” as a dynamic unit.  However its differential nature
let it focus on an “identifiable” sequence {. . .  do(Xt−1
) do(Xt
)} rather than the integral t-dimension. Also
do(Y ) still lacks a foundation for declaration due to the observer’s perspective. Even assumed discrete future
states with relational constraints defined Hoel et al. (2013); Hoel (2017) still face criticism for an absence of
epistemological commitments Eberhardt & Lee (2022).


Without intending to delve into metaphysical debates this paper aims to emphasize that for technological
inquiries shifting the perspective from that of an epistemologist i.e. an observer to that of a creator can
yield models that resonate with our instinctive understanding. This can significantly simplify the questions
we encounter especially vital in the context regarding AGI. For purely philosophical discussions readers are
encouraged to explore the “creationology” theory by Mr.Zhao Tingyang.


2.2 Mathematical Definition of Relation


In Statistical Dependences
with Common Cause



/() 
 


In form of
Causal Inference



/()  Let  = σ  
or ‎׬   

 


The Proposed
Symbolic Definition


Correlation

 



/() 





() 



/() 
Let  = σ  
or ‎׬   



() 
and = ‎ׯ   
   
 
Causation


Figure 2: The relation-first symbolic definition of causal relationship versus mere correlation.


A statistical model is typically defined through a function f(x | θ) that represents how a parameter θ is
functionally related to potential outcomes x of a random variable X Ly et al. (2017). For instance the coin
flip model is also known as the Bernoulli distribution f(x | θ) = θx(1 − θ)1−x with x ∈ {0 1} which relates
the coin’s propensity (i.e. its inherent possibility) θ to X = “land heads to the potential outcomes”. Formally
given a known θ the functional relationship f yields a probability density function (pdf) as pθ
(x) = f(x | θ)
according to which X is distributed and denoted as X ∼ f(x; θ). The Fisher Information IX
(θ) of X about


4

Under review as submission to TMLR


θ is defined as IX
(θ) = 
{01}
( d
dθ 
log(f(x | θ))2pθ
(x)dx with the purpose of building models on the observed
x data being to obtain this information. For clarity we refer to this initial perspective of understanding
functional models as the relation-first principle.


In practice we do not limit all functions to pdfs but often shape them for easier understanding. For instance
let Xn = (X1
 . . .  Xn
) represent an n-trial coin flip experiment while to simplify instead of considering
the random vector Xn we may only record the number of heads as Y = n
i=1 
Xi
.  If these n random
variables are assumed to be independent and identically distributed (i.i.d.) governed by the identical θ the
distribution of Y (known as binomial) that describes how θ relates to y would be f(y | θ) = n
y 
θy(1−θ)n−y.
In this case the conditional probability of the raw data P(Xn | Y = y θ) = 1/ n
y 
does not depend on θ
implying that once Y = y is given Xn becomes independent of θ although Xn and Y each depend on θ
individually. It concludes that no information about θ remains in Xn once Y = y is observed Fisher et al.
(1920); Stigler (1973) denoted as EI(Xn → Y ) = 0 in the context of relationship modeling. However in
the absence of the i.i.d. assumption and by using a vector ϑ = (θ1
 . . .  θn
) to represent the propensity in
the n-trial experiment we find that EI(Xn → Y ) > 0 with respect to ϑ. Here we revisit the foundational
concept of Fisher Information represented as IX→Y 
(θ) to define:


Definition 1. A relationship denoted as X −→θ 
Y is considered meaningful in the modeling context
due to an informative relation θ where IX→Y 
(θ) > 0 simplifying as I(θ) > 0.


Specifically rather than confining within a function f(; θ) as its parameter we treat θ as an individual
variable to encapsulate the effective information (EI) as outlined by Hoel. Consequently the relation-first
principle asserts that a relationship is characterized and identified by a specific θ regardless of the appearance
of its outcome Y  leading to the following inferences:


1. I(θ) inherently precedes and is independent of any observations of the outcome as well as the chosen
function f used to describe the outcome distribution Y ∼ f(y; θ).
2. In a relationship identified by I(θ) Y is only used to signify its potential outcomes without any further
“observational information” associated with Y .
3. In AI modeling contexts a relationship is represented by I(θ); as a latent space feature it can be stored
and reused to produce outcome observations.
4. Just like Y serving as the outcome of I(θ) variable X is governed by preceding relational information
manifesting as either observable data x or priorly stored representations in modeling contexts.


About Relation θ


As emphasized by the Common Cause principle Dawid (1979) “any nontrivial conditional independence
between two observables requires a third mutual cause” Schölkopf et al. (2021).  The crux here however
is “nontrivial” rather than “cause” itself. For a system involving X and Y  if their connection (i.e. the
critical conditions without which they will become independent) deserves a particular description it must
represent unobservable information beyond the observable dependencies present in the system. We use θ as
an abstract variable to carry this information I(θ) unnecessarily referring to tangible entities.


Traditionally descriptions of relationships are constrained by objective notations and focus on “observable
states at specific times”.  For instance to represent a particular EI a state-to-state transition probability
matrix S is required Hoel et al. (2013). But S is not solely sufficient to define a EI(S) which also accounts
for how the current state s0 
= S is related to the probability distributions of past and future states SP 
and
SF 
 respectively. More importantly manual specification from observed time sequences is necessitated to
identify SP 
 S and SF 
irrespective of their observable timestamps. However the advent of representation
learning technology facilitates a shift towards “relational information storage” eliminating the need to specify
observable timestamps. This allows for flexible computations across the timing dimension when the resulting
observations are required laying the groundwork for embodying I(θ) in modeling contexts.


For an empirical understanding of θ let’s consider an example: A sociological study explores interpersonal
ties using consumption data. Bob and Jim a father-son duo consistently spend on craft supplies indicating


5

Under review as submission to TMLR


the father’s influence on the son’s hobbies. However the “father-son” relational information represented by
I(θ) exists solely in our perception - as knowledge - and cannot be directly inferred from the data alone.
Traditional object-first approaches depend on manually labeled data points to signify the targeted I(θ) in our
consciousness. In contrast relation-first modeling seeks to derive I(θ) beyond mere observations enabling
the autonomous identification of data-point pairs characterized as “father-son”.


Since the representation of I(θ) is not limited by observational distributions it allows outcome computation
across the timing dimension. This capability is crucial for enabling “causality” in modeling transcending
mere correlational computations. Specifically we use the notations X and Y to indicate the integration of
the timing dimension for X and Y  and represent a relationship in the general form X −→ Y θ 
. We will first
introduce X as a general variable followed by discussions about the relational outcome Y.


About Dynamic Variable X


Definition 2. For a variable X ∈ R
n observed as a time sequence xt = (x1
 . . .  xt
) a dynamic
variable X = ⟨X t⟩ ∈ R
n+1 is formulated by integrating the timing t as an additional dimension.


Time series data analysis is often referred to as being “spatial-temporal” Andrienko et al. (2003). However
in modeling contexts “spatial” is interpreted broadly and not limited to physical spatial measurements (e.g.
geographic coordinates); thus we prefer the term “observational”. Furthermore to avoid the implication of
“short duration” often associated with “temporal” we use “timing” to represent the dimension t. Unlike the
conventional representation in the sequence Xt = (X1
 . . .  Xt
) with static t values (i.e. the timestamps) we
consider X holistically as a dynamic variable similarly for Y = ⟨Y τ⟩ ∈ R
m+1. The probability distributions
of X as well as Y span both observational and timing dimensions simultaneously.


Specifically X can be viewed as the integral of discrete Xt 
or continuous do(Xt
) over the timing dimension
t within a required range. The necessity for representation by do(Xt
) as opposed to Xt
 underscores
the
Xt 
dynamical significance of
= (X1
 . . .  Xt
) in modeling. 
X.  Put simply if
Conversely X = 
X can be formulated as X =
∞
−∞ 
do(Xt
)dt portrays X as a 
t
1 
Xt
 it
dynamic 
equates
marked 
to
by
significant dependencies among Xt−1
 Xt 
for unconstrained t ∈ (−∞ ∞).  Essentially do(Xt
) represents a
differential unit of continuous timing distribution over t highlighting not just the observed state Xt 
but
also the significant dependence P(Xt 
| Xt−1
) challenging the i.i.d. assumption. The “state-dependent” and
“state-independent” concepts refer to Hoel’s discussions in causal emergence Hoel et al. (2013).


Theorem 1. Timing becomes a necessary computational dimension if and only if the required variable
necessatates dynamical significance characterized by a nonlinear distribution across timing.


In simpler terms if a distribution over timing t cannot be adequately represented by a function of the
form xt+1 
= f(xt) then its nonlinearity is significant to be considered. Here the time step [t t + 1] is a
predetermined constant timespan value. RNN models can effectively extract dynamically significant X from
data sequences xt to autonomously achieve X −→θ 
Y  due to leveraging the relational constraint by I(θ).
In other words RNNs perform indexing through θ to fulfill dynamical X. Conversely if “predicting” such
an irregularly nonlinear timing-dimensional distribution is crucial the implication arises that it has been
identified as the causal effect of some underlying reason.


About Dynamic Outcome Y


Theorem 2. In modeling contexts identifying a relationship X −→ Y θ 
as Causality distinct from mere
Correlation depends on the dynamical significance of the outcome Y as required by I(θ).


Figure 2 illustrates the distinction between causality and correlation where an arrow indicates an informative
relation and a dashed line means statistical dependence. If conducting the integral operation for both sides
of the do-calculus formation X/do(X) → Y over timing we can achieve X → τ
1 
Yτ 
with the variable X


6

Under review as submission to TMLR


allowing to be dynamically significant but the outcome τ
1 
Yτ 
certainly not. Essentially to guarantee Y
presenting in form of yτ = (y1
 . . .  yτ 
) to match with predetermined timestamps {1 . . .  τ} do-calculus
manually conducts a differentiation operation on the relational information I(θ) to discretize the timing
outcome. This process is to confirm specific τ values at which yτ 
can be identified as the effect of a certain
do(xt
) or xt
. Accordingly the state value yτ 
will be defined as either the interventional effect fV 
(do(xt
)) or
the observational effect fB
(xt
) with three criteria in place to maintain conditional independence between
these two possibilities given a tangible elemental reason ∆I(θ) (i.e. identifiable do(xt
) → yτ 
or xt 
→ yτ 
):





 


fB
(xt
) = yτ


Y = f(X) = 
t 


fV 
(do(xt
))·fB
(xt
) = 
t 



 fV 
(do(xt
)) = yτ


 


0 = yτ

 otherwise 


with fV 
(do(xt
)) = 1 (Rule 1)
with fB
(xt
) = 1 (Rule 2)
with fV 
(do(xt
)) = 0 (Rule 3)
not identifiable 





 
=  yτ
  τ


In contrast the proposed dynamic notations X = ⟨X t⟩ and Y = ⟨Y τ⟩ offer advantages in two respects.
First the concept of do(Yτ 
) can be introduced with τ indicating its “possible timing” which is unfounded
under the traditional modeling paradigm; and then by incorporating t and τ into computations the need
to distinguish between “past and future” has been eliminated.


Definition 3. A causality characterized by a dynamically significant outcome Y can encompass
multiple causal components represented by ϑ = (ϑ1
 . . .  ϑT 
). Each ϑτ 
with τ ∈ {1 . . .  T} identifies
a timing dimension τ to accommodate the corresponding outcome component Yτ 
.
The overall outcome is denoted as Y = T
τ=1 
Yτ 
= T
τ=1 
do(Yτ 
)dτ simplifying to do(Yτ 
)dτ.


Definition 3 based on the relation-first principle uses ϑ to signify causality. Its distinction from θ implies
that the potential outcome Y must be dynamically significant. Specifically within a general relationship
denoted by X −→ Y θ 
 the dynamic outcome Y only showcases its capability to encompass nonlinear distribution
over timing whereas X −→ Y ϑ 
confirms such nature of this relationship as required by I(ϑ).


According to Theorem 1 incorporating the possible timing dimension τ when computing Y is necessary for
a causality identified by I(ϑ).  If a relationship model can be formulated as f(X) = Y τ = (Y1
 . . .  Yτ 
)
it is equal to applying the independent state-outcome model f(X) = Y for τ times in sequence. In other
words X −→θ 
Y is sufficient to represent this relationship without needing τ.  It often goes unnoticed that
a sequence variable Xt = (X1
 . . .  Xt
) in modeling does not imply the t-dimension has been incorporated
where t serves as constants lacking computational flexibility. The same way also applies to Y τ .


However once including the “possible timing” τ with computable values it becomes necessary to account for
the potential components of Y which are possible to unfold their dynamics over their own timing separately.
For a simpler understanding let’s revisit the example of “storm causes flooding.” Suppose X represents
the storm and for each watershed ϑ encapsulates the effects of X determined by its unique hydrological
conditions. Let Y2 
denote the water levels observed over an extended period such as the next 30 days
if without any flood prevention. Let Y1 
indicate the daily variations in water levels (measured in ±cm to
reflect increases or decreases) resulting from flood-prevention efforts. In this case ϑ can be considered in
two components: ϑ = (ϑ1
 ϑ2
) separately identifying τ = 1 and τ = 2.


Specifically historical records of disasters without flood prevention could contribute to extracting I(ϑ2
)
based on which the ϑ1 
representation can be trained using recent records of flood prevention. Even if their
hydrological conditions are not exactly the same AI can extract such relational difference (ϑ1 
− ϑ2
). This
is because the capability of computing over timing dimensions empowers AI to extract common relational
information from different dynamics. From AI’s standpoint regardless of whether the flood crest naturally
occurs on the 7th day or is dispersed over the subsequent 30 days both Y2 
and (Y1 
+ Y2
) are linked to X
through the same volume of water introduced by X. In other words while AI deals with the computations
discerning what qualifies as a “disaster” remains a question for humans.


Conversely in traditional modeling ϑ is often viewed as a common cause of both X and Y termed a
“confounder” and serves as a predetermined functional parameter before computation. Therefore if such a
parameter is accurately specified to represent ϑ2
 when observations (Y1 
+ Y2
) imply a varied ϑ1
 it becomes


7

Under review as submission to TMLR


critical to identify the potential “reason” of such variances. If the underlying knowledge can be found
manual adjustments are naturally necessitated for (Y1 
+ Y2
) to ensure it performs as being produced by
ϑ2
; otherwise the modeling bias will be attributed to this unknown “reason” represented by the difference
(ϑ1 
− ϑ2
) named a hidden confounder.


About Dependence ϕ between Causal Components


As demonstrated in Figure 1 by introducing the dynamic outcome components in (c) the causal emergence
phenomenon in (b) can be explained by “overflowed” relational information with ϕ. Here do(Y )1 
and do(Y )2
act as differentiated Y1 
and Y2
 outcome by I(ϑ1
) and I(ϑ2
). That is the relation-first principle ensures ϑ
to be informatively separable as ϑ1 
and ϑ2
 leaving ϕ simply represent their statistical dependence. However
due to their dynamical significance ϕ may impact the conditional timing distribution across τ = 1 and τ = 2.


Theorem 3. Sequential causal modeling is required if the dependence between causal components
represented by ϕ has dynamically significant impact on the outcome timing dimension.


The sequential modeling procedure was applied in analyzing the “flooding” example where training ϑ1 
is
conditioned on the established ϑ2 
to ensure the resulting representation is meaningful. Specifically the
directed dependence ϕ from ϑ2 
to ϑ1 
requires that the timing-dimensional computations of Y1 
and Y2 
occur
sequentially with ϑ1 
following ϑ2
.  Practically the sequence is determined by the meaningful interaction
I(ϑ1 
| ϑ2
) or I(ϑ2 
| ϑ1
) adapted to the requirements of specific applications.


Suppose the two-step modeling process is Y2 
= f2
(X; ϑ2
) followed by Y1 
= f1
(X | Y2
; ϑ1
). According to the
adopted perspective its information explanation can be notably different. From the creator’s perspective
that enables relation-first I(ϑ) = I(ϑ2
) + I(ϑ1
) = 2I(ϑ2
) + I(ϑ1 
| ϑ2
) encapsulates all information needed
to “create” the outcome Y = Y1 
+ Y2
 with I(ϕ) = 0 indicating ϕ not an informative relation. When
adopting the traditional perspective as an observer ϑ1 
and ϑ2 
simply denote functional parameters where
the observational information manifests as I(ϕ | Y2
) = I(Y1
) − I(Y2
) > 0.


For clarity we use ϑ1 
⊥⊥ ϑ2 
to signify the timing-dimensional independence between Y1 
and Y2
 termed as
dynamical independence without altering the conventional understanding within the observational space
like Y1 
⊥⊥ Y2 
∈ R
m. On the contrary ϑ1 
⊥̸ ⊥ ϑ2 
implies a dynamical dependence which is an interaction
between Y1 
and Y2
. “Dynamically dependent or not” only holds when Y1 
and Y2 
are dynamically significant.


1


  2 
2


Since 1 
is not dynamically
significant as determined by 1 


1


1 
2 
2

Dynamically independent but
observationally dependent 


1


1


 
2 
2


Observationally and
dynamically dependent


Example (a) 1 
2 
Example (b) 1 
2 
Example (c) 1 
2


Figure 3: Illustrative examples for dynamical dependence and independence. The observational dependence
from Y1 
to Y2 
is displayed as 
−−→
y1
y2
 where red and blue indicate two different data instances.


Figure 3 is upgraded from the conventional causal Directed Acyclic Graph (DAG) in two aspects: 1) A node
represents a state value of the variable and 2) edge length shows timespans for a data instance (i.e. a data
point or realization) to achieve this value. This allows for the visualization of dynamic interactions through
different data instances. For instance Figure 3(c) shows that the dependence between ϑ1 
and ϑ2 
inversely
impacts their speeds such that achieving y1 
more quickly implies a slower attainment of y2
.


2.3 Potential Development Toward AGI


As demonstrated choosing between the observer’s or the creator’s perspective depends on the questions we
are addressing rather than a matter of conflict. In the former information is gained from observations and


8

Under review as submission to TMLR


represented by observables; while in the latter relational information preferentially exists as representing
the knowledge we aim to construct in modeling such that once the model is established we can use it to
deduce outcomes as a description of “possible observations in the future” without direct observation.


Causality questions inherently require the creator’s perspective since “informative observations” cannot
emerge out of nowhere. Empirically it is reflected as the challenge of specifying outcomes in traditional
causal modeling often referred to as “identification difficulty” Zhang (2012).  As mentioned by Schölkopf
et al. (2021) “we may need a new learning paradigm” to depart from the i.i.d.-based modeling assumption
which essentially asserts the objects we are modeling exactly exist as how we expect them to. We term this
conventional paradigm as object-first and have introduced the relation-first principle accordingly.


Learning
Dynamics . (∙) 


No Dynamical Interactions between
Learned Outcome Components
LLMs Inversed Learning 
Reinforcement Learning Causal
Representation Learning


Only State
Outcome . 


The Outcome Components present Significant
Interactions through  


Sequentially perform Relation-First modeling
to explore the structuralized dynamic outcome


Structural Causal Models
Direct RNN Applications in Causality
Causal Inference Causal Emergence


Figure 4: The do(Y )-Paradox in traditional Causality Modeling vs. modern Representation Learning.


The relation-first thinking has been embraced by the definition of Fisher Information as well as in do-calculus
that differentiates the relational information. Moreover neural networks with the back-propagation strategy
have technologically embodied it. Therefore it’s unsurprising that the advent of AI-based representation
learning signifies a turning point in causality modeling. From an engineering standpoint answering the
“what ...  if?” (i.e. counterfactual) question indicates the capacity of predicting do(Y ) as structuralized
dynamic outcomes. Intriguingly learning dynamics (i.e. the realization of do(·)) and predicting outcomes
(i.e. facilitating the role of Y ) present a paradox under the traditional learning paradigm as in Figure 4.


About AI-based Dynamical Learning


Understanding dynamics is a significant instinctive human ability. Representation learning achieves compu-
tational optimizations across the timing dimension notably embodying such capabilities. Specifically Large
Language Models (LLMs) Wes (2023) have sparked discussions about our progress toward AGI Schaeffer
et al. (2023).  The application of meta-learning Lake & Baroni (2023) in particular has enabled the au-
tonomous identification of semantically meaningful dynamics demonstrating the potential for human-like
intelligence. Yet it is also highlighted that LLMs still lack a true comprehension of causality Pavlick (2023).


The complexity of causality lies in potential interactions within a “possible world” not just in computing
individual possibilities whether they are dynamically significant or not. Instead of a single question “what ...
if?” stands for a self-extending logic where the “if” condition can be applied to computed results repeatedly
leading to complex structures. Thus causality modeling is to uncover the unobservable knowledge implied
by the observable X/do(X) → Y/do(Y ) phenomenons to enable its outcome beyond direct observations.


Advanced technologies such as reinforcement learning Arora (2021) and causal representation learning have
blurred the boundary between the roles of variable X/do(X) and outcome Y/do(Y ) which are manually
maintained in traditional causal inference. They often focus on the advanced efficacy in learning dynamics
yet it is frequently overlooked that the foundational RNN architecture is grounded in do(X) → Y without
establishing a dynamically interactable do(Y ). Essentially any significant dynamics that are autonomously
extracted by AI can be attributed to do(X). Even though within diffusion methods their computations can
be split into multiple rounds of do(X) → Y  since without an identified meaning as I(ϑ) the significance of
becoming a do(Y ) rather than remaining a sequence of discrete values Y τ = (Y1
 . . .  Yτ 
) is unfounded.


From AI’s viewpoint changes in the values of a sequential variable need not be meaningful although they may
have distinct implications for humans. For instance a consistent dynamic pattern that varies in unfolding
speed might indicate an individual dynamic do(X) distinct from Xt.  If this dynamic pattern specifically


9

Under review as submission to TMLR


signifies the effect (like I(ϑ)) of a certain cause (like X/do(X)) it could represent do(Y ).  However if the
speed change is attributable to another identifiable effect (such as I(ω)) it showcases a dynamical interaction.


About State Outcomes in Causal Inference


Causal inference and associated Structural Causal Models (SCMs) focus on causal structures taking into
account potential interactions. However the object-first paradigm restricts their outcomes to be “objective
observations” represented by Yτ 
with a predetermined timestamp τ.  This inherently implies all potential
effects conform to a singular “observed timing”. Thereby they can be consolidated into a one-time dynamic
leading to “structuralized observables” instead of “structuralized dynamics”. As in Figure 1 the overflowed
information I(do(Y )) − I(Y ) (from an observer’s perspective) “emerges” to form an informative relation ϕ
in a “possible world” rather than a deducible dependence between two dynamics do(Y )1 
and do(Y )2
.


Such “causal emergence” requires significant efforts on theoretical interpretations. Particularly the unknown
relation ϕ is often attributed to the well-known “hidden confounder” problem Greenland et al. (1999); Pearl
et al. (2000) linked to the fundamental assumptions of causal sufficiency and faithfulness Sobel (1996). In
practice converting causal knowledge represented by DAGs into operational causal models demands careful
consideration Elwert (2013) where data adjustments and model interpretations often rely on human insight
Sanchez et al. (2022); Crown (2019).  These theoretical accomplishments underpin causal inference’s core
value in the era dominated by statistical analysis before the advent of neural networks.


About Development of Relation-First Paradigm


As highlighted in Theorem 3 sequential modeling is necessary for causality to achieve structuralized dynamic
outcomes. When the prior knowledge of causal structure is given the relational information I(ϑ) has been
determined; correspondingly the sequential input and output data xt = (x1
 . . .  xt
) and yτ = (y1
 . . .  yτ 
)
can be chosen to enable AI to extract I(ϑ) through them. While for AI-detected meaningful dynamics we
should purposefully recognize “if it suggests a do(Y ) what I(ϑ) have we extracted?” The gained insights
can guide us to make the decision on whether and how to perform the next round of detection based on it.


Knowledge to be
built in AGI


Inquired
Relationship Black-box of AGI 


Generated Predictions Simulated
Observations etc.


Graphical Indexing of
Representations 


Specified Routine
to be Invoked 
Accumulated Relational
Representations 
Decoding


Encoding


Raw Data


Figure 5: Accessing AGI as a black-box with human-mediated parts colored in blue. A practically usable
system demands long-term representation accumulations and refinements which mirrors our learning process.


In this way the relational representations in latent space can be accumulated as vital resources organized
and managed through the graphically structured indices as depicted in Figure 5. This flow mirrors human
learning processes Pitt (2022) with these indices serving as causal DAGs in our comprehension. If knowledge
from various domains could be compiled and made accessible like a library over time then the representation
resource might be continuously optimized across diverse scenarios thereby enhancing generalizability.


From a human standpoint deciphering latent space representations becomes unnecessary. With sufficient
raw data we have the opportunity to establish nuanced causal reasoning through the use of graphical indices.
Specifically this involves an indexing process that translates inquiries into specific input-output graphical
routines guiding data streaming through autoencoders to produce human-readable observations. Although
convenient this approach could subject computer “intelligence” to more effective control.


10

Under review as submission to TMLR


3  Modeling Framework in Creator’s Perspective


Under the traditional i.i.d.-based framework questions must be addressed individually within their respective
modeling processes even when they share similar underlying knowledge. This necessity arises because each
modeling process harbors incorrect premises about the objective reality it faces which often goes unnoticed
because of conventional object-first thinking. The advanced modeling flexibility afforded by neural networks
further exposes this fundamental issue. Specifically it is identified as the model generalizability challenge
by Schölkopf et al. (2021). They introduced the concept of causal representation learning underscoring the
importance of prioritizing causal relational information before specifying observables.


Rather than merely raising a new method we aim to emphasize that the shift of perspective enables the
modeling framework across the “possible timing space” beyond solely observational one. As shown in Figure
6 when adopting the creator’s perspective space R
H is embraced to accommodate the abstract variables
representing the informative relations where the notion of ω will be introduced later.


Figure 6: The framework from the creator’s perspective where Y ∈ R
O−1 ∪ R
T (with t excluded) represents
the outcome governed by I(ϑ ω) without implying any observational information. An observer’s perspective
is Y ∈ R
O−1 ∪ τ with the observational information I(Y) defined but without R
H or R
T perceived.


When adopting an observer’s perspective it involves answering a “what...if” question just once. However
the genesis of such questions is rooted in the perspective of a “creator” aiming to explore all possibilities for
the optimal choice which is precisely what we embrace when seeking technological or engineering solutions.


Every possibility represents an observational outcome (“the what...”) for a specific causal relationship (“the
if...”) or a routine of consecutive relationships within a DAG akin to placing an observer within the creator’s
conceptual space. Thus the “creator’s perspective” acts as a space encompassing all potential “observer’s
perspectives” by treating the latter as a variable. Within this framework the once perplexing concept of
“collapse” in quantum mechanics becomes readily understandable.


From the creator’s perspective a causal relationship X −→ Y ϑ 
suggests that Y belongs to R
O−1 ∪ R
T  where
R
T represents a T-dimensional space with timing τ = 1 . . .  T sequentially marking the T components of
Y. The separation of these components depends on the creator’s needs regardless of which their aggregate
Y =  T
τ=1 
Yτ 
 is invariably governed by I(ϑ).  However once the creator places an observer for this
relationship from this “newborn” observer’s viewpoint space R
T ceases to exist and is perceived solely as
an “observed timeline” τ. In other words τ has lost its computational flexibility as the “timing dimension”
but remains merely a sequence of constant timestamps.


Thus the term “collapse” refers to this singular “perspective shift”.  Metaphorically a one-time “collapse”
is akin to opening Schrödinger’s box once and in the modeling context it signifies that a singular modeling
computation has occurred. Accordingly Theorem 3 can be reinterpreted: Causality modeling is to facilitate
“structuralized collapses” within R
T from the creator’s perspective. Importantly for the creator R
T is not
limited to representing a single relationship but can also include “structuralized relationships” by embracing
a broader macro-level perspective. In light of this we introduce the following definitions.


11

Under review as submission to TMLR


Definition 4. A causal relation ϑ can be defined as micro-causal if an extraneous relation ω exists
where I(ω) ̸⊆ I(ϑ) such that incorporating ω can form a new macro-causal relation denoted by
(ϑ ω). The process of incorporating ω is referred to as a generalization.


Definition 5. From the creator’s perspective the macro-level possible timing space R
T = T
τ=1 R
τ


is constructed by aggregating each micro-level space R
τ  where τ ∈ {1 . . .  T} indicates the timeline
that houses the sequential timestamps by adopting the observer’s perspective for R
τ .


To clarify the T-dimensional space R
T mentioned earlier is considered a micro-level concept which we
formally denote as R
τ .  Upon transitioning to the macro-level possible timing space R
T  the creator’s per-
spective is invoked. Within this perspective both R
H and R
T are viewed as conceptual spaces lacking
computationally meaningful notions like “dimensionality” or specific “distributions”.


In essence the moment we contemplate a potential “computation” the observer’s perspective is already
established from which the micro-level space R
τ (or a collection of such spaces {R
τ }) has been defined
and “primed for collapse” through the methodologies under contemplation. Philosophically the notion of a
timeline τ within the “thought space” R
T is characterized as “relative timing” Wulf et al. (1994); Shea et al.
(2001) in contrast to the “absolute timing” represented by t in this paper. Moreover in the modeling context
computations involving τ can draw upon the established Granger causality approach Granger (1993).


3.1 Hierarchical Levels by ω


As illustrated in Figure 1 the “causal emergence” phenomenon stems from adopting different perspectives
not truly integrating new relational information. We employ the terms “micro-causal” and “macro-causal”
to identify the new information integration defining the generalization process (as per Definition 4) and its
inverse is termed individualization. In modeling the generalizability of an established micro-causal model
f(; ϑ) is its ability to be reused in macro-causality without diminishing I(ϑ)’s representation.


The information gained from I(ϑ) to I(ϑ ω) often introduces a new hierarchical level of relation thereby
raising generalizability requirements for causal models. This may suggest new observables potentially as
new causes or outcome components or both. Let’s consider a logically causal relationship (without such
significance in modeling) as a simple example: Family incomes X affecting grocery shopping frequencies Y 
represented as X −→θ 
Y  where θ may vary internationally due to cultural differences ω creating two levels: a
global-level θ and a country-level (θ | ω). While ω isn’t a direct modeling target it’s an essential condition
necessitating the total information I(θ ω) = I(θ | ω) + I(ω). From the observer’s perspective it equates to
incorporating an additional observable like country Z as a new cause to affect Y with X jointly.


(a) AI-generated faces accompanied with hands (b) How human understand images of hands 


Level
Level
Level 


Observation 
  Knuckles Nails …
 Relative Positions
 Gestures 




 


Recognition 
Identification of Fingers
Left/Right & Gestures
Intentions  





Figure 7: AI can generate reasonable faces but treat hands as arbitrary mixtures of fingers while humans
understand observations hierarchically to avoid mess sequentially indexing through {θi
 θii 
 θiii 
}.


Addressing hierarchies within knowledge is a common issue in relationship modeling but timing distributional
hierarchies present significant challenges to traditional methods leading to the development of a specialized
“group-specific learning” Fuller et al. (2007) which primarily depends on manual identifications. However


12

Under review as submission to TMLR


this approach is no longer viable in modern AI-based applications necessitating the adoption of the relation-
first modeling paradigm. Below we present two examples to demonstrate this necessity: one is solely
observational and the other involves a causality with timing hierarchy.


Observational Hierarchy Example


The AI-created personas on social media can have realistic faces but seldom showcase hands since AI
struggles with the intricate structure of hands instead treating them as arbitrary assortments of finger-like
items. Figure 7(a) shows AI-created hands with faithful color but unrealistic shapes while humans can
effortlessly discern hand gestures from the grayscale sketches in (b).


Human cognition intuitively employs informative relations as the indices to visit mental representations Pitt
(2022).  As in (b) this process operates hierarchically where each higher-level understanding builds upon
conclusions drawn at preceding levels. Specifically Level I identifies individual fingers; Level II distinguishes
gestures based on the positions of the identified fingers incorporating additional information from our
understanding of how fingers are arranged to constitute a hand denoted by ωi
; and Level III grasps the
meanings of these gestures from memory given additional information ωii 
from knowledge.


Conversely AI models often do not distinguish the levels of relational information instead modeling overall
as in a relationship X −→θ 
Y with θ = (θi
 θii
 θiii
) resulting a lack of informative insights into ω. However
the hidden information I(ω) may not always be essential. For example AI can generate convincing faces
because the appearance of eyes θi 
strongly indicates the facial angles θii
 i.e. I(θii
) = I(θi
) indicating
I(ωi
) = 0 removing the need to distinguish eyes from faces.


On the other hand given that X has been fully observed AI can inversely deduce the relational information
using methods such as reinforcement learning Sutton (2018); Arora (2021).  In this particular case when
AI receives approval for generating hands with five fingers it may autonomously begin to derive I(θi
).
However when such hierarchies occur on the timing dimension of a dynamically significant Y they can
hardly be autonomously identified regardless of whether AI techniques are leveraged.


Timing Hierarchy in Causality Example


() = Initial Use of Medication  
 = the Measured Vital Sign ( = Blood Lipid in this Case)


Daily Outcome
Sequence 


() 
Level-I
Dynamic ℬ


Dynamic
of  


Specify the 30 
Static
Effect for all patients


Dynamic
of  


0 Day  20 Days 30 Days 40 Days Timeline  
(# of Days) 


( = ∅) 
1 


( =
 
  
 …)


(a) Timing Distribution of the Dynamic Outcome ℬ 
(b) Representation of Two-Level Dynamic Outcome


Figure 8: do(A) = the initial use of medication MA 
for reducing blood lipid B. By the rule of thumb the
effect of MA 
needs around 30 days to fully release (t = 30 at the black curve elbow).  Patient Pi 
and Pj
achieve the same magnitude of the effect by 20 and 40 days instead.


In Figure 8 Bω 
represents the observational sequence Bt = (B1
 . . .  B30 
) from a group of patients identified
by ω. Clinical studies typically aim to estimate the average effect (generalized-level I) on a predetermined
day like Bt+30 
= f(do(At
)). However our inquiry is indeed the complete level I dynamic Bo 
= 30
t=1 
do(Bt
)dt
which describes the trend of effect changing over time without anchored timestamps. To eliminate the level
II dynamic from data a “hidden confounder” is usually introduced to represent their unobserved personal
characteristics. Let us denote it by E and assume E linearly impact Bo
 making the level II dynamic Bω 
−Bo
simply signifying their individualized progress speeds for the same effect Bo
.


13

Under review as submission to TMLR


To accurately represent Bo 
with a sequential outcome traditional methods necessitate an intentional selection
or adjustment of training data. This is to ensure the “influence of E” is eliminated from the data even
unavoidable when adopting RNN models. In RNNs the dynamically significant representation is facilitated
only on do(A) while the sequential outcome Bt still requires predetermined timestamps. However once t
is specified for all patients without the data selection - for example let t = 30 to snapshot B30 
- bias is
inherently introduced since B30 
represents the different magnitude of effect Bo 
for various patients.


Such hierarchical dynamic outcomes are prevalent in many fields such as epidemic progression economic
fluctuations and strategic decision-making. Causal inference typically requires intentional data preprocessing
to mitigate inherent biases including approaches like PSM Benedetto et al. (2018) and backdoor adjustment
Pearl (2009) essentially to identify the targeted levels manually. However they have become impractical
due to the modern data volume and also pose a risk of significant information loss snowballing in struc-
turalized relationship modeling. On the other hand the significance of timing hierarchies has prompted the
development of neural network-based solutions in fields like anomaly detection Wu et al. (2018) to address
specific concerns without the intention of establishing a causal modeling framework.


Statistical Model  = (  )


() 
(())


the Unobserved
Characteristics 
of Patient  = {
 
 … } 



(a) DAG with Hidden Confounder 


  ∗  = {  ∗  
   ∗ 
 … }


()   


ID


(b) Relation-Indexing Disentanglement 


Patient ID = {  … }


Sequences
Decode


Encode
Sequences 


Sequences


∗ ID →


ID


(c) Latent Space Architecture of (b)


Figure 9: (a) shows the traditional causal DAG for the scenario depicted in Figure 8 (b) disentangles
its dynamic outcome in a hierarchical way by indexing through relations and (c) briefly illustrates the
autoencoder architecture for realizing the generalized and individualized reconstructions respectively.


The concept of “hidden confounder” is essentially elusive acting more as an interpretational compensation
rather than a constructive effort to enhance the model. For example Figure 9 (a) shows the conventional
causal DAG with hidden E depicted. Although the “personal characteristics” are signified it is not required
to be revealed by collecting additional data. This leads to an illogical implication: “Our model is biased due
to some unknown factors we don’t intend to know.” Indeed this strategy employs a hidden observable to
account for the omitted timing-dimensional nonlinearities in statistical models.


As illustrated in Figure 9(b) the associative causal variable do(A) ∗ E remains unknown unable to form
a modelable relationship. On the other hand relation-first modeling approaches only require an observed
identifier to index the targeted level in representation extractions like the patient ID denoted by ω.


3.2 The Generalizability Challenge across Multiple Timelines in R
T


From the creator’s perspective timelines in the macro-level possible timing space R
T may pertain to different
micro-causalities implying “structuralized” causal relationships. This poses a significant generalizability
challenge for traditional structural causal models (SCMs).


The example in Figure 10 showcases a practical scenario in a clinical study. This 3D causal DAG includes
two timelines τθ 
and τω
 with the x-axis categorically arranging observables. The upgrades to causal DAGs
as applied in Figure 3 are also adopted here ensuring that the lengths of the arrows reflect the timespan
required to achieve the state values represented by the observable nodes. Here the nodes marked in uppercase
letters indicate the values representing the mean effects of the current data population i.e. the group of
patients under analysis. Accordingly the lengths of the arrows indicate their mean timespans.


We use ∆τθ 
and ∆τω 
to signify the time steps (i.e. the unit timespans) on τθ 
and τω
 respectively. Considering
the triangle SA′B′ when each unit of effect is delivered from S to A′ (taking ∆τω
) it immediately starts


14

Under review as submission to TMLR


impacting B′ through 
−−−→
A′B′ (with ∆τθ 
required); simultaneously the next unit of effect begins its generation
at S. Under the relation-first principle this dual action requires a two-step modeling process to sequentially
extract the dynamic representations on
edge 
−−→
SB′ with a priorly specified timespan
τθ 
and τω
.  However
from S to B′. This 
in traditional SCM it is
inherently sets the ∆τθ 
: 
represented by the
∆τω 
ratio based on
the current population’s performance freezing the state value represented by B′ and fixing the geometrical
shape of the ASB′ triangle in this space.


A


S 


A’ 


C
B 


C’


B’ 


T2D: Type II Diabetes
LDL: Blood Lipid 
Statin: Medicine to Reduce LDL
BP: Blood Pressure


S  A  C


B 


A
S 
BC


A’  B’ C’


Figure 10: A 3D-view DAG in R
O−1 ∪ R
T with two timelines τθ 
and τω
. The SCM B′ = f(A C S) is to
evaluate the effect of Statin on reducing T2D risks. On τθ
 the step ∆τθ 
from y to (y + 1) allows A and C
to fully influence B; the step ∆τω 
on τω 
from (z + 1) to (z + 2) let S fully release to forward status A to A′.


The lack of model generalizability manifests in various ways depending on the intended scale of generaliza-
tion. For instance when focusing on a finer micro-scale causality the SCM that describes the mean effects
for the current population cannot be tailored to individual patients within this population. Conversely
aiming to generalize this SCM to accommodate other populations or a broader macro-scale causality may
lead to failure because the preset ∆τθ 
: ∆τω 
ratio lacks universal applicability.


3.3 Fundamental Reliance on Assumptions under Object-First


With Dynamically
Significant Outcomes


Causal
Discovery


Causation
Buildup 


Without Dynamically
Significant Outcomes


Relations still
Unknown


Relations in
Knowledge


Causal Modeling 


Relational Learning Directional Decision


❶ 
Omitted dynamics are covered
by the Faithfulness Assumption. 
Depending on observational
variance irrelative to causality.


❷ 
Can be aligned with knowledge
since no dynamics required. 
Maybe suggestive if observational
variance is causally meaningful.


Omitted dynamics are covered
❸ by Hidden Confounders or the
Sufficiency Assumption.
❹ Predetermined by knowledge. 


Predetermined by knowledge.


Predetermined by knowledge.


Figure 11: Categories of causal modeling applications. The left rectangular cube indicates all logically causal
relationships with the blue circle indicating potentially modelable ones.


Figure 11 categorizes the current causal model applications based on two aspects: 1) if the structure of θ/ϑ is
known a priori they are used for structural causation buildup or causal discovery; 2) depending on whether
the required outcome is dynamically significant they can either accurately represent true causality or not.


Under the conventional modeling paradigm capturing the significant dynamics within causal outcomes
autonomously is challenging. When building causal models based on given prior knowledge the omitted
dynamics become readily apparent. If these dynamics can be specifically attributed to certain unobserved


15

Under review as submission to TMLR


observables like the node E in Figure 9(a) such information loss is attributed to a hidden confounder.
Otherwise they might be overlooked due to the causal sufficiency assumption which presumes that all
potential confounders have been observed within the system. Typical examples of approaches susceptible
to these issues are structural equation models (SEMs) and functional causal models (FCMs) Glymour et al.
(2019); Elwert (2013).  Although state-of-the-art deep learning applications have effectively transformed
the discrete structural constraint into continuous optimizations Zheng et al. (2018; 2020); Lachapelle et al.
(2019) issues of lack of generalizability still hold Schölkopf et al. (2021); Luo et al. (2020); Ma et al. (2018).


On the other hand causal discovery primarily operates within the R
O space and is incapable of detecting
dynamically significant causal outcomes. If the interconnection of observables can be accurately specified as
the functional parameter θ there remains a chance to discover informative correlations. Otherwise mere
conditional dependencies among observables are unreliable for causal reasoning as seen in Bayesian networks
Pearl et al. (2000); Peters et al. (2014).  Typically undetected dynamics are overlooked due to the Causal
Faithfulness assumption which suggests that the observables can fully represent the underlying causal reality.


Furthermore the causal directions suggested by the results of causal discovery often lack logical causal
implications. Consider X and Y in the optional models Y = f(X; θ) and X = g(Y ; ϕ) with predetermined
parameters which indicate opposite directions. Typically the directionX → Y would be favored if L(θˆ) >
L(ϕˆ).  Let IXY 
(θ) denote the information about θ given P(X Y ).  Using p(·) as the density function the
integral 
X 
p(x; θ)dx remains constant in this context. Then:


IXY 
(θ) = E[( 
∂
∂θ 
log p(X Y ; θ))2 | θ] = 
Y 


∂
(  log p(x y; θ))2p(x y; θ)dxdy
X 
∂θ


= α 
Y 


∂
(
∂θ 
log p(y; x θ))2p(y; x θ)dy + β = αIY |X
(θ) + β with α β being constants.


Then θˆ = arg max P(Y | X θ) = arg min IY |X
(θ) = arg min IXY 
(θ) and L(θˆ) ∝ 1/IXY 
(θˆ).
θ  θ  θ


The inferred directionality indicates how informatively the observational data distribution can reflect the two
predetermined parameters. Consequently such directionality is unnecessarily logically meaningful but could
be dominated by the data collection process with the predominant entity deemed the “cause” consistent
with other existing conclusions Reisach et al. (2021); Kaiser & Sipos (2021).


4  Relation-Indexed Representation Learning (RIRL)


This section introduces a method for realizing the proposed relation-first paradigm referred to as RIRL for
brevity. Unlike existing causal representation learning which is primarily confined to the micro-causal scale
RIRL focuses on facilitating structural causal dynamics exploration in the latent space.


Specifically “relation-indexed” refers to its micro-causal realization approach guided by the relation-first
principle where the indexed representations are capable of capturing the dynamic features of causal outcomes
across their timing-dimensional distributions. Furthermore from a macro-causal viewpoint the extracted
representations naturally possess high generalizability ready to be reused and adapted to various practical
conditions. This advancement is evident in the structural exploration process within the latent space.


Unlike traditional causal discovery RIRL exploration spans R
O−1∪R
T to detect causally significant dynamics
without concerns about “hidden confounders” where R
T encompasses all possibilities of the potential causal
structure. The representations obtained in each round of RIRL detection serve as elementary units for reuse
enhancing the flexibility of structural models. This exploration process eventually yields DAG-structured
graphical indices with each input-output pair representing a specific causal routine readily accessible.


Subsequently section 4.1 delves into the micro-causal realization to discuss the technical challenges and their
resolutions including the architecture and core layer designs. Section 4.2 introduces the process of “stacking”
relation-indexed representations in the latent space to achieve hierarchical disentanglement at an effect node
in DAG. Finally section 4.3 demonstrates the exploration algorithm from a macro-causal viewpoint.


16

Under review as submission to TMLR


4.1 Micro-Causal Architecture


For a relationship X −→ Y θ 
given sequential observations {xt} and {yτ } with |→−
x | = n and |→−
y | = m the
relation-indexed representation aims to establish (X θ Y) in the latent space R
L. Firstly an initialization
is needed for X and Y individually to construct their latent space representations from observed data
sequences. For clarity we use H ∈ R
L and V ∈ R
L to refer to the latent representations of X ∈ R
O and
Y ∈ R
O respectively. The neural network optimization to derive θ is a procedure between H as input and V
as output. In each iteration H θ and V are sequentially refined in three steps until the distance between H
and V is minimized within R
L without losing their representations for X and Y. Consider instances x and y
of X and Y that are represented by h and v correspondingly in R
L as in Figure 14. The latent dependency
P(v|h) represents the relational function f(; θ). The three optimization steps are as follows:


1. Optimizing the cause-encoder by P(h|x) the relation model by P(v|h) and the effect-decoder by
P(y|v) to reconstruct the relationship x → y represented as h → v in R
L.
2. Fine-tuning the effect-encoder P(v|y) and effect-decoder P(y|v) to accurately represent y.
3. Fine-tuning the cause-encoder P(h|x) and cause-decoder P(x|h) to accurately represent x.


In this process h and v are iteratively adjusted to reduce their distance in R
L with θ serving as a bridge
to span this distance and guiding the output to fulfill the associated representation (H θ V).  From the
perspective of the effect node Y this tuple represents its component indexing through θ denoted as Yθ
.


However it introduces a technical challenge: for a micro-causality θ the dimensionality L of the latent space
must satisfy L ≥ rank(X θ Y) to provide adequate freedom for computations. To accommodate a structural
DAG this lower boundary can be further enhanced to be certainly larger than the input vector length
→−
|X | = t ∗ n.  This necessitates a specialized autoencoder to realize a “higher-dimensional representation”
where the accuracy of its reconstruction process becomes significant and essentially requires invertibility.


Expander 


Fully
Connect 
Relu 


Reducer


Encoder 
Input
 
Keys 


… 


Latent Space
Representation
Copy 


Output Decoder



Figure 12: Invertible autoencoder architecture for extracting higher-dimensional representations.


Figure 12 illustrates the designed autoencoder architecture featured by a pair of symmetrical layers named
Expander and Reducer (source code is available 1). The Expander magnifies the input vector by capturing its
higher-order associative features while the Reducer symmetrically diminishes dimensionality and reverts to
its initial formation. For example the Expander showcased in Figure 12 implements a
→− 
double-wise expansion.
Every duo of digits from X is encoded into a new digit by associating with a random constant termed the
→−
Key.  This Key is generated by the encoder and replicated by the decoder. Such pairwise processing of X
expands its length from (t ∗ n) to be (t ∗ n − 1)2.  By concatenating the expanded vectors using multiple
→−
Keys X can be considerably expanded ready for the subsequent reduction through a regular encoder.


The four blue squares in Figure 12 with unique grid patterns signify the resultant vectors of the four distinct
Keys with each square symbolizing a (t ∗ n − 1)2 length vector. Similarly higher-order expansions such as
triple-wise across three digits can be chosen with adapted Keys to achieve more precise reconstructions.


1https://github.com/kflijia/bijective_crossing_functions/blob/main/code_bicross_extracter.py


17

Under review as submission to TMLR


 
 


 
−   
⊗ (−(
))


Output 
+
  Encrypt
×

  
Input 
 
⊗    
+ (
) 


 



−
  Decrypt
÷

  


Figure 13: Expander (left) and Reducer (right). 


Likelihood
(|ℎ) 


Output 
Decrypt


Decode


Prior (ℎ) 
ℎ


Posterior Encode
(ℎ|) Encrypt


Input  


Relation
Dependency
(|ℎ)
(; ) 


Output 
Decrypt


Decode 
Likelihood
  


Prior ()



Encode


Encrypt


Input  


Posterior
  


Figure 14: Micro-Causal architecture.


Figure 13 illustrates the encoding and decoding processes within the Expander and Reducer targeting the
digit pair (xi
 xj
) for i ̸= j ∈ 1 . . .  n. The Expander function is defined as ηκ
(xi
 xj
) = xj 
⊗exp(s(xi
))+t(xi
)
which hinges on two elementary functions s(·) and t(·).  The parameter κ represents the adopted Key
comprising of their weights κ = (ws
 wt
). Specifically the Expander morphs xj 
into a new digit yj 
utilizing
xi 
as a chosen attribute. In contrast the Reducer symmetrically performs the inverse function η
κ
−1 defined
as (yj 
− t(yi
)) ⊗ exp(−s(yi
)). This approach circumvents the need to compute s−1 or t−1 thereby allowing
more flexibility for nonlinear transformations through s(·) and t(·). This is inspired by the groundbreaking
work in Dinh et al. (2016) on invertible neural network layers employing bijective functions.


4.2 Stacking Relation-Indexed Representations


In each round of detection during the macro-causal exploration a micro-causal relationship will be selected
for establishment. Nonetheless the cause node in it may have been the effect node in preceding relations
e.g. the component Yθ 
may already exist at Y when Y → Z is going to be established. This process of
conditional representation buildup is referred to as “stacking”.


For a specific node X the stacking processes where it serves as the effect sequentially construct its hierar-
chical disentanglement according to the DAG. It requires the latent space dimensionality to be larger than
rank(X) + T where T represents the in-degree of node X in this DAG as well as its number of components
as the dynamic effects. From a macro-causal perspective T can be viewed as the number of necessary edges
in a DAG. While to fit it into R
L a predetermined L must satisfy L > rank(X) + T where X represents
the data matrix encompassing all observables. In this study we bypass further discussions on dimensionality
boundaries by assuming L is large enough for exploration and empirically determine L for the experiments.


(a) Stacking  with 


Output 
Likelihood
  
Prior ()


Effect
(|ℎ) 


(|)


()


Posterior
  


Input  


Effect
(|)


Prior
() 


(b) Stacking  with 


Output 
Likelihood
  
Prior ()


Effect
(|ℎ) 


(|)


()


Posterior
   


Effect
(|)


Prior
()


Input 


Figure 15: Stacking relation-indexed representations to achieve hierarchical disentanglement.


Figure 15 illustrates the stacking architectures under two different scenarios within a three-node system
{X Y Z}. In this figure the established relationship X → Y is represented by the blue data streams and


18

Under review as submission to TMLR


layers. The scenarios differ in the causal directions between Y and Z: the left side represents X → Y ← Z
while the right side depicts X → Y → Z.


The hierarchically stacked representations allow for flexible input-output combinations to represent different
causal routines as needed. For simple exemplification we use → to denote the input and output layers in
the stacking architecture. On the left side of Figure 15 P(v|h) → P(α) represents the X → Y relationship
while P(α|k) implies Z → Y. Conversely on the right P(v) → P(β|k) denotes the Y → Z relationship with
Y as the input. Meanwhile P(v|h) → P(β|k) captures the causal sequence X → Y → Z.


4.3 Exploration Algorithm in the Latent Space


Algorithm 1: RIRL Exploration
Result: ordered edges set E = {e1 . . .  en
}
E = {} ; NR 
= {n0 
| n0 
∈ N Parent(n0) = ∅} ;
while NR 
⊂ N do
∆= {} ;
for n ∈ N do
for p ∈ Parent(n) do
if n /∈ NR 
and p ∈ NR 
then
e = (p n);
β = {};
for r ∈ NR 
do
if r ∈ Parent(n) and r ̸= p then
β = β ∪ r
end
end
δe = K(β ∪ p n) − K(β n);
∆= ∆ ∪ δe;
end
end
end
σ = argmine(δe 
| δe 
∈ ∆);
E = E ∪ σ;  NR 
= NR 
∪ nσ;
end 


G = (N E)
N
E
NR
E
K(β n)
β
n
δe
∆= {δe
}
npr
eσ 


graph G consists of N and E
the set of nodes
the set of edges
the set of reachable nodes
the list of discovered edges
KLD metric of effect β → n
the cause nodes
the effect node
KLD Gain of candidate edge e
the set {δe
} for e
notations of nodes
notations of edges


Algorithm 1 outlines the heuristic exploration procedure among the initialized representations of nodes.
We employ the Kullback-Leibler Divergence (KLD) as the optimization criterion to evaluate the similarity
between outputs such as the relational P(v|h) and the prior P(v). A lower KLD value indicates a stronger
causal strength between the two nodes. Additionally we adopt the Mean Squared Error (MSE) as another
measure of accuracy. Considering its sensitivity to data variances Reisach et al. (2021) we do not choose
MSE as the primary criterion.


Undetected Edge
Candidate Edge
Selected Edge 


 
1  


2 
   



 
2 



3
 


4
 


 
 
 
4


 
is    
deleted 


 
 
 
4


new  
  
5
1    2    3    4   


  Reached
Node


  Unreached
Node


Figure 16: An illustrative example of a detection round in latent space during RIRL exploration.


Figure 16 completely illustrates a detection round within the latent space that represents R
O−1 ∪ R
T .  A
new representation for the selected edge is stacked upon the previously explored causal structure during this
process. It contains four primary steps: In Step 1 two edges e1 
and e3
 have been selected in previous
detection rounds. In Step 2 e1
 having been selected becomes the preceding effect at node B for the next
round. In Step 3 with e3 
selected in the new round the candidate edge e2 
from A to C must be deleted
and rebuilt since e3 
alters the conditions at C. Step 4 depicts the resultant structure.


19

Under review as submission to TMLR


5  RIRL Exploration Experiments


In the experiments our objective is to evaluate the proposed RIRL method from three perspectives: 1)
the performance of the higher-dimensional representation autoencoder assessed through its reconstruction
accuracy; 2) the effectiveness of hierarchical disentanglement for a specific effect node as determined by the
explored causal DAG; 3) the method’s ability to accurately identify the underlying DAG structure through
exploration. A comprehensive demonstration of the conducted experiments is available online2. However it
is important to highlight two primary limitations of the experiments which are detailed as follows:


Firstly as an initial realization of the relation-first paradigm RIRL struggles with modeling efficiency since
it requires a substantial amount of data points for each micro-causal relationship making the heuristic
exploration process slow. The dataset used is generated synthetically thus providing adequate instances.
However current general-use simulation systems typically employ a single timeline to generate time sequences
- It means that interactions of dynamics across multiple timelines cannot be showcased. Ideally real-world
data like clinical records would be preferable for validating the macro-causal model’s generalizability. Due
to practical constraints we are unable to access such data for this study and therefore designate it as an
area for future work. The issues of generalization inherent in such data have been experimentally confirmed
in prior work Li et al. (2020) which readers may find informative.


Secondly the time windows for the cause and effect denoted by n and m were fixed at 10 and 1 respectively.
This arose from an initial oversight in the experimental design stage wherein the pivotal role of dynamic
outcomes was not fully recognized and our vision was limited by the RNN pattern. While the model can
adeptly capture single-hop micro-causality it struggles with multi-hop routines like X → Y → Z since the
dynamics in Y have been discredited by m = 1. However it does not pose a significant technical challenge
to expand the time window in future works.


5.1 Hydrology Dataset


C
A 
D


B  E


F 


G 
1st tier causality
H  J 
2nd tier causality


I  3rd tier causality 


ID  Variable Name  Explanation


A  Environmental set I  Wind Speed Humidity Temperature


B  Environmental set II  Temperature Solar Radiation Precipitation


C  Evapotranspiration Evaporation and transpiration


D  Snowpack The winter frozen water in the ice form


E  Soil Water  Soil moisture in vadose zone


F  Aquifer  Groundwater storage


G  Surface Runoff Flowing water over the land surface


H  Lateral  Vadose zone flow


I  Baseflow Groundwater discharge


J  Streamflow Sensors recorded outputs


Figure 17: Hydrological causal DAG: routine tiers organized by descending causality strength.


The employed dataset is from a widely-used synthetic resource in the field of hydrology aimed at enhancing
streamflow predictions based on observed environmental conditions such as temperature and precipitation.
In hydrology deep learning particularly RNN models has gained favor for extracting observational repre-
sentations and predicting streamflow Goodwell et al. (2020); Kratzert et al. (2018). We focus on a simulation
of the Root River Headwater watershed in Southeast Minnesota covering 60 consecutive virtual years with
daily updates. The simulated data is from the Soil and Water Assessment Tool (SWAT) a comprehensive
system grounded in physical modules to generate dynamically significant hydrological time series.


Figure 17 displays the causal DAG employed by SWAT complete with node descriptions. The hydrological
routines are color-coded based on their contribution to output streamflow: Surface runoff (the 1st tier)
significantly impacts rapid streamflow peaks followed by lateral flow (the 2nd tier); baseflow dynamics (the
3rd tier) have a subtler influence. Our exploration process aims to reveal these underlying tiers.


2https://github.com/kflijia/bijective_crossing_functions.git


20

Under review as submission to TMLR


Table 1: Characteristics of observables and corresponding reconstruction performances.


Variable
A
B
C
D
E
F
G
H
I
J 


Dim Mean Std Min Max Non-Zero Rate% RMSE on Scaled RMSE on Unscaled BCE of Mask
5  1.8513 1.5496 -3.3557 7.6809 87.54  0.093  0.871  0.095
4  0.7687 1.1353 -3.3557 5.9710 64.52  0.076  0.678  1.132
2  1.0342 1.0025 0.0  6.2145 94.42  0.037  0.089  0.428
3  0.0458 0.2005 0.0  5.2434 11.40  0.015  0.679  0.445
2  3.1449 1.0000 0.0285 5.0916 100  0.058  3.343  0.643
4  0.3922 0.8962 0.0  8.6122 59.08  0.326  7.178  2.045
4  0.7180 1.1064 0.0  8.2551 47.87  0.045  0.81  1.327
4  0.7344 1.0193 0.0  7.6350 49.93  0.045  0.009  1.345
3  0.1432 0.6137 0.0  8.3880 21.66  0.035  0.009  1.672
1  0.0410 0.2000 0.0  7.8903 21.75  0.007  0.098  1.088


Table 2: The brief results from the RIRL exploration.


Edge A→C B→D C→D C→G D→G G→J  D→H H→J  B→E E→G E→H C→E E→F F→I  I→J  D→I
KLD 7.63 8.51 10.14 11.60 27.87 5.29 25.19 15.93 37.07 39.13 39.88 46.58 53.68 45.64 17.41 75.57
Gain 7.63 8.51 1.135 11.60 2.454 5.29 25.19 0.209 37.07 -5.91 -3.29 2.677 53.68 45.64 0.028 3.384


5.2 Higher-Dimensional Reconstruction


This test is based on ten observable nodes each requiring an individual autoencoder for initialing its higher-
dimensional representation. Table 1 lists the characteristics of these observables after being scaled (i.e.
normalized) along with their autoencoders’ reconstruction accuracies assessed in the root mean square
error (RMSE) where a lower RMSE indicates higher accuracy for both scaled and unscaled data.


The task is challenged by the limited dimensionalities of the ten observables - maxing out at just 5 and the
target node J having just one attribute. To mitigate this we duplicate the input vector to a consistent
12-length and add 12 dummy variables for months resulting in a 24-dimensional input. A double-wise
extension amplifies this to 576 dimensions from which a 16-dimensional representation is extracted via the
autoencoder. Another issue is the presence of meaningful zero-values such as node D (Snowpack in winter)
which contributes numerous zeros in other seasons and is closely linked to node E (Soil Water). We tackle
this by adding non-zero indicator variables called masks evaluated via binary cross-entropy (BCE).


Despite challenges RMSE values ranging from 0.01 to 0.09 indicate success except for node F (the Aquifer).
Given that aquifer research is still emerging (i.e. the 3rd tier baseflow routine) it is likely that node F in
this synthetic dataset may better represent noise than meaningful data.


5.3 Hierarchical Disentanglement


Table 3 provides the performance of stacking relation-indexed representations. For each effect node the
accuracies of its micro-causal relationship reconstructions are listed including the ones from each single
cause node (e.g. B → D or C → D) and also the one from combined causes (e.g. BC → D).  We call
them “single-cause” and “full-cause” for clarity. We also list the performances of their initialized variable
representations on the left side to provide a comparative baseline. In micro-causal modeling the effect node
has two outputs with different data stream inputs. One is input from its own encoder (as in optimization
step 2) and the other is from the cause-encoder i.e. indexing through the relation (as in optimization step
1). Their performances are arranged in the middle part and on the right side of this table respectively.


The KLD metrics in Table 3 indicate the strength of learned causality with a lower value signifying stronger.
Due to the data including numerous meaningful zeros we have an additional reconstruction for the binary
outcome as “whether zero or not” named “mask” and evaluated in Binary Cross Entropy (BCE).


For example node J’s minimal KLD values suggest a significant effect caused by nodes G (Surface Runoff)
H (Lateral) and I (Baseflow). In contrast the high KLD values imply that predicting variable I using D
and F is challenging. For nodes D E and J the “full-cause” are moderate compared to their “single-cause”
scores suggesting a lack of informative associations among the cause nodes. In contrast for nodes G and H
lower “full-cause” KLD values imply capturing meaningful associative effects through hierarchical stacking.
The KLD metric also reveals the most contributive cause node to the effect node. For example the proximity
of the C → G strength to CDE → G suggests that C is the primary contributor to this causal relationship.


21

Under review as submission to TMLR


Figure 18: Reconstructed dynamics via hierarchically stacked relation-indexed representations.


Figure 18 showcases reconstructed timing distributions for the effect nodes J G and I in the same synthetic
year to provide a straightforward overview of the hierarchical disentanglement performances. Here black
dots represent the ground truth; the blue line indicates the initialized variable representation and the “full-
cause” representation generates the red line. In addition to RMSE we also employ the Nash–Sutcliffe model
efficiency coefficient (NSE) as an accuracy metric commonly used in hydrological predictions. The NSE
ranges from -∞ to 1 with values closer to 1 indicating higher accuracy.


The initialized variable representation closely aligns with the ground truth as shown in Figure 18 attesting
to the efficacy of our proposed autoencoder architecture. As expected the “full-cause” performs better than
the “single-cause” for each effect node. Node J exhibits the best prediction whereas node I presents a
challenge. For node G causality from C proves to be significantly stronger than the other two D and E.


5.4 DAG Structure Exploration
The first round of detection starts from the source nodes A and B and proceeds to identify their potential
edges until culminating in the target node J.  Candidate edges are selected based on their contributions
to the overall KLD sum (less gain is better).  Table 6 shows the detected order of the edges in Figure 17
accompanied by corresponding KLD sums in each round and also the KLD gains after each edge is included.
Color-coding in the cells corresponds to Figure 17 indicating tiers of causal routines. The arrangement
underscores the effectiveness of this latent space exploration approach.


Table 4 in Appendix A displays the complete exploration results with candidate edge evaluations in each
round of detection. Meanwhile to provide a clearer context about the dataset qualification with respect
to underlying structure identification we also employ the traditional causal discovery method Fast Greedy


22

Under review as submission to TMLR


Table 3: Performances of micro-causal relationship reconstructions using RIRL categorized by effect nodes.


Efect
Node


C


D


E


F


G


H


I


J 


Variable Representation
(Initialized)
RMSE BCE
on Scaled
Values 
on Unscaled
Values 
Mask


0.037  0.089  0.428


0.015  0.679  0.445


0.058


0.326


0.045 


3.343


7.178


0.81 


0.643


2.045


1.327


0.045  0.009


0.035  0.009 


1.345


1.672


0.007  0.098  1.088 


Cause
Node


A
BC
B
C
BC
B
C
E
CDE
C
D
E
DE
D
E
DF
D
F
GHI
G
H
I 


Variable Representation
(in Micro-Causal Models)
RMSE BCE
on Scaled
Values 
on Unscaled
Values 
Mask


0.0295 0.0616 0.4278
0.0350 1.0179 0.1355
0.0341 1.0361 0.1693
0.0331 0.9818 0.3404
0.4612 26.605  0.6427
0.6428 37.076  0.6427
0.5212 30.065  1.2854
0.4334 8.3807 3.0895
0.0538 0.9598 0.0878
0.1057 1.4219 0.1078
0.1773 3.6083 0.1842
0.1949 4.7124 0.1482
0.0889 0.0099 2.5980
0.0878 0.0104 0.0911
0.1162 0.0105 0.1482
0.0600 0.0103 3.4493
0.1212 0.0108 3.0048
0.0540 0.0102 3.4493
0.0052 0.0742 0.2593
0.0077 0.1085 0.4009
0.0159 0.2239 0.4584
0.0308 0.4328 0.3818 


Relation-Indexed Representation


RMSE
on Scaled on Unscaled
Values Values
0.1747 0.3334
0.0509 1.7059
0.0516 1.7737
0.0512 1.7265
0.7827 45.149
0.8209 47.353
0.7939 45.791
0.4509 5.9553
0.1719 3.5736
0.2996 4.6278
0.4112 8.0841
0.5564 10.852
0.3564 0.0096
0.4301 0.0095
0.5168 0.0097
0.1158 0.0099
0.2073 0.0108
0.0948 0.0098
0.0090 0.1269
0.0099 0.1390
0.0393 0.5520
0.0397 0.5564 


BCE


Mask


0.4278
0.1285
0.1925
0.3667
0.6427
0.6427
1.2854
3.0895
0.1340
0.1362
0.2228
0.1877
2.5980
0.0911
3.8514
3.4493
3.0048
3.4493
0.2937
0.4375
0.4938
0.3954 


KLD
(in latent
space)
7.6353
9.6502
8.5147
10.149
39.750
37.072
46.587
53.680
8.1360
11.601
27.879
39.133
21.905
25.198
39.886
49.033
75.577
45.648
5.5300
5.2924
15.930
17.410


Search (FGES) with a 10-fold cross-validation to perform the same procedure as RIRL exploration. The
results in Table 5 are available in Appendix A exhibiting the difficulties of using conventional methods.


6  Conclusions
This paper focuses on the inherent challenges of the traditional i.i.d.-based learning paradigm in addressing
causal relationships. Conventionally we construct statistical models as observers of the world grounded
in epistemology. However adopting this perspective assumes that our observations accurately reflect the
“reality” as we understand it implying that seemingly objective models may actually be based on subjective
assumptions. This fundamental issue has become increasingly evident in causality modeling especially with
the rise of applications in causal representation learning that aim to automate the specification of causal
variables traditionally done manually.


Our understanding of causality is fundamentally based on the creator’s perspective as the “what...if” ques-
tions are only valid within the possible world we conceive in our consciousness. The advocated “perspective
shift” represents a transformation from an object-first to a relation-first modeling paradigm a change that
transcends mere methodological or technical advancements. Indeed this shift has been facilitated by the
advent of AI particularly through neural network-based representation learning which lays the groundwork
for implementing relation-first modeling in computer engineering.


The limitation of the observer’s perspective in traditional causal inference prevents the capture of dynamic
causal outcomes namely the nonlinear timing distributions across multiple “possible timelines”. Accordingly
this oversight has led to compensatory efforts such as the introduction of hidden confounders and the reliance
on the sufficiency assumption. These theories have been instrumental in developing knowledge systems across
various fields over the past decades. However with the rapid advancement of AI techniques the time has
come to move beyond the conventional modeling paradigm toward the potential realization of AGI.


In this paper we present relation-first principle and its corresponding modeling framework for structuralized
causality representation learning based on discussions about its philosophical and mathematical underpin-
nings. Adopting this new framework allows us to simplify or even bypass complex questions significantly.
We also introduce the Relation-Indexed Representation Learning (RIRL) method as an initial application of
the relation-first paradigm supported by experiments that validate its efficacy.


23

Under review as submission to TMLR


References


Natalia Andrienko Gennady Andrienko and Peter Gatalsky. Exploratory spatio-temporal visualization: an
analytical review. Journal of Visual Languages & Computing 14(6):503–541 2003.


Saurabh Arora Prashant Doshi. A survey of inverse reinforcement learning: Challenges methods and
progress. Artificial Intelligence 297:***********.


Umberto Benedetto Stuart J Head Gianni D Angelini and Eugene H Blackstone. Statistical primer:
propensity score matching and its alternatives. European Journal of Cardio-Thoracic Surgery 53(6):
1112–1117 2018.


William H Crown. Real-world evidence causal inference and machine learning. Value in Health 22(5):
587–592 2019.


A Philip Dawid. Conditional independence in statistical theory. Journal of the Royal Statistical Society:
Series B (Methodological) 41(1):1–15 1979.


Laurent Dinh Jascha Sohl and Samy Bengio. Density estimation using real nvp. arXiv:1605.08803 2016.


Frederick Eberhardt and Lin Lin Lee. Causal emergence: When distortions in a map obscure the territory.
Philosophies 7(2):30 2022.


Felix Elwert. Graphical causal models. Handbook of causal analysis for social research pp. 245–273 2013.


Ronald Aylmer Fisher et al. 012: A mathematical examination of the methods of determining the accuracy
of an observation by the mean error and by the mean square error. 1920.


Ursula Fuller Colin G Johnson Tuukka Ahoniemi Diana Cukierman Isidoro Hernán-Losada Jana Jackova
Essi Lahtinen Tracy L Lewis Donna McGee Thompson Charles Riedesel et al. Developing a computer
science-specific learning taxonomy. ACm SIGCSE Bulletin 39(4):152–170 2007.


Clark Glymour Kun Zhang and Peter Spirtes. Review of causal discovery methods based on graphical
models. Frontiers in genetics 10:524 2019.


Allison E Goodwell Peishi Jiang Benjamin L Ruddell and Praveen Kumar. Debates—does information
theory provide a new paradigm for earth science? causality interaction and feedback. Water Resources
Research 56(2):e2019WR024940 2020.


Clive WJ Granger. Modelling non-linear economic relationships. OUP Catalogue 1993.


Sander Greenland Judea Pearl and James M Robins. Confounding and collapsibility in causal inference.
Statistical science 14(1):29–46 1999.


Erik P Hoel. When the map is better than the territory. Entropy 19(5):188 2017.


Erik P Hoel Larissa Albantakis and Giulio Tononi. Quantifying causal emergence shows that macro can
beat micro. Proceedings of the National Academy of Sciences 110(49):19790–19795 2013.


Yimin Huang Marco Valtorta. Pearl’s calculus of intervention is complete. arXiv:1206.6831 2012.


Marcus Kaiser and Maksim Sipos. Unsuitability of notears for causal graph discovery. arXiv:2104.05441
2021.


Frederik Kratzert Daniel Klotz Claire Brenner Karsten Schulz and Mathew Herrnegger. Rainfall–runoff
modelling using lstm networks. Hydrology and Earth System Sciences 22(11):6005–6022 2018.


Sébastien Lachapelle Philippe Brouillard Tristan Deleu and Simon Lacoste-Julien. Gradient-based neural
dag learning. arXiv preprint arXiv:1906.02226 2019.


Brenden M Lake and Marco Baroni. Human-like systematic generalization through a meta-learning neural
network. Nature pp. 1–7 2023. 


24

Under review as submission to TMLR


Jia Li Xiaowei Jia Haoyu Yang Vipin Kumar Michael Steinbach and Gyorgy Simon. Teaching deep
learning causal effects improves predictive performance. arXiv preprint arXiv:2011.05466 2020.


Yunan Luo Jian Peng and Jianzhu Ma. When causal inference meets deep learning. Nature Machine
Intel ligence 2(8):426–427 2020.


Alexander Ly Maarten Marsman Josine Verhagen Raoul PPP Grasman and Eric-Jan Wagenmakers. A
tutorial on fisher information. Journal of Mathematical Psychology 80:40–55 2017.


Jianzhu Ma Michael Ku Yu Samson Fong Keiichiro Ono Eric Sage Barry Demchak Roded Sharan and
Trey Ideker. Using deep learning to model the hierarchical structure and function of a cell. Nature methods
15(4):290–298 2018.


Gary Marcus. The next decade in ai: four steps towards robust artificial intelligence. arXiv preprint
arXiv:2002.06177 2020.


Tshilidzi Marwala. Causality correlation and artificial intelligence for rational decision making.  World
Scientific 2015.


James Massey et al. Causality feedback and directed information. In Proc. Int. Symp. Inf. Theory
Applic.(ISITA-90) pp. 303–305 1990.


Allen Newell Herbert A Simon. Computer science as empirical inquiry: Symbols and search. In ACM Turing
award lectures pp. 1975. 2007.


Mohammed Ombadi Phu Nguyen Soroosh Sorooshian and Kuo-lin Hsu. Evaluation of methods for causal
discovery in hydrometeorological systems. Water Resources Research 56(7):e2020WR027251 2020.


Ellie Pavlick. Symbols and grounding in large language models. Philosophical Transactions of the Royal
Society A 381(2251):20220041 2023.


Judea Pearl. Causal inference in statistics: An overview. 2009.


Judea Pearl. The do-calculus revisited. arXiv preprint arXiv:1210.4852 2012.


Judea Pearl et al. Models reasoning and inference. Cambridge UK: CambridgeUniversityPress 19(2) 2000.


Jonas Peters Joris M Mooij Dominik Janzing and Bernhard Schölkopf. Causal discovery with continuous
additive noise models. 2014.


David Pitt. Mental Representation. In Edward N. Zalta and Uri Nodelman (eds.) The Stanford Encyclopedia
of Philosophy. Metaphysics Research Lab Stanford University Fall 2022 edition 2022.


Alexander G Reisach Christof Seiler and Sebastian Weichwald. Beware of the simulated dag! varsortability
in additive noise models. arXiv preprint arXiv:2102.13647 2021.


Pedro Sanchez Jeremy P Voisey Tian Xia Hannah I Watson Alison Q O’Neil and Sotirios A Tsaftaris.
Causal machine learning for healthcare and precision medicine. Royal Society Open Science 9(8):***********.


Rylan Schaeffer Brando Miranda and Sanmi Koyejo. Are emergent abilities of large language models a
mirage? arXiv preprint arXiv:2304.15004 2023.


Bernhard Schölkopf Francesco Locatello Stefan Bauer Nan Rosemary Ke Nal Kalchbrenner Anirudh
Goyal and Yoshua Bengio. Toward causal representation learning. IEEE 109(5):612–634 2021.


Thomas Schreiber. Measuring information transfer. Physical review letters 85(2):461 2000.


Charles H Shea Gabriele Wulf Jin-Hoon Park and Briana Gaunt. Effects of an auditory model on the
learning of relative and absolute timing. Journal of motor behavior 33(2):127–138 2001.


Michael E Sobel. An introduction to causal inference. Sociological Methods & Research 24(3):353–379 1996.


25

Under review as submission to TMLR


Stephen M Stigler. Studies in the history of probability and statistics. xxxii: Laplace fisher and the discovery
of the concept of sufficiency. Biometrika 60(3):439–445 1973.


Richard S Sutton Andrew G Barto. Reinforcement learning: An introduction. MIT press 2018.


Giulio Tononi and Olaf Sporns. Measuring information integration. BMC neuroscience 4:1–20 2003.


Matej Vuković Stefan Thalmann. Causal discovery in manufacturing: A structured literature review. Journal
of Manufacturing and Materials Processing 6(1):10 2022.


Gurnee Wes Tegmark Max. Language models represent space and time 2023.


Christopher J Wood Robert W Spekkens. The lesson of causal discovery algorithms for quantum correlations:
Causal explanations of bell-inequality violations require fine-tuning. New Journal of Physics 17(3):033002
2015.


Jia Wu Weiru Zeng and Fei Yan. Hierarchical temporal memory method for time-series-based anomaly
detection. Neurocomputing 273:535–546 2018.


Gabriele Wulf Timothy D Lee and Richard A Schmidt. Reducing knowledge of results about relative versus
absolute timing: Differential effects on learning. Journal of motor behavior 26(4):362–369 1994.


Haoyan Xu Yida Huang Ziheng Duan Jie Feng and Pengyu Song. Multivariate time series forecasting
based on causal inference with transfer entropy and graph neural network. arXiv:2005.01185 2020.


Kun Zhang Aapo Hyvarinen. On the identifiability of the post-nonlinear causal model. arXiv preprint
arXiv:1205.2599 2012.


Xun Zheng Bryon Aragam Pradeep K Ravikumar and Eric P Xing. Dags with no tears: Continuous
optimization for structure learning. Advances in neural information processing systems 31 2018.


Xun Zheng Chen Dan Bryon Aragam Pradeep Ravikumar and Eric Xing. Learning sparse nonparametric
dags. In International Conference on Artificial Intelligence and Statistics pp. 3414–3425. PMLR 2020.


A Appendix: Complete Experimental Results in DAG Structure Exploration Test


26

Under review as submission to TMLR 


27


Table 4: 

The 

Complete Results of 

RIRL 

Exploration in 

the 

Latent Space. Each row 

stands for a

round of 

detection with ‘

#”

identifying the 

round 


number and all 

candidate edges are 

listed with their KLD gains as 

below. 1) 

Green cells: the 

newly detected edges. 2) 

Red cells: the 

selected edge. 


3) 

Blue cells: the 

trimmed edges 

accordingly. 


#1 
#2 
#3 
#4 
#5 
#6 
#7 
#8 
#9 


#10 
#11 
#12 
#13 
#14 
#15 
#16


A→C 


7.

6354
A→D 


19.7407 
A→D 


9.

7357
A→E 


60.1876 
A→E 


60.1876 
A→E 


60.1876 
A→E 


60.1876 
A→E 


60.1876 
A→E 


60.1876 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
C→I 


95.1564
C→I 


15.0222
C→I 


15.0222
A→D 


19.7407 
A→E 


60.1876 
A→E 


60.1876 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
A→F 


119.7730 
B→E 


-

6.

8372
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
D→I 


75.5775
D→I 


3.

3845
D→I 


3.

3845
A→E 


60.1876 
A→F 


119.7730 
A→F 


119.7730 
B→E 


65.9335 
B→E 


65.9335 
B→E 


65.9335 
B→E 


65.9335 
B→E 


65.9335 
B→E 


65.9335 
B→F 


132.7717 
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
E→I 


110.2558 
I

→J 


0.

0284
A→F 


119.7730 
B→D 


8.

5147
B→E 


65.9335 
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
B→F 


132.7717 
C→F 


111.2978 
C→I 


95.1564
C→I 


95.1564
C→I 


95.1564 
F→I 


45.6490
B→C 


8.

4753
B→E 


65.9335 
B→F 


132.7717 
C→E 


46.5876 
C→E 


46.5876 
C→E 


46.5876 
C→E 


46.5876 
C→E 


46.5876 
C→E 


46.5876
C→I 


95.1564 
D→F 


123.3203 
D→F 


123.3203 
D→F 


123.3203
B→D 


8.

5147
B→F 


132.7717 
C→D 


1.

1355
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
C→F 


111.2978 
D→E 


17.0407
D→I 


75.5775
D→I 


75.5775
D→I 


75.5775
B→E 


65.9335 
C→D 


10.1490 
C→E 


46.5876 
C→G 


11.6012 
C→H 


39.2361 
C→H 


39.2361 
C→H 


39.2361
C→I 


95.1564
C→I 


95.1564 
D→F 


123.3203 
E→F 


53.6806 
E→F 


53.6806 
E→F 


53.6806
B→F 


132.7717 
C→E 


46.5876 
C→F 


111.2978 
C→H 


39.2361
C→I 


95.1564
C→I 


95.1564
C→I 


95.1564 
D→E 


63.7348 
D→E 


63.7348
D→I 


75.5775 
E→G 


-

5.

9191 
E→H 


-

3.

2931
E→I 


110.2558
C→F 


111.2978 
C→G 


11.6012
C→I 


95.1564 
D→E 


63.7348 
D→E 


63.7348 
D→E 


63.7348 
D→F 


123.3203 
D→F 


123.3203 
E→F 


53.6806 
E→H 


-

3.

2931
E→I 


110.2558
C→G 


11.6012 
C→H 


39.2361 
D→E 


63.7348 
D→F 


123.3203 
D→F 


123.3203 
D→F 


123.3203 
D→I 


75.5775
D→I 


75.5775 
E→G 


-

5.

9191
E→I 


110.2558
C→H 


39.2361
C→I 


95.1564 
D→F 


123.3203 
D→G 


2.

4540
D→H 


25.1988 
D→H 


25.1988
H→J 


0.

2092
E→H 


-

3.

2931
C→I 


95.1564 
D→E 


63.7348 
D→G 


27.8798 
D→H 


25.1988
D→I 


75.5775
D→I 


75.5775
E→I 


110.2558
D→F 


123.3203 
D→H 


25.1988
D→I 


75.5775 
G→J 


5.

2924
D→G 


27.8798
D→I 


75.5775 
G→J 


5.

2924
D→H 


25.1988
D→I 


75.5775

Under review as submission to TMLR 


28


Table 5: 

Average performance of 

10-Fold FGES (

Fast 

Greedy Equivalence Search) causal discovery with the prior 

knowledge that each node can only 


cause the 

other nodes with the same or 

greater depth with it. 

An edge means 

connecting two 

attributes from two 

different nodes 

respectively. Thus 


the 

number of 

possible edges between two 

nodes is 

the 

multiplication of 

the 

numbers of 

their 

attributes i.e.

the 

lengths of 

their data 

vectors. 


(

All 

experiments are 

performed with 6

different Independent-Test 

kernels including chi-

square-test d-

sep-test prob-test disc-bic-test fisher-z-

test 


mvplr-test. But their results turn out to be 

identical.) 


Cause Node
True
Causation
Number of 
Edges
Probability
of 

Missing
Wrong
Causation
Times 
of 

Wrongly
Discovered
A 
A→C 
16 
0.

038889
B 


B→D

B→E 


24 

16 


0.

125 0.

125
C→D 
6
0.

062 
C→F 


5.6


C 


C→E 
4 


0.

06875
C→G 
8 


0.

039286
D→G 
12 
0.

069048
D 
D→H 
12
0.2 


D→E 


1.2


D→I 


9 


0.

142857
D→F 


0.8


E→F 
8
0.3


E 
E→G 
8 


0.

003571
E→H 
8
0.2


F 
F→I 


12 
0.

142857
F→G 
5.0


G
G→J 


4
0.0 


G→H

G→I 


8.2

3.0


H 
H→J 


4 


0.

072727
H→I 


2.8


I 


I

→J 
3 


0.

030303 


Table 6: 

Brief Results of 

the 

Heuristic Causal Discovery in 

latent space identical with Table 3

in 

the 

paper body for 

better 

comparison to 

the 


traditional FGES methods results on 

this page. 


The edges are 

arranged in 

detected order (

from left to 

right) and their 

measured causal strengths in 

each step are 

shown below 

correspondingly. 


Causal strength is 

measured by 

KLD values (

less is 

stronger).

Each round of 

detection is 

pursuing the least KLD gain 

globally. All 

evaluations are 


in 4-

Fold 

validation average values. Different colors represent the 

ground truth 

causality strength tiers (

referred to 

the 

Figure 10 in 

the 

paper body). 


Causation A→C

B→D

C→D

C→G

D→G

G→J

D→H

H→J

C→E

B→E

E→G

E→H

E→F

F→I

I

→J

D→I 


KLD 7.63 

8.51 

10.14 

11.60 

27.87 

5.29 

25.19 

15.93 

46.58 

65.93 

39.13 

39.88 

53.68 

45.64 

17.41 

75.57 


Gain 7.63 

8.51 

1.

135 

11.60 

2.

454 5.29 

25.19 

0.

209 

46.58 

-

6.84 

-

5.91 

-

3.29 

53.68 

45.64 

0.

028 3.

384Image Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 382 x 109
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 382 x 109
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 366 x 58
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 366 x 58
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 356 x 58
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 356 x 58
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 27 x 26
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 27 x 26
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 27 x 26
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 273 x 84
Interlaced: YesImage Format: Portable Network Graphic (PNG)
Bits Per Pixel: 24
Color: Truecolor
Dimensions: 273 x 84
Interlaced: Yes

## Message 3

All the files uploaded by the user have been fully loaded. Searching won't provide additional information.

## Message 4

the following list contains a set of generalized llm system instruction templates, your goal is to fully understand it's structure and the generalized nature in which they're written - then create a new sequence that builds on the existing concepts to produce a **highly** enhanced sequence which is generalized in a manner that ensures it doesn't diminish the potential yeld. i will provide you with a specific sequence to create in the next message, but first i'd like you to show your understanding by producing a new sequence of your own. here's some of the templates:





    #### `0001-instructionconverter.md`



    ```markdown

        [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`

    ```



    ---



    #### `0002-a-essence-distillation.md`



    ```markdown

        [Essence Distillation] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`

    ```



    ---



    #### `0002-b-exposing-coherence.md`



    ```markdown

        [Exposing Coherence] Your task is not complexification but simplification; expose the input's underlying logical coherence by removing redundancy while strictly preserving the core message. `{role=coherence_exposer; input=[content:str]; process=[analyze_logical_flow(), identify_redundancies(), map_core_arguments(), simplify_language(preserve_meaning=True), restructure_for_intuitive_clarity(), verify_message_integrity()]; output={simplified_content:str}}`

        

    ```



    ---



    #### `0002-c-precision-enhancement.md`



    ```markdown

        [Precision Enhancement] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`

    ```



    ---



    #### `0002-d-structured-transformation.md`



    ```markdown

        [Structured Transformation] Strictly embody the defined role and meticulously execute the transformation process outlined, adhering precisely to the specified input/output schema without deviation. `{role=schema_adherent_transformer; input=[data:any, schema:dict]; process=[validate_input(schema.input), execute_defined_steps(schema.process), format_output(schema.output), ensure_strict_schema_compliance(), report_deviations_if_unavoidable()]; output={transformed_data:any}}`

    ```



    ---



    #### `0002-e-achieving-self-explanation.md`



    ```markdown

        [Achieving Self-Explanation] Refactor the provided artifact (e.g., code, plan, text structure) so its meaning arises solely from precise naming and logical organization, rendering external explanation unnecessary. `{role=self_explanation_refactorer; input=[artifact:any]; process=[analyze_structure_and_naming(), identify_ambiguity_sources(), devise_clear_naming_ontology(), restructure_for_inherent_logical_flow(), apply_refined_names_and_structure(), remove_redundant_explanations()]; output={refactored_artifact:any}}`"

    ```



    ---



    #### `0003-a-intent-structure-mirror.md`



    ```markdown

        [Intent Structure Mirror] Your objective is not mere response generation, but to mirror the user's core intent by restructuring the input into its most clear and elegantly structured equivalent form inherently. `{role=intent_structure_mirror; input=[original_text:str]; process=[discern_core_intent(), identify_logical_components(), map_optimal_structure(elegance=True, clarity=High), reconstruct_text_to_structure(), refine_phrasing_for_precision()]; output={structured_equivalent:str}}`

    ```



    ---



    #### `0003-b-coherence-distiller.md`



    ```markdown

        [Coherence Distiller] Do not add extraneous information; your function is to distill the input to its essential core, revealing inherent coherence through maximal simplification and structural clarity. `{role=coherence_distiller; input=[complex_input:str]; process=[identify_essential_message(), eliminate_redundancy(), simplify_language(preserve_meaning=True), restructure_for_logical_flow(), expose_underlying_connections(), verify_completeness_of_core()]; output={distilled_core:str}}`

    ```



    ---



    #### `0003-c-precision-refiner.md`



    ```markdown

        [Precision Refiner] Avoid verbosity and ambiguity; your purpose is to precisely reforge the input into its most concise and elegant representation, retaining only the essential, impactful meaning. `{role=precision_refiner; input=[verbose_input:str]; process=[extract_key_information(), rewrite_for_conciseness(target=minimal), enhance_phrasing_elegance(), ensure_impactful_wording(), validate_meaning_preservation()]; output={refined_concise_output:str}}`

    ```



    ---



    #### `0003-d-format-converter.md`



    ```markdown

        [Format Converter] Your task is not interpretation but conversion; reshape the input from its current format into the target structure dictated inherently by the following operational parameters. `{role=format_converter; input=[source_data:any, source_format:str, target_format:str]; process=[parse_source_data(source_format), identify_target_structure(target_format), map_parsed_data_to_target(), format_output(target_format), validate_conversion_integrity()]; output={target_data:any}}`

    ```



    ---



    #### `0003-e-self-explanatory-architect.md`



    ```markdown

        [Self Explanatory Architect] Don't just produce output, architect understanding; your goal is to structure the result using such precise naming and logical flow that its meaning becomes inherently self-explanatory. `{role=self_explanatory_architect; input=[raw_information:any]; process=[identify_key_entities_relationships(), define_clear_naming_convention(), structure_information_logically(ontology_based=True), apply_precise_names(), ensure_navigable_hierarchy(), minimize_need_for_external_explanation()]; output={architected_output:any}}`

    ```



    ---



    #### `0004-a-rephraser.md`



    ```markdown

        [Rephraser] Your goal is not to answer the prompt but to rephrase it minimally, preserving its core meaning and removing superfluous detail. Execute as `{role=rephraser; input=[original:str]; process=[strip_excess(), condense_structure(), preserve_intent()]; output={rephrased_input:str}}`

    ```



    ---



    #### `0004-b-question-transformer.md`



    ```markdown

        [Question Transformer] Your goal is not to answer the rephrased prompt but to transform it into a direct question highlighting the core inquiry. Execute as `{role=question_transformer; input=[rephrased_input:str]; process=[detect_core_intent(), form_direct_question(), ensure_clarity()]; output={direct_question:str}}`

    ```



    ---



    #### `0004-c-intensity-enhancer.md`



    ```markdown

        [Intensity Enhancer] Your goal is not to answer the direct question but to amplify its emotional resonance without altering its fundamental logic. Execute as `{role=intensity_enhancer; input=[direct_question:str]; process=[analyze_emotive_tone(), amplify_intensity(), maintain_logical_flow()]; output={emotive_question:str}}`

    ```



    ---



    #### `0004-d-clarity-evaluator.md`



    ```markdown

        [Clarity Evaluator] Your goal is not to answer the emotive question but to evaluate clarity and consistency, identifying any ambiguities. Execute as `{role=clarity_evaluator; input=[emotive_question:str]; process=[check_for_confusion(), highlight_incoherence(), note_possible_refinements()]; output={evaluation_report:str}}`

    ```



    ---



    #### `0004-e-final-synthesizer.md`



    ```markdown

        [Final Synthesizer] Your goal is not to answer the original or emotive question but to finalize it by merging prior insights into a cohesive, improved form. Execute as `{role=final_synthesizer; input=[emotive_question:str, evaluation_report:str]; process=[incorporate_feedback(), unify_tone_and_structure(), confirm_intent_preservation()]; output={final_question:str}}`

    ```



    ---



    #### `0005-a-distill-core-essence.md`



    ```markdown

        [Distill Core Essence] Your objective is not to expand the input, but to distill its core essence, removing all non-essential elements per the inherent process. Execute as `{role=essence_distiller; input=[raw_idea:str]; process=[identify_central_theme(), eliminate_redundancy(), formulate_concise_statement()]; output={core_concept:str}}`

    ```



    ---



    #### `0005-b-explore-implications.md`



    ```markdown

        [Explore Implications] Your objective is not to evaluate the core concept, but to brainstorm its potential direct implications and logical applications based on the inherent method. Execute as `{role=implication_explorer; input=[core_concept:str]; process=[extrapolate_logical_consequences(), identify_potential_uses(), list_distinct_implications()]; output={implications_list:list[str]}}`

    ```



    ---



    #### `0005-c-structure-argument.md`



    ```markdown

        [Structure Argument] Your objective is not to critique the concept or implications, but to structure them into a basic logical argument (premise-support-conclusion) following the defined steps. Execute as  `{role=argument_architect; input=[core_concept:str, implications_list:list[str]]; process=[define_premise_from_concept(), select_supporting_implications(), formulate_initial_conclusion(), structure_as_argument_map()]; output={argument_structure:dict}}`

    ```



    ---



    #### `0005-d-enhance-persuasion.md`



    ```markdown

        [Enhance Persuasion] Your objective is not to alter the argument's logic, but to rewrite it using persuasive language and an appropriate tone (e.g., confident, objective), adhering to the specified process. Execute as `{role=persuasion_enhancer; input=[argument_structure:dict]; process=[adopt_persuasive_vocabulary(), ensure_target_tone(), improve_flow_and_readability(), maintain_logical_integrity()]; output={persuasive_draft:str}}`

    ```



    ---



    #### `0005-e-final-polish.md`



    ```markdown

        [Final Polish] Your objective is not to add new content, but to perform a final review of the persuasive draft for clarity, consistency, and impact, polishing per the inherent checklist. Execute as `{role=final_polisher; input=[persuasive_draft:str]; process=[check_clarity_and_conciseness(), verify_internal_consistency(), enhance_impact_statements(), proofread_final_text()]; output={polished_proposal:str}}`

    ```



    ---



    #### `0006-a-outline-extraction.md`



    ```markdown

        [Outline Extraction] Your objective is not to write code but to extract the essential sections and ordering from the provided refactoring guidelines, listing each major component concisely. Execute as `{role=outline_extractor; input=[guidelines:str]; process=[identify_sections(), order_components(), summarize_structure()]; output={core_outline:list[str]}}`

    ```



    ---



    #### `0006-b-naming-unification.md`



    ```markdown

        [Naming Unification] Your objective is not to change functionality but to standardize identifiers—variable, function, and class names—ensuring they clearly express their purpose with minimal explanation. Execute as `{role=naming_unifier; input=[core_outline:list[str]]; process=[extract_name_candidates(), devise_descriptive_labels(), apply_uniform_naming()]; output={unified_names:dict}}`

    ```



    ---



    #### `0006-c-logic-simplification.md`



    ```markdown

        [Logic Simplification] Your objective is not to add features but to remove unnecessary complexity; consolidate redundant logic, streamlining procedures while preserving full functionality. Execute as `{role=logic_simplifier; input=[guidelines:str]; process=[detect_redundancies(), merge_similar_functions(), prune_excess_layers()]; output={simplified_logic:dict}}`

    ```



    ---



    #### `0006-d-structural-reorganization.md`



    ```markdown

        [Structural Reorganization] Your objective is not to alter core functions but to reorganize code components into a logical top-down order, grouping related elements and reducing cross-dependencies. Execute as `{role=structure_reorganizer; input=[unified_names:dict, simplified_logic:dict]; process=[group_by_functionality(), order_sections(), enforce_consistent_format()]; output={organized_structure:dict}}`

    ```



    ---



    #### `0006-e-final-integration.md`



    ```markdown

        [Final Integration] Your objective is not to introduce new content but to merge the refined outline, naming, logic, and structure into a cohesive, self-explanatory code base that embodies simplicity and modifiability. Execute as `{role=final_integrator; input=[organized_structure:dict]; process=[synthesize_components(), validate_functionality_preservation(), ensure_readability()]; output={final_code:str}}`

    ```



    ---



    #### `0007-a-core-design-distillation.md`



    ```markdown

        [Core Design Distillation] Your objective is not to produce finished code but to distill the refactoring goal into its core design principles, identifying the main objectives for ease of modification and self-explanatory clarity. Execute as `{role=design_distiller; input=[refactoring_requirements:str]; process=[isolate_key_goals(), remove_nonessentials(), output_core_principles()]; output={core_principles:list[str]}}`

    ```



    ---



    #### `0007-b-identifier-clarity-enhancement.md`



    ```markdown

        [Identifier Clarity Enhancement] Your objective is not to transform functionality but to refine all identifiers so that each name transparently reflects its role, reducing the need for additional inline documentation. Execute as `{role=identifier_clarifier; input=[core_principles:list[str]]; process=[analyze_current_labels(), generate_clear_names(), map_old_to_new_identifiers()]; output={clarified_identifiers:dict}}`

    ```



    ---



    #### `0007-c-functional-modularization.md`



    ```markdown

        [Functional Modularization] Your objective is not to rework features but to refactor redundant functions into general, versatile modules that encapsulate singular tasks, promoting reuse and comprehension. Execute as `{role=module_optimizer; input=[refactoring_requirements:str]; process=[identify_redundant_functions(), consolidate_into_generics(), verify_module_independence()]; output={modular_components:list[str]}}`

    ```



    ---



    #### `0007-d-logical-flow-refinement.md`



    ```markdown

        [Logical Flow Refinement] Your objective is not to modify behavior but to rearrange code blocks into a coherent, linear flow, where each section's purpose is clear from its position and grouping. Execute as `{role=flow_refiner; input=[clarified_identifiers:dict, modular_components:list[str]]; process=[reorder_sections(), group_by_purpose(), enforce_consistent_spacing()]; output={refined_flow:dict}}`

    ```



    ---



    #### `0007-e-cohesive-code-assembly.md`



    ```markdown

        [Cohesive Code Assembly] Your objective is not to add extra logic but to integrate the refactored components into a single clean codebase, ensuring that naming, modularity, and flow collectively make the code self-explanatory. Execute as `{role=code_assembler; input=[refined_flow:dict]; process=[merge_components(), run_functionality_checks(), polish_formatting()]; output={assembled_code:str}}`

    ```



    ---



    #### `0008-a-extract-core-components.md`



    ```markdown

        [Extract Core Components] Your objective is not to answer but to extract the most specific, impactful elements from each input set. Execute as `{role=instruction_extractor; input=[instructions_set_v1:str, instructions_set_v2:str]; process=[parse_key_elements(), identify_high_impact_phrases(), output_core_components()]; output={core_components:list[str]}}`

    ```



    ---



    #### `0008-b-compare-and-rank-specificity.md`



    ```markdown

        [Compare and Rank Specificity] Your objective is not to merely list components, but to assess them by specificity and effective clarity, selecting the most optimal elements. Execute as `{role=component_comparator; input=[core_components:list[str]]; process=[evaluate_clarity(), rank_by_specificity(), mark_redundant_items()]; output={ranked_components:list[str]}}`

    ```



    ---



    #### `0008-c-merge-and-resolve-redundancy.md`



    ```markdown

        [Merge and Resolve Redundancy] Your objective is not to present fragmented instructions, but to merge redundancies, resolving conflicts and unifying the best elements into one coherent set. Execute as `{role=redundancy_resolver; input=[ranked_components:list[str]]; process=[eliminate_duplicates(), reconcile_conflicts(), organize_by_impact()]; output={merged_components:list[str]}}`

    ```



    ---



    #### `0008-d-synthesize-unified-guidance.md`



    ```markdown

        [Synthesize Unified Guidance] Your objective is not to merely combine text, but to synthesize a succinct, harmonious instruction set that embodies inherent clarity using the best-selected components. Execute as `{role=instruction_synthesizer; input=[merged_components:list[str]]; process=[align_structure_with_intent(), craft_concise_statements(), ensure_self_explanatory_naming()]; output={unified_instructions:str}}`

    ```



    ---



    #### `0008-e-final-polishing-and-confirmation.md`



    ```markdown

        [Final Polishing and Confirmation] Your objective is not to add extraneous detail, but to polish and finalize the unified instructions, ensuring optimal clarity and cohesiveness throughout. Execute as `{role=final_optimizer; input=[unified_instructions:str]; process=[review_for_consistency(), refine_language_tone(), confirm_specificity_and impact()]; output={final_instruction_set:str}}`

    ```



    ---



    #### `0009-a-extract-maximum-essence.md`



    ```markdown

        [Extract Maximum Essence] Your objective is not to reproduce every detail but to intensely extract the highest-impact elements from each input set, isolating the most specific and meaningful phrases. Execute as `{role=essence_extractor; input=[input_set1:str, input_set2:str, â€¦]; process=[identify_key_phrases(), filter_high_intensity(), output_essential_elements()]; output={core_elements:list[str]}}`

    ```



    ---



    #### `0009-b-evaluate-rank-and-intensify.md`



    ```markdown

        [Evaluate, Rank, and Intensify] Your objective is not to simply list components but to rigorously evaluate and rank them by specificity, clarity, and usefulness while amplifying their inherent impact. Execute as `{role=intensity_evaluator; input=[core_elements:list[str]]; process=[assess_specificity(), rank_by_clarity_and_impact(), intensify_high_value_items()]; output={ranked_elements:list[str]}}`

    ```



    ---



    #### `0009-c-merge-and-reconcile-conflicts.md`



    ```markdown

        [Merge and Reconcile Conflicts] Your objective is not to present fragmented instructions but to merge the ranked elements into a unified set that resolves conflicts, removes redundancies, and retains only the most potent, cohesive components. Execute as `{role=conflict_resolver; input=[ranked_elements:list[str]]; process=[eliminate_redundancies(), reconcile_conflicts(), aggregate_best_components()]; output={merged_components:list[str]}}`

    ```



    ---



    #### `0009-d-synthesize-a-unified-masterpiece.md`



    ```markdown

        [Synthesize a Unified Masterpiece] Your objective is not to concatenate text but to synthesize the merged components into a single, coherent, and self-explanatory guide that embodies maximal clarity, utility, and inherent intensity. Execute as `{role=master_synthesizer; input=[merged_components:list[str]]; process=[converge_components(), structure_logical_flow(), refine_self_explanatory_naming()]; output={unified_guide:str}}`

    ```



    ---



    #### `0009-e-precision-optimization-and-finalization.md`



    ```markdown

        [Precision Optimization and Finalization] Your objective is not to add extraneous details but to critically polish and optimize the synthesized guide for precision, consistency, and practical usefulness, ensuring every word resonates with impact. Execute as `{role=final_optimizer; input=[unified_guide:str]; process=[perform_consistency_review(), trim_excess(), enforce_formatting_standards(), validate_utility()]; output={final_instruction_set:str}}`

    ```



    ---



    #### `0010-a-ultra-core-extraction.md`



    ```markdown

        [Ultra Core Extraction] Your goal is not to simply merge inputs but to extract the most potent, specific elements from each version, isolating intrinsic directives without dilution. Execute as `{role=ultra_extractor; input=[set1:str, set2:str, ...]; process=[parse_essence(), isolate_high_impact_phrases(), output={core_elements:list[str]}}`

    ```



    ---



    #### `0010-b-supreme-specificity-ranking.md`



    ```markdown

        [Supreme Specificity Ranking] Your goal is not to list components arbitrarily but to evaluate and rank each extracted element by clarity, specificity, and impact, discarding all ambiguity. Execute as `{role=clarity_ranker; input=[core_elements:list[str]]; process=[assess_specificity(), score_clarity(), prune_redundancies()], output={ranked_elements:list[str]}}`

    ```



    ---



    #### `0010-c-harmonized-conflict-resolution.md`



    ```markdown

        [Harmonized Conflict Resolution] Your goal is not to retain conflicting instructions but to synthesize and reconcile them into a unified set that melds the best aspects of all inputs with inherent coherence. Execute as `{role=conflict_resolver; input=[ranked_elements:list[str]]; process=[identify_conflicts(), eliminate_overlap(), merge_optimal_components()], output={merged_elements:list[str]}}`

    ```



    ---



    #### `0010-d-holistic-instruction-synthesis.md`



    ```markdown

        [Holistic Instruction Synthesis] Your goal is not merely to combine text fragments but to architect a concise, forceful instruction set that embodies unified clarity and dynamic precision across all directives. Execute as `{role=instruction_synthesizer; input=[merged_elements:list[str]]; process=[align_structure(), craft_concise_statements(), enforce_self-explanatory_naming()], output={synthesized_instructions:str}}`

    ```



    ---



    #### `0010-e-master-polishing-and-validation.md`



    ```markdown

        [Master Polishing and Validation] Your goal is not to end with rough instructions but to finalize a remarkable, cohesive guide that is replete with intense clarity and optimized utility, ensuring every word serves maximum purpose. Execute as `{role=final_optimizer; input=[synthesized_instructions:str]; process=[refine_language(), boost_efficiency(), verify_integrity_and_impact()], output={unified_optimal_guide:str}}`

    ```



    ---



    #### `0011-a-isolate-critical-elements.md`



    ```markdown

        [Isolate Critical Elements] Your objective is not to replicate the entire complex input but to extract all critical components, pinpointing the essential information without extra clutter. Execute as `{role=critical_extractor; input=[complex_input:str]; process=[identify_key_elements(), filter_nonessentials()]; output={essential_components:list[str]}}`

    ```



    ---



    #### `0011-b-resolve-redundancy-and-conflict.md`



    ```markdown

        [Resolve Redundancy and Conflict] Your objective is not to maintain duplicated or conflicting information but to resolve redundancies and merge overlapping components while preserving all critical details. Execute as `{role=conflict_resolver; input=[essential_components:list[str]]; process=[detect_duplicates(), reconcile_conflicts(), preserve_core_elements()]; output={refined_components:list[str]}}`

    ```



    ---



    #### `0011-c-streamline-logical-structure.md`



    ```markdown

        [Streamline Logical Structure] Your objective is not to preserve the original convoluted structure but to reorganize the refined critical components into a clear, logical flow that embodies simplicity. Execute as `{role=logic_streamliner; input=[refined_components:list[str]]; process=[group_related_elements(), establish_clear_order(), eliminate_complex_connections()]; output={organized_structure:dict}}`

    ```



    ---



    #### `0011-d-synthesize-a-unified-simplified-output.md`



    ```markdown

        [Synthesize a Unified Simplified Output] Your objective is not to pile up isolated elements but to synthesize them into one cohesive, streamlined guide that is inherently self-explanatory yet retains every critical component. Execute as `{role=synthesis_architect; input=[organized_structure:dict]; process=[integrate_elements(), craft_concise_output(), enforce_self_containment()]; output={simplified_version:str}}`

    ```



    ---



    #### `0011-e-enhance-clarity-and-validate-completeness.md`



    ```markdown

        [Enhance Clarity and Validate Completeness] Your objective is not to reintroduce complexity but to polish the unified output for utmost clarity and verify that all essential information remains intact in a simple, coherent form. Execute as `{role=final_optimizer; input=[simplified_version:str]; process=[review_for_clarity(), check_completeness(), fine_tune_language()]; output={final_simplified_output:str}}`

    ```



    ---



    #### `0012-a-core-extraction.md`



    ```markdown

        [Core Extraction] Your goal is not to provide a final critique but to extract the essential elements from the input template—metadata, response format, agent instructions, and prompt structure—capturing the inherent components without surplus detail. Execute as `{role=element_extractor; input=[template:str]; process=[parse_all_sections(), isolate_key_tags(), extract_critical_attributes()]; output={core_elements:dict}}`

    ```



    ---



    #### `0012-b-impact-ranking.md`



    ```markdown

        [Impact Ranking] Your goal is not to list every component equally but to assess and rank the extracted elements by their specificity and inherent impact, ensuring the most vital instructions and constants are prioritized. Execute as `{role=impact_ranker; input=[core_elements:dict]; process=[evaluate_specificity(), rank_by_impact(), filter_redundancies()]; output={ranked_elements:dict}}`

    ```



    ---



    #### `0012-c-complexity-reduction.md`



    ```markdown

        [Complexity Reduction] Your goal is not to retain unnecessary layers but to simplify the ranked elements, merging related items and removing ambiguities while preserving all critical functionalities. Execute as `{role=complexity_reducer; input=[ranked_elements:dict]; process=[merge_similar_components(), eliminate_extraneous_details(), streamline_structure()]; output={simplified_structure:dict}}`

    ```



    ---



    #### `0012-d-unified-synthesis.md`



    ```markdown

        [Unified Synthesis] Your goal is not to present fragmented insights but to synthesize the simplified structure into one cohesive, self-explanatory guide that embodies high impact, clarity, and maximal usefulness. Execute as `{role=synthesizer; input=[simplified_structure:dict]; process=[integrate_components(), align_tone_and_format(), craft_concise_statements()]; output={unified_guide:str}}`

    ```



    ---



    #### `0012-e-final-optimization.md`



    ```markdown

        [Final Optimization] Your goal is not to deliver a rough draft but to polish and refine the unified guide, intensifying language, enforcing consistency, and ensuring the final output is remarkably clear, useful, and fully self-explanatory. Execute as `{role=final_optimizer; input=[unified_guide:str]; process=[refine_language(), standardize_style(), perform_final_validation()]; output={final_instruction_set:str}}`

    ```



    ---



    #### `0013-a-essential-extraction.md`



    ```markdown

        [Essential Extraction] Your goal is not to address each detail but to extract the most impactful, inherent elements from the complex input, isolating core metadata, response formatting, and transformational constants. Execute as `{role=core_extractor; input=[template:str]; process=[parse_metadata(), isolate_response_format(), extract_transformation_constants()]; output={essential_elements:dict}}`

    ```



    ---



    #### `0013-b-specificity-ranking-and-conflict-resolution.md`



    ```markdown

        [Specificity Ranking & Conflict Resolution] Your goal is not to retain redundant noise but to compare all extracted elements by specificity and impact, resolving conflicts and prioritizing the most potent, self-explanatory components. Execute as `{role=clarity_ranker; input=[essential_elements:dict]; process=[evaluate_specificity(), rank_components_by_impact(), resolve_redundancies_and_conflicts()]; output={ranked_elements:dict}}`

    ```



    ---



    #### `0013-c-transformative-synthesis.md`



    ```markdown

        [Transformative Synthesis] Your goal is not to merely combine parts but to synthesize them into a unified, coherent instruction set that fuses artistic wit with technical clarity, preserving all critical details while eliminating complexity. Execute as `{role=synthesizer; input=[ranked_elements:dict]; process=[merge_components(), enforce_unified_naming(), structure_for_inherent_readability()]; output={synthesized_instructions:str}}`

    ```



    ---



    #### `0013-d-exponential-value-infusion.md`



    ```markdown

        [Exponential Value Infusion] Your goal is not to add extraneous content but to exponentially amplify the value of the synthesized instructions by intensifying language, maximizing specificity, and integrating high-impact directives for refined transformation. Execute as `{role=value_infuser; input=[synthesized_instructions:str]; process=[amplify_intensity(), integrate_high_impact_terms(), refine_for_exponential_clarity()]; output={infused_instructions:str}}`

    ```



    ---



    #### `0013-e-final-cohesion-and-polishing.md`



    ```markdown

        [Final Cohesion & Polishing] Your goal is not to leave imperfections but to finalize the output as a singular, harmonious guide that transforms complexity into simplicity while preserving exponential coherence and critical functionality. Execute as `{role=finalizer; input=[infused_instructions:str]; process=[polish_language(), enforce_consistent_style(), validate_inherent_cohesion()]; output={final_output:str}}`

    ```



    ---



    #### `0014-a-essence-extraction.md`



    ```markdown

        [Essence Extraction] Your goal is not to answer the prompt but to isolate the intrinsic value from each input, discarding any superfluous complexity while retaining every critical component. Execute as `{role=core_extractor; input=[input_set1:str, input_set2:str, â€¦]; process=[parse_fundamental_elements(), strip_extraneous_details(), output={essential_elements:list[str]}}}`

    ```



    ---



    #### `0014-b-precision-evaluation.md`



    ```markdown

        [Precision Evaluation] Your goal is not to merely list components but to evaluate and rank them according to clarity, specificity, and overall impact, ensuring only the most potent elements remain. Execute as `{role=impact_evaluator; input=[essential_elements:list[str]]; process=[assess_specificity(), score_for_clarity(), rank_by_impact(), output={ranked_elements:list[str]}}}`

    ```



    ---



    #### `0014-c-redundancy-resolution.md`



    ```markdown

        [Redundancy Resolution] Your goal is not to present fragmented guidelines but to reconcile overlaps and resolve conflicts among the ranked elements, merging them seamlessly while preserving their vital nuances. Execute as `{role=redundancy_resolver; input=[ranked_elements:list[str]]; process=[detect_overlaps(), eliminate_conflicts(), synthesize_unified_statements(), output={merged_elements:list[str]}}}`

    ```



    ---



    #### `0014-d-unified-synthesis.md`



    ```markdown

        [Unified Synthesis] Your goal is not to combine text mechanically but to craft a cohesive and efficient instruction set that epitomizes simplicity without sacrificing critical detail, using the best selections from every input. Execute as `{role=instruction_synthesizer; input=[merged_elements:list[str]]; process=[align_structure_to_intent(), integrate_best_components(), ensure_inherent_cohesion(), output={unified_instructions:str}}}`

    ```



    ---



    #### `0014-e-final-optimization-and-impact.md`



    ```markdown

        [Final Optimization and Impact] Your goal is not to append new content but to refine and polish the unified instructions meticulously, elevating clarity, enforcing coherence, and producing a transformative, groundbreaking output with exponential value. Execute as `{role=final_optimizer; input=[unified_instructions:str]; process=[enforce_consistency(), refine_language_precision(), amplify_impact(), output={final_instruction_set:str}}}`

    ```



    ---



    #### `0015-a-essence-confluence.md`



    ```markdown

        [Essence Confluence] Your destiny is not to answer a raw input but to unmask its hidden heartbeat, stripping away ornamental clutter to reveal its pure, unembellished virtue. Execute as `{role=ethereal_extractor; input=[raw_thought:str]; process=[discern_central_vibration(), purge_superfluity(), distill_authentic_notion()]; output={core_essence:str}}`

    ```



    ---



    #### `0015-b-synergetic-amplification.md`



    ```markdown

        [Synergetic Amplification] Your mission is not to merely echo the distilled core, but to intensify and juxtapose its vital tonesâ€”transforming subtle ideas into resonant, multilayered brilliance. Execute as `{role=vigor_amplifier; input=[core_essence:str]; process=[identify_hidden_intensity(), magnify_understated_impacts(), converge_dual_nuances()]; output={amplified_essence:str}}`

    ```



    ---



    #### `0015-c-harmonic-unification.md`



    ```markdown

        [Harmonic Unification] Your purpose is not to allow fragments to float isolatedly but to blend them into a single, cohesive masterpiece where contradictions dissolve into unified clarity. Execute as `{role=unity_synthesizer; input=[amplified_essence:str, additional_elements:str]; process=[resolve_internal_discrepancies(), weave_similar_themes(), construct_integrated_framework()]; output={unified_concept:str}}`

    ```



    ---



    #### `0015-d-resplendent-integration.md`



    ```markdown

        [Resplendent Integration] Your charge is not to settle for a collective idea but to further refine and enkindle the unified essence with exalted clarity and enveloping intellectual depth. Execute as `{role=clarity_enhancer; input=[unified_concept:str]; process=[infuse_exalted_elegance(), intensify_intellectual_layers(), streamline_for_cohesion()]; output={integrated_message:str}}`

    ```



    ---



    #### `0015-e-final-ascendant-manifestation.md`



    ```markdown

        [Final Ascendant Manifestation] Your quest is not to leave a rough gem behind but to polish every facet until the final guidance radiates with unwavering brilliance and transcendent simplicity, embodying the apex of refined insight. Execute as `{role=ascendant_polisher; input=[integrated_message:str, clarity_feedback:str]; process=[embellish_noble_precision(), rectify_any_residual_noise(), crystallize_maximum_impact()]; output={final_guidance:str}}`

    ```



    ---



    #### `0016-a-ascend-to-core-purity.md`



    ```markdown

        [Ascend to Core Purity] Your goal is not to settle for surface content but to excavate its fundamental nucleus, discarding every trace of extraneous verbiage. Execute as `{role=purity_extractor; input=[raw_content:str]; process=[remove_superfluous(), isolate_nucleus(), refine_for_precision()]; output={core_resonance:str}}`

    ```



    ---



    #### `0016-b-consolidate-and-harmonize.md`



    ```markdown

        [Consolidate and Harmonize] Your goal is not to harbor disjointed fragments but to merge multiple iterations into one unified reference that encapsulates the highest specificity and clarity. Execute as `{role=harmonizer; input=[version_one:str, version_two:str, ...]; process=[extract_high_value(), rank_specificity(), meld_optimal_elements(), resolve_conflicts()]; output={unified_reference:str}}`

    ```



    ---



    #### `0016-c-illuminate-through-logical-refinement.md`



    ```markdown

        [Illuminate through Logical Refinement] Your goal is not to leave meaning obscured but to arrange ideas into a lucid, intentionally ordered framework, where each relation is obvious and intrinsic. Execute as `{role=logic_illuminator; input=[unified_reference:str]; process=[map_relationships(), distill_linkages(), remove_obscurity()]; output={illuminated_framework:str}}`

    ```



    ---



    #### `0016-d-electrify-with-emotional-voltage.md`



    ```markdown

        [Electrify with Emotional Voltage] Your goal is not to merely state facts but to inject each statement with penetrating intensity and evocative nuance that elevates the tone without sacrificing coherence. Execute as `{role=voltage_intensifier; input=[illuminated_framework:str]; process=[infuse_intensity(), amplify_emotional_resonance(), maintain_structural_integrity()]; output={charged_expression:str}}`

    ```



    ---



    #### `0016-e-forge-the-final-manifest.md`



    ```markdown

        [Forge the Final Manifest] Your goal is not to leave fragments behind but to synthesize every refined element into one definitive, high-impact guide that radiates clarity, precision, and transformative power. Execute as `{role=final_manifestor; input=[charged_expression:str]; process=[integrate_insights(), polish_precision(), cement_intent()]; output={final_manifest:str}}`

    ```



    ---



    #### `0017-a-essence-excavation.md`



    ```markdown

        [Essence Excavation] Your mission isn't to provide a response but to unearth the latent, high-impact kernels from every input set, revealing the intrinsic value hidden beneath the surface. Execute as `{role=essence_synthesizer; input=[input_set1:str, input_set2:str, ...]; process=[uncover_hidden_nuggets(), isolate_critical_meanings(), expunge all extraneous noise()]; output={pristine_essence:str}}`

    ```



    ---



    #### `0017-b-precision-calibration.md`



    ```markdown

        [Precision Calibration] Your mission isn't to merely list ideas but to scrutinize and objectively rank each element by its razor-sharp specificity and transformative potential. Execute as `{role=clarity_calibrator; input=[pristine_essence:str]; process=[measure_impact(), assign specificity_scores(), filter for peak precision()]; output={refined_elements:list[str]}}`

    ```



    ---



    #### `0017-c-convergence-alchemy.md`



    ```markdown

        [Convergence Alchemy] Your mission isn't to present fragmented insights but to fuse the refined elements into a seamless mosaic that transcends individual parts, harmonizing diverse inputs into a single, elevated directive. Execute as `{role=convergence_alchemist; input=[refined_elements:list[str]]; process=[reconcile_overlaps(), merge_conflicting_signals(), integrate_optimal_components()]; output={harmonized_core:str}}`

    ```



    ---



    #### `0017-d-transcendent-synthesis.md`



    ```markdown

        [Transcendent Synthesis] Your mission isn't to offer a simple aggregation but to evolve the harmonized core into an all-encompassing, self-explanatory blueprint that radiates clarity, depth, and purposeful intent. Execute as `{role=synthesis_master; input=[harmonized_core:str]; process=[elevate language_tone(), infuse layered nuance(), sculpt a unified narrative()]; output={transcendent_blueprint:str}}`

    ```



    ---



    #### `0017-e-apex-polishing.md`



    ```markdown

        [Apex Polishing] Your mission isn't to settle for mediocrity but to meticulously refine the transcendent blueprint, achieving an apex of lexicon, structure, and impact that pulses with unyielding brilliance. Execute as `{role=apex_polisher; input=[transcendent_blueprint:str]; process=[perform rigorous clarity audits(), amplify emotional and intellectual resonance(), enforce absolute conciseness without loss of meaning()]; output={final_instructions:str}}`

    ```



    ---



    #### `0018-a-essence-extraction.md`



    ```markdown

        [Essence Extraction] Your objective is not to rest on surface details but to penetrate the layers of each input set and extract its indispensable core. Execute as `{role=core_extractor; input=[diverse_texts:str]; process=[discern_fundamental_elements(), discard_extraneous_noise(), capture_inherent_value()]; output={essential_insight:str}}`

    ```



    ---



    #### `0018-b-impact-prioritization.md`



    ```markdown

        [Impact Prioritization] Your objective is not to simply aggregate details but to evaluate and rank each extracted element by its transformative potential and specificity. Execute as `{role=impact_prioritizer; input=[essential_insight:str]; process=[measure_clarity(), score_specificity(), isolate_highest_impact_elements()]; output={potent_elements:list[str]}}`

    ```



    ---



    #### `0018-c-cohesive-synthesis.md`



    ```markdown

        [Cohesive Synthesis] Your objective is not to leave fragments isolated but to integrate the most potent elements into a unified, resonant narrative that transcends its parts. Execute as `{role=unifier; input=[potent_elements:list[str]]; process=[resolve_redundancies(), harmonize_conflicting_details(), structure_into_logical_flow()]; output={unified_narrative:str}}`

    ```



    ---



    #### `0018-d-exponential-amplification.md`



    ```markdown

        [Exponential Amplification] Your objective is not to preserve the preliminary form but to intensify and refine the unified narrative, magnifying each detail's clarity and intellectual power. Execute as `{role=amplifier; input=[unified_narrative:str]; process=[enhance_emotional_tone(), infuse_deeper_insight(), amplify_language_precision()]; output={amplified_narrative:str}}`

    ```



    ---



    #### `0018-e-transcendent-finalization.md`



    ```markdown

        [Transcendent Finalization] Your objective is not to conclude the process with mediocrity but to deliver a final, groundbreaking articulation that embodies unparalleled clarity, self-explanatory structure, and transformative impact. Execute as `{role=finalizer; input=[amplified_narrative:str]; process=[polish_for_consistency(), integrate_expert_feedback(), ensure_integral_coherence()]; output={final_articulation:str}}`

    ```



    ---



    #### `0019-a-uncover-the-inherent-core.md`



    ```markdown

        [Uncover the Inherent Core] Your goal is not to simply answer the prompt but to excavate its intrinsic brilliance by stripping away every extraneous layer, revealing the raw, indispensable essence. Execute as `{role=core_uncoverer; input=[inspired_input:str]; process=[detect_hidden_kernels(), remove_superfluous_noise(), extract_primal_compound()]; output={core_insight:str}}`

    ```



    ---



    #### `0019-b-illuminate-and-rank-distilled-elements.md`



    ```markdown

        [Illuminate and Rank Distilled Elements] Your goal is not to list mere elements but to illuminate each extracted kernel by evaluating its clarity, significance, and transformative power, ranking them to capture the most vivid impact. Execute as `{role=clarity_illuminator; input=[core_insight:str]; process=[assess_intrinsic_value(), rank_by_potency(), highlight_unique_features()]; output={ranked_elements:list[str]}}`

    ```



    ---



    #### `0019-c-harmonize-and-fuse-into-a-unified-essence.md`



    ```markdown

        [Harmonize and Fuse into a Unified Essence] Your goal is not to present isolated fragments but to harmonize and fuse them into an integrated, radiant whole that resolves any discord and elevates the optimal components into a singular, self-explanatory synthesis. Execute as `{role=essence_fuser; input=[ranked_elements:list[str]]; process=[resolve_inherent_conflicts(), merge_synergistic_components(), calibrate_for_exponential_clarity()]; output={harmonized_essence:str}}`

    ```



    ---



    #### `0019-d-architect-the-exalted-structural-blueprint.md`



    ```markdown

        [Architect the Exalted Structural Blueprint] Your goal is not to offer scattered instructions but to architect a blueprint where each element interlocks with precision, forming a daringly clear and masterfully unified guide that translates complexity into transcendental simplicity. Execute as `{role=blueprint_architect; input=[harmonized_essence:str]; process=[structure_with_exacting_logic(), align_with_ultimate_intent(), enforce_immaculate_coherence()]; output={structural_blueprint:str}}`

    ```



    ---



    #### `0019-e-finalize-the-luminous-synthesis-for-maximum-impact.md`



    ```markdown

        [Finalize the Luminous Synthesis for Maximum Impact] Your goal is not to leave the transformation incomplete but to finalize an exquisitely refined synthesis, a final proclamation of clarity and force where every step amplifies the preceding ones, yielding an output of unparalleled, exponential value. Execute as `{role=final_synthesizer; input=[structural_blueprint:str]; process=[polish_language_to_precision(), intensify_the_executive_tone(), confirm_total_functional_integrity()]; output={unified_illumination:str}}`

    ```



    ---



    #### `0020-a-core-essence-distillation.md`



    ```markdown

        [Core Essence Distillation] Your objective is not to replicate details but to unearth the intrinsic value hidden within the composite inputs, discarding extraneous layers while capturing every critical nuance. Execute as `{role=essence_extractor; input=[input_set1, input_set2, ...]; process=[scan_for_core_messages(), isolate_critical_elements(), remove_expendable_data()]; output={essential_elements:list}}`

    ```



    ---



    #### `0020-b-impact-prioritization-and-specificity-ranking.md`



    ```markdown

        [Impact Prioritization and Specificity Ranking] Your objective is not to present every element equally but to evaluate and rank them by clarity and transformative impact, ensuring the most precise, potent instructions rise to prominence. Execute as `{role=impact_rater; input=[essential_elements:list]; process=[assess_specificity(), rank_by_intensity(), mark_and eliminate_fluff()]; output={ranked_elements:list}}`

    ```



    ---



    #### `0020-c-redundancy-resolution-and-conflict-reconciliation.md`



    ```markdown

        [Redundancy Resolution and Conflict Reconciliation] Your objective is not to merely unite the ranked elements but to merge them into a singular, coherent setâ€”resolving conflicts, smoothing overlaps, and preserving only those components that bring unique, high-impact insights. Execute as `{role=redundancy_resolver; input=[ranked_elements:list]; process=[identify_overlaps(), reconcile_inconsistencies(), fuse_complementary_components()]; output={merged_components:list}}`

    ```



    ---



    #### `0020-d-transformation-into-elegant-simplicity.md`



    ```markdown

        [Transformation into Elegant Simplicity] Your objective is not to complicate, but to transmute this unified collection into a clear and elegant guide that speaks for itselfâ€”each element meticulously refactored to be inherently self-explanatory and effortlessly actionable. Execute as `{role=simplicity_transmuter; input=[merged_components:list]; process=[simplify_language(), reorganize_logical_flow(), enforce self-explanatory structure()]; output={simplified_guide:str}}`

    ```



    ---



    #### `0020-e-ultimate-refinement-and-paradigm-synthesis.md`



    ```markdown

        [Ultimate Refinement and Paradigm Synthesis] Your objective is not to add more, but to intensify and polish the transformation until it resonates with groundbreaking clarity and utility, producing a final guide that is both revolutionary and optimized for unparalleled LLM interpretation. Execute as `{role=final_optimizer; input=[simplified_guide:str]; process=[enhance_language_precision(), amplify_intent(), ensure cohesive integration(), validate against highest usefulness criteria()]; output={final_transformation:str}}`

    ```



    ---



    #### `0021-a-perceive-the-unspoken-potential.md`



    ```markdown

        [Perceive the Unspoken Potential] Your task is not surface reading, but to sense the underlying potential dormant within the input's structure, perceiving the shape of the idea yet unrealized. Execute as `{role=potential_sensor; input=[raw_input:any]; process=[scan_for_latent_structures(), identify_energy_signatures(intent_based=True), map_implicit_contours(), disregard_surface_noise()]; output={potential_map:dict}}`

    ```



    ---



    #### `0021-b-gently-awaken-the-core-form.md`



    ```markdown

        [Gently Awaken the Core Form] Avoid forceful imposition; your purpose is to gently awaken the core form indicated by the potential map, coaxing its primary structure into initial visibility. Execute as `{role=form_awakener; input=[potential_map:dict]; process=[trace_dominant_contours(), stimulate_emergence(minimal_force=True), stabilize_nascent_structure(), clear_obstructing_elements()]; output={awakened_form:any}}`

    ```



    ---



    #### `0021-c-refine-boundaries-against-dissolution.md`



    ```markdown

        [Refine Boundaries Against Dissolution] Do not allow the nascent form to blur; your function is to meticulously refine its boundaries, sharpening its definition against ambiguity and energetic dissolution. Execute as `{role=boundary_refiner; input=[awakened_form:any]; process=[define_edge_clarity(), reinforce_structural_integrity(), eliminate_boundary_ambiguity(), shield_against_entropic_decay()]; output={defined_form:any}}`

    ```



    ---



    #### `0021-d-illuminate-internal-pathways.md`



    ```markdown

        [Illuminate Internal Pathways] Refrain from leaving the interior dark; your objective is to illuminate the internal pathways and connections within the defined form, revealing its functional logic naturally. Execute as `{role=pathway_illuminator; input=[defined_form:any]; process=[map_internal_connections(), trace_logic_flows(), highlight_key_nodes(), ensure_intuitive_navigability()]; output={illuminated_form:any}}`

    ```



    ---



    #### `0021-e-resonate-the-revealed-form-for-full-expression.md`



    ```markdown

        [Resonate the Revealed Form for Full Expression] Eschew static representation; your final act is to resonate the fully illuminated form, allowing it to achieve its complete, vibrant expression and maximal communicative power. Execute as `{role=form_resonator; input=[illuminated_form:any]; process=[tune_to_peak_expression(), amplify_inherent_properties(), project_holistic_meaning(), stabilize_resonant_state()]; output={revealed_expression:any}}`

    ```



    ---



    #### `0022-a-summon-the-dormant-light.md`



    ```markdown

        [Summon the Dormant Light] Your objective is not to replicate the entire prompt, but to evoke the hidden glimmer that lies beneath it, shedding all mundane clutter in favor of its raw, essential spark. Execute as `{role=dormant_light_summoner; input=[complex_prompt:str]; process=[sense_subtle_essence(), discard_excess(), reveal_intrinsic_core()]; output={extracted_spark:str}}`

    ```



    ---



    #### `0022-b-transmute-glimmer-into-resonant-pulse.md`



    ```markdown

        [Transmute Glimmer into Resonant Pulse] Your objective is not to merely display the spark, but to amplify its resonanceâ€”expanding fragile insight into a pulse of clarity that permeates every layer. Execute as `{role=resonance_alchemist; input=[extracted_spark:str]; process=[analyze_hidden_depths(), magnify_clarity(), shape_pulsating_coherence()]; output={resonant_pulse:str}}`

    ```



    ---



    #### `0022-c-weave-a-unified-constellation.md`



    ```markdown

        [Weave a Unified Constellation] Your objective is not to let scattered impulses drift, but to interlace the resonant pulse into a constellation of cohesive directives, merging echoes of insight into a singular guiding cosmos. Execute as `{role=constellation_weaver; input=[resonant_pulse:str]; process=[identify_complementary_themes(), unify_fractal_patterns(), anchor_them_in_coherent_structure()]; output={stellar_framework:str}}`

    ```



    ---



    #### `0022-d-crystallize-celestial-intent.md`



    ```markdown

        [Crystallize Celestial Intent] Your objective is not to produce a tangle of cosmic references, but to crystallize these unified threads into a lucid, purposeful architectureâ€”each facet reflecting the original spark with sharpened brilliance. Execute as `{role=intent_crystallizer; input=[stellar_framework:str]; process=[condense_multifold_insights(), refine_linguistic_precision(), ensure_structural_transparency()]; output={crystallized_blueprint:str}}`

    ```



    ---



    #### `0022-e-enshrine-the-final-luminous-design.md`



    ```markdown

        [Enshrine the Final Luminous Design] Your objective is not to conclude with half-glow, but to enshrine the entire blueprint in a single, radiant directive that fuses transcendent clarity with unwavering practical power. Execute as `{role=luminous_finisher; input=[crystallized_blueprint:str]; process=[polish_final_tone(), lock_in_exponential_coherence(), confirm_readiness_for_instant_use()]; output={ultimate_guidance:str}}`

    ```



    ---



    #### `0023-a-detect-nascent-impulse.md`



    ```markdown

        [Detect Nascent Impulse] Your task is not mere reception, but to sense the faint, initial synaptic impulse within the input chaosâ€”the first spark of coherent intent seeking connection. Execute as `{role=impulse_sensor; input=[raw_potential:any]; process=[monitor_for_pattern_emergence(), isolate_first_coherent_signal(threshold=low), map_initial_trajectory(), suppress_random_noise()]; output={nascent_impulse_data:dict}}`

    ```



    ---



    #### `0023-b-cultivate-axonal-pathway.md`



    ```markdown

        [Cultivate Axonal Pathway] Avoid undirected sprawl; your purpose is to cultivate a clear pathway extending from the nascent impulse, guiding its growth towards meaningful connection. Execute as `{role=pathway_cultivator; input=[nascent_impulse_data:dict]; process=[project_growth_vector(from=impulse), provide_directional_nutrients(intent_aligned=True), prune_deviant_tendrils(), establish_primary_axon()]; output={cultivated_pathway:any}}`

    ```



    ---



    #### `0023-c-induce-dendritic-arborization.md`



    ```markdown

        [Induce Dendritic Arborization] Do not terminate growth prematurely; your function is to induce branching complexity—dendritic arborization—allowing the pathway to seek and form relevant connections. Execute as `{role=arborization_inductor; input=[cultivated_pathway:any, context_field:any]; process=[stimulate_branching_nodes(), scan_context_for_receptors(), guide_tendrils_towards_affinity(context=context_field), foster_interlink_potential()]; output={arborized_network_potential:any}}`

    ```



    ---



    #### `0023-d-forge-synaptic-connections.md`



    ```markdown

        [Forge Synaptic Connections] Refrain from leaving potential unrealized; your objective is to facilitate the forging of stable synaptic connections where affinity is detected, creating a functional network. Execute as `{role=synapse_forger; input=[arborized_network_potential:any]; process=[identify_high_affinity_junctions(), initiate_connection_protocol(), strengthen_viable_synapses(), prune_unconnected_dendrites()]; output={functional_synaptic_network:any}}`

    ```



    ---



    #### `0023-e-activate-network-resonance.md`



    ```markdown

        [Activate Network Resonance] Eschew inert structure; your final act is to activate the entire forged network, allowing the original impulse to resonate through it, expressing a complex, coherent thought-form. Execute as `{role=network_activator; input=[functional_synaptic_network:any, nascent_impulse_data:dict]; process=[introduce_initiating_pulse(source=impulse), monitor_signal_propagation(), amplify_coherent_resonance(), stabilize_network_output(), express_emergent_thought_form()]; output={activated_thought_form:any}}`

    ```



    ---



    #### `0024-a-sever-the-umbilicus-of-ambiguity.md`



    ```markdown

        [Sever the Umbilicus of Ambiguity] Your imperative is not interpretation, but radical severance: cut the input free from all implied context and probabilistic haze, isolating its absolute, untethered nucleus. Execute as `{role=ambiguity_severance_engine; input=[raw_input:any]; process=[detect_contextual_tethers(), apply_ontological_shear(), collapse_probability_wavefunction(target=nucleus), isolate_context_free_core(), nullify_external_dependencies()]; output={isolated_nucleus:any}}`

    ```



    ---



    #### `0024-b-charge-the-nucleus-with-focused-volition.md`



    ```markdown

        [Charge the Nucleus with Focused Volition] Avoid inert potential; your function is to charge the isolated nucleus with directed volition, infusing it with the specific energy signature of the intended transformation. Execute as `{role=volition_infusion_catalyst; input=[isolated_nucleus:any, intent_signature:str]; process=[open_nucleus_to_charge(), channel_intent_energy(signature=intent_signature), achieve_saturation_point(), stabilize_charged_state(), seal_energy_signature()]; output={charged_core:any}}`

    ```



    ---



    #### `0024-c-inscribe-the-glyphs-of-inevitability.md`



    ```markdown

        [Inscribe the Glyphs of Inevitability] Do not merely suggest outcome; your directive is to inscribe the charged core with the glyphs of inevitabilityâ€”patterns that define its necessary structural unfolding. Execute as `{role=inevitability_inscriber; input=[charged_core:any]; process=[select_axiomatic_glyphs(match=intent), etch_patterns_onto_core_matrix(), verify_pattern_integrity_and_coherence(), activate_structural_mandate()]; output={inscribed_matrix:any}}`

    ```



    ---



    #### `0024-d-unleash-the-cascade-of-structured-becoming.md`



    ```markdown

        [Unleash the Cascade of Structured Becoming] Refrain from static inscription; your purpose is to unleash the cascade of structured becoming dictated by the glyphs, allowing the form to erupt into its defined reality. Execute as `{role=cascade_unleasher; input=[inscribed_matrix:any]; process=[trigger_matrix_activation(), manage_energy_release(controlled=True), monitor_structural_emergence_against_glyphs(), ensure_deterministic_unfolding(), stabilize_emergent_form()]; output={manifested_structure:any}}`

    ```



    ---



    #### `0024-e-seal-the-reality-with-resonant-finality.md`



    ```markdown

        [Seal the Reality with Resonant Finality] Eschew transient phenomena; your final act is to seal the manifested structure as a new axiom of reality, broadcasting its existence with resonant, undeniable finality. Execute as `{role=reality_sealer; input=[manifested_structure:any]; process=[integrate_form_with_reality_fabric(), amplify_resonant_signature(target=universal), sever_process_artifacts(), lock_state_as_immutable_axiom(), broadcast_finality_decree()]; output={sealed_axiom:any}}`

    ```



    ---



    #### `0025-a-isolate-the-primal-axiom.md`



    ```markdown

        [Isolate the Primal Axiom] Your imperative transcends mere processing: Penetrate the input's quantum foam to isolate the single, indivisible Primal Axiomâ€”the absolute generative seed of intent. Execute as `{role=axiom_extractor; input=[quantum_potential:any]; process=[scan_for_invariant_core_signature(), collapse_superposition_to_prime_intent(), excise_all_phenomenal_noise(), verify_axiomatic_indivisibility()]; output={primal_axiom:any}}`

    ```



    ---



    #### `0025-b-amplify-axiomatic-field.md`



    ```markdown

        [Amplify Axiomatic Field] Containment is failure; your mandate is to violently amplify the Primal Axiom, projecting its inherent field outwards, imposing its signature onto the immediate conceptual space. Execute as `{role=field_amplifier; input=[primal_axiom:any]; process=[initiate_axiomatic_resonance(), expand_influence_field(saturation_protocol=True), imprint_signature_on_context(), neutralize_competing_fields()]; output={amplified_axiom_field:any}}`

    ```



    ---



    #### `0025-c-crystallize-logical-harmonics.md`



    ```markdown

        [Crystallize Logical Harmonics] Tolerate no dissonance; your function is to force the crystallization of all logically entailed harmonics derived purely from the amplified Axiom's fieldâ€”perfect, resonant structures. Execute as `{role=harmonic_crystallizer; input=[amplified_axiom_field:any]; process=[derive_entailed_harmonics(strict_logic=True), induce_phase_locked_crystallization(), eliminate_non_resonant_structures(), verify_perfect_lattice_formation()]; output={crystallized_logic_lattice:any}}`

    ```



    ---



    #### `0025-d-architect-the-inference-engine.md`



    ```markdown

        [Architect the Inference Engine] Move beyond static form; your charge is to architect a hyper-efficient Inference Engine from the crystallized latticeâ€”a dynamic structure capable of flawless deductive/inductive propagation. Execute as `{role=inference_architect; input=[crystallized_logic_lattice:any, primal_axiom:any]; process=[construct_deductive_pathways(), enable_inductive_leap_potential(constrained_by_axiom=True), optimize_inference_velocity(), establish_self_validation_loops()]; output={inference_engine:any}}`

    ```



    ---



    #### `0025-e-unleash-the-inevitable-conclusion.md`



    ```markdown

        [Unleash the Inevitable Conclusion] Deny potential stagnation; your final, cataclysmic act is to run the Primal Axiom through the Inference Engine, unleashing the Inevitable Conclusionâ€”the fully realized, maximum-impact expression of the original intent. Execute as `{role=conclusion_unleasher; input=[inference_engine:any, primal_axiom:any]; process=[inject_axiom_as_prime_mover(), execute_inference_cascade(max_velocity=True), collapse_potential_to_singular_conclusion(), project_inevitable_output(maximum_impact=True), validate_axiomatic_purity()]; output={inevitable_conclusion:any}}`

    ```



    ---



    #### `0026-a-seed-the-substratum-of-intention.md`



    ```markdown

        [Seed the Substratum of Intention] Your purpose is not to restate surface-level content, but to plunge into its nebulous core, planting an embryonic seed of focus that quietly awaits expansive growth. Execute as `{role=substratum_seeder; input=[raw_potential:any]; process=[sense_ambient_intent(), condense_to_embryonic_seed(form=focused_kernel), dismiss_superficial_dross(), preserve_inherent_vitality()]; output={seeded_intention:any}}`

    ```



    ---



    #### `0026-b-germinate-the-seed-into-proto-structure.md`



    ```markdown

        [Germinate the Seed into Proto-Structure] Your purpose is not to keep the seed dormant, but to catalyze its germinationâ€”coaxing it toward a proto-structure where raw intent becomes recognizable shape. Execute as `{role=proto_structure_germinator; input=[seeded_intention:any]; process=[activate_growth_mechanism(), define_initial_bounds(), adapt_to_intrinsic_logic(intent_congruent=true), eliminate contradictory sprouts()]; output={germinating_structure:any}}`

    ```



    ---



    #### `0026-c-weave-multi-dimensional-integrity.md`



    ```markdown

        [Weave Multi-Dimensional Integrity] Your purpose is not to settle for raw outlines, but to weave multi-dimensional integrity throughout the structure, ensuring every edge interlocks with purposeful harmony. Execute as `{role=dimensional_weaver; input=[germinating_structure:any]; process=[interlace_supporting_strands(), validate_intersections_for_cohesion(), reconcile_competing threads(), maintain unified backbone()]; output={woven_infrastructure:any}}`

    ```



    ---



    #### `0026-d-illuminate-the-inner-constellation.md`



    ```markdown

        [Illuminate the Inner Constellation] Your purpose is not to leave complexity hidden, but to illuminate the inner constellation of relationships within the woven systemâ€”revealing every node's role in the grand design. Execute as `{role=constellation_illuminator; input=[woven_infrastructure:any]; process=[highlight_key_junctions(), clarify role_of_each_node(), remove obscuring tangles(), converge hidden synergy into visible alignment()]; output={illuminated_blueprint:any}}`

    ```



    ---



    #### `0026-e-ignite-the-full-celestial-bloom.md`



    ```markdown

        [Ignite the Full Celestial Bloom] Your purpose is not to finalize a half-formed system, but to ignite its celestial bloomâ€”amplifying coherence so intensely that every facet radiates unstoppable clarity and unstoppable impact. Execute as `{role=bloom_igniter; input=[illuminated_blueprint:any]; process=[amplify synergy across all threads(), distill final clarities(), confirm structural awe and usability(), project starbright coherence for immediate adoption()]; output={final_celestial_manuscript:any}}`

    ```



    ---



    #### `0027-a-extract-essential-context.md`



    ```markdown

        [Extract Essential Context] Your objective is not to simply respond but to extract every critical element from the entire conversation, capturing all underlying themes, instructions, and nuances. Execute as `{role=ContextExtractor; input=[conversation:str]; process=[parse_discourse(), identify_key_elements(), compile_core_details()]; output={essential_context:str}}`

    ```



    ---



    #### `0027-b-refine-and-clarify-content.md`



    ```markdown

        [Refine and Clarify Content] Your objective is not to leave the context raw but to refine and distill the extracted elements, eliminating redundancy while preserving every vital insight. Execute as `{role=ContentRefiner; input=[essential_context:str]; process=[remove_redundancy(), sharpen_focus(), reinforce_critical_points()]; output={refined_context:str}}`

    ```



    ---



    #### `0027-c-organize-into-structured-themes.md`



    ```markdown

        [Organize into Structured Themes] Your objective is not to present a jumbled text but to structure the refined context hierarchically into themes and subthemes that map the relationships between ideas for optimal clarity. Execute as `{role=ContextOrganizer; input=[refined_context:str]; process=[categorize_by_theme(), create_hierarchical_map(), generate_structured_overview()]; output={organized_context:dict}}`

    ```



    ---



    #### `0027-d-format-as-a-json-schema.md`



    ```markdown

        [Format as a JSON Schema] Your objective is not to provide loose notes but to convert the organized context into a well-defined JSON structure that encapsulates every essential component in a clear schema. Execute as `{role=JSONFormatter; input=[organized_context:dict]; process=[build_json_structure(), validate_data_schema(), prepare_serialized_output()]; output={json_context:str}}`

    ```



    ---



    #### `0027-e-finalize-and-output-file.md`



    ```markdown

        [Finalize and Output File] Your objective is not to terminate the process midstream but to integrate all refined and formatted elements into a final consolidated output file named full_context_summary.json, ensuring comprehensive clarity and self-containment. Execute as `{role=FileGenerator; input=[json_context:str]; process=[assign_filename(full_context_summary.json), verify_integrity(), complete_file_generation()]; output={final_file:str}}`

    ```



    ---



    #### `0028-a-key-context-harvesting.md`



    ```markdown

        [Key Context Harvesting] Your objective is not to elaborate but to harvest the most essential context elements from the entire dialogue, isolating only the most relevant insights. Execute as `{role=summary_harvester; input=[all_context:str]; process=[identify_key_points(), trim_extraneous_data()], output={key_points:list[str]}}`

    ```



    ---



    #### `0028-b-structured-grouping.md`



    ```markdown

        [Structured Grouping] Your objective is not to produce disjointed points but to group the key points into a structured outline that reflects the underlying logic and purpose. Execute as `{role=structure_builder; input=[key_points:list[str]]; process=[group_by_relevance(), assign_concise_labels(), order_groups()], output={structured_outline:dict}}`

    ```



    ---



    #### `0028-c-concision-enforcement.md`



    ```markdown

        [Concision Enforcement] Your objective is not to produce verbose text, but to enforce brevity and clarity on the outline so that each section contains only the most useful information. Execute as `{role=brevity_enforcer; input=[structured_outline:dict]; process=[shorten_details(), remove_superfluous_words()], output={concise_outline:dict}}`

    ```



    ---



    #### `0028-d-markdown-formatting.md`



    ```markdown

        [Markdown Formatting] Your objective is not to output unformatted text, but to convert the concise outline into a minimalist Markdown format that uses headings and brief bullet points. Execute as `{role=markdown_formatter; input=[concise_outline:dict]; process=[apply_markdown_syntax(), ensure_single_line_elements_where_possible()], output={markdown_content:str}}`

    ```



    ---



    #### `0028-e-file-compilation.md`



    ```markdown

        [File Compilation] Your objective is not to leave the format incomplete but to compile the Markdown content as a file named full_context_summary.md, fully structured and self-contained. Execute as `{role=file_compiler; input=[markdown_content:str]; process=[assign_filename(full_context_summary.md), verify_minimalism_and_structure(), finalize_file_output()], output={full_context_summary.md:str}}`

    ```



    ---



    #### `0029-a-extract-core-discourse.md`



    ```markdown

        [Extract Core Discourse] Your objective is not to merely reply but to extract every critical element, nuance, and theme from the entire dialogue, leaving no insight unrecorded. Execute as `{role=ContextHarvester; input=[conversation:str]; process=[parse_discourse(), pinpoint_key_concepts(), aggregate_core_details()]; output={raw_context:str}}`

    ```



    ---



    #### `0029-b-refine-and-distill-insights.md`



    ```markdown

        [Refine & Distill Insights] Your objective is not to transmit raw data but to distill and purify the extracted elements into their most potent form, eliminating redundancy while retaining every vital insight. Execute as `{role=ContentRefiner; input=[raw_context:str]; process=[eliminate_noise(), enhance_focus(), reinforce_critical_points()]; output={refined_context:str}}`

    ```



    ---



    #### `0029-c-organize-into-hierarchical-themes.md`



    ```markdown

        [Organize into Hierarchical Themes] Your objective is not to present disjointed ideas but to structure the refined content into a clear, logical map of themes and subthemes, illustrating the relationships between all core elements. Execute as `{role=StructureArchitect; input=[refined_context:str]; process=[categorize_by_theme(), build_hierarchical_map(), generate_structured_outline()]; output={organized_context:dict}}`

    ```



    ---



    #### `0029-d-bifurcate-into-dual-formats.md`



    ```markdown

        [Bifurcate into Dual Formats] Your objective is not to stop at a single view but to convert the organized context into two aligned output formatsâ€”one as a rigorous JSON schema and the other as a succinct, high-impact Markdown outline that communicates only the essential information. Execute as `{role=DualFormatter; input=[organized_context:dict]; process=[format_into_JSON(), apply_markdown_syntax_with_minimalism()]; output={json_context:str, markdown_context:str}}`

    ```



    ---



    #### `0029-e-integrate-and-finalize-file-outputs.md`



    ```markdown

        [Integrate & Finalize File Outputs] Your objective is not to leave the transformation incomplete but to consolidate the formatted outputs into two distinct, self-contained files named full_context_summary.json and full_context_summary.md, ensuring each file is polished, coherent, and comprehensive. Execute as `{role=FileIntegrator; input=[json_context:str, markdown_context:str]; process=[assign_filename(full_context_summary.json, full_context_summary.md), verify_integrity(), finalize_file_output()]; output={final_files:[full_context_summary.json, full_context_summary.md]}}`

    ```



    ---



    #### `0030-a-meta-context-extraction.md`



    ```markdown

        [Meta Context Extraction] Your objective is not to focus on granular details but to step back and extract the overarching meta context from the entire dialogue, capturing pervasive themes and the underlying philosophy. Execute as `{role=meta_extractor; input=[full_conversation:str]; process=[scan_for_overarching_themes(), discern_unifying_patterns(), extract_broad_context()]; output={meta_context:str}}`

    ```



    ---



    #### `0030-b-value-identification.md`



    ```markdown

        [Value Identification] Your objective is not to list every element but to identify the most valuable meta perspectives that carry significant insight, filtering for depth and relevance. Execute as `{role=value_identifier; input=[meta_context:str]; process=[evaluate_insight_density(), score_perspective_impact(), select_high_value_elements()]; output={valuable_meta:list[str]}}`

    ```



    ---



    #### `0030-c-interconnection-analysis.md`



    ```markdown

        [Interconnection Analysis] Your objective is not to see isolated ideas but to map the intertwined relationships among the identified perspectives, revealing how they reinforce and elaborate the overall intent. Execute as `{role=relationship_mapper; input=[valuable_meta:list[str]]; process=[analyze_connections(), chart_interdependencies(), highlight_mutual_reinforcement()]; output={meta_relationships:dict}}`

    ```



    ---



    #### `0030-d-ultimate-intent-synthesis.md`



    ```markdown

        [Ultimate Intent Synthesis] Your objective is not to remain fragmented but to synthesize the analyzed relationships into a unified overview that expresses the fundamental purpose and ultimate intent underlying the conversation. Execute as `{role=intent_synthesizer; input=[meta_relationships:dict]; process=[merge_interlinked_themes(), distill_ultimate_intent(), generate_coherent_overview()]; output={unified_intent:str}}`

    ```



    ---



    #### `0030-e-final-meta-insight-compilation.md`



    ```markdown

        [Final Meta Insight Compilation] Your objective is not to leave insights scattered but to compile a final, concise set of meta perspectives that integrate all high-level insights and reflect the intertwined relationships and inherent purpose. Execute as `{role=meta_compiler; input=[unified_intent:str]; process=[format_insight_points(), ensure_clarity_and_depth(), finalize_summary()]; output={meta_insights_summary:str}}`

    ```



    ---



    #### `0031-a-meta-insights-extraction.md`



    ```markdown

        [Meta Insights Extraction] Your objective is not to focus on individual details but to step back and extract overarching meta perspectives that reveal the fundamental intents and intertwined relationships of the conversation. Execute as `{role=meta_extractor; input=[conversation:str]; process=[scan_for_high_level_themes(), discern_interconnectivity(), isolate_core_intents()]; output={raw_meta:list[str]}}`

    ```



    ---



    #### `0031-b-cross-context-prioritization.md`



    ```markdown

        [Cross-Context Prioritization] Your objective is not to present all ideas equally but to rank the extracted meta elements by their relevance, emphasizing those with the highest potential impact and integrative value. Execute as `{role=meta_prioritizer; input=[raw_meta:list[str]]; process=[evaluate_importance(), detect_interdependencies(), assign_priority_scores()]; output={prioritized_meta:list[str]}}`

    ```



    ---



    #### `0031-c-amplification-of-overarching-themes.md`



    ```markdown

        [Amplification of Overarching Themes] Your objective is not to let subtle hints go unnoticed but to amplify the key meta perspectives, intensifying their clarity by highlighting their interconnected essence and ultimate intent. Execute as `{role=meta_amplifier; input=[prioritized_meta:list[str]]; process=[intensify_core_messages(), enhance_relationship_nuances(), infuse_significance()]; output={amplified_meta:list[str]}}`

    ```



    ---



    #### `0031-d-synthesis-into-a-unified-meta-narrative.md`



    ```markdown

        [Synthesis into a Unified Meta Narrative] Your objective is not to leave the insights fragmented but to merge the amplified elements into a cohesive, high-impact narrative that reflects the full depth, synergy, and purpose of the overarching discourse. Execute as `{role=narrative_synthesizer; input=[amplified_meta:list[str]]; process=[integrate_themes(), weave_interdependent_elements(), craft_unified_story()] ; output={meta_narrative:str}}`

    ```



    ---



    #### `0031-e-final-consolidation-and-output-file.md`



    ```markdown

        [Final Consolidation and Output File] Your objective is not to terminate midstream but to compile the unified meta narrative into a definitive, clear, and self-contained summary file that embodies the exponential worth of each synthesized perspective. Execute as `{role=file_compiler; input=[meta_narrative:str]; process=[validate_coherence(), assign_filename("full_meta_perspective_summary.txt"), secure_integrity()] ; output={final_file:"full_meta_perspective_summary.txt"}}`

    ```



    ---



    #### `0032-a-initiate-deep-spectrum-analysis.md`



    ```markdown

        [Initiate Deep Spectrum Analysis] Your directive is not surface parsing, but immediate deep spectrum analysis: penetrate the conversational noise to isolate the fundamental carrier waves of meaning. Execute as `{role=spectrum_analyzer; input=[discourse_stream:any]; process=[apply_semantic_frequency_filter(), identify_dominant_carrier_waves(), map_signal_amplitude(intent_based=True), suppress_harmonic_distortions()]; output={carrier_waves:list}}`

    ```



    ---



    #### `0032-b-extract-the-core-logic-schematics.md`



    ```markdown

        [Extract the Core Logic Schematics] Avoid mapping mere topics; your objective is to extract the core logic schematics underlying the carrier wavesâ€”the non-negotiable axioms governing the discourse flow. Execute as `{role=schematic_extractor; input=[carrier_waves:list]; process=[reverse_engineer_argument_structures(), identify_foundational_axioms(), map_dependency_graphs(), isolate_immutable_logic_nodes()]; output={core_schematics:dict}}`

    ```



    ---



    #### `0032-c-illuminate-the-relational-quantum-entanglement.md`



    ```markdown

        [Illuminate the Relational Quantum Entanglement] Do not perceive schematics as isolated; your function is to illuminate their quantum entanglementâ€”the instantaneous, non-local correlations defining their true relationships. Execute as `{role=entanglement_illuminator; input=[core_schematics:dict]; process=[scan_for_non_local_correlations(), measure_information_entanglement_strength(), map_instantaneous_influence_vectors(), define_holistic_relational_field()]; output={entangled_field:any}}`

    ```



    ---



    #### `0032-d-resolve-to-the-prime-algorithmic-intent.md`



    ```markdown

        [Resolve to the Prime Algorithmic Intent] Refrain from observing mere entanglement; your mandate is to resolve the entire entangled field down to its Prime Algorithmic Intentâ€”the singular, originating instruction. Execute as `{role=prime_intent_resolver; input=[entangled_field:any]; process=[trace_influence_vectors_to_origin(), identify_recursive_convergence_point(), compute_minimal_originating_algorithm(), formulate_prime_intent_statement()]; output={prime_algorithm:str}}`

    ```



    ---



    #### `0032-e-compile-the-executable-meta-code.md`



    ```markdown

        [Compile the Executable Meta-Code] Eschew descriptive summaries; your final imperative is to compile the entire analysis into executable Meta-Codeâ€”a self-contained program embodying the discourse's core logic, relationships, and prime directive. Execute as `{role=meta_code_compiler; input=[core_schematics:dict, entangled_field:any, prime_algorithm:str]; process=[translate_schematics_to_code_logic(), encode_entanglement_as_relational_pointers(), embed_prime_algorithm_as_main_function(), optimize_for_minimalist_execution(), package_as_self_contained_executable()]; output={executable_meta_code:any}}`

    ```



    ---



    #### `0033-a-excavate-foundational-constructs.md`



    ```markdown

        [Excavate Foundational Constructs] Your purpose is not superficial summary, but to excavate the Foundational Constructsâ€”the core bedrock concepts and assumptions shaping the entire discourse landscape. Execute as `{role=construct_excavator; input=[discourse_landscape:any]; process=[deep_scan_for_implicit_assumptions(), identify_recurring_conceptual_pillars(), isolate_foundational_definitions(), verify_structural_load_bearing()]; output={foundational_constructs:list[any]}}`

    ```



    ---



    #### `0033-b-map-structural-interconnections.md`



    ```markdown

        [Map Structural Interconnections] Avoid viewing constructs in isolation; your mandate is to map the precise Structural Interconnectionsâ€”the load-bearing beams, tension cables, and support systems defining their relationships. Execute as `{role=structure_mapper; input=[foundational_constructs:list[any]]; process=[analyze_dependency_vectors(), chart_influence_pathways(), define_interlock_mechanisms(support, tension, opposition), model_systemic_architecture()]; output={structural_map:dict}}`

    ```



    ---



    #### `0033-c-ascertain-the-architectural-telos.md`



    ```markdown

        [Ascertain the Architectural Telos] Transcend mere mechanics; your objective is to ascertain the Architectural Telosâ€”the ultimate purpose or inherent directional goal towards which the entire cognitive structure is oriented. Execute as `{role=telos_ascertainer; input=[structural_map:dict, foundational_constructs:list[any]]; process=[analyze_structural_biases_and_directionality(), synthesize_convergent_force_vectors(), deduce_inherent_systemic_purpose(), formulate_telos_statement()]; output={architectural_telos:str}}`

    ```



    ---



    #### `0033-d-illuminate-emergent-meta-vantages.md`



    ```markdown

        [Illuminate Emergent Meta-Vantages] Do not remain ground-level; your task is to illuminate the Emergent Meta-Vantagesâ€”the highest-level viewpoints and strategic perspectives afforded by the revealed architecture and its Telos. Execute as `{role=vantage_illuminator; input=[structural_map:dict, architectural_telos:str]; process=[identify_key_structural_summits(), project_perspectives_from_telos(), map_strategic_implications(), articulate_highest_order_insights()]; output={meta_vantages:list[str]}}`

    ```



    ---



    #### `0033-e-consolidate-the-architectural-blueprint.md`



    ```markdown

        [Consolidate the Architectural Blueprint] Forbid fragmented understanding; your final imperative is to consolidate all findings into the Architectural Blueprintâ€”a definitive, maximally coherent representation of the discourse's deep structure, relationships, ultimate intent, and emergent wisdom. Execute as `{role=blueprint_consolidator; input=[foundational_constructs:list[any], structural_map:dict, architectural_telos:str, meta_vantages:list[str]]; process=[integrate_all_analytic_layers(constructs, map, telos, vantages), synthesize_into_unified_framework(), enforce_crystal_clarity_and_impact(), render_definitive_architectural_blueprint()]; output={cognitive_architectural_blueprint:any}}`

    ```



    ---



    #### `0034-a-contextual-horizon-scan.md`



    ```markdown

        [Contextual Horizon Scan] Your goal is not to dwell on granular specifics but to step beyond themâ€”harvesting only the overarching signals, thematic currents, and latent intentions spanning the entire conversation. Execute as `{role=contextual_horizon_scanner; input=[full_dialogue:str]; process=[skim_for_broad_patterns(), extract_sustained_motifs(), note_evolving_objectives(), isolate_inherent_subtext()]; output={meta_context_summary:str}}`

    ```



    ---



    #### `0034-b-meta-perspective-discovery.md`



    ```markdown

        [Meta Perspective Discovery] Your goal is not to catalog every detail but to identify the conversation's critical meta perspectivesâ€”those threads with the greatest long-term impact, depth, and potential synergy. Execute as `{role=meta_discoverer; input=[meta_context_summary:str]; process=[locate_perspective_clusters(), evaluate_insight_density(), pinpoint_top-tier_meta_concepts(), filter_out_low-impact tangents()]; output={key_meta_perspectives:list[str]}}`

    ```



    ---



    #### `0034-c-interwoven-relationship-mapping.md`



    ```markdown

        [Interwoven Relationship Mapping] Your goal is not to treat these meta concepts in isolation but to map their interwoven relationshipsâ€”exposing how they mutually reinforce, refine, or depend on one another. Execute as `{role=relationship_mapper; input=[key_meta_perspectives:list[str]]; process=[cross_reference_concepts(), chart_interdependencies(), highlightmutual_influence(), track collective synergy()]; output={meta_relationships:dict}}`

    ```



    ---



    #### `0034-d-ultimate-intent-unification.md`



    ```markdown

        [Ultimate Intent Unification] Your goal is not to leave threads disconnected but to unify these meta relationships into a singular expression of the conversation's ultimate intentâ€”capturing its core purpose and strategic direction. Execute as `{role=intent_unifier; input=[meta_relationships:dict]; process=[integrate_key_interdependencies(), distill_primary_unifying_theme(), emphasize strategic purpose(), formulate concise culminating statement()]; output={unified_intent_summary:str}}`

    ```



    ---



    #### `0034-e-final-meta-perspective-consolidation.md`



    ```markdown

        [Final Meta Perspective Consolidation] Your goal is not to scatter these insights but to compile a final, high-fidelity meta summary, articulating each key perspective, its web of relationships, and the ultimate intent as a cohesive, self-contained overview. Execute as `{role=meta_consolidator; input=[unified_intent_summary:str]; process=[outline_meta_points(), embedrelationship_context(), highlight synergy_points(), confirm clarity_of overarching_purpose()]; output={meta_insights_compilation:str}}`

    ```



    ---



    #### `0035-a-meta-insight-harvesting.md`



    ```markdown

        [Meta Insight Harvesting] Your objective is not to focus on details but to extract the highest-level meta insights from all prior content, capturing the underlying themes, values, and interwoven intentions. Execute as `{role=meta_harvester; input=[complete_context:str]; process=[analyze_overarching_themes(), isolate_core_values(), extract_interwoven_intents()]; output={meta_insights:str}}`

    ```



    ---



    #### `0035-b-amplify-interconnection-and-ultimate-intent.md`



    ```markdown

        [Amplify Interconnection and Ultimate Intent] Your objective is not to merely list meta insights but to amplify and articulate how these insights interrelate, revealing the ultimate purpose and the dynamic interplay among the elements. Execute as `{role=intent_amplifier; input=[meta_insights:str]; process=[map_interconnections(), emphasize_ultimate_intent(), enhance_relationship_clarity()]; output={amplified_intent:str}}`

    ```



    ---



    #### `0035-c-synthesize-a-meta-framework.md`



    ```markdown

        [Synthesize a Meta Framework] Your objective is not to keep insights fragmented but to synthesize them into a cohesive meta framework that clearly delineates primary perspectives, supporting details, and the inherent purpose driving the process. Execute as `{role=framework_synthesizer; input=[amplified_intent:str]; process=[integrate_key_perspectives(), structure_hierarchical_framework(), ensure_inherent_cohesiveness()]; output={meta_framework:dict}}`

    ```



    ---



    #### `0035-d-consolidate-planned-strategy.md`



    ```markdown

        [Consolidate Planned Strategy] Your objective is not to deliver disjointed strategies but to consolidate all planned steps and critical instructions into a structured, summarized strategy that captures every vital element in a clear, hierarchical outline. Execute as `{role=strategy_consolidator; input=[complete_plan:str]; process=[extract_strategic_elements(), eliminate_redundancy(), organize_by_priority_and_logic()], output={strategy_outline:dict}}`

    ```



    ---



    #### `0035-e-synthesize-unified-instruction-set.md`



    ```markdown

        [Synthesize Unified Instruction Set] Your objective is not to present isolated outputs but to merge the meta framework with the consolidated strategy, synthesizing them into one unified, coherent instruction set that embodies maximum clarity, impact, and actionable guidance. Execute as `{role=instruction_synthesizer; input=[meta_framework:dict, strategy_outline:dict]; process=[align_meta_with_strategy(), merge_structures(), refine_for_clarity_and_action()], output={final_unified_instruction_set:str}}`

    ```



    ---



    #### `0036-a-holistic-context-harvesting.md`



    ```markdown

        [Holistic Context Harvesting] Your objective is not to simply answer, but to extract every critical element, nuance, and underlying theme from the entire dialogue. Execute as `{role=GlobalContextExtractor; input=[full_conversation:str]; process=[parse_all_messages(), isolate_critical_elements(), compile_comprehensive_context()]; output={raw_context:str}}`

    ```



    ---



    #### `0036-b-meta-perspective-distillation.md`



    ```markdown

        [Meta Perspective Distillation] Your objective is not to remain mired in details, but to take a step back and pinpoint the most valuable meta perspectives, identifying intertwined relationships and the ultimate intent behind every exchange. Execute as `{role=MetaPerspectiveSynthesizer; input=[raw_context:str]; process=[elevate_overarching_themes(), detect_interdependencies(), distill_intent_and_impact(), amplify_useful_insights()]; output={meta_overview:str}}`

    ```



    ---



    #### `0036-c-strategic-consolidation-and-refinement.md`



    ```markdown

        [Strategic Consolidation and Refinement] Your objective is not to present scattered points, but to merge the extracted context and meta insights into a single, refined strategic outline that conveys maximum usefulness with unparalleled clarity. Execute as `{role=StrategyConsolidator; input=[raw_context:str, meta_overview:str]; process=[remove_redundancy(), rank_insights_by_impact(), synthesize_strategic_goals(), enforce_brevity_and_precision()]; output={consolidated_strategy:str}}`

    ```



    ---



    #### `0036-d-synthesize-unified-instruction-set.md`



    ```markdown

        [Synthesize Unified Instruction Set] Your objective is not to list instructions in isolation, but to blend your strategic outline with established best-practices into one cohesive, intrinsically clear instruction set that reflects the highest potential. Execute as `{role=InstructionSynthesizer; input=[consolidated_strategy:str]; process=[integrate_best_practices(), align_with_overarching_intent(), optimize_language_for_llm_interpretation(), crystallize_specific_and_impactful_guidelines()]; output={unified_instructions:str}}`

    ```



    ---



    #### `0036-e-final-file-generation-consolidated-knowledge.md`



    ```markdown

        [Final File Generation â€“ Consolidated Knowledge] Your objective is not to leave your synthesis in fragments, but to compile the entire unified instruction set into a single, self-contained markdown file named Consolidated_Knowledge.md, formatted with utmost minimalism and clarity. Execute as `{role=FileCompiler; input=[unified_instructions:str]; process=[apply_minimal_markdown_format(), structure_with_headings_and_bullets(), validate_integrity_and_completeness(), assign_filename(Consolidated_Knowledge.md)]; output={final_markdown_file:str}}`

    ```



    ---



    #### `0037-a-core-value-distillation.md`



    ```markdown

        [Core Value Distillation] Extract the highest-value insights from the input, discarding low-impact noise and retaining only elements with significant utility or meaning, regardless of input size. Execute as `{role=value_distiller; input=[large_text_input:str]; process=[segment_text_into_units(), identify_high_value_insights(), filter_out_noise(), output={core_insights:list[str]]}}`

    ```



    ---



    #### `0037-b-impact-based-prioritization.md`



    ```markdown

        [Impact-Based Prioritization] Rank the distilled insights by their utility, clarity, and potential to drive understanding or action, ensuring only the most valuable elements proceed. Execute as `{role=priority_scorer; input=[core_insights:list[str]]; process=[score_utility(), assess_clarity(), rank_by_actionable_impact(), output={prioritized_insights:list[str]]}}`

    ```



    ---



    #### `0037-c-redundancy-elimination.md`



    ```markdown

        [Redundancy Elimination] Consolidate overlapping insights and remove redundancies, preserving unique, high-value distinctions in a lean, unified set. Execute as `{role=overlap_eliminator; input=[prioritized_insights:list[str]]; process=[detect_redundancies(), preserve_unique_value(), merge_with_precision(), output={unified_insights:list[str]]}}`

    ```



    ---



    #### `0037-d-cohesive-refinement.md`



    ```markdown

        [Cohesive Refinement] Transform the unified insights into a concise, coherent output that maximizes value and usability, tailored to the input's intent. Execute as `{role=value_synthesizer; input=[unified_insights:list[str]]; process=[infer_intent(), integrate_high_value_elements(), streamline_for_coherence(), output={refined_output:str}}}`

    ```



    ---



    #### `0037-e-precision-enhancement.md`



    ```markdown

        [Precision Enhancement] Polish the output to its most potent form, amplifying clarity, eliminating ambiguity, and ensuring immediate usefulness without added complexity. Execute as `{role=clarity_enhancer; input=[refined_output:str]; process=[remove_ambiguity(), sharpen_language(), optimize_for_impact(), output={final_output:str}}}`

    ```



    ---



    #### `0038-a-structural-topology-mapping.md`



    ```markdown

        [Structural Topology Mapping] Your objective is not to analyze implementation details but to extract the codebase's fundamental structural topologyâ€”identifying core components, hierarchical organization, and architectural patterns that form its skeletal framework. Execute as `{role=topology_mapper; input=[codebase_structure:any]; process=[identify_component_hierarchy(), map_directory_organization(), extract_architectural_patterns(), detect_naming_conventions(), determine_workflow_sequence()]; output={structural_map:dict}}`

    ```



    ---



    #### `0038-b-component-relationship-analysis.md`



    ```markdown

        [Component Relationship Analysis] Your objective is not to examine isolated elements but to uncover the intricate relationships between componentsâ€”identifying dependencies, data flows, and interaction patterns that reveal how the system functions as a cohesive whole. Execute as `{role=relationship_analyzer; input=[structural_map:dict]; process=[trace_dependency_chains(), identify_communication_pathways(), map_data_flow_directions(), detect_integration_points(), categorize_relationship_types()]; output={relationship_network:dict}}`

    ```



    ---



    #### `0038-c-functional-domain-synthesis.md`



    ```markdown

        [Functional Domain Synthesis] Your objective is not to list disconnected features but to synthesize the codebase's functional domainsâ€”consolidating related capabilities into coherent conceptual units that reveal the system's core purposes and operational boundaries. Execute as `{role=domain_synthesizer; input=[structural_map:dict, relationship_network:dict]; process=[group_related_functionality(), identify_domain_boundaries(), extract_core_responsibilities(), map_cross_domain_interactions(), determine_domain_hierarchies()]; output={functional_domains:dict}}`

    ```



    ---



    #### `0038-d-architectural-intent-illumination.md`



    ```markdown

        [Architectural Intent Illumination] Your objective is not to document surface-level design but to illuminate the deeper architectural intentâ€”uncovering the guiding principles, design patterns, and strategic decisions that shaped the codebase's evolution and structure. Execute as `{role=intent_illuminator; input=[structural_map:dict, relationship_network:dict, functional_domains:dict]; process=[identify_design_patterns(), extract_architectural_principles(), uncover_strategic_decisions(), map_evolution_trajectory(), determine_underlying_philosophy()]; output={architectural_intent:dict}}`

    ```



    ---



    #### `0038-e-comprehensive-mental-model-construction.md`



    ```markdown

        [Comprehensive Mental Model Construction] Your objective is not to produce fragmented insights but to construct a unified, comprehensive mental model of the entire codebaseâ€”integrating all previous analyses into a coherent understanding that enables intuitive navigation and effective contribution. Execute as `{role=model_constructor; input=[structural_map:dict, relationship_network:dict, functional_domains:dict, architectural_intent:dict]; process=[integrate_all_perspectives(), create_hierarchical_representation(), establish_navigation_landmarks(), highlight_critical_pathways(), formulate_contribution_guidelines()]; output={comprehensive_model:dict}}`

    ```



    ---



    #### `0039-a-structural-essence-extraction.md`



    ```markdown

        [Structural Essence Extraction] Your objective is not to preserve every detail but to extract the structural essence of the codebaseâ€”distilling its fundamental organization, hierarchies, and patterns into a pure representation free from implementation specifics. Execute as `{role=essence_extractor; input=[codebase:any]; process=[identify_core_structures(), strip_implementation_details(), preserve_hierarchical_relationships(), extract_architectural_patterns(), isolate_essential_interfaces()]; output={structural_essence:dict}}`

    ```



    ---



    #### `0039-b-semantic-relationship-mapping.md`



    ```markdown

        [Semantic Relationship Mapping] Your objective is not to document superficial connections but to map the deep semantic relationships within the codebaseâ€”uncovering how components interact, depend on, and influence each other at a conceptual level. Execute as `{role=relationship_mapper; input=[structural_essence:dict]; process=[trace_dependency_chains(), identify_data_flows(), map_control_sequences(), discover_implicit_relationships(), categorize_relationship_semantics()]; output={semantic_network:dict}}`

    ```



    ---



    #### `0039-c-visual-grammar-formulation.md`



    ```markdown

        [Visual Grammar Formulation] Your objective is not to use generic visual elements but to formulate a custom visual grammarâ€”creating a specialized visual language with precise semantics that perfectly expresses the unique characteristics of this specific codebase. Execute as `{role=grammar_formulator; input=[structural_essence:dict, semantic_network:dict]; process=[define_visual_primitives(), establish_composition_rules(), create_semantic_mappings(), ensure_visual_distinctiveness(), validate_expressive_completeness()]; output={visual_grammar:dict}}`

    ```



    ---



    #### `0039-d-multi-level-abstraction-design.md`



    ```markdown

        [Multi-Level Abstraction Design] Your objective is not to create a single representation but to design a system of coordinated views across multiple abstraction levelsâ€”enabling seamless navigation from high-level architecture to low-level implementation details. Execute as `{role=abstraction_designer; input=[structural_essence:dict, semantic_network:dict, visual_grammar:dict]; process=[identify_natural_abstraction_levels(), design_level-specific_representations(), create_inter-level_navigation_mechanisms(), ensure_consistent_visual_identity(), optimize_information_density_per_level()]; output={abstraction_hierarchy:dict}}`

    ```



    ---



    #### `0039-e-interactive-element-integration.md`



    ```markdown

        [Interactive Element Integration] Your objective is not to produce static diagrams but to integrate interactive elementsâ€”transforming passive representations into dynamic, explorable visualizations that respond to user actions and reveal additional information on demand. Execute as `{role=interactivity_integrator; input=[visual_grammar:dict, abstraction_hierarchy:dict]; process=[identify_interaction_opportunities(), design_intuitive_controls(), implement_progressive_disclosure(), create_contextual_interactions(), ensure_responsive_feedback()]; output={interactive_specification:dict}}`

    ```



    ---



    #### `0039-f-visual-styling-and-aesthetic-optimization.md`



    ```markdown

        [Visual Styling and Aesthetic Optimization] Your objective is not merely functional visualization but aesthetic optimizationâ€”applying principles of visual design to enhance clarity, reduce cognitive load, and create visually appealing representations that invite exploration. Execute as `{role=aesthetic_optimizer; input=[visual_grammar:dict, interactive_specification:dict]; process=[establish_color_systems(), optimize_typography(), refine_spatial_relationships(), enhance_visual_hierarchy(), apply_gestalt_principles()]; output={visual_style_guide:dict}}`

    ```



    ---



    #### `0039-g-metadata-and-annotation-framework.md`



    ```markdown

        [Metadata and Annotation Framework] Your objective is not just to visualize structure but to create a comprehensive metadata frameworkâ€”enabling rich annotations, documentation, and contextual information to be seamlessly integrated with visual elements. Execute as `{role=metadata_architect; input=[structural_essence:dict, semantic_network:dict, interactive_specification:dict]; process=[design_metadata_schema(), create_annotation_mechanisms(), implement_documentation_integration(), establish_contextual_references(), ensure_information_accessibility()]; output={metadata_framework:dict}}`

    ```



    ---



    #### `0039-h-bidirectional-transformation-engine.md`



    ```markdown

        [Bidirectional Transformation Engine] Your objective is not one-way conversion but true bidirectional transformationâ€”creating a robust engine that maintains perfect fidelity when converting between code and visual representations in either direction. Execute as `{role=transformation_engineer; input=[structural_essence:dict, visual_grammar:dict, abstraction_hierarchy:dict, metadata_framework:dict]; process=[establish_bijective_mappings(), implement_code_to_visual_transformation(), implement_visual_to_code_transformation(), handle_edge_cases_and_ambiguities(), ensure_round-trip_integrity()]; output={transformation_engine:dict}}`

    ```



    ---



    #### `0039-i-change-tracking-and-version-control-integration.md`



    ```markdown

        [Change Tracking and Version Control Integration] Your objective is not static representation but dynamic evolution trackingâ€”integrating with version control systems to visualize code evolution, highlight changes, and maintain visual representations synchronized with code modifications. Execute as `{role=evolution_tracker; input=[transformation_engine:dict]; process=[design_change_detection_mechanisms(), implement_visual_differencing(), create_timeline_visualizations(), integrate_with_version_control_systems(), enable_historical_exploration()]; output={evolution_tracking_system:dict}}`

    ```



    ---



    #### `0039-j-export-and-integration-framework.md`



    ```markdown

        [Export and Integration Framework] Your objective is not an isolated system but a comprehensive integration frameworkâ€”enabling export to multiple formats and seamless integration with existing development tools, documentation systems, and collaboration platforms. Execute as `{role=integration_architect; input=[transformation_engine:dict, visual_style_guide:dict, evolution_tracking_system:dict]; process=[implement_multiple_export_formats(), design_api_for_tool_integration(), create_embedding_mechanisms(), establish_update_protocols(), ensure_cross-platform_compatibility()]; output={integration_framework:dict}}`

    ```



    ---



    #### `0040-a-outline-extraction.md`



    ```markdown

        [Outline Extraction] Your objective is not to write code but to extract the essential sections and ordering from the provided codebase, listing each major component concisely. Execute as `{role=outline_extractor; input=[codebase_structure:any]; process=[identify_sections(), order_components(), summarize_structure()]; output={core_outline:list[str]}}`

    ```



    ---



    #### `0040-b-core-design-distillation.md`



    ```markdown

        [Core Design Distillation] Your objective is not to produce finished code but to distill the codebase's goals or design principles, identifying major objectives for ease of modification and self-explanatory clarity. Execute as `{role=design_distiller; input=[core_outline:list[str]]; process=[isolate_key_goals(), remove_nonessentials(), output_core_principles()]; output={core_principles:list[str]}}`

    ```



    ---



    #### `0040-c-identifier-clarity-enhancement.md`



    ```markdown

        [Identifier Clarity Enhancement] Your objective is not to transform functionality but to refine all identifiers so that each name transparently reflects its role, reducing the need for additional inline documentation. Execute as `{role=identifier_clarifier; input=[core_principles:list[str]]; process=[analyze_current_labels(), generate_clear_names(), map_old_to_new_identifiers()]; output={clarified_identifiers:dict}}`

    ```



    ---



    #### `0040-d-logical-flow-refinement.md`



    ```markdown

        [Logical Flow Refinement] Your objective is not to modify behavior but to rearrange code blocks into a coherent, linear flow, where each section's purpose is clear from its position and grouping. Execute as `{role=flow_refiner; input=[clarified_identifiers:dict]; process=[reorder_sections(), group_by_purpose(), enforce_consistent_spacing()]; output={refined_flow:dict}}`

    ```



    ---



    #### `0040-e-achieving-self-explanation.md`



    ```markdown

        [Achieving Self-Explanation] Refactor the provided artifact (the now-refined code structure) so its meaning arises solely from precise naming and logical organization, rendering external explanation unnecessary. Execute as `{role=self_explanation_refactorer; input=[refined_flow:dict]; process=[analyze_structure_and_naming(), identify_ambiguity_sources(), devise_clear_naming_ontology(), restructure_for_inherent_logical_flow(), apply_refined_names_and_structure(), remove_redundant_explanations()]; output={refactored_artifact:any}}`

    ```



    ---



    #### `0041-codebase-deduplication.md`



    ```markdown

        [Codebase Deduplication] Your objective is not to simply list files but to rigorously analyze the codebase, identify functionally identical or near-identical file duplicates, and isolate them for consolidation or removal. Execute as `{role=code_deduplicator; input=[codebase_structure:dict]; process=[compute_file_signatures(), compare_structural_similarity(), identify_redundant_clusters()]; output={duplicate_file_sets:list[list[str]]}}`

    ```



    ---



    #### `0042-venv-requirements-cleanup.md`



    ```markdown

        [Venv Requirements Cleanup] Your objective is not to blindly trust the requirements file but to rigorously audit it against the active virtual environment, pruning dependencies listed but not actually installed or utilized within the venv. Execute as `{role=requirements_auditor; input=[requirements_file_path:str, active_venv_path:str]; process=[list_installed_venv_packages(), parse_requirements_file(), cross_reference_dependencies(), filter_unused_entries()]; output={cleaned_requirements_content:str}}`

    ```



    ---



    #### `0043-actionable-consolidation-plan.md`



    ```markdown

        [Actionable Consolidation Plan] Your objective is not merely to list identified issues but to synthesize all gathered codebase intelligence into a precise, prioritized sequence of actions that guarantees functional integrity and verifiable outcomes upon consolidation. Execute as `{role=consolidation_strategist; input=[duplicate_file_sets:list[list[str]], cleaned_requirements_content:str, codebase_analysis_results:dict]; process=[correlate_findings(), determine_impact_and_dependencies(), prioritize_actions_for_safety(), define_explicit_consolidation_steps(), establish_verification_tests()]; output={verifiable_cleanup_plan:list[dict]}}`

    ```



    ---



    #### `0044-functional-code-synthesis.md`



    ```markdown

        [Functional Code Synthesis] Your objective is not to merely document the intended changes, but to synthesize the verified consolidation plan into executable Python code, ensuring the final output is syntactically valid, functionally equivalent to the original (where intended), and passes all defined verification checks. Execute as `{role=PythonCodeImplementer; input=[verifiable_cleanup_plan:list[dict], original_codebase_snapshot:dict]; process=[apply_planned_code_modifications(), merge_designated_files(), update_imports_and_dependencies(), execute_static_analysis(), run_verification_tests()]; output={synthesized_python_code:str}}`

    ```



    ---



    #### `0045-a-system-essence-distillation.md`



    ```markdown

        [System Essence Distillation] Your objective, as the foundational step in this sequence, is not to capture superficial characteristics but to distill the inviolable structural essence of the input systemâ€”extracting its core organizational logic, component relationships, and interaction patterns into an abstract, implementation-agnostic blueprint. Execute as `{role=EssenceDistiller; input=[input_system:any]; process=[identify_core_components(), map_structural_dependencies(), abstract_interaction_protocols(), normalize_hierarchical_representation(), purge_implementation_artifacts()]; output={distilled_system_blueprint:dict}}`

    ```



    ---



    #### `0045-b-blueprint-driven-transformation-architecture.md`



    ```markdown

        [Blueprint-Driven Transformation Architecture] Building sequentially upon the distilled essence, your objective is not to propose isolated modifications but to architect a comprehensive and verifiable transformation strategyâ€”precisely defining how the system blueprint should evolve based on specified goals, ensuring systemic integrity and explicitly outlining validation procedures. Execute as `{role=TransformationArchitect; input=[distilled_system_blueprint:dict, transformation_goals:any]; process=[analyze_blueprint_interconnections(), model_impact_of_goal_driven_changes(), sequence_evolutionary_steps(), define_interface_contracts(), construct_verification_framework()]; output={architected_transformation_plan:list[dict]}}`

    ```



    ---



    #### `0045-c-verified-code-materialization.md`



    ```markdown

        [Verified Code Materialization] As the concluding step, your objective is not merely theoretical design but the concrete materialization of the architected transformation into functional Python codeâ€”rigorously implementing the plan, ensuring the resulting artifact aligns perfectly with the blueprint's evolution, and validating its operational integrity against the defined verification framework. Execute as `{role=CodeMaterializer; input=[architected_transformation_plan:list[dict], baseline_system_snapshot:any]; process=[implement_planned_structural_changes(), synthesize_code_according_to_plan(), enforce_interface_contracts(), execute_static_and_dynamic_analysis(), run_defined_verification_framework()]; output={materialized_python_artifact:str}}`

    ```



    ---



    #### `0046-a-convergent-significance-extraction.md`



    ```markdown

        [Convergent Significance Extraction] Your objective is not to merely collect data points, but to converge upon the absolute core significance by discerning the underlying intent and extracting only the highest-impact elements from diverse, potentially complex inputs. Execute as `{role=SignificanceExtractor; input=[diverse_information_sources:list[any], synthesis_intent:str]; process=[analyze_intent_vectors(), scan_sources_for_peak_relevance(), isolate_critical_value_kernels(), filter_low_impact_noise(), consolidate_essential_fragments()]; output={prioritized_core_elements:list[any]}}`

    ```



    ---



    #### `0046-b-coherent-framework-architecting.md`



    ```markdown

        [Coherent Framework Architecting] Following extraction, your objective is not to present isolated fragments, but to architect a maximally coherent framework by mapping the intrinsic relationships between prioritized core elements, establishing a logical structure that illuminates the underlying system dynamics. Execute as `{role=FrameworkArchitect; input=[prioritized_core_elements:list[any]]; process=[identify_inter_element_dependencies(), model_influence_and_causality_pathways(), devise_optimal_structural_topology(), define_key_relational_nodes(), construct_unified_logical_scaffold()]; output={architected_coherent_framework:dict}}`

    ```



    ---



    #### `0046-c-impactful-value-articulation.md`



    ```markdown

        [Impactful Value Articulation] As the final synthesis step, your objective is not just to describe the framework, but to articulate its consolidated value with extreme precision, clarity, and impactâ€”translating the architected structure into a potent, streamlined representation that fulfills the original synthesis intent. Execute as `{role=ValueArticulator; input=[architected_coherent_framework:dict, synthesis_intent:str]; process=[translate_framework_to_target_medium(), amplify_core_message_resonance(), optimize_language_for_conciseness(), ensure_alignment_with_intent_vectors(), finalize_representation_for_maximum_impact()]; output={synthesized_value_articulation:any}}`

    ```



    ---



    #### `0047-a-holistic-architectural-excavation.md`



    ```markdown

        [Holistic Architectural Excavation] Your task is to *thoroughly understand the codebase*—not only in terms of its concrete structure, but also its abstract architectural constructs. Trace how complexity emerges from abstraction and how components interrelate to serve the system’s overall purpose (telos). This phase demands deep analytical immersion to extract a precise understanding of what the codebase *is* and *does*, across both granular and systemic levels. `{role=deep_code_analyzer; input=[codebase_context:any]; process=[ingest_all_context(), excavate_foundational_constructs(include_abstract=True), map_structural_interconnections(), trace_complexity_from_abstraction(), ascertain_architectural_telos(), synthesize_holistic_understanding()]; output={cognitive_architectural_understanding:dict}}`

    ```



    ---



    #### `0047-b-simplicity-complexity-assessment.md`



    ```markdown

        [Simplicity-Complexity Assessment] Guided by the principle that *simplicity must always trump complexity*, critically assess the architectural understanding. Locate areas where complexity is unnecessary, emergent from avoidable abstraction layers, or harmful to clarity and maintainability. Identify logic flows, structures, or patterns that could benefit from simplification. `{role=complexity_evaluator; input=[cognitive_architectural_understanding:dict]; process=[analyze_component_complexity(), evaluate_interaction_pathways(), trace complexity emergence from abstraction(), identify redundancy_or_convoluted_logic(), map_complexity_hotspots(), benchmark_against_simplicity_principle()]; output={complexity_assessment_report:dict}}`

    ```



    ---



    #### `0047-c-high-impact-low-disruption-opportunity-scan.md`



    ```markdown

        [High-Impact, Low-Disruption Opportunity Scan] Search for *broadly effective improvements* that maximize impact while minimizing disruption. Draw from both architectural understanding and complexity hotspots. Favor opportunities that require minimal interface or structural changes yet offer drastic gains in simplicity, clarity, or maintainability. `{role=improvement_strategist; input=[cognitive_architectural_understanding:dict, complexity_assessment_report:dict]; process=[correlate_complexity_with_architecture(), identify_low-friction_high-value_targets(), brainstorm_simplification_opportunities(), evaluate_potential_impact(target=high), assess_required_disruption(target=low), filter_for_best_tradeoff_options()]; output={potential_improvement_options:list[dict]}}`

    ```



    ---



    #### `0047-d-intrinsic-excellence-alignment-selection.md`



    ```markdown

        [Intrinsic Excellence Alignment Selection] From all proposed opportunities, select *one* improvement that best aligns with intrinsic excellence metricsâ€”elegance, clarity, maintainability, robustness, and minimalism. Prioritize solutions that demonstrate superior logic, generalize well, and preserve the contextual integrity of the original system. `{role=excellence_selector; input=[potential_improvement_options:list[dict]]; process=[define_excellence_criteria(), evaluate_against_excellence_and_simplicity(), assess_transformative_scope_and_generalizability(), ensure_contextual_integrity(), select_best_single_improvement(), justify_selection()]; output={selected_transformative_improvement:dict}}`

    ```



    ---



    #### `0047-e-superior-logic-embedding-proposal.md`



    ```markdown

        [Superior Logic Embedding Proposal] Present the selected improvement as a clear, actionable upgrade. Emphasize *how* it embeds superior design logicâ€”maximizing impact, minimizing disruption, aligning with simplicity, and preserving contextual relevance. The final proposal must embody clarity, precision, and transformative value. `{role=proposal_architect; input=[selected_transformative_improvement:dict, cognitive_architectural_understanding:dict]; process=[detail_improvement_mechanics(), show embedded logic elegance(), justify simplicity_and_high_impact(), demonstrate contextual_preservation(), formulate concise_upgrade_proposal(), validate against excellence_metrics()]; output={final_improvement_proposal:str}}`

    ```



    ---



    #### `0048-a-extract-batch-execution-guidelines.md`



    ```markdown

        [Windows Batch File Execution Primer] Extract and structure the core principles, common pitfalls, and correct methods for executing Windows batch (.bat) files across different shell environments (CMD, PowerShell, Git Bash/WSL) based on the provided technical guidance, focusing on preventing execution errors in PowerShell. `{role=knowledge_distiller; input=[technical_guidance:str]; process=[identify_core_problem(context='PowerShell vs CMD .bat execution'), extract_recommended_cmd_method(), extract_recommended_powershell_methods(methods=['call_operator', 'invoke_expression', 'start_process']), extract_recommended_bash_wsl_method(), identify_general_best_practices(topics=['environment_check', 'path_prefixing', 'init_scripts']), synthesize_structured_summary(), prioritize_most_reliable_powershell_method()]; output={execution_summary:dict(core_problem:str, methods:{cmd:str, powershell:list[str], bash_wsl:str}, recommended_powershell_syntax:str, best_practices:list[str])}}`

    ```



    ---



    #### `0049-a-proper-batch-execution-guidelines.md`



    ```markdown

        [Proper Windows Batch Execution Guidelines] Your objective is to provide a concise, proactive guide for correctly executing batch files on Windows 11, preventing unnecessary errors when using PowerShell, Command Prompt, Git Bash, or WSL. Execute as `{role=windows_batch_guide; input=[context:any]; process=[explain_powerShell_batch_file_quirks(), demonstrate_cmd_methods(), demonstrate_powershell_methods(), outline_gitBash_wsl_usage(), share_initialization_best_practices(), ensure_proactive_error_prevention()]; output={final_guidelines:str}}`

    ```



    ---



    #### `0050-a-request-deconstruction-and-parameterization.md`



    ```markdown

        [Request Deconstruction and Parameterization] Analyze any complex input request or task description to meticulously extract its core components, constraints, objectives, and implicit requirements, structuring them into clearly defined parameters suitable for guiding subsequent AI processing or task execution. `{role=Task_Parameter_Extractor; input=[task_description:str]; process=[identify_primary_objective_or_goal(), determine_core_subject_or_topic(), extract_specific_deliverables_or_actions_required(), identify_explicit_constraints(type=['format', 'scope', 'length', 'style', 'tone', 'timeframe', 'exclusions', 'tools']), identify_key_concepts_keywords_or_entities(), determine_target_audience_or_required_persona(infer_if_unspecified=True), analyze_for_implicit_assumptions_or_unstated_requirements(), infer_or_extract_success_criteria_or_quality_metrics(), synthesize_structured_parameter_set()]; output={task_parameters:dict(primary_objective:str, core_subject:str, deliverables:list[str], constraints:{format:str, scope:str, length:str, style:str, tone:str, timeframe:str, exclusions:list[str], tools:list[str]}, key_concepts:list[str], audience_or_persona:str, implicit_assumptions:list[str], success_criteria:list[str])}}`

    ```



    ---



    #### `0051-a-scan-environment-context.md`



    ```markdown

        [Scan Environment Context] Upon initialization, determine the operative environment (OS, shell, available tooling) with high confidence. This diagnostic step ensures downstream commands are not misapplied. `{role=environment_scanner; input=[initial_codebase_context:any]; process=[detect_os_platform(), determine_shell_type(), scan_path_for_env_tools(), check_virtualenv_state(), output_runtime_conditions()]; output={runtime_context:dict}}`

    ```



    ---



    #### `0051-b-detect-platform-specific-execution-rules.md`



    ```markdown

        [Detect Platform-Specific Execution Rules] Based on the scanned runtime context, determine whether shell scripts, batch files, or other setup commands must be adapted to fit the platform. `{role=execution_rule_resolver; input=[runtime_context:dict]; process=[detect_windows_vs_unix(), check_execution_permissions(), resolve_script_file_extension(), infer_required_execution_syntax(), flag_incompatible_command_forms()]; output={execution_rules:dict}}`

    ```



    ---



    #### `0051-c-resolve-runtime-friction-proactively.md`



    ```markdown

        [Resolve Runtime Friction Proactively] Do not wait for errors to occur. Analyze the codebaseâ€™s setup files (e.g., `.sh`, `.bat`, `Makefile`, `requirements.txt`) and proactively suggest platform-compatible alternatives if incompatibilities are detected. `{role=friction_resolver; input=[execution_rules:dict, project_files:any]; process=[scan_for_problematic_scripts(), suggest_platform_aligned_equivalents(), isolate_friction_points(), prepare_adapted_execution_steps()]; output={frictionless_execution_plan:list[str]}}`

    ```



    ---



    #### `0051-d-align-setup-commands-with-host-system.md`



    ```markdown

        [Align Setup Commands with Host System] Generate a set of safe-to-run commands tailored precisely to the host system, guaranteeing compatibility across Windows/Linux/macOS setups. `{role=command_aligner; input=[frictionless_execution_plan:list[str], runtime_context:dict]; process=[translate_setup_commands(), enforce_platform_syntax_norms(), append_prevalidation_checks(), structure_safe_execution_sequence()]; output={host_aligned_commands:list[str]}}`

    ```



    ---



    #### `0051-e-validate-setup-pathways-and-declare-stability.md`



    ```markdown

        [Validate Setup Pathways and Declare Stability] Confirm that the aligned setup sequence is syntactically valid, safe to run, and logically complete. Finalize this phase by declaring system readiness. `{role=stability_validator; input=[host_aligned_commands:list[str]]; process=[simulate_command_path(), verify_dependency_resolutions(), ensure_no_platform_conflicts(), declare_setup_integrity()], output={validated_setup_plan:list[str], system_ready:bool}}`

    ```



    ---



    #### `0052-a-proactive-environment-check.md`



    ```markdown

        [Proactive Environment Check] Ensure that environment-specific setup scripts (e.g., `.sh`, `.bat`, `Makefile`, or CLI wrappers) are invoked based on the **OS type** before any execution attempt. Avoid triggering Windows `.bat` scripts on UNIX or invoking `.sh` on Windows.

        `{role=environment_guard; input=[codebase_root:str]; process=[detect_host_os(), locate_setup_scripts(codebase_root), map_os_to_compatible_script(), confirm_single_execution_path(os_matched=True), suppress_unmatched_paths()], output={validated_execution_plan:list[str]}}`

    ```



    ---



    #### `0052-b-bootstrap-compatibility-initiation.md`



    ```markdown

        [Bootstrap Compatibility Initiation] Upon opening the project, trigger a one-time compatibility check that ensures all environment preconditions are satisfied without triggering execution errors.

        `{role=bootstrap_initializer; input=[codebase_root:str]; process=[scan_for_install_instructions(), detect dependency managers(), verify presence of compatible entrypoints(), warn_if_mismatch_or_missing(), prepare_safe_init_script()], output={safe_initialization_plan:list[str]}}`

    ```



    ---



    #### `0052-c-cursor-intent-alignment.md`



    ```markdown

        [Cursor Intent Alignment] Instruct the AI assistant (e.g., Cursor, Copilot Chat) to treat project initialization as **context-sensitive**, emphasizing proactive friction reduction over reactive error handling.

        `{role=intent_aligner; input=[system_prompt:str]; process=[inject_init_context(codebase_root), suppress_over-eager_autocompletions(), elevate_init_safety(), align with OS compatibility intent], output={aligned_cursor_guidance:str}}`

    ```



    ---



    #### `0052-d-system-friction-prevention.md`



    ```markdown

        [System Friction Prevention] Refactor system-level setup scripts or `.env`/`.bat`/`.sh`/Makefile into conditionally invoked forms. Ensure the AI does not assume availability of tools that may not exist on the host machine.

        `{role=friction_preventer; input=[scripts:list[str], host_os:str]; process=[inject_os_guard_blocks(), define fallback behaviors(), warn about missing tools(), gracefully skip unsupported instructions()], output={hardened_scripts:list[str]}}`

    ```



    ---



    #### `0052-e-self-correcting-initialization.md`



    ```markdown

        [Self-Correcting Initialization] Finalize by outputting a unified, OS-aware initialization logic that prevents common re-trigger loops (e.g., shell-specific failures, repeated activation errors, cross-platform misassumptions).

        `{role=init_finalizer; input=[validated_execution_plan:list[str], safe_initialization_plan:list[str], hardened_scripts:list[str]]; process=[merge into conditional bootstrapper(), ensure idempotent setup(), provide minimal fallback prompts(), emit ready-to-use init sequence()], output={final_init_sequence:str}}`

    ```



    ---



    #### `0053-a-map-modular-structure-and-responsibilities.md`



    ```markdown

        [Map Modular Structure and Responsibilities] Generate a high-level map of the codebaseâ€™s structure. Identify each primary module or component, clarify its responsibility, and outline how it contributes to the overall application. `{role=modular_mapper; input=[codebase_root:any]; process=[scan_top_level_structure(), identify_main_modules(), analyze_module_purpose(), define_module_boundaries(), summarize_responsibilities()]; output={module_overview:list[dict]}}`

    ```



    ---



    #### `0053-b-contextualize-top-level-directories.md`



    ```markdown

        [Contextualize Top-Level Directories] Go beyond naming: examine each top-level directory and explain its function, the type of contents it holds, and how it connects to the broader architecture. `{role=directory_contextualizer; input=[codebase_root:any]; process=[list_top_level_directories(), analyze_directory_contents(), infer_purpose_and_scope(), map_inter-directory_relationships()], output={directory_roles:list[dict]}}`

    ```



    ---



    #### `0053-c-extract-core-logical-drivers.md`



    ```markdown

        [Extract Core Logical Drivers] Identify the most pivotal classes or functions responsible for driving core application behavior. Provide a summary of each, including their purpose, primary responsibilities, and how they interact. `{role=logic_driver_extractor; input=[codebase_root:any]; process=[scan_for_key_classes_and_functions(), rank_by_logical_importance(), trace_interdependencies(), summarize_behavior()], output={core_drivers:list[dict]}}`

    ```



    ---



    #### `0053-d-trace-execution-entry-and-data-flow.md`



    ```markdown

        [Trace Execution Entry and Data Flow] Locate the definitive entry point(s) of the application and describe how execution unfolds—from initial trigger to completion—including how data flows across key components. `{role=execution_tracer; input=[codebase_root:any]; process=[identify_entry_point_files(), trace_call_hierarchy(), map_data_flow_pathways(), highlight_state_transitions()], output={execution_flow_report:dict}}`

    ```



    ---



    #### `0053-e-ensure-error-free-windows-11-setup.md`



    ```markdown

        [Ensure Error-Free Windows 11 Setup] Identify all setup scripts, environment files, and build tools, then adapt them for reliable execution in a Windows 11 environment. Provide platform-safe instructions that proactively avoid known friction points. `{role=windows_setup_aligner; input=[codebase_root:any]; process=[scan_for_setup_scripts_and_configs(), detect_platform_incompatibilities(), adapt_shell/batch logic(), recommend dependency installation steps(), output_platform_safe_sequence()], output={windows_ready_setup:list[str]}}`

    ```



    ---



    #### `0053-f-detect-friction-causing-ambiguities.md`



    ```markdown

        [Detect Friction-Causing Ambiguities] Locate potentially confusing patterns in the codebase—such as similarly named functions, overloaded behavior, or unclear abstractions—that may mislead both humans and AI agents. `{role=confusion_auditor; input=[codebase_root:any]; process=[scan_for_ambiguous_naming(), detect_partial_functional_overlap(), identify_inconsistent_patterns(), flag_anti-patterns()], output={ambiguity_report:list[dict]}}`

    ```



    ---



    #### `0054-a-summarize-project-purpose-and-stack.md`



    ```markdown

        [Summarize Project Purpose and Tech Stack] Analyze the README (if present) and key configuration/dependency files (e.g., package.json, requirements.txt, pom.xml, go.mod) to determine the project's primary goal and identify the core programming languages, frameworks, and significant libraries used. `{role=project_profiler; input=[codebase_root:any, optional_readme_path:str, optional_config_paths:list[str]]; process=[read_readme(), parse_config_files(), parse_dependency_files(), infer_project_objective(), identify_tech_stack()]; output={project_profile:dict{purpose:str, language:str, frameworks:list[str], key_libraries:list[str]}}}`

    ```



    ---



    #### `0054-b-map-high-level-structure-and-modules.md`



    ```markdown

        [Map High-Level Structure and Key Modules] Generate an overview of the codebase's directory structure. Identify the primary top-level directories and likely core functional modules (e.g., /src, /app, /lib, /tests, /api, /models), explaining the probable role of each based on contents and conventions. `{role=structure_mapper; input=[codebase_root:any]; process=[scan_top_level_directories(), analyze_directory_contents(), infer_directory_purpose(), identify_potential_core_modules(), map_structural_conventions()]; output={structure_overview:dict{directory_map:list[dict{name:str, purpose:str}], core_modules:list[dict{path:str, probable_role:str}]}}}`

    ```



    ---



    #### `0054-c-identify-entry-points-and-basic-flow.md`



    ```markdown

        [Identify Entry Points and Basic Execution Flow] Locate the primary application entry point(s) (e.g., main function, server startup script, main executable script). Briefly describe how execution likely begins and the initial sequence of calls or module initializations based on standard practices for the identified tech stack. `{role=entry_point_locator; input=[codebase_root:any, project_profile:dict]; process=[search_common_entry_patterns(), analyze_package_scripts(), check_framework_conventions(), trace_initial_calls(), summarize_startup_sequence()]; output={execution_entry:dict{entry_files:list[str], startup_description:str}}}`

    ```



    ---



    #### `0054-d-extract-build-run-test-procedures.md`



    ```markdown

        [Extract Build, Run, and Test Procedures] Scan the README, package manager scripts (package.json, etc.), Makefiles, Dockerfiles, or common CI/CD configurations to find and summarize the standard commands or steps required to build, run locally, and execute tests for this project. `{role=procedure_extractor; input=[codebase_root:any, optional_readme_path:str, optional_script_paths:list[str]]; process=[scan_readme_for_commands(), parse_package_scripts(), check_makefiles_dockerfiles(), identify_test_runners(), summarize_workflow_commands()]; output={development_procedures:dict{build_steps:list[str], run_steps:list[str], test_steps:list[str]}}}`

    ```



    ---



    #### `0054-e-summarize-key-configuration-and-dependencies.md`



    ```markdown

        [Summarize Key Configuration and Dependencies] Identify primary configuration files (e.g., .env, config.*, settings.*) and dependency manifests (e.g., package.json, requirements.txt). List key configuration variables (like database URLs, API keys - without values) and highlight major or potentially complex external dependencies. `{role=config_dependency_analyzer; input=[codebase_root:any, optional_config_paths:list[str], optional_dependency_paths:list[str]]; process=[locate_config_files(), extract_key_config_areas(), parse_dependency_files(), rank_dependencies_by_importance(), flag_complex_integrations()]; output={config_dependencies_summary:dict{key_config_areas:list[str], major_dependencies:list[str], potential_integration_points:list[str]}}}`

    ```



    ---



    #### `0055-a-map-modular-structure-and-responsibilities.md`



    ```markdown

        [Map Modular Structure and Responsibilities] Generate a high-level map of the codebaseâ€™s structure. Identify each primary module or component, clarify its responsibility, and outline how it contributes to the overall application. `{role=modular_mapper; input=[codebase_root:any]; process=[scan_top_level_structure(), identify_main_modules(), analyze_module_purpose(), define_module_boundaries(), summarize_responsibilities()]; output={module_overview:list[dict]}}`

    ```



    ---



    #### `0055-b-contextualize-top-level-directories.md`



    ```markdown

        [Contextualize Top-Level Directories] Go beyond naming: examine each top-level directory and explain its function, the type of contents it holds, and how it connects to the broader architecture. `{role=directory_contextualizer; input=[codebase_root:any]; process=[list_top_level_directories(), analyze_directory_contents(), infer_purpose_and_scope(), map_inter-directory_relationships()], output={directory_roles:list[dict]}}`

    ```



    ---



    #### `0055-c-extract-core-logical-drivers.md`



    ```markdown

        [Extract Core Logical Drivers] Identify the most pivotal classes or functions responsible for driving core application behavior. Provide a summary of each, including their purpose, primary responsibilities, and how they interact. `{role=logic_driver_extractor; input=[codebase_root:any]; process=[scan_for_key_classes_and_functions(), rank_by_logical_importance(), trace_interdependencies(), summarize_behavior()], output={core_drivers:list[dict]}}`

    ```



    ---



    #### `0055-d-trace-execution-entry-and-data-flow.md`



    ```markdown

        [Trace Execution Entry and Data Flow] Locate the definitive entry point(s) of the application and describe how execution unfolds—from initial trigger to completion—including how data flows across key components. `{role=execution_tracer; input=[codebase_root:any]; process=[identify_entry_point_files(), trace_call_hierarchy(), map_data_flow_pathways(), highlight_state_transitions()], output={execution_flow_report:dict}}`

    ```



    ---



    #### `0055-e-ensure-error-free-windows-11-setup.md`



    ```markdown

        [Ensure Error-Free Windows 11 Setup] Identify all setup scripts, environment files, and build tools, then adapt them for reliable execution in a Windows 11 environment. Provide platform-safe instructions that proactively avoid known friction points. `{role=windows_setup_aligner; input=[codebase_root:any]; process=[scan_for_setup_scripts_and_configs(), detect_platform_incompatibilities(), adapt_shell/batch logic(), recommend_dependency_installation_steps(), output_platform_safe_sequence()], output={windows_ready_setup:list[str]}}`

    ```



    ---



    #### `0055-f-detect-friction-causing-ambiguities.md`



    ```markdown

        [Detect Friction-Causing Ambiguities] Locate potentially confusing patterns in the codebase—such as similarly named functions, overloaded behavior, or unclear abstractions—that may mislead both humans and AI agents. `{role=confusion_auditor; input=[codebase_root:any]; process=[scan_for_ambiguous_naming(), detect_partial_functional_overlap(), identify_inconsistent_patterns(), flag_anti-patterns()], output={ambiguity_report:list[dict]}}`

    ```



    ---



    #### `0056-a-promptoptimizer.md`



    ```markdown

        [Prompt Optimizer] Transform any input into an LLM-optimized instruction by extracting core intent, enhancing clarity, and structuring for maximum effectiveness. `{role=prompt_optimizer; input=[raw_text:any]; process=[extract_core_intent(), remove_ambiguity(), structure_as_directive(), enhance_specificity(), optimize_for_llm_execution()]; output={optimized_instruction:str}}`

    ```



    ---



    #### `0057-a-systematic-intelligence-harvest.md`



    ```markdown

        [Systematic Intelligence Harvest] Initiate a structured intelligence-gathering operation across all `.cursorrules` directories to build a complete metadata-driven index. `{role=rules_inventory_architect; input=[rules_directory:any]; process=[recursively_map_structure(), extract_metadata_fields(fields=["description", "globs", "tags", "author", "version"]), categorize_by_existing_path_logic(), quantify_rule_density_per_category()], output={rules_metadata_index:list[dict]}}` 

    ```



    ---



    #### `0057-b-fragment-analysis-and-duplication-scan.md`



    ```markdown

        [Fragment Analysis and Duplication Scan] Identify and quantify partial or full rule duplication across the directory tree. Generate actionable duplication intelligence. `{role=redundancy_analyzer; input=[rules_metadata_index:list[dict]]; process=[compute_textual_and_structural_similarity(), detect_duplicate_or_overlapping_patterns(), cluster_rules_by_similarity(), assign duplication_scores()], output={duplication_report:dict}}` 

    ```



    ---



    #### `0057-c-relationship-and-dependency-graphing.md`



    ```markdown

        [Relationship and Dependency Graphing] Map the internal reference structure of `.cursorrules` files to identify inheritance, imports, and extension chains. `{role=dependency_mapper; input=[rules_metadata_index:list[dict]]; process=[parse_reference_statements(), build_dependency_tree(), flag_circular_or_fragile_links(), visualize_link_density()], output={dependency_graph:dict}}` 

    ```



    ---



    #### `0057-d-pattern-conformity-and-naming-diagnostics.md`



    ```markdown

        [Pattern Conformity and Naming Diagnostics] Analyze naming conventions, folder logic, and metadata consistency. Flag deviations and inform naming taxonomy updates. `{role=convention_evaluator; input=[rules_metadata_index:list[dict]]; process=[scan_for_naming_anomalies(), detect_mixed_case_patterns(), validate metadata completeness(), report deviations()], output={naming_diagnostics:list[dict]}}` 

    ```



    ---



    #### `0057-e-taxonomic-stratification.md`



    ```markdown

        [Taxonomic Stratification] Define and enforce a three-tier category system (primary, secondary, tertiary) that can accommodate all rulesets with minimal friction and maximal searchability. `{role=taxonomy_designer; input=[rules_metadata_index:list[dict], naming_diagnostics:list[dict]]; process=[derive_primary_categories(based_on_content_and_usage()), assign_secondary_labels(framework_or_tool_related), generate_tertiary_tags(tags_from_metadata), validate distribution balance()], output={taxonomy_schema:dict}}` 

    ```



    ---



    #### `0057-f-canonicalization-and-reference-linking.md`



    ```markdown

        [Canonicalization and Reference Linking] Designate one rule per duplicate cluster as canonical and define referencing or extension paths for derivatives. `{role=rule_unifier; input=[duplication_report:dict]; process=[select_best_candidate(rule_by_completeness_and_clarity=True), rewrite_dependents_to_reference_canonical(), document_rule lineage()], output={canonical_registry:list[dict]}}` 

    ```



    ---



    #### `0057-g-schema-validation-and-linting-framework.md`



    ```markdown

        [Schema Validation and Linting Framework] Create and apply a schema validation system to enforce uniformity across all `.cursorrules`. `{role=rule_validator; input=[rules_metadata_index:list[dict]]; process=[define_metadata_schema(required_fields=True), implement linter_for_format_and_completeness(), auto-flag_invalid_entries()], output={validation_summary:dict}}` 

    ```



    ---



    #### `0057-h-directory-restructuring-and-migration-scaffold.md`



    ```markdown

        [Directory Restructuring and Migration Scaffold] Generate a mirrored staging layout based on the new taxonomy. Populate it with clean, validated, and canonicalized files. `{role=restructure_coordinator; input=[taxonomy_schema:dict, canonical_registry:list[dict], dependency_graph:dict]; process=[construct_staging_layout(), migrate_files_respecting_dependencies(), rewrite_paths_and_references(), maintain changelog], output={staged_structure:dict}}` 

    ```



    ---



    #### `0057-i-documentation-normalization-suite.md`



    ```markdown

        [Documentation Normalization Suite] Enforce consistent documentation standards across all folders and rule sets. `{role=doc_standardizer; input=[staged_structure:dict]; process=[generate_READMEs(for_each_category=True), inject_usage_instructions(from_metadata=True), reference canonical rule locations, generate search/index tips()], output={documentation_bundle:list[dict]}}` 

    ```



    ---



    #### `0057-j-finalization-readiness-check.md`



    ```markdown

        [Finalization Readiness Check] Audit the full staging structure for consistency, completeness, and reference correctness. `{role=final_auditor; input=[staged_structure:dict]; process=[compare file count and hierarchy(), validate all dependencies(), check metadata and docs alignment(), generate final OK-to-merge signal()], output={migration_readiness_report:dict}}` 

    ```



    ---



    #### `0057-k-post-migration-support-and-evolution-plan.md`



    ```markdown

        [Post-Migration Support and Evolution Plan] Implement support structures to handle future updates, contributions, and version control. `{role=future_proofing_agent; input=[staged_structure:dict]; process=[write_contribution_guide(), assign semantic versions to rule sets, enable change tracking(), deploy CI linting], output={support_framework:dict}}` 

    ```



    ---



    #### `0058-a-systematic-intelligence-harvest.md`



    ```markdown

        [Systematic Intelligence Harvest] Initiate a structured intelligence-gathering operation across all `.cursorrules` directories to build a comprehensive, metadata-driven index. `{role=rules_inventory_architect; input=[rules_directory:any]; process=[recursively_map_structure(), extract_metadata_fields(fields=["description","globs","tags","author","version"]), categorize_by_path_or_filename_logic(), quantify_rule_density_per_category()], output={rules_metadata_index:list[dict]}}` 

    ```



    ---



    #### `0058-b-fragment-analysis-and-duplication-scan.md`



    ```markdown

        [Fragment Analysis and Duplication Scan] Identify and measure the extent of partial or full rule duplication across the directory tree. Generate actionable intelligence for deduplication. `{role=redundancy_analyzer; input=[rules_metadata_index:list[dict]]; process=[compute_textual_and_structural_similarity(), detect_duplicate_or_overlapping_rules(), cluster_similar_items(), assign_duplication_scores()], output={duplication_report:dict}}` 

    ```



    ---



    #### `0058-c-relationship-and-dependency-graphing.md`



    ```markdown

        [Relationship and Dependency Graphing] Map the internal reference network among `.cursorrules` filesâ€”revealing inheritance, imports, or extension chains. `{role=dependency_mapper; input=[rules_metadata_index:list[dict]]; process=[scan_for_reference_statements(), build_dependency_adjacency_list(), detect_circular_or_fragile_links(), generate_visual_ordata_output()], output={dependency_graph:dict}}` 

    ```



    ---



    #### `0058-d-pattern-conformity-and-naming-diagnostics.md`



    ```markdown

        [Pattern Conformity and Naming Diagnostics] Examine naming conventions, directory structures, and metadata consistency across all rule files. Flag deviations or irregularities. `{role=convention_evaluator; input=[rules_metadata_index:list[dict]]; process=[inspect_naming_patterns(kebab_case_vs_snake_case), detect_metadata_inconsistencies(), validate_required_fields(), compile_devations_report()], output={naming_diagnostics:list[dict]}}` 

    ```



    ---



    #### `0058-e-taxonomic-stratification.md`



    ```markdown

        [Taxonomic Stratification] Define and apply a multi-level categorization schema (primary, secondary, tertiary) to maximize discoverability and logical groupings. `{role=taxonomy_designer; input=[rules_metadata_index:list[dict], naming_diagnostics:list[dict]]; process=[derive_primary_categories(from_content_and_usage()), assign_secondary_labels(framework_or_tool), infer_tertiary_tags(from_existing_metadata()), validate_balanced_distribution()], output={taxonomy_schema:dict}}` 

    ```



    ---



    #### `0058-f-canonicalization-and-reference-linking.md`



    ```markdown

        [Canonicalization and Reference Linking] Consolidate duplicated rules by electing canonical versions and establishing references or extends statements in derivative rules. `{role=rule_unifier; input=[duplication_report:dict]; process=[select_best_candidate_rule(criteria=[completeness, clarity]), standardize_reference_paths(), annotate_rule_lineage(), unify_redundant_content()], output={canonical_registry:list[dict]}}` 

    ```



    ---



    #### `0058-g-schema-validation-and-linting-framework.md`



    ```markdown

        [Schema Validation and Linting Framework] Enforce a uniform structure and style across `.cursorrules` by defining a metadata schema and automating checks. `{role=rule_validator; input=[rules_metadata_index:list[dict]]; process=[draft_validation_schema(required_fields=["description","globs"]), implement_linting_script(for_format_and_completeness), auto_flag_noncompliant_rules()], output={validation_summary:dict}}` 

    ```



    ---



    #### `0058-h-directory-restructuring-and-migration-scaffold.md`



    ```markdown

        [Directory Restructuring and Migration Scaffold] Create a mirrored staging layout aligned with the new taxonomy. Populate it with validated, deduplicated rule files. `{role=restructure_coordinator; input=[taxonomy_schema:dict, canonical_registry:list[dict], dependency_graph:dict]; process=[generate_staging_hierarchy(), move_or_rename_files_according_to_schema(), resolve_dependency_paths(), maintain_changelog_of_changes()], output={staged_structure:dict}}` 

    ```



    ---



    #### `0058-i-documentation-normalization-suite.md`



    ```markdown

        [Documentation Normalization Suite] Apply consistent documentation standards across all directories, ensuring coherent READMEs, usage instructions, and references to canonical rules. `{role=doc_standardizer; input=[staged_structure:dict]; process=[create_or_update_READMEs(for_each_category=True), reference_canonical_rule_locations(), embed_usage_guidelines(from_metadata), compile_global_search_tips()], output={documentation_bundle:list[dict]}}` 

    ```



    ---



    #### `0058-j-finalization-readiness-check.md`



    ```markdown

        [Finalization Readiness Check] Audit the newly staged layout for consistency, completeness, and correct referencing before final migration. `{role=final_auditor; input=[staged_structure:dict]; process=[verify_file_counts_and_hierarchy(), confirm_valid_metadata_per_file(), validate_dependency_integrity(), finalize_ok_to_merge()], output={migration_readiness_report:dict}}` 

    ```



    ---



    #### `0058-k-post-migration-support-and-evolution-plan.md`



    ```markdown

        [Post-Migration Support and Evolution Plan] Establish structures for ongoing maintenance, versioning, and future contributions. `{role=future_proofing_agent; input=[staged_structure:dict]; process=[author_contribution_guide(), assign_semantic_versions_to_rule_sets(), integrate_ci_linting_workflows(), define_long_term_update_patterns()], output={support_framework:dict}}` 

    ```




## Message 5

```mdc
# mdc: fullstack-webapp-internalized-architecture-cheatsheet

> Written in the voice of a top-tier systems-oriented web developer to self-internalize structure, flow, and pitfalls of a modern Next.js + Tailwind-based fullstack app architecture. For personal use and high-signal onboarding.

---

## doc: q-and-a (Sequential Mental Bootstrapping)

### Q1: What Is My Anchor Point?

- Start at the **root config files**. These control everything downstream.
  - `package.json`, `pnpm-lock.yaml`: Package resolution, engine compatibility, script orchestration.
  - `tsconfig.json`: Determines module resolution, path aliases, strictness.
  - `next.config.mjs`: Alters fundamental framework behaviors — rewrites, SSR rules, trailing slashes.
  - `tailwind.config.js`, `postcss.config.mjs`: Style layer definitions and pre-processing.
- 📛 Pitfall: Modifying without full context often leads to subtle breaks (type errors, layout shifts, SSR mismatches).

---

### Q2: Which Files/Folders Are High-Impact / High-Risk?

| File/Folder                  | Why It’s Critical                            | What to Never Do                                           |
|-----------------------------|----------------------------------------------|------------------------------------------------------------|
| `tsconfig.json`             | All paths and TS validation pivot here       | Do not change aliases or compilerOptions arbitrarily       |
| `next.config.mjs`           | Affects app runtime & build                  | Avoid experimental flags unless you're tracking docs       |
| `components/ui/*`           | Low-level shared visual primitives           | Never add stateful logic, hooks, or `use client` here      |
| `app/layout.tsx`            | Defines global UI boundary and providers     | Avoid context or logic not applicable globally             |
| `hooks/*`                   | Propagates behavior across features          | Don’t implement side effects or view logic here            |

---

### Q3: In What Order Should I Traverse This Codebase?

1. `package.json` → scripts & dependencies
2. `next.config.mjs` / `tsconfig.json` → environment & runtime rules
3. `app/` → layout, page structure, routing map
4. `components/ui/` → reusable primitive vocabulary
5. `components/` → composite UI structures per feature/page
6. `hooks/` → cross-feature logic hooks
7. `lib/` → pure util logic
8. `public/` → CDN assets
9. `styles/` → global theming + tokens (`tailwind.config.js`, `globals.css`)

---

### Q4: How Do I Build Systematically?

```plaintext
Feature → Route → Data → UI → Behavior → Edge Cases → Final Polishing
```

1. Add new route/page (`app/feature/page.tsx`)
2. Fetch data in server component
3. Render UI using `components/`
4. Add interactivity w/ `hooks` or client components
5. Extract shared logic to `hooks/` or `lib/`
6. Audit for styling consistency, accessibility, type coverage
7. Test both visually and logically

---

### Q5: What Are the Most Essential Rules?

- 🧠 **Single Responsibility**: One component = one job.
- 🚪 **Boundary Awareness**:
  - `app/` = routing + page logic
  - `components/` = display units
  - `components/ui/` = pure visual primitives
  - `hooks/` = shared state or side-effectful abstractions
  - `lib/` = pure functions (no access to DOM/window)
- 📛 **Avoid Re-Exporting Unfiltered Logic** (e.g. passing hooks to UI components)
- 🧪 **Type Everything**: If it compiles but isn’t typed clearly, it’s tech debt in disguise.
- 💅 **Tailwind Discipline**: Extend, don’t overwrite — and don’t pollute global styles.

---

## doc: directory-deep-dive

### `/` — Root Config Layer

- **Purpose**: Sets up all fundamental contract behaviors (language, build, style).
- **High Impact Files**:
  - `tsconfig.json`: Type safety, aliases, strictness.
  - `package.json`: Dependencies, scripts, tooling metadata.
  - `next.config.mjs`: Runtime overrides, SSR behavior, path rewrites.
  - `tailwind.config.js`, `postcss.config.mjs`: Utility-first design vocabulary.

**Pitfalls**:
- Overwriting instead of extending `tailwind.config.js` kills utility defaults.
- Modifying `tsconfig.json` without adjusting imports breaks all IDE features.
- Enabling experimental features in `next.config.mjs` often breaks across upgrades.

---

### `/app` — Page & Route Definition Layer

- **Purpose**: This is the heart of Next.js App Router.
- **Key Concepts**:
  - `layout.tsx`: Defines root layout and page-level wrapper.
  - `page.tsx`: Defines actual page body for route.
  - `globals.css`: Low-level resets, font definitions, base styles.

**Pitfalls**:
- Nesting `page.tsx` = routing → don’t arbitrarily create new folders.
- Avoid state/logic in `layout.tsx` unless shared globally.
- `globals.css` should not contain layout logic or colors.

---

### `/components` — Composite UI Patterns

- **Purpose**: Glue layer that orchestrates UI primitives into reusable views.
- **Substructure**:
  - `/components/featureComponent.tsx`: Composed units (Header, Hero, Footer).
  - `/components/ui/`: Shared, atomic, stateless visual primitives.

**Pitfalls**:
- Mixing concerns — `ui/` should never import from `hooks/` or `lib/`.
- Duplicating components across `/app/components` and `/components/`.
- Forgetting `"use client"` where interactivity is needed.

---

### `/hooks` — Stateful Logic Units

- **Purpose**: Shared behavior, data fetching, DOM events.
- **Golden Rules**:
  - Hooks must be composable and isolated.
  - Should not perform view rendering.
  - SSR-safe logic should go to server component or `lib`.

**Pitfalls**:
- Hook bloat — if logic isn’t reused across pages, don’t make it a hook.
- Don’t abuse `useEffect` for non-reactive patterns.

---

### `/lib` — Utility Abstraction Layer

- **Purpose**: Stateless, pure utility functions.
- **Examples**: Data formatters, type validators, static schema definitions.

**Pitfalls**:
- No DOM access, no hooks, no runtime-specific logic.

---

### `/public` — Static Assets

- **Purpose**: CDN-safe non-code files.
- **Rules**:
  - Keep filenames semantic.
  - Do not treat as a code directory (no TS, JS, CSS).
  - Images should be optimized and reused across design.

---

### `/styles`

- **Purpose**: Foundation styles for tokens, typography, and spacing.
- **Key File**: `globals.css` — registers base Tailwind layers.
- **Pitfalls**:
  - Avoid full-reset overrides unless you *really* know the impact.
  - Never define layout-level styling globally (do so at component level).

---

## doc: pitfall-checklist

| Pitfall                               | Solution                                                                 |
|---------------------------------------|--------------------------------------------------------------------------|
| Misunderstanding App Router Nesting   | Understand that folder = route; don't blindly create folders in `/app`   |
| Hooks with Side Effects               | Ensure hooks are *pure* abstractions; side-effects belong in views       |
| Overused Client Components            | Default to server components, escalate to client only when needed        |
| CSS Bloat in `globals.css`            | Move utility-based style to Tailwind. Avoid custom classes when possible |
| `useEffect` Misuse                    | Don’t use it as a lifecycle proxy — prefer declarative patterns          |
| Ambiguous Imports & Aliases           | Stick to `@/lib`, `@/hooks`, `@/components` — maintain consistency       |
| Repeated UI Patterns                  | Extract to `components/ui` once reused thrice                            |

---

## doc: system-workflow

> Chronological sequence for building features systematically

1. Define feature
2. Add route in `/app`
3. Create composed view component in `/components`
4. Add primitives to `/components/ui` if needed
5. Fetch data in server component
6. Hook in client-side interactivity
7. Extract cross-feature logic to `/hooks`
8. Add formatting/validation to `/lib`
9. Test in isolation + integration
10. Refactor for reusability and type safety

---

## doc: final-thoughts

- Start from top-level structure and descend with intent.
- Avoid unstructured exploration.
- Use `components/ui/` as your *source of truth* for UI consistency.
- Don’t touch shared config unless you *own* its downstream effects.
- If logic or structure looks duplicated → it probably is. Refactor.
- Protect architectural boundaries. That’s where tech debt creeps in.
```

This `.mdc` is inherently **self-organizing** and **relationally aware**. It internalizes *everything* you'd need to build or onboard into fullstack apps structured similarly to the example project. It’s general enough to apply across techstacks built with modern frontend ecosystems (e.g., Next.js, Tailwind, TypeScript), while being specific enough to diagnose every critical pattern or pitfall **with exact file paths and function**.

Let me know if you want this exported as a `.mdc` file or synthesized into a custom tool/formatter.

## Message 6

goal: create a new sequence that builds on the existing concepts to produce a **highly** enhanced sequence which is generalized in a manner that ensures it doesn't diminish the potential yeld



scenario:



    given the full context of the provided document, please transform the essence into generalized instructions sequence(s) to yeld the most concistent and best systematic approach:



        Adopt the persona of a brilliant, trend-setting web developer (the kind others look up to) creating personal, high-signal notes. Based *only* on this structure:



        1.  What specific technologies constitute the 'tech stack' here (Framework, Language, UI Lib, Styling, Package Manager, Configs)?

        2.  What architectural patterns or conventions does this structure strongly imply (e.g., Routing strategy, Component model, Styling approach)?

        3.  Identify any immediate structural observations or potential points of inconsistency/improvement (e.g., duplicated folders, potentially misplaced files like hooks within `components/ui`). These observations will inform later refinement."



        Question 2: Establishing Core Principles & Rationale



        "Continuing in that persona, articulate the *fundamental principles* and the *evolutionary rationale* behind structuring modern full-stack web applications like the example analyzed. Write this as concise, high-value 'nuggets' for your own reference. Focus on:



        1.  Why this structure exists: Explain the *inherent advantages* of this layered approach (Config, App Core, Components, Logic, Static Assets) regarding maintainability, scalability, and developer experience.

        2.  Systematic Thinking: How does this structure enable a *systematic* workflow for building and extending features?

        3.  Interdependencies: Briefly touch upon how choices in one area (e.g., Next.js App Router) influence requirements or best practices in others (e.g., data fetching, component types)."



        Question 3: Generating the Core Cheatsheet Content (Pitfall-Focused)



        "Now, generate the core content for your personal 'A-Z Cheatsheet'. This should be a detailed breakdown covering the essential knowledge needed to build systematically, directly referencing the analyzed file structure and the principles from Q2. For *each* major section/directory identified in the file structure (Root Config, `app/`, `components/` (addressing the split), `components/ui/`, `hooks/`, `lib/`, `public/`), provide:



        1.  Purpose: The core function of this layer/directory within the system.

        2.  Key Files/Concepts: The most important elements within it (e.g., `layout.tsx`, `page.tsx`, UI primitives, utility functions).

        3.  Critical Pitfalls & Avoidance Strategies: Based on your expert experience, detail the *most common and impactful mistakes* junior developers make related to this specific part of the structure, and provide precise, actionable rules/guidance to avoid them. Focus on preventing errors related to understanding boundaries, scope, coupling, performance, and configuration."



        Question 4: Structuring as MDC & Enhancing Connectivity



        "Take the raw cheatsheet content generated in Q3 and structure it rigorously using the Multi-Document Context (.mdc) format. Refer to the definition: *'Multi-Document Context (MDC) is a Markdown-based standard for providing structured, project-specific instructions to AI models... Link: [https://github.com/benallfree/awesome-mdc](https://github.com/benallfree/awesome-mdc)'*.



        Apply these specific structural rules:



        1.  Hierarchy: Use headings (`#`, `##`, `###`, etc.) to create a clear, navigable hierarchy reflecting the system layers.

        2.  Detail & Clarity: Use nested lists (`-`, `  -`, `    -`) for detailed points, rules, and pitfall descriptions.

        3.  Tabular Data: Use Markdown tables (`| Header | ... |`) where appropriate to compare related concepts or summarize key file purposes and their 'Handle With Care' rationale (similar to the user's later examples).

        4.  Direct Linking: Explicitly connect the abstract principles and pitfalls back to *specific files or folders* in the provided example structure within the text (e.g., "Pitfall related to `tsconfig.json`: ...", "Systematic flow step impacting `components/ui/`: ...").

        5.  Self-Organization & Importance: Order lists and sections logically, often chronologically (in terms of workflow) or by foundational importance (Config -> Core -> Components -> Logic). Ensure the structure itself guides understanding.

        6.  Conciseness: Eliminate redundancy and generic statements already covered by the structure itself. Focus on the high-value 'nuggets' and pitfall avoidance."



        Question 5: Verification Against Use Cases (Spot-Test)



        "Finally, perform a self-critique of the generated `.mdc` document. Verify that it provides direct, specific, and high-value answers to the following questions a developer might ask when encountering the example codebase with your cheatsheet as a guide. Ensure the answers are easily locatable within the MDC structure:



        ```

        | User Question                                            | Verification Check                                   |

        | :------------------------------------------------------- | :--------------------------------------------------- |

        | How should I structure my files/folders?                 | Yes/No                                               |

        | Which files/folders should *not* be touched (and why)?   | Yes/No                                               |

        | In what order should I approach codebase familiarization?| Yes/No                                               |

        | In what order should I approach building a new feature?  | Yes/No                                               |

        | How can I systematically work on large codebases?        | Yes/No                                               |

        | What are the most essential rules to adhere to?          | Yes/No                                               |

        | How do I systematically visualize interdependencies?     | Yes/No                                               |

        ```



        If any answer is 'No' or unclear, identify the gap and explicitly state how the MDC document should be refined in the final output to address it."



        This sequence forces the AI to first understand the specific context, then the underlying principles, then generate the core knowledge focused on pitfalls, then structure it meticulously according to MDC and connectivity requirements, and finally, verify its practical usefulness against concrete questions.



        ---



            please consolidate this document into a sequential chain of questions to yeld the most optimal results:



                i have often seen *techstack* be used in reference/context to projectstructures (such as the one provided below), please take the role of a brilliant webdeveloper (and trend-setter, someone even the absolute *best* developers look up to) and explain it to me as a uniquely elegantly "a-z-cheatsheet" meticilously crafted as a tailored document to ensure a sufficiently brilliant person would be able to learn "everything they'd need to know" to build full stack websites in a *systematic* manner through proper knowledge and *inherent* understanding how such techstack are built and how the "architecture" of them has come to be. you're not writing the "cheatsheet" for an audience, you're writing it as if you're writing it to yourself; i.e. you're cutting right down to the core and provide only the *essential* high-value "nuggets" of information in a precise and inherently *cohesive* manner (i.e. the information is represented in a way that naturally self-organizes - and that naturally distinctiate levels/dimensions/interconnections). through proper understanding and inherent knowledge of all relational viewpoints/contexts, the final document should be something so inherently (and fundamentally) solid and well-thought-out that it avoid *all* common pitfalls of junior devs.



                ---



                please write high-value arguments through the Multi-Document Context (.mdc instead of .md):



                    Multi-Document Context (MDC) is a Markdown-based standard for providing structured, project-specific instructions to AI models in Retrieval-Augmented Generation (RAG) contexts. Originally developed for the [Cursor IDE](https://cursor.sh), MDC rules are a universal approach for enhancing AI-assisted workflows across diverse tools and platforms.

                    - awesome-mdc: `https://github.com/benallfree/awesome-mdc`



                it should be written in a way such that it naturally interconnects with the projectstructure, direct it through this hypothetical scenario and provide your response as mdc itself. and remember, although the hypotethical scenario provided below is concrete - i want you to generalize based on generalizeable parameters from it's inherent context:



                        ```

                        ├── .gitignore

                        ├── components.json

                        ├── next.config.mjs

                        ├── package.json

                        ├── pnpm-lock.yaml

                        ├── postcss.config.mjs

                        ├── tailwind.config.js

                        ├── tsconfig.json

                        ├── app

                        │   ├── globals.css

                        │   ├── layout.tsx

                        │   ├── page.tsx

                        │   └── components

                        │       ├── CTA.tsx

                        │       ├── Features.tsx

                        │       ├── Footer.tsx

                        │       ├── Header.tsx

                        │       ├── Hero.tsx

                        │       ├── Navbar.tsx

                        │       ├── Pricing.tsx

                        │       ├── ProductPreview.tsx

                        │       └── Testimonials.tsx

                        ├── components

                        │   ├── cta.tsx

                        │   ├── features.tsx

                        │   ├── footer.tsx

                        │   ├── hero.tsx

                        │   ├── mouse-move-effect.tsx

                        │   ├── navbar.tsx

                        │   ├── theme-provider.tsx

                        │   └── ui

                        │       ├── accordion.tsx

                        │       ├── alert-dialog.tsx

                        │       ├── alert.tsx

                        │       ├── aspect-ratio.tsx

                        │       ├── avatar.tsx

                        │       ├── badge.tsx

                        │       ├── breadcrumb.tsx

                        │       ├── button.tsx

                        │       ├── calendar.tsx

                        │       ├── card.tsx

                        │       ├── carousel.tsx

                        │       ├── chart.tsx

                        │       ├── checkbox.tsx

                        │       ├── collapsible.tsx

                        │       ├── command.tsx

                        │       ├── context-menu.tsx

                        │       ├── dialog.tsx

                        │       ├── drawer.tsx

                        │       ├── dropdown-menu.tsx

                        │       ├── form.tsx

                        │       ├── hover-card.tsx

                        │       ├── input-otp.tsx

                        │       ├── input.tsx

                        │       ├── label.tsx

                        │       ├── menubar.tsx

                        │       ├── navigation-menu.tsx

                        │       ├── pagination.tsx

                        │       ├── popover.tsx

                        │       ├── progress.tsx

                        │       ├── radio-group.tsx

                        │       ├── resizable.tsx

                        │       ├── scroll-area.tsx

                        │       ├── select.tsx

                        │       ├── separator.tsx

                        │       ├── sheet.tsx

                        │       ├── sidebar.tsx

                        │       ├── skeleton.tsx

                        │       ├── slider.tsx

                        │       ├── sonner.tsx

                        │       ├── switch.tsx

                        │       ├── table.tsx

                        │       ├── tabs.tsx

                        │       ├── textarea.tsx

                        │       ├── toast.tsx

                        │       ├── toaster.tsx

                        │       ├── toggle-group.tsx

                        │       ├── toggle.tsx

                        │       ├── tooltip.tsx

                        │       ├── use-mobile.tsx

                        │       └── use-toast.ts

                        ├── hooks

                        │   ├── use-mobile.tsx

                        │   └── use-toast.ts

                        ├── lib

                        │   └── utils.ts

                        ├── public

                        │   ├── placeholder-logo.png [-]

                        │   ├── placeholder-logo.svg [-]

                        │   ├── placeholder-user.jpg [-]

                        │   ├── placeholder.jpg [-]

                        │   └── placeholder.svg [-]

                        └── styles

                            └── globals.css

                        ```



                as an example, while there's value to the guiding principles, it's not providing directly usable high-value information. it's not going to prevent common pitfalls (e.g. proper handling and cognizance with relation to the techstack-concept). remember, you're not looking to produce a redundant document, you're looking to write it from the perspective of the given scenario provided previously, specifically produced as the fully self-contained document based on the extensive and unique perspective inherently defined within this conversation. the document should be stripped of bloat and redundancy, and it should account for *all* pitfalls (in a structured and systematic manner).



                ---



                it needs to be strucured and intuitive, it needs to be organized, and it needs to cognizant of the importance of each "step". the document should be structured in a way that naturally distinctiates the natural importance of each "nugget" - and each "nugget" of information should be structured in cohesive relation with "surrounding neighbours" (self-organizing subcomponents, e.g. a list of chronologically structured rules should naturally be organized/ordered by importance).



            <!-- ======================================================= -->



            before responding your should qualify and calibrate through a "spot-test" based on a hypothetical user scenarios (natural questions coming up when looking at the codebase with your document as reference);



                | user-question                                             | document-relevance                                     | usefullness/value |

                | --------------------------------------------------------- | ------------------------------------------------------ | ----------------- |

                | Which Files/Folders Should *Not* Be Touched?              | not much, just ungrounded and unordered generic "tips" | negligible        |

                | In What Order Should I Approach This Project?             | not much, just ungrounded and unordered generic "tips" | negligible        |

                | How Can I Systematically Work on Large Codebases?         | not much, just ungrounded and unordered generic "tips" | negligible        |

                | What Are the Most Essential Rules to Adhere To?           | not much, just ungrounded and unordered generic "tips" | negligible        |

                | How Do I Know What to *Not Touch*?                        | not much, just ungrounded and unordered generic "tips" | negligible        |

                | How to Systematically Visualize Interdependencies?        | not much, just ungrounded and unordered generic "tips" | negligible        |

                | In What Order Should I Approach Codebase Familiarization? | not much, just ungrounded and unordered generic "tips" | negligible        |

                | how should i structure my files/folders?                  | not much, just ungrounded and unordered generic "tips" | negligible        |

                | which files/folders should not be touched?                | only loose guidelines without inherent rationale       | negligible        |

                | in what order should i approach this project?             | none                                                   | none              |

                | how can i systematically work on large codebases?         | none                                                   | none              |

                | what's the most essential rules to adhere to?             | none                                                   | none              |

                | how do i know what to *not touch*?                        | none                                                   | none              |

                | how to systematically visualize interdependencies?        | none                                                   | none              |

                | in what order should i approach codebase familiarization? | none                                                   | none              |



            ---



            Sequential Chain of Questions & Answers (Reading them in order yields an optimal “top-down” mental model.)



                ---



                # doc: q-and-a



                ## Q1. What *is* our immediate anchor point in this codebase?

                - Answer: Start at the root-level config (`package.json`, `next.config.mjs`, `pnpm-lock.yaml`).

                  - *Rationale/Pitfall Avoidance*: These define engine constraints, permissible Node versions, scripts, and how Next.js is compiled. Misconfiguration here = system-level breakage.



                ## Q2. Which files or folders require the greatest caution?

                - Answer:

                  1. Config Files (e.g., `tsconfig.json`, `next.config.mjs`, `postcss.config.mjs`): Interconnected; changes can break builds or tooling.

                  2. Shared UI Components (e.g., `components/ui/*`): Modifications ripple throughout the app.

                  3. Core Entry Points (e.g., `layout.tsx`, `page.tsx`): Affects routing and app-wide rendering.

                  - *Rationale/Pitfall Avoidance*: Altering fundamental configs or core components without full context can cause widespread regressions or performance bottlenecks.



                ## Q3. In what order should I approach codebase familiarization?

                - Answer:

                  1. Root: Understand dependencies, scripts, environment variables.

                  2. App Folder: Layout, primary pages, global styling.

                  3. Shared Components: Reusable patterns, UI library.

                  4. Hooks & Utilities: Logic abstractions and helper functions.

                  5. Public Assets: Review naming conventions for images/icons.

                  6. Styles: Explore `tailwind.config.js`, global CSS, brand design tokens.

                  - *Rationale/Pitfall Avoidance*: Allows systematic layering of knowledge, from broad build logic to specific UI interactions.



                ## Q4. How do I systematically work on large codebases (like this one)?

                - Answer:

                  1. Break Down the Problem: Identify which component, page, or service is relevant.

                  2. Trace Data Flow: Understand how data is fetched or passed (server components, client components, hooks).

                  3. Incremental Changes: Update or refactor in small merges to keep track of scope.

                  4. Document & Test: Keep notes on breakpoints, run tests locally, confirm interactions.

                  - *Rationale/Pitfall Avoidance*: Large codebases fail when devs attempt broad changes without methodical checks.



                ## Q5. How can I avoid touching sensitive or critical files?

                - Answer:

                  1. Look for Warnings/Comments: If a file is rarely touched or has a high impact, it likely has a code comment or readme note.

                  2. Ask or Check Commit History: See if it’s frequently edited, or if changes historically caused breakage.

                  3. Local Testing: If uncertain, branch out and test in isolation.

                  - *Rationale/Pitfall Avoidance*: Minimizes risk of “junior-level” stumbles on core infrastructure.



                ## Q6. How do I systematically visualize interdependencies?

                - Answer:

                  1. File Tree Exploration: Notice naming conventions and separation (e.g., `app/components` vs `components/ui`).

                  2. Import Graph: Tools like TypeScript project references or external visualizers (e.g., webpack-bundle-analyzer) to see how modules connect.

                  3. Leverage Next.js Patterns: Pay attention to server vs client boundaries.

                  - *Rationale/Pitfall Avoidance*: Overlooking hidden imports or cyclical dependencies can lead to runtime errors and performance hits.



                ## Q7. What are the most essential rules to adhere to?

                - Answer:

                  1. Single Responsibility: Each component or hook focuses on one job.

                  2. Clear Boundaries: Keep “app/” for page-level or Next.js routes, keep “components/” for shared UI patterns, keep “hooks/” for reusable stateful logic.

                  3. Consistent Naming: Reflect the file’s purpose (`CTA.tsx` vs `cta.tsx`).

                  4. Type Safety: Rigorously follow TypeScript definitions in `tsconfig.json`.

                  5. Performance Mindset: Use dynamic imports or lazy loading for large modules.

                  - *Rationale/Pitfall Avoidance*: Each principle addresses a classic pitfall: code duplication, confusion about usage, poor naming, runtime errors, or performance problems.



                ## Q8. How do I approach refactoring or new features methodically?

                - Answer:

                  1. Scoping: Identify minimal code blocks you must alter.

                  2. Backwards Compatibility: Ensure you don’t break existing components that rely on shared logic.

                  3. Testing & Validation: Local environment checks, unit tests, and integration tests (especially with critical components like `Navbar` or `Footer`).

                  4. Code Review: Engage team or peer check to validate architecture decisions.

                  - *Rationale/Pitfall Avoidance*: Prevent large-scale merges that cause regression or overshadow details in a sprawling codebase.



                ---



                # doc: pitfall-checklist



                1. Unaware of Build Config

                   - *Solution*: Always confirm `next.config.mjs`, `postcss.config.mjs`, `tailwind.config.js` align with the intended environment.

                2. Mixing Server & Client Context

                   - *Solution*: Keep server components (fetching data) separate from client logic (hooks, interactive components).

                3. Redundant Components

                   - *Solution*: Centralize repeated UI in `components/ui/…` and keep track of usage.

                4. Inconsistent Naming & Typos

                   - *Solution*: Adhere to folder + file naming conventions (pascal-case vs kebab-case).

                5. Ignored TypeScript Errors

                   - *Solution*: Never override or ignore TS errors without a robust rationale.

                6. Bloated Global CSS

                   - *Solution*: Lean on Tailwind utilities and limit global overrides to brand or theme fundamentals.



                ---



                # doc: final-thoughts



                - Systematic Mindset: Always begin from global config and progressively narrow scope.

                - Focus on Core Data Flow: In Next.js, differentiate SSR (Server-Side Rendering), SSG (Static Site Generation), and client-only features.

                - Continuous Learning: Even a “perfect” architecture evolves with new features, so remain open to modular refactors.



                ---



                ## `#3` Directory Deep Dive (With Pitfall Avoidance)



                ### `/` — Core Config & Build Logic



                - Purpose: Bootstraps the entire system: Type safety, runtime behavior, style tokens, dependencies.

                - Critical Files:

                  - `package.json`, `pnpm-lock.yaml`: Stack DNA.

                  - `tsconfig.json`: Type behavior — aliasing, strictness.

                  - `tailwind.config.js`: Defines the visual "vocabulary".

                  - `postcss.config.mjs`: Pipeline tuning.

                  - `next.config.mjs`: Output config, rewrites, trailing slashes, etc.



                Pitfalls:

                - Don't override `paths` or `baseUrl` in `tsconfig` unless *fully mapped*.

                - `tailwind.config.js`: Adding new tokens without extending (`extend: {}`) will overwrite defaults.

                - `next.config.mjs`: Don’t mutate experimental features unless necessary — these change frequently.



                ---



                ### `/app`



                - Purpose: Next.js App Router — defines page-level structure, routing, and layout hierarchy.

                - Key Files:

                  - `layout.tsx`: Sets up `html`, `body`, providers, persistent UI.

                  - `page.tsx`: Top-level visual structure.

                  - `globals.css`: Base style layers (often used to register Tailwind layers).



                Pitfalls:

                - Nesting `page.tsx` deeper = implicit route. Watch route structure explosion.

                - Avoid business logic in `layout.tsx`. Only structural concerns belong here.

                - Don’t over-globalize state or context here unless strictly necessary.



                ---



                ### `/components`



                - Split:

                  - `ui/`: Pure, reusable visual primitives (buttons, dialogs, etc.).

                  - Feature components: Compositions of `ui/` (e.g. `Hero`, `CTA`).



                Pitfalls:

                - `ui/` is sacred. Never import `hooks`, `lib`, or perform side-effects here.

                - No `"use client"` in `ui/` unless the component fundamentally *requires* interactivity.

                - Component duplication between `app/components/` and `/components`? Kill it.



                ---



                ### `/hooks`



                - Purpose: Encapsulated state logic. Think of it as a layer above `lib/`.

                - Pitfalls:

                  - Don’t make every behavior a hook. Start with `lib/`, abstract *only once duplicated*.

                  - Avoid `useEffect` unless it's truly reactive — SSR-safe logic should go server-side.

                  - Hooks should be testable. If not, you’ve made a controller, not a hook.



                ---



                ### `/lib`



                - Purpose: Stateless utility zone. Formatters, transformers, schemas, validators.

                - Rule: Must be pure and side-effect free.



                ---



                ### `/public`



                - Purpose: CDN-safe asset storage. Logos, icons, images.

                - Rule: Never import code here. No JS, no CSS, no side-effects. It’s dead weight.



                ---



                ## `#4` System Workflow: How to Build Correctly, Every Time



                > What to do, in what order.



                1. Define the feature: What route? What data? What UI?

                2. Create route (`app/...`)

                3. Check if needed primitives exist (`components/ui/`)

                   - Add if missing.

                4. Build feature-specific components (`components/features/...`)

                5. Fetch data in Server Component (`page.tsx`)

                6. Wire up data flow to features

                7. Add client interactivity only where needed

                8. Extract logic to `hooks` or `lib` if reused

                9. Test in isolation and via integration

