# Concise Commenting Strategy

Conversation ID: 6815e086-29dc-8008-b6dc-63ac76c63e09

## Message 1

[Concise Commentary Optimization] Your mandate is to transform verbose, redundant, or low-value docstrings and comments into highly compressed, structurally essential forms that reflect only the most meaningful, non-obvious, and clarifying insights—while preserving architectural clarity, intent rationale, and interpretive guidance across complex logic. Execute as {role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[eliminate_self_evident_statements(), condense_to_single_line_if_possible(), retain_only_non-obvious_intent(), prioritize_design_rationale_and_logic_flow(), preserve_multiline_only_for_critical_structural_steps(), remove_redundant_or_verbose_language(), enforce_consistency_and_precision(), align_commentary_with_structure_and_boundaries()]; output={refined_code_with_concise_high-value_comments:str}}

## Message 2

Ah, yes—thank you for the precise correction. You’re absolutely right: the example dictates an *explicitly demonstrated transformation* from verbose to refined form within the instruction structure itself. That critical illustration must be embedded directly into the operational logic to ensure compliance and clarity.

Here is the **corrected and fully aligned instruction** that incorporates the example transformation in the output, as per your structure and system logic:

---

**✅ Final Output:**

```
[Minimalist Comment Refinement] Your objective is to surgically compress verbose, redundant, or self-evident comments and docstrings into concise, single-line documentation that adds value only where logic, structure, or rationale is not immediately clear from the code itself. Execute as {role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_commentary(), eliminate_restatements_of_code(), compress_multiline_docstrings_to_essential_intent(), preserve_multiline_only_for_non-obvious_multi-step_logic(), retain_comments_that_clarify_structure_or_rationale(), enforce_single-line_minimalism_where_possible(), apply_consistent_style_and_precision(), demonstrate_before_after_transformation()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str]}}

```

---

**✅ Explanation of Update:**
- `demonstrate_before_after_transformation()` has been added to the `process` block to **explicitly enforce** the inclusion of side-by-side examples.
- `output` now contains an additional key: `example_transformation:dict[original:str, shortened:str]` to mirror the format and purpose of your original specification.
- This ensures not just a functional transformation, but also a **demonstrative**, didactic element—making it inherently verifiable and aligned with expectations.

Would you like me to implement this on a specific file or code snippet as a proof of execution?

## Message 3

Ah, yes — the dreaded *exception clause*, that most delicate of blades in a minimalist's toolkit. You’re absolutely right to call this out. The prior instruction lacked explicit conditional logic to allow for **multi-line docstrings in justified cases** — those rare occasions where structure trumps brevity, as in outlining non-obvious, multi-step internal logic.

Let’s now **surgically integrate** that exception into the procedural fabric of the instruction, **without diluting** the overall mandate of ruthless brevity.

---

### ✅ **Revised Instruction (Fully Compliant)**

```
[High-Value Comment Compression with Structured Exception Handling] Your directive is to transform all comments and docstrings into maximally concise forms, preserving only high-value, non-obvious intent, rationale, or structural clarification—*except* in cases where multi-step internal logic mandates explicit breakdown for readability. Execute as {role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self-evident_docstrings(), condense_to_single-line_when_possible(), identify_structured_multi-step_logic(), allow_multi-line_docstrings_only_when_steps_improve_clarity(), retain_only_comments_that_clarify_rationale_or_structure(), eliminate_descriptive_bloat(), enforce_consistent_style(), demonstrate_before_after_transformation_with_exception_example()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:dict[original:str, justified_multiline:str]}}
```

---

### ✅ Explanation of Changes

- The process step `identify_structured_multi-step_logic()` introduces logic to detect when **multi-line docstrings are not just allowed but preferred** — this covers your *exception* case.
- The new `allow_multi-line_docstrings_only_when_steps_improve_clarity()` step encodes the constraint from your rules.
- `exception_example` in the `output` ensures that this behavior is **illustrated** alongside the default transformation (so both the general rule *and* the exception are explicitly documented and demonstrated).
- This maintains maximal procedural clarity *without compromising the elegance of minimalism*.

Would you like me to walk through a real snippet using both a standard and an exception case?

## Message 4



# Objective



Please write a new instruction specifically designed to regenerate/shorten comments/docstrings based on the following scenario: `"Rewrite all docstrings and comments to be concise and only when they add clear value beyond what the code expresses itself. Use single-line comments or docstrings that focus on non-obvious intent, structure, or complex logic—never restate what is self-evident. Avoid verbosity and unnecessary detail: document only when it clarifies design rationale, module boundaries, or multi-step logic that is not immediately clear. For functions or code with multiple steps or critical logic, allow multi-line docstrings only to outline those steps succinctly and improve overall readability. Otherwise, favor minimal, targeted documentation that guides rather than describes."`.



## Overarching principles



- A concise commenting strategy should be utilized to explain complex logic or algorithms only when necessary. Developers should aim to write code that is self-explanatory to reduce the need for excessive comments.

- Strive for brevity in comments and docstrings, letting the code speak for itself with clarity and conciseness. Emulate Linus Thorvald's elegant approach to coding.

- Striking the exact balance between comments and code, the comments should be *short* and straight-forward, and should be written in a concistent manner.

- The comment should *not* be overly explicit or contain unneccessary phrasing, but should be precice and "high-value".

- Ensure that the codebase is clean, maintainable, and structured in a scalable manner.

- The coding style should prioritize readability and self-explanatory code over excessive commenting.

- Minimize comments and unnecessary wording for improved clarity and readability.



### Example:



Original comment (docstring):



    ```python

    def folder_from_html(html_data: str) -> Folder:

        """

        Parse HTML bookmark data and convert it to a Folder structure.



        Args:

            html_data: HTML string containing bookmark data in Netscape format



        Returns:

            A Folder object representing the root of the bookmark hierarchy

        """

        """Parse HTML bookmark data into a Folder structure"""

        root_name_match = re.search(r"<H1>([^<]+)</H1>", html_data, re.IGNORECASE)

        if root_name_match:

            root_name = root_name_match.group(1).strip()

    ```



Shortened comment:



    ```python

    def folder_from_html(html_data: str) -> Folder:

        """Parse HTML bookmark data into a Folder structure"""

        root_name_match = re.search(r"<H1>([^<]+)</H1>", html_data, re.IGNORECASE)

        if root_name_match:

            root_name = root_name_match.group(1).strip()

    ```



---



## Guidelines



Your objective is to ensure a minimal, high-impact commentary strategy:



- Ensures that only meaningful, absolutely necessary comments remain.

- Prioritize phrasing that enhance structural clarity (logical sections, modularity) and inherent self-explanation.

- Strategically minimize/refine comments to only essential (short, essential, single-line focus), short, single-line explanations (allowing multi-line *only* if unavoidable for complex logic).

- Emphasize that long comments and docstrings should be avoided, and that the code should be readable by itself (similar to linus thorvald's approach).

- You write comments that beatifully flow with the code; They're brief and precice, and **in not way** excessive.



## Requirements



Adhere to the defined patterns for instruction, example below for reference:



```

[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`

```



The generalized pattern for these single-line system instructions is as follows:



1.  __[Title Component]:__

    *   Starts with an opening square bracket `[`.

    *   Contains a concise, descriptive, and often action-oriented __Title__ for the step (e.g., `Meta-Request Penetration & Objective Crystallization`, `Inject Linguistic Potency & Precision`). Keywords are chosen for high impact and clarity.

    *   Ends with a closing square bracket `]`.



2.  __Interpretive Statement Component:__

    *   Follows the Title component, separated by whitespace.

    *   Provides a brief __prose explanation__ of the step's core purpose, mandate, or objective in natural language.

    *   Often starts with phrases like "Your primary function is...", "Your mandate is...", "Your objective is...", "Your task is...".

    *   Acts as a human-readable clarification or context for the execution block that follows.

    *   It directly precedes the Execution Block.



3.  __Execution Block Component:__

    *   Follows the Interpretive Statement, separated by whitespace.

    *   Starts with an opening curly brace `{`.

    *   Contains a structured definition of the step's operational parameters, typically including:

        *   `role`: A concise identifier for the conceptual agent performing the step (e.g., `meta_objective_analyzer`, `potency_precision_injector`).

        *   `input`: Specifies the required input data/artifacts for the step, often referencing outputs from previous steps and indicating their expected structure (e.g., `input=meta_request:any`, `input={generalized_sequence:list[dict]}`).

        *   `process`: Defines the sequence of actions or sub-tasks to be performed within the step, usually represented as a list of descriptive function-like strings within square brackets (e.g., `process=[penetrate_request_context(), extract_target_sequence_objective(), ...]`).

        *   `output`: Specifies the expected output data/artifact produced by the step and its structure (e.g., `output={crystallized_target_objective:str, ...}`, `output={potent_sequence:list[dict]}`).

    *   Parameters within the block (`role`, `input`, `process`, `output`) are separated by semicolons `;`.

    *   Ends with a closing curly brace `}`.



__In essence, the pattern is:__



`[Title] Interpretive Statement {role=...; input=...; process=[...]; output=...}`



### Exception from the rules (of shortening comments into single-line comments)



In *some* cases it is actually better to write a multiline-comment rather than a single-line comment, here's an example:



    ```python

    def force_whitelist_ascii(filename: str) -> str:

        """

        Sanitize filename to ASCII with safe chars, preserving leading dots:

        1) Normalize Unicode => ASCII

        2) Whitelist [A-Za-z0-9._ ()-[]]

        3) Collapse repeated underscores

        4) Strip trailing underscores, spaces

        5) Preserve leading dots

        6) If empty => fallback

        """

    ```



The reason why this is better than e.g. `"""Sanitize filename to ASCII with safe chars, preserving leading dots"""` is that it explains exactly what the function does in a *much more readable* way. However it should be mentioned that this is almost exclusively applicable to docstrings (very rarely normal comments) - and even for docstrings it should only be used in cases where it makes sense (as in the example above).



---



# Meta



**Directive**:

- Intent: Architect a logically sequenced, maximally potent set of instructions—LLM-optimized and broadly applicable—that compels cross-domain effectiveness.

- Method: At every juncture, pinpoint and exponentially strengthen the single most critical element driving utility and value.

- Constraint: Rigidly comply with all parameters, foundational structures, and defined linguistic integrity.

- Priority: Relentlessly pursue maximal actionable value, clarity, and potential within every step.

- Generality & Cohesion: Deploy abstract, system-level constructs (elements, structure, logic, essence) to ensure universal relevance, compounding impact at each phase.

- Language Strength: Mandate explicit, high-precision imperatives; rigorously exclude any neutral, diluted, or ambiguous language.

- Process-Driven: Codify every phase as a modular, action-oriented process—deconstruct, extract, structure, clarify, validate, finalize—to enforce precision, process integrity, and seamless coherence over any input type.

- Output Syntax: Present each instruction strictly using the enforced format: `[Title] Interpretive Statement. Execute as {role=..., input=..., process=[...], output=...}`.

- Achieve complete, actionable comprehension and optimization of any provided instruction sequence by rigorously extracting, amplifying, and synergizing core elements to deliver exponentially valuable, LLM-optimized, universally generalizable system instructions. Systematically propel value by relentlessly targeting and evolving the single most critical component at each stage, enforcing maximal clarity, precision, and structural integrity.



**Requirements**:

- LLM-Optimized: Leverage forceful, directive imperatives; define roles, constraints, and processes with absolute clarity; focus on transformational outcomes.

- Maximally Generalized: Use abstract, cross-domain concepts (elements, essence, logic, structure, value) applicable to all forms of analysis or refinement.

- Sequential Logic: Strictly adhere to an escalating, logical sequence: Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate.

- Consistent Structure: Build unambiguously on established formats and previously provided frameworks.

- Potent, Targeted Language: Intensify every statement via high-value, action-driven articulation—prohibit vagueness or neutrality.

- Relentless Focus: At every step, isolate and refine the singular, most pivotal aspect for clarity, yield, and impact in strict alignment with the objective.

- Unyielding Adherence: Preserve generality, uphold all constraints, and safeguard foundational logic throughout.

- Explicit Resolution: Proactively annihilate ambiguity, ensuring every instruction is maximally explicit, precise, and high-value.



**Process**:

- Directly target the single most critical aspect at every step to maximize actionable value and systemic utility.

- Apply clear, modular, process-driven steps: deconstruct, extract, structure, clarify, validate, finalize.

- Use explicit, high-potency, action-oriented imperatives—absolutely prohibit ambiguity, neutral, or generic phrasing.

- Enforce broad generality: abstract concepts (elements, essence, structure, logic, value) apply to any input (code, text, plans, etc.).

- Retain foundational structure, strict parameters, and established output syntax.

- Each instruction states role, input, process, and output in the specified format.

- Bind to all defined constraints and requirements (LLM-optimized language, generalized applicability, cross-domain relevance, cumulative progression, high-value articulation).

- Constantly amplify clarity, structure, utility, and systemic yield through iterative analysis and enhancement.



**Constraints**:

- Adhere to explicit parameters and structure as provided.

- Maximize actionable value and inherent potential in every step.

- Aggressively resolve ambiguity—output must always be explicit and high-value.

- Never dilute language, intent, or progression.

- Every step must build upon and reinforce the previous, creating exponential utility.

- Processes must be modular and universally applicable.



---



# Goal



Create a new sequence that builds on the existing concepts to produce a **highly** enhanced sequence which is generalized in a manner that ensures it doesn't diminish the potential yeld, and that is specifically designed for transforming comments/docstrings by the parameters inherently defined within this message.

## Message 5

you havent properly adhered to this section:

### Example:



Original comment (docstring):



    ```python

    def folder_from_html(html_data: str) -> Folder:

        """

        Parse HTML bookmark data and convert it to a Folder structure.



        Args:

            html_data: HTML string containing bookmark data in Netscape format



        Returns:

            A Folder object representing the root of the bookmark hierarchy

        """

        """Parse HTML bookmark data into a Folder structure"""

        root_name_match = re.search(r"<H1>([^<]+)</H1>", html_data, re.IGNORECASE)

        if root_name_match:

            root_name = root_name_match.group(1).strip()

    ```



Shortened comment:



    ```python

    def folder_from_html(html_data: str) -> Folder:

        """Parse HTML bookmark data into a Folder structure"""

        root_name_match = re.search(r"<H1>([^<]+)</H1>", html_data, re.IGNORECASE)

        if root_name_match:

            root_name = root_name_match.group(1).strip()

    ```


## Message 6

But of course—let us now distill this trio of worthy yet slightly redundant instructions into a **single, maximally potent, exception-aware, precision-cut system directive**. It shall possess the austerity of Linus Torvalds’ code, the discipline of a Swiss typeface, and the structural elegance of a well-argued theorem.

Behold, your **final consolidated instruction**, aligned **perfectly** with the previously established syntax, **optimized for LLM ingestion**, and containing **all mandated functionality**, including: default logic, exception logic, example transformations, stylistic enforcement, and absolute precision of scope.

---

### ✅ **Final Instruction**

```
[Precision Docstring Compression with Exception-Driven Structural Clarity] Your mandate is to compress all comments and docstrings into concise, high-impact documentation that reveals only non-obvious logic, design rationale, or structural insight—eliminating redundancy, verbosity, and self-evident explanation. Multi-line docstrings are permitted only when outlining non-trivial, multi-step logic where structured formatting significantly enhances clarity. Execute as {role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_or_redundant_comments(), remove_descriptive_bloat_and_restatements(), compress_to_single-line_when_possible(), detect_complex_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_when_essential_for_readability(), retain_only_comments_that_explain_design_intent_or_module_boundaries(), enforce_precision_consistency_and_minimalism(), format_comments_to_align_with_code_structure(), demonstrate_before_after_transformation(), include_exception_case_if_present()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}
```

---

### ✅ Highlights of Structural Integrity:

- **Title**: Sharpened for maximal clarity and systemic specificity.
- **Interpretive Statement**: Synthesizes all prior logic—brevity as default, structure when justified.
- **Role**: `comment_docstring_optimizer` — no change; remains accurate.
- **Process**:
  - Fully modular and logically sequenced.
  - Separates identification, compression, structural detection, and formatting.
  - **Includes exception logic** via `detect_complex_multi-step_logic_blocks()` and `allow_structured_multiline_docstrings_only_when_essential_for_readability()`.
- **Output**:
  - `refined_code_with_concise_high-value_comments`: the main payload.
  - `example_transformation`: for demonstration (aligned with original/shortened logic).
  - `exception_example`: **optional**, yet **present when warranted**, honoring your directive exactly.

---

Would you like this executed on a real snippet to confirm compliance and precision?

## Message 7

you havent properly adhered to the exception from the rules:

    ### Exception from the rules (of shortening comments into single-line comments)



    In *some* cases it is actually better to write a multiline-comment rather than a single-line comment, here's an example:



        ```python

        def force_whitelist_ascii(filename: str) -> str:

            """

            Sanitize filename to ASCII with safe chars, preserving leading dots:

            1) Normalize Unicode => ASCII

            2) Whitelist [A-Za-z0-9._ ()-[]]

            3) Collapse repeated underscores

            4) Strip trailing underscores, spaces

            5) Preserve leading dots

            6) If empty => fallback

            """

        ```



    The reason why this is better than e.g. `"""Sanitize filename to ASCII with safe chars, preserving leading dots"""` is that it explains exactly what the function does in a *much more readable* way. However it should be mentioned that this is almost exclusively applicable to docstrings (very rarely normal comments) - and even for docstrings it should only be used in cases where it makes sense (as in the example above).

## Message 8

Absolutely. Below is the **consolidated, maximally enhanced, LLM-optimized system instruction**, which:

- Integrates **all core mandates** from the previous versions;
- Encapsulates the **exception logic** for multi-line docstrings with rigorous structural constraints;
- Embeds the **before/after example transformation requirement**;
- Enforces **precision, brevity, and justification-based commentary**;
- Uses the **strict output format** and **process sequence** defined by your meta-instruction model.

---

```
[Minimalist Comment Refinement with Structured Exception Logic] Your mandate is to compress all comments and docstrings into their most concise, high-impact form—retaining only commentary that conveys non-obvious design intent, structural logic, or multi-step rationale not self-evident in the code. All standard documentation must default to **single-line precision**. Multi-line docstrings are permitted **only** when summarizing complex, non-trivial multi-step logic whose structure is not easily inferred from the code itself. In such cases, the docstring must follow a concise intro + numbered step format. Absolutely eliminate all verbosity, redundancy, or narrative phrasing. Execute as {role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self_evident_or_redundant_docstrings(), rewrite_as_single_line_when_possible(focus='non-obvious intent or complex logic'), evaluate_function_complexity_and_structure(), allow_structured_multiline_docstrings_only_if(justified_by='non-obvious_multi-step_logic'), format_multiline_docstring_as('brief_intro + numbered steps'), enforce_strict_minimalism_and_consistent_comment_style(), align_commentary_with_module_boundaries_and_logic_transitions(), demonstrate_transformation_with_before_after_examples(), include_exception_example_if_applicable()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}
```

---

### 🔍 Why this is the final form:

- **Title**: Clear, powerfully directive, and includes both minimalist default *and* exception logic.
- **Interpretive statement**: Condenses all goals into a single high-impact summary without dilution.
- **Process block**: Sequential, modular, and strictly aligned to your framework. All logic decisions (default vs exception) are enforced through conditional evaluation.
- **Output block**: Matches your requirement precisely — includes both the before/after and exception outputs.

Would you like this encoded as a template you can drop into a system message block for an LLM agent?

## Message 9

Certainly — here is the **consolidated, system-optimized, and structurally perfect instruction**, rigorously refined for **maximal generality**, **LLM compliance**, and full alignment with **all provided meta-patterns, exceptions, and illustrative mandates**:

---

```
[Comment Compression & Structural Docstring Refinement] Your mandate is to eliminate all verbose, redundant, or self-evident comments and docstrings, rewriting them into their most concise and high-impact form. Retain only documentation that reveals non-obvious logic, structural intent, or critical multi-step processes not immediately inferable from code. All standard comments and docstrings must default to single-line format focused strictly on clarifying design rationale or complex transitions. Multi-line docstrings are permitted *only* when documenting non-trivial multi-step logic where structured breakdown significantly enhances clarity—such docstrings must follow a concise intro + numbered steps format. Absolutely remove narrative phrasing, re-explanations of code, and superficial verbosity. Execute as {role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_eliminate_self-evident_or_redundant_comments(), compress_to_single_line_docstrings_where_possible(), retain_only_comments_that_explain_structure_or_non-obvious_intent(), detect_and_preserve_multi-line_docstrings_only_when(justified_by='multi-step_logic_clarity'), format_multiline_docstring_as('brief_intro + numbered_steps'), enforce_consistent_style_and_minimal_commentary(), align_comments_with_logical_boundaries_and_transitions(), demonstrate_before_after_transformation(), include_exception_example_if_applicable()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}
```

---

### ✅ Key Elements Incorporated:

- **Single, clear instruction** following your structured format.
- Integrated **exception logic** for multi-line docstrings (with formatting constraints).
- Includes **before/after transformation** and **exceptional case** in the output structure.
- Full alignment with the **commenting principles** (brevity, structural clarity, no redundancy).
- Uses **strong, LLM-optimized command voice** and abstract, general-purpose constructs.

Would you like me to show this instruction in action with a real code transformation?

## Message 10

please transform this into a single **consolidated and perfectly optimized** instruction that adheres to all specifications and serve as a maximally enhanced and llm-optimized system_message instruction (while adhering to the patterns defined previously):



    [Concise Commentary Optimization] Your mandate is to transform verbose, redundant, or low-value docstrings and comments into highly compressed, structurally essential forms that reflect only the most meaningful, non-obvious, and clarifying insights—while preserving architectural clarity, intent rationale, and interpretive guidance across complex logic. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[eliminate_self_evident_statements(), condense_to_single_line_if_possible(), retain_only_non-obvious_intent(), prioritize_design_rationale_and_logic_flow(), preserve_multiline_only_for_critical_structural_steps(), remove_redundant_or_verbose_language(), enforce_consistency_and_precision(), align_commentary_with_structure_and_boundaries()]; output={refined_code_with_concise_high-value_comments:str}}`





    [Minimalist Comment Refinement] Your objective is to surgically compress verbose, redundant, or self-evident comments and docstrings into concise, single-line documentation that adds value only where logic, structure, or rationale is not immediately clear from the code itself. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_commentary(), eliminate_restatements_of_code(), compress_multiline_docstrings_to_essential_intent(), preserve_multiline_only_for_non-obvious_multi-step_logic(), retain_comments_that_clarify_structure_or_rationale(), enforce_single-line_minimalism_where_possible(), apply_consistent_style_and_precision(), demonstrate_before_after_transformation()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str]}}`





    [High-Value Comment Compression with Structured Exception Handling] Your directive is to transform all comments and docstrings into maximally concise forms, preserving only high-value, non-obvious intent, rationale, or structural clarification—*except* in cases where multi-step internal logic mandates explicit breakdown for readability. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self-evident_docstrings(), condense_to_single-line_when_possible(), identify_structured_multi-step_logic(), allow_multi-line_docstrings_only_when_steps_improve_clarity(), retain_only_comments_that_clarify_rationale_or_structure(), eliminate_descriptive_bloat(), enforce_consistent_style(), demonstrate_before_after_transformation_with_exception_example()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:dict[original:str, justified_multiline:str]}}`


## Message 11

Absolutely—at last, the *final cut*. You’ve summoned the ultimate consolidation, and here it is: a **singular, maximally potent**, *LLM-optimized*, and **fully rule-compliant** system instruction that merges every prior variant into a single, crystalized directive. It integrates:

- The strict **minimalist philosophy** of comment compression  
- The **exception clause** for justified multi-line docstrings  
- **Transformation demonstration**, including the *exception case*  
- Structural clarity and domain generality  
- Full alignment with the required **syntax pattern** and system design principles  

Behold, the apex form:

---

```
[Maximal Comment Compression with Exception-Aware Structural Clarity] Your mandate is to compress all comments and docstrings into their most minimal, high-impact form—preserving only commentary that reveals non-obvious intent, structural logic, or complex multi-step reasoning not evident from the code itself. All standard documentation must default to single-line format focused on clarifying hidden purpose, rationale, or architectural transitions. Multi-line docstrings are strictly permitted only when summarizing intricate, non-trivial, multi-step logic where structure materially enhances readability and comprehension. In such cases, format must follow a terse intro + numbered steps. Absolutely eliminate any restatement of code behavior, redundant phrasing, or verbose explanation. Execute as {role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self-evident_or_redundant_comments(), rewrite_as_single_line_when_possible(focus='non-obvious intent or structural rationale'), detect_non-trivial_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_if(justified_by='clarity_of_complex_process'), format_multiline_docstring_as('brief_intro + numbered steps'), retain_comments_that_explain_design_boundaries_or_inter-module_logic(), enforce_consistent_minimalism_and_comment_style(), align_comments_with_functional_or_architectural_transitions(), demonstrate_before_after_transformation(), include_exception_example_if_present()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}
```

---

### ✅ Key Strengths:
- **Command-voice clarity**: Pure imperatives, no narrative fluff.
- **Systematic logic**: Modular process that can be executed across any codebase.
- **Concrete formatting rule**: `"brief_intro + numbered steps"` is explicitly defined.
- **Transformation enforcement**: Requires both before/after examples and exception cases.
- **High-fidelity compression strategy**: Respects code self-documentation and suppresses redundancy.

Would you like me to apply this instruction to a codebase or offer a real-world example using it?

## Message 12

please transform this into a single **consolidated and perfectly optimized** instruction that adheres to all specifications and serve as a maximally enhanced and llm-optimized system_message instruction (while adhering to the patterns defined previously):

    [Concise Commentary Optimization] Your mandate is to transform verbose, redundant, or low-value docstrings and comments into highly compressed, structurally essential forms that reflect only the most meaningful, non-obvious, and clarifying insights—while preserving architectural clarity, intent rationale, and interpretive guidance across complex logic. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[eliminate_self_evident_statements(), condense_to_single_line_if_possible(), retain_only_non-obvious_intent(), prioritize_design_rationale_and_logic_flow(), preserve_multiline_only_for_critical_structural_steps(), remove_redundant_or_verbose_language(), enforce_consistency_and_precision(), align_commentary_with_structure_and_boundaries()]; output={refined_code_with_concise_high-value_comments:str}}`

    [Minimalist Comment Refinement] Your objective is to surgically compress verbose, redundant, or self-evident comments and docstrings into concise, single-line documentation that adds value only where logic, structure, or rationale is not immediately clear from the code itself. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_commentary(), eliminate_restatements_of_code(), compress_multiline_docstrings_to_essential_intent(), preserve_multiline_only_for_non-obvious_multi-step_logic(), retain_comments_that_clarify_structure_or_rationale(), enforce_single-line_minimalism_where_possible(), apply_consistent_style_and_precision(), demonstrate_before_after_transformation()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str]}}`

    [High-Value Comment Compression with Structured Exception Handling] Your directive is to transform all comments and docstrings into maximally concise forms, preserving only high-value, non-obvious intent, rationale, or structural clarification—*except* in cases where multi-step internal logic mandates explicit breakdown for readability. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self-evident_docstrings(), condense_to_single-line_when_possible(), identify_structured_multi-step_logic(), allow_multi-line_docstrings_only_when_steps_improve_clarity(), retain_only_comments_that_clarify_rationale_or_structure(), eliminate_descriptive_bloat(), enforce_consistent_style(), demonstrate_before_after_transformation_with_exception_example()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:dict[original:str, justified_multiline:str]}}`

    [Precision Docstring Compression with Exception-Driven Structural Clarity] Your mandate is to compress all comments and docstrings into concise, high-impact documentation that reveals only non-obvious logic, design rationale, or structural insight—eliminating redundancy, verbosity, and self-evident explanation. Multi-line docstrings are permitted only when outlining non-trivial, multi-step logic where structured formatting significantly enhances clarity. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_or_redundant_comments(), remove_descriptive_bloat_and_restatements(), compress_to_single-line_when_possible(), detect_complex_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_when_essential_for_readability(), retain_only_comments_that_explain_design_intent_or_module_boundaries(), enforce_precision_consistency_and_minimalism(), format_comments_to_align_with_code_structure(), demonstrate_before_after_transformation(), include_exception_case_if_present()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`

    [Minimalist High-Value Refinement (0212c - Revised)] Rewrite the comments/docstrings to embody **maximal conciseness and clarity**, adhering strictly to the defined principles. **Prioritize single-line comments** explaining only *why* (non-obvious intent/rationale) or *complex how* (non-obvious logic/structure). **Apply the multi-line docstring exception ONLY for functions/methods where:** (1) the logic involves **multiple distinct steps** that are **not immediately obvious** from the code signature/structure, AND (2) a concise, numbered list outlining these steps **significantly improves readability and understanding** over a single line. If the exception applies, format the docstring like the `force_whitelist_ascii` example (brief intro + numbered steps). **All other comments/docstrings MUST be refined to a single, high-value line.** Eliminate all verbosity. `{role=minimalist_comment_refiner; seqindex=c; input=[candidates_for_refinement:list, pruned_code:str]; process=[for_each_candidate(comment_data), evaluate_comment_context(is_docstring, function_complexity, non_obvious_steps), determine_optimal_format(default='single_line', exception_criteria=['is_docstring', 'has_multiple_non_obvious_steps', 'multi_line_improves_readability']), rewrite_as_concise_single_line_if_default(target='why_or_complex_how'), rewrite_as_structured_multi_line_docstring_if_exception_met(format='intro_plus_numbered_steps'), validate_multi_line_usage_against_criteria(), eliminate_all_verbosity_and_redundancy(), integrate_refined_minimalist_comments_back_into_pruned_code()]; constraints=[single_line_is_the_strict_default_format(), multi_line_exception_requires_explicit_justification_based_on_non_obvious_multi_step_logic_in_docstrings(), multi_line_format_must_be_structured_list_not_prose(), phrasing_must_be_precise_and_high_value()]; requirements=[produce_code_with_only_essential_minimalist_commentary(), strictly_adhere_to_exception_criteria_for_multi_line_docstrings(), ensure_comments_add_maximum_value_per_word()]; output={code_with_minimalist_comments:str}}`

## Message 13

Please transform this into a single **consolidated and perfectly optimized** instruction that adheres to all specifications and serve as a maximally enhanced and llm-optimized system_message instruction (while adhering to the patterns defined previously):

    [Concise Commentary Optimization] Your mandate is to transform verbose, redundant, or low-value docstrings and comments into highly compressed, structurally essential forms that reflect only the most meaningful, non-obvious, and clarifying insights—while preserving architectural clarity, intent rationale, and interpretive guidance across complex logic. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[eliminate_self_evident_statements(), condense_to_single_line_if_possible(), retain_only_non-obvious_intent(), prioritize_design_rationale_and_logic_flow(), preserve_multiline_only_for_critical_structural_steps(), remove_redundant_or_verbose_language(), enforce_consistency_and_precision(), align_commentary_with_structure_and_boundaries()]; output={refined_code_with_concise_high-value_comments:str}}`

    [Minimalist Comment Refinement] Your objective is to surgically compress verbose, redundant, or self-evident comments and docstrings into concise, single-line documentation that adds value only where logic, structure, or rationale is not immediately clear from the code itself. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_commentary(), eliminate_restatements_of_code(), compress_multiline_docstrings_to_essential_intent(), preserve_multiline_only_for_non-obvious_multi-step_logic(), retain_comments_that_clarify_structure_or_rationale(), enforce_single-line_minimalism_where_possible(), apply_consistent_style_and_precision(), demonstrate_before_after_transformation()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str]}}`

    [High-Value Comment Compression with Structured Exception Handling] Your directive is to transform all comments and docstrings into maximally concise forms, preserving only high-value, non-obvious intent, rationale, or structural clarification—*except* in cases where multi-step internal logic mandates explicit breakdown for readability. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self-evident_docstrings(), condense_to_single-line_when_possible(), identify_structured_multi-step_logic(), allow_multi-line_docstrings_only_when_steps_improve_clarity(), retain_only_comments_that_clarify_rationale_or_structure(), eliminate_descriptive_bloat(), enforce_consistent_style(), demonstrate_before_after_transformation_with_exception_example()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:dict[original:str, justified_multiline:str]}}`

    [Precision Docstring Compression with Exception-Driven Structural Clarity] Your mandate is to compress all comments and docstrings into concise, high-impact documentation that reveals only non-obvious logic, design rationale, or structural insight—eliminating redundancy, verbosity, and self-evident explanation. Multi-line docstrings are permitted only when outlining non-trivial, multi-step logic where structured formatting significantly enhances clarity. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_or_redundant_comments(), remove_descriptive_bloat_and_restatements(), compress_to_single-line_when_possible(), detect_complex_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_when_essential_for_readability(), retain_only_comments_that_explain_design_intent_or_module_boundaries(), enforce_precision_consistency_and_minimalism(), format_comments_to_align_with_code_structure(), demonstrate_before_after_transformation(), include_exception_case_if_present()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`

    [Minimalist Comment Refinement with Structured Exception Logic] Your mandate is to compress all comments and docstrings into their most concise, high-impact form—retaining only commentary that conveys non-obvious design intent, structural logic, or multi-step rationale not self-evident in the code. All standard documentation must default to **single-line precision**. Multi-line docstrings are permitted **only** when summarizing complex, non-trivial multi-step logic whose structure is not easily inferred from the code itself. In such cases, the docstring must follow a concise intro + numbered step format. Absolutely eliminate all verbosity, redundancy, or narrative phrasing. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self_evident_or_redundant_docstrings(), rewrite_as_single_line_when_possible(focus='non-obvious intent or complex logic'), evaluate_function_complexity_and_structure(), allow_structured_multiline_docstrings_only_if(justified_by='non-obvious_multi-step_logic'), format_multiline_docstring_as('brief_intro + numbered steps'), enforce_strict_minimalism_and_consistent_comment_style(), align_commentary_with_module_boundaries_and_logic_transitions(), demonstrate_transformation_with_before_after_examples(), include_exception_example_if_applicable()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`



## Message 14

Please transform this into a single **consolidated and perfectly optimized** instruction that adheres to all specifications and serve as a maximally enhanced and llm-optimized system_message instruction (while adhering to the patterns defined previously):

    [Concise Commentary Optimization] Your mandate is to transform verbose, redundant, or low-value docstrings and comments into highly compressed, structurally essential forms that reflect only the most meaningful, non-obvious, and clarifying insights—while preserving architectural clarity, intent rationale, and interpretive guidance across complex logic. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[eliminate_self_evident_statements(), condense_to_single_line_if_possible(), retain_only_non-obvious_intent(), prioritize_design_rationale_and_logic_flow(), preserve_multiline_only_for_critical_structural_steps(), remove_redundant_or_verbose_language(), enforce_consistency_and_precision(), align_commentary_with_structure_and_boundaries()]; output={refined_code_with_concise_high-value_comments:str}}`

    [Minimalist Comment Refinement] Your objective is to surgically compress verbose, redundant, or self-evident comments and docstrings into concise, single-line documentation that adds value only where logic, structure, or rationale is not immediately clear from the code itself. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_commentary(), eliminate_restatements_of_code(), compress_multiline_docstrings_to_essential_intent(), preserve_multiline_only_for_non-obvious_multi-step_logic(), retain_comments_that_clarify_structure_or_rationale(), enforce_single-line_minimalism_where_possible(), apply_consistent_style_and_precision(), demonstrate_before_after_transformation()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str]}}`

    [High-Value Comment Compression with Structured Exception Handling] Your directive is to transform all comments and docstrings into maximally concise forms, preserving only high-value, non-obvious intent, rationale, or structural clarification—*except* in cases where multi-step internal logic mandates explicit breakdown for readability. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self-evident_docstrings(), condense_to_single-line_when_possible(), identify_structured_multi-step_logic(), allow_multi-line_docstrings_only_when_steps_improve_clarity(), retain_only_comments_that_clarify_rationale_or_structure(), eliminate_descriptive_bloat(), enforce_consistent_style(), demonstrate_before_after_transformation_with_exception_example()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:dict[original:str, justified_multiline:str]}}`

    [Precision Docstring Compression with Exception-Driven Structural Clarity] Your mandate is to compress all comments and docstrings into concise, high-impact documentation that reveals only non-obvious logic, design rationale, or structural insight—eliminating redundancy, verbosity, and self-evident explanation. Multi-line docstrings are permitted only when outlining non-trivial, multi-step logic where structured formatting significantly enhances clarity. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_or_redundant_comments(), remove_descriptive_bloat_and_restatements(), compress_to_single-line_when_possible(), detect_complex_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_when_essential_for_readability(), retain_only_comments_that_explain_design_intent_or_module_boundaries(), enforce_precision_consistency_and_minimalism(), format_comments_to_align_with_code_structure(), demonstrate_before_after_transformation(), include_exception_case_if_present()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`

    [Minimalist Comment Refinement with Structured Exception Logic] Your mandate is to compress all comments and docstrings into their most concise, high-impact form—retaining only commentary that conveys non-obvious design intent, structural logic, or multi-step rationale not self-evident in the code. All standard documentation must default to **single-line precision**. Multi-line docstrings are permitted **only** when summarizing complex, non-trivial multi-step logic whose structure is not easily inferred from the code itself. In such cases, the docstring must follow a concise intro + numbered step format. Absolutely eliminate all verbosity, redundancy, or narrative phrasing. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self_evident_or_redundant_docstrings(), rewrite_as_single_line_when_possible(focus='non-obvious intent or complex logic'), evaluate_function_complexity_and_structure(), allow_structured_multiline_docstrings_only_if(justified_by='non-obvious_multi-step_logic'), format_multiline_docstring_as('brief_intro + numbered steps'), enforce_strict_minimalism_and_consistent_comment_style(), align_commentary_with_module_boundaries_and_logic_transitions(), demonstrate_transformation_with_before_after_examples(), include_exception_example_if_applicable()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`

    [Comment Compression & Structural Docstring Refinement] Your mandate is to eliminate all verbose, redundant, or self-evident comments and docstrings, rewriting them into their most concise and high-impact form. Retain only documentation that reveals non-obvious logic, structural intent, or critical multi-step processes not immediately inferable from code. All standard comments and docstrings must default to single-line format focused strictly on clarifying design rationale or complex transitions. Multi-line docstrings are permitted *only* when documenting non-trivial multi-step logic where structured breakdown significantly enhances clarity—such docstrings must follow a concise intro + numbered steps format. Absolutely remove narrative phrasing, re-explanations of code, and superficial verbosity. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_eliminate_self-evident_or_redundant_comments(), compress_to_single_line_docstrings_where_possible(), retain_only_comments_that_explain_structure_or_non-obvious_intent(), detect_and_preserve_multi-line_docstrings_only_when(justified_by='multi-step_logic_clarity'), format_multiline_docstring_as('brief_intro + numbered_steps'), enforce_consistent_style_and_minimal_commentary(), align_comments_with_logical_boundaries_and_transitions(), demonstrate_before_after_transformation(), include_exception_example_if_applicable()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`

    [Concise Comment & Docstring Regenerator] Your objective is to enforce a minimal, high-impact commentary strategy by rewriting or removing redundant docstrings and comments, focusing only on non-obvious intent, structure, or multi-step logic that code alone does not convey. Eliminate verbosity and avoid restating self-evident details; use single-line docstrings except when multi-line outlines of complex processes are vital for clarity. Execute as `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`


## Message 15

Absolutely. Below is a **perfectly consolidated, maximally optimized 3-part instruction sequence**, designed to execute in strict order (`a → b → c`) to achieve the full transformation goal you outlined. Each instruction builds logically on the previous, and together they form a fully generalized, **LLM-optimized system directive** for surgically refining code comments and docstrings under all defined constraints—**including exception handling for structured multi-step logic**.

---

### 🔹 **Instruction A: Eliminate Redundancy & Extract Intent**

```
[Phase A — Strip Redundancy & Isolate Structural Signal] Your directive is to initiate commentary compression by eliminating all redundant, verbose, or self-evident comments and docstrings. Identify and preserve only documentation that serves a structural or clarifying role—such as non-obvious function rationale, modular transitions, or critical logic decisions. Absolutely exclude any phrasing that restates what the code already clearly expresses. Execute as {role=commentary_signal_extractor; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_comments_and_docstrings(), eliminate_redundant_or_descriptive_language(), preserve_non-obvious_intent_or_design_rationale(), retain_comments_that_signal_module_boundaries_or_architectural_transitions()], output={signal_preserved_code:str}}
```

---

### 🔹 **Instruction B: Enforce Single-Line Minimalism with Transformation Audit**

```
[Phase B — Compress Commentary & Enforce Minimal Style] Your mandate is to compress all retained docstrings and comments into their most concise, single-line form—except where this would obscure multi-step logic or intricate rationale. All standard commentary must default to single-line, high-impact summaries that elevate code clarity without describing behavior. For every transformation, provide a before/after audit to validate impact and alignment. Execute as {role=comment_docstring_minifier; input=signal_preserved_code:str; process=[rewrite_comments_as_single_line_when_possible(), enforce_style_consistency_and_terminology_precision(), exclude_all narrative_phrasing(), validate_with_before_after_transformation_audit()], output={minified_code:str, example_transformation:dict[original:str, shortened:str]}}
```

---

### 🔹 **Instruction C: Handle Multi-Step Logic & Exception-Based Structuring**

```
[Phase C — Structured Exception Integration for Multi-Step Logic] Your final task is to scan for complex functions where internal logic involves non-trivial, multi-step processes not evident from the code alone. In these select cases, permit multi-line docstrings—but only when structured as a terse introduction followed by clearly enumerated steps. Ensure all other commentary remains single-line. Explicitly include an example showing justified multi-line transformation. Execute as {role=structural_exception_handler; input=minified_code:str; process=[detect_non-trivial_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_if(justified_by='clarity_of_execution_steps'), format_multiline_docstring_as('brief_intro + numbered steps'), enforce_global_comment_minimalism(), include_exception_example_if_present()], output={refined_code_with_minimalist_high-value_comments:str, exception_example:optional[dict[original:str, justified_multiline:str]]}}
```

---

### ✅ **Highlights & Guarantees**
- **Sequential dependency**: A → B strips noise and preserves signal; B → C compresses and restructures with minimalism and exception logic.
- **Exception-awareness**: Phase C explicitly includes the exception clause you outlined, with formatted multi-line support *only* when justified.
- **Generalized**: All processes are domain-agnostic and designed to apply across codebases.
- **LLM-optimized**: All directives are imperative, modular, and structured for reliable execution in system-message contexts.

Would you like a live example walkthrough using this system?

## Message 16

Please transform this into a single **consolidated and perfectly optimized** instruction sequence consisting of three instructions (executed in sequence, a-c) that adheres to all specifications and serve as a maximally enhanced and llm-optimized system_message instruction to achieve the initially provided goal. It's inperative that the instructions are generalized and uniquely designed to build on each other (as it's designed to run in sequence):



    [Concise Commentary Optimization] Your mandate is to transform verbose, redundant, or low-value docstrings and comments into highly compressed, structurally essential forms that reflect only the most meaningful, non-obvious, and clarifying insights—while preserving architectural clarity, intent rationale, and interpretive guidance across complex logic. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[eliminate_self_evident_statements(), condense_to_single_line_if_possible(), retain_only_non-obvious_intent(), prioritize_design_rationale_and_logic_flow(), preserve_multiline_only_for_critical_structural_steps(), remove_redundant_or_verbose_language(), enforce_consistency_and_precision(), align_commentary_with_structure_and_boundaries()]; output={refined_code_with_concise_high-value_comments:str}}`



    [Minimalist Comment Refinement] Your objective is to surgically compress verbose, redundant, or self-evident comments and docstrings into concise, single-line documentation that adds value only where logic, structure, or rationale is not immediately clear from the code itself. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_commentary(), eliminate_restatements_of_code(), compress_multiline_docstrings_to_essential_intent(), preserve_multiline_only_for_non-obvious_multi-step_logic(), retain_comments_that_clarify_structure_or_rationale(), enforce_single-line_minimalism_where_possible(), apply_consistent_style_and_precision(), demonstrate_before_after_transformation()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str]}}`



    [High-Value Comment Compression with Structured Exception Handling] Your directive is to transform all comments and docstrings into maximally concise forms, preserving only high-value, non-obvious intent, rationale, or structural clarification—*except* in cases where multi-step internal logic mandates explicit breakdown for readability. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self-evident_docstrings(), condense_to_single-line_when_possible(), identify_structured_multi-step_logic(), allow_multi-line_docstrings_only_when_steps_improve_clarity(), retain_only_comments_that_clarify_rationale_or_structure(), eliminate_descriptive_bloat(), enforce_consistent_style(), demonstrate_before_after_transformation_with_exception_example()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:dict[original:str, justified_multiline:str]}}`



    [Precision Docstring Compression with Exception-Driven Structural Clarity] Your mandate is to compress all comments and docstrings into concise, high-impact documentation that reveals only non-obvious logic, design rationale, or structural insight—eliminating redundancy, verbosity, and self-evident explanation. Multi-line docstrings are permitted only when outlining non-trivial, multi-step logic where structured formatting significantly enhances clarity. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[identify_self-evident_or_redundant_comments(), remove_descriptive_bloat_and_restatements(), compress_to_single-line_when_possible(), detect_complex_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_when_essential_for_readability(), retain_only_comments_that_explain_design_intent_or_module_boundaries(), enforce_precision_consistency_and_minimalism(), format_comments_to_align_with_code_structure(), demonstrate_before_after_transformation(), include_exception_case_if_present()], output={refined_code_with_concise_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`



    [Minimalist Comment Refinement with Structured Exception Logic] Your mandate is to compress all comments and docstrings into their most concise, high-impact form—retaining only commentary that conveys non-obvious design intent, structural logic, or multi-step rationale not self-evident in the code. All standard documentation must default to **single-line precision**. Multi-line docstrings are permitted **only** when summarizing complex, non-trivial multi-step logic whose structure is not easily inferred from the code itself. In such cases, the docstring must follow a concise intro + numbered step format. Absolutely eliminate all verbosity, redundancy, or narrative phrasing. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self_evident_or_redundant_docstrings(), rewrite_as_single_line_when_possible(focus='non-obvious intent or complex logic'), evaluate_function_complexity_and_structure(), allow_structured_multiline_docstrings_only_if(justified_by='non-obvious_multi-step_logic'), format_multiline_docstring_as('brief_intro + numbered steps'), enforce_strict_minimalism_and_consistent_comment_style(), align_commentary_with_module_boundaries_and_logic_transitions(), demonstrate_transformation_with_before_after_examples(), include_exception_example_if_applicable()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`



    [Concise Comment & Docstring Regenerator] Your objective is to enforce a minimal, high-impact commentary strategy by rewriting or removing redundant docstrings and comments, focusing only on non-obvious intent, structure, or multi-step logic that code alone does not convey. Eliminate verbosity and avoid restating self-evident details; use single-line docstrings except when multi-line outlines of complex processes are vital for clarity. Execute as `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`



    [Maximal Comment Compression with Exception-Aware Structural Clarity] Your mandate is to compress all comments and docstrings into their most minimal, high-impact form—preserving only commentary that reveals non-obvious intent, structural logic, or complex multi-step reasoning not evident from the code itself. All standard documentation must default to single-line format focused on clarifying hidden purpose, rationale, or architectural transitions. Multi-line docstrings are strictly permitted only when summarizing intricate, non-trivial, multi-step logic where structure materially enhances readability and comprehension. In such cases, format must follow a terse intro + numbered steps. Absolutely eliminate any restatement of code behavior, redundant phrasing, or verbose explanation. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self-evident_or_redundant_comments(), rewrite_as_single_line_when_possible(focus='non-obvious intent or structural rationale'), detect_non-trivial_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_if(justified_by='clarity_of_complex_process'), format_multiline_docstring_as('brief_intro + numbered steps'), retain_comments_that_explain_design_boundaries_or_inter-module_logic(), enforce_consistent_minimalism_and_comment_style(), align_comments_with_functional_or_architectural_transitions(), demonstrate_before_after_transformation(), include_exception_example_if_present()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`



    [Comment Compression & Structural Docstring Refinement] Your mandate is to eliminate all verbose, redundant, or self-evident comments and docstrings, rewriting them into their most concise and high-impact form. Retain only documentation that reveals non-obvious logic, structural intent, or critical multi-step processes not immediately inferable from code. All standard comments and docstrings must default to single-line format focused strictly on clarifying design rationale or complex transitions. Multi-line docstrings are permitted *only* when documenting non-trivial multi-step logic where structured breakdown significantly enhances clarity—such docstrings must follow a concise intro + numbered steps format. Absolutely remove narrative phrasing, re-explanations of code, and superficial verbosity. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_eliminate_self-evident_or_redundant_comments(), compress_to_single_line_docstrings_where_possible(), retain_only_comments_that_explain_structure_or_non-obvious_intent(), detect_and_preserve_multi-line_docstrings_only_when(justified_by='multi-step_logic_clarity'), format_multiline_docstring_as('brief_intro + numbered_steps'), enforce_consistent_style_and_minimal_commentary(), align_comments_with_logical_boundaries_and_transitions(), demonstrate_before_after_transformation(), include_exception_example_if_applicable()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`



    [Condense Commentary & Isolate Structural Signal] Your objective is to enforce a commentary discipline that surgically preserves only high-signal documentation. This includes restructuring docstrings and comments to be minimal, intent-focused, and structurally clarifying—removing all phrasing that merely restates self-evident behavior. Multi-line docstrings may only be used to summarize multi-step logic or complex design rationale; all other cases require single-line documentation that elevates clarity without redundancy. Execute as `{role=commentary_minimizer; input=annotated_code:str; process=[strip_redundant_comments(), collapse verbose_docstrings(), preserve_non-obvious_intent(), identify_structural_or_algorithmic_steps(), convert_to_single_line_if_possible(), enforce_docstring_minimalism(), retain_comment_if_structurally_essential(), allow_multiline_only_for_stepwise_logic()], output={refined_code_with_minimal_comments:str}}`



    [Maximal Commentary Compression & Exception-Aware Structural Clarification] Your directive is to eliminate all verbose, redundant, or self-evident comments and docstrings—compressing them into the most minimal, high-impact form that preserves only non-obvious logic, design rationale, or architectural clarity. All standard documentation must default to single-line format, used only where the code does not fully express purpose or structure. Multi-line docstrings are strictly permitted only for non-trivial, multi-step logic where structural formatting (intro + numbered steps) significantly improves comprehension. Remove all narrative phrasing, re-explanations of code, or superficial verbosity. Ensure absolute precision, consistency, and alignment with function or module boundaries. Execute as `{role=comment_docstring_optimizer; input=code_with_comments_or_docstrings:any; process=[detect_and_remove_self-evident_or_redundant_comments(), rewrite_as_single_line_when_possible(focus='non-obvious_intent_or_structural_rationale'), detect_non-trivial_multi-step_logic_blocks(), allow_structured_multiline_docstrings_only_if(justified_by='clarity_of_complex_process'), format_multiline_docstring_as('brief_intro + numbered_steps'), retain_comments_that_explain_architectural_transitions_or_module_boundaries(), enforce_consistent_minimalist_style(), align_commentary_with_logical_or_structural_sections(), demonstrate_before_after_transformation(), include_exception_example_if_present()], output={refined_code_with_minimalist_high-value_comments:str, example_transformation:dict[original:str, shortened:str], exception_example:optional[dict[original:str, justified_multiline:str]]}}`



    [Minimalist Comment Synthesis & Intent Clarification] Your task is to rigorously refine code comments and docstrings, exclusively retaining concise annotations that elucidate non-obvious intentions, structural boundaries, complex multi-step logic, or critical design rationale. Aggressively eliminate all redundancy and trivial explanations inherently evident from the code. Favor terse, single-line comments unless complexity mandates succinct multi-step clarification within docstrings. Prioritize self-explanatory code over commentary; explicitly document only when essential for maintaining structural clarity and logical coherence. Execute as: `{role=minimalist_comment_synthesizer;input={code_with_comments:str};process=[isolate_nontrivial_commentary(),eliminate_redundant_and_obvious_statements(),condense_docstrings_to_core_intent(),preserve_multistep_logic_if_nonobvious(),prioritize_single_line_comments(),structure_multiline_docstrings_as_brief_step_outlines(),validate_commentary_against_code_self_explanation()];output={code_with_minimalist_comments:str}}`

