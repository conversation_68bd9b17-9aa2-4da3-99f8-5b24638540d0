# Code Guidelines

## Project Structure
This project follows a clean, modular architecture using modern Python tooling:

- **UV Package Manager**: For fast, reliable dependency management
- **Modular Design**: Clear separation of concerns between modules
- **Error Handling**: Robust error handling with detailed logging
- **Long Path Support**: Windows long path support for deep directory structures

## Development Standards

### Code Style
- Follow PEP 8 conventions
- Use Black for code formatting (line length: 88)
- Use meaningful variable and function names
- Keep functions focused and single-purpose

### Dependencies
- Minimize external dependencies
- Use UV for all package management operations
- Pin dependency versions in pyproject.toml
- Document any optional dependencies

### Error Handling
- Use try-catch blocks for external operations
- Provide meaningful error messages
- Log errors with appropriate detail levels
- Gracefully handle missing or corrupted data

### Documentation
- Include docstrings for all public functions
- Keep README.md updated with usage examples
- Document any breaking changes
- Maintain techstack.md with current technologies

## File Organization
`
chatgpt_export_to_markdown/
 src/
    __init__.py
    main.py              # Main entry point
    conversation_set.py  # Core data models
 pyproject.toml           # Project configuration
 uv_init.bat             # Environment setup
 run.bat                 # Application launcher
 README.md               # User documentation
 techstack.md            # Technical documentation
`

## Best Practices
- Use type hints where appropriate
- Implement progress bars for long operations
- Support both interactive and command-line usage
- Handle Windows path limitations properly
- Validate input data before processing
