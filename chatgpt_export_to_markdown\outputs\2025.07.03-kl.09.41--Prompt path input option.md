# Prompt path input option

Conversation ID: 68663431-e5ec-8008-b690-95dfac70cf37

## Message 1

please modify the following batch script such that it also prompts for the input path (but uses the current as default if user doesn't override)? keep the existing structure and functionality, but just add a prompt sp the user has the *option* to override input path:

```batch
:: =============================================================================

:: cmd: initialize

:: =============================================================================

@ECHO OFF

SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION

IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

SET "__init_path__=%CD%"

SET "__base_name__=%~n0"



:: =============================================================================

:: cmd: prompt for source path

:: =============================================================================

:PromptForSourcePath

ECHO.

ECHO Enter the source path for the backup.

ECHO Press ENTER to use the current directory as the default.

ECHO.

ECHO Default source: %CD%

ECHO.

SET "input_source_path="

SET /P "input_source_path=Enter source path: "



IF "!input_source_path!"=="" (

    SET "__init_path__=%CD%"

) ELSE (

    SET "__init_path__=!input_source_path!"

)



:: Clean up trailing backslash if present, as it can cause issues.

IF "!__init_path__:~-1!"=="\" SET "__init_path__=!__init_path__:~0,-1!"



:: Validate that the chosen source path actually exists before proceeding.

IF NOT EXIST "!__init_path__!" (

    ECHO.

    ECHO ERROR: Source path not found. Aborting operation.

    ECHO       Path: "!__init_path__!"

    GOTO EndScript

)



:: =============================================================================

:: robocopy: define arguments

:: =============================================================================

::

:: /COPY:DAT  - Copies Data, Attributes, Timestamps.

:: /DCOPY:T   - Copies directory Timestamps.

:: /E         - Copies subdirectories, including empty ones.

:: /V         - Verbose output (includes skipped files). Useful for logs.

:: /TS        - Includes source file Timestamps in output.

:: /FP        - Includes Full Path names of files in output.

:: /R:1       - Number of Retries on failed copies: 1.

:: /W:3       - Wait time between retries: 3 seconds.

SET "COPY_ARGS_SAFE=/COPY:DAT /DCOPY:T /E /V /TS /R:0 /W:3"



:: /XF        - Exclude Files matching these names/types.

:: /XD        - Exclude Directories matching these names.

:: Add or remove exclusions as needed.

SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /XF *.tmp *.pyc /XD venv node_modules __pycache__ .venv"



:: /LOG       - Create new log file (using unique name).

:: /NS        - No Size - don't log file sizes.

:: /TEE       - Outputs to console window as well as log file.

:: Uncomment the line below to enable logging

:: SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /NS /LOG:robocopy_log.txt /TEE"



:: =============================================================================

:: cmd: prompt for destination path

:: =============================================================================

:PromptForDestinationPath

ECHO.

ECHO Source path: !__init_path!

ECHO.

SET "destination_path="

SET /P "destination_path=Enter the destination path and press ENTER: "



IF "!destination_path!"=="" (

    ECHO.

    ECHO No destination path was provided. Aborting operation.

    GOTO EndScript

)



:: =============================================================================

:: cmd: process destination path

:: =============================================================================

:ProcessDestinationPath

SET "final_destination=!destination_path!"



:: Check if the input is a simple name (contains no path separators like \ or :).

:: If it is, we assume the user wants to create the backup alongside the source folder, not inside it.

ECHO "!destination_path!" | FINDSTR /R /C:"[\\:]" > NUL

IF ERRORLEVEL 1 (

    ECHO Detected simple name. Destination will be created in the parent directory.

    SET "final_destination=..\!destination_path!"

)



:: =============================================================================

:: robocopy: execute

:: =============================================================================

:ExecuteRobocopy

SET timer_start=%time:~0,8%

ECHO.

ECHO ============================================================================

ECHO Starting Robocopy...

ECHO.

ECHO     Source: !__init_path!

ECHO Destination: !final_destination!

ECHO.

ECHO Command: ROBOCOPY "!__init_path__!" "!final_destination!" %COPY_ARGS_SAFE%

ECHO ============================================================================

ECHO.



ROBOCOPY "!__init_path__!" "!final_destination!" %COPY_ARGS_SAFE%



SET timer_end=%time:~0,8%

ECHO.

ECHO ============================================================================

ECHO Robocopy finished.

ECHO.

ECHO - Start time : %timer_start%

ECHO - End time   : %timer_end%

ECHO ============================================================================

ECHO.



:: =============================================================================

:: cmd: exit

:: =============================================================================

:EndScript

ECHO Press any key to close this window...

PAUSE > NUL

EXIT /B
```

## Message 2

Here's your updated batch script with **optional user input for the source path**. It preserves your structure and adds a prompt allowing the user to override the current directory, while still defaulting to it if no input is provided:

```batch
:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__base_name__=%~n0"

:: =============================================================================
:: cmd: prompt for source path (with default)
:: =============================================================================
:PromptForSourcePath
ECHO.
ECHO Enter the source path for the backup.
ECHO Press ENTER to use the current directory as default.
ECHO.
ECHO Default source: %CD%
ECHO.
SET "input_source_path="
SET /P "input_source_path=Enter source path: "

IF "!input_source_path!"=="" (
    SET "__init_path__=%CD%"
) ELSE (
    SET "__init_path__=!input_source_path!"
)

:: Clean up trailing backslash if present
IF "!__init_path__:~-1!"=="\" SET "__init_path__=!__init_path__:~0,-1!"

:: Validate that the chosen source path actually exists
IF NOT EXIST "!__init_path__!" (
    ECHO.
    ECHO ERROR: Source path not found. Aborting operation.
    ECHO       Path: "!__init_path__!"
    GOTO EndScript
)

:: =============================================================================
:: robocopy: define arguments
:: =============================================================================
SET "COPY_ARGS_SAFE=/COPY:DAT /DCOPY:T /E /V /TS /R:0 /W:3"
SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /XF *.tmp *.pyc /XD venv node_modules __pycache__ .venv"
:: SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /NS /LOG:robocopy_log.txt /TEE"

:: =============================================================================
:: cmd: prompt for destination path
:: =============================================================================
:PromptForDestinationPath
ECHO.
ECHO Source path: !__init_path!
ECHO.
SET "destination_path="
SET /P "destination_path=Enter the destination path and press ENTER: "

IF "!destination_path!"=="" (
    ECHO.
    ECHO No destination path was provided. Aborting operation.
    GOTO EndScript
)

:: =============================================================================
:: cmd: process destination path
:: =============================================================================
:ProcessDestinationPath
SET "final_destination=!destination_path!"

ECHO "!destination_path!" | FINDSTR /R /C:"[\\:]" > NUL
IF ERRORLEVEL 1 (
    ECHO Detected simple name. Destination will be created in the parent directory.
    SET "final_destination=..\!destination_path!"
)

:: =============================================================================
:: robocopy: execute
:: =============================================================================
:ExecuteRobocopy
SET timer_start=%time:~0,8%
ECHO.
ECHO ============================================================================
ECHO Starting Robocopy...
ECHO.
ECHO     Source: !__init_path!
ECHO Destination: !final_destination!
ECHO.
ECHO Command: ROBOCOPY "!__init_path__!" "!final_destination!" %COPY_ARGS_SAFE%
ECHO ============================================================================
ECHO.

ROBOCOPY "!__init_path__!" "!final_destination!" %COPY_ARGS_SAFE%

SET timer_end=%time:~0,8%
ECHO.
ECHO ============================================================================
ECHO Robocopy finished.
ECHO.
ECHO - Start time : %timer_start%
ECHO - End time   : %timer_end%
ECHO ============================================================================
ECHO.

:: =============================================================================
:: cmd: exit
:: =============================================================================
:EndScript
ECHO Press any key to close this window...
PAUSE > NUL
EXIT /B
```

### ✅ Summary of changes:
- Added a prompt for source path with `%CD%` as default.
- Ensured trailing backslashes are stripped.
- Ensured the input is validated.
- Did not affect any other behavior of the script.

Let me know if you want similar prompts for the destination or log output as well.

## Message 3

'\\NAS\backup\folder' 
CMD does not support UNC paths as current directories.

## Message 4

You're absolutely right: **CMD does not support UNC paths (e.g. `\\NAS\backup\folder`) as the current working directory**. This limitation affects lines like:

```batch
CD /D "\\NAS\backup\folder"
```

which will result in an error like:

> "CMD does not support UNC paths as current directories."

---

### ✅ Fix: Use pushd instead of cd

You can solve this by replacing `CD /D` with `PUSHD`, which automatically maps UNC paths to a temporary drive letter. Here’s how to modify your script safely:

---

### ✅ Modify This Block:

Replace this:

```batch
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
```

With this:

```batch
:: Safely push the initial path (supports UNC paths)
IF EXIST "%~1" (PUSHD "%~1") ELSE (PUSHD "%~dp0")
SET "__init_path__=%CD%"
```

And then **at the very end of the script**, before exiting, restore the original path with:

```batch
POPD
```

---

### 🔁 Full Fix Context (Initial Section):

```batch
:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION

:: Support UNC paths using PUSHD
IF EXIST "%~1" (PUSHD "%~1") ELSE (PUSHD "%~dp0")
SET "__base_name__=%~n0"
```

### 🔚 Final Line of Script (after `PAUSE > NUL`):

```batch
POPD
EXIT /B
```

---

### Why this works:

- `PUSHD` maps UNC paths to a temporary drive letter (like `Z:`).
- `POPD` undoes that mapping when the script exits.
- No change needed elsewhere—you can continue using `!__init_path__!` as before.

Let me know if you’d like an example of how the mapped drive letter works behind the scenes, or if you'd like to display it to the user for logging purposes.

## Message 5

please show the full updated script

## Message 6

Here is your **fully updated batch script** that:

- Prompts the user for an optional source path (defaults to current directory).
- Uses `PUSHD` instead of `CD` to allow **UNC paths** (`\\NAS\share\folder`).
- Cleans trailing slashes.
- Restores the original working directory at the end using `POPD`.

---

```batch
:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION

:: Support UNC paths using PUSHD (maps to a temp drive if needed)
IF EXIST "%~1" (PUSHD "%~1") ELSE (PUSHD "%~dp0")
SET "__base_name__=%~n0"

:: =============================================================================
:: cmd: prompt for source path (with default)
:: =============================================================================
:PromptForSourcePath
ECHO.
ECHO Enter the source path for the backup.
ECHO Press ENTER to use the current directory as default.
ECHO.
ECHO Default source: %CD%
ECHO.
SET "input_source_path="
SET /P "input_source_path=Enter source path: "

IF "!input_source_path!"=="" (
    SET "__init_path__=%CD%"
) ELSE (
    SET "__init_path__=!input_source_path!"
)

:: Clean up trailing backslash if present
IF "!__init_path__:~-1!"=="\" SET "__init_path__=!__init_path__:~0,-1!"

:: Validate that the chosen source path actually exists
IF NOT EXIST "!__init_path__!" (
    ECHO.
    ECHO ERROR: Source path not found. Aborting operation.
    ECHO       Path: "!__init_path__!"
    GOTO EndScript
)

:: =============================================================================
:: robocopy: define arguments
:: =============================================================================
SET "COPY_ARGS_SAFE=/COPY:DAT /DCOPY:T /E /V /TS /R:0 /W:3"
SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /XF *.tmp *.pyc /XD venv node_modules __pycache__ .venv"
:: SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /NS /LOG:robocopy_log.txt /TEE"

:: =============================================================================
:: cmd: prompt for destination path
:: =============================================================================
:PromptForDestinationPath
ECHO.
ECHO Source path: !__init_path!
ECHO.
SET "destination_path="
SET /P "destination_path=Enter the destination path and press ENTER: "

IF "!destination_path!"=="" (
    ECHO.
    ECHO No destination path was provided. Aborting operation.
    GOTO EndScript
)

:: =============================================================================
:: cmd: process destination path
:: =============================================================================
:ProcessDestinationPath
SET "final_destination=!destination_path!"

:: If the input is a simple name (no \ or :), create the folder alongside the source path
ECHO "!destination_path!" | FINDSTR /R /C:"[\\:]" > NUL
IF ERRORLEVEL 1 (
    ECHO Detected simple name. Destination will be created in the parent directory.
    SET "final_destination=..\!destination_path!"
)

:: =============================================================================
:: robocopy: execute
:: =============================================================================
:ExecuteRobocopy
SET timer_start=%time:~0,8%
ECHO.
ECHO ============================================================================
ECHO Starting Robocopy...
ECHO.
ECHO     Source: !__init_path!
ECHO Destination: !final_destination!
ECHO.
ECHO Command: ROBOCOPY "!__init_path__!" "!final_destination!" %COPY_ARGS_SAFE%
ECHO ============================================================================
ECHO.

ROBOCOPY "!__init_path__!" "!final_destination!" %COPY_ARGS_SAFE%

SET timer_end=%time:~0,8%
ECHO.
ECHO ============================================================================
ECHO Robocopy finished.
ECHO.
ECHO - Start time : %timer_start%
ECHO - End time   : %timer_end%
ECHO ============================================================================
ECHO.

:: =============================================================================
:: cmd: exit
:: =============================================================================
:EndScript
ECHO Press any key to close this window...
PAUSE > NUL
POPD
EXIT /B
```

---

### ✅ Summary of Fixes
- `PUSHD`/`POPD` → supports UNC paths.
- Optional source path prompt.
- Safe fallback to `%CD%`.
- Trailing slash cleaned.
- Restores directory state on exit.

Let me know if you'd like to log which drive letter `PUSHD` maps to (e.g., `Z:`) or display it for the user.
