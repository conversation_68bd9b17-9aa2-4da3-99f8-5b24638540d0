# Stage Branching Refactor

Conversation ID: 684ad40c-d938-8008-b20c-da494d8490bc

## Message 1

please transform this such that each stage is branched from the root rather than the scattered and inconsistent way it's currently manifested:

```

├── core

│   ├── __init__.py

│   ├── __main__.py

│   ├── catalog.py

│   ├── config.py

│   ├── executor.py

│   ├── models.py

│   ├── utils.py

│   └── validation.py

├── docs

│   ├── 01_INSTRUCTION_PATTERNS.md

│   ├── 02_DIRECTIONAL_VECTORS.md

│   ├── 03_TEMPLATE_SPECIFICATION.md

│   ├── 04_SYSTEM_ARCHITECTURE.md

│   ├── 05_EXECUTION_GUIDE.md

│   ├── 06_DEVELOPMENT_GUIDE.md

│   ├── 07_QUICK_REFERENCE.md

│   ├── README.md

│   ├── RulesForAI.md

│   └── RulesForAI.minified.md

├── examples

│   ├── basic_usage.py

│   ├── cli_example.md

│   ├── declarative_template_creation.py

│   ├── stage_based_workflow.py

│   └── validate_templates.py

├── templates

│   ├── archive

│   │   ├── directional_vector_generator.py

│   │   ├── lvl1.md.generate.5000-5999.testing.py

│   │   ├── lvl1.md.generate.8000-8999.runway.py

│   │   ├── lvl1.md.generate.extractors.py

│   │   └── semantic_catalog.py

│   ├── md

│   │   ├── archive_legacy

│   │   │   ├── 1000-reflection_initializer.md

│   │   │   ├── 1000-reflection_initializer.testprompt

│   │   │   ├── 1001-directional_translator.md

│   │   │   ├── 1001-directional_translator.testprompt

│   │   │   ├── 1002-signal_resonator.md

│   │   │   ├── 1002-signal_resonator.testprompt

│   │   │   ├── 1003-reflection_initializer.md

│   │   │   ├── 1003-reflection_initializer.testprompt

│   │   │   ├── 1004-directional_translator.md

│   │   │   ├── 1004-directional_translator.testprompt

│   │   │   ├── 1005-signal_resonator.md

│   │   │   ├── 1005-signal_resonator.testprompt

│   │   │   ├── 1007-reflection_initializer.md

│   │   │   ├── 1007-reflection_initializer.testprompt

│   │   │   ├── 1008-directional_translator.md

│   │   │   ├── 1008-directional_translator.testprompt

│   │   │   ├── 1009-signal_resonator.md

│   │   │   ├── 1009-signal_resonator.testprompt

│   │   │   ├── 1010-b-title_extractor.md

│   │   │   ├── 1010-c-title_extractor.md

│   │   │   ├── 1010-d-title_extractor.md

│   │   │   ├── 1020-a-function_namer.md

│   │   │   ├── 1020-b-function_namer.md

│   │   │   ├── 1020-c-function_namer.md

│   │   │   ├── 1020-d-function_namer.md

│   │   │   ├── 1030-a-form_classifier.md

│   │   │   ├── 1031-a-form_classifier.md

│   │   │   ├── 1031-c-form_classifier.md

│   │   │   ├── 1031-d-form_classifier.md

│   │   │   ├── 2001-directional_translator.md

│   │   │   ├── 2002-reflection_initializer.md

│   │   │   ├── 3002-directional_translator.md

│   │   │   ├── 5998-a-meta_reflection_initializer.md

│   │   │   ├── 5998-b-directional_translator.md

│   │   │   ├── 5998-c-identity_mapper.md

│   │   │   ├── 5998-d-signal_resonator.md

│   │   │   ├── 5998-e-closure_deferment_unit.md

│   │   │   ├── 5999-a-directional_agent.md

│   │   │   ├── 8980-a-runway_prompt_generator.md

│   │   │   ├── 8980-b-runway_prompt_generator.md

│   │   │   ├── 8980-c-runway_prompt_generator.md

│   │   │   ├── 8980-d-runway_prompt_generator.md

│   │   │   ├── 8990-a-runway_prompt_generator.md

│   │   │   ├── 8990-b-runway_prompt_generator.md

│   │   │   ├── 8990-c-runway_prompt_generator.md

│   │   │   ├── 8990-d-runway_prompt_generator.md

│   │   │   ├── 9000-b-intensify.md

│   │   │   ├── 9000-c-diminish.md

│   │   │   ├── 9001-a-clarify.md

│   │   │   ├── 9001-b-purify.md

│   │   │   ├── 9001-c-obscure.md

│   │   │   ├── 9002-a-expand.md

│   │   │   ├── 9002-c-restructure.md

│   │   │   ├── 9003-a-elevate.md

│   │   │   ├── 9003-b-distill.md

│   │   │   ├── 9003-c-synthesize.md

│   │   │   ├── 9004-b-concretize.md

│   │   │   ├── 9004-c-transcend.md

│   │   │   ├── 9005-a-accelerate.md

│   │   │   ├── 9005-b-stabilize.md

│   │   │   ├── 9005-c-harmonize.md

│   │   │   ├── 9006-a-superpose.md

│   │   │   ├── 9006-b-entangle.md

│   │   │   └── 9006-c-collapse.md

│   │   ├── stage1

│   │   │   ├── 1000-test_evaluator.md

│   │   │   ├── 1001-content_synthesizer.md

│   │   │   ├── 1006-data_metamorphosis.md

│   │   │   ├── 1010-a-title_extractor.md

│   │   │   └── 1031-b-form_classifier.md

│   │   ├── stage2

│   │   │   └── 2000-reflection_initializer.md

│   │   ├── stage3

│   │   │   └── 3001-reflection_initializer.md

│   │   ├── 9000-a-amplify.md

│   │   ├── 9002-b-compress.md

│   │   └── 9004-a-abstract.md

│   ├── __init__.py

│   ├── catalog.py

│   ├── lvl1.md.templates.json

│   ├── stage1_prototypes.py

│   ├── stage2_candidates.py

│   └── stage3_production.py

├── .gitignore

├── CHANGELOG.md

├── CRYSTALLIZATION_SUMMARY.md

├── LICENSE

├── README.md

├── requirements.txt

└── setup.py

```

## Message 2

Understood. The objective is to **refactor the folder structure** such that **each stage (e.g., stage1, stage2, stage3)** is **branched directly from the root** rather than scattered under deeply nested folders (like `templates/md/` or `templates/archive/`). This will help enforce a **cleaner and more centralized hierarchy per stage**, with logical segregation by stage instead of format or origin.

---

### ✅ REFACTORED STRUCTURE (STAGE-BRANCHED FROM ROOT)

```
├── stage1
│   ├── 1000-test_evaluator.md
│   ├── 1001-content_synthesizer.md
│   ├── 1006-data_metamorphosis.md
│   ├── 1010-a-title_extractor.md
│   ├── 1031-b-form_classifier.md
│   ├── stage1_prototypes.py
│   └── legacy
│       ├── [all stage1-related legacy md/testprompt files]
├── stage2
│   ├── 2000-reflection_initializer.md
│   ├── stage2_candidates.py
│   └── legacy
│       ├── [all stage2-related legacy md/testprompt files]
├── stage3
│   ├── 3001-reflection_initializer.md
│   ├── stage3_production.py
│   └── legacy
│       ├── [all stage3-related legacy md/testprompt files]
├── core
│   ├── __init__.py
│   ├── __main__.py
│   ├── catalog.py
│   ├── config.py
│   ├── executor.py
│   ├── models.py
│   ├── utils.py
│   └── validation.py
├── templates
│   ├── directional_vector_generator.py
│   ├── semantic_catalog.py
│   ├── lvl1.md.templates.json
│   ├── 9000-a-amplify.md
│   ├── 9002-b-compress.md
│   ├── 9004-a-abstract.md
│   ├── archive
│   │   ├── lvl1.md.generate.*.py
│   │   ├── md/
│   │   ├── archive_legacy/
├── docs
│   ├── 01_INSTRUCTION_PATTERNS.md
│   ├── 02_DIRECTIONAL_VECTORS.md
│   ├── 03_TEMPLATE_SPECIFICATION.md
│   ├── 04_SYSTEM_ARCHITECTURE.md
│   ├── 05_EXECUTION_GUIDE.md
│   ├── 06_DEVELOPMENT_GUIDE.md
│   ├── 07_QUICK_REFERENCE.md
│   ├── README.md
│   ├── RulesForAI.md
│   └── RulesForAI.minified.md
├── examples
│   ├── basic_usage.py
│   ├── cli_example.md
│   ├── declarative_template_creation.py
│   ├── stage_based_workflow.py
│   └── validate_templates.py
├── .gitignore
├── CHANGELOG.md
├── CRYSTALLIZATION_SUMMARY.md
├── LICENSE
├── README.md
├── requirements.txt
└── setup.py
```

---

### 🔧 KEY RESTRUCTURING CHANGES

- `stage1/`, `stage2/`, `stage3/` are now **first-class folders** at the root level.
- Legacy `.md` and `.testprompt` files have been moved under `stageX/legacy/` where appropriate, to preserve historical materials.
- Template scripts like `stage1_prototypes.py`, etc., moved to respective stage folders.
- Central template assets like `lvl1.md.templates.json` and other general `.md` files remain under `templates/`.

---

### 🧠 NEXT ACTION OPTIONS

- !transform [another tree] – Refactor another structure
- !generate-index – Generate indexed README.md per stage folder
- !flatten-all – Collapse all nested folders into flat layout
- !extract-stage-mapping – Map all templates by stage into CSV
- !help – Get full command reference

Would you like a script to automatically perform this transformation on disk, or just need this plan as a reference?
