# AI Skeleton-based Image Generation

Conversation ID: 682c6886-9128-8008-8ca9-bef44505b3ac

## Message 1

{'content_type': 'image_asset_pointer', 'asset_pointer': 'file-service://file-JamQq9th94ATPE8vobzX4R', 'size_bytes': 14844, 'width': 650, 'height': 687, 'fovea': None, 'metadata': {'dalle': None, 'gizmo': None, 'generation': None, 'container_pixel_height': None, 'container_pixel_width': None, 'emu_omit_glimpse_image': None, 'emu_patches_override': None, 'sanitized': True, 'asset_pointer_link': None, 'watermarked_asset_pointer': None}}
# Context

when working on ai-assisted image and video generation, what are the recent trends with regards to generating images/videos based on an input "skeleton" (e.g. animated black/white alpha to make the generated output use the skeleton/bw-alpha only as input from which to guide the ai?

I've attached an image to illustrate the concept:

White Alpha (Circle Area): Represents a circular lens or portal.

Black Alpha (Background): Represents the environment seen through (or around) this circular element.


# Example

Visual Concept & Style:

1. The Circle (White Alpha): Treat it as a highly realistic, transparent lens or portal. Include subtle reflections or refractions indicating glass or polished metal. Add cinematic lighting effects—soft glows, faint lens flares, and sharp highlights where the light source hits the lens surface.


2. The Environment (Black Alpha): Depict a dramatic, immersive scene visible through and around the circle. Go for a cinematic, hyper-realistic vibe. Consider any setting that resonates with the idea of looking through a lens or portal.


3. Animation/Video Movement (if generating a video): Start with the circular lens in close-up, then gradually reveal the environment in the background. Incorporate subtle camera panning or dolly movement to emphasize three-dimensionality.


## Message 2

Thanks for the context and the visual reference—it’s very helpful. To tailor the research accurately, could you clarify a few things:

1. Are you looking specifically for **AI models or tools** (e.g., ControlNet, AnimateDiff) that support image/video generation guided by skeleton or alpha masks?
2. Should the research focus on **open-source solutions**, **commercial platforms**, or both?
3. Are you interested in **2D images**, **3D scenes**, **video animation**, or a combination of these?
4. Do you need coverage of **implementation-level details** (like codebases, pipelines, model weights) or a higher-level overview of current capabilities and trends?

Once I have these details, I’ll get started right away.

## Message 3



I'm already using multiple ai image and video generators, but probably runway the most. The reason for my inquiry is that i want to develop a systematic, generalized and concistent way of expressing the prompts such that i can produce my own animated bw-inputs (i work as an animator and prompt engineer) and pass them in as "skeletons" (completely regenerate, but to do so guided by the animated input) to concistently produce high-quality videos *that use the inputs only as guide. 

Please write a generalized prompt template that embodies these concepts inherently (to use in combination with a bw-input).The goal of the instruction templates is to demonstrate this ability when used as prompt with an accompanied bw-input guide/skeleton (such as the provided example)

Be less explicit with regards to what the shapes represent (but rather emphasize a generalized direction towards a professional artist/photographer/videographer), put more focus in on inherently conveying llm-optimized rules/instructions that ensures correct interpretation/use of the attached bw-inputs

We need to take everything collectively learned within the topic and leverage the generalized interpretations og the concepts. Instead of trying to reinvent the wheel by generating completely new sequences/approaches, lets rather leverage everything we can and apply it.





## Message 4

Great, I’ll develop a generalized, professional-grade prompt template that guides AI image/video generation tools (such as Runway or others) to interpret black-and-white skeleton inputs correctly. The goal is to regenerate high-quality, cinematic output that uses the input purely as a spatial or compositional guide while allowing stylistic freedom in the generated content.

I'll focus on recent techniques and language patterns from current tools, models, and professional practices that support this approach. I’ll be back shortly with a well-structured template and guidance for prompt engineering in this context.

## Message 5

# Generalized Prompt Template for Cinematic AI Generation with B/W Skeleton Guides

Professional AI animators can leverage **black-and-white “skeleton” inputs** (abstract line art, masks, or alpha mattes) as structural guides for image/video generation. The key is to write prompts that **treat the white regions of the input as spatial guides** – like frames, motion paths, or portals – **without interpreting them as literal objects or shapes**. This prompt template and guidelines help maintain a consistent cinematic style and camera logic across generations, while keeping semantic content flexible. We break down the prompt into modular **slots** (e.g. [Scene Mood], [Visual Style], [Camera Movement]) that you can fill in to adapt to different scenes and genres.

## Master Prompt Template

**Template Structure:** Use a series of concise, descriptive phrases (not long narration) to specify each aspect. Comma-separated keywords and short clauses help the visual model focus on key features【14†L315-L324】. Below are the main prompt slots and how to use them:

- **[Scene Mood]** – *Adjectives conveying the emotional tone or atmosphere.* For example: *mysterious, tranquil, ominous, hopeful, whimsical, tense, epic.* This sets the overall feel of the scene.

- **[Scene Subject/Action]** – *A brief description of the scene’s primary subject or action, without over-specifying.* Describe **what is happening or the main focus** in general terms (e.g. *“a lone figure walking through an ancient forest”*, *“two ships converging in a stormy sea”*, *“city streets bustling with nightlife”*). Imply story elements without fully narrating【14†L319-L327】 – *include hints of narrative or motion to make the scene dynamic, but avoid lengthy exposition*.

- **[Setting/Environment]** – *(Optional)* If not already clear from the subject, add a setting or location phrase. For instance: *“in a foggy mountain valley”*, *“beneath neon-lit skyscrapers”*, *“on a desolate alien planet”*. This grounds the scene context.

- **[Visual Style]** – *Artistic or cinematographic style descriptors to ensure consistency.* Choose a style or genre and stick to it across prompts for a unified look. Examples: *“photorealistic 4K live-action”*, *“film noir, high-contrast”*, *“Studio Ghibli-inspired animation”*, *“surrealist painting style”*, *“documentary realism, grainy film”*, *“futuristic cyberpunk aesthetic”*. This slot controls color palette and overall imagery vibe.

- **[Lighting Design]** – *Describe the lighting and color mood.* Lighting is crucial for cinematic quality, so include terms like: *“soft golden hour sunlight”*, *“moody underlit shadows”*, *“dim blue twilight with streaks of light”*, *“harsh neon backlighting”*, *“volumetric rays through fog”*. Consistent lighting across prompts helps maintain visual continuity.

- **[Camera Angle & Movement]** – *Describe the camera perspective and motion to add cinematic dynamism.* For instance: *“wide-angle establishing shot”*, *“low-angle view looking up”*, *“over-the-shoulder perspective”*, *“handheld camera follows the subject”*, *“slow dolly-in toward the subject”*, or *“sweeping panoramic pan across the scene”*. Including camera behavior prompts the model to think in cinematic terms (e.g. Runway’s Gen-4 example: *“a handheld camera tracks the mechanical bull as it runs across the desert… cinematic live-action.”*【40†L79-L82】). These phrases encourage a sense of movement and depth (parallax) in the generated output. You can even mention focus shifts (a **refocus** effect) like “the camera rack-focuses from foreground to background” to suggest dynamic focus changes, though results may vary by model.

- **[Structure Guide Interpretation]** – *Instruct the model how to treat the white **skeleton input** shape in the composition.* This is crucial: **describe the white regions as abstract framing or motion guides** rather than objects. For example, if your B/W guide is a **white silhouette** or shape:
  - Use terms like *“framed by [shape]”*, *“visible through a [shape] portal”*, *“within a [shape] spotlight”*, or *“following a [shape] path”*. 
  - **Do NOT** explicitly call it a specific object unless intended; instead describe its *function*: e.g. *“a scene viewed through a circular aperture of light”* rather than “a white circle object in the scene.” This way the model uses that shape as a lens or window for the scene, **not a literal solid item**. 
  - If the shape suggests motion or direction (e.g. an arrow-like streak), you might say *“along a streak of light guiding the motion”*. For a human-like stick figure guide, you might say *“an ethereal outline guiding the character’s movement”* instead of naming it a stick figure. The goal is to **integrate the shape aesthetically** into the scene (as lighting, framing, motion trail, etc.) so the AI respects the composition but fills in its own semantic content【17†L66-L72】.

- **[Atmosphere & Ambience]** – *Extra details to enrich the scene and create **ambient motion**.* Mention environmental elements that can move or set mood: *“swirling mist”*, *“dust motes floating in the shafts of light”*, *“raindrops and puddle reflections”*, *“leaves drifting on the breeze”*, *“embers and smoke in the air”*. These not only add cinematic flair but also give the model hints of **ambient movement**, contributing to that subtle parallax and depth. For example, dust trailing behind a running subject emphasizes motion【40†L79-L82】. Such details support the storytelling without specifying new objects.

- **[Cinematic Tone & Quality]** – *General cinematic adjectives to tie it all together.* You can explicitly include terms like *“cinematic”*, *“film still”*, *“ultra high definition”*, *“masterful cinematic composition”*, *“dynamic and immersive”*. Also, lens effects: *“shallow depth of field (bokeh)”*, *“lens flare”*, *“motion blur”* (for action), *“film grain”*, *“anamorphic lens”* can be added here to reinforce the look. These help ensure the output feels like a frame from a high-quality film.

Using these slots, you can construct a prompt that covers mood, content, style, lighting, camera, and the interpretation of your B/W guide. **Focus on strong visual keywords and avoid overly long sentences** – this improves the model’s understanding【14†L315-L324】. You can always iterate by adding or refining one element at a time (e.g. first get the composition right with a basic prompt, then add more style or atmosphere in increments).

**Template Example:** *(fill in or adjust each slot as needed; keep the overall structure)*

```
[Scene Mood], [Scene Subject/Action] [Setting], [Lighting], [Visual Style] style, 
composed within [Structure Guide Interpretation], [Camera Angle/Movement], 
[Atmosphere/Ambient details], cinematic, high-quality.
```

*For instance:* `Serene and mysterious, a lone figure wanders an ancient forest at dusk, soft blue twilight with misty shafts of light, photorealistic cinematic style, viewed through a circular glowing portal in the dark, wide-angle shot with slow dolly movement, faint fog rolling between gnarled trees, cinematic 4K detail.` – In this example, the **white circular shape** in the input is described as a *“circular glowing portal in the dark”* (so the model treats it like a magical light frame), and we included mood (*serene, mysterious*), subject (*figure in forest*), setting (*ancient forest at dusk*), lighting (*soft blue twilight, mist*), style (*photorealistic cinematic*), camera (*wide-angle, slow dolly*), and atmosphere (*fog*). The prompt uses commas to separate ideas and create a clear, evocative image for the AI.

## Interpreting the B/W Skeleton Input in Prompts

【39†embed_image】 *Example:* A simple black-and-white **skeleton guide** (white circle on black) can be treated as an **abstract framing device** in the prompt. Rather than literally describing “a white circle,” you might write *“the scene is visible through a circular portal of light”*. This tells the AI to use the circular shape as a **lens or window** into the scene. Likewise, a jagged white shape could be a *“shattered light frame”* or *“an opening crack through which the scene emerges.”* The idea is to **embrace the shape’s geometry as a creative constraint** – a part of the composition – while letting the model fill it with appropriate content. By instructing the model in this way, *the white portions act as interpretive spatial guides (framing or directing the composition) and not as literal objects to be identified*. Research on sketch-guided generation shows that treating drawn shapes as loose guides yields more realistic results than forcing exact literal forms【17†L66-L72】. In practice, **your prompt should explicitly mention the presence of the shape as a framing/portal or motion guide**, so the AI knows to incorporate it. If you omit it entirely, the model might still use the ControlNet/guide input, but describing it in the prompt can reinforce alignment between your intent and the model’s interpretation.

**Tips for using the B/W guide:**

- Ensure the **input image is high-contrast** (clear white on black) and free of extra artifacts, so the model gets a clean structural cue【40†L29-L37】【40†L30-L34】. A high-quality guide helps the AI “understand” the composition better.

- In the prompt, **refer to the shape’s role** early if it’s crucial to composition. For example: *“inside a **bright oval frame of light**, [Scene Subject]…”* or *“framed by **towering white arches** (from shape), the scene…”*. By mentioning it early, you signal its importance as part of the scene setup.

- **Stay abstract:** Don’t assign a specific identity to an ambiguous shape. If your skeleton is just a blob or splash, describe it in terms of effect (e.g. *“a glowing haze in that form”*, *“an abstract portal shape”*). This avoids misinterpretation. As one sketch-to-image study notes, strict adherence to rough shapes can lead to misshapen objects; it’s better to let the model interpret the shape as a *rough guideline* for form and composition【17†L66-L72】 rather than an exact outline to fill in.

- **Test and iterate:** After generating, you might find the shape wasn’t used exactly as intended. Try tweaking the [Structure Guide] phrase – perhaps the shape should be more/less emphasized. Words like *“subtle”* or *“glowing”* can make the frame less concrete, whereas *“massive”* or *“solid”* might make it a more dominant physical object. Adjust to get the desired balance.

## Maintaining Consistent Cinematic Style

To achieve professional, coherent results, maintain consistency in style and cinematography across all your prompts in a project. This means reusing or only gently adjusting certain slots:

- **Visual Style & Lighting:** Pick a specific visual style and lighting scheme and use it in every prompt (unless deliberately changing for a story reason). For example, if you decide on *“dark, moody cyberpunk neon, photorealistic”*, keep those phrases constant. This ensures each generated scene feels like part of the same film or universe.

- **Camera Logic:** It helps to imagine you are the director/camera operator for all these AI-generated shots. Decide on a general camera approach (e.g. always handheld and up-close, or always smooth wide-angle panoramic). By consistently saying, for instance, *“handheld camera, documentary style”* in each prompt, or always using *“wide establishing shot”*, you establish a common viewpoint. Consistent camera perspective prevents jarring shifts between outputs.

- **Color Palette:** If one scene is warm and another is cool with no narrative reason, the outputs may feel disjointed. Use your lighting and style descriptors to keep color tones aligned. For example, *“sepia-toned, sun-drenched lighting”* across multiple prompts will yield a series of images/videos with a similar palette.

- **Recurring Descriptive Keywords:** If your content varies (one prompt is forest, another is city), you can still thread them together with recurring keywords that reflect the **theme or mood**. For instance, a theme of *mystery* might mean each prompt includes *“shadowy”*, *“foggy”*, or *“glowing lights in darkness”* even in different settings. This provides a subtle continuity of atmosphere.

By following these practices, you essentially create a **prompt formula** that can be adapted for different scenes but still *“feels” cohesive*. Prompt engineers often iterate in this way: lock down a base style prompt, then swap out subjects or specific story elements as needed. *“Providing manual annotations (like sketches or masks) lets users actively shape generation while the model still fills in details to align with the user’s vision【29†L29-L37】. Similarly, a consistent prompt framework helps ensure the AI’s interpretations align with your creative vision across iterations.”*

## Genre-Specific Variants and Examples

Finally, here are **optional variant guidelines** for different genres or use cases. These demonstrate how you might adjust the template’s slots for a particular style, while still leveraging the B/W skeleton guide effectively:

### Surreal / Dreamlike Variant
For a surreal, artistic result, emphasize imaginative and atmospheric terms:
- **Mood:** Dreamlike, ethereal, haunting, whimsical, otherworldly. (e.g. *“dreamy and ethereal”*)
- **Subject/Scene:** Can be abstract or metaphorical. (e.g. *“floating islands merging into clocks”* or *“figures with melting faces dancing in the sky”* – something that doesn’t fully make sense, which the AI can interpret creatively.)
- **Visual Style:** Surrealist painting, fantasy illustration, or cinematic but with a twist (e.g. *“Dalí-inspired surreal style”*, *“psychedelic art film”*, *“vibrant fantasy cinematic”*).
- **Lighting:** Soft, surreal lighting – perhaps colored lighting (e.g. *“neon twilight glow”*, *“irradiated pastel haze”*) to create an uncanny atmosphere.
- **Camera:** A floating or impossible camera angle (e.g. *“impossible overhead perspective slowly rotating”* or *“fluid dreamlike camera movement”*). In surreal scenes, the camera might not obey real-world constraints – feel free to describe it doing magical motions.
- **Structure Guide Usage:** The B/W guide shape can be a *portal to a dream realm*, a *mirror-like frame*, or a *halo around subjects*. For example, if the shape is a spiral, *“a spiral vortex opens at the center, revealing the scene”*. Emphasize that the shape is part of the dream imagery.
- **Ambience:** Add plenty of magical or bizarre environmental details (floating particles, glowing symbols in the air, distortions). This gives the model freedom to be creative within the structural guide. 

*Example Prompt (Surreal):*  
`Whimsical and eerie, a figure with a clock for a face drifts through a landscape of floating islands, soft neon twilight illumination, surrealist painting style, the scene seen through a swirling misty portal (white spiral guide) at the center, camera rotates slowly upside-down, glowing particles and liquid droplets float upward, cinematic and otherworldly.`

### Documentary / Realism Variant
For a documentary or realistic style (where the AI output should feel like real footage or a nature documentary):
- **Mood:** Realistic, gritty, intimate, or straightforward. (e.g. *“calm and observational”*, *“tense and raw”* if investigative)
- **Subject/Scene:** Likely real-world scenarios or nature. (e.g. *“lions hunting on the savanna”*, *“a busy marketplace at noon”*, *“an interview with soft lighting”*)
- **Visual Style:** Use *“documentary film style”*, *“cinematic but realistic”*, *“analog film grain”*. You might add *“in the style of BBC nature documentary”* or *“handheld camcorder look”* depending on the vibe. Keep it photoreal.
- **Lighting:** Natural lighting appropriate to the scene (sunlight, indoor practical lighting). Avoid overly stylized lighting; instead use terms like *“soft natural light”*, *“harsh midday sun”*, *“dim indoor light”* etc.
- **Camera:** Often handheld or static observational angles. (e.g. *“steady handheld camera at eye level”*, *“panning slowly as if filmed from the shoulder”*). This conveys a human camera operator presence. You might also specify lens type if needed (documentaries often use normal lenses, not extreme wide or telephoto unless specifying).
- **Structure Guide Usage:** The B/W shape here could represent a physical frame or viewing window in the environment (for instance, a circular guide could be *“the view through binoculars”* or a *“camera lens vignette”*). If the shapes are abstract, you might tone down their presence: *“subtle out-of-focus foreground element”* (if the shape is something like a blob at the edge) so it feels like a camera artifact. Essentially, integrate the shape as if *the documentary camera* had some framing through it (like viewing through bushes, etc.).
- **Ambience:** Include realistic ambient motion – e.g. *“grass swaying in the wind”*, *“crowd moving in the background”*, *“dust in the air”*. This aligns with how documentaries capture environmental movement. 

*Example Prompt (Documentary):*  
`Calm and natural, a herd of elephants crosses a river at dawn, soft golden morning light, documentary film style with grain, viewed through a circular binocular vignette (white guide as binocular frame), handheld camera at eye-level follows slowly, water splashes and birds flying through the frame, cinematic realism.`

*(In this example, the white circular guide is described as a binocular vignette – encouraging the AI to use it as the dark round framing you often see when looking through binoculars in documentaries.)*

### Action Cinematic Variant
For high-octane action scenes:
- **Mood:** Intense, dramatic, adrenaline-filled. (e.g. *“intense and fast-paced”*, *“urgent, high tension”*)
- **Subject/Scene:** Chase scenes, battles, explosions, etc. (e.g. *“a car chase through narrow streets”*, *“a medieval battle raging on a field”*)
- **Visual Style:** Big-budget action movie style. Terms like *“Hollywood blockbuster, ultra-realistic”*, *“high dynamic range action style”*, *“Michael Bay-esque”* (if you want lens flares and saturation). You can include *“slow-motion cinematic moments”* if desired.
- **Lighting:** Strong contrasts, maybe fire and shadows, or bright stage lights – whatever suits the scene (e.g. *“flickering orange firelight and deep shadows”*, or *“bright stadium lights and motion blur”*).
- **Camera:** Very dynamic. Use terms like *“rapid tracking shot”*, *“shaky handheld cam”* (for chaotic feel), *“aerial chase cam”*, *“dramatic zoom”*. For example: *“the camera whips around with the fighter”* or *“a shaky cam chase view”*. Including **motion blur** cues can help (though not all models handle motion blur well in static images, video models might).
- **Structure Guide Usage:** Interpret the white shapes as **motion trails, impact flashes, or dynamic framing**. For instance, if the guide is a streak or curve, call it *“a trail of light following the speeding car”* or *“shockwave rings emanating from the impact”*. If it’s a shape in the center, maybe it’s an explosion flash or a spotlight. Essentially, tie the shape to the action – this prevents the model from making it a random object and instead uses it to enhance the sense of motion or impact.
- **Ambience:** Lots of debris, sparks, rain, smoke, etc. Anything that conveys movement. E.g. *“dust and debris flying”*, *“rain pouring and splashing”*, *“sparks and embers in the air”*. These elements underscore the chaos of action and also serve the **parallax** idea (foreground debris vs background action).

*Example Prompt (Action):*  
`Intense and chaotic, two cars race and collide on a wet highway at night, glistening neon reflections on slick pavement, ultra-realistic blockbuster style, white-hot sparks fly in a circular burst (white guide shape as explosion flash) at the moment of impact, camera shakes and pans rapidly to follow the crash, motion blur and debris across the frame, cinematic high drama.`

*(Here the white circular shape is used as an explosion flash/sparks effect. The prompt conveys fast camera movement and uses blur and debris to emphasize speed. The mood and style are consistently “blockbuster”.)*

### Sci-Fi / Futuristic Variant
For science fiction or futuristic scenes:
- **Mood:** Awe-inspiring, mysterious, high-tech, or ominous. (e.g. *“awe-struck and futuristic”*, *“tense and dystopian”*)
- **Subject/Scene:** Spacecraft, alien worlds, advanced technology, etc. (e.g. *“a colossal spaceship emerges from hyperspace”*, *“cyberpunk cityscape with flying vehicles”*)
- **Visual Style:** Sci-fi film style. References like *“Blade Runner style neon city”*, *“NASA footage realism”*, *“futuristic utopia aesthetic”*. You might use *“CGI cinematic”* or *“hyper-detailed sci-fi concept art”* depending on whether you want it realistic or illustrative. Consistency is key: decide if it’s gritty and realistic or clean and stylized future.
- **Lighting:** Often dramatic and colored (neon glows, holographic light, dark shadows with LED accents, etc.). E.g. *“glowing blue holographic light sources”*, *“green digital code reflections”*, *“stark white laboratory lighting”*.
- **Camera:** Could be expansive (to show scale) or immersive. For instance: *“wide shot, camera slowly tilting up to reveal a massive starship”*, or *“first-person POV through a helmet visor”*. You can incorporate **parallax** by describing layers of technology in foreground vs background (e.g. *“multiple depth layers of flying cars at different distances”*).
- **Structure Guide Usage:** The B/W shapes can be utilized as sci-fi elements: a white outline might be a *holographic interface* or *forcefield*, a white frame could be a *portal or Stargate*, lines could be *laser beams or trajectory arcs*. Frame it in the prompt accordingly: *“a circular stargate opens (white shape) revealing the scene”*, or *“energy beams (following the white paths) connect the structures in frame”*. This way the model integrates the guides as futuristic tech or phenomena.
- **Ambience:** Sci-fi atmosphere might include things like *“floating digital particles”*, *“rain in neon city lights”*, *“foggy air with searchlights”*, *“stars and cosmic dust drifting”*. These give motion and context appropriate to sci-fi.

*Example Prompt (Sci-Fi):*  
`Majestic and futuristic, a colossal spacecraft hovers above a neon-lit megacity skyline, glowing holographic billboards and flying vehicles trail light, hyper-detailed sci-fi cinematic style, the scene framed by a circular energy portal (white guide used as futuristic portal) in the foreground, camera pans upward to reveal the ship’s scale, volumetric fog with searchlights piercing through, cinematic and awe-inspiring.`

*(In this example, the white circular guide becomes an “energy portal” in the scene, and the prompt maintains a consistent high-tech style with neon, holograms, and a sweeping camera motion.)*

---

**Final recommendations:** Keep prompts **modular and iterative**. Start simple – for example, *“a dramatic scene, cinematic lighting, seen through a round portal”* – and check if the model uses your B/W guide well. Then incrementally add complexity: mood adjectives, more specific style cues, detailed atmospheric elements. This stepwise approach is suggested by experts (e.g., Runway Gen-4 recommends beginning with basic motion then adding elements one by one【40†L60-L69】【40†L71-L75】). By refining in stages, you can zero in on the effect of each prompt component. 

Remember, the **black-and-white skeleton is your co-director**: it provides composition and movement, while your text prompt provides the **story and cinematic direction**. When used together thoughtfully, you “actively shape the generation process” with sketches and prompts, bridging the gap between your creative intent and the AI’s output【29†L29-L37】【29†L84-L89】. With a strong prompt template and clear vocabulary, you can drive AI image/video models to produce coherent, cinematic scenes guided by nothing more than abstract lines and shapes – a powerful new workflow for animation and content creation. Enjoy experimenting with your custom skeleton-guided cinematic prompts! 

**Sources:** 

- Segmind Blog – *Guide to ControlNet Scribble* (on using sketches to guide image generation)【29†L29-L37】【29†L84-L89】  
- Tang et al. – *Block and Detail: Scaffolding Sketch-to-Image Generation* (on treating rough sketches as guides, not exact shapes)【17†L66-L72】  
- OpenArt – *How to Write Cinematic Prompts* (prompt keyword vs. narrative tips)【14†L315-L324】【14†L319-L327】  
- Runway Gen-4 Documentation – *Prompting Guide* (text-to-video prompting with camera and motion examples)【40†L79-L82】【40†L60-L69】

## Message 6

Runway still doesn't understand that the bw input is only meant as guide. You haven't mentioned anything fundamental; you're just adding in uncontrolled complexity. The most integral/crucial component to address is the way in which we can convey the interpretation such that it concistently is able to perform the intended actions. What existing knowledge have you applied? Which simple and effective techniques/concepts have we missed?


## Message 7

You're absolutely right—the core challenge lies in ensuring that Runway interprets black-and-white (B/W) inputs as **guides** rather than literal content. Despite Runway's flexibility, it doesn't inherently distinguish between structural guides and semantic elements unless explicitly instructed.

---

### 🧠 Fundamental Concepts to Emphasize

1. **Explicitly Define the Role of B/W Inputs**: Clearly state in your prompt that the B/W input serves as a structural guide, not as content to be rendered. For example:

   > "Use the provided black-and-white mask solely as a compositional guide; do not render the mask itself in the output."

2. **Leverage ControlNet Techniques**: ControlNet allows for conditioning the generation process on additional inputs like edge maps or pose estimations. By integrating similar conditioning methods, you can guide the model more effectively citeturn0search1.

3. **Simplify and Focus Prompts**: Avoid complex or overly detailed prompts that might confuse the model. Instead, use clear, concise instructions that focus on the desired outcome.

---

### 🛠️ Practical Prompt Template

Here's a refined prompt structure to guide Runway's interpretation:

> "Generate a [desired scene] using the accompanying black-and-white image as a structural guide. The white areas indicate regions of interest or focus, while black areas represent background or non-essential regions. Do not render the guide itself; instead, use it to inform composition and spatial arrangement."

Replace `[desired scene]` with your specific content, such as "a bustling cityscape at night" or "a serene forest landscape."

---

### 🔄 Iterative Refinement

If the model continues to misinterpret the B/W input, consider the following steps:

- **Adjust the Guide Image**: Ensure that the guide is clear, with high contrast between black and white regions.

- **Modify the Prompt**: Reiterate the role of the guide in different terms, such as "structural map," "layout guide," or "composition template."

- **Combine with Additional Inputs**: If possible, provide supplementary inputs like sketches or reference images to reinforce the intended structure.

---

By focusing on these strategies, you can enhance Runway's ability to interpret B/W inputs as guides, leading to more consistent and desired outputs. 

## Message 8

Please write a finalized prompt example based on using the circular bw as input with runway, do your best to guarantee the input is concistently interpreted correct (as a guide from which to use as "anchors")

## Message 9

Certainly. Here's a **finalized prompt example** specifically crafted for Runway (e.g. Gen-2 or Gen-3 video models), designed to **consistently guide the model to treat a circular black-and-white input as a compositional anchor**, not literal content.

This prompt emphasizes *how* the input should be interpreted, using clean, directive language optimized for current AI behavior:

---

### **Prompt Example (for use with circular B/W input):**

> **"Cinematic shot of [scene subject or mood], composed using the attached black-and-white input as a spatial guide. Use the white area strictly as a compositional anchor or framing zone—do not generate or render the shape itself. Treat the circular white region as an abstract focus area, like a lens, portal, or spatial window. Use it only to guide depth, subject placement, and camera focus. The black background represents negative space and should not be visible in the final output. Maintain a naturalistic, photorealistic style with dramatic lighting and soft ambient motion. Interpret the input only as a layout map for subject framing, not as a visual element to include."**

---

### **Example with inserted content:**

> **"Cinematic shot of a lone explorer stepping into a glowing forest at twilight, composed using the attached black-and-white input as a spatial guide. Use the white area strictly as a compositional anchor—do not render the circular shape. Treat it as a transparent framing zone, like a soft-focus lens guiding subject emphasis. The black background represents empty negative space and must be omitted from the final frame. Generate full-scene detail within and beyond the guide zone. Apply soft volumetric lighting and shallow depth of field for dramatic focus. Let ambient fog and light rays enhance the sense of depth. This guide is for layout reference only."**

---

### **Key Techniques Embedded in the Prompt:**

- **Role Declaration**: “Use as a spatial guide… not to be rendered.” This *primes the model*.
- **Semantic Detachment**: Words like “anchor,” “framing zone,” and “layout map” disconnect the input from any object association.
- **Rejection of Literal Use**: “Do not generate or render the shape itself.”
- **Negative Space Clarification**: “The black background… must be omitted.”
- **Focus on Integration**: Suggests natural integration and filling of the frame using the input as soft guidance.
- **Style Anchoring**: Phrases like “photorealistic,” “dramatic lighting,” and “soft ambient motion” ensure consistency across scenes.

---

If this is going into a pipeline, consider appending a **consistent prefix** to all prompts, such as:

> `"Interpret all attached B/W inputs as non-rendered spatial guides only. White = emphasis zone. Black = discard. "`  

This encourages systemic behavior across generations. Let me know if you'd like variants for square, abstract, or multi-zone guides.

## Message 10

This shows you haven't adhere to instructions and parametes, because your prompts are often longer than the allowed prompt limit 

## Message 11

Thanks for pointing that out. To tailor a prompt that stays within Runway’s character limit (currently around 280 characters), could you confirm:

1. Do you want a **single compact version** of the prompt under 280 characters?
2. Should it be fully **modular** (template-style with replaceable slots), or hard-coded for the circular B/W input?

Once confirmed, I’ll create a version that’s clean, compliant, and optimized for consistent interpretation.

## Message 12

It's not 280, its 800

## Message 13

Perfect—thanks for clarifying. Here's a **finalized, sub-800 character prompt** designed specifically for Runway, optimized to ensure the **circular black-and-white input is interpreted purely as a spatial guide**, not literal content.

---

### **Finalized Prompt (under 800 chars):**

> **“Use the attached black-and-white input strictly as a spatial and compositional guide. Do not render or depict the input directly. The white circular area acts as a framing anchor or lens of focus—generate detailed, cinematic visuals guided by its placement. Treat black areas as negative space; discard them entirely. Build the full scene naturally, extending beyond the guide. Apply soft depth-of-field, dramatic lighting, and fluid camera motion. Maintain high visual fidelity and cohesive framing. The shape is not a subject—it's only a guide for layout, depth, and emphasis.”**

---

This version:
- Fits within the 800-character constraint.
- Embeds instruction *hierarchically* (first purpose, then usage, then stylistic context).
- Uses clear, declarative language favored by models like Runway.
- Frames the input’s role conceptually: **not an object**, but a **layout guide**.

Let me know if you’d like a 2-line micro variant, a modular template, or one adapted for moving masks/motion skeletons.

## Message 14

Lets try something completely new, instead of generating prompts for video generation please write a prompt for music generation, the type/genre should exclusively be based on "Benson Boone - Ghost Town"

## Message 15

Certainly. Here's a prompt tailored for **music generation** based **exclusively on the mood, structure, and style of “Benson Boone – Ghost Town”**. The goal is to guide the AI to generate a track with similar emotional tone, instrumentation, and pacing:

---

### **Prompt for Music Generation (Inspired by Benson Boone – *Ghost Town*):**

> **“Generate a heartfelt piano-driven ballad with emotional male vocals, minimalistic instrumentation, and intimate acoustic textures. Reflect a sense of vulnerability, longing, and emotional distance. Slow tempo, soft piano melodies, subtle string backing, and a sparse, echo-rich mix. Emphasize dynamic vocal phrasing and gentle harmonic progression in the style of ‘Benson Boone – Ghost Town’. Preserve a raw, cinematic feel with gradual build-up and restrained production.”**

---

Let me know if you want this adapted for a specific AI music model (like Suno, Udio, or Riffusion) or converted into a modular template for multiple songs in this emotional/acoustic genre.
