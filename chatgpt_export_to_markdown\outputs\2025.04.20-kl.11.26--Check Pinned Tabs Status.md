# Check Pinned Tabs Status

Conversation ID: 6804bdb0-53e0-8008-877b-63d0b6dbbd59

## Message 1

Looking at this plugin for Sublime Text, please explain how I can check whether a tab is pinned or not from *another* Sublime Text plugin:



    # Dir `<PERSON><PERSON>_AutosortTabs`



    ### File Structure



    ```

    ├── Jorn_AutosortTabs.py

    ├── Jorn_AutosortTabs.sublime-commands

    ├── Jorn_AutosortTabs.sublime-keymap

    ├── Jorn_AutosortTabs.sublime-settings

    ├── Main.sublime-menu

    └── Tab Context.sublime-menu

    ```



    ---



    #### `Jorn_AutosortTabs.py`



    ```python

        import sublime

        import sublime_plugin

        import os

        import time

        from collections import defaultdict, deque



        PLUGIN_NAME = "Jorn_AutosortTabs"

        MAX_REORDERS_PER_SECOND = 3





        class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):

            """

            Main plugin that:

              - Tracks pinned tabs (pinned_tabs).

              - Places pinned tabs at the front (in left-to-right order).

              - Optionally autosorts unpinned tabs behind pinned on activation if user settings say so.

              - Prevents infinite loops via:

                1) Recursion guard (_is_reordering),

                2) Arrangement check (skip reorder if already correct),

                3) Frequency-based limit (MAX_REORDERS_PER_SECOND).

            """



            _instance = None



            def __init__(self):

                super().__init__()

                self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")

                self.pinned_tabs = defaultdict(lambda: defaultdict(set))

                self._is_reordering = False

                self._last_arrangements = defaultdict(lambda: defaultdict(tuple))

                self._reorder_timestamps = deque()

                Jorn_AutosortTabsCommand._instance = self

                print(f"[DEBUG] {PLUGIN_NAME} initialized")



            @classmethod

            def instance(cls):

                """

                Used by pin/unpin commands to reference this plugin instance directly.

                """

                return cls._instance



            # -------------------------------------------------------------------------

            # Sublime Event Hooks

            # -------------------------------------------------------------------------

            def on_activated_async(self, view):

                """

                If pinned, reorder the group to ensure pinned remain at the front.

                If unpinned and meets "autosort" conditions, move it behind pinned tabs.

                """



                if not view or not view.window():

                    return

                if self._is_reordering:

                    return



                window = view.window()

                group, view_index = window.get_view_index(view)



                print(f"[DEBUG] on_activated_async: view_id={view.id()}, group={group}, index={view_index}")



                if self._is_pinned(view):

                    print(f"[DEBUG] View is pinned, reordering group")

                    self._reorder_group(window, group)

                else:

                    if not self._should_process(view):

                        print(f"[DEBUG] Skipping unpinned view (no autosort)")

                        return



                    pinned_count = len(self.pinned_tabs[window.id()][group])

                    if view_index > pinned_count:

                        if not self._check_reorder_frequency():

                            return

                        self._is_reordering = True

                        try:

                            window.set_view_index(view, group, pinned_count)

                        finally:

                            self._is_reordering = False



                # Update visual indicators for all tabs in this group

                self._update_tab_decorations(window, group)



            def on_load_async(self, view):

                """

                Update decorations when a file is loaded.

                """

                if not view or not view.window():

                    return

                window = view.window()

                group, _ = window.get_view_index(view)

                self._update_tab_decorations(window, group)



            def on_new_async(self, view):

                """

                Update decorations when a new view is created.

                """

                if not view or not view.window():

                    return

                window = view.window()

                group, _ = window.get_view_index(view)

                self._update_tab_decorations(window, group)



            def on_close(self, view):

                """

                Remove closed views from pinned data sets.

                """

                view_id = view.id() if view else None



                if view and view.window():

                    window = view.window()

                    group, _ = window.get_view_index(view)

                    # Store for later decoration update

                    update_window = window

                    update_group = group



                # Clean up color scheme storage

                if hasattr(self, '_original_color_schemes') and view_id in self._original_color_schemes:

                    del self._original_color_schemes[view_id]



                for w_id, group_map in list(self.pinned_tabs.items()):

                    for g_id, pinned_set in list(group_map.items()):

                        pinned_set.discard(view_id)



                # Update visual indicators for the affected group

                if 'update_window' in locals() and 'update_group' in locals():

                    self._update_tab_decorations(update_window, update_group)



            def on_post_window_command(self, window, command_name, args):

                """

                After possible tab-drag commands, sync pinned data and reorder to keep pinned in front.

                """

                if command_name in ("drag_select", "drag_drop", "move_tab"):

                    self._sync_pinned_data(window)

                    self._reorder_all_groups_in_window(window)



                    # Update decorations after reordering

                    for group in range(window.num_groups()):

                        self._update_tab_decorations(window, group)



            # -------------------------------------------------------------------------

            # Public Pin/Unpin

            # -------------------------------------------------------------------------

            def pin_tab(self, view):

                """

                Pin a tab, then reorder group so pinned appear at the front.

                """

                window = view.window()

                if not window:

                    return

                group, _ = window.get_view_index(view)

                view_id = view.id()

                print(f"[DEBUG] Pinning tab: view_id={view_id}, group={group}, file={view.file_name() or 'untitled'}")

                self.pinned_tabs[window.id()][group].add(view_id)

                self._reorder_group(window, group)

                self._update_tab_decorations(window, group)



            def unpin_tab(self, view):

                """

                Unpin a tab, reorder group to keep pinned at front.

                """

                window = view.window()

                if not window:

                    return

                group, _ = window.get_view_index(view)

                view_id = view.id()

                print(f"[DEBUG] Unpinning tab: view_id={view_id}, group={group}, file={view.file_name() or 'untitled'}")

                self.pinned_tabs[window.id()][group].discard(view_id)

                self._reorder_group(window, group)

                self._update_tab_decorations(window, group)



            # -------------------------------------------------------------------------

            # Internal Helpers

            # -------------------------------------------------------------------------

            def _update_tab_decorations(self, window, group):

                """

                Add visual indicators to pinned tabs using both gutter icons and theme-based tab styling.

                """

                print(f"[DEBUG] Updating tab decorations for window={window.id()}, group={group}")



                if not self.settings.get("show_pin_indicators", True):

                    print("[DEBUG] Pin indicators disabled, removing all")

                    # If indicators disabled, remove all

                    views_in_group = window.views_in_group(group)

                    for view in views_in_group:

                        view.erase_regions("jorn_pinned_tab_indicator")

                        view.settings().erase("jorn_autosort_is_pinned")

                    return



                views_in_group = window.views_in_group(group)

                pinned_ids = self.pinned_tabs[window.id()][group]

                print(f"[DEBUG] Found {len(pinned_ids)} pinned tabs in this group: {pinned_ids}")



                indicator_style = self.settings.get("pin_indicator_style", "icon")

                print(f"[DEBUG] Current indicator style: {indicator_style}")



                # First, store original color schemes if not already stored

                if not hasattr(self, '_original_color_schemes'):

                    self._original_color_schemes = {}

                    print("[DEBUG] Initialized _original_color_schemes")



                for view in views_in_group:

                    view_id = view.id()

                    view_name = view.file_name() or "untitled"



                    if view_id in pinned_ids:

                        print(f"[DEBUG] Decorating pinned tab: view_id={view_id}, file={view_name}")



                        # 1. Add icon in gutter if style includes "icon"

                        if indicator_style in ["icon", "both", "theme"]:

                            view.add_regions(

                                "jorn_pinned_tab_indicator",

                                [sublime.Region(0, 0)],

                                "region.orangish",

                                "bookmark",

                                sublime.DRAW_NO_FILL | sublime.DRAW_NO_OUTLINE

                            )

                            print(f"[DEBUG] Added bookmark icon to {view_id}")



                        # 2. Apply theme settings for pinned tabs

                        if indicator_style in ["theme", "both"]:

                            # Make sure we set this as a view-specific setting

                            view.settings().set("jorn_autosort_is_pinned", True)

                            print(f"[DEBUG] Set jorn_autosort_is_pinned=True for theme styling on {view_id}")

                        else:

                            view.settings().erase("jorn_autosort_is_pinned")



                        # 3. Also apply color scheme if style is "both" or "color"

                        if indicator_style in ["color", "both"] and self.settings.get("use_color_scheme", False):

                            print(f"[DEBUG] Attempting to apply color scheme to {view_id}")

                            # Save original color scheme if not already saved

                            if view_id not in self._original_color_schemes:

                                original_scheme = view.settings().get('color_scheme', None)

                                self._original_color_schemes[view_id] = original_scheme

                                print(f"[DEBUG] Saved original color scheme: {original_scheme}")



                            # Apply pinned tab color scheme

                            color_scheme_path = "Packages/Jorn_AutosortTabs/JornPinnedTab.sublime-color-scheme"

                            print(f"[DEBUG] Setting color_scheme to {color_scheme_path}")

                            view.settings().set("color_scheme", color_scheme_path)



                            # Verify it was set

                            current_scheme = view.settings().get('color_scheme', None)

                            print(f"[DEBUG] After setting, color_scheme is now: {current_scheme}")



                        elif view_id in self._original_color_schemes and self.settings.get("use_color_scheme", False):

                            # Restore color scheme if not using color style anymore

                            original_scheme = self._original_color_schemes[view_id]

                            print(f"[DEBUG] Restoring original color scheme: {original_scheme}")

                            if original_scheme:

                                view.settings().set("color_scheme", original_scheme)

                            else:

                                view.settings().erase("color_scheme")

                            del self._original_color_schemes[view_id]

                    else:

                        print(f"[DEBUG] Removing decorations from unpinned tab: view_id={view_id}, file={view_name}")

                        # Remove all indicators for unpinned tabs



                        # 1. Remove icon indicator

                        view.erase_regions("jorn_pinned_tab_indicator")



                        # 2. Remove theme setting

                        view.settings().erase("jorn_autosort_is_pinned")



                        # 3. Restore original color scheme if we were using it

                        if view_id in self._original_color_schemes and self.settings.get("use_color_scheme", False):

                            original_scheme = self._original_color_schemes[view_id]

                            print(f"[DEBUG] Restoring original color scheme for unpinned tab: {original_scheme}")

                            if original_scheme:

                                view.settings().set("color_scheme", original_scheme)

                            else:

                                view.settings().erase("color_scheme")

                            del self._original_color_schemes[view_id]



            def _should_process(self, view):

                """

                Return True if the plugin is enabled and the view meets autosort conditions.

                """

                if not self.settings.get("autosort_on_tab_activated", False):

                    return False

                return self._meets_sorting_conditions(view)



            def _meets_sorting_conditions(self, view):

                """

                Check if the tab is considered 'deleted', 'modified', 'saved', or 'unsaved'

                according to user settings.

                """

                file_name = view.file_name()

                if not file_name:

                    return self.settings.get("autosort_unsaved_tabs", False)

                if not os.path.exists(file_name):

                    return self.settings.get("autosort_deleted_tabs", False)

                if view.is_dirty():

                    return self.settings.get("autosort_modified_tabs", False)

                return self.settings.get("autosort_saved_tabs", False)



            def _is_pinned(self, view):

                window = view.window()

                if not window:

                    return False

                group, _ = window.get_view_index(view)

                is_pinned = view.id() in self.pinned_tabs[window.id()][group]

                print(f"[DEBUG] is_pinned check: view_id={view.id()}, result={is_pinned}")

                return is_pinned



            def _compute_arrangement_tuple(self, window, group, desired_views):

                """

                Represent pinned/unpinned arrangement as a tuple, e.g.: ('P:101', 'P:105', 'U:103')

                """

                pinned_ids = self.pinned_tabs[window.id()][group]

                result = []

                for v in desired_views:

                    if v.id() in pinned_ids:

                        result.append(f"P:{v.id()}")

                    else:

                        result.append(f"U:{v.id()}")

                return tuple(result)



            def _reorder_group(self, window, group):

                """

                Rebuild pinned-first arrangement in a single pass.

                Then skip if arrangement is already correct (arrangement check).

                Then check rate limit, then do the reorder if needed.

                """



                if not window or group < 0:

                    return



                if self._is_reordering:

                    return



                views_in_group = window.views_in_group(group)

                pinned_ids = self.pinned_tabs[window.id()][group]



                pinned_views = []

                unpinned_views = []

                for v in views_in_group:

                    if v.id() in pinned_ids:

                        pinned_views.append(v)

                    else:

                        unpinned_views.append(v)



                desired_order = pinned_views + unpinned_views



                # 1) Arrangement check: skip if already correct

                arrangement_tuple = self._compute_arrangement_tuple(window, group, desired_order)

                prev_tuple = self._last_arrangements[window.id()][group]

                if arrangement_tuple == prev_tuple:

                    return



                # 2) Check reorder frequency

                if not self._check_reorder_frequency():

                    return  # too many reorders happening => skip



                # 3) Perform reorder

                self._is_reordering = True

                try:

                    for idx, v in enumerate(desired_order):

                        _, view_index = window.get_view_index(v)

                        if view_index != idx:

                            window.set_view_index(v, group, idx)

                finally:

                    self._is_reordering = False



                # 4) Update last arrangement

                self._last_arrangements[window.id()][group] = arrangement_tuple



            def _reorder_all_groups_in_window(self, window):

                """

                Reorder pinned vs unpinned in all groups for the given window.

                """

                if not window:

                    return

                for g_id in range(window.num_groups()):

                    self._reorder_group(window, g_id)



            def _sync_pinned_data(self, window):

                """

                If pinned tabs have been moved between groups, update pinned_tabs accordingly.

                """

                if not window:

                    return

                w_id = window.id()



                group_views = {}

                for group_index in range(window.num_groups()):

                    group_views[group_index] = {v.id() for v in window.views_in_group(group_index)}



                new_pinned = defaultdict(set)

                for old_group, pinned_set in self.pinned_tabs[w_id].items():

                    for vid in pinned_set:

                        found = False

                        for new_g, views_in_g in group_views.items():

                            if vid in views_in_g:

                                new_pinned[new_g].add(vid)

                                found = True

                                break

                        # if not found, it's presumably closed => skip



                self.pinned_tabs[w_id] = new_pinned



            # -------------------------------------------------------------------------

            # Frequency-based Loop Prevention

            # -------------------------------------------------------------------------

            def _check_reorder_frequency(self):

                """

                Rate-limit reorder calls to avoid infinite loops triggered externally.

                """

                now = time.time()

                cutoff = now - 1.0

                while self._reorder_timestamps and self._reorder_timestamps[0] < cutoff:

                    self._reorder_timestamps.popleft()



                if len(self._reorder_timestamps) >= MAX_REORDERS_PER_SECOND:

                    return False



                self._reorder_timestamps.append(now)

                return True





        # -------------------------------------------------------------------------

        # Single Toggle Command for Pin/Unpin

        # -------------------------------------------------------------------------

        class JornAutosortTogglePinTabCommand(sublime_plugin.TextCommand):

            """

            Toggle/Pin/Unpin the right-clicked tab.

            """



            def run(self, edit, action="toggle", group=-1, index=-1, **kwargs):

                window = self.view.window()

                if not window:

                    return



                views_in_group = window.views_in_group(group)

                if not views_in_group:

                    return



                if index < 0 or index >= len(views_in_group):

                    target_view = views_in_group[-1]

                else:

                    target_view = views_in_group[index]



                plugin = Jorn_AutosortTabsCommand.instance()

                if not plugin:

                    return



                pinned = plugin._is_pinned(target_view)



                if action == "pin":

                    if pinned:

                        sublime.status_message("[Jorn Autosort] Tab is already pinned.")

                    else:

                        plugin.pin_tab(target_view)

                        sublime.status_message("[Jorn Autosort] Tab pinned.")

                elif action == "unpin":

                    if pinned:

                        plugin.unpin_tab(target_view)

                        sublime.status_message("[Jorn Autosort] Tab unpinned.")

                    else:

                        sublime.status_message("[Jorn Autosort] Tab is not pinned.")

                elif action == "toggle":

                    if pinned:

                        plugin.unpin_tab(target_view)

                        sublime.status_message("[Jorn Autosort] Tab unpinned.")

                    else:

                        plugin.pin_tab(target_view)

                        sublime.status_message("[Jorn Autosort] Tab pinned.")

                else:

                    sublime.status_message(f"[Jorn Autosort] Unknown action: {action}")



            def is_visible(self, action="toggle", group=-1, index=-1, **kwargs):

                """

                Dynamically updated context menu

                - Hides the pin/unpin group depending on the current group lock state.

                """

                if action not in ("pin", "unpin", "toggle"):

                    return False



                window = self.view.window()

                if not window:

                    return False



                views_in_group = window.views_in_group(group)

                if not views_in_group:

                    return False



                if index < 0 or index >= len(views_in_group):

                    target_view = views_in_group[-1]

                else:

                    target_view = views_in_group[index]



                plugin = Jorn_AutosortTabsCommand.instance()

                if not plugin:

                    return False



                pinned = plugin._is_pinned(target_view)

                if action == "pin":

                    return not pinned

                elif action == "unpin":

                    return pinned

                return True  # 'toggle' is always visible



        # -------------------------------------------------------------------------

        # Toggle Autosort on Tab Activated Command

        # -------------------------------------------------------------------------

        class JornToggleAutosortOnTabActivatedCommand(sublime_plugin.ApplicationCommand):

            """

            Toggle the "autosort_on_tab_activated" setting.

            """

            def run(self):

                settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")

                current_value = settings.get("autosort_on_tab_activated", False)

                settings.set("autosort_on_tab_activated", not current_value)

                sublime.save_settings(f"{PLUGIN_NAME}.sublime-settings")



                new_value = not current_value

                status = "enabled" if new_value else "disabled"

                sublime.status_message(f"[Jorn Autosort] Autosort on tab activation {status}")





        class JornCyclePinIndicatorStyleCommand(sublime_plugin.ApplicationCommand):

            """

            Cycle through available pin indicator styles.

            """

            def run(self):

                settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")

                styles = ["icon", "color", "both", "theme", "none"]

                current_style = settings.get("pin_indicator_style", "icon")



                if not settings.get("show_pin_indicators", True):

                    # If indicators are disabled, enable them with default style

                    settings.set("show_pin_indicators", True)

                    settings.set("pin_indicator_style", "icon")

                    style_msg = "icon"

                else:

                    # If indicators are enabled, cycle through styles

                    try:

                        current_index = styles.index(current_style)

                        next_index = (current_index + 1) % len(styles)

                        next_style = styles[next_index]

                    except ValueError:

                        next_style = "icon"  # Default if current style is invalid



                    if next_style == "none":

                        settings.set("show_pin_indicators", False)

                        style_msg = "disabled"

                    else:

                        settings.set("pin_indicator_style", next_style)

                        style_msg = next_style



                sublime.save_settings(f"{PLUGIN_NAME}.sublime-settings")



                # Update all tabs with new style

                instance = Jorn_AutosortTabsCommand.instance()

                if instance:

                    for window in sublime.windows():

                        for group in range(window.num_groups()):

                            instance._update_tab_decorations(window, group)



                sublime.status_message(f"[Jorn Autosort] Pin indicators: {style_msg}")





        class JornRestoreOriginalThemeCommand(sublime_plugin.ApplicationCommand):

            """

            Restore the original theme that was active before any theme changes.

            """

            def run(self):

                plugin_settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")

                original_theme = plugin_settings.get("original_theme", None)



                if original_theme:

                    prefs = sublime.load_settings("Preferences.sublime-settings")

                    current = prefs.get("theme", "")



                    # Only restore if current theme appears to be one of ours

                    if "JornPinnedTab" in current or "PinnedTab" in current:

                        prefs.set("theme", original_theme)

                        sublime.save_settings("Preferences.sublime-settings")

                        sublime.status_message(f"[Jorn Autosort] Restored original theme: {original_theme}")

                    else:

                        sublime.status_message("[Jorn Autosort] Theme wasn't changed, no need to restore")

                else:

                    sublime.status_message("[Jorn Autosort] No original theme saved to restore")





        def plugin_loaded():

            """

            Initialize tab decorations and apply theming when plugin is loaded.

            """

            print(f"[DEBUG] {PLUGIN_NAME} plugin_loaded called")



            # Approach 1: Add our setting to theme_settings for better compatibility

            try:

                # Add the 'jorn_autosort_is_pinned' key to the list of settings that get

                # checked by the theme system (otherwise view-specific settings don't affect themes)

                prefs = sublime.load_settings("Preferences.sublime-settings")

                settings_list = prefs.get("theme_settings", [])

                if "jorn_autosort_is_pinned" not in settings_list:

                    settings_list.append("jorn_autosort_is_pinned")

                    prefs.set("theme_settings", settings_list)

                    sublime.save_settings("Preferences.sublime-settings")

                    print("[DEBUG] Added jorn_autosort_is_pinned to theme_settings")

                else:

                    print("[DEBUG] jorn_autosort_is_pinned already in theme_settings")

            except Exception as e:

                print(f"[DEBUG] Error setting up theme_settings: {str(e)}")



            # Approach 2: Try to directly change the theme

            try:

                # Get current theme

                prefs = sublime.load_settings("Preferences.sublime-settings")

                current_theme = prefs.get("theme", "")

                print(f"[DEBUG] Current theme: {current_theme}")



                # Save original theme if not already saved

                plugin_settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")

                if not plugin_settings.has("original_theme"):

                    plugin_settings.set("original_theme", current_theme)

                    sublime.save_settings(f"{PLUGIN_NAME}.sublime-settings")

                    print(f"[DEBUG] Saved original theme: {current_theme}")



                # Check plugin settings

                print(f"[DEBUG] show_pin_indicators: {plugin_settings.get('show_pin_indicators', True)}")

                print(f"[DEBUG] pin_indicator_style: {plugin_settings.get('pin_indicator_style', 'icon')}")

                print(f"[DEBUG] use_color_scheme: {plugin_settings.get('use_color_scheme', False)}")



                # If pinned tab indicators are enabled and style is "theme",

                # set our theme directly

                if (plugin_settings.get("show_pin_indicators", True) and

                        plugin_settings.get("pin_indicator_style", "icon") == "theme"):

                    # prefs.set("theme", "JornPinnedTab.sublime-theme")

                    # sublime.save_settings("Preferences.sublime-settings")

                    print("[DEBUG] Would set theme to JornPinnedTab.sublime-theme, but disabled for now")

                    pass  # Disabled direct theme switching for now - use the more compatible first approach

            except Exception as e:

                print(f"[DEBUG] Error setting theme: {str(e)}")



            # Check if color scheme files exist

            color_scheme_path = os.path.join(sublime.packages_path(), "Jorn_AutosortTabs", "JornPinnedTab.sublime-color-scheme")

            print(f"[DEBUG] Checking if color scheme exists at: {color_scheme_path}")

            print(f"[DEBUG] File exists: {os.path.isfile(color_scheme_path)}")



            # Update decorations for all tabs

            instance = Jorn_AutosortTabsCommand.instance()

            if instance:

                print("[DEBUG] Updating decorations for all tabs")

                for window in sublime.windows():

                    for group in range(window.num_groups()):

                        instance._update_tab_decorations(window, group)

            else:

                print("[DEBUG] No plugin instance available yet")

    ```



    ---



    #### `Jorn_AutosortTabs.sublime-commands`



    ```sublime-commands

        [

            // {

            //     "caption": "Jorn - Toggle Sticky Tab",

            //     "command": "jorn_toggle_sticky_tab"

            // },



            {

                // "caption": "Jorn Autosort: Enable for current tab",

                "caption": "Jorn Autosort: Pin Tab",

                "command": "jorn_autosort_pin_tab",

                "args": {"group": -1, "index": -1 }

            },

            {

                // "caption": "Jorn Autosort: Disable for current tab",

                "caption": "Jorn Autosort: Unpin Tab",

                "command": "jorn_autosort_unpin_tab",

                "args": {"group": -1, "index": -1 }

            },

            {

                "caption": "Jorn Autosort: Toggle Autosort on Tab Activation",

                "command": "jorn_toggle_autosort_on_tab_activated"

            },

            {

                "caption": "Jorn Autosort: Cycle Pin Indicator Style",

                "command": "jorn_cycle_pin_indicator_style"

            },

            {

                "caption": "Jorn Autosort: Restore Original Theme",

                "command": "jorn_restore_original_theme"

            }

        ]

    ```



    ---



    #### `Jorn_AutosortTabs.sublime-keymap`



    ```sublime-keymap

        [

        ]

    ```



    ---



    #### `Jorn_AutosortTabs.sublime-settings`



    ```sublime-settings

        {

            "autosort_on_tab_activated": false,

            "print_on_tab_activation": true,



            //

            "autosort_unsaved_tabs": false,

            "autosort_deleted_tabs": false,

            "autosort_modified_tabs": true,

            "autosort_saved_tabs": true,





            // pinned tabs

            // - will always be topmost, but will not be affected by the autosort. the

            //   tabs affected by autosort will always be placed *after* all pinned

            //   tabs.

            "enable_pinned_tabs": true,

            "pinned_tabs_list": [],

            "pinned_tabs_patterns": [],



            // If true, pinned tab you just activated or pinned is moved

            // to the front among pinned tabs

            "reorder_pinned_on_activation": false,



            // Visual indicator settings

            "show_pin_indicators": false,

            "pin_indicator_style": "icon", // Options: "icon", "color", "both", "theme"



            // Whether to apply color scheme changes (only used if pin_indicator_style is "color" or "both")

            "use_color_scheme": false

        }

    ```



    ---



    #### `Main.sublime-menu`



    ```sublime-menu

        [

            {

                // Ribbon-Menu: Preferences -> Package Settings

                "caption": "Preferences",

                "mnemonic": "n",

                "id": "preferences",

                "children":

                [

                    {

                        "caption": "Package Settings",

                        "mnemonic": "P",

                        "id": "package-settings",

                        "children":

                        [

                            {

                                // Jorn Tools

                                "caption": "Jorn Tools",

                                "mnemonic": "J",

                                "id": "jorn-tools",

                                "children": [

                                    {

                                        "caption": "Jorn_AutosortTabs",

                                        "children":

                                        [

                                            {/* --------------------------------------------- */ "caption": "-"},

                                            {

                                                "caption": "Main.sublime-menu",

                                                "command": "open_file",

                                                "args": {"file": "${packages}/Jorn_AutosortTabs/Main.sublime-menu"},

                                            },

                                            {/* --------------------------------------------- */ "caption": "-"},

                                            {

                                                "caption": "Jorn_AutosortTabs.py",

                                                "command": "open_file",

                                                "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.py"},

                                            },

                                            {/* --------------------------------------------- */ "caption": "-"},

                                            {

                                                "caption": "Settings",

                                                "command": "open_file",

                                                "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-settings"},

                                            },

                                            {

                                                "caption": "Settings – User",

                                                "command": "open_file",

                                                "args": {"file": "${packages}/User/Default.sublime-settings"},

                                            },

                                            {/* --------------------------------------------- */ "caption": "-"},

                                            {

                                                "caption": "Keymap",

                                                "command": "open_file",

                                                "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-keymap"},

                                            },

                                            {

                                                "caption": "Keymap – User",

                                                "command": "open_file",

                                                "args": {"file": "${packages}/User/Default (Windows).sublime-keymap"},

                                            },

                                            {/* --------------------------------------------- */ "caption": "-"},

                                            {

                                                "caption": "Commands",

                                                "command": "open_file",

                                                "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-commands"},

                                            },

                                            {

                                                "caption": "Commands – User",

                                                "command": "open_file",

                                                "args": {"file": "${packages}/User/Default (Windows).sublime-commands"},

                                            },

                                            {/* --------------------------------------------- */ "caption": "-"},

                                            {

                                                "caption": "Open Folder -> Todo",

                                                "args": {"cmd": ["explorer.exe", "${packages}/Jorn_AutosortTabs"]}

                                                // "command": "jorn_open_directory",

                                                // "args": {"group": -1, "index": -1 },

                                            },

                                            {/* --------------------------------------------- */ "caption": "-"},

                                        ]

                                    }

                                ]

                            }

                        ]

                    }

                ]

            },

            {

                // Ribbon-Menu: Jorn Tools

                "caption": "Jorn Tools",

                "mnemonic": "J",

                "id": "jorn-tools",

                "children": [

                    {

                        "caption": "Jorn_AutosortTabs",

                        "children":

                        [

                            {/* --------------------------------------------- */ "caption": "-"},

                            {

                                "caption": "Main.sublime-menu",

                                "command": "open_file",

                                "args": {"file": "${packages}/Jorn_AutosortTabs/Main.sublime-menu"},

                            },

                            {/* --------------------------------------------- */ "caption": "-"},

                            {

                                "caption": "Jorn_AutosortTabs.py",

                                "command": "open_file",

                                "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.py"},

                            },

                            {/* --------------------------------------------- */ "caption": "-"},

                            {

                                "caption": "Settings",

                                "command": "open_file",

                                "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-settings"},

                            },

                            {

                                "caption": "Settings – User",

                                "command": "open_file",

                                "args": {"file": "${packages}/User/Default.sublime-settings"},

                            },

                            {/* --------------------------------------------- */ "caption": "-"},

                            {

                                "caption": "Keymap",

                                "command": "open_file",

                                "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-keymap"},

                            },

                            {

                                "caption": "Keymap – User",

                                "command": "open_file",

                                "args": {"file": "${packages}/User/Default (Windows).sublime-keymap"},

                            },

                            {/* --------------------------------------------- */ "caption": "-"},

                            {

                                "caption": "Commands",

                                "command": "open_file",

                                "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-commands"},

                            },

                            {

                                "caption": "Commands – User",

                                "command": "open_file",

                                "args": {"file": "${packages}/User/Default (Windows).sublime-commands"},

                            },

                            {/* --------------------------------------------- */ "caption": "-"},

                            {

                                "caption": "Open Folder -> Todo",

                                "args": {"cmd": ["explorer.exe", "${packages}/Jorn_AutosortTabs"]}

                                // "command": "jorn_open_directory",

                                // "args": {"group": -1, "index": -1 },

                            },

                            {/* --------------------------------------------- */ "caption": "-"},

                        ]

                    }

                ]

            }

        ]



    ```



    ---



    #### `Tab Context.sublime-menu`



    ```sublime-menu

        [

            { "caption": "-", "id": "jorn_autosort_toggle_separator" },

            {

                "caption": "Jorn Autosort: Pin Tab",

                "command": "jorn_autosort_toggle_pin_tab",

                "args": {"action": "pin", "group": -1, "index": -1},

            },

            {

                "caption": "Jorn Autosort: Unpin Tab",

                "command": "jorn_autosort_toggle_pin_tab",

                "args": {"action": "unpin", "group": -1, "index": -1},

            },

            { "caption": "-", "id": "jorn_autosort_toggle_separator" },

            {

                "caption": "Jorn Autosort: Toggle Autosort on Tab Activation",

                "command": "jorn_toggle_autosort_on_tab_activated"

            },

            {

                "caption": "Jorn Autosort: Cycle Pin Indicator Style",

                "command": "jorn_cycle_pin_indicator_style"

            }

        ]



    ```




## Message 2

**Piecing together the solution**

I'm pondering the intricacies of accessing `Jorn_AutosortTabsCommand` from a different Sublime Text plugin. Considering methods to locate the plugin, check its instance, and verify pin status in another plugin, despite the lack of direct referencing mechanisms.

**Navigating internal methods**

I see that accessing internal methods in Sublime Text plugins can be tricky. Importing the relevant class or mapping out the pinned data structure might be the best solutions.

## Message 3

Below is a concise summary of how *Jorn_AutosortTabs* keeps track of pinned tabs, and how you can check (from your own plugin) whether a particular `view` is pinned.

---

## 1. How **Jorn_AutosortTabs** stores pinned tabs

1. **`pinned_tabs`** data structure  
   - In `Jorn_AutosortTabsCommand`, there is a dictionary:  
     ```python
     self.pinned_tabs[window_id][group] = {set_of_view_ids}
     ```
   - Each key is the `window.id()`. Each value is a dict whose keys are group indices (like 0, 1, 2, …). Each of those maps to a `set` of pinned view IDs (`view.id()`).

2. **`_is_pinned(self, view)`** helper method  
   - Internally, the plugin uses `_is_pinned(view)` to check if a given `view` is pinned:
     ```python
     def _is_pinned(self, view):
         window = view.window()
         if not window:
             return False
         group, _ = window.get_view_index(view)
         return view.id() in self.pinned_tabs[window.id()][group]
     ```

3. **Singleton instance**  
   - Because Sublime’s event-listener classes are not trivial to reference directly, the plugin stores a singleton-like reference to itself in `Jorn_AutosortTabsCommand._instance`. This is returned via:
     ```python
     @classmethod
     def instance(cls):
         return cls._instance
     ```

---

## 2. Checking pinned status in **your own plugin**

From another Sublime plugin, you can import `Jorn_AutosortTabsCommand` (assuming it is in the same `Packages` scope and importable by name). Then do something like:

```python
import sublime
import sublime_plugin

# Import the class, or use your relative path or package import
try:
    from Jorn_AutosortTabs import Jorn_AutosortTabsCommand
except ImportError:
    Jorn_AutosortTabsCommand = None

class MyOtherPluginCommand(sublime_plugin.TextCommand):
    def run(self, edit):
        if not Jorn_AutosortTabsCommand:
            sublime.status_message("Jorn_AutosortTabs not found.")
            return

        plugin_instance = Jorn_AutosortTabsCommand.instance()
        if not plugin_instance:
            sublime.status_message("Jorn_AutosortTabs plugin not initialized yet.")
            return

        # Check the pinned status of the *current* view
        pinned = plugin_instance._is_pinned(self.view)
        if pinned:
            sublime.status_message("This tab is pinned!")
        else:
            sublime.status_message("This tab is not pinned.")
```

### Important notes
- In Sublime Text, plugin load order is not always guaranteed. Always guard your call with a check to make sure the plugin class and instance exist.  
- `_is_pinned` is nominally an “internal” method (underscored), but you can safely use it if you accept that it’s part of the plugin’s code, not an official Sublime API.  
- Alternatively, you can directly inspect the data structure:
  ```python
  pinned_set = plugin_instance.pinned_tabs[window.id()].get(group, set())
  is_pinned = (view.id() in pinned_set)
  ```
  This is essentially the same check that `_is_pinned(view)` does.  

---

### In short
**The simplest approach** is to call:
```python
plugin_instance = Jorn_AutosortTabsCommand.instance()
is_pinned = plugin_instance._is_pinned(my_view)
```
…making sure you handle the case where that plugin might not be installed or loaded yet.

## Message 4

Please show how this plugin can exclude pinned tabs from closing (by `Jorn_AutosortTabs`):



    # Dir `Jorn_CloseTabs`



    ### File Structure



    ```

    ├── JornCloseTabs.py

    └── JornCloseTabs.sublime-commands

    ```



    ---



    #### `JornCloseTabs.py`



    ```python



        # [Close Tabs]

        import os

        import sublime

        import sublime_plugin

        import time

        import math

        import traceback



        class JornCloseTabsCommand(sublime_plugin.WindowCommand):

            valid_tab_types = [

                "all", "empty", "saved", "deleted", "notsaved",

                "modified", "not_in_project", "inside_backups",

                "inside_tmp", "same_extension", "not_modified_in_hours"

            ]

            last_args = {}



            def run(self, tab_types=[], tab_syntaxes=[], current_group_only=True, confirmation_prompt=True, hours=None, exclude_active_tabs=False):

                self.last_args = {

                    "current_group_only": current_group_only,

                    "tab_types": tab_types,

                    "tab_syntaxes": tab_syntaxes,

                    "confirmation_prompt": confirmation_prompt,

                    "hours": hours,

                    "exclude_active_tabs": exclude_active_tabs,

                }



                try:

                    if 'all' in tab_types:

                        tab_views = self.get_tab_views(current_group_only)

                    else:

                        tab_views = self.get_tab_views(current_group_only)

                        tab_views = self.find_matching_tabs(tab_views, tab_types, tab_syntaxes, hours)

                    if exclude_active_tabs:

                        tab_views = self.filter_active_tabs(tab_views)

                except Exception as e:

                    print(f"Error retrieving tabs: {e}")

                    print(traceback.format_exc())

                    tab_views = []



                if tab_views and (not confirmation_prompt or self.confirm_closing_tabs(tab_views, len(tab_views))):

                    self.close_tabs(tab_views)



            # Tab retrieval

            def get_tab_views(self, current_group_only):

                    try:

                        return self.window.views_in_group(self.window.active_group()) if current_group_only else self.window.views()

                    except Exception as e:

                        print(f"Error getting tab views: {e}")

                        print(traceback.format_exc())

                        return []



            def filter_active_tabs(self, tab_views):

                active_views = set()

                for group_index in range(self.window.num_groups()):

                    active_view = self.window.active_view_in_group(group_index)

                    if active_view:

                        active_views.add(active_view)

                return [view for view in tab_views if view not in active_views]



            # Tab matching

            def find_matching_tabs(self, tab_views, tab_types, tab_syntaxes, hours):

                matching_tabs = []

                for view in tab_views:

                    try:

                        if self.tab_matches_conditions(view, tab_types, tab_syntaxes, hours):

                            matching_tabs.append(view)

                    except Exception as e:

                        print(f"Error processing tab '{self.get_tab_info(view)}': {e}")

                        print(traceback.format_exc())

                        continue  # Skip this tab and continue with others

                return matching_tabs



            def tab_matches_conditions(self, view, tab_types, tab_syntaxes, hours):

                try:

                    return self.matches_tab_type(view, tab_types, hours) and self.matches_syntax(view, tab_syntaxes)

                except Exception as e:

                    print(f"Error matching conditions for tab '{self.get_tab_info(view)}': {e}")

                    print(traceback.format_exc())

                    return False  # If there's an error, don't match this tab



            def matches_tab_type(self, view, tab_types, hours):

                for tab_type in tab_types:

                    if tab_type == "not_modified_in_hours":

                        if self.is_tab_type_not_modified_in_hours(view, hours):

                            return True

                    else:

                        check_func = getattr(self, "is_tab_type_" + tab_type, None)

                        if check_func is not None:

                            try:

                                if check_func(view):

                                    return True

                            except Exception as e:

                                print(f"Error in '{tab_type}' check for tab '{self.get_tab_info(view)}': {e}")

                                print(traceback.format_exc())

                                continue  # Skip this check and continue with others

                        else:

                            print('Unsupported: ["{}"]. Valid tab-types are: ["{}"]'.format(

                                tab_type, '", "'.join(self.valid_tab_types)))

                return False



            def matches_syntax(self, view, tab_syntaxes):

                if not tab_syntaxes:

                    return True

                try:

                    tab_syntax = view.settings().get('syntax')

                    return any(syntax.lower() in tab_syntax.lower() for syntax in tab_syntaxes)

                except Exception as e:

                    print(f"Error matching syntax for tab '{self.get_tab_info(view)}': {e}")

                    print(traceback.format_exc())

                    return False



            # Tab contains less than 1 character (stripped of whitespaces)

            def is_tab_type_empty(self, view):

                try:

                    return len(view.substr(sublime.Region(0, view.size())).strip()) <= 1

                except Exception as e:

                    print(f"Error checking if tab is empty: {e}")

                    print(traceback.format_exc())

                    return False



            # Tab has been saved

            def is_tab_type_saved(self, view):

                try:

                    return view.file_name() and not view.is_dirty() and os.path.exists(view.file_name())

                except Exception as e:

                    print(f"Error checking if tab is saved: {e}")

                    print(traceback.format_exc())

                    return False



            # Tab's file has been moved or deleted

            def is_tab_type_deleted(self, view):

                try:

                    return view.file_name() and not os.path.exists(view.file_name())

                except Exception as e:

                    print(f"Error checking if tab is deleted: {e}")

                    print(traceback.format_exc())

                    return False



            # Tab has -never- been saved (to a file)

            def is_tab_type_notsaved(self, view):

                try:

                    return not view.file_name()

                except Exception as e:

                    print(f"Error checking if tab is not saved: {e}")

                    print(traceback.format_exc())

                    return False



            # Tab has unsaved changes

            def is_tab_type_modified(self, view):

                try:

                    return view.file_name() and view.is_dirty() and os.path.exists(view.file_name())

                except Exception as e:

                    print(f"Error checking if tab is modified: {e}")

                    print(traceback.format_exc())

                    return False



            # Tab is not in the current project

            def is_tab_type_not_in_project(self, view):

                try:

                    file_name = view.file_name()

                    if not file_name:

                        return False

                    project_folders = self.window.folders()

                    return not any(file_name.startswith(folder) for folder in project_folders)

                except Exception as e:

                    print(f"Error checking if tab is not in project: {e}")

                    print(traceback.format_exc())

                    return False



            # Tab is inside folder: .backups

            def is_tab_type_inside_backups(self, view):

                try:

                    file_name = view.file_name()

                    if not file_name:

                        return False

                    return ".backups" in file_name

                except Exception as e:

                    print(f"Error checking if tab is inside backups: {e}")

                    print(traceback.format_exc())

                    return False



            # Tab is inside folder: .tmp

            def is_tab_type_inside_tmp(self, view):

                try:

                    file_name = view.file_name()

                    if not file_name:

                        return False

                    return ".tmp" in file_name

                except Exception as e:

                    print(f"Error checking if tab is inside tmp: {e}")

                    print(traceback.format_exc())

                    return False



            # Tab has the same extension

            def is_tab_type_same_extension(self, view):

                try:

                    active_view = self.window.active_view()

                    if not active_view or not active_view.file_name() or not view.file_name():

                        return False

                    return os.path.splitext(active_view.file_name())[1] == os.path.splitext(view.file_name())[1]

                except Exception as e:

                    print(f"Error checking if tab has the same extension: {e}")

                    print(traceback.format_exc())

                    return False



            # Tab has not been modified in x hours

            def is_tab_type_not_modified_in_hours(self, view, hours):

                try:

                    if not view.file_name() or hours is None:

                        return False

                    last_modified_time = os.path.getmtime(view.file_name())

                    current_time = time.time()

                    return (current_time - last_modified_time) / 3600 > hours

                except FileNotFoundError:

                    # File was deleted after the check

                    print(f"File not found when checking modification time: {view.file_name()}")

                    return False

                except Exception as e:

                    print(f"Error checking if tab was not modified in hours: {e}")

                    print(traceback.format_exc())

                    return False



            def get_tab_info(self, view):

                try:

                    return os.path.basename(view.file_name()) if view.file_name() else '{} characters'.format(view.size())

                except Exception as e:

                    print(f"Error getting tab info: {e}")

                    print(traceback.format_exc())

                    return 'Unknown'



            def build_prompt(self, tabs_to_close, total_tabs):

                try:

                    prompt_header = 'You are about to close {}/{} tabs:\n'.format(len(tabs_to_close), total_tabs)

                    prompt_message = ''.join('-> Tab {}: {}\n'.format(i+1, self.get_tab_info(tab)) for i, tab in enumerate(tabs_to_close))

                    prompt_footer = '\nThis cannot be undone, are you sure?'

                    return prompt_header + prompt_message + prompt_footer

                except Exception as e:

                    print(f"Error building prompt: {e}")

                    print(traceback.format_exc())

                    return "Are you sure you want to close the selected tabs?"



            def confirm_closing_tabs(self, tabs_to_close, total_tabs):

                try:

                    prompt_message = self.build_prompt(tabs_to_close, total_tabs)

                    return sublime.ok_cancel_dialog(prompt_message)

                except Exception as e:

                    print(f"Error displaying confirmation dialog: {e}")

                    print(traceback.format_exc())

                    return False



            def close_tabs(self, tabs_to_close):

                confirmation_was_skipped = not self.last_args.get("confirmation_prompt", True) # Default to True if not found

                closed_count = 0

                closed_tabs = set()

                skipped_tabs = set()



                for tab in tabs_to_close:

                    try:

                        # Failsafe: Only skip if confirmation was explicitly false AND tab is dirty AND file exists

                        if confirmation_was_skipped and tab.is_dirty() and tab.file_name() and os.path.exists(tab.file_name()):

                            skipped_tabs.add(self.get_tab_info(tab))

                            continue # Skip closing this specific tab

                        # Catch and close

                        closed_tabs.add(self.get_tab_info(tab))

                        tab.set_scratch(True)

                        tab.close()

                        closed_count += 1

                    except Exception as e:

                        print(f"Error closing tab '{self.get_tab_info(tab)}': {e}")

                        print(traceback.format_exc())

                        continue



                if skipped_tabs:

                    print('\n')

                    print(f"Skipped {len(skipped_tabs)} tabs (through failsafe trigger)")

                    print(f"Skipped {skipped_tabs}")



                if closed_tabs:

                    print('\n')

                    print(f"Closed {len(closed_tabs)} tabs")

                    print(f"Closed {closed_tabs}")



                return

    ```



    ---



    #### `JornCloseTabs.sublime-commands`



    ```sublime-commands

        [

            //     "caption": "Jorn: Close Tabs: Cleanup tabs older than \t dm:<last1hours",



            {

                "caption": "Jorn - Close Tabs: [ < 15 Minutes > ] \t <Cleanup Tabs> | dm:<last15minutes",

                "command": "jorn_close_tabs",

                "args":

                {

                    "current_group_only": false,

                    // "tab_types": ["not_modified_in_hours", "empty", "deleted"],

                    "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],

                    "tab_syntaxes": [],

                    "confirmation_prompt": false, // set to true to preview

                    "hours": 0.25,

                    "exclude_active_tabs": false, // if true: only affect background tabs

                }

            },

            {

                "caption": "Jorn - Close Tabs: [ < Hour > ] \t <Cleanup Tabs> | dm:<last1hours",

                "command": "jorn_close_tabs",

                "args":

                {

                    "current_group_only": false,

                    // "tab_types": ["not_modified_in_hours", "empty", "deleted"],

                    "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],

                    "tab_syntaxes": [],

                    "confirmation_prompt": false, // set to true to preview

                    "hours": 1,

                    "exclude_active_tabs": false, // if true: only affect background tabs

                }

            },

            {

                "caption": "Jorn - Close Tabs: [ < Day > ] \t <Cleanup Tabs> | dm:<last24hours",

                "command": "jorn_close_tabs",

                "args":

                {

                    "current_group_only": false,

                    // "tab_types": ["not_modified_in_hours", "empty", "deleted"],

                    "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],

                    "tab_syntaxes": [],

                    "confirmation_prompt": false, // set to true to preview

                    "hours": 24,

                    "exclude_active_tabs": false, // if true: only affect background tabs

                }

            },

            {

                "caption": "Jorn - Close Tabs: [ < Week > ] \t <Cleanup Tabs> | dm:<last1week",

                "command": "jorn_close_tabs",

                "args":

                {

                    "current_group_only": false,

                    // "tab_types": ["not_modified_in_hours", "empty", "deleted"],

                    "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],

                    "tab_syntaxes": [],

                    "confirmation_prompt": false, // set to true to preview

                    "hours": 168,

                    "exclude_active_tabs": false, // if true: only affect background tabs

                }

            },



            {

                "caption": "Jorn - Close Tabs: [ < All Inactive Background-Tabs > ] \t <Cleanup Background Tabs> | *",

                "command": "jorn_close_tabs",

                "args":

                {

                    "current_group_only": false,

                    "tab_types": ["empty", "deleted", "saved"],

                    "tab_syntaxes": [],

                    "confirmation_prompt": false,

                    "exclude_active_tabs": true,



                }

            },



        ]







            // "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],

    ```




## Message 5

**Considering tab exclusion**

I'm working on the `JornCloseTabs` plugin to ensure pinned tabs aren't closed. This involves skipping these tabs in the closing logic, possibly using `_is_pinned()` to filter them out.

**Refining the approach**

I’m integrating a function to filter out pinned tabs from the closing logic in `run`. If available, the `Jorn_AutosortTabsCommand` instance helps identify pinned tabs to exclude.

**Mapping the strategy**

I’m thinking of integrating the functionality by default to skip pinned tabs in `JornCloseTabs.py`. This approach seems efficient and straightforward.

## Message 6

Below is an **example** showing how to modify `JornCloseTabs.py` to **skip closing pinned tabs** (as tracked by the `Jorn_AutosortTabs` plugin). Essentially, you:

1. **Import** the `Jorn_AutosortTabsCommand` class (if it exists).  
2. **Check** `pinned_plugin._is_pinned(view)` before closing a tab.  
3. **Skip** pinned tabs.

---

## 1. Add an import statement (at the top of `JornCloseTabs.py`)

```python
# JornCloseTabs.py

import sublime
import sublime_plugin
import os
import time
import traceback

# 1) Try importing the Autosort plugin class
try:
    from Jorn_AutosortTabs import Jorn_AutosortTabsCommand
except ImportError:
    Jorn_AutosortTabsCommand = None
```

This way, **if** the user has `Jorn_AutosortTabs` installed, you can reference its singleton instance and check pinned status. If they don’t have it installed, `Jorn_AutosortTabsCommand` will be `None`, and your code simply won’t exclude anything.

---

## 2. Filter out pinned tabs (in `close_tabs` or earlier)

Inside `close_tabs`, you can skip pinned tabs by checking `_is_pinned(view)`:

```python
def close_tabs(self, tabs_to_close):
    """
    Close all views in `tabs_to_close`, but skip pinned ones if the Autosort plugin is present.
    """
    confirmation_was_skipped = not self.last_args.get("confirmation_prompt", True)
    closed_tabs = set()
    skipped_tabs = set()

    # 2) Get the plugin instance (if available)
    pinned_plugin = Jorn_AutosortTabsCommand.instance() if Jorn_AutosortTabsCommand else None

    for tab in tabs_to_close:
        try:
            # 3) If pinned, skip
            if pinned_plugin and pinned_plugin._is_pinned(tab):
                skipped_tabs.add(self.get_tab_info(tab))
                continue

            # Failsafe example: skip if the file is dirty and unconfirmed
            if confirmation_was_skipped and tab.is_dirty() and tab.file_name() and os.path.exists(tab.file_name()):
                skipped_tabs.add(self.get_tab_info(tab))
                continue

            # If not pinned or failsafe, actually close
            closed_tabs.add(self.get_tab_info(tab))
            tab.set_scratch(True)  # avoid "Save changes?" prompt
            tab.close()
        except Exception as e:
            print(f"Error closing tab '{self.get_tab_info(tab)}': {e}")
            print(traceback.format_exc())
            continue

    if skipped_tabs:
        print('\n[INFO] Skipped {} pinned/unsaved tabs:\n{}'.format(len(skipped_tabs), skipped_tabs))
    if closed_tabs:
        print('\n[INFO] Closed {} tabs:\n{}'.format(len(closed_tabs), closed_tabs))
```

### Explanation

1. **`pinned_plugin = Jorn_AutosortTabsCommand.instance()`**  
   - Retrieves the active instance of the autosort plugin.
2. **Check**:  
   ```python
   if pinned_plugin and pinned_plugin._is_pinned(tab):
       # skip pinned tabs
       continue
   ```
3. If pinned, we simply do **`continue`** and avoid closing the tab.

---

## 3. (Optional) Filter pinned tabs earlier

If you prefer not to even list pinned tabs in your “tabs to close” list, you could do it earlier in `run()`:

```python
def run(self, tab_types=[], tab_syntaxes=[], current_group_only=True, confirmation_prompt=True,
        hours=None, exclude_active_tabs=False):

    # ... existing code to gather tab_views ...

    # Optionally, remove pinned tabs from the list altogether
    pinned_plugin = Jorn_AutosortTabsCommand.instance() if Jorn_AutosortTabsCommand else None
    if pinned_plugin:
        tab_views = [v for v in tab_views if not pinned_plugin._is_pinned(v)]

    # Now proceed with the rest
    if tab_views and (not confirmation_prompt or self.confirm_closing_tabs(tab_views, len(tab_views))):
        self.close_tabs(tab_views)
```

This approach means pinned tabs never even get to the confirmation stage (they just won’t appear in your close list). Pick whichever flow suits your preference.

---

### In summary

- **Import** `Jorn_AutosortTabsCommand` if it’s available.  
- **Check** `_is_pinned(view)` on each tab.  
- **Skip** pinned tabs so they remain open.  

That’s it! Your `JornCloseTabs` plugin will then automatically exclude pinned tabs maintained by `Jorn_AutosortTabs`.

## Message 7

Please show the full updated code using `3. (Optional) Filter pinned tabs earlier`

## Message 8

**Updating JormCloseTabs**

I'm reinforcing the JormCloseTabs code by ensuring pinned tabs are excluded from closure. This involves importing necessary elements and tweaking the run method to filter out pinned tabs.

**Pulling together the code**

I’m gathering the entire code snippet with pinned checks, including imports and adjustments in the run method. This involves integrating the pinned plugin code effectively.

## Message 9

Below is an **example of the full `JornCloseTabs.py`** updated to **exclude pinned tabs earlier** (so pinned tabs never appear in the closing list). All other code remains unchanged, with just the few lines added/modified to import and filter out pinned tabs:

```python
# JornCloseTabs.py

import os
import sublime
import sublime_plugin
import time
import traceback

# 1) Try importing the Autosort plugin class (optional if user has it installed)
try:
    from Jorn_AutosortTabs import Jorn_AutosortTabsCommand
except ImportError:
    Jorn_AutosortTabsCommand = None

class JornCloseTabsCommand(sublime_plugin.WindowCommand):
    valid_tab_types = [
        "all", "empty", "saved", "deleted", "notsaved",
        "modified", "not_in_project", "inside_backups",
        "inside_tmp", "same_extension", "not_modified_in_hours"
    ]
    last_args = {}

    def run(self, tab_types=[], tab_syntaxes=[], current_group_only=True, confirmation_prompt=True,
            hours=None, exclude_active_tabs=False):
        self.last_args = {
            "current_group_only": current_group_only,
            "tab_types": tab_types,
            "tab_syntaxes": tab_syntaxes,
            "confirmation_prompt": confirmation_prompt,
            "hours": hours,
            "exclude_active_tabs": exclude_active_tabs,
        }

        try:
            # 2) Gather all candidate tab views
            if 'all' in tab_types:
                tab_views = self.get_tab_views(current_group_only)
            else:
                tab_views = self.get_tab_views(current_group_only)
                tab_views = self.find_matching_tabs(tab_views, tab_types, tab_syntaxes, hours)

            # Optionally remove active tabs
            if exclude_active_tabs:
                tab_views = self.filter_active_tabs(tab_views)

            # 3) **Exclude pinned tabs** (if Jorn_AutosortTabs is installed)
            pinned_plugin = Jorn_AutosortTabsCommand.instance() if Jorn_AutosortTabsCommand else None
            if pinned_plugin:
                tab_views = [v for v in tab_views if not pinned_plugin._is_pinned(v)]

        except Exception as e:
            print(f"Error retrieving tabs: {e}")
            print(traceback.format_exc())
            tab_views = []

        # Continue with confirmation and closing
        if tab_views and (not confirmation_prompt or self.confirm_closing_tabs(tab_views, len(tab_views))):
            self.close_tabs(tab_views)

    # -------------------------------------------------------------------------
    # Tab retrieval
    # -------------------------------------------------------------------------
    def get_tab_views(self, current_group_only):
        try:
            return self.window.views_in_group(self.window.active_group()) if current_group_only else self.window.views()
        except Exception as e:
            print(f"Error getting tab views: {e}")
            print(traceback.format_exc())
            return []

    def filter_active_tabs(self, tab_views):
        active_views = set()
        for group_index in range(self.window.num_groups()):
            active_view = self.window.active_view_in_group(group_index)
            if active_view:
                active_views.add(active_view)
        return [view for view in tab_views if view not in active_views]

    # -------------------------------------------------------------------------
    # Tab matching
    # -------------------------------------------------------------------------
    def find_matching_tabs(self, tab_views, tab_types, tab_syntaxes, hours):
        matching_tabs = []
        for view in tab_views:
            try:
                if self.tab_matches_conditions(view, tab_types, tab_syntaxes, hours):
                    matching_tabs.append(view)
            except Exception as e:
                print(f"Error processing tab '{self.get_tab_info(view)}': {e}")
                print(traceback.format_exc())
                continue  # Skip this tab and continue with others
        return matching_tabs

    def tab_matches_conditions(self, view, tab_types, tab_syntaxes, hours):
        try:
            return self.matches_tab_type(view, tab_types, hours) and self.matches_syntax(view, tab_syntaxes)
        except Exception as e:
            print(f"Error matching conditions for tab '{self.get_tab_info(view)}': {e}")
            print(traceback.format_exc())
            return False  # If there's an error, don't match this tab

    def matches_tab_type(self, view, tab_types, hours):
        for tab_type in tab_types:
            if tab_type == "not_modified_in_hours":
                if self.is_tab_type_not_modified_in_hours(view, hours):
                    return True
            else:
                check_func = getattr(self, "is_tab_type_" + tab_type, None)
                if check_func is not None:
                    try:
                        if check_func(view):
                            return True
                    except Exception as e:
                        print(f"Error in '{tab_type}' check for tab '{self.get_tab_info(view)}': {e}")
                        print(traceback.format_exc())
                        continue  # Skip this check and continue with others
                else:
                    print('Unsupported: ["{}"]. Valid tab-types are: ["{}"]'.format(
                        tab_type, '", "'.join(self.valid_tab_types)))
        return False

    def matches_syntax(self, view, tab_syntaxes):
        if not tab_syntaxes:
            return True
        try:
            tab_syntax = view.settings().get('syntax')
            return any(syntax.lower() in tab_syntax.lower() for syntax in tab_syntaxes)
        except Exception as e:
            print(f"Error matching syntax for tab '{self.get_tab_info(view)}': {e}")
            print(traceback.format_exc())
            return False

    # -------------------------------------------------------------------------
    # Tab type checks
    # -------------------------------------------------------------------------
    def is_tab_type_empty(self, view):
        try:
            return len(view.substr(sublime.Region(0, view.size())).strip()) <= 1
        except Exception as e:
            print(f"Error checking if tab is empty: {e}")
            print(traceback.format_exc())
            return False

    def is_tab_type_saved(self, view):
        try:
            return view.file_name() and not view.is_dirty() and os.path.exists(view.file_name())
        except Exception as e:
            print(f"Error checking if tab is saved: {e}")
            print(traceback.format_exc())
            return False

    def is_tab_type_deleted(self, view):
        try:
            return view.file_name() and not os.path.exists(view.file_name())
        except Exception as e:
            print(f"Error checking if tab is deleted: {e}")
            print(traceback.format_exc())
            return False

    def is_tab_type_notsaved(self, view):
        try:
            return not view.file_name()
        except Exception as e:
            print(f"Error checking if tab is not saved: {e}")
            print(traceback.format_exc())
            return False

    def is_tab_type_modified(self, view):
        try:
            return view.file_name() and view.is_dirty() and os.path.exists(view.file_name())
        except Exception as e:
            print(f"Error checking if tab is modified: {e}")
            print(traceback.format_exc())
            return False

    def is_tab_type_not_in_project(self, view):
        try:
            file_name = view.file_name()
            if not file_name:
                return False
            project_folders = self.window.folders()
            return not any(file_name.startswith(folder) for folder in project_folders)
        except Exception as e:
            print(f"Error checking if tab is not in project: {e}")
            print(traceback.format_exc())
            return False

    def is_tab_type_inside_backups(self, view):
        try:
            file_name = view.file_name()
            if not file_name:
                return False
            return ".backups" in file_name
        except Exception as e:
            print(f"Error checking if tab is inside backups: {e}")
            print(traceback.format_exc())
            return False

    def is_tab_type_inside_tmp(self, view):
        try:
            file_name = view.file_name()
            if not file_name:
                return False
            return ".tmp" in file_name
        except Exception as e:
            print(f"Error checking if tab is inside tmp: {e}")
            print(traceback.format_exc())
            return False

    def is_tab_type_same_extension(self, view):
        try:
            active_view = self.window.active_view()
            if not active_view or not active_view.file_name() or not view.file_name():
                return False
            return os.path.splitext(active_view.file_name())[1] == os.path.splitext(view.file_name())[1]
        except Exception as e:
            print(f"Error checking if tab has the same extension: {e}")
            print(traceback.format_exc())
            return False

    def is_tab_type_not_modified_in_hours(self, view, hours):
        try:
            if not view.file_name() or hours is None:
                return False
            last_modified_time = os.path.getmtime(view.file_name())
            current_time = time.time()
            return (current_time - last_modified_time) / 3600 > hours
        except FileNotFoundError:
            # File was deleted after the check
            print(f"File not found when checking modification time: {view.file_name()}")
            return False
        except Exception as e:
            print(f"Error checking if tab was not modified in hours: {e}")
            print(traceback.format_exc())
            return False

    # -------------------------------------------------------------------------
    # Helpers
    # -------------------------------------------------------------------------
    def get_tab_info(self, view):
        try:
            return os.path.basename(view.file_name()) if view.file_name() else '{} characters'.format(view.size())
        except Exception as e:
            print(f"Error getting tab info: {e}")
            print(traceback.format_exc())
            return 'Unknown'

    def build_prompt(self, tabs_to_close, total_tabs):
        try:
            prompt_header = 'You are about to close {}/{} tabs:\n'.format(len(tabs_to_close), total_tabs)
            prompt_message = ''.join('-> Tab {}: {}\n'.format(i+1, self.get_tab_info(tab)) for i, tab in enumerate(tabs_to_close))
            prompt_footer = '\nThis cannot be undone, are you sure?'
            return prompt_header + prompt_message + prompt_footer
        except Exception as e:
            print(f"Error building prompt: {e}")
            print(traceback.format_exc())
            return "Are you sure you want to close the selected tabs?"

    def confirm_closing_tabs(self, tabs_to_close, total_tabs):
        try:
            prompt_message = self.build_prompt(tabs_to_close, total_tabs)
            return sublime.ok_cancel_dialog(prompt_message)
        except Exception as e:
            print(f"Error displaying confirmation dialog: {e}")
            print(traceback.format_exc())
            return False

    def close_tabs(self, tabs_to_close):
        confirmation_was_skipped = not self.last_args.get("confirmation_prompt", True)
        closed_count = 0
        closed_tabs = set()
        skipped_tabs = set()

        for tab in tabs_to_close:
            try:
                # Failsafe: Only skip if confirmation was explicitly false AND tab is dirty
                if confirmation_was_skipped and tab.is_dirty() and tab.file_name() and os.path.exists(tab.file_name()):
                    skipped_tabs.add(self.get_tab_info(tab))
                    continue

                closed_tabs.add(self.get_tab_info(tab))
                tab.set_scratch(True)  # avoid "Save changes?" prompt
                tab.close()
                closed_count += 1
            except Exception as e:
                print(f"Error closing tab '{self.get_tab_info(tab)}': {e}")
                print(traceback.format_exc())
                continue

        if skipped_tabs:
            print('\n')
            print(f"Skipped {len(skipped_tabs)} tabs (through failsafe trigger)")
            print(f"Skipped {skipped_tabs}")

        if closed_tabs:
            print('\n')
            print(f"Closed {len(closed_tabs)} tabs")
            print(f"Closed {closed_tabs}")

        return
```

### Summary of Changes

1. **Import** `Jorn_AutosortTabsCommand` (line 12).  
2. In `run(...)` (around line 42 in this snippet):  
   - After gathering and filtering views, we do:
     ```python
     pinned_plugin = Jorn_AutosortTabsCommand.instance() if Jorn_AutosortTabsCommand else None
     if pinned_plugin:
         tab_views = [v for v in tab_views if not pinned_plugin._is_pinned(v)]
     ```
   - This removes **all pinned tabs** from the closing list before we do the confirmation.  

With these changes, pinned tabs maintained by **Jorn_AutosortTabs** will be fully excluded from closure.
